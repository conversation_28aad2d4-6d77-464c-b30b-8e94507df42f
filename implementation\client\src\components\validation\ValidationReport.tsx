import React, { useState } from 'react'
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
  Button,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  CardHeader,
  LinearProgress,
  Tooltip,
} from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import ErrorIcon from '@mui/icons-material/Error'
import WarningIcon from '@mui/icons-material/Warning'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import InfoIcon from '@mui/icons-material/Info'
import SpeedIcon from '@mui/icons-material/Speed'
import DevicesIcon from '@mui/icons-material/Devices'

interface ValidationError {
  code: string
  message: string
  path?: string
}

interface ValidationResult {
  valid: boolean
  errors: ValidationError[]
  warnings: ValidationError[]
}

interface PerformanceMetrics {
  assetCount: number
  totalAssetSize: number
  complexityScore: number
  estimatedLoadTime: number
  estimatedMemoryUsage: number
}

interface PerformanceRecommendation {
  code: string
  message: string
  priority: 'low' | 'medium' | 'high'
}

interface PerformanceAnalysis {
  impact: 'low' | 'medium' | 'high'
  metrics: PerformanceMetrics
  recommendations: PerformanceRecommendation[]
}

interface CompatibilityIssue {
  code: string
  message: string
  severity: 'warning' | 'error'
  path?: string
}

interface CompatibilityResult {
  compatible: boolean
  targetEnvironment: string
  issues: CompatibilityIssue[]
}

interface ValidationReportProps {
  sceneId?: string
  validationResult?: ValidationResult
  performanceAnalysis?: PerformanceAnalysis
  compatibilityResult?: CompatibilityResult
  loading?: boolean
  error?: string
  onValidate?: () => void
  onAnalyzePerformance?: () => void
  onCheckCompatibility?: (environment: string) => void
}

/**
 * Validation Report Component
 * 
 * Displays validation results, performance analysis, and compatibility check results
 */
const ValidationReport: React.FC<ValidationReportProps> = ({
  sceneId,
  validationResult,
  performanceAnalysis,
  compatibilityResult,
  loading = false,
  error,
  onValidate,
  onAnalyzePerformance,
  onCheckCompatibility,
}) => {
  const [selectedEnvironment, setSelectedEnvironment] = useState<string>('quest2')

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) {
      return `${bytes} B`
    } else if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(2)} KB`
    } else if (bytes < 1024 * 1024 * 1024) {
      return `${(bytes / 1024 / 1024).toFixed(2)} MB`
    } else {
      return `${(bytes / 1024 / 1024 / 1024).toFixed(2)} GB`
    }
  }

  // Get color for impact level
  const getImpactColor = (impact: 'low' | 'medium' | 'high'): string => {
    switch (impact) {
      case 'low':
        return 'success.main'
      case 'medium':
        return 'warning.main'
      case 'high':
        return 'error.main'
      default:
        return 'text.primary'
    }
  }

  // Get color for priority level
  const getPriorityColor = (priority: 'low' | 'medium' | 'high'): string => {
    switch (priority) {
      case 'low':
        return 'info'
      case 'medium':
        return 'warning'
      case 'high':
        return 'error'
      default:
        return 'default'
    }
  }

  // Get color for severity level
  const getSeverityColor = (severity: 'warning' | 'error'): string => {
    switch (severity) {
      case 'warning':
        return 'warning'
      case 'error':
        return 'error'
      default:
        return 'default'
    }
  }

  // Render validation results
  const renderValidationResults = () => {
    if (!validationResult) {
      return (
        <Box sx={{ textAlign: 'center', my: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={onValidate}
            disabled={loading || !sceneId}
          >
            {loading ? <CircularProgress size={24} /> : 'Validate Scene'}
          </Button>
        </Box>
      )
    }

    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          {validationResult.valid ? (
            <CheckCircleIcon color="success" sx={{ mr: 1 }} />
          ) : (
            <ErrorIcon color="error" sx={{ mr: 1 }} />
          )}
          <Typography variant="h6">
            {validationResult.valid ? 'Scene is valid' : 'Scene has validation errors'}
          </Typography>
        </Box>

        {validationResult.errors.length > 0 && (
          <Accordion defaultExpanded={!validationResult.valid}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography color="error" sx={{ display: 'flex', alignItems: 'center' }}>
                <ErrorIcon sx={{ mr: 1 }} />
                {validationResult.errors.length} Error{validationResult.errors.length !== 1 ? 's' : ''}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {validationResult.errors.map((error, index) => (
                  <React.Fragment key={`error-${index}`}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Chip
                              label={error.code}
                              color="error"
                              size="small"
                              sx={{ mr: 1 }}
                            />
                            <Typography variant="body2">{error.message}</Typography>
                          </Box>
                        }
                        secondary={error.path && `Path: ${error.path}`}
                      />
                    </ListItem>
                    {index < validationResult.errors.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        )}

        {validationResult.warnings.length > 0 && (
          <Accordion defaultExpanded={validationResult.valid && validationResult.warnings.length > 0}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography color="warning.main" sx={{ display: 'flex', alignItems: 'center' }}>
                <WarningIcon sx={{ mr: 1 }} />
                {validationResult.warnings.length} Warning{validationResult.warnings.length !== 1 ? 's' : ''}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {validationResult.warnings.map((warning, index) => (
                  <React.Fragment key={`warning-${index}`}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Chip
                              label={warning.code}
                              color="warning"
                              size="small"
                              sx={{ mr: 1 }}
                            />
                            <Typography variant="body2">{warning.message}</Typography>
                          </Box>
                        }
                        secondary={warning.path && `Path: ${warning.path}`}
                      />
                    </ListItem>
                    {index < validationResult.warnings.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        )}

        {validationResult.valid && validationResult.warnings.length === 0 && (
          <Alert severity="success">No issues found in the scene.</Alert>
        )}
      </Box>
    )
  }

  // Render performance analysis
  const renderPerformanceAnalysis = () => {
    if (!performanceAnalysis) {
      return (
        <Box sx={{ textAlign: 'center', my: 2 }}>
          <Button
            variant="contained"
            color="primary"
            onClick={onAnalyzePerformance}
            disabled={loading || !sceneId}
            startIcon={<SpeedIcon />}
          >
            {loading ? <CircularProgress size={24} /> : 'Analyze Performance'}
          </Button>
        </Box>
      )
    }

    const { impact, metrics, recommendations } = performanceAnalysis

    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <SpeedIcon sx={{ mr: 1, color: getImpactColor(impact) }} />
          <Typography variant="h6">
            Performance Impact: <span style={{ color: getImpactColor(impact) }}>{impact.toUpperCase()}</span>
          </Typography>
        </Box>

        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardHeader title="Asset Metrics" />
              <CardContent>
                <Typography variant="body2">
                  Asset Count: {metrics.assetCount}
                </Typography>
                <Typography variant="body2">
                  Total Asset Size: {formatFileSize(metrics.totalAssetSize)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardHeader title="Performance Metrics" />
              <CardContent>
                <Typography variant="body2">
                  Complexity Score: {metrics.complexityScore}
                </Typography>
                <Typography variant="body2">
                  Estimated Load Time: {metrics.estimatedLoadTime.toFixed(2)}s
                </Typography>
                <Typography variant="body2">
                  Estimated Memory Usage: {formatFileSize(metrics.estimatedMemoryUsage)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {recommendations.length > 0 && (
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                {recommendations.length} Recommendation{recommendations.length !== 1 ? 's' : ''}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {recommendations.map((recommendation, index) => (
                  <React.Fragment key={`recommendation-${index}`}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Chip
                              label={recommendation.code}
                              color={getPriorityColor(recommendation.priority)}
                              size="small"
                              sx={{ mr: 1 }}
                            />
                            <Typography variant="body2">{recommendation.message}</Typography>
                          </Box>
                        }
                        secondary={`Priority: ${recommendation.priority.toUpperCase()}`}
                      />
                    </ListItem>
                    {index < recommendations.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        )}
      </Box>
    )
  }

  // Render compatibility check
  const renderCompatibilityCheck = () => {
    const environments = [
      { value: 'quest2', label: 'Quest 2' },
      { value: 'quest3', label: 'Quest 3' },
      { value: 'pico4', label: 'Pico 4' },
      { value: 'steamvr', label: 'SteamVR' },
    ]

    if (!compatibilityResult) {
      return (
        <Box sx={{ textAlign: 'center', my: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            {environments.map((env) => (
              <Button
                key={env.value}
                variant={selectedEnvironment === env.value ? 'contained' : 'outlined'}
                color="primary"
                size="small"
                onClick={() => setSelectedEnvironment(env.value)}
                sx={{ mx: 0.5 }}
              >
                {env.label}
              </Button>
            ))}
          </Box>
          <Button
            variant="contained"
            color="primary"
            onClick={() => onCheckCompatibility && onCheckCompatibility(selectedEnvironment)}
            disabled={loading || !sceneId}
            startIcon={<DevicesIcon />}
          >
            {loading ? <CircularProgress size={24} /> : `Check Compatibility with ${selectedEnvironment.toUpperCase()}`}
          </Button>
        </Box>
      )
    }

    const { compatible, targetEnvironment, issues } = compatibilityResult

    return (
      <Box>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <DevicesIcon sx={{ mr: 1, color: compatible ? 'success.main' : 'error.main' }} />
          <Typography variant="h6">
            Compatibility with {targetEnvironment.toUpperCase()}: {' '}
            <span style={{ color: compatible ? 'green' : 'red' }}>
              {compatible ? 'COMPATIBLE' : 'NOT COMPATIBLE'}
            </span>
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
          {environments.map((env) => (
            <Button
              key={env.value}
              variant={targetEnvironment === env.value ? 'contained' : 'outlined'}
              color="primary"
              size="small"
              onClick={() => onCheckCompatibility && onCheckCompatibility(env.value)}
              sx={{ mx: 0.5 }}
            >
              {env.label}
            </Button>
          ))}
        </Box>

        {issues.length > 0 ? (
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                {issues.length} Issue{issues.length !== 1 ? 's' : ''}
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                {issues.map((issue, index) => (
                  <React.Fragment key={`issue-${index}`}>
                    <ListItem>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Chip
                              label={issue.code}
                              color={getSeverityColor(issue.severity)}
                              size="small"
                              sx={{ mr: 1 }}
                            />
                            <Typography variant="body2">{issue.message}</Typography>
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption">
                              Severity: {issue.severity.toUpperCase()}
                            </Typography>
                            {issue.path && (
                              <Typography variant="caption" sx={{ display: 'block' }}>
                                Path: {issue.path}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < issues.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        ) : (
          <Alert severity="success">No compatibility issues found.</Alert>
        )}
      </Box>
    )
  }

  return (
    <Paper sx={{ p: 3 }}>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Scene Validation</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {renderValidationResults()}
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Performance Analysis</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {renderPerformanceAnalysis()}
        </AccordionDetails>
      </Accordion>

      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h6">Compatibility Check</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {renderCompatibilityCheck()}
        </AccordionDetails>
      </Accordion>
    </Paper>
  )
}

export default ValidationReport
