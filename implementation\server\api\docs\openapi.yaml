openapi: 3.0.3
info:
  title: MVS-VR API
  description: |
    API documentation for the MVS-VR platform.
    
    This API provides endpoints for managing assets, scenes, blueprints, and user authentication.
  version: 1.0.0
  contact:
    name: MVS-VR Support
    email: <EMAIL>
    url: https://mvs-vr.com/support
  license:
    name: Proprietary
    url: https://mvs-vr.com/license

servers:
  - url: https://api.mvs-vr.com/v1
    description: Production server
  - url: https://staging-api.mvs-vr.com/v1
    description: Staging server
  - url: http://localhost:3000/v1
    description: Local development server

tags:
  - name: Authentication
    description: Authentication endpoints
  - name: Users
    description: User management endpoints
  - name: Assets
    description: Asset management endpoints
  - name: Scenes
    description: Scene management endpoints
  - name: Blueprints
    description: Blueprint management endpoints
  - name: Analytics
    description: Analytics and reporting endpoints

paths:
  /auth/register:
    post:
      tags:
        - Authentication
      summary: Register a new user
      description: Creates a new user account with the provided information.
      operationId: registerUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRegistration'
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Invalid input
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security: []

  /auth/login:
    post:
      tags:
        - Authentication
      summary: Login user
      description: Authenticates a user and returns access and refresh tokens.
      operationId: loginUser
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserLogin'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security: []

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh access token
      description: Refreshes the access token using a valid refresh token.
      operationId: refreshToken
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - refreshToken
              properties:
                refreshToken:
                  type: string
                  description: Refresh token
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: Invalid refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security: []

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: Logout user
      description: Invalidates the user's tokens.
      operationId: logoutUser
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - bearerAuth: []

  /users/me:
    get:
      tags:
        - Users
      summary: Get current user
      description: Returns the profile of the currently authenticated user.
      operationId: getCurrentUser
      responses:
        '200':
          description: User profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - bearerAuth: []

  /assets:
    get:
      tags:
        - Assets
      summary: List assets
      description: Returns a list of assets for the authenticated user.
      operationId: listAssets
      parameters:
        - name: limit
          in: query
          description: Maximum number of assets to return
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 100
        - name: offset
          in: query
          description: Number of assets to skip
          schema:
            type: integer
            default: 0
            minimum: 0
        - name: type
          in: query
          description: Filter by asset type
          schema:
            type: string
        - name: status
          in: query
          description: Filter by asset status
          schema:
            type: string
            enum: [uploaded, processing, ready, failed]
        - name: name
          in: query
          description: Filter by asset name (partial match)
          schema:
            type: string
      responses:
        '200':
          description: List of assets
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  assets:
                    type: array
                    items:
                      $ref: '#/components/schemas/Asset'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
      security:
        - bearerAuth: []

components:
  schemas:
    UserRegistration:
      type: object
      required:
        - email
        - password
        - name
      properties:
        email:
          type: string
          format: email
          description: User's email address
          example: <EMAIL>
        password:
          type: string
          format: password
          description: User's password
          example: Password123!
        name:
          type: string
          description: User's full name
          example: John Doe
        role:
          type: string
          description: User's role
          enum: [admin, vendor, client]
          default: client
          example: client

    UserLogin:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          description: User's email address
          example: <EMAIL>
        password:
          type: string
          format: password
          description: User's password
          example: Password123!

    AuthResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        token:
          type: string
          description: Access token
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        refreshToken:
          type: string
          description: Refresh token
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        user:
          $ref: '#/components/schemas/UserProfile'

    UserProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: User ID
          example: 123e4567-e89b-12d3-a456-************
        email:
          type: string
          format: email
          description: User's email address
          example: <EMAIL>
        name:
          type: string
          description: User's full name
          example: John Doe
        role:
          type: string
          description: User's role
          enum: [admin, vendor, client]
          example: client
        created_at:
          type: string
          format: date-time
          description: Account creation timestamp
          example: 2023-01-01T00:00:00Z
        updated_at:
          type: string
          format: date-time
          description: Account update timestamp
          example: 2023-01-01T00:00:00Z

    Asset:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Asset ID
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: Asset name
          example: Chair Model
        description:
          type: string
          description: Asset description
          example: A 3D model of a modern chair
        type:
          type: string
          description: Asset MIME type
          example: model/gltf-binary
        size:
          type: integer
          description: Asset size in bytes
          example: 1048576
        status:
          type: string
          description: Asset processing status
          enum: [uploaded, processing, ready, failed]
          example: ready
        url:
          type: string
          format: uri
          description: Asset URL
          example: https://assets.mvs-vr.com/123e4567-e89b-12d3-a456-************
        thumbnail_url:
          type: string
          format: uri
          description: Asset thumbnail URL
          example: https://assets.mvs-vr.com/123e4567-e89b-12d3-a456-************/thumbnail.jpg
        optimized_url:
          type: string
          format: uri
          description: Optimized asset URL
          example: https://assets.mvs-vr.com/123e4567-e89b-12d3-a456-************/optimized
        created_at:
          type: string
          format: date-time
          description: Asset creation timestamp
          example: 2023-01-01T00:00:00Z
        updated_at:
          type: string
          format: date-time
          description: Asset update timestamp
          example: 2023-01-01T00:00:00Z

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 100
        limit:
          type: integer
          description: Maximum number of items per page
          example: 20
        offset:
          type: integer
          description: Number of items skipped
          example: 0
        has_more:
          type: boolean
          description: Whether there are more items
          example: true

    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: object
          properties:
            code:
              type: string
              description: Error code
              example: INVALID_CREDENTIALS
            message:
              type: string
              description: Error message
              example: Invalid email or password

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from the /auth/login endpoint
