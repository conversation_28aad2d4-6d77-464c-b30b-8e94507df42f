/**
 * Analytics API
 *
 * This file exports the main analytics router that includes all analytics endpoints.
 */

import { Router } from 'express';
import { logger } from '../../lib/logger';

// Import routers
import phaseMetricsRouter from './phase-metrics.ts';
import realtimeRouter from './realtime.ts';
import exportRouter from './export.ts';
import comparativeRouter from './comparative.ts';
import customReportsRouter from './custom-reports.ts';
import realtimeVisualizationRouter from './realtime-visualization.ts';

// Create main router
const router = Router();

// Register sub-routers
router.use('/phase-metrics', phaseMetricsRouter);
router.use('/realtime', realtimeRouter);
router.use('/export', exportRouter);
router.use('/comparative', comparativeRouter);
router.use('/custom-reports', customReportsRouter);
router.use('/realtime-visualization', realtimeVisualizationRouter);

// Log router initialization
logger.info('Analytics API router initialized');

export default router;
