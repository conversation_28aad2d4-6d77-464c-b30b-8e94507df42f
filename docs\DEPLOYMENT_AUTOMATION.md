# Deployment Automation Documentation

This document provides comprehensive documentation for the MVS-VR deployment automation, including infrastructure as code, containerization, deployment scripts, and monitoring.

## Table of Contents

1. [Infrastructure as Code](#infrastructure-as-code)
2. [Containerization](#containerization)
3. [Deployment Scripts](#deployment-scripts)
4. [Deployment Monitoring](#deployment-monitoring)
5. [Best Practices](#best-practices)

## Infrastructure as Code

The MVS-VR project uses Terraform to manage infrastructure as code, enabling reproducible, version-controlled deployments across different environments.

### Terraform Configuration

The Terraform configuration is located in the `mvs-vr-v2/implementation/server/terraform/` directory and includes:

- `main.tf`: Main Terraform configuration
- `variables.tf`: Variable definitions
- `modules/`: Reusable Terraform modules
  - `redis/`: Redis deployment module
  - `directus/`: Directus deployment module
  - `server/`: Server deployment module
  - `monitoring/`: Monitoring deployment module

### Resource Management

Terraform manages the following resources:

1. **Kubernetes Resources**
   - Namespaces
   - Deployments
   - Services
   - Persistent Volume Claims
   - ConfigMaps
   - Secrets

2. **AWS Resources** (when using AWS)
   - S3 Buckets for storage
   - IAM Roles and Policies
   - CloudFront Distributions

### Environment Configuration

Separate Terraform configurations are maintained for each environment:

- Development
- Staging
- Production

### Usage

To apply the Terraform configuration:

```bash
cd mvs-vr-v2/implementation/server/terraform
terraform init
terraform apply -var-file=environments/staging.tfvars
```

## Containerization

The MVS-VR application is containerized using Docker, enabling consistent deployment across different environments.

### Docker Images

The following Docker images are used:

1. **Server**: Node.js application with Next.js
2. **Directus**: Headless CMS for content management
3. **Redis**: Caching and session management

### Multi-Stage Builds

Multi-stage Docker builds are used to optimize image size and security:

1. **Build Stage**: Compiles the application
2. **Runtime Stage**: Contains only the necessary runtime dependencies

### Image Optimization

Docker images are optimized for:

- Size: Using multi-stage builds and Alpine-based images
- Security: Running as non-root users
- Performance: Including only necessary dependencies

### Container Registry

Docker images are stored in GitHub Container Registry (ghcr.io) with the following naming convention:

```
ghcr.io/your-org/mvs-vr/<service>:<tag>
```

Where:
- `<service>` is the service name (server, directus)
- `<tag>` is the version tag or commit SHA

## Deployment Scripts

Deployment scripts automate the process of deploying the MVS-VR application to different environments.

### Windows PowerShell Script

The PowerShell deployment script (`deploy.ps1`) provides deployment automation for Windows environments.

#### Features

- Environment-specific configuration
- Docker image building and tagging
- Docker image pushing to registry
- Terraform infrastructure provisioning
- Smoke testing

#### Usage

```powershell
.\scripts\deployment\deploy.ps1 -Environment staging -Version 1.0.0 -ApplyTerraform
```

### Bash Script

The Bash deployment script (`deploy.sh`) provides deployment automation for Linux/macOS environments.

#### Features

- Environment-specific configuration
- Docker image building and tagging
- Docker image pushing to registry
- Terraform infrastructure provisioning
- Smoke testing

#### Usage

```bash
./scripts/deployment/deploy.sh --environment staging --version 1.0.0 --terraform
```

### Environment Configuration

Environment-specific configuration is stored in `.env.<environment>` files, containing:

- API keys and secrets
- Database connection strings
- Feature flags
- Environment-specific settings

## Deployment Monitoring

Deployment monitoring ensures that deployments are successful and the application is running correctly.

### Health Checks

Health checks are implemented at multiple levels:

1. **Container Health Checks**: Using Docker HEALTHCHECK
2. **Kubernetes Liveness Probes**: Ensuring containers are running
3. **Kubernetes Readiness Probes**: Ensuring services are ready to accept traffic
4. **Application Health Endpoints**: Custom health check endpoints

### Metrics Collection

Deployment metrics are collected using:

1. **Prometheus**: For metrics collection
2. **Grafana**: For metrics visualization

Key metrics include:

- Deployment frequency
- Deployment duration
- Deployment success rate
- Rollback frequency

### Logging

Deployment logs are collected and centralized using:

1. **Kubernetes Logging**: Container logs
2. **Application Logging**: Application-specific logs
3. **CI/CD Logs**: Pipeline execution logs

### Alerting

Alerts are configured for:

- Failed deployments
- Deployment duration exceeding thresholds
- Health check failures
- Resource utilization spikes after deployment

## Best Practices

### Infrastructure as Code

- Keep Terraform code modular and reusable
- Use remote state storage for collaboration
- Implement state locking to prevent conflicts
- Use variables for environment-specific configuration
- Document all resources and their purpose

### Containerization

- Use multi-stage builds for smaller images
- Run containers as non-root users
- Scan images for vulnerabilities
- Use specific version tags instead of `latest`
- Implement proper health checks

### Deployment Scripts

- Make scripts idempotent
- Implement proper error handling
- Add logging for troubleshooting
- Include rollback functionality
- Validate environment before deployment

### Deployment Monitoring

- Monitor both infrastructure and application
- Set up alerts for critical issues
- Collect and analyze deployment metrics
- Implement automated smoke tests
- Document troubleshooting procedures
