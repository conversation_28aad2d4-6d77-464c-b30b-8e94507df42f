/**
 * Vitest tests for VisualEditors component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createWrapper } from '../src/test-utils/component-test-utils.js';

// Mock the child components
const ShowroomLayoutEditorMock = {
  name: 'ShowroomLayoutEditor',
  props: ['data', 'onChange'],
  template: '<div class="showroom-layout-editor">Showroom Layout Editor</div>',
};

const ProductConfiguratorMock = {
  name: 'ProductConfigurator',
  props: ['data', 'onChange'],
  template: '<div class="product-configurator">Product Configurator</div>',
};

const MaterialTextureEditorMock = {
  name: 'MaterialTextureEditor',
  props: ['data', 'onChange'],
  template: '<div class="material-texture-editor">Material Texture Editor</div>',
};

const LightingEditorMock = {
  name: 'LightingEditor',
  props: ['data', 'onChange'],
  template: '<div class="lighting-editor">Lighting Editor</div>',
};

const AnimationEditorMock = {
  name: 'AnimationEditor',
  props: ['data', 'onChange'],
  template: '<div class="animation-editor">Animation Editor</div>',
};

// Create a mock VisualEditors component
const VisualEditorsMock = {
  name: 'VisualEditors',
  components: {
    ShowroomLayoutEditor: ShowroomLayoutEditorMock,
    ProductConfigurator: ProductConfiguratorMock,
    MaterialTextureEditor: MaterialTextureEditorMock,
    LightingEditor: LightingEditorMock,
    AnimationEditor: AnimationEditorMock,
  },
  props: {
    initialData: {
      type: Object,
      default: () => ({}),
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      activeEditor: 'layout',
      data: this.initialData || {
        layout: {},
        products: {},
        materials: {},
        lighting: {},
        animations: {},
      },
    };
  },
  methods: {
    setActiveEditor(editor) {
      this.activeEditor = editor;
    },
    
    handleDataChange(editorType, newData) {
      this.data[editorType] = newData;
      
      if (this.onChange) {
        this.onChange(this.data);
      }
    },
  },
  template: `
    <div class="visual-editors">
      <div class="editor-tabs">
        <button class="tab-button" :class="{ active: activeEditor === 'layout' }" @click="setActiveEditor('layout')">Layout</button>
        <button class="tab-button" :class="{ active: activeEditor === 'products' }" @click="setActiveEditor('products')">Products</button>
        <button class="tab-button" :class="{ active: activeEditor === 'materials' }" @click="setActiveEditor('materials')">Materials</button>
        <button class="tab-button" :class="{ active: activeEditor === 'lighting' }" @click="setActiveEditor('lighting')">Lighting</button>
        <button class="tab-button" :class="{ active: activeEditor === 'animations' }" @click="setActiveEditor('animations')">Animations</button>
      </div>
      
      <div class="editor-content">
        <ShowroomLayoutEditor 
          v-if="activeEditor === 'layout'" 
          :data="data.layout" 
          :onChange="newData => handleDataChange('layout', newData)" 
        />
        
        <ProductConfigurator 
          v-if="activeEditor === 'products'" 
          :data="data.products" 
          :onChange="newData => handleDataChange('products', newData)" 
        />
        
        <MaterialTextureEditor 
          v-if="activeEditor === 'materials'" 
          :data="data.materials" 
          :onChange="newData => handleDataChange('materials', newData)" 
        />
        
        <LightingEditor 
          v-if="activeEditor === 'lighting'" 
          :data="data.lighting" 
          :onChange="newData => handleDataChange('lighting', newData)" 
        />
        
        <AnimationEditor 
          v-if="activeEditor === 'animations'" 
          :data="data.animations" 
          :onChange="newData => handleDataChange('animations', newData)" 
        />
      </div>
    </div>
  `,
};

describe('VisualEditors', () => {
  let wrapper;
  const mockProps = {
    initialData: {
      layout: { showroom: 'default' },
      products: { items: [] },
      materials: { textures: [] },
      lighting: { ambient: 0.5 },
      animations: { sequences: [] },
    },
    onChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    wrapper = createWrapper(VisualEditorsMock, {
      propsData: mockProps,
    });
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.visual-editors').exists()).toBe(true);
  });

  it('renders the tabs correctly', () => {
    const tabs = wrapper.findAll('.tab-button');
    expect(tabs.length).toBe(5);
    
    // Check tab names
    expect(tabs.at(0).text()).toBe('Layout');
    expect(tabs.at(1).text()).toBe('Products');
    expect(tabs.at(2).text()).toBe('Materials');
    expect(tabs.at(3).text()).toBe('Lighting');
    expect(tabs.at(4).text()).toBe('Animations');
  });

  it('shows the ShowroomLayoutEditor by default', () => {
    expect(wrapper.findComponent(ShowroomLayoutEditorMock).exists()).toBe(true);
    expect(wrapper.findComponent(ProductConfiguratorMock).exists()).toBe(false);
    expect(wrapper.findComponent(MaterialTextureEditorMock).exists()).toBe(false);
    expect(wrapper.findComponent(LightingEditorMock).exists()).toBe(false);
    expect(wrapper.findComponent(AnimationEditorMock).exists()).toBe(false);
  });

  it('switches to ProductConfigurator when clicking the Products tab', async () => {
    // Set the active editor to 'products'
    await wrapper.vm.setActiveEditor('products');
    await wrapper.vm.$nextTick();
    
    // Check that the correct editor is shown
    expect(wrapper.findComponent(ShowroomLayoutEditorMock).exists()).toBe(false);
    expect(wrapper.findComponent(ProductConfiguratorMock).exists()).toBe(true);
    expect(wrapper.findComponent(MaterialTextureEditorMock).exists()).toBe(false);
    expect(wrapper.findComponent(LightingEditorMock).exists()).toBe(false);
    expect(wrapper.findComponent(AnimationEditorMock).exists()).toBe(false);
  });

  it('calls the onChange prop when editor data changes', async () => {
    // Simulate a change in the layout editor
    const newLayoutData = { showroom: 'custom' };
    wrapper.vm.handleDataChange('layout', newLayoutData);
    
    // Check that the data is updated
    expect(wrapper.vm.data.layout).toEqual(newLayoutData);
    
    // Check that the onChange prop is called with the updated data
    expect(mockProps.onChange).toHaveBeenCalledWith({
      layout: newLayoutData,
      products: { items: [] },
      materials: { textures: [] },
      lighting: { ambient: 0.5 },
      animations: { sequences: [] },
    });
  });
});
