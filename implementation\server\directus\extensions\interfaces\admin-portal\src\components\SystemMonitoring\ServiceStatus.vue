<template>
  <v-card class="service-status">
    <v-card-title>Service Status</v-card-title>
    
    <v-card-text>
      <v-skeleton-loader
        v-if="loading"
        type="card"
        class="mx-auto"
      ></v-skeleton-loader>
      
      <div v-else class="services-content">
        <v-list>
          <v-list-item
            v-for="(service, key) in services"
            :key="key"
            class="service-item"
          >
            <v-list-item-avatar>
              <v-icon
                :color="getStatusColor(service.status)"
                size="32"
              >
                {{ getStatusIcon(service.status) }}
              </v-icon>
            </v-list-item-avatar>
            
            <v-list-item-content>
              <v-list-item-title class="service-name">
                {{ formatServiceName(key) }}
              </v-list-item-title>
              <v-list-item-subtitle class="service-status-text">
                Status: {{ formatStatus(service.status) }}
              </v-list-item-subtitle>
              <v-list-item-subtitle class="service-last-checked">
                Last checked: {{ formatTimestamp(service.last_checked) }}
              </v-list-item-subtitle>
            </v-list-item-content>
            
            <v-list-item-action>
              <v-btn
                icon
                small
                @click="showServiceDetails(key, service)"
              >
                <v-icon>mdi-information-outline</v-icon>
              </v-btn>
            </v-list-item-action>
          </v-list-item>
        </v-list>
        
        <div v-if="!services || Object.keys(services).length === 0" class="no-services">
          <v-icon large color="grey lighten-1">mdi-server-off</v-icon>
          <p>No service information available</p>
        </div>
      </div>
    </v-card-text>
    
    <!-- Service Details Dialog -->
    <v-dialog
      v-model="detailsDialog"
      max-width="500"
    >
      <v-card>
        <v-card-title class="headline">
          {{ selectedServiceName }}
        </v-card-title>
        
        <v-card-text>
          <v-list>
            <v-list-item>
              <v-list-item-content>
                <v-list-item-subtitle>Status</v-list-item-subtitle>
                <v-list-item-title>
                  <v-chip
                    :color="getStatusColor(selectedService?.status)"
                    text-color="white"
                    small
                  >
                    {{ formatStatus(selectedService?.status) }}
                  </v-chip>
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            
            <v-list-item>
              <v-list-item-content>
                <v-list-item-subtitle>Last Checked</v-list-item-subtitle>
                <v-list-item-title>
                  {{ formatTimestamp(selectedService?.last_checked) }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            
            <v-divider></v-divider>
            
            <v-list-item v-if="selectedService?.details">
              <v-list-item-content>
                <v-list-item-subtitle>Details</v-list-item-subtitle>
                <v-list-item-title>
                  {{ selectedService.details }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            
            <v-list-item v-if="getServiceDescription(selectedServiceKey)">
              <v-list-item-content>
                <v-list-item-subtitle>Description</v-list-item-subtitle>
                <v-list-item-title>
                  {{ getServiceDescription(selectedServiceKey) }}
                </v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </v-card-text>
        
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="primary"
            text
            @click="detailsDialog = false"
          >
            Close
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script>
export default {
  name: 'ServiceStatus',
  
  props: {
    services: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      detailsDialog: false,
      selectedServiceKey: null,
      selectedService: null,
      serviceDescriptions: {
        api_gateway: 'Handles API requests and routes them to the appropriate services.',
        auth_service: 'Manages user authentication and authorization.',
        database_service: 'Provides database access and management.',
        storage_service: 'Handles file storage and retrieval.',
        asset_processor: 'Processes and optimizes assets for the platform.',
        notification_service: 'Manages and delivers notifications to users.'
      }
    };
  },
  
  computed: {
    selectedServiceName() {
      if (!this.selectedServiceKey) return '';
      return this.formatServiceName(this.selectedServiceKey);
    }
  },
  
  methods: {
    // Format service name
    formatServiceName(key) {
      return key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    },
    
    // Format status
    formatStatus(status) {
      if (!status) return 'Unknown';
      return status.charAt(0).toUpperCase() + status.slice(1);
    },
    
    // Format timestamp
    formatTimestamp(timestamp) {
      if (!timestamp) return 'N/A';
      return new Date(timestamp).toLocaleString();
    },
    
    // Get status color
    getStatusColor(status) {
      switch (status) {
        case 'healthy':
          return 'success';
        case 'degraded':
          return 'warning';
        case 'unhealthy':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    // Get status icon
    getStatusIcon(status) {
      switch (status) {
        case 'healthy':
          return 'mdi-check-circle';
        case 'degraded':
          return 'mdi-alert';
        case 'unhealthy':
          return 'mdi-alert-circle';
        default:
          return 'mdi-help-circle';
      }
    },
    
    // Show service details
    showServiceDetails(key, service) {
      this.selectedServiceKey = key;
      this.selectedService = service;
      this.detailsDialog = true;
    },
    
    // Get service description
    getServiceDescription(key) {
      return this.serviceDescriptions[key] || '';
    }
  }
};
</script>

<style scoped>
.service-status {
  height: 100%;
}

.services-content {
  min-height: 200px;
}

.service-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  transition: background-color 0.2s;
}

.service-item:last-child {
  border-bottom: none;
}

.service-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.service-name {
  font-weight: 500;
}

.service-status-text {
  font-size: 0.875rem;
}

.service-last-checked {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}

.no-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: rgba(0, 0, 0, 0.6);
}

.no-services p {
  margin-top: 16px;
}
</style>
