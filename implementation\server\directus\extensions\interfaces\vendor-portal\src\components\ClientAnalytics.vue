<template>
  <div class="client-analytics">
    <div class="analytics-header">
      <h2>Client Analytics</h2>
      <div class="analytics-controls">
        <div class="filter">
          <label>Date Range:</label>
          <select v-model="dateRange" @change="updateAnalytics">
            <option value="7">Last 7 Days</option>
            <option value="30">Last 30 Days</option>
            <option value="90">Last 90 Days</option>
            <option value="365">Last 12 Months</option>
          </select>
        </div>
        <div class="filter">
          <label>Client Segment:</label>
          <select v-model="clientSegment" @change="updateAnalytics">
            <option value="all">All Clients</option>
            <option value="active">Active Clients</option>
            <option value="inactive">Inactive Clients</option>
            <option value="new">New Clients</option>
            <option value="returning">Returning Clients</option>
          </select>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
      <span>Loading analytics data...</span>
    </div>

    <div v-else class="analytics-content">
      <div class="metrics-row">
        <div class="metric-card">
          <div class="metric-icon">
            <i class="material-icons">people</i>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.totalClients }}</div>
            <div class="metric-label">Total Clients</div>
            <div class="metric-change" :class="getChangeClass(metrics.clientsChange)">
              <i class="material-icons">{{ getChangeIcon(metrics.clientsChange) }}</i>
              <span>{{ formatChange(metrics.clientsChange) }}</span>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="material-icons">person_add</i>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.newClients }}</div>
            <div class="metric-label">New Clients</div>
            <div class="metric-change" :class="getChangeClass(metrics.newClientsChange)">
              <i class="material-icons">{{ getChangeIcon(metrics.newClientsChange) }}</i>
              <span>{{ formatChange(metrics.newClientsChange) }}</span>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="material-icons">shopping_cart</i>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.totalOrders }}</div>
            <div class="metric-label">Total Orders</div>
            <div class="metric-change" :class="getChangeClass(metrics.ordersChange)">
              <i class="material-icons">{{ getChangeIcon(metrics.ordersChange) }}</i>
              <span>{{ formatChange(metrics.ordersChange) }}</span>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="material-icons">attach_money</i>
          </div>
          <div class="metric-content">
            <div class="metric-value">${{ formatNumber(metrics.totalRevenue) }}</div>
            <div class="metric-label">Total Revenue</div>
            <div class="metric-change" :class="getChangeClass(metrics.revenueChange)">
              <i class="material-icons">{{ getChangeIcon(metrics.revenueChange) }}</i>
              <span>{{ formatChange(metrics.revenueChange) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="charts-row">
        <div class="chart-container half">
          <AnalyticsChart
            title="Client Acquisition"
            type="line"
            :data="clientAcquisitionData"
            @period-change="updateClientAcquisitionChart"
          />
        </div>

        <div class="chart-container half">
          <AnalyticsChart
            title="Client Engagement"
            type="bar"
            :data="clientEngagementData"
            @period-change="updateClientEngagementChart"
          />
        </div>
      </div>

      <div class="charts-row">
        <div class="chart-container half">
          <AnalyticsChart
            title="Client Segments"
            type="doughnut"
            :data="clientSegmentsData"
          />
        </div>

        <div class="chart-container half">
          <AnalyticsChart
            title="Client Activity Types"
            type="pie"
            :data="activityTypesData"
          />
        </div>
      </div>

      <div class="analytics-section">
        <h3>Top Clients by Engagement</h3>
        <div class="top-clients-list">
          <div class="top-clients-header">
            <div class="col-rank">#</div>
            <div class="col-client">Client</div>
            <div class="col-engagement">Engagement Score</div>
            <div class="col-orders">Orders</div>
            <div class="col-revenue">Revenue</div>
            <div class="col-actions">Actions</div>
          </div>
          <div v-for="(client, index) in topClients" :key="client.id" class="top-client-item">
            <div class="col-rank">{{ index + 1 }}</div>
            <div class="col-client">
              <div class="client-avatar">
                <img v-if="client.avatar" :src="client.avatar" :alt="client.name">
                <div v-else class="placeholder-avatar small">
                  {{ getInitials(client.name) }}
                </div>
              </div>
              <div class="client-info">
                <div class="client-name">{{ client.name }}</div>
                <div class="client-company">{{ client.company || 'No company' }}</div>
              </div>
            </div>
            <div class="col-engagement">
              <div class="engagement-bar">
                <div
                  class="engagement-progress"
                  :style="{ width: `${client.engagement}%` }"
                  :class="{
                    'high': client.engagement >= 70,
                    'medium': client.engagement >= 40 && client.engagement < 70,
                    'low': client.engagement < 40
                  }"
                ></div>
              </div>
              <span class="engagement-score">{{ client.engagement }}</span>
            </div>
            <div class="col-orders">{{ client.orders || 0 }}</div>
            <div class="col-revenue">${{ formatNumber(client.revenue || 0) }}</div>
            <div class="col-actions">
              <button class="btn btn-icon" @click="viewClient(client)" title="View Client">
                <i class="material-icons">visibility</i>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="analytics-section">
        <h3>Client Retention Analysis</h3>
        <div class="retention-metrics">
          <div class="retention-metric">
            <div class="retention-value">{{ metrics.retentionRate }}%</div>
            <div class="retention-label">Retention Rate</div>
          </div>
          <div class="retention-metric">
            <div class="retention-value">{{ metrics.churnRate }}%</div>
            <div class="retention-label">Churn Rate</div>
          </div>
          <div class="retention-metric">
            <div class="retention-value">{{ metrics.averageLifetime }} months</div>
            <div class="retention-label">Avg. Client Lifetime</div>
          </div>
          <div class="retention-metric">
            <div class="retention-value">${{ formatNumber(metrics.ltv) }}</div>
            <div class="retention-label">Lifetime Value</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import AnalyticsChart from './AnalyticsChart.vue';

export default {
  name: 'ClientAnalytics',

  components: {
    AnalyticsChart
  },

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      dateRange: '30',
      clientSegment: 'all',
      metrics: {
        totalClients: 0,
        clientsChange: 0,
        newClients: 0,
        newClientsChange: 0,
        totalOrders: 0,
        ordersChange: 0,
        totalRevenue: 0,
        revenueChange: 0,
        retentionRate: 0,
        churnRate: 0,
        averageLifetime: 0,
        ltv: 0
      },
      clientAcquisitionData: {},
      clientEngagementData: {},
      clientSegmentsData: {},
      activityTypesData: {},
      topClients: []
    };
  },

  mounted() {
    this.loadAnalyticsData();
  },

  methods: {
    // Load analytics data
    async loadAnalyticsData() {
      this.loading = true;

      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/analytics/clients?vendor_id=${this.vendorId}&date_range=${this.dateRange}&segment=${this.clientSegment}`);
        // const data = response.data;

        // Mock data for demonstration
        setTimeout(() => {
          this.generateMockData();
          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading analytics data:', error);
        this.loading = false;
      }
    },

    // Generate mock data for demonstration
    generateMockData() {
      // Generate metrics
      this.metrics = {
        totalClients: Math.floor(Math.random() * 500) + 100,
        clientsChange: (Math.random() * 30) - 10,
        newClients: Math.floor(Math.random() * 50) + 10,
        newClientsChange: (Math.random() * 40) + 5,
        totalOrders: Math.floor(Math.random() * 1000) + 200,
        ordersChange: (Math.random() * 25) + 2,
        totalRevenue: Math.floor(Math.random() * 100000) + 20000,
        revenueChange: (Math.random() * 20) + 5,
        retentionRate: Math.floor(Math.random() * 30) + 65,
        churnRate: Math.floor(Math.random() * 20) + 5,
        averageLifetime: Math.floor(Math.random() * 24) + 6,
        ltv: Math.floor(Math.random() * 5000) + 1000
      };

      // Generate top clients
      this.topClients = [
        {
          id: '1234abcd-5678-efgh-ijkl-mnopqrstuvwx',
          name: 'John Smith',
          company: 'Acme Corporation',
          engagement: 92,
          orders: 12,
          revenue: 15750.50
        },
        {
          id: '2345bcde-6789-fghi-jklm-nopqrstuvwxy',
          name: 'Sarah Johnson',
          company: 'Johnson Interiors',
          engagement: 88,
          orders: 8,
          revenue: 9850.75
        },
        {
          id: '4567defg-8901-hijk-lmno-pqrstuvwxyza',
          name: 'Emily Davis',
          company: 'Davis Home Staging',
          engagement: 78,
          orders: 6,
          revenue: 7200.25
        },
        {
          id: '5678efgh-9012-ijkl-mnop-qrstuvwxyzab',
          name: 'Robert Wilson',
          company: 'Wilson Construction',
          engagement: 65,
          orders: 4,
          revenue: 5500.00
        },
        {
          id: '3456cdef-7890-ghij-klmn-opqrstuvwxyz',
          name: 'Michael Brown',
          company: 'Brown & Associates',
          engagement: 45,
          orders: 2,
          revenue: 2800.50
        }
      ];

      // Generate chart data
      this.updateClientAcquisitionChart('week');
      this.updateClientEngagementChart('week');
      this.updateClientSegmentsChart();
      this.updateActivityTypesChart();
    },

    // Update analytics data when filters change
    updateAnalytics() {
      this.loadAnalyticsData();
    },

    // Update client acquisition chart
    updateClientAcquisitionChart(period) {
      // This would be replaced with actual API call
      // Generate mock data for demonstration
      const labels = this.generateLabels(period);

      this.clientAcquisitionData = {
        datasets: [
          {
            label: 'New Clients',
            data: this.generateRandomData(labels.length, 1, 10),
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.2)',
            borderWidth: 2,
            tension: 0.4
          },
          {
            label: 'Returning Clients',
            data: this.generateRandomData(labels.length, 5, 20),
            borderColor: '#2ecc71',
            backgroundColor: 'rgba(46, 204, 113, 0.2)',
            borderWidth: 2,
            tension: 0.4
          }
        ]
      };
    },

    // Update client engagement chart
    updateClientEngagementChart(period) {
      // This would be replaced with actual API call
      // Generate mock data for demonstration
      const labels = this.generateLabels(period);

      this.clientEngagementData = {
        datasets: [
          {
            label: 'Visits',
            data: this.generateRandomData(labels.length, 10, 50),
            backgroundColor: 'rgba(52, 152, 219, 0.7)',
            borderColor: '#3498db',
            borderWidth: 1
          },
          {
            label: 'Orders',
            data: this.generateRandomData(labels.length, 5, 20),
            backgroundColor: 'rgba(46, 204, 113, 0.7)',
            borderColor: '#2ecc71',
            borderWidth: 1
          },
          {
            label: 'Messages',
            data: this.generateRandomData(labels.length, 15, 40),
            backgroundColor: 'rgba(155, 89, 182, 0.7)',
            borderColor: '#9b59b6',
            borderWidth: 1
          }
        ]
      };
    },

    // Update client segments chart
    updateClientSegmentsChart() {
      // This would be replaced with actual API call
      // Generate mock data for demonstration
      this.clientSegmentsData = {
        datasets: [
          {
            data: [35, 25, 15, 10, 15],
            backgroundColor: [
              'rgba(52, 152, 219, 0.7)',
              'rgba(46, 204, 113, 0.7)',
              'rgba(155, 89, 182, 0.7)',
              'rgba(241, 196, 15, 0.7)',
              'rgba(231, 76, 60, 0.7)'
            ],
            borderColor: [
              '#3498db',
              '#2ecc71',
              '#9b59b6',
              '#f1c40f',
              '#e74c3c'
            ],
            borderWidth: 1
          }
        ]
      };
    },

    // Update activity types chart
    updateActivityTypesChart() {
      // This would be replaced with actual API call
      // Generate mock data for demonstration
      this.activityTypesData = {
        datasets: [
          {
            data: [40, 25, 20, 15],
            backgroundColor: [
              'rgba(52, 152, 219, 0.7)',
              'rgba(46, 204, 113, 0.7)',
              'rgba(155, 89, 182, 0.7)',
              'rgba(241, 196, 15, 0.7)'
            ],
            borderColor: [
              '#3498db',
              '#2ecc71',
              '#9b59b6',
              '#f1c40f'
            ],
            borderWidth: 1
          }
        ]
      };
    },

    // Generate random data for charts
    generateRandomData(count, min, max) {
      return Array.from({ length: count }, () =>
        Math.floor(Math.random() * (max - min + 1)) + min
      );
    },

    // Generate labels based on period
    generateLabels(period) {
      const now = new Date();
      const labels = [];

      if (period === 'day') {
        // Last 24 hours
        for (let i = 23; i >= 0; i--) {
          const hour = new Date(now);
          hour.setHours(now.getHours() - i);
          labels.push(`${hour.getHours()}:00`);
        }
      } else if (period === 'week') {
        // Last 7 days
        for (let i = 6; i >= 0; i--) {
          const day = new Date(now);
          day.setDate(now.getDate() - i);
          labels.push(day.toLocaleDateString('en-US', { weekday: 'short' }));
        }
      } else if (period === 'month') {
        // Last 30 days
        for (let i = 29; i >= 0; i--) {
          const day = new Date(now);
          day.setDate(now.getDate() - i);
          labels.push(`${day.getDate()}/${day.getMonth() + 1}`);
        }
      } else if (period === 'year') {
        // Last 12 months
        for (let i = 11; i >= 0; i--) {
          const month = new Date(now);
          month.setMonth(now.getMonth() - i);
          labels.push(month.toLocaleDateString('en-US', { month: 'short' }));
        }
      }

      return labels;
    },

    // Format number with commas
    formatNumber(number) {
      return number.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    // Format percentage change
    formatChange(value) {
      const sign = value >= 0 ? '+' : '';
      return `${sign}${value.toFixed(1)}%`;
    },

    // Get CSS class for change value
    getChangeClass(value) {
      if (value > 0) return 'positive';
      if (value < 0) return 'negative';
      return 'neutral';
    },

    // Get icon for change value
    getChangeIcon(value) {
      if (value > 0) return 'trending_up';
      if (value < 0) return 'trending_down';
      return 'trending_flat';
    },

    // Get initials from name
    getInitials(name) {
      if (!name) return '';

      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },

    // View client details
    viewClient(client) {
      this.$emit('view-client', client);
    }
  }
};
</script>

<style scoped>
.client-analytics {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
  position: relative;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.analytics-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.analytics-controls {
  display: flex;
  gap: 15px;
}

.filter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.filter select {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-size: 14px;
}

.loading-overlay {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--theme--background-rgb), 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.metrics-row {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.metric-card {
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--primary);
}

.metric-icon i {
  font-size: 24px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 5px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: var(--theme--success);
}

.metric-change.negative {
  color: var(--theme--danger);
}

.metric-change.neutral {
  color: var(--theme--foreground-subdued);
}

.metric-change i {
  font-size: 16px;
}

.charts-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
}

.chart-container {
  flex: 1;
  min-width: 300px;
}

.chart-container.half {
  flex-basis: calc(50% - 10px);
}

.analytics-section {
  margin-bottom: 30px;
}

.analytics-section h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
}

.top-clients-list {
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.top-clients-header {
  display: flex;
  background-color: var(--theme--background-subdued);
  padding: 12px 16px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
  border-bottom: 1px solid var(--theme--border-color);
}

.top-client-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
  align-items: center;
}

.top-client-item:last-child {
  border-bottom: none;
}

.col-rank {
  width: 40px;
  font-weight: 600;
  text-align: center;
}

.col-client {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.client-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--background-subdued);
}

.client-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-avatar.small {
  width: 32px;
  height: 32px;
  font-size: 14px;
  background-color: var(--theme--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.client-info {
  min-width: 0;
}

.client-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.client-company {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.col-engagement {
  width: 150px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.engagement-bar {
  flex: 1;
  height: 6px;
  background-color: var(--theme--background-subdued);
  border-radius: 3px;
  overflow: hidden;
}

.engagement-progress {
  height: 100%;
  border-radius: 3px;
}

.engagement-progress.high {
  background-color: var(--theme--primary);
}

.engagement-progress.medium {
  background-color: var(--theme--warning);
}

.engagement-progress.low {
  background-color: var(--theme--danger);
}

.engagement-score {
  font-weight: 600;
  min-width: 30px;
  text-align: right;
}

.col-orders {
  width: 80px;
  text-align: center;
}

.col-revenue {
  width: 120px;
  text-align: right;
}

.col-actions {
  width: 60px;
  display: flex;
  justify-content: flex-end;
}

.retention-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.retention-metric {
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  text-align: center;
}

.retention-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  color: var(--theme--primary);
}

.retention-label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-icon {
  padding: 6px;
  border-radius: var(--theme--border-radius);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-icon i {
  font-size: 18px;
  margin-right: 0;
}
</style>
