/**
 * Authentication Integration Endpoints for Directus
 *
 * This extension provides custom endpoints for integrating authentication between Directus and Supabase.
 * It includes endpoints for login, user creation, role synchronization, token refresh, and security features.
 */

const { createClient } = require('@supabase/supabase-js');
const Joi = require('joi');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');

module.exports = function registerEndpoint(
  router,
  { services, exceptions, database, getSchema, logger, env },
) {
  const { UsersService, RolesService, AuthenticationService } = services;
  const {
    ServiceUnavailableException,
    ForbiddenException,
    InvalidPayloadException,
    InvalidCredentialsException,
  } = exceptions;

  // Initialize Supabase client
  const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
  const directusSecret = env.SECRET || process.env.SECRET;

  if (!supabaseUrl || !supabaseKey) {
    logger.warn(
      'Supabase credentials not set. Auth integration endpoints may not function correctly.',
    );
  }

  if (!directusSecret) {
    logger.warn('Directus secret not set. Auth integration endpoints may not function correctly.');
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  // Role mapping between Supabase and Directus
  const ROLE_MAPPING = {
    // Supabase role: Directus role name
    super_admin: 'System Admin',
    admin: 'Zone Admin',
    support_admin: 'Support Admin',
    vendor_admin: 'Vendor Admin',
    vendor_manager: 'Vendor Manager',
    vendor_editor: 'Vendor Editor',
    vendor_analyst: 'Vendor Analyst',
    client_admin: 'Client Admin',
    client_designer: 'Client Designer',
    client_registered: 'Client Registered',
  };

  // Validation schemas
  const schemas = {
    login: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required(),
      csrf_token: Joi.string(),
    }),
    createUser: Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string()
        .min(8)
        .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
        .message(
          'Password must contain at least 8 characters, including uppercase, lowercase, number and special character',
        )
        .required(),
      first_name: Joi.string().required(),
      last_name: Joi.string().required(),
      role: Joi.string().required(),
      vendor_id: Joi.string().allow(null),
    }),
    updateUser: Joi.object({
      id: Joi.string().required(),
      email: Joi.string().email(),
      first_name: Joi.string(),
      last_name: Joi.string(),
      role: Joi.string(),
      vendor_id: Joi.string().allow(null),
    }),
    refreshToken: Joi.object({
      refresh_token: Joi.string().required(),
      csrf_token: Joi.string(),
    }),
    resetPassword: Joi.object({
      email: Joi.string().email().required(),
    }),
    verifyEmail: Joi.object({
      token: Joi.string().required(),
    }),
    enableMfa: Joi.object({
      token: Joi.string().required(),
    }),
    verifyMfa: Joi.object({
      token: Joi.string().required(),
      code: Joi.string().required(),
    }),
  };

  // Helper function to validate request payload
  function validatePayload(schema, payload) {
    const { error, value } = schema.validate(payload);
    if (error) {
      throw new InvalidPayloadException(error.message);
    }
    return value;
  }

  // Helper function to get Directus role ID from name
  async function getDirectusRoleId(roleName) {
    try {
      const rolesService = new RolesService({
        schema: await getSchema(),
      });

      const roles = await rolesService.readByQuery({
        filter: { name: { _eq: roleName } },
        limit: 1,
      });

      if (roles.length === 0) {
        throw new Error(`Role not found: ${roleName}`);
      }

      return roles[0].id;
    } catch (error) {
      logger.error(`Error getting Directus role ID: ${error.message}`);
      throw error;
    }
  }

  // Helper function to create or update Directus user
  async function createOrUpdateDirectusUser(userData, isCreate = true) {
    try {
      // Get Directus role ID
      const supabaseRole = userData.role;
      const directusRoleName = ROLE_MAPPING[supabaseRole] || 'Client Registered';
      const roleId = await getDirectusRoleId(directusRoleName);

      // Prepare user data
      const directusUserData = {
        email: userData.email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        role: roleId,
        provider: 'supabase',
        external_identifier: userData.id,
        status: 'active',
      };

      // Add password for new users
      if (isCreate && userData.password) {
        directusUserData.password = userData.password;
      }

      // Create or update user
      const usersService = new UsersService({
        schema: await getSchema(),
      });

      if (isCreate) {
        // Create new user
        return await usersService.createOne(directusUserData);
      } else {
        // Find existing user
        const existingUsers = await usersService.readByQuery({
          filter: { email: { _eq: userData.email } },
          limit: 1,
        });

        if (existingUsers.length === 0) {
          throw new Error(`User not found: ${userData.email}`);
        }

        // Update existing user
        return await usersService.updateOne(existingUsers[0].id, directusUserData);
      }
    } catch (error) {
      logger.error(`Error creating/updating Directus user: ${error.message}`);
      throw error;
    }
  }

  // Endpoint: Login with Supabase and get Directus token
  router.post('/login', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.login, req.body);

      // Authenticate with Supabase
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email: payload.email,
        password: payload.password,
      });

      if (authError) {
        throw new InvalidCredentialsException('Invalid email or password');
      }

      // Get user data from Supabase
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, email, first_name, last_name, role, vendor_id')
        .eq('email', payload.email)
        .single();

      if (userError) {
        throw new ServiceUnavailableException('Error retrieving user data');
      }

      // Find or create user in Directus
      const usersService = new UsersService({
        schema: await getSchema(),
      });

      const existingUsers = await usersService.readByQuery({
        filter: { email: { _eq: payload.email } },
        limit: 1,
      });

      let directusUser;

      if (existingUsers.length === 0) {
        // Create new user in Directus
        directusUser = await createOrUpdateDirectusUser(
          {
            ...userData,
            password: payload.password,
          },
          true,
        );
      } else {
        directusUser = existingUsers[0];
      }

      // Generate Directus token
      const authenticationService = new AuthenticationService({
        schema: await getSchema(),
        accountability: {
          ip: req.ip,
          userAgent: req.get('user-agent'),
        },
      });

      const directusToken = await authenticationService.createToken(directusUser.id);

      // Return tokens and user data
      res.json({
        data: {
          supabase: {
            access_token: authData.session.access_token,
            refresh_token: authData.session.refresh_token,
            expires_at: authData.session.expires_at,
          },
          directus: {
            access_token: directusToken,
            refresh_token: null,
          },
          user: {
            id: userData.id,
            email: userData.email,
            first_name: userData.first_name,
            last_name: userData.last_name,
            role: userData.role,
            vendor_id: userData.vendor_id,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Create a new user in both Supabase and Directus
  router.post('/users', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.createUser, req.body);

      // Check if user is authorized to create users
      if (!req.accountability?.admin) {
        throw new ForbiddenException('You do not have permission to create users');
      }

      // Create user in Supabase
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: payload.email,
        password: payload.password,
        email_confirm: true,
      });

      if (authError) {
        throw new ServiceUnavailableException(
          `Error creating user in Supabase: ${authError.message}`,
        );
      }

      // Create user in Supabase users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: payload.email,
          first_name: payload.first_name,
          last_name: payload.last_name,
          role: payload.role,
          vendor_id: payload.vendor_id,
        })
        .select()
        .single();

      if (userError) {
        // Rollback Supabase auth user
        await supabase.auth.admin.deleteUser(authData.user.id);
        throw new ServiceUnavailableException(
          `Error creating user in Supabase: ${userError.message}`,
        );
      }

      // Create user in Directus
      const directusUser = await createOrUpdateDirectusUser(
        {
          ...userData,
          password: payload.password,
        },
        true,
      );

      res.json({
        data: {
          id: userData.id,
          email: userData.email,
          first_name: userData.first_name,
          last_name: userData.last_name,
          role: userData.role,
          vendor_id: userData.vendor_id,
        },
      });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Update a user in both Supabase and Directus
  router.patch('/users/:id', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.updateUser, {
        id: req.params.id,
        ...req.body,
      });

      // Check if user is authorized to update users
      if (!req.accountability?.admin && req.accountability?.user !== payload.id) {
        throw new ForbiddenException('You do not have permission to update this user');
      }

      // Update user in Supabase users table
      const updateData = {};

      if (payload.email) updateData.email = payload.email;
      if (payload.first_name) updateData.first_name = payload.first_name;
      if (payload.last_name) updateData.last_name = payload.last_name;
      if (payload.role && req.accountability?.admin) updateData.role = payload.role;
      if (payload.vendor_id !== undefined && req.accountability?.admin)
        updateData.vendor_id = payload.vendor_id;

      const { data: userData, error: userError } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', payload.id)
        .select()
        .single();

      if (userError) {
        throw new ServiceUnavailableException(
          `Error updating user in Supabase: ${userError.message}`,
        );
      }

      // Update user in Directus
      if (Object.keys(updateData).length > 0) {
        await createOrUpdateDirectusUser(userData, false);
      }

      res.json({
        data: {
          id: userData.id,
          email: userData.email,
          first_name: userData.first_name,
          last_name: userData.last_name,
          role: userData.role,
          vendor_id: userData.vendor_id,
        },
      });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Validate Supabase token and get Directus token
  router.post('/validate-token', async (req, res, next) => {
    try {
      const token = req.body.token;

      if (!token) {
        throw new InvalidPayloadException('Token is required');
      }

      // Validate Supabase token
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser(token);

      if (error) {
        throw new InvalidCredentialsException('Invalid token');
      }

      // Get user data from Supabase
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, email, first_name, last_name, role, vendor_id')
        .eq('id', user.id)
        .single();

      if (userError) {
        throw new ServiceUnavailableException('Error retrieving user data');
      }

      // Find user in Directus
      const usersService = new UsersService({
        schema: await getSchema(),
      });

      const existingUsers = await usersService.readByQuery({
        filter: { email: { _eq: userData.email } },
        limit: 1,
      });

      if (existingUsers.length === 0) {
        throw new InvalidCredentialsException('User not found in Directus');
      }

      // Generate Directus token
      const authenticationService = new AuthenticationService({
        schema: await getSchema(),
        accountability: {
          ip: req.ip,
          userAgent: req.get('user-agent'),
        },
      });

      const directusToken = await authenticationService.createToken(existingUsers[0].id);

      // Return tokens and user data
      res.json({
        data: {
          directus: {
            access_token: directusToken,
            refresh_token: null,
          },
          user: {
            id: userData.id,
            email: userData.email,
            first_name: userData.first_name,
            last_name: userData.last_name,
            role: userData.role,
            vendor_id: userData.vendor_id,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Refresh tokens
  router.post('/refresh', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.refreshToken, req.body);

      // Refresh Supabase token
      const { data, error } = await supabase.auth.refreshSession({
        refresh_token: payload.refresh_token,
      });

      if (error) {
        throw new InvalidCredentialsException('Invalid refresh token');
      }

      // Get user data from Supabase
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id, email, first_name, last_name, role, vendor_id')
        .eq('id', data.user.id)
        .single();

      if (userError) {
        throw new ServiceUnavailableException('Error retrieving user data');
      }

      // Find user in Directus
      const usersService = new UsersService({
        schema: await getSchema(),
      });

      const existingUsers = await usersService.readByQuery({
        filter: { email: { _eq: userData.email } },
        limit: 1,
      });

      if (existingUsers.length === 0) {
        throw new InvalidCredentialsException('User not found in Directus');
      }

      // Generate Directus token
      const authenticationService = new AuthenticationService({
        schema: await getSchema(),
        accountability: {
          ip: req.ip,
          userAgent: req.get('user-agent'),
        },
      });

      const directusToken = await authenticationService.createToken(existingUsers[0].id);

      // Return tokens and user data
      res.json({
        data: {
          supabase: {
            access_token: data.session.access_token,
            refresh_token: data.session.refresh_token,
            expires_at: data.session.expires_at,
          },
          directus: {
            access_token: directusToken,
            refresh_token: null,
          },
          user: {
            id: userData.id,
            email: userData.email,
            first_name: userData.first_name,
            last_name: userData.last_name,
            role: userData.role,
            vendor_id: userData.vendor_id,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Reset password
  router.post('/reset-password', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.resetPassword, req.body);

      // Send password reset email with Supabase
      const { error } = await supabase.auth.resetPasswordForEmail(payload.email, {
        redirectTo: `${req.get('origin') || env.PUBLIC_URL}/reset-password`,
      });

      if (error) {
        throw new ServiceUnavailableException(
          `Error sending password reset email: ${error.message}`,
        );
      }

      res.json({
        data: {
          message: 'Password reset email sent',
        },
      });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Verify email
  router.post('/verify-email', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.verifyEmail, req.body);

      // Verify email with Supabase
      const { error } = await supabase.auth.verifyOtp({
        token_hash: payload.token,
        type: 'email',
      });

      if (error) {
        throw new InvalidCredentialsException(`Error verifying email: ${error.message}`);
      }

      res.json({
        data: {
          message: 'Email verified successfully',
        },
      });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Enable MFA
  router.post('/enable-mfa', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.enableMfa, req.body);

      // Validate token
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser(payload.token);

      if (error) {
        throw new InvalidCredentialsException('Invalid token');
      }

      // Enable MFA for the user
      const { data, error: mfaError } = await supabase.auth.mfa.enroll({
        factorType: 'totp',
      });

      if (mfaError) {
        throw new ServiceUnavailableException(`Error enabling MFA: ${mfaError.message}`);
      }

      res.json({
        data: {
          qr_code: data.totp.qr_code,
          secret: data.totp.secret,
        },
      });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Verify MFA
  router.post('/verify-mfa', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.verifyMfa, req.body);

      // Validate token
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser(payload.token);

      if (error) {
        throw new InvalidCredentialsException('Invalid token');
      }

      // Verify MFA code
      const { data, error: mfaError } = await supabase.auth.mfa.challenge({
        factorId: user.factors[0].id,
        code: payload.code,
      });

      if (mfaError) {
        throw new InvalidCredentialsException(`Invalid MFA code: ${mfaError.message}`);
      }

      res.json({
        data: {
          message: 'MFA verified successfully',
        },
      });
    } catch (error) {
      next(error);
    }
  });

  return router;
};
