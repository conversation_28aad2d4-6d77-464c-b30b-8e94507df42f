<template>
  <div class="performance-testing-tools">
    <div class="performance-header">
      <h2 class="performance-title">Performance Testing Tools</h2>
      <div class="performance-actions">
        <button class="run-test-button" @click="runPerformanceTest" :disabled="isRunningTest">
          <i class="material-icons">play_arrow</i>
          {{ isRunningTest ? 'Running Test...' : 'Run Performance Test' }}
        </button>
        <button class="export-button" @click="exportResults" :disabled="!hasTestResults">
          <i class="material-icons">download</i>
          Export Results
        </button>
      </div>
    </div>

    <div class="performance-content">
      <div class="performance-settings">
        <h3 class="section-title">Test Settings</h3>
        <div class="settings-form">
          <div class="form-group">
            <label for="test-url">URL to Test</label>
            <input
              id="test-url"
              v-model="testUrl"
              type="text"
              placeholder="https://example.com"
            />
          </div>

          <div class="form-row">
            <div class="form-group half">
              <label for="device-type">Device Type</label>
              <select id="device-type" v-model="deviceType">
                <option value="desktop">Desktop</option>
                <option value="mobile">Mobile</option>
                <option value="tablet">Tablet</option>
              </select>
            </div>

            <div class="form-group half">
              <label for="connection-type">Connection Type</label>
              <select id="connection-type" v-model="connectionType">
                <option value="online">No Throttling</option>
                <option value="4g">Fast 4G</option>
                <option value="3g">Slow 3G</option>
                <option value="2g">Slow 2G</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="test-iterations">Test Iterations</label>
            <input
              id="test-iterations"
              v-model.number="testIterations"
              type="number"
              min="1"
              max="10"
            />
            <div class="help-text">Number of times to run the test (1-10)</div>
          </div>

          <div class="form-group">
            <label>Metrics to Measure</label>
            <div class="metrics-container">
              <div
                v-for="metric in availableMetrics"
                :key="metric.id"
                class="metric-checkbox"
              >
                <input
                  :id="`metric-${metric.id}`"
                  type="checkbox"
                  v-model="selectedMetrics"
                  :value="metric.id"
                />
                <label :for="`metric-${metric.id}`">{{ metric.name }}</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="performance-results">
        <h3 class="section-title">Test Results</h3>

        <div v-if="!hasTestResults" class="empty-state">
          <i class="material-icons">speed</i>
          <p>Run a performance test to see results</p>
        </div>

        <div v-else class="results-container">
          <!-- Performance Score -->
          <div class="performance-score">
            <div class="score-circle" :style="scoreCircleStyle">
              <span class="score-value">{{ performanceScore }}</span>
            </div>
            <div class="score-label">Performance Score</div>
          </div>

          <!-- Core Web Vitals -->
          <div class="web-vitals">
            <h4 class="vitals-title">Core Web Vitals</h4>

            <div class="vitals-grid">
              <div
                v-for="vital in coreWebVitals"
                :key="vital.id"
                class="vital-item"
                :class="getVitalScoreClass(vital.score)"
              >
                <div class="vital-header">
                  <span class="vital-name">{{ vital.name }}</span>
                  <span class="vital-score">{{ vital.value }}{{ vital.unit }}</span>
                </div>
                <div class="vital-bar">
                  <div class="vital-bar-fill" :style="{ width: `${vital.score}%` }"></div>
                </div>
                <div class="vital-description">{{ vital.description }}</div>
              </div>
            </div>
          </div>

          <!-- Asset Loading -->
          <div class="asset-loading">
            <h4 class="asset-title">Asset Loading</h4>

            <div class="asset-table">
              <div class="asset-table-header">
                <div class="asset-cell">Type</div>
                <div class="asset-cell">Count</div>
                <div class="asset-cell">Size</div>
                <div class="asset-cell">Load Time</div>
              </div>

              <div
                v-for="asset in assetLoadingData"
                :key="asset.type"
                class="asset-table-row"
              >
                <div class="asset-cell">{{ asset.type }}</div>
                <div class="asset-cell">{{ asset.count }}</div>
                <div class="asset-cell">{{ formatSize(asset.size) }}</div>
                <div class="asset-cell">{{ asset.loadTime }}ms</div>
              </div>
            </div>
          </div>

          <!-- Performance Timeline -->
          <div class="performance-timeline">
            <h4 class="timeline-title">Performance Timeline</h4>

            <div class="timeline-chart">
              <div class="timeline-markers">
                <div
                  v-for="marker in timelineMarkers"
                  :key="marker.id"
                  class="timeline-marker"
                  :style="{ left: `${marker.position}%` }"
                >
                  <div class="marker-line"></div>
                  <div class="marker-label">{{ marker.label }}</div>
                </div>
              </div>

              <div class="timeline-bars">
                <div
                  v-for="event in timelineEvents"
                  :key="event.id"
                  class="timeline-bar"
                  :style="{
                    left: `${event.start}%`,
                    width: `${event.duration}%`,
                    backgroundColor: event.color
                  }"
                  :title="`${event.name}: ${event.startTime}ms - ${event.endTime}ms (${event.endTime - event.startTime}ms)`"
                >
                  <span class="event-name">{{ event.name }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Optimization Suggestions -->
          <div class="optimization-suggestions">
            <h4 class="suggestions-title">Optimization Suggestions</h4>

            <div class="suggestions-list">
              <div
                v-for="(suggestion, index) in optimizationSuggestions"
                :key="index"
                class="suggestion-item"
                :class="{ 'high-priority': suggestion.priority === 'high' }"
              >
                <div class="suggestion-header">
                  <i class="material-icons">{{ getSuggestionIcon(suggestion.priority) }}</i>
                  <span class="suggestion-title">{{ suggestion.title }}</span>
                </div>
                <div class="suggestion-description">{{ suggestion.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue';

export default {
  name: 'PerformanceTestingTools',

  setup() {
    // Test settings
    const testUrl = ref('https://example.com');
    const deviceType = ref('desktop');
    const connectionType = ref('online');
    const testIterations = ref(3);
    const isRunningTest = ref(false);

    // Available metrics
    const availableMetrics = [
      { id: 1, name: 'Core Web Vitals' },
      { id: 2, name: 'Asset Loading' },
      { id: 3, name: 'Memory Usage' },
      { id: 4, name: 'Frame Rate' },
      { id: 5, name: 'Network Requests' }
    ];

    // Selected metrics
    const selectedMetrics = ref([1, 2, 4]);

    // Test results
    const testResults = ref(null);
    const hasTestResults = computed(() => testResults.value !== null);

    // Performance score (0-100)
    const performanceScore = computed(() => {
      if (!hasTestResults.value) return 0;
      return 87; // Mock score
    });

    // Score circle style
    const scoreCircleStyle = computed(() => {
      const score = performanceScore.value;
      let color = '#4caf50'; // Green

      if (score < 50) {
        color = '#f44336'; // Red
      } else if (score < 90) {
        color = '#ff9800'; // Orange
      }

      return {
        background: `conic-gradient(${color} ${score}%, #e0e0e0 0)`
      };
    });

    // Core Web Vitals
    const coreWebVitals = reactive([
      {
        id: 1,
        name: 'LCP',
        value: 2.4,
        unit: 's',
        score: 85,
        description: 'Largest Contentful Paint measures loading performance.'
      },
      {
        id: 2,
        name: 'FID',
        value: 28,
        unit: 'ms',
        score: 95,
        description: 'First Input Delay measures interactivity.'
      },
      {
        id: 3,
        name: 'CLS',
        value: 0.12,
        unit: '',
        score: 75,
        description: 'Cumulative Layout Shift measures visual stability.'
      },
      {
        id: 4,
        name: 'FCP',
        value: 1.2,
        unit: 's',
        score: 90,
        description: 'First Contentful Paint measures perceived load speed.'
      }
    ]);

    // Asset loading data
    const assetLoadingData = reactive([
      { type: 'HTML', count: 1, size: 45600, loadTime: 120 },
      { type: 'CSS', count: 3, size: 128400, loadTime: 180 },
      { type: 'JavaScript', count: 8, size: 856200, loadTime: 450 },
      { type: 'Images', count: 12, size: 1458000, loadTime: 680 },
      { type: 'Fonts', count: 2, size: 78400, loadTime: 210 }
    ]);

    // Timeline markers
    const timelineMarkers = reactive([
      { id: 1, label: 'Navigation Start', position: 0 },
      { id: 2, label: 'FCP', position: 25 },
      { id: 3, label: 'LCP', position: 50 },
      { id: 4, label: 'Load Complete', position: 90 }
    ]);

    // Timeline events
    const timelineEvents = reactive([
      { id: 1, name: 'DNS Lookup', start: 0, duration: 5, startTime: 0, endTime: 45, color: '#9c27b0' },
      { id: 2, name: 'TCP Connection', start: 5, duration: 7, startTime: 45, endTime: 110, color: '#2196f3' },
      { id: 3, name: 'Request', start: 12, duration: 8, startTime: 110, endTime: 180, color: '#ff9800' },
      { id: 4, name: 'Response', start: 20, duration: 10, startTime: 180, endTime: 270, color: '#4caf50' },
      { id: 5, name: 'DOM Processing', start: 30, duration: 25, startTime: 270, endTime: 500, color: '#f44336' },
      { id: 6, name: 'Resource Loading', start: 40, duration: 50, startTime: 350, endTime: 800, color: '#795548' }
    ]);

    // Optimization suggestions
    const optimizationSuggestions = reactive([
      {
        title: 'Reduce JavaScript Bundle Size',
        description: 'Your JavaScript bundle is quite large (856KB). Consider code splitting and lazy loading to improve initial load time.',
        priority: 'high'
      },
      {
        title: 'Optimize Images',
        description: 'Images account for 1.4MB of your page. Use WebP format and proper sizing to reduce image payload.',
        priority: 'high'
      },
      {
        title: 'Eliminate Render-Blocking Resources',
        description: 'Several CSS files are blocking rendering. Consider inlining critical CSS and deferring non-critical CSS.',
        priority: 'medium'
      },
      {
        title: 'Implement Resource Hints',
        description: 'Use preconnect for third-party domains and preload for critical resources to improve loading performance.',
        priority: 'medium'
      }
    ]);

    // Methods
    const runPerformanceTest = async () => {
      isRunningTest.value = true;

      try {
        // In a real implementation, this would make API calls to run performance tests
        // For now, we'll just simulate a delay
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Set test results
        testResults.value = {
          timestamp: new Date(),
          url: testUrl.value,
          device: deviceType.value,
          connection: connectionType.value,
          iterations: testIterations.value
        };
      } catch (error) {
        console.error('Error running performance test:', error);
      } finally {
        isRunningTest.value = false;
      }
    };

    const exportResults = () => {
      // In a real implementation, this would generate and download a report
      console.log('Exporting performance test results');
    };

    const formatSize = (bytes) => {
      if (bytes < 1024) return `${bytes} B`;
      if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
      return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    };

    const getVitalScoreClass = (score) => {
      if (score >= 90) return 'good';
      if (score >= 50) return 'needs-improvement';
      return 'poor';
    };

    const getSuggestionIcon = (priority) => {
      switch (priority) {
        case 'high':
          return 'priority_high';
        case 'medium':
          return 'info';
        default:
          return 'lightbulb';
      }
    };

    return {
      testUrl,
      deviceType,
      connectionType,
      testIterations,
      isRunningTest,
      availableMetrics,
      selectedMetrics,
      hasTestResults,
      performanceScore,
      scoreCircleStyle,
      coreWebVitals,
      assetLoadingData,
      timelineMarkers,
      timelineEvents,
      optimizationSuggestions,
      runPerformanceTest,
      exportResults,
      formatSize,
      getVitalScoreClass,
      getSuggestionIcon
    };
  }
};
</script>

<style scoped>
.performance-testing-tools {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
}

.performance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.performance-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.performance-actions {
  display: flex;
  gap: 8px;
}

.run-test-button,
.export-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.run-test-button {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border: none;
}

.run-test-button:hover:not(:disabled) {
  background-color: var(--theme--primary-dark);
}

.export-button {
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  border: 1px solid var(--theme--border-color);
}

.export-button:hover:not(:disabled) {
  background-color: var(--theme--background-subdued);
}

.run-test-button:disabled,
.export-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.performance-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 24px;
}

.performance-settings,
.performance-results {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 0 0 1px var(--theme--border-color);
  overflow: hidden;
}

.section-title {
  margin: 0;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid var(--theme--border-color);
}

.settings-form {
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group.half {
  flex: 1;
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
}

.help-text {
  margin-top: 4px;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.metrics-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.metric-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-checkbox input[type="checkbox"] {
  margin: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.results-container {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

/* Performance Score */
.performance-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-bottom: 8px;
}

.score-circle::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background-color: var(--theme--background);
  border-radius: 50%;
}

.score-value {
  position: relative;
  font-size: 32px;
  font-weight: 700;
}

.score-label {
  font-size: 16px;
  font-weight: 500;
}

/* Core Web Vitals */
.web-vitals {
  padding: 16px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.vitals-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.vitals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.vital-item {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 12px;
  border-left: 4px solid #ccc;
}

.vital-item.good {
  border-left-color: #4caf50;
}

.vital-item.needs-improvement {
  border-left-color: #ff9800;
}

.vital-item.poor {
  border-left-color: #f44336;
}

.vital-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.vital-name {
  font-weight: 600;
}

.vital-score {
  font-weight: 600;
}

.vital-bar {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  margin-bottom: 8px;
  overflow: hidden;
}

.vital-bar-fill {
  height: 100%;
  background-color: #4caf50;
  border-radius: 3px;
}

.vital-description {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

/* Asset Loading */
.asset-loading {
  padding: 16px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.asset-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.asset-table {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.asset-table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  background-color: var(--theme--background-subdued);
  font-weight: 600;
}

.asset-table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  border-top: 1px solid var(--theme--border-color);
}

.asset-cell {
  padding: 8px 12px;
}

/* Performance Timeline */
.performance-timeline {
  padding: 16px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.timeline-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.timeline-chart {
  height: 120px;
  position: relative;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 16px 0;
}

.timeline-markers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.timeline-marker {
  position: absolute;
  top: 0;
  height: 100%;
}

.marker-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: rgba(0, 0, 0, 0.1);
}

.marker-label {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  white-space: nowrap;
  background-color: var(--theme--background-subdued);
  padding: 2px 4px;
  border-radius: 2px;
}

.timeline-bars {
  position: relative;
  height: 100%;
}

.timeline-bar {
  position: absolute;
  height: 20px;
  border-radius: 3px;
  padding: 0 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 10px;
  color: white;
  display: flex;
  align-items: center;
}

.timeline-bar:nth-child(1) { top: 0; }
.timeline-bar:nth-child(2) { top: 25px; }
.timeline-bar:nth-child(3) { top: 50px; }
.timeline-bar:nth-child(4) { top: 75px; }
.timeline-bar:nth-child(5) { top: 25px; }
.timeline-bar:nth-child(6) { top: 50px; }

.event-name {
  font-size: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Optimization Suggestions */
.optimization-suggestions {
  padding: 16px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.suggestions-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.suggestions-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.suggestion-item {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 12px;
  border-left: 4px solid #2196f3;
}

.suggestion-item.high-priority {
  border-left-color: #f44336;
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.suggestion-header i {
  color: #2196f3;
}

.suggestion-item.high-priority .suggestion-header i {
  color: #f44336;
}

.suggestion-title {
  font-weight: 600;
}

.suggestion-description {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

/* Responsive */
@media (max-width: 1200px) {
  .performance-content {
    grid-template-columns: 1fr;
  }

  .vitals-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .vitals-grid {
    grid-template-columns: 1fr;
  }

  .asset-table-header,
  .asset-table-row {
    grid-template-columns: 1fr 1fr;
  }

  .asset-cell:nth-child(3),
  .asset-cell:nth-child(4) {
    border-top: 1px solid var(--theme--border-color);
  }
}
</style>
