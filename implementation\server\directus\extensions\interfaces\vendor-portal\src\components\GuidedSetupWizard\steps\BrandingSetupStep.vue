<template>
  <wizard-step
    title="Branding Setup"
    description="Configure your brand identity that will be displayed to clients."
    :step-data="stepData"
    :validation-schema="validationSchema"
    :help-tips="helpTips"
    @update:step-data="updateStepData"
    @validate="validateStep"
  >
    <div class="branding-setup-form">
      <div class="form-section">
        <h3 class="section-title">Brand Colors</h3>
        
        <div class="form-group">
          <label for="primary-color">Primary Color *</label>
          <div class="color-picker">
            <input
              id="primary-color"
              type="color"
              v-model="localStepData.primaryColor"
              @input="updateField('primaryColor', $event.target.value)"
            />
            <input
              type="text"
              v-model="localStepData.primaryColor"
              @input="updateField('primaryColor', $event.target.value)"
              placeholder="#000000"
            />
          </div>
        </div>
        
        <div class="form-group">
          <label for="secondary-color">Secondary Color *</label>
          <div class="color-picker">
            <input
              id="secondary-color"
              type="color"
              v-model="localStepData.secondaryColor"
              @input="updateField('secondaryColor', $event.target.value)"
            />
            <input
              type="text"
              v-model="localStepData.secondaryColor"
              @input="updateField('secondaryColor', $event.target.value)"
              placeholder="#000000"
            />
          </div>
        </div>
        
        <div class="form-group">
          <label for="accent-color">Accent Color</label>
          <div class="color-picker">
            <input
              id="accent-color"
              type="color"
              v-model="localStepData.accentColor"
              @input="updateField('accentColor', $event.target.value)"
            />
            <input
              type="text"
              v-model="localStepData.accentColor"
              @input="updateField('accentColor', $event.target.value)"
              placeholder="#000000"
            />
          </div>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Typography</h3>
        
        <div class="form-group">
          <label for="font-primary">Primary Font *</label>
          <select
            id="font-primary"
            v-model="localStepData.fontPrimary"
            @change="updateField('fontPrimary', $event.target.value)"
          >
            <option value="">Select a font</option>
            <option value="Arial, sans-serif">Arial</option>
            <option value="Helvetica, sans-serif">Helvetica</option>
            <option value="'Open Sans', sans-serif">Open Sans</option>
            <option value="'Roboto', sans-serif">Roboto</option>
            <option value="'Montserrat', sans-serif">Montserrat</option>
            <option value="'Lato', sans-serif">Lato</option>
            <option value="'Poppins', sans-serif">Poppins</option>
            <option value="Georgia, serif">Georgia</option>
            <option value="'Times New Roman', serif">Times New Roman</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="font-secondary">Secondary Font</label>
          <select
            id="font-secondary"
            v-model="localStepData.fontSecondary"
            @change="updateField('fontSecondary', $event.target.value)"
          >
            <option value="">Select a font</option>
            <option value="Arial, sans-serif">Arial</option>
            <option value="Helvetica, sans-serif">Helvetica</option>
            <option value="'Open Sans', sans-serif">Open Sans</option>
            <option value="'Roboto', sans-serif">Roboto</option>
            <option value="'Montserrat', sans-serif">Montserrat</option>
            <option value="'Lato', sans-serif">Lato</option>
            <option value="'Poppins', sans-serif">Poppins</option>
            <option value="Georgia, serif">Georgia</option>
            <option value="'Times New Roman', serif">Times New Roman</option>
          </select>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Logo Settings</h3>
        
        <div class="form-group">
          <label for="logo-placement">Logo Placement *</label>
          <select
            id="logo-placement"
            v-model="localStepData.logoPlacement"
            @change="updateField('logoPlacement', $event.target.value)"
          >
            <option value="top-left">Top Left</option>
            <option value="top-center">Top Center</option>
            <option value="top-right">Top Right</option>
          </select>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Theme Settings</h3>
        
        <div class="form-group">
          <label for="theme-mode">Theme Mode *</label>
          <div class="theme-mode-selector">
            <div 
              class="theme-mode-option"
              :class="{ active: localStepData.themeMode === 'light' }"
              @click="updateField('themeMode', 'light')"
            >
              <div class="theme-mode-preview light-mode"></div>
              <span>Light</span>
            </div>
            
            <div 
              class="theme-mode-option"
              :class="{ active: localStepData.themeMode === 'dark' }"
              @click="updateField('themeMode', 'dark')"
            >
              <div class="theme-mode-preview dark-mode"></div>
              <span>Dark</span>
            </div>
            
            <div 
              class="theme-mode-option"
              :class="{ active: localStepData.themeMode === 'auto' }"
              @click="updateField('themeMode', 'auto')"
            >
              <div class="theme-mode-preview auto-mode"></div>
              <span>Auto</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Preview</h3>
        
        <div 
          class="branding-preview"
          :style="{
            '--primary-color': localStepData.primaryColor,
            '--secondary-color': localStepData.secondaryColor,
            '--accent-color': localStepData.accentColor,
            '--font-primary': localStepData.fontPrimary,
            '--font-secondary': localStepData.fontSecondary
          }"
        >
          <div class="preview-header">
            <div class="preview-logo">LOGO</div>
            <div class="preview-nav">
              <div class="preview-nav-item">Home</div>
              <div class="preview-nav-item">Products</div>
              <div class="preview-nav-item">About</div>
              <div class="preview-nav-item">Contact</div>
            </div>
          </div>
          
          <div class="preview-content">
            <h1 class="preview-title">Welcome to Your Showroom</h1>
            <p class="preview-text">
              This is a preview of how your branding will look in the virtual showroom.
              The colors and fonts you select will be applied throughout the interface.
            </p>
            
            <button class="preview-button">Explore Products</button>
          </div>
        </div>
      </div>
    </div>
  </wizard-step>
</template>

<script>
import WizardStep from '../WizardStep.vue';

export default {
  name: 'BrandingSetupStep',
  
  components: {
    WizardStep
  },
  
  props: {
    stepData: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      localStepData: {
        primaryColor: '#3B82F6',
        secondaryColor: '#1E293B',
        accentColor: '#F59E0B',
        fontPrimary: "'Roboto', sans-serif",
        fontSecondary: "'Open Sans', sans-serif",
        logoPlacement: 'top-left',
        themeMode: 'light',
        ...this.stepData
      },
      validationSchema: {
        primaryColor: {
          required: true,
          pattern: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
          patternMessage: 'Please enter a valid hex color code',
          label: 'Primary Color'
        },
        secondaryColor: {
          required: true,
          pattern: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
          patternMessage: 'Please enter a valid hex color code',
          label: 'Secondary Color'
        },
        accentColor: {
          pattern: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
          patternMessage: 'Please enter a valid hex color code',
          label: 'Accent Color'
        },
        fontPrimary: {
          required: true,
          label: 'Primary Font'
        },
        logoPlacement: {
          required: true,
          label: 'Logo Placement'
        },
        themeMode: {
          required: true,
          label: 'Theme Mode'
        }
      },
      helpTips: [
        {
          title: 'Color Selection',
          text: 'Choose colors that match your brand identity. The primary color will be used for buttons and highlights, while the secondary color will be used for backgrounds and text.'
        },
        {
          title: 'Font Selection',
          text: 'Select fonts that are easy to read and match your brand style. The primary font will be used for headings, while the secondary font will be used for body text.'
        }
      ]
    };
  },
  
  methods: {
    updateStepData(data) {
      this.localStepData = { ...data };
      this.$emit('update:step-data', this.localStepData);
    },
    
    updateField(field, value) {
      this.localStepData[field] = value;
      this.$emit('update:step-data', this.localStepData);
    },
    
    validateStep(isValid) {
      this.$emit('validate', isValid);
    }
  }
};
</script>

<style scoped>
.branding-setup-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--theme--foreground);
}

.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--theme--foreground);
}

.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group select:focus {
  border-color: var(--theme--primary);
  outline: none;
}

.color-picker {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-picker input[type="color"] {
  width: 40px;
  height: 40px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  padding: 0;
  cursor: pointer;
}

.color-picker input[type="text"] {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.color-picker input[type="text"]:focus {
  border-color: var(--theme--primary);
  outline: none;
}

.theme-mode-selector {
  display: flex;
  gap: 16px;
}

.theme-mode-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.theme-mode-preview {
  width: 80px;
  height: 60px;
  border-radius: 4px;
  border: 1px solid var(--theme--border-color);
  transition: all 0.2s ease;
}

.theme-mode-option.active .theme-mode-preview {
  border-color: var(--theme--primary);
  box-shadow: 0 0 0 2px var(--theme--primary-background), 0 0 0 4px var(--theme--primary);
}

.light-mode {
  background-color: #FFFFFF;
}

.dark-mode {
  background-color: #1E293B;
}

.auto-mode {
  background: linear-gradient(to right, #FFFFFF 50%, #1E293B 50%);
}

.theme-mode-option span {
  font-size: 14px;
}

.branding-preview {
  background-color: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--secondary-color);
  color: #FFFFFF;
}

.preview-logo {
  font-family: var(--font-primary);
  font-weight: bold;
  font-size: 20px;
}

.preview-nav {
  display: flex;
  gap: 16px;
}

.preview-nav-item {
  font-family: var(--font-primary);
  cursor: pointer;
}

.preview-content {
  padding: 32px 24px;
}

.preview-title {
  font-family: var(--font-primary);
  font-size: 24px;
  margin: 0 0 16px 0;
  color: var(--secondary-color);
}

.preview-text {
  font-family: var(--font-secondary);
  font-size: 14px;
  margin: 0 0 24px 0;
  color: #4B5563;
}

.preview-button {
  font-family: var(--font-primary);
  padding: 10px 16px;
  background-color: var(--primary-color);
  color: #FFFFFF;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preview-button:hover {
  opacity: 0.9;
}
</style>
