import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import GuidedSetupWizard from '../GuidedSetupWizard.vue';
import WizardContainer from '../WizardContainer.vue';
import CompanyProfileStep from '../steps/CompanyProfileStep.vue';
import UserAccountStep from '../steps/UserAccountStep.vue';
import BrandingSetupStep from '../steps/BrandingSetupStep.vue';
import ProductUploadStep from '../steps/ProductUploadStep.vue';
import ShowroomConfigStep from '../steps/ShowroomConfigStep.vue';
import CompletionStep from '../steps/CompletionStep.vue';
import GuidedSetupService from '../../../services/GuidedSetupService';

// Mock the components to avoid rendering issues in tests
vi.mock('../WizardContainer.vue', () => ({
  default: {
    name: '<PERSON><PERSON>ontainer',
    render: h => h('div'),
    props: [
      'title',
      'description',
      'steps',
      'initialStepIndex',
      'allowStepNavigation',
      'showBackButton',
      'showSaveButton',
      'showHelpSection',
      'nextButtonText',
      'finishButtonText',
      'storageKey',
    ],
  },
}));

vi.mock('../steps/CompanyProfileStep.vue', () => ({
  default: {
    name: 'CompanyProfileStep',
    render: h => h('div'),
    props: ['stepData'],
  },
}));

vi.mock('../steps/UserAccountStep.vue', () => ({
  default: {
    name: 'UserAccountStep',
    render: h => h('div'),
    props: ['stepData'],
  },
}));

vi.mock('../steps/BrandingSetupStep.vue', () => ({
  default: {
    name: 'BrandingSetupStep',
    render: h => h('div'),
    props: ['stepData'],
  },
}));

vi.mock('../steps/ProductUploadStep.vue', () => ({
  default: {
    name: 'ProductUploadStep',
    render: h => h('div'),
    props: ['stepData'],
  },
}));

vi.mock('../steps/ShowroomConfigStep.vue', () => ({
  default: {
    name: 'ShowroomConfigStep',
    render: h => h('div'),
    props: ['stepData'],
  },
}));

vi.mock('../steps/CompletionStep.vue', () => ({
  default: {
    name: 'CompletionStep',
    render: h => h('div'),
    props: ['stepData'],
  },
}));

// Mock the GuidedSetupService
vi.mock('../../../services/GuidedSetupService', () => ({
  default: {
    getOnboardingStatus: vi.fn(),
    saveOnboardingStatus: vi.fn(),
    trackWizardAnalytics: vi.fn(),
  },
}));

describe('GuidedSetupWizard', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(GuidedSetupWizard, {
      propsData: {
        vendorId: 'test-vendor-id',
      },
    });
  });

  afterEach(() => {
    wrapper.destroy();
    vi.clearAllMocks();
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('renders the WizardContainer component', () => {
    expect(wrapper.findComponent(WizardContainer).exists()).toBe(true);
  });

  it('passes the correct props to WizardContainer', () => {
    const wizardContainer = wrapper.findComponent(WizardContainer);

    expect(wizardContainer.props('title')).toBe('Vendor Onboarding Wizard');
    expect(wizardContainer.props('steps')).toHaveLength(6);
    expect(wizardContainer.props('storageKey')).toBe('vendor-onboarding-wizard');
  });

  it('has the correct number of steps', () => {
    expect(wrapper.vm.wizardSteps).toHaveLength(6);
    expect(wrapper.vm.wizardSteps[0].component).toBe(CompanyProfileStep);
    expect(wrapper.vm.wizardSteps[1].component).toBe(UserAccountStep);
    expect(wrapper.vm.wizardSteps[2].component).toBe(BrandingSetupStep);
    expect(wrapper.vm.wizardSteps[3].component).toBe(ProductUploadStep);
    expect(wrapper.vm.wizardSteps[4].component).toBe(ShowroomConfigStep);
    expect(wrapper.vm.wizardSteps[5].component).toBe(CompletionStep);
  });

  it('loads onboarding status when mounted', async () => {
    // Arrange
    const mockStatus = {
      id: 'status-id',
      vendor_id: 'test-vendor-id',
      is_completed: false,
      progress_data: JSON.stringify({
        currentStepIndex: 2,
        stepsData: {
          0: { companyName: 'Test Company' },
          1: { adminUser: { firstName: 'John' } },
        },
      }),
    };

    GuidedSetupService.getOnboardingStatus.mockResolvedValue(mockStatus);

    // Act
    const wrapper = mount(GuidedSetupWizard, {
      propsData: {
        vendorId: 'test-vendor-id',
      },
    });

    // Wait for promises to resolve
    await wrapper.vm.$nextTick();

    // Assert
    expect(GuidedSetupService.getOnboardingStatus).toHaveBeenCalledWith('test-vendor-id');
    expect(wrapper.emitted('load-progress')).toBeTruthy();
    expect(wrapper.emitted('load-progress')[0][0].vendorId).toBe('test-vendor-id');
  });

  it('handles wizard completion', async () => {
    // Arrange
    const completionData = {
      stepsData: {
        0: { companyName: 'Test Company' },
        1: { adminUser: { firstName: 'John' } },
      },
    };

    GuidedSetupService.saveOnboardingStatus.mockResolvedValue({
      id: 'status-id',
      vendor_id: 'test-vendor-id',
      is_completed: true,
    });

    GuidedSetupService.trackWizardAnalytics.mockResolvedValue({
      id: 'analytics-id',
    });

    // Act
    await wrapper.vm.handleWizardComplete(completionData);

    // Assert
    expect(GuidedSetupService.saveOnboardingStatus).toHaveBeenCalledWith(
      'test-vendor-id',
      true,
      completionData,
    );

    expect(GuidedSetupService.trackWizardAnalytics).toHaveBeenCalledWith('test-vendor-id', {
      eventType: 'wizard_completed',
      eventData: expect.objectContaining({
        completedAt: expect.any(String),
      }),
    });

    expect(wrapper.emitted('complete')).toBeTruthy();
    expect(wrapper.emitted('complete')[0][0].vendorId).toBe('test-vendor-id');
    expect(wrapper.emitted('complete')[0][0].setupData).toEqual(completionData);
  });

  it('handles save progress', async () => {
    // Arrange
    const progressData = {
      currentStepIndex: 1,
      stepsData: {
        0: { companyName: 'Test Company' },
      },
    };

    GuidedSetupService.saveOnboardingStatus.mockResolvedValue({
      id: 'status-id',
      vendor_id: 'test-vendor-id',
      is_completed: false,
    });

    // Act
    await wrapper.vm.handleSaveProgress(progressData);

    // Assert
    expect(GuidedSetupService.saveOnboardingStatus).toHaveBeenCalledWith(
      'test-vendor-id',
      false,
      progressData,
    );

    expect(wrapper.emitted('save-progress')).toBeTruthy();
    expect(wrapper.emitted('save-progress')[0][0].vendorId).toBe('test-vendor-id');
    expect(wrapper.emitted('save-progress')[0][0].progressData).toEqual(progressData);
  });

  it('handles analytics events', () => {
    // Arrange
    const eventData = {
      type: 'step_view',
      data: {
        stepIndex: 1,
        stepTitle: 'User Accounts',
      },
    };

    GuidedSetupService.trackWizardAnalytics.mockResolvedValue({
      id: 'analytics-id',
    });

    // Act
    wrapper.vm.handleAnalyticsEvent(eventData);

    // Assert
    expect(GuidedSetupService.trackWizardAnalytics).toHaveBeenCalledWith('test-vendor-id', {
      eventType: eventData.type,
      eventData: eventData.data,
    });
  });

  it('handles exit button click', () => {
    // Act
    wrapper.vm.handleExit();

    // Assert
    expect(wrapper.emitted('exit')).toBeTruthy();
  });
});
