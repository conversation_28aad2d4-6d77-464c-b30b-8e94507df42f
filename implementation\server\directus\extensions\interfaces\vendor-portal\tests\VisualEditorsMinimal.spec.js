import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Create a minimal VisualEditors component for testing
const MinimalVisualEditors = {
  name: 'MinimalVisualEditors',
  props: {
    vendorId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      activeTab: 'layout',
      tabs: [
        { id: 'layout', label: 'Showroom Layout', icon: 'dashboard' },
        { id: 'product', label: 'Product Config', icon: 'settings' },
        { id: 'material', label: 'Materials', icon: 'palette' },
        { id: 'lighting', label: 'Lighting', icon: 'lightbulb' },
        { id: 'animation', label: 'Animation', icon: 'movie' },
      ],
      loading: false,
      error: null,
    };
  },
  methods: {
    setActiveTab(tabId) {
      this.activeTab = tabId;
    },
  },
  template: `
    <div class="visual-editors">
      <div class="visual-editors-header">
        <h2 class="visual-editors-title">Visual Editors</h2>
        <div class="visual-editors-tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            class="tab-button"
            :class="{ active: activeTab === tab.id }"
            @click="setActiveTab(tab.id)"
          >
            <span>{{ tab.label }}</span>
          </button>
        </div>
      </div>
      <div class="visual-editors-content">
        <div v-if="activeTab === 'layout'" class="editor-container">
          <div class="mock-showroom-layout-editor">Showroom Layout Editor</div>
        </div>
        <div v-if="activeTab === 'product'" class="editor-container">
          <div class="mock-product-configurator">Product Configurator</div>
        </div>
        <div v-if="activeTab === 'material'" class="editor-container">
          <div class="mock-material-texture-editor">Material Texture Editor</div>
        </div>
        <div v-if="activeTab === 'lighting'" class="editor-container">
          <div class="mock-lighting-editor">Lighting Editor</div>
        </div>
        <div v-if="activeTab === 'animation'" class="editor-container">
          <div class="mock-animation-editor">Animation Editor</div>
        </div>
      </div>
    </div>
  `,
};

// Mock the API
const mockApi = {
  get: vi.fn(),
};

describe('VisualEditors Minimal Test', () => {
  let wrapper;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock API responses
    mockApi.get.mockResolvedValue({
      data: {
        data: [],
      },
    });

    // Mount component
    wrapper = mount(MinimalVisualEditors, {
      propsData: {
        vendorId: 'vendor1',
      },
      mocks: {
        $api: mockApi,
      },
    });
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.visual-editors').exists()).toBe(true);
  });

  it('renders the tabs correctly', () => {
    const tabs = wrapper.findAll('.tab-button');
    expect(tabs.length).toBe(5);

    // Check tab names using at() method for Vue Test Utils v1
    expect(tabs.at(0).text()).toContain('Showroom Layout');
    expect(tabs.at(1).text()).toContain('Product Config');
    expect(tabs.at(2).text()).toContain('Materials');
    expect(tabs.at(3).text()).toContain('Lighting');
    expect(tabs.at(4).text()).toContain('Animation');
  });

  it('has the correct initial active tab', () => {
    expect(wrapper.vm.activeTab).toBe('layout');
  });

  it('changes active tab when tab is clicked', async () => {
    // Initial tab should be 'layout'
    expect(wrapper.vm.activeTab).toBe('layout');

    // Click on the product tab
    await wrapper.findAll('.tab-button').at(1).trigger('click');
    expect(wrapper.vm.activeTab).toBe('product');

    // Click on the material tab
    await wrapper.findAll('.tab-button').at(2).trigger('click');
    expect(wrapper.vm.activeTab).toBe('material');
  });

  it('shows the correct editor component based on active tab', async () => {
    // Initial tab is 'layout', so ShowroomLayoutEditor should be visible
    expect(wrapper.find('.mock-showroom-layout-editor').exists()).toBe(true);

    // Change to product tab
    await wrapper.findAll('.tab-button').at(1).trigger('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.find('.mock-product-configurator').exists()).toBe(true);

    // Change to material tab
    await wrapper.findAll('.tab-button').at(2).trigger('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.find('.mock-material-texture-editor').exists()).toBe(true);
  });
});
