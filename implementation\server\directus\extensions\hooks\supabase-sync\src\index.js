/**
 * Supabase Sync Hook for Directus
 *
 * This hook synchronizes data between Directus and Supabase.
 * It listens for create, update, and delete events on specific collections
 * and replicates those changes to the corresponding Supabase tables.
 */

const { createClient } = require('@supabase/supabase-js');

// Collections to sync with Supabase
const COLLECTIONS_TO_SYNC = [
  'showroom_layouts',
  'interaction_behaviors',
  'ui_elements',
  'vendor_branding',
  'product_displays',
  'transitions',
  'actions',
];

// Mapping between Directus collections and Supabase tables (if different)
const COLLECTION_TABLE_MAP = {
  // 'directus_collection_name': 'supabase_table_name'
};

module.exports = function registerHook({ env, logger, database }) {
  // Initialize Supabase client
  const supabaseUrl = env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    logger.warn('Supabase credentials not set. Supabase sync hook will not function.');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  // Helper function to get the corresponding Supabase table name
  function getTableName(collection) {
    return COLLECTION_TABLE_MAP[collection] || collection;
  }

  // Helper function to log and handle errors
  function handleError(operation, collection, error) {
    logger.error(`Supabase sync error during ${operation} on ${collection}: ${error.message}`);
    logger.debug(error.stack);
  }

  // Register hooks for each collection
  for (const collection of COLLECTIONS_TO_SYNC) {
    // Create hook
    const createHook = async (payload, meta) => {
      try {
        const tableName = getTableName(collection);
        const { error } = await supabase.from(tableName).insert(payload);

        if (error) throw error;
        logger.info(`Synced new item to Supabase: ${tableName} (ID: ${payload.id})`);
      } catch (error) {
        handleError('create', collection, error);
      }
    };

    // Update hook
    const updateHook = async (payload, meta) => {
      try {
        const tableName = getTableName(collection);
        const { error } = await supabase.from(tableName).update(payload).eq('id', payload.id);

        if (error) throw error;
        logger.info(`Synced update to Supabase: ${tableName} (ID: ${payload.id})`);
      } catch (error) {
        handleError('update', collection, error);
      }
    };

    // Delete hook
    const deleteHook = async (payload, meta) => {
      try {
        const tableName = getTableName(collection);
        const { error } = await supabase.from(tableName).delete().eq('id', payload.id);

        if (error) throw error;
        logger.info(`Synced deletion to Supabase: ${tableName} (ID: ${payload.id})`);
      } catch (error) {
        handleError('delete', collection, error);
      }
    };

    // Register the hooks
    return {
      'items.create': async (payload, meta) => {
        if (meta.collection === collection) {
          await createHook(payload, meta);
        }
      },
      'items.update': async (payload, meta) => {
        if (meta.collection === collection) {
          await updateHook(payload, meta);
        }
      },
      'items.delete': async (payload, meta) => {
        if (meta.collection === collection) {
          await deleteHook(payload, meta);
        }
      },
    };
  }
};
