import React, { useState } from 'react';
import { GetServerSideProps } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import {
  Box,
  Breadcrumbs,
  Button,
  Card,
  CardContent,
  Container,
  Divider,
  Grid,
  Link,
  Paper,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import { Code, Home } from '@mui/icons-material';
import { AdminLayout } from '../components/AdminLayout';
import { BlueprintList } from '../components/BlueprintList';
import { BlueprintEditor } from '../components/BlueprintEditor';
import { Blueprint } from '../../shared/models/blueprint';

interface BlueprintsPageProps {
  vendors: any[];
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`blueprint-tabpanel-${index}`}
      aria-labelledby={`blueprint-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

export default function BlueprintsPage({ vendors }: BlueprintsPageProps) {
  const supabase = useSupabaseClient();
  const [tabValue, setTabValue] = useState<number>(0);
  const [selectedVendor, setSelectedVendor] = useState<string | null>(null);
  const [selectedBlueprint, setSelectedBlueprint] = useState<Blueprint | null>(null);
  const [isCreating, setIsCreating] = useState<boolean>(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSelectBlueprint = (blueprint: Blueprint) => {
    setSelectedBlueprint(blueprint);
    setTabValue(1); // Switch to detail tab
  };

  const handleCreateBlueprint = () => {
    setSelectedBlueprint(null);
    setIsCreating(true);
    setTabValue(1); // Switch to detail tab
  };

  const handleSaveBlueprint = (blueprint: Blueprint) => {
    setSelectedBlueprint(blueprint);
    setIsCreating(false);
    setTabValue(0); // Switch back to list tab
  };

  const handleCancelEdit = () => {
    setIsCreating(false);
    setTabValue(0); // Switch back to list tab
  };

  return (
    <AdminLayout title="Blueprint Management">
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ mb: 4 }}>
          <Breadcrumbs aria-label="breadcrumb">
            <Link
              underline="hover"
              color="inherit"
              href="/admin"
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <Home sx={{ mr: 0.5 }} fontSize="inherit" />
              Dashboard
            </Link>
            <Typography sx={{ display: 'flex', alignItems: 'center' }} color="text.primary">
              <Code sx={{ mr: 0.5 }} fontSize="inherit" />
              Blueprints
            </Typography>
          </Breadcrumbs>
          <Typography variant="h4" component="h1" sx={{ mt: 2 }}>
            Blueprint Management
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
            Create and manage behavior blueprints for your VR experiences
          </Typography>
        </Box>

        <Paper sx={{ width: '100%', mb: 4 }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="blueprint tabs">
              <Tab label="Blueprint List" />
              <Tab label="Blueprint Details" disabled={!selectedBlueprint && !isCreating} />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 3 }}>
              <Box sx={{ flex: { xs: '1 1 100%', md: '0 0 25%' } }}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Vendors
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Button
                        variant={selectedVendor === null ? 'contained' : 'outlined'}
                        onClick={() => setSelectedVendor(null)}
                      >
                        All Vendors
                      </Button>
                      {vendors.map(vendor => (
                        <Button
                          key={vendor.id}
                          variant={selectedVendor === vendor.id ? 'contained' : 'outlined'}
                          onClick={() => setSelectedVendor(vendor.id)}
                        >
                          {vendor.name}
                        </Button>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Box>
              <Box sx={{ flex: { xs: '1 1 100%', md: '0 0 70%' } }}>
                <BlueprintList
                  vendorId={selectedVendor || undefined}
                  onSelectBlueprint={handleSelectBlueprint}
                />
              </Box>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            {isCreating ? (
              <BlueprintEditor
                vendorId={selectedVendor || vendors[0]?.id}
                onSave={handleSaveBlueprint}
                onCancel={handleCancelEdit}
              />
            ) : selectedBlueprint ? (
              <BlueprintEditor
                vendorId={selectedBlueprint.vendor_id}
                blueprintId={selectedBlueprint.id}
                onSave={handleSaveBlueprint}
                onCancel={handleCancelEdit}
              />
            ) : (
              <Typography>No blueprint selected</Typography>
            )}
          </TabPanel>
        </Paper>
      </Container>
    </AdminLayout>
  );
}

export const getServerSideProps: GetServerSideProps = async context => {
  const supabase = createServerSupabaseClient(context);

  // Check if user is authenticated
  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    return {
      redirect: {
        destination: '/admin/login',
        permanent: false,
      },
    };
  }

  // Fetch vendors
  const { data: vendors } = await supabase.from('vendors').select('id, name');

  return {
    props: {
      vendors: vendors || [],
    },
  };
};
