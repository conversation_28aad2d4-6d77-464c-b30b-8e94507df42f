import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { SceneBlueprintValidatorService } from '../../../services/scene/scene-blueprint-validator';

// Initialize scene blueprint validator service
const sceneBlueprintValidator = new SceneBlueprintValidatorService(supabase);

/**
 * Validate blueprints in a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const validateSceneBlueprints = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Validate scene blueprints
    const result = await sceneBlueprintValidator.validateSceneBlueprints(scene_id);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating scene blueprints', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Validate blueprint instances
 *
 * @param req - Request
 * @param res - Response
 */
export const validateBlueprintInstances = async (req: Request, res: Response): Promise<void> => {
  try {
    const { instances } = req.body;

    // Validate parameters
    if (!instances || !Array.isArray(instances) || instances.length === 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INSTANCES',
          message: 'Blueprint instances are required',
        },
      });
      return;
    }

    // Validate blueprint instances
    const result = await sceneBlueprintValidator.validateBlueprintInstances(instances);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating blueprint instances', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Check blueprint compatibility
 *
 * @param req - Request
 * @param res - Response
 */
export const checkBlueprintCompatibility = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { target_environment } = req.query;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!target_environment || typeof target_environment !== 'string') {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_TARGET_ENVIRONMENT',
          message: 'Target environment is required',
        },
      });
      return;
    }

    // Check blueprint compatibility
    const result = await sceneBlueprintValidator.checkBlueprintCompatibility(
      scene_id,
      target_environment,
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error checking blueprint compatibility', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on method and path
    if (req.method === 'GET' && req.params.scene_id && req.query.action === 'compatibility') {
      await checkBlueprintCompatibility(req, res);
    } else if (req.method === 'GET' && req.params.scene_id) {
      await validateSceneBlueprints(req, res);
    } else if (req.method === 'POST') {
      await validateBlueprintInstances(req, res);
    } else {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
    }
  } catch (error) {
    logger.error('Error in blueprint validation handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
}
