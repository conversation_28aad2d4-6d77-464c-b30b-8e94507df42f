# Docker Image Export and Upload Guide for DigitalOcean

## Overview

This guide provides comprehensive instructions for exporting Docker images from your local development environment and deploying them to DigitalOcean. This approach is ideal for:

- Environments with limited internet bandwidth
- Offline or air-gapped deployments
- Consistent deployments across multiple servers
- Backup and disaster recovery scenarios

## Prerequisites

### Local Development Environment
- Docker and Docker Compose installed
- MVS-VR v2 project with all Dockerfiles
- Sufficient disk space (minimum 10GB recommended)
- Network access for optional registry operations

### DigitalOcean Environment
- DigitalOcean Droplet with Docker installed
- SSH access to the server
- Sufficient storage space for images
- Optional: DigitalOcean Container Registry access

## Quick Start

### 1. Export Images Locally
```bash
# Navigate to project directory
cd mvs-vr-v2

# Run the export script
./scripts/export-docker-images.sh

# Or with custom options
./scripts/export-docker-images.sh --version v1.0.0 --compress
```

### 2. Upload to DigitalOcean
```bash
# Upload using rsync (recommended)
rsync -avz --progress docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/

# Or using SCP
scp -r docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/
```

### 3. Deploy on Server
```bash
# Connect to server
ssh root@YOUR_SERVER_IP

# Navigate to uploaded files
cd /opt/mvs-vr/docker-exports

# Deploy services
./deploy-to-digitalocean.sh
```

## Detailed Export Process

### Export Script Options

The `export-docker-images.sh` script supports various options:

```bash
# Basic export with compression
./scripts/export-docker-images.sh --compress

# Export with custom version tag
./scripts/export-docker-images.sh --version v1.2.3

# Export and push to DigitalOcean Container Registry
./scripts/export-docker-images.sh --push-registry

# Skip building (use existing images)
./scripts/export-docker-images.sh --skip-build

# Dry run to see what would happen
./scripts/export-docker-images.sh --dry-run

# Verbose output for debugging
./scripts/export-docker-images.sh --verbose

# Clean up local images after export
./scripts/export-docker-images.sh --cleanup
```

### Export Output Structure

After running the export script, you'll find:

```
docker-exports/
├── api-gateway-20240101-120000.tar.gz
├── asset-service-20240101-120000.tar.gz
├── scene-service-20240101-120000.tar.gz
├── blueprint-service-20240101-120000.tar.gz
├── llm-service-20240101-120000.tar.gz
├── auth-service-20240101-120000.tar.gz
├── analytics-service-20240101-120000.tar.gz
├── monitoring-service-20240101-120000.tar.gz
├── directus-20240101-120000.tar.gz
├── export-manifest.json
├── load-images.sh
├── deploy-to-digitalocean.sh
└── UPLOAD_INSTRUCTIONS.md
```

### Export Manifest

The `export-manifest.json` contains metadata about exported images:

```json
{
  "export_date": "2024-01-01T12:00:00Z",
  "version": "20240101-120000",
  "images": {
    "api-gateway": {
      "file": "api-gateway-20240101-120000.tar.gz",
      "size": 524288000,
      "image_id": "sha256:abc123...",
      "tags": ["20240101-120000", "latest"]
    }
  }
}
```

## Upload Methods

### Method 1: Direct Upload (SCP/RSYNC)

**Advantages:**
- Simple and straightforward
- Works with any server setup
- No additional infrastructure required

**Best for:**
- One-time deployments
- Small to medium-sized images
- Direct server access

```bash
# Using RSYNC (recommended for large files)
rsync -avz --progress --partial docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/

# Using SCP
scp -r docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/

# For very large files, consider compression
tar czf mvs-vr-images.tar.gz docker-exports/
scp mvs-vr-images.tar.gz root@YOUR_SERVER_IP:/opt/mvs-vr/
```

### Method 2: DigitalOcean Container Registry

**Advantages:**
- Centralized image storage
- Version management
- Multiple server deployments
- Integrated with DigitalOcean ecosystem

**Best for:**
- Production deployments
- Multiple environments
- Team collaboration

```bash
# Setup DigitalOcean Container Registry
doctl registry create mvs-vr

# Export and push to registry
./scripts/export-docker-images.sh --push-registry

# On server, pull from registry
docker-compose -f docker-compose.registry.yml up -d
```

### Method 3: DigitalOcean Spaces (Object Storage)

**Advantages:**
- Cost-effective for large files
- CDN integration available
- Backup and archival capabilities

**Best for:**
- Large image archives
- Backup scenarios
- Multiple region deployments

```bash
# Upload to Spaces using s3cmd
s3cmd put docker-exports/*.tar.gz s3://your-space/mvs-vr-images/

# Download on server
wget https://your-space.nyc3.digitaloceanspaces.com/mvs-vr-images/api-gateway-VERSION.tar.gz
```

## Deployment on DigitalOcean

### Server Preparation

Ensure your DigitalOcean Droplet is properly configured:

```bash
# Update system
apt update && apt upgrade -y

# Install Docker if not already installed
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create application directory
mkdir -p /opt/mvs-vr
cd /opt/mvs-vr
```

### Loading Images

The exported package includes a `load-images.sh` script:

```bash
# Make script executable
chmod +x load-images.sh

# Load all images
./load-images.sh

# Verify images are loaded
docker images | grep mvs-vr-v2
```

### Deployment Options

#### Option 1: Using Exported Images
```bash
# Use the deployment script
./deploy-to-digitalocean.sh

# Or manually with docker-compose
docker-compose -f docker-compose.yml -f docker-compose.exported.yml up -d
```

#### Option 2: Using Registry Images
```bash
# Login to DigitalOcean Container Registry
doctl registry login

# Deploy from registry
docker-compose -f docker-compose.registry.yml up -d
```

## Verification and Testing

### Health Checks

After deployment, verify all services are running:

```bash
# Check service status
docker-compose ps

# API health check
curl http://localhost:3000/health

# Directus health check
curl http://localhost:8055/server/health

# Database connectivity
docker-compose exec postgres pg_isready -U postgres

# Redis connectivity
docker-compose exec redis redis-cli ping
```

### Service Logs

Monitor service logs for any issues:

```bash
# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f api-gateway
docker-compose logs -f directus

# Check for errors
docker-compose logs | grep -i error
```

### Performance Monitoring

```bash
# Check resource usage
docker stats

# Monitor disk usage
df -h

# Check network connectivity
netstat -tlnp | grep :3000
```

## Troubleshooting

### Common Issues

#### Large File Upload Problems
```bash
# Use rsync with resume capability
rsync -avz --progress --partial --inplace docker-exports/ root@SERVER:/opt/mvs-vr/docker-exports/

# Split large files if needed
split -b 1G large-image.tar.gz image-part-
```

#### Image Loading Failures
```bash
# Check available disk space
df -h

# Clean up old images
docker system prune -a

# Manually load specific image
docker load -i api-gateway-VERSION.tar.gz
```

#### Service Startup Issues
```bash
# Check Docker daemon status
systemctl status docker

# Restart Docker if needed
systemctl restart docker

# Check for port conflicts
netstat -tlnp | grep :3000
```

### Performance Optimization

#### Image Size Reduction
```bash
# Use multi-stage builds in Dockerfiles
# Remove unnecessary packages and files
# Use alpine-based images where possible
```

#### Upload Optimization
```bash
# Compress exports (enabled by default)
./scripts/export-docker-images.sh --compress

# Use parallel uploads
parallel -j4 scp {} root@SERVER:/opt/mvs-vr/docker-exports/ ::: *.tar.gz
```

## Security Considerations

### Image Security
- Scan images for vulnerabilities before export
- Use minimal base images
- Keep images updated with security patches
- Remove sensitive data from images

### Transfer Security
- Use SSH keys instead of passwords
- Encrypt sensitive data in transit
- Verify image checksums after transfer
- Use secure networks for uploads

### Server Security
- Configure firewall rules properly
- Use non-root users where possible
- Enable Docker security features
- Monitor access logs

## Automation and CI/CD

### Automated Exports
```bash
# Create scheduled exports
crontab -e
# Add: 0 2 * * 0 /path/to/mvs-vr-v2/scripts/export-docker-images.sh --compress
```

### Integration with CI/CD
```yaml
# Example GitHub Actions workflow
name: Export and Deploy
on:
  push:
    tags: ['v*']
jobs:
  export-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Export Images
        run: ./scripts/export-docker-images.sh --version ${{ github.ref_name }}
      - name: Upload to Server
        run: rsync -avz docker-exports/ ${{ secrets.SERVER_USER }}@${{ secrets.SERVER_IP }}:/opt/mvs-vr/docker-exports/
```

## Best Practices

### Development Workflow
1. Test locally before export
2. Use semantic versioning for releases
3. Document changes in each export
4. Maintain export archives for rollback

### Production Deployment
1. Always backup before deployment
2. Use blue-green deployment strategies
3. Monitor services after deployment
4. Have rollback procedures ready

### Maintenance
1. Regular cleanup of old images
2. Monitor disk usage
3. Update base images regularly
4. Review and update security settings

## Next Steps

After successful deployment:

1. **Configure Monitoring**: Set up Grafana dashboards and alerts
2. **SSL Certificates**: Install and configure SSL certificates
3. **Domain Configuration**: Point your domain to the server
4. **Backup Strategy**: Implement regular backup procedures
5. **Performance Tuning**: Optimize based on usage patterns

For additional help, refer to:
- [DigitalOcean Step-by-Step Guide](./digitalocean-step-by-step.md)
- [Deployment Checklist](./deployment-checklist.md)
- [Troubleshooting Guide](./troubleshooting.md)
