// Admin Dashboard Components
import AdminDashboard from './AdminDashboard.vue';
import {
  DashboardWidget,
  StatsWidget,
  ChartWidget,
  ActivityWidget,
  SystemWidget,
} from './Dashboard';

// User Management Components
import { UserManagement, UserDetailDialog, UserFormDialog } from './UserManagement';

// System Monitoring Components
import { SystemStatusOverview, AlertSummary } from './SystemMonitoring';

// Export all components
export {
  // Admin Dashboard
  AdminDashboard,
  DashboardWidget,
  StatsWidget,
  ChartWidget,
  ActivityWidget,
  SystemWidget,

  // User Management
  UserManagement,
  UserDetailDialog,
  UserFormDialog,

  // System Monitoring
  SystemStatusOverview,
  AlertSummary,
};

// Default export
export default {
  // Admin Dashboard
  AdminDashboard,
  DashboardWidget,
  StatsWidget,
  ChartWidget,
  ActivityWidget,
  SystemWidget,

  // User Management
  UserManagement,
  UserDetailDialog,
  UserFormDialog,

  // System Monitoring
  SystemStatusOverview,
  AlertSummary,
};
