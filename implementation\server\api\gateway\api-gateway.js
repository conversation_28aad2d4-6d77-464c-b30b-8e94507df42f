/**
 * API Gateway
 *
 * This module implements the API Gateway functionality for the server.
 * It handles routing, request validation, compression, and error handling.
 */

const express = require('express');
const compression = require('compression');
const cors = require('cors');
const helmet = require('helmet');
const { logger } = require('../middleware/auth-middleware');
const { apiLimiter } = require('../middleware/rate-limit-middleware');

/**
 * Setup API Gateway
 * @param {Object} app - Express app
 */
function setupApiGateway(app) {
  // Apply global middleware
  app.use(helmet());
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));
  app.use(
    cors({
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key', 'X-CSRF-Token'],
      credentials: true,
    }),
  );
  app.use(apiLimiter);
  app.use(compressResponse());

  // Apply error handling middleware
  app.use(handleErrors);

  logger.info('API Gateway setup complete');
}

/**
 * Route request to appropriate service
 * @param {Object} options - Routing options
 * @returns {Function} - Express middleware
 */
function routeRequest(options) {
  return (req, res, next) => {
    try {
      const { path, service, method } = options;

      if (req.path !== path) {
        return next();
      }

      if (method && req.method !== method) {
        return res.status(405).json({
          success: false,
          error: {
            code: 'METHOD_NOT_ALLOWED',
            message: `Method ${req.method} is not allowed for this endpoint`,
          },
        });
      }

      // Log request
      logger.debug(`Routing request to ${service}: ${req.method} ${req.path}`);

      // Call service
      return service(req, res, next);
    } catch (error) {
      logger.error('Error routing request:', error);
      next(error);
    }
  };
}

/**
 * Validate request
 * @param {Object} options - Validation options
 * @returns {Function} - Express middleware
 */
function validateRequest(options) {
  return (req, res, next) => {
    try {
      const { path, methods, requiredHeaders, requiredParams, requiredBody } = options;

      // Check path
      if (path && req.path !== path) {
        return next();
      }

      // Check method
      if (methods && !methods.includes(req.method)) {
        return res.status(405).json({
          success: false,
          error: {
            code: 'METHOD_NOT_ALLOWED',
            message: `Method ${req.method} is not allowed for this endpoint`,
          },
        });
      }

      // Check required headers
      if (requiredHeaders) {
        for (const header of requiredHeaders) {
          if (!req.headers[header.toLowerCase()]) {
            return res.status(400).json({
              success: false,
              error: {
                code: 'MISSING_REQUIRED_HEADER',
                message: `Missing required header: ${header}`,
              },
            });
          }
        }
      }

      // Check required query parameters
      if (requiredParams) {
        for (const param of requiredParams) {
          if (!req.query[param]) {
            return res.status(400).json({
              success: false,
              error: {
                code: 'MISSING_REQUIRED_PARAM',
                message: `Missing required query parameter: ${param}`,
              },
            });
          }
        }
      }

      // Check required body fields
      if (requiredBody) {
        for (const field of requiredBody) {
          if (!req.body || req.body[field] === undefined) {
            return res.status(400).json({
              success: false,
              error: {
                code: 'MISSING_REQUIRED_FIELD',
                message: `Missing required body field: ${field}`,
              },
            });
          }
        }
      }

      // Request is valid
      return next();
    } catch (error) {
      logger.error('Error validating request:', error);
      next(error);
    }
  };
}

/**
 * Compress response
 * @returns {Function} - Express middleware
 */
function compressResponse() {
  return compression({
    filter: (req, res) => {
      // Don't compress responses for clients that don't support it
      if (req.headers['accept-encoding'] && req.headers['accept-encoding'].includes('gzip')) {
        return compression.filter(req, res);
      }
      return false;
    },
    level: 6, // Default compression level
  });
}

/**
 * Handle errors
 * @param {Error} err - Error object
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
function handleErrors(err, req, res, next) {
  // Log error
  logger.error('API Gateway error:', err);

  // Get status code and error code
  const statusCode = err.statusCode || 500;
  const errorCode = err.code || 'INTERNAL_SERVER_ERROR';
  const message = statusCode === 500 ? 'An unexpected error occurred' : err.message;

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: {
      code: errorCode,
      message,
    },
  });
}

module.exports = {
  setupApiGateway,
  routeRequest,
  validateRequest,
  compressResponse,
  handleErrors,
};
