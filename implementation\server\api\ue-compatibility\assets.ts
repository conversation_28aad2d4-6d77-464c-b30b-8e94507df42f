/**
 * UE Asset Compatibility
 *
 * This module provides endpoints for validating and managing assets
 * for Unreal Engine 5.4+ compatibility.
 */

import { Router } from 'express';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { z } from 'zod';
import { AssetService } from '../../services/asset-service';

// Create router
const router = Router();

// Define UE version schema
const UEVersionSchema = z.object({
  major: z.number(),
  minor: z.number(),
  patch: z.number(),
});

// Define asset validation schema
const AssetValidationSchema = z.object({
  asset_id: z.string().uuid(),
  ue_version: UEVersionSchema,
});

// Define asset delta schema
const AssetDeltaSchema = z.object({
  asset_id: z.string().uuid(),
  base_version: z.string(),
  target_version: z.string().optional(),
  ue_version: UEVersionSchema,
});

/**
 * Validate asset for UE compatibility
 *
 * @param req Request object
 * @param res Response object
 */
async function validateAsset(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = AssetValidationSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract asset ID and UE version
    const { asset_id, ue_version } = bodyResult.data;

    // Log the request
    logger.info('Asset validation request', {
      asset_id,
      ue_version,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get asset
    const { data: asset, error: assetError } = await supabase
      .from('assets')
      .select('id, vendor_id, name, description, type, size, hash, version, created_at, updated_at')
      .eq('id', asset_id)
      .single();

    if (assetError) {
      logger.error('Error fetching asset', { error: assetError, asset_id });
      return res.status(500).json({ error: 'Error fetching asset' });
    }

    if (!asset) {
      return res.status(404).json({ error: 'Asset not found' });
    }

    // Validate asset for UE compatibility
    const validationResult = validateAssetForUE(asset, ue_version);

    // Return validation result
    return res.status(200).json({
      valid: validationResult.valid,
      issues: validationResult.issues,
      warnings: validationResult.warnings,
      asset_id,
      ue_version,
    });
  } catch (error) {
    return errorHandler(error, req, res);
  }
}

/**
 * Get asset delta for UE compatibility
 *
 * @param req Request object
 * @param res Response object
 */
async function getAssetDelta(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = AssetDeltaSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract asset ID, versions, and UE version
    const { asset_id, base_version, target_version, ue_version } = bodyResult.data;

    // Log the request
    logger.info('Asset delta request', {
      asset_id,
      base_version,
      target_version,
      ue_version,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get asset
    const { data: asset, error: assetError } = await supabase
      .from('assets')
      .select('id, vendor_id, name, description, type, size, hash, version, created_at, updated_at')
      .eq('id', asset_id)
      .single();

    if (assetError) {
      logger.error('Error fetching asset', { error: assetError, asset_id });
      return res.status(500).json({ error: 'Error fetching asset' });
    }

    if (!asset) {
      return res.status(404).json({ error: 'Asset not found' });
    }

    // Check if delta updates are supported for this UE version
    if (!isDeltaUpdatesSupportedForUE(ue_version)) {
      return res.status(400).json({
        error: 'Delta updates not supported',
        message: `Delta updates are not supported for UE ${ue_version.major}.${ue_version.minor}.${ue_version.patch}`,
      });
    }

    // Get asset delta
    const assetService = new AssetService(supabase);
    const delta = await assetService.getAssetDelta(
      asset_id,
      base_version,
      target_version || 'latest',
    );

    // Return delta
    return res.status(200).json({
      asset_id,
      base_version,
      target_version: delta.target_version,
      delta_size: delta.delta_size,
      delta_url: delta.delta_url,
      delta_hash: delta.delta_hash,
      ue_version,
    });
  } catch (error) {
    return errorHandler(error, req, res);
  }
}

/**
 * Validate asset for UE compatibility
 *
 * @param asset Asset object
 * @param version UE version
 * @returns Validation result
 */
function validateAssetForUE(
  asset: Record<string, any>,
  version: z.infer<typeof UEVersionSchema>,
): {
  valid: boolean;
  issues: Array<{ code: string; message: string; path: string }>;
  warnings: Array<{ code: string; message: string; path: string }>;
} {
  const issues: Array<{ code: string; message: string; path: string }> = [];
  const warnings: Array<{ code: string; message: string; path: string }> = [];

  // Check asset type compatibility
  if (asset.type === 'model') {
    // Check for UE 5.4+ specific features
    if (version.major === 5 && version.minor >= 4) {
      // UE 5.4+ supports all model types
    } else if (version.major === 5) {
      // UE 5.0-5.3 has some limitations
      if (asset.size > 100 * 1024 * 1024) {
        // 100 MB
        warnings.push({
          code: 'LARGE_MODEL',
          message: `Large models (>${Math.floor(asset.size / (1024 * 1024))} MB) may cause performance issues in UE ${version.major}.${version.minor}`,
          path: 'size',
        });
      }
    } else if (version.major === 4) {
      // UE 4.x has more limitations
      if (asset.size > 50 * 1024 * 1024) {
        // 50 MB
        issues.push({
          code: 'MODEL_TOO_LARGE',
          message: `Models larger than 50 MB are not recommended for UE ${version.major}.${version.minor}`,
          path: 'size',
        });
      }
    }
  } else if (asset.type === 'texture') {
    // Check texture size limitations
    if (version.major === 4) {
      // UE 4.x has texture size limitations
      if (asset.size > 16 * 1024 * 1024) {
        // 16 MB
        issues.push({
          code: 'TEXTURE_TOO_LARGE',
          message: `Textures larger than 16 MB may not be supported in UE ${version.major}.${version.minor}`,
          path: 'size',
        });
      }
    }
  }

  return {
    valid: issues.length === 0,
    issues,
    warnings,
  };
}

/**
 * Check if delta updates are supported for UE version
 *
 * @param version UE version
 * @returns True if supported, false otherwise
 */
function isDeltaUpdatesSupportedForUE(version: z.infer<typeof UEVersionSchema>): boolean {
  // Delta updates are supported in UE 5.4+
  if (version.major === 5 && version.minor >= 4) {
    return true;
  }

  // Not supported in other versions
  return false;
}

// Register routes
router.post('/validate', validateAsset);
router.post('/delta', getAssetDelta);

// Export router
export default router;
