<template>
  <div class="showroom-analytics">
    <div class="analytics-header">
      <h3>{{ showroom ? showroom.name + ' Analytics' : 'Showroom Analytics' }}</h3>
      <div class="analytics-controls">
        <div class="date-range-selector">
          <label>Date Range:</label>
          <select v-model="dateRange" @change="updateAnalytics">
            <option value="7">Last 7 Days</option>
            <option value="30">Last 30 Days</option>
            <option value="90">Last 90 Days</option>
            <option value="365">Last 12 Months</option>
            <option value="custom">Custom Range</option>
          </select>
        </div>
        <div v-if="dateRange === 'custom'" class="custom-date-range">
          <div class="date-input">
            <label>From:</label>
            <input type="date" v-model="customDateFrom" @change="updateAnalytics">
          </div>
          <div class="date-input">
            <label>To:</label>
            <input type="date" v-model="customDateTo" @change="updateAnalytics">
          </div>
        </div>
        <button class="btn btn-secondary" @click="exportAnalytics">
          <i class="material-icons">download</i> Export
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
      <span>Loading analytics data...</span>
    </div>

    <div v-else class="analytics-content">
      <div class="metrics-row">
        <div class="metric-card">
          <div class="metric-icon">
            <i class="material-icons">visibility</i>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.totalVisits }}</div>
            <div class="metric-label">Total Visits</div>
            <div class="metric-change" :class="getChangeClass(metrics.visitsChange)">
              <i class="material-icons">{{ getChangeIcon(metrics.visitsChange) }}</i>
              <span>{{ formatChange(metrics.visitsChange) }}</span>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="material-icons">person</i>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.uniqueVisitors }}</div>
            <div class="metric-label">Unique Visitors</div>
            <div class="metric-change" :class="getChangeClass(metrics.visitorsChange)">
              <i class="material-icons">{{ getChangeIcon(metrics.visitorsChange) }}</i>
              <span>{{ formatChange(metrics.visitorsChange) }}</span>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="material-icons">schedule</i>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ formatTime(metrics.avgTimeSpent) }}</div>
            <div class="metric-label">Avg. Time Spent</div>
            <div class="metric-change" :class="getChangeClass(metrics.timeSpentChange)">
              <i class="material-icons">{{ getChangeIcon(metrics.timeSpentChange) }}</i>
              <span>{{ formatChange(metrics.timeSpentChange) }}</span>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="metric-icon">
            <i class="material-icons">touch_app</i>
          </div>
          <div class="metric-content">
            <div class="metric-value">{{ metrics.interactions }}</div>
            <div class="metric-label">Product Interactions</div>
            <div class="metric-change" :class="getChangeClass(metrics.interactionsChange)">
              <i class="material-icons">{{ getChangeIcon(metrics.interactionsChange) }}</i>
              <span>{{ formatChange(metrics.interactionsChange) }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="charts-row">
        <div class="chart-container">
          <div class="chart-header">
            <h4>Visits Over Time</h4>
            <div class="chart-controls">
              <button
                v-for="period in chartPeriods"
                :key="period.value"
                class="btn btn-small"
                :class="{ active: visitsChartPeriod === period.value }"
                @click="visitsChartPeriod = period.value; updateVisitsChart()"
              >
                {{ period.label }}
              </button>
            </div>
          </div>
          <div class="chart-body">
            <canvas ref="visitsChart"></canvas>
          </div>
        </div>
      </div>

      <div class="charts-row">
        <div class="chart-container half">
          <div class="chart-header">
            <h4>Traffic Sources</h4>
          </div>
          <div class="chart-body">
            <canvas ref="sourcesChart"></canvas>
          </div>
        </div>

        <div class="chart-container half">
          <div class="chart-header">
            <h4>Device Types</h4>
          </div>
          <div class="chart-body">
            <canvas ref="devicesChart"></canvas>
          </div>
        </div>
      </div>

      <div class="analytics-section">
        <h4>Top Products</h4>
        <div class="top-products-list">
          <div class="top-products-header">
            <div class="col-rank">#</div>
            <div class="col-product">Product</div>
            <div class="col-views">Views</div>
            <div class="col-interactions">Interactions</div>
            <div class="col-avg-time">Avg. Time</div>
            <div class="col-conversion">Conversion</div>
          </div>
          <div v-for="(product, index) in topProducts" :key="product.id" class="top-product-item">
            <div class="col-rank">{{ index + 1 }}</div>
            <div class="col-product">
              <div class="product-thumbnail">
                <img v-if="product.thumbnail" :src="product.thumbnail" :alt="product.name">
                <div v-else class="product-placeholder">
                  <i class="material-icons">inventory_2</i>
                </div>
              </div>
              <div class="product-info">
                <div class="product-name">{{ product.name }}</div>
                <div class="product-category">{{ product.category }}</div>
              </div>
            </div>
            <div class="col-views">{{ product.views }}</div>
            <div class="col-interactions">{{ product.interactions }}</div>
            <div class="col-avg-time">{{ formatTime(product.avgTime) }}</div>
            <div class="col-conversion">
              <div class="conversion-rate">{{ product.conversion }}%</div>
              <div class="conversion-bar">
                <div
                  class="conversion-progress"
                  :style="{ width: `${product.conversion}%` }"
                  :class="{
                    'high': product.conversion >= 10,
                    'medium': product.conversion >= 5 && product.conversion < 10,
                    'low': product.conversion < 5
                  }"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="analytics-section">
        <h4>User Behavior</h4>
        <div class="behavior-metrics">
          <div class="behavior-metric">
            <div class="behavior-value">{{ metrics.bounceRate }}%</div>
            <div class="behavior-label">Bounce Rate</div>
          </div>
          <div class="behavior-metric">
            <div class="behavior-value">{{ metrics.completionRate }}%</div>
            <div class="behavior-label">Tour Completion</div>
          </div>
          <div class="behavior-metric">
            <div class="behavior-value">{{ metrics.returnRate }}%</div>
            <div class="behavior-label">Return Rate</div>
          </div>
          <div class="behavior-metric">
            <div class="behavior-value">{{ metrics.shareRate }}%</div>
            <div class="behavior-label">Share Rate</div>
          </div>
        </div>

        <div class="heatmap-container">
          <div class="heatmap-header">
            <h5>Interaction Heatmap</h5>
            <div class="heatmap-controls">
              <select v-model="heatmapView" @change="updateHeatmap">
                <option value="top">Top View</option>
                <option value="front">Front View</option>
                <option value="side">Side View</option>
                <option value="3d">3D View</option>
              </select>
            </div>
          </div>
          <div class="heatmap-body">
            <div class="heatmap-image">
              <img :src="heatmapImage" alt="Interaction Heatmap">
            </div>
          </div>
        </div>
      </div>

      <div class="analytics-section">
        <h4>Conversion Funnel</h4>
        <div class="funnel-container">
          <div class="funnel-stage" v-for="(stage, index) in conversionFunnel" :key="index">
            <div class="funnel-bar" :style="{ width: `${stage.percentage}%` }">
              <div class="funnel-label">{{ stage.name }}</div>
              <div class="funnel-value">{{ stage.value }} ({{ stage.percentage }}%)</div>
            </div>
            <div v-if="index < conversionFunnel.length - 1" class="funnel-arrow">
              <i class="material-icons">arrow_downward</i>
              <div class="conversion-rate">{{ stage.conversionRate }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShowroomAnalytics',

  props: {
    showroom: {
      type: Object,
      default: null
    },
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      dateRange: '30',
      customDateFrom: '',
      customDateTo: '',
      visitsChartPeriod: 'day',
      heatmapView: 'top',
      heatmapImage: null,

      chartPeriods: [
        { value: 'hour', label: 'Hourly' },
        { value: 'day', label: 'Daily' },
        { value: 'week', label: 'Weekly' },
        { value: 'month', label: 'Monthly' }
      ],

      metrics: {
        totalVisits: 0,
        visitsChange: 0,
        uniqueVisitors: 0,
        visitorsChange: 0,
        avgTimeSpent: 0,
        timeSpentChange: 0,
        interactions: 0,
        interactionsChange: 0,
        bounceRate: 0,
        completionRate: 0,
        returnRate: 0,
        shareRate: 0
      },

      topProducts: [],
      conversionFunnel: [],

      charts: {
        visits: null,
        sources: null,
        devices: null
      }
    };
  },

  mounted() {
    // Set default date range for custom dates
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    this.customDateFrom = thirtyDaysAgo.toISOString().split('T')[0];
    this.customDateTo = today.toISOString().split('T')[0];

    this.loadAnalyticsData();
  },

  methods: {
    // Load analytics data
    async loadAnalyticsData() {
      this.loading = true;

      try {
        // Get date parameters
        let dateParams = '';
        if (this.dateRange === 'custom') {
          dateParams = `&start_date=${this.customDateFrom}&end_date=${this.customDateTo}`;
        } else {
          dateParams = `&date_range=${this.dateRange}`;
        }

        // Get showroom analytics data
        const response = await fetch(`/api/analytics/showrooms/${this.showroom?.id || 'all'}?vendor_id=${this.vendorId}${dateParams}`);
        const data = await response.json();

        if (data.success) {
          // Update metrics
          this.metrics = data.data.metrics;

          // Update top products
          this.topProducts = data.data.topProducts;

          // Update conversion funnel
          this.conversionFunnel = data.data.conversionFunnel;

          // Initialize charts with real data
          this.initCharts(data.data.charts);

          // Update heatmap
          this.updateHeatmap();
        } else {
          console.error('Error loading analytics data:', data.error);
          // Fall back to mock data for demonstration
          this.generateMockData();
          this.initCharts();
        }
      } catch (error) {
        console.error('Error loading analytics data:', error);
        // Fall back to mock data for demonstration
        this.generateMockData();
        this.initCharts();
      } finally {
        this.loading = false;
      }
    },

    // Generate mock data for demonstration
    generateMockData() {
      // Generate metrics
      this.metrics = {
        totalVisits: Math.floor(Math.random() * 5000) + 1000,
        visitsChange: (Math.random() * 30) - 10,
        uniqueVisitors: Math.floor(Math.random() * 2000) + 500,
        visitorsChange: (Math.random() * 25) - 5,
        avgTimeSpent: Math.floor(Math.random() * 600) + 120, // in seconds
        timeSpentChange: (Math.random() * 20) - 5,
        interactions: Math.floor(Math.random() * 10000) + 2000,
        interactionsChange: (Math.random() * 35) + 5,
        bounceRate: Math.floor(Math.random() * 40) + 10,
        completionRate: Math.floor(Math.random() * 50) + 30,
        returnRate: Math.floor(Math.random() * 30) + 10,
        shareRate: Math.floor(Math.random() * 15) + 5
      };

      // Generate top products
      this.topProducts = [
        {
          id: 'prod1',
          name: 'Modern Sofa',
          category: 'Furniture',
          thumbnail: null,
          views: Math.floor(Math.random() * 1000) + 500,
          interactions: Math.floor(Math.random() * 500) + 200,
          avgTime: Math.floor(Math.random() * 300) + 60,
          conversion: Math.floor(Math.random() * 15) + 5
        },
        {
          id: 'prod2',
          name: 'Pendant Light',
          category: 'Lighting',
          thumbnail: null,
          views: Math.floor(Math.random() * 800) + 400,
          interactions: Math.floor(Math.random() * 400) + 150,
          avgTime: Math.floor(Math.random() * 250) + 50,
          conversion: Math.floor(Math.random() * 12) + 3
        },
        {
          id: 'prod3',
          name: 'Wall Art',
          category: 'Decor',
          thumbnail: null,
          views: Math.floor(Math.random() * 600) + 300,
          interactions: Math.floor(Math.random() * 300) + 100,
          avgTime: Math.floor(Math.random() * 200) + 40,
          conversion: Math.floor(Math.random() * 10) + 2
        },
        {
          id: 'prod4',
          name: 'Patio Set',
          category: 'Outdoor',
          thumbnail: null,
          views: Math.floor(Math.random() * 500) + 200,
          interactions: Math.floor(Math.random() * 250) + 80,
          avgTime: Math.floor(Math.random() * 180) + 30,
          conversion: Math.floor(Math.random() * 8) + 1
        },
        {
          id: 'prod5',
          name: 'Office Chair',
          category: 'Office',
          thumbnail: null,
          views: Math.floor(Math.random() * 400) + 150,
          interactions: Math.floor(Math.random() * 200) + 60,
          avgTime: Math.floor(Math.random() * 150) + 25,
          conversion: Math.floor(Math.random() * 6) + 1
        }
      ];

      // Generate conversion funnel
      const visitors = this.metrics.uniqueVisitors;
      const productViews = Math.floor(visitors * (Math.random() * 0.3 + 0.6)); // 60-90% of visitors view products
      const addToCart = Math.floor(productViews * (Math.random() * 0.2 + 0.3)); // 30-50% of product viewers add to cart
      const checkout = Math.floor(addToCart * (Math.random() * 0.2 + 0.4)); // 40-60% of cart adders checkout
      const purchase = Math.floor(checkout * (Math.random() * 0.2 + 0.7)); // 70-90% of checkout complete purchase

      this.conversionFunnel = [
        {
          name: 'Visitors',
          value: visitors,
          percentage: 100,
          conversionRate: Math.round((productViews / visitors) * 100)
        },
        {
          name: 'Product Views',
          value: productViews,
          percentage: Math.round((productViews / visitors) * 100),
          conversionRate: Math.round((addToCart / productViews) * 100)
        },
        {
          name: 'Add to Cart',
          value: addToCart,
          percentage: Math.round((addToCart / visitors) * 100),
          conversionRate: Math.round((checkout / addToCart) * 100)
        },
        {
          name: 'Checkout',
          value: checkout,
          percentage: Math.round((checkout / visitors) * 100),
          conversionRate: Math.round((purchase / checkout) * 100)
        },
        {
          name: 'Purchase',
          value: purchase,
          percentage: Math.round((purchase / visitors) * 100)
        }
      ];

      // Generate heatmap image
      this.updateHeatmap();
    },

    // Initialize charts
    initCharts() {
      this.updateVisitsChart();
      this.updateSourcesChart();
      this.updateDevicesChart();
    },

    // Update analytics data when filters change
    updateAnalytics() {
      this.loadAnalyticsData();
    },

    // Update visits chart
    updateVisitsChart() {
      // This would be replaced with actual chart library initialization
      console.log('Updating visits chart with period:', this.visitsChartPeriod);

      // Mock chart update
      this.$nextTick(() => {
        const canvas = this.$refs.visitsChart;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Draw mock chart
          ctx.beginPath();
          ctx.moveTo(0, canvas.height * 0.8);
          ctx.lineTo(canvas.width * 0.2, canvas.height * 0.6);
          ctx.lineTo(canvas.width * 0.4, canvas.height * 0.7);
          ctx.lineTo(canvas.width * 0.6, canvas.height * 0.4);
          ctx.lineTo(canvas.width * 0.8, canvas.height * 0.5);
          ctx.lineTo(canvas.width, canvas.height * 0.3);
          ctx.strokeStyle = '#3498db';
          ctx.lineWidth = 3;
          ctx.stroke();
        }
      });
    },

    // Update sources chart
    updateSourcesChart() {
      // This would be replaced with actual chart library initialization
      console.log('Updating sources chart');

      // Mock chart update
      this.$nextTick(() => {
        const canvas = this.$refs.sourcesChart;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Draw mock pie chart
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2;
          const radius = Math.min(centerX, centerY) * 0.8;

          const segments = [
            { color: '#3498db', start: 0, end: Math.PI * 0.5 },
            { color: '#2ecc71', start: Math.PI * 0.5, end: Math.PI * 1.1 },
            { color: '#9b59b6', start: Math.PI * 1.1, end: Math.PI * 1.5 },
            { color: '#f1c40f', start: Math.PI * 1.5, end: Math.PI * 2 }
          ];

          segments.forEach(segment => {
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, segment.start, segment.end);
            ctx.closePath();
            ctx.fillStyle = segment.color;
            ctx.fill();
          });
        }
      });
    },

    // Update devices chart
    updateDevicesChart() {
      // This would be replaced with actual chart library initialization
      console.log('Updating devices chart');

      // Mock chart update
      this.$nextTick(() => {
        const canvas = this.$refs.devicesChart;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          // Draw mock donut chart
          const centerX = canvas.width / 2;
          const centerY = canvas.height / 2;
          const outerRadius = Math.min(centerX, centerY) * 0.8;
          const innerRadius = outerRadius * 0.6;

          const segments = [
            { color: '#3498db', start: 0, end: Math.PI * 0.7 },
            { color: '#2ecc71', start: Math.PI * 0.7, end: Math.PI * 1.3 },
            { color: '#9b59b6', start: Math.PI * 1.3, end: Math.PI * 2 }
          ];

          segments.forEach(segment => {
            ctx.beginPath();
            ctx.arc(centerX, centerY, outerRadius, segment.start, segment.end);
            ctx.arc(centerX, centerY, innerRadius, segment.end, segment.start, true);
            ctx.closePath();
            ctx.fillStyle = segment.color;
            ctx.fill();
          });
        }
      });
    },

    // Update heatmap
    async updateHeatmap() {
      try {
        // Get date parameters
        let dateParams = '';
        if (this.dateRange === 'custom') {
          dateParams = `&start_date=${this.customDateFrom}&end_date=${this.customDateTo}`;
        } else {
          dateParams = `&date_range=${this.dateRange}`;
        }

        // Get heatmap data from API
        const response = await fetch(`/api/analytics/realtime/heatmap?vendor_id=${this.vendorId}&showroom_id=${this.showroom?.id || 'all'}&view_type=${this.heatmapView}${dateParams}`);
        const data = await response.json();

        if (data.success && data.data.length > 0) {
          // Generate heatmap using h337 (heatmap.js)
          this.$nextTick(() => {
            const heatmapContainer = this.$el.querySelector('.heatmap-image');
            if (heatmapContainer) {
              // Clear previous heatmap
              heatmapContainer.innerHTML = '';

              // Create heatmap container
              const heatmapElement = document.createElement('div');
              heatmapElement.style.width = '800px';
              heatmapElement.style.height = '400px';
              heatmapElement.style.position = 'relative';
              heatmapContainer.appendChild(heatmapElement);

              // Create heatmap instance
              const heatmapInstance = h337.create({
                container: heatmapElement,
                radius: 20,
                maxOpacity: 0.8,
                minOpacity: 0.1,
                blur: 0.85
              });

              // Add data points
              heatmapInstance.setData({
                max: 10,
                data: data.data.map(point => ({
                  x: point.x * 800, // Scale to container width
                  y: point.y * 400, // Scale to container height
                  value: point.value
                }))
              });
            }
          });
        } else {
          // Fall back to placeholder if no data
          this.heatmapImage = `https://via.placeholder.com/800x400/333333/FFFFFF?text=No+Interaction+Data+(${this.heatmapView}+view)`;
        }
      } catch (error) {
        console.error('Error loading heatmap data:', error);
        // Fall back to placeholder
        this.heatmapImage = `https://via.placeholder.com/800x400/333333/FFFFFF?text=Interaction+Heatmap+(${this.heatmapView}+view)`;
      }
    },

    // Export analytics data
    exportAnalytics() {
      // Show export options dialog
      this.$buefy.dialog.prompt({
        title: 'Export Analytics Data',
        message: 'Select export format:',
        inputAttrs: {
          type: 'select',
          options: ['CSV', 'Excel', 'PDF']
        },
        confirmText: 'Export',
        cancelText: 'Cancel',
        onConfirm: (format) => {
          this.downloadExport(format.toLowerCase());
        }
      });
    },

    // Download export in selected format
    downloadExport(format) {
      // Get date parameters
      let dateParams = '';
      if (this.dateRange === 'custom') {
        dateParams = `&start_date=${this.customDateFrom}&end_date=${this.customDateTo}`;
      } else {
        dateParams = `&date_range=${this.dateRange}`;
      }

      // Build export URL
      const exportUrl = `/api/analytics/export/${format}?vendor_id=${this.vendorId}&report_type=showrooms&showroom_id=${this.showroom?.id || 'all'}${dateParams}`;

      // Open download in new window
      window.open(exportUrl, '_blank');

      // Show success message
      this.$buefy.toast.open({
        message: `Exporting data in ${format.toUpperCase()} format...`,
        type: 'is-success'
      });
    },

    // Format time in seconds to mm:ss or hh:mm:ss
    formatTime(seconds) {
      if (!seconds) return '00:00';

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      } else {
        return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
    },

    // Format percentage change
    formatChange(value) {
      const sign = value >= 0 ? '+' : '';
      return `${sign}${value.toFixed(1)}%`;
    },

    // Get CSS class for change value
    getChangeClass(value) {
      if (value > 0) return 'positive';
      if (value < 0) return 'negative';
      return 'neutral';
    },

    // Get icon for change value
    getChangeIcon(value) {
      if (value > 0) return 'trending_up';
      if (value < 0) return 'trending_down';
      return 'trending_flat';
    }
  }
};
</script>

<style scoped>
.showroom-analytics {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
  position: relative;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.analytics-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.analytics-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.date-range-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-range-selector label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.date-range-selector select {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-size: 14px;
}

.custom-date-range {
  display: flex;
  gap: 10px;
}

.date-input {
  display: flex;
  align-items: center;
  gap: 5px;
}

.date-input label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.date-input input {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-size: 14px;
}

.loading-overlay {
  position: absolute;
  top: 60px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--theme--background-rgb), 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.metrics-row {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--primary);
}

.metric-icon i {
  font-size: 24px;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 5px;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  font-weight: 500;
}

.metric-change.positive {
  color: var(--theme--success);
}

.metric-change.negative {
  color: var(--theme--danger);
}

.metric-change.neutral {
  color: var(--theme--foreground-subdued);
}

.metric-change i {
  font-size: 16px;
}

.charts-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.chart-container {
  flex: 1;
  min-width: 300px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.chart-container.half {
  flex-basis: calc(50% - 10px);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--theme--border-color);
}

.chart-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 5px;
}

.chart-body {
  padding: 15px;
  height: 300px;
  position: relative;
}

.chart-body canvas {
  width: 100%;
  height: 100%;
}

.analytics-section {
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.analytics-section h4 {
  margin: 0;
  padding: 15px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid var(--theme--border-color);
}

.top-products-list {
  width: 100%;
}

.top-products-header {
  display: flex;
  background-color: var(--theme--background-subdued);
  padding: 12px 16px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
  font-size: 14px;
}

.top-product-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
  align-items: center;
}

.top-product-item:last-child {
  border-bottom: none;
}

.col-rank {
  width: 40px;
  font-weight: 600;
  text-align: center;
}

.col-product {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-placeholder i {
  font-size: 20px;
  color: var(--theme--foreground-subdued);
}

.product-info {
  min-width: 0;
}

.product-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-category {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.col-views,
.col-interactions,
.col-avg-time {
  width: 100px;
  text-align: center;
}

.col-conversion {
  width: 150px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.conversion-rate {
  font-weight: 600;
  text-align: center;
}

.conversion-bar {
  height: 6px;
  background-color: var(--theme--background-subdued);
  border-radius: 3px;
  overflow: hidden;
}

.conversion-progress {
  height: 100%;
  border-radius: 3px;
}

.conversion-progress.high {
  background-color: var(--theme--primary);
}

.conversion-progress.medium {
  background-color: var(--theme--warning);
}

.conversion-progress.low {
  background-color: var(--theme--danger);
}

.behavior-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px;
}

.behavior-metric {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  text-align: center;
}

.behavior-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
  color: var(--theme--primary);
}

.behavior-label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.heatmap-container {
  padding: 0 20px 20px;
}

.heatmap-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.heatmap-header h5 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.heatmap-controls select {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-size: 14px;
}

.heatmap-image {
  width: 100%;
  height: 400px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.heatmap-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.funnel-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.funnel-stage {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.funnel-bar {
  height: 50px;
  background-color: var(--theme--primary);
  border-radius: var(--theme--border-radius);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  color: white;
  font-weight: 500;
  min-width: 200px;
  transition: width 0.5s ease;
}

.funnel-arrow {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--theme--foreground-subdued);
}

.funnel-arrow i {
  font-size: 24px;
}

.conversion-rate {
  font-size: 12px;
  font-weight: 600;
}

.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--background-accent);
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-small.active {
  background-color: var(--theme--primary);
  color: white;
}
</style>
