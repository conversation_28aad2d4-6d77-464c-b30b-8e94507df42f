import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import TeamMemberManagement from '../src/components/TeamMemberManagement.vue';

describe('TeamMemberManagement', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(TeamMemberManagement, {
      // Use Vue 2 syntax
    });
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.team-member-management').exists()).toBe(true);
  });

  it('renders the team header', () => {
    expect(wrapper.find('.team-header').exists()).toBe(true);
    expect(wrapper.find('.team-title').text()).toBe('Team Members');
    expect(wrapper.find('.invite-button').exists()).toBe(true);
  });

  it('renders the team filters', () => {
    expect(wrapper.find('.team-filters').exists()).toBe(true);
    expect(wrapper.find('.search-container').exists()).toBe(true);
    expect(wrapper.find('.filter-container').exists()).toBe(true);
  });

  it('renders the team table', () => {
    expect(wrapper.find('.team-table').exists()).toBe(true);
    expect(wrapper.find('.team-table thead').exists()).toBe(true);
    expect(wrapper.find('.team-table tbody').exists()).toBe(true);
  });

  it('renders the pagination controls', () => {
    expect(wrapper.find('.pagination-container').exists()).toBe(true);
    expect(wrapper.find('.pagination-info').exists()).toBe(true);
    expect(wrapper.find('.pagination-controls').exists()).toBe(true);
    expect(wrapper.find('.pagination-size').exists()).toBe(true);
  });

  it('shows the invite modal when clicking the invite button', async () => {
    // Initially, the invite modal should not be visible
    expect(wrapper.find('.modal-overlay').exists()).toBe(false);

    // Click the invite button
    await wrapper.find('.invite-button').trigger('click');

    // Now the invite modal should be visible
    expect(wrapper.find('.modal-overlay').exists()).toBe(true);
    expect(wrapper.find('.modal-header h3').text()).toBe('Invite Team Member');
  });

  it('closes the invite modal when clicking the close button', async () => {
    // Open the invite modal
    await wrapper.find('.invite-button').trigger('click');
    expect(wrapper.find('.modal-overlay').exists()).toBe(true);

    // Click the close button
    await wrapper.find('.modal-header .close-button').trigger('click');

    // The invite modal should be closed
    expect(wrapper.find('.modal-overlay').exists()).toBe(false);
  });

  it('filters team members when searching', async () => {
    // Get the initial number of team members
    const initialMemberCount = wrapper.findAll('.team-table tbody tr').length;

    // Enter a search query
    const searchInput = wrapper.find('.search-input');
    await searchInput.setValue('John');

    // The number of displayed team members should be less than or equal to the initial count
    const filteredMemberCount = wrapper.findAll('.team-table tbody tr').length;
    expect(filteredMemberCount).toBeLessThanOrEqual(initialMemberCount);
  });

  it('filters team members by role', async () => {
    // Get the initial number of team members
    const initialMemberCount = wrapper.findAll('.team-table tbody tr').length;

    // Select a role filter
    const roleFilter = wrapper.find('#role-filter');
    await roleFilter.setValue('admin');

    // The number of displayed team members should be less than or equal to the initial count
    const filteredMemberCount = wrapper.findAll('.team-table tbody tr').length;
    expect(filteredMemberCount).toBeLessThanOrEqual(initialMemberCount);
  });

  it('filters team members by status', async () => {
    // Get the initial number of team members
    const initialMemberCount = wrapper.findAll('.team-table tbody tr').length;

    // Select a status filter
    const statusFilter = wrapper.find('#status-filter');
    await statusFilter.setValue('active');

    // The number of displayed team members should be less than or equal to the initial count
    const filteredMemberCount = wrapper.findAll('.team-table tbody tr').length;
    expect(filteredMemberCount).toBeLessThanOrEqual(initialMemberCount);
  });

  it('sorts team members', async () => {
    // Select a sort option
    const sortBy = wrapper.find('#sort-by');
    await sortBy.setValue('name');

    // Click the sort direction button
    await wrapper.find('.sort-direction-button').trigger('click');

    // The team members should be sorted
    // This is hard to test without specific knowledge of the data,
    // but we can at least verify that the component doesn't break
    expect(wrapper.findAll('.team-table tbody tr').length).toBeGreaterThan(0);
  });

  it('changes page size', async () => {
    // Select a page size
    const pageSize = wrapper.find('#page-size');
    await pageSize.setValue('25');

    // The page size should be updated
    expect(wrapper.vm.pageSize).toBe('25');
  });

  it('validates the invite form', async () => {
    // Open the invite modal
    await wrapper.find('.invite-button').trigger('click');

    // Initially, the form should be invalid (computed property returns falsy value)
    expect(wrapper.vm.isInviteFormValid).toBeFalsy();

    // Fill in the form
    await wrapper.find('#invite-email').setValue('<EMAIL>');
    await wrapper.find('#invite-first-name').setValue('Test');
    await wrapper.find('#invite-last-name').setValue('User');
    await wrapper.find('#invite-role').setValue('editor');

    // Now the form should be valid
    expect(wrapper.vm.isInviteFormValid).toBeTruthy();
  });

  it('sends an invitation when the form is valid', async () => {
    // Open the invite modal
    await wrapper.find('.invite-button').trigger('click');

    // Fill in the form
    await wrapper.find('#invite-email').setValue('<EMAIL>');
    await wrapper.find('#invite-first-name').setValue('Test');
    await wrapper.find('#invite-last-name').setValue('User');
    await wrapper.find('#invite-role').setValue('editor');

    // Submit the form (click the submit button inside the modal)
    await wrapper.find('.modal-footer .invite-button').trigger('click');

    // The invite modal should be closed (check showInviteModal state)
    expect(wrapper.vm.showInviteModal).toBe(false);

    // A new team member should be added
    const teamMembers = wrapper.vm.teamMembers;
    const newMember = teamMembers.find(m => m.email === '<EMAIL>');
    expect(newMember).toBeTruthy();
    expect(newMember.name).toBe('Test User');
    expect(newMember.role).toBe('editor');
    expect(newMember.status).toBe('invited');
  });
});
