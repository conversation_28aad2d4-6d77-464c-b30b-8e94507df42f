/**
 * API Key Management Endpoints
 *
 * This module provides endpoints for managing API keys.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { authenticate } from '../middleware/auth-middleware';

// Define the request schema for creating an API key
const CreateApiKeySchema = z.object({
  name: z.string().min(1).max(100),
  expiration: z.number().optional(), // Expiration in days, optional
  permissions: z.array(z.string()).optional(), // Array of permission strings
  scopes: z.array(z.string()).optional(), // Array of scope strings
});

// Define the request schema for updating an API key
const UpdateApiKeySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  expiration: z.number().optional(), // Expiration in days, optional
  permissions: z.array(z.string()).optional(), // Array of permission strings
  scopes: z.array(z.string()).optional(), // Array of scope strings
  enabled: z.boolean().optional(),
});

/**
 * Generate a secure API key
 * @returns {string} API key
 */
function generateApiKey() {
  // Generate a UUID v4
  const uuid = uuidv4();

  // Generate a random string
  const random = crypto.randomBytes(16).toString('hex');

  // Combine and format as a prefixed key
  return `mvs_${uuid.replace(/-/g, '')}_${random}`;
}

/**
 * Hash an API key for storage
 * @param {string} apiKey - API key to hash
 * @returns {string} Hashed API key
 */
function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

/**
 * API Key management endpoint
 *
 * This endpoint allows users to create, list, update, and delete API keys.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate the request
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return await listApiKeys(req, res, supabase, user);
      case 'POST':
        return await createApiKey(req, res, supabase, user);
      case 'PUT':
        return await updateApiKey(req, res, supabase, user);
      case 'DELETE':
        return await deleteApiKey(req, res, supabase, user);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    return errorHandler(error, res);
  }
}

/**
 * List API keys for a user
 */
async function listApiKeys(req, res, supabase, user) {
  // Get API keys for the user
  const { data, error } = await supabase
    .from('api_keys')
    .select('id, name, created_at, last_used, expires_at, permissions, scopes, enabled')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (error) {
    logger.error('Error listing API keys', { error, user_id: user.id });
    return res.status(500).json({ error: 'Error listing API keys' });
  }

  return res.status(200).json({ data });
}

/**
 * Create a new API key
 */
async function createApiKey(req, res, supabase, user) {
  // Validate request body
  const bodyResult = CreateApiKeySchema.safeParse(req.body);
  if (!bodyResult.success) {
    return res
      .status(400)
      .json({ error: 'Invalid request body', details: bodyResult.error.format() });
  }

  const { name, expiration, permissions, scopes } = bodyResult.data;

  // Generate API key
  const apiKey = generateApiKey();

  // Hash API key for storage
  const hashedKey = hashApiKey(apiKey);

  // Calculate expiration date if provided
  const expiresAt = expiration
    ? new Date(Date.now() + expiration * 24 * 60 * 60 * 1000).toISOString()
    : null;

  // Insert API key into database
  const { data, error } = await supabase
    .from('api_keys')
    .insert({
      user_id: user.id,
      name,
      key_hash: hashedKey,
      expires_at: expiresAt,
      permissions: permissions || [],
      scopes: scopes || [],
      enabled: true,
    })
    .select('id, name, created_at, expires_at, permissions, scopes, enabled');

  if (error) {
    logger.error('Error creating API key', { error, user_id: user.id });
    return res.status(500).json({ error: 'Error creating API key' });
  }

  // Log API key creation
  logger.info('API key created', {
    user_id: user.id,
    api_key_id: data[0].id,
    name: data[0].name,
  });

  // Return the API key (only shown once)
  return res.status(201).json({
    data: {
      ...data[0],
      key: apiKey, // Only returned once
    },
  });
}

/**
 * Update an API key
 */
async function updateApiKey(req, res, supabase, user) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: 'API key ID is required' });
  }

  // Validate request body
  const bodyResult = UpdateApiKeySchema.safeParse(req.body);
  if (!bodyResult.success) {
    return res
      .status(400)
      .json({ error: 'Invalid request body', details: bodyResult.error.format() });
  }

  // Check if API key exists and belongs to user
  const { data: existingKey, error: fetchError } = await supabase
    .from('api_keys')
    .select('id')
    .eq('id', id)
    .eq('user_id', user.id)
    .single();

  if (fetchError || !existingKey) {
    return res.status(404).json({ error: 'API key not found' });
  }

  // Update API key
  const { data, error } = await supabase
    .from('api_keys')
    .update(bodyResult.data)
    .eq('id', id)
    .eq('user_id', user.id)
    .select('id, name, created_at, expires_at, permissions, scopes, enabled');

  if (error) {
    logger.error('Error updating API key', { error, user_id: user.id, api_key_id: id });
    return res.status(500).json({ error: 'Error updating API key' });
  }

  // Log API key update
  logger.info('API key updated', {
    user_id: user.id,
    api_key_id: id,
  });

  return res.status(200).json({ data: data[0] });
}

/**
 * Delete an API key
 */
async function deleteApiKey(req, res, supabase, user) {
  const { id } = req.query;

  if (!id) {
    return res.status(400).json({ error: 'API key ID is required' });
  }

  // Check if API key exists and belongs to user
  const { data: existingKey, error: fetchError } = await supabase
    .from('api_keys')
    .select('id')
    .eq('id', id)
    .eq('user_id', user.id)
    .single();

  if (fetchError || !existingKey) {
    return res.status(404).json({ error: 'API key not found' });
  }

  // Delete API key
  const { error } = await supabase.from('api_keys').delete().eq('id', id).eq('user_id', user.id);

  if (error) {
    logger.error('Error deleting API key', { error, user_id: user.id, api_key_id: id });
    return res.status(500).json({ error: 'Error deleting API key' });
  }

  // Log API key deletion
  logger.info('API key deleted', {
    user_id: user.id,
    api_key_id: id,
  });

  return res.status(204).send();
}
