<template>
  <div class="client-manager">
    <div class="header">
      <h2>Client Management</h2>
      <div class="actions">
        <div class="search">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search clients..."
            @input="filterClients"
          />
          <i class="material-icons">search</i>
        </div>
        <button class="btn btn-secondary" @click="toggleGlobalAnalytics">
          <i class="material-icons">insights</i> Analytics
        </button>
        <button class="btn btn-primary" @click="showAddClientModal = true">
          <i class="material-icons">add</i> Add Client
        </button>
      </div>
    </div>

    <!-- Global Analytics View -->
    <div v-if="showGlobalAnalytics" class="global-analytics-container">
      <ClientAnalytics :vendorId="vendorId" @view-client="viewClientFromAnalytics" />
    </div>

    <!-- Client Management View -->
    <div v-else>
      <div class="filters">
        <div class="filter-row">
          <div class="filter">
            <label>Status:</label>
            <select v-model="statusFilter" @change="filterClients">
              <option value="all">All</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div class="filter">
            <label>Activity:</label>
            <select v-model="activityFilter" @change="filterClients">
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="week">Last 7 Days</option>
              <option value="month">Last 30 Days</option>
              <option value="quarter">Last 90 Days</option>
              <option value="year">Last Year</option>
            </select>
          </div>

          <div class="filter">
            <label>Sort By:</label>
            <select v-model="sortBy" @change="filterClients">
              <option value="name">Name</option>
              <option value="company">Company</option>
              <option value="date_created">Date Created</option>
              <option value="last_activity">Last Activity</option>
              <option value="engagement">Engagement Score</option>
            </select>
          </div>

          <div class="filter">
            <label>Order:</label>
            <select v-model="sortOrder" @change="filterClients">
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
        </div>

        <div class="filter-row">
          <div class="filter filter-tags">
            <label>Tags:</label>
            <div class="tag-selector">
              <div
                v-for="tag in availableTags"
                :key="tag.id"
                class="tag"
                :class="{ active: selectedTags.includes(tag.id) }"
                @click="toggleTag(tag.id)"
              >
                {{ tag.name }}
              </div>
            </div>
          </div>

          <div class="filter">
            <label>View:</label>
            <div class="view-toggle">
              <button
                class="view-toggle-btn"
                :class="{ active: viewMode === 'grid' }"
                @click="viewMode = 'grid'"
                title="Grid View"
              >
                <i class="material-icons">grid_view</i>
              </button>
              <button
                class="view-toggle-btn"
                :class="{ active: viewMode === 'list' }"
                @click="viewMode = 'list'"
                title="List View"
              >
                <i class="material-icons">view_list</i>
              </button>
            </div>
          </div>
        </div>

        <div class="active-filters" v-if="hasActiveFilters">
          <div class="active-filters-label">Active Filters:</div>
          <div class="active-filter-tags">
            <div v-if="statusFilter !== 'all'" class="active-filter">
              Status: {{ statusFilter }}
              <button @click="clearFilter('status')" class="clear-filter">×</button>
            </div>
            <div v-if="activityFilter !== 'all'" class="active-filter">
              Activity: {{ getActivityFilterLabel() }}
              <button @click="clearFilter('activity')" class="clear-filter">×</button>
            </div>
            <div v-for="tagId in selectedTags" :key="tagId" class="active-filter">
              Tag: {{ getTagName(tagId) }}
              <button @click="removeTag(tagId)" class="clear-filter">×</button>
            </div>
            <div v-if="searchQuery" class="active-filter">
              Search: "{{ searchQuery }}"
              <button @click="clearFilter('search')" class="clear-filter">×</button>
            </div>
          </div>
          <button @click="clearAllFilters" class="clear-all-filters">
            Clear All
          </button>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading clients...</span>
    </div>

    <div v-else-if="filteredClients.length === 0" class="empty-state">
      <i class="material-icons">people</i>
      <h3>No clients found</h3>
      <p v-if="searchQuery">No clients match your search criteria. Try adjusting your filters.</p>
      <p v-else>You haven't added any clients yet. Click "Add Client" to get started.</p>
    </div>

    <div v-else>
      <!-- Grid View -->
      <div v-if="viewMode === 'grid'" class="client-grid">
        <div
          v-for="client in filteredClients"
          :key="client.id"
          class="client-card"
          :class="{ 'client-card--inactive': client.status === 'inactive' }"
        >
        <div class="client-avatar">
          <img v-if="client.avatar" :src="client.avatar" :alt="client.name">
          <div v-else class="placeholder-avatar">
            {{ getInitials(client.name) }}
          </div>
          <div class="client-status" :class="client.status">
            {{ client.status }}
          </div>
        </div>
        <div class="client-info">
          <h3>{{ client.name }}</h3>
          <p class="client-company">{{ client.company || 'No company' }}</p>
          <div class="client-contact">
            <div v-if="client.email" class="contact-item">
              <i class="material-icons">email</i>
              <span>{{ client.email }}</span>
            </div>
            <div v-if="client.phone" class="contact-item">
              <i class="material-icons">phone</i>
              <span>{{ client.phone }}</span>
            </div>
          </div>
          <div class="client-tags" v-if="client.tags && client.tags.length > 0">
            <div v-for="tagId in client.tags" :key="tagId" class="client-tag">
              {{ getTagName(tagId) }}
            </div>
          </div>
          <div class="client-meta">
            <span class="client-id">ID: {{ client.id.substring(0, 8) }}...</span>
            <span class="client-date">Last Activity: {{ formatDate(client.last_activity) }}</span>
          </div>
          <div class="client-engagement" v-if="client.engagement !== undefined">
            <div class="engagement-label">Engagement:</div>
            <div class="engagement-bar">
              <div
                class="engagement-progress"
                :style="{ width: `${client.engagement}%` }"
                :class="{
                  'high': client.engagement >= 70,
                  'medium': client.engagement >= 40 && client.engagement < 70,
                  'low': client.engagement < 40
                }"
              ></div>
            </div>
            <div class="engagement-score">{{ client.engagement }}</div>
          </div>
        </div>
        <div class="client-actions">
          <button class="btn btn-icon" @click="viewClient(client)">
            <i class="material-icons">visibility</i>
          </button>
          <button class="btn btn-icon" @click="editClient(client)">
            <i class="material-icons">edit</i>
          </button>
          <button class="btn btn-icon" @click="confirmDeleteClient(client)">
            <i class="material-icons">delete</i>
          </button>
        </div>
      </div>

      <!-- List View -->
      <div v-else-if="viewMode === 'list'" class="client-list">
        <table class="client-table">
          <thead>
            <tr>
              <th class="col-name">Name</th>
              <th class="col-company">Company</th>
              <th class="col-contact">Contact</th>
              <th class="col-tags">Tags</th>
              <th class="col-activity">Last Activity</th>
              <th class="col-engagement">Engagement</th>
              <th class="col-status">Status</th>
              <th class="col-actions">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="client in filteredClients"
              :key="client.id"
              :class="{ 'row-inactive': client.status === 'inactive' }"
            >
              <td class="col-name">
                <div class="list-name-container">
                  <div class="list-avatar">
                    <img v-if="client.avatar" :src="client.avatar" :alt="client.name">
                    <div v-else class="placeholder-avatar small">
                      {{ getInitials(client.name) }}
                    </div>
                  </div>
                  <span>{{ client.name }}</span>
                </div>
              </td>
              <td class="col-company">{{ client.company || '—' }}</td>
              <td class="col-contact">
                <div class="list-contact">
                  <div v-if="client.email" class="list-contact-item">
                    <i class="material-icons">email</i>
                    <span>{{ client.email }}</span>
                  </div>
                  <div v-if="client.phone" class="list-contact-item">
                    <i class="material-icons">phone</i>
                    <span>{{ client.phone }}</span>
                  </div>
                </div>
              </td>
              <td class="col-tags">
                <div class="list-tags" v-if="client.tags && client.tags.length > 0">
                  <div v-for="(tagId, index) in client.tags.slice(0, 2)" :key="tagId" class="client-tag small">
                    {{ getTagName(tagId) }}
                  </div>
                  <div v-if="client.tags.length > 2" class="client-tag small more">
                    +{{ client.tags.length - 2 }}
                  </div>
                </div>
                <span v-else>—</span>
              </td>
              <td class="col-activity">{{ formatDate(client.last_activity) }}</td>
              <td class="col-engagement">
                <div v-if="client.engagement !== undefined" class="list-engagement">
                  <div class="engagement-bar small">
                    <div
                      class="engagement-progress"
                      :style="{ width: `${client.engagement}%` }"
                      :class="{
                        'high': client.engagement >= 70,
                        'medium': client.engagement >= 40 && client.engagement < 70,
                        'low': client.engagement < 40
                      }"
                    ></div>
                  </div>
                  <span class="engagement-score">{{ client.engagement }}</span>
                </div>
                <span v-else>—</span>
              </td>
              <td class="col-status">
                <div class="client-status" :class="client.status">
                  {{ client.status }}
                </div>
              </td>
              <td class="col-actions">
                <div class="list-actions">
                  <button class="btn btn-icon" @click="viewClient(client)" title="View Details">
                    <i class="material-icons">visibility</i>
                  </button>
                  <button class="btn btn-icon" @click="editClient(client)" title="Edit Client">
                    <i class="material-icons">edit</i>
                  </button>
                  <button class="btn btn-icon" @click="confirmDeleteClient(client)" title="Delete Client">
                    <i class="material-icons">delete</i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add/Edit Client Modal -->
    <div v-if="showAddClientModal || showEditClientModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ showEditClientModal ? 'Edit Client' : 'Add New Client' }}</h3>
          <button class="btn btn-icon" @click="closeModals">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="client-name">Client Name</label>
            <input
              id="client-name"
              type="text"
              v-model="clientForm.name"
              placeholder="Enter client name"
              required
            />
          </div>
          <div class="form-group">
            <label for="client-company">Company</label>
            <input
              id="client-company"
              type="text"
              v-model="clientForm.company"
              placeholder="Enter company name"
            />
          </div>
          <div class="form-group">
            <label for="client-email">Email</label>
            <input
              id="client-email"
              type="email"
              v-model="clientForm.email"
              placeholder="Enter email address"
            />
          </div>
          <div class="form-group">
            <label for="client-phone">Phone</label>
            <input
              id="client-phone"
              type="tel"
              v-model="clientForm.phone"
              placeholder="Enter phone number"
            />
          </div>
          <div class="form-group">
            <label for="client-status">Status</label>
            <select id="client-status" v-model="clientForm.status">
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          <div class="form-group">
            <label for="client-notes">Notes</label>
            <textarea
              id="client-notes"
              v-model="clientForm.notes"
              placeholder="Enter notes about this client"
              rows="3"
            ></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeModals">Cancel</button>
          <button
            class="btn btn-primary"
            @click="saveClient"
            :disabled="!clientForm.name"
          >
            {{ showEditClientModal ? 'Update Client' : 'Add Client' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Confirm Delete</h3>
          <button class="btn btn-icon" @click="showDeleteModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to delete the client "{{ clientToDelete?.name }}"?</p>
          <p class="warning">This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showDeleteModal = false">Cancel</button>
          <button class="btn btn-danger" @click="deleteClient">Delete Client</button>
        </div>
      </div>
    </div>

    <!-- Client Details Modal -->
    <div v-if="showClientDetailsModal" class="modal">
      <div class="modal-content modal-content-large">
        <div class="modal-header">
          <h3>Client Details</h3>
          <div class="modal-header-actions">
            <button class="btn btn-icon" @click="showAddActivityModal = true" title="Add Activity">
              <i class="material-icons">add_circle</i>
            </button>
            <button class="btn btn-icon" @click="showClientDetailsModal = false">
              <i class="material-icons">close</i>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div v-if="selectedClient" class="client-details">
            <div class="client-details-header">
              <div class="client-details-avatar">
                <img v-if="selectedClient.avatar" :src="selectedClient.avatar" :alt="selectedClient.name">
                <div v-else class="placeholder-avatar large">
                  {{ getInitials(selectedClient.name) }}
                </div>
              </div>
              <div class="client-details-info">
                <h2>{{ selectedClient.name }}</h2>
                <p v-if="selectedClient.company" class="client-company">{{ selectedClient.company }}</p>
                <div class="client-meta-info">
                  <div class="client-status" :class="selectedClient.status">
                    {{ selectedClient.status }}
                  </div>
                  <div class="client-tags" v-if="selectedClient.tags && selectedClient.tags.length > 0">
                    <div v-for="tagId in selectedClient.tags" :key="tagId" class="client-tag">
                      {{ getTagName(tagId) }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="client-details-metrics">
                <div class="metric-card">
                  <div class="metric-value">{{ formatDate(selectedClient.date_created) }}</div>
                  <div class="metric-label">Client Since</div>
                </div>
                <div class="metric-card">
                  <div class="metric-value">{{ getClientAge(selectedClient.date_created) }}</div>
                  <div class="metric-label">Relationship</div>
                </div>
                <div class="metric-card" v-if="selectedClient.engagement !== undefined">
                  <div class="metric-value">
                    <div class="engagement-score-large">{{ selectedClient.engagement }}</div>
                    <div class="engagement-bar">
                      <div
                        class="engagement-progress"
                        :style="{ width: `${selectedClient.engagement}%` }"
                        :class="{
                          'high': selectedClient.engagement >= 70,
                          'medium': selectedClient.engagement >= 40 && selectedClient.engagement < 70,
                          'low': selectedClient.engagement < 40
                        }"
                      ></div>
                    </div>
                  </div>
                  <div class="metric-label">Engagement</div>
                </div>
              </div>
            </div>

            <div class="client-details-tabs">
              <div
                class="client-details-tab"
                :class="{ active: activeClientTab === 'overview' }"
                @click="activeClientTab = 'overview'"
              >
                Overview
              </div>
              <div
                class="client-details-tab"
                :class="{ active: activeClientTab === 'activity' }"
                @click="activeClientTab = 'activity'"
              >
                Activity
              </div>
              <div
                class="client-details-tab"
                :class="{ active: activeClientTab === 'communication' }"
                @click="activeClientTab = 'communication'"
              >
                Communication
              </div>
              <div
                class="client-details-tab"
                :class="{ active: activeClientTab === 'orders' }"
                @click="activeClientTab = 'orders'"
              >
                Orders
              </div>
              <div
                class="client-details-tab"
                :class="{ active: activeClientTab === 'analytics' }"
                @click="activeClientTab = 'analytics'"
              >
                Analytics
              </div>
              <div
                class="client-details-tab"
                :class="{ active: activeClientTab === 'preferences' }"
                @click="activeClientTab = 'preferences'"
              >
                Preferences
              </div>
            </div>

            <!-- Overview Tab -->
            <div v-if="activeClientTab === 'overview'" class="client-details-tab-content">
              <div class="client-details-section">
                <h4>Contact Information</h4>
                <div class="contact-grid">
                  <div v-if="selectedClient.email" class="contact-item">
                    <i class="material-icons">email</i>
                    <span>{{ selectedClient.email }}</span>
                    <button class="btn btn-icon btn-small" @click="copyToClipboard(selectedClient.email)" title="Copy to clipboard">
                      <i class="material-icons">content_copy</i>
                    </button>
                  </div>
                  <div v-if="selectedClient.phone" class="contact-item">
                    <i class="material-icons">phone</i>
                    <span>{{ selectedClient.phone }}</span>
                    <button class="btn btn-icon btn-small" @click="copyToClipboard(selectedClient.phone)" title="Copy to clipboard">
                      <i class="material-icons">content_copy</i>
                    </button>
                  </div>
                </div>
              </div>

              <div class="client-details-section">
                <div class="section-header-with-actions">
                  <h4>Notes</h4>
                  <button class="btn btn-icon" @click="editNotes" title="Edit Notes">
                    <i class="material-icons">edit</i>
                  </button>
                </div>
                <div v-if="editingNotes" class="notes-editor">
                  <textarea
                    v-model="editedNotes"
                    rows="4"
                    placeholder="Enter notes about this client"
                  ></textarea>
                  <div class="notes-editor-actions">
                    <button class="btn btn-secondary" @click="cancelEditNotes">Cancel</button>
                    <button class="btn btn-primary" @click="saveNotes">Save Notes</button>
                  </div>
                </div>
                <div v-else>
                  <p v-if="selectedClient.notes" class="client-notes">{{ selectedClient.notes }}</p>
                  <p v-else class="empty-text">No notes available</p>
                </div>
              </div>

              <div class="client-details-section">
                <h4>Recent Activity</h4>
                <div v-if="selectedClient.activity && selectedClient.activity.length > 0" class="activity-timeline">
                  <div
                    v-for="(activity, index) in selectedClient.activity.slice(0, 3)"
                    :key="index"
                    class="activity-item"
                  >
                    <div class="activity-icon">
                      <i class="material-icons">{{ getActivityIcon(activity.type) }}</i>
                    </div>
                    <div class="activity-content">
                      <div class="activity-header">
                        <span class="activity-type">{{ formatActivityType(activity.type) }}</span>
                        <span class="activity-date">{{ formatDateTime(activity.date) }}</span>
                      </div>
                      <p class="activity-description">{{ activity.description }}</p>
                    </div>
                  </div>
                  <div v-if="selectedClient.activity.length > 3" class="view-all-link">
                    <button class="btn btn-text" @click="activeClientTab = 'activity'">
                      View all activity ({{ selectedClient.activity.length }})
                    </button>
                  </div>
                </div>
                <p v-else class="empty-text">No activity recorded</p>
              </div>
            </div>

            <!-- Activity Tab -->
            <div v-if="activeClientTab === 'activity'" class="client-details-tab-content">
              <div class="client-details-section">
                <div class="section-header-with-actions">
                  <h4>Activity History</h4>
                  <div class="section-actions">
                    <div class="filter">
                      <select v-model="activityTypeFilter" @change="filterClientActivity">
                        <option value="all">All Types</option>
                        <option value="visit">Visits</option>
                        <option value="order">Orders</option>
                        <option value="message">Messages</option>
                        <option value="call">Calls</option>
                        <option value="meeting">Meetings</option>
                      </select>
                    </div>
                    <button class="btn btn-primary" @click="showAddActivityModal = true">
                      <i class="material-icons">add</i> Add Activity
                    </button>
                  </div>
                </div>
                <div v-if="filteredClientActivity && filteredClientActivity.length > 0" class="activity-timeline">
                  <div
                    v-for="(activity, index) in filteredClientActivity"
                    :key="index"
                    class="activity-item"
                  >
                    <div class="activity-icon">
                      <i class="material-icons">{{ getActivityIcon(activity.type) }}</i>
                    </div>
                    <div class="activity-content">
                      <div class="activity-header">
                        <span class="activity-type">{{ formatActivityType(activity.type) }}</span>
                        <span class="activity-date">{{ formatDateTime(activity.date) }}</span>
                      </div>
                      <p class="activity-description">{{ activity.description }}</p>
                    </div>
                    <div class="activity-actions">
                      <button class="btn btn-icon" @click="editActivity(index)" title="Edit Activity">
                        <i class="material-icons">edit</i>
                      </button>
                      <button class="btn btn-icon" @click="deleteActivity(index)" title="Delete Activity">
                        <i class="material-icons">delete</i>
                      </button>
                    </div>
                  </div>
                </div>
                <p v-else class="empty-text">No activity recorded</p>
              </div>
            </div>

            <!-- Orders Tab -->
            <div v-if="activeClientTab === 'orders'" class="client-details-tab-content">
              <div class="client-details-section">
                <div class="section-header-with-actions">
                  <h4>Order History</h4>
                  <button class="btn btn-primary">
                    <i class="material-icons">add</i> Create Order
                  </button>
                </div>
                <div v-if="clientOrders && clientOrders.length > 0" class="orders-list">
                  <div class="orders-header">
                    <div class="col-order-id">Order ID</div>
                    <div class="col-order-date">Date</div>
                    <div class="col-order-items">Items</div>
                    <div class="col-order-total">Total</div>
                    <div class="col-order-status">Status</div>
                    <div class="col-order-actions">Actions</div>
                  </div>
                  <div
                    v-for="order in clientOrders"
                    :key="order.id"
                    class="order-item"
                  >
                    <div class="col-order-id">{{ order.id }}</div>
                    <div class="col-order-date">{{ formatDate(order.date) }}</div>
                    <div class="col-order-items">{{ order.items.length }} items</div>
                    <div class="col-order-total">${{ order.total.toFixed(2) }}</div>
                    <div class="col-order-status">
                      <div class="order-status" :class="order.status">{{ order.status }}</div>
                    </div>
                    <div class="col-order-actions">
                      <button class="btn btn-icon" title="View Order">
                        <i class="material-icons">visibility</i>
                      </button>
                    </div>
                  </div>
                </div>
                <p v-else class="empty-text">No orders found</p>
              </div>
            </div>

            <!-- Communication Tab -->
            <div v-if="activeClientTab === 'communication'" class="client-details-tab-content">
              <div class="client-details-section">
                <div class="communication-tools">
                  <div class="communication-tool-card">
                    <div class="communication-tool-header">
                      <i class="material-icons">email</i>
                      <h4>Email</h4>
                    </div>
                    <div class="communication-tool-content">
                      <div class="form-group">
                        <label for="email-subject">Subject</label>
                        <input
                          id="email-subject"
                          type="text"
                          v-model="emailForm.subject"
                          placeholder="Enter email subject"
                        />
                      </div>
                      <div class="form-group">
                        <label for="email-template">Template</label>
                        <select id="email-template" v-model="emailForm.template" @change="selectEmailTemplate">
                          <option value="">Select a template</option>
                          <option value="introduction">Introduction</option>
                          <option value="follow-up">Follow-up</option>
                          <option value="quote">Quote</option>
                          <option value="order-confirmation">Order Confirmation</option>
                          <option value="custom">Custom</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="email-message">Message</label>
                        <textarea
                          id="email-message"
                          v-model="emailForm.message"
                          rows="6"
                          placeholder="Enter your message"
                        ></textarea>
                      </div>
                      <div class="form-group">
                        <label for="email-attachment">Attachments</label>
                        <div class="file-upload">
                          <button class="btn btn-secondary" @click="triggerFileInput">
                            <i class="material-icons">attach_file</i> Add Attachment
                          </button>
                          <input
                            id="email-attachment"
                            type="file"
                            ref="fileInput"
                            @change="handleFileUpload"
                            style="display: none"
                            multiple
                          />
                        </div>
                        <div v-if="emailForm.attachments.length > 0" class="attachment-list">
                          <div v-for="(file, index) in emailForm.attachments" :key="index" class="attachment-item">
                            <i class="material-icons">insert_drive_file</i>
                            <span>{{ file.name }}</span>
                            <button class="btn btn-icon" @click="removeAttachment(index)">
                              <i class="material-icons">close</i>
                            </button>
                          </div>
                        </div>
                      </div>
                      <div class="form-actions">
                        <button class="btn btn-secondary" @click="saveEmailDraft">Save Draft</button>
                        <button
                          class="btn btn-primary"
                          @click="sendEmailMessage"
                          :disabled="!emailForm.subject || !emailForm.message"
                        >
                          Send Email
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="communication-tool-card">
                    <div class="communication-tool-header">
                      <i class="material-icons">sms</i>
                      <h4>SMS</h4>
                    </div>
                    <div class="communication-tool-content">
                      <div class="form-group">
                        <label for="sms-template">Template</label>
                        <select id="sms-template" v-model="smsForm.template" @change="selectSmsTemplate">
                          <option value="">Select a template</option>
                          <option value="appointment-reminder">Appointment Reminder</option>
                          <option value="order-status">Order Status</option>
                          <option value="follow-up">Follow-up</option>
                          <option value="custom">Custom</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="sms-message">Message</label>
                        <textarea
                          id="sms-message"
                          v-model="smsForm.message"
                          rows="4"
                          placeholder="Enter your message"
                          maxlength="160"
                        ></textarea>
                        <div class="character-count">
                          {{ smsForm.message.length }}/160 characters
                        </div>
                      </div>
                      <div class="form-actions">
                        <button
                          class="btn btn-primary"
                          @click="sendSmsMessage"
                          :disabled="!smsForm.message"
                        >
                          Send SMS
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="communication-tool-card">
                    <div class="communication-tool-header">
                      <i class="material-icons">event</i>
                      <h4>Schedule</h4>
                    </div>
                    <div class="communication-tool-content">
                      <div class="form-group">
                        <label for="schedule-type">Type</label>
                        <select id="schedule-type" v-model="scheduleForm.type">
                          <option value="call">Phone Call</option>
                          <option value="meeting">Meeting</option>
                          <option value="showroom-visit">Showroom Visit</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="schedule-date">Date & Time</label>
                        <input
                          id="schedule-date"
                          type="datetime-local"
                          v-model="scheduleForm.dateTime"
                        />
                      </div>
                      <div class="form-group">
                        <label for="schedule-duration">Duration</label>
                        <select id="schedule-duration" v-model="scheduleForm.duration">
                          <option value="15">15 minutes</option>
                          <option value="30">30 minutes</option>
                          <option value="45">45 minutes</option>
                          <option value="60">1 hour</option>
                          <option value="90">1.5 hours</option>
                          <option value="120">2 hours</option>
                        </select>
                      </div>
                      <div class="form-group">
                        <label for="schedule-notes">Notes</label>
                        <textarea
                          id="schedule-notes"
                          v-model="scheduleForm.notes"
                          rows="3"
                          placeholder="Enter any notes or agenda items"
                        ></textarea>
                      </div>
                      <div class="form-group">
                        <label class="checkbox-label">
                          <input type="checkbox" v-model="scheduleForm.sendReminder">
                          <span>Send reminder to client</span>
                        </label>
                      </div>
                      <div class="form-actions">
                        <button
                          class="btn btn-primary"
                          @click="scheduleEvent"
                          :disabled="!scheduleForm.dateTime"
                        >
                          Schedule
                        </button>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="communication-history">
                  <h4>Communication History</h4>
                  <div class="communication-tabs">
                    <div
                      class="communication-tab"
                      :class="{ active: activeCommunicationTab === 'all' }"
                      @click="activeCommunicationTab = 'all'"
                    >
                      All
                    </div>
                    <div
                      class="communication-tab"
                      :class="{ active: activeCommunicationTab === 'email' }"
                      @click="activeCommunicationTab = 'email'"
                    >
                      Email
                    </div>
                    <div
                      class="communication-tab"
                      :class="{ active: activeCommunicationTab === 'sms' }"
                      @click="activeCommunicationTab = 'sms'"
                    >
                      SMS
                    </div>
                    <div
                      class="communication-tab"
                      :class="{ active: activeCommunicationTab === 'calls' }"
                      @click="activeCommunicationTab = 'calls'"
                    >
                      Calls
                    </div>
                  </div>

                  <div class="communication-list">
                    <div v-if="filteredCommunications.length === 0" class="empty-state">
                      <p>No communication history found</p>
                    </div>
                    <div v-else>
                      <div
                        v-for="(comm, index) in filteredCommunications"
                        :key="index"
                        class="communication-item"
                        :class="comm.type"
                      >
                        <div class="communication-icon">
                          <i class="material-icons">{{ getCommunicationIcon(comm.type) }}</i>
                        </div>
                        <div class="communication-content">
                          <div class="communication-header">
                            <div class="communication-type">{{ formatCommunicationType(comm.type) }}</div>
                            <div class="communication-date">{{ formatDateTime(comm.date) }}</div>
                          </div>
                          <div v-if="comm.type === 'email'" class="communication-subject">
                            {{ comm.subject }}
                          </div>
                          <div class="communication-message">{{ comm.message }}</div>
                          <div v-if="comm.attachments && comm.attachments.length > 0" class="communication-attachments">
                            <div v-for="(attachment, i) in comm.attachments" :key="i" class="communication-attachment">
                              <i class="material-icons">attach_file</i>
                              <span>{{ attachment }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Analytics Tab -->
            <div v-if="activeClientTab === 'analytics'" class="client-details-tab-content">
              <div class="client-details-section">
                <div class="analytics-overview">
                  <div class="analytics-metrics">
                    <div class="analytics-metric-card">
                      <div class="analytics-metric-icon">
                        <i class="material-icons">shopping_cart</i>
                      </div>
                      <div class="analytics-metric-content">
                        <div class="analytics-metric-value">{{ clientAnalytics.totalOrders || 0 }}</div>
                        <div class="analytics-metric-label">Total Orders</div>
                      </div>
                    </div>

                    <div class="analytics-metric-card">
                      <div class="analytics-metric-icon">
                        <i class="material-icons">attach_money</i>
                      </div>
                      <div class="analytics-metric-content">
                        <div class="analytics-metric-value">${{ formatNumber(clientAnalytics.totalSpent || 0) }}</div>
                        <div class="analytics-metric-label">Total Spent</div>
                      </div>
                    </div>

                    <div class="analytics-metric-card">
                      <div class="analytics-metric-icon">
                        <i class="material-icons">calendar_today</i>
                      </div>
                      <div class="analytics-metric-content">
                        <div class="analytics-metric-value">{{ getClientAge(selectedClient?.date_created) }}</div>
                        <div class="analytics-metric-label">Client Age</div>
                      </div>
                    </div>

                    <div class="analytics-metric-card">
                      <div class="analytics-metric-icon">
                        <i class="material-icons">trending_up</i>
                      </div>
                      <div class="analytics-metric-content">
                        <div class="analytics-metric-value">{{ selectedClient?.engagement || 0 }}</div>
                        <div class="analytics-metric-label">Engagement Score</div>
                      </div>
                    </div>
                  </div>

                  <div class="analytics-charts">
                    <div class="analytics-chart">
                      <h4>Activity Over Time</h4>
                      <div class="chart-placeholder">
                        <div class="chart-bars">
                          <div class="chart-bar" v-for="(value, index) in clientAnalytics.activityData" :key="index" :style="{ height: `${value}%` }"></div>
                        </div>
                        <div class="chart-labels">
                          <div class="chart-label" v-for="(label, index) in clientAnalytics.activityLabels" :key="index">{{ label }}</div>
                        </div>
                      </div>
                    </div>

                    <div class="analytics-chart">
                      <h4>Order History</h4>
                      <div class="chart-placeholder">
                        <div class="chart-line">
                          <svg viewBox="0 0 300 100" preserveAspectRatio="none">
                            <polyline
                              fill="none"
                              stroke="var(--theme--primary)"
                              stroke-width="2"
                              points="0,100 50,80 100,90 150,40 200,60 250,30 300,50"
                            />
                          </svg>
                        </div>
                        <div class="chart-labels">
                          <div class="chart-label" v-for="(label, index) in clientAnalytics.orderLabels" :key="index">{{ label }}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="analytics-details">
                    <div class="analytics-detail-section">
                      <h4>Engagement Breakdown</h4>
                      <div class="engagement-breakdown">
                        <div class="engagement-item" v-for="(item, index) in clientAnalytics.engagementBreakdown" :key="index">
                          <div class="engagement-item-label">{{ item.label }}</div>
                          <div class="engagement-item-bar">
                            <div class="engagement-item-progress" :style="{ width: `${item.value}%` }"></div>
                          </div>
                          <div class="engagement-item-value">{{ item.value }}%</div>
                        </div>
                      </div>
                    </div>

                    <div class="analytics-detail-section">
                      <h4>Product Interests</h4>
                      <div class="product-interests">
                        <div class="product-interest-item" v-for="(item, index) in clientAnalytics.productInterests" :key="index">
                          <div class="product-interest-icon">
                            <i class="material-icons">{{ item.icon }}</i>
                          </div>
                          <div class="product-interest-details">
                            <div class="product-interest-name">{{ item.name }}</div>
                            <div class="product-interest-bar">
                              <div class="product-interest-progress" :style="{ width: `${item.percentage}%` }"></div>
                            </div>
                          </div>
                          <div class="product-interest-value">{{ item.percentage }}%</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="analytics-recommendations">
                    <h4>Recommendations</h4>
                    <div class="recommendation-items">
                      <div class="recommendation-item" v-for="(item, index) in clientAnalytics.recommendations" :key="index">
                        <div class="recommendation-icon">
                          <i class="material-icons">{{ item.icon }}</i>
                        </div>
                        <div class="recommendation-content">
                          <div class="recommendation-title">{{ item.title }}</div>
                          <div class="recommendation-description">{{ item.description }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Preferences Tab -->
            <div v-if="activeClientTab === 'preferences'" class="client-details-tab-content">
              <div class="client-details-section">
                <h4>Client Preferences</h4>
                <div class="preferences-grid">
                  <div class="preference-group">
                    <h5>Communication Preferences</h5>
                    <div class="preference-item">
                      <label class="checkbox-label">
                        <input type="checkbox" v-model="clientPreferences.emailUpdates">
                        <span>Email Updates</span>
                      </label>
                    </div>
                    <div class="preference-item">
                      <label class="checkbox-label">
                        <input type="checkbox" v-model="clientPreferences.smsNotifications">
                        <span>SMS Notifications</span>
                      </label>
                    </div>
                    <div class="preference-item">
                      <label class="checkbox-label">
                        <input type="checkbox" v-model="clientPreferences.phoneContact">
                        <span>Phone Contact</span>
                      </label>
                    </div>
                  </div>

                  <div class="preference-group">
                    <h5>Product Interests</h5>
                    <div class="preference-item">
                      <label class="checkbox-label">
                        <input type="checkbox" v-model="clientPreferences.furniture">
                        <span>Furniture</span>
                      </label>
                    </div>
                    <div class="preference-item">
                      <label class="checkbox-label">
                        <input type="checkbox" v-model="clientPreferences.lighting">
                        <span>Lighting</span>
                      </label>
                    </div>
                    <div class="preference-item">
                      <label class="checkbox-label">
                        <input type="checkbox" v-model="clientPreferences.decor">
                        <span>Decor</span>
                      </label>
                    </div>
                    <div class="preference-item">
                      <label class="checkbox-label">
                        <input type="checkbox" v-model="clientPreferences.outdoor">
                        <span>Outdoor</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div class="preference-actions">
                  <button class="btn btn-primary" @click="savePreferences">Save Preferences</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer-actions">
            <button class="btn btn-icon" @click="sendEmail" title="Send Email">
              <i class="material-icons">email</i>
            </button>
            <button class="btn btn-icon" @click="scheduleCall" title="Schedule Call">
              <i class="material-icons">call</i>
            </button>
            <button class="btn btn-icon" @click="scheduleMeeting" title="Schedule Meeting">
              <i class="material-icons">event</i>
            </button>
          </div>
          <div>
            <button class="btn btn-secondary" @click="showClientDetailsModal = false">Close</button>
            <button class="btn btn-primary" @click="editClient(selectedClient)">Edit Client</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Activity Modal -->
    <div v-if="showAddActivityModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingActivity !== null ? 'Edit Activity' : 'Add Activity' }}</h3>
          <button class="btn btn-icon" @click="closeActivityModal">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="activity-type">Activity Type</label>
            <select id="activity-type" v-model="activityForm.type">
              <option value="visit">Visit</option>
              <option value="order">Order</option>
              <option value="message">Message</option>
              <option value="call">Call</option>
              <option value="meeting">Meeting</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div class="form-group">
            <label for="activity-date">Date & Time</label>
            <input
              id="activity-date"
              type="datetime-local"
              v-model="activityForm.date"
            />
          </div>

          <div class="form-group">
            <label for="activity-description">Description</label>
            <textarea
              id="activity-description"
              v-model="activityForm.description"
              rows="3"
              placeholder="Enter activity description"
            ></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeActivityModal">Cancel</button>
          <button
            class="btn btn-primary"
            @click="saveActivity"
            :disabled="!activityForm.type || !activityForm.description"
          >
            {{ editingActivity !== null ? 'Update Activity' : 'Add Activity' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ClientAnalytics from './ClientAnalytics.vue';

export default {
  name: 'ClientManager',

  components: {
    ClientAnalytics
  },

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      clients: [],
      filteredClients: [],
      loading: true,
      searchQuery: '',
      statusFilter: 'all',
      activityFilter: 'all',
      sortBy: 'last_activity',
      sortOrder: 'desc',
      viewMode: 'grid',
      selectedTags: [],
      availableTags: [
        { id: 'vip', name: 'VIP' },
        { id: 'new', name: 'New Client' },
        { id: 'returning', name: 'Returning' },
        { id: 'commercial', name: 'Commercial' },
        { id: 'residential', name: 'Residential' },
        { id: 'designer', name: 'Designer' },
        { id: 'architect', name: 'Architect' },
        { id: 'contractor', name: 'Contractor' }
      ],
      showAddClientModal: false,
      showEditClientModal: false,
      showDeleteModal: false,
      showClientDetailsModal: false,
      showAddActivityModal: false,
      clientForm: {
        name: '',
        company: '',
        email: '',
        phone: '',
        status: 'active',
        notes: ''
      },
      clientToDelete: null,
      selectedClient: null,
      activeClientTab: 'overview',
      activityTypeFilter: 'all',
      filteredClientActivity: [],
      editingNotes: false,
      editedNotes: '',
      editingActivity: null,
      activityForm: {
        type: 'visit',
        date: '',
        description: ''
      },
      clientPreferences: {
        emailUpdates: true,
        smsNotifications: false,
        phoneContact: true,
        furniture: true,
        lighting: false,
        decor: true,
        outdoor: false
      },
      clientOrders: [
        {
          id: 'ORD-1234',
          date: '2023-05-15T10:30:00Z',
          items: [
            { id: 'PROD-001', name: 'Modern Sofa', quantity: 1, price: 899.99 },
            { id: 'PROD-002', name: 'Coffee Table', quantity: 1, price: 349.99 }
          ],
          total: 1249.98,
          status: 'completed'
        },
        {
          id: 'ORD-1235',
          date: '2023-04-02T14:15:00Z',
          items: [
            { id: 'PROD-003', name: 'Floor Lamp', quantity: 2, price: 129.99 }
          ],
          total: 259.98,
          status: 'completed'
        }
      ],
      // Communication tools data
      activeCommunicationTab: 'all',
      emailForm: {
        template: '',
        subject: '',
        message: '',
        attachments: []
      },
      smsForm: {
        template: '',
        message: ''
      },
      scheduleForm: {
        type: 'call',
        dateTime: '',
        duration: '30',
        notes: '',
        sendReminder: true
      },
      communications: [
        {
          type: 'email',
          date: '2023-05-22T14:30:00Z',
          subject: 'Follow-up on your recent visit',
          message: 'Thank you for visiting our showroom last week. I wanted to follow up on the items you were interested in. Let me know if you have any questions!',
          attachments: ['Product_Catalog.pdf']
        },
        {
          type: 'sms',
          date: '2023-05-20T10:15:00Z',
          message: 'Your order #ORD-1234 has been shipped and will arrive in 3-5 business days. Thank you for your purchase!'
        },
        {
          type: 'call',
          date: '2023-05-18T15:45:00Z',
          message: 'Discussed product options and pricing for the upcoming project. Client requested a formal quote by email.'
        },
        {
          type: 'email',
          date: '2023-05-15T09:30:00Z',
          subject: 'Order Confirmation #ORD-1234',
          message: 'Thank you for your order! This email confirms your recent purchase. Your order number is ORD-1234. Please keep this for your records.',
          attachments: ['Order_Receipt.pdf', 'Delivery_Information.pdf']
        }
      ],
      emailTemplates: {
        'introduction': {
          subject: 'Welcome to our Vendor Portal',
          message: 'Dear {{clientName}},\n\nThank you for your interest in our products. We are excited to work with you and help you find the perfect items for your needs.\n\nPlease let me know if you have any questions or if there's anything specific you're looking for.\n\nBest regards,\n{{vendorName}}'
        },
        'follow-up': {
          subject: 'Following up on your recent interest',
          message: 'Dear {{clientName}},\n\nI wanted to follow up on your recent interest in our products. Have you had a chance to review the information I sent?\n\nI'm available to answer any questions you might have or provide additional information.\n\nBest regards,\n{{vendorName}}'
        },
        'quote': {
          subject: 'Quote for your project',
          message: 'Dear {{clientName}},\n\nThank you for your interest in our products. I've attached a quote for the items we discussed.\n\nPlease review the quote and let me know if you have any questions or if you'd like to make any changes.\n\nBest regards,\n{{vendorName}}'
        },
        'order-confirmation': {
          subject: 'Order Confirmation #{{orderNumber}}',
          message: 'Dear {{clientName}},\n\nThank you for your order! This email confirms your recent purchase. Your order number is {{orderNumber}}.\n\nYour order is being processed and will be shipped soon. You will receive a shipping confirmation with tracking information when your order ships.\n\nBest regards,\n{{vendorName}}'
        }
      },
      smsTemplates: {
        'appointment-reminder': 'Reminder: You have an appointment at our showroom tomorrow at {{time}}. We look forward to seeing you!',
        'order-status': 'Your order #{{orderNumber}} has been {{status}} and will arrive in {{days}} business days. Thank you for your purchase!',
        'follow-up': 'Thank you for your recent visit to our showroom. We hope you enjoyed your experience. Please let us know if you have any questions!'
      },

      // Client analytics data
      clientAnalytics: {
        totalOrders: 8,
        totalSpent: 5750.25,
        activityData: [25, 40, 30, 60, 45, 75, 55],
        activityLabels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        orderLabels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
        engagementBreakdown: [
          { label: 'Visits', value: 85 },
          { label: 'Orders', value: 65 },
          { label: 'Messages', value: 75 },
          { label: 'Calls', value: 40 }
        ],
        productInterests: [
          { name: 'Furniture', percentage: 80, icon: 'chair' },
          { name: 'Lighting', percentage: 45, icon: 'lightbulb' },
          { name: 'Decor', percentage: 65, icon: 'home' },
          { name: 'Outdoor', percentage: 30, icon: 'deck' }
        ],
        recommendations: [
          {
            title: 'Send Follow-up Email',
            description: 'Client hasn\'t been contacted in 2 weeks. Consider sending a follow-up email.',
            icon: 'email'
          },
          {
            title: 'Suggest Complementary Products',
            description: 'Based on recent purchases, client may be interested in coffee tables and side tables.',
            icon: 'shopping_cart'
          },
          {
            title: 'Schedule Showroom Visit',
            description: 'Client has shown interest in new lighting collection. Consider inviting for a showroom visit.',
            icon: 'event'
          }
        ]
      },

      // Global analytics data
      showGlobalAnalytics: false
    };
  },

  computed: {
    hasActiveFilters() {
      return (
        this.statusFilter !== 'all' ||
        this.activityFilter !== 'all' ||
        this.selectedTags.length > 0 ||
        this.searchQuery.trim() !== ''
      );
    },

    filteredCommunications() {
      if (!this.communications) return [];

      if (this.activeCommunicationTab === 'all') {
        return [...this.communications].sort((a, b) => {
          return new Date(b.date) - new Date(a.date);
        });
      } else {
        return this.communications
          .filter(comm => comm.type === this.activeCommunicationTab)
          .sort((a, b) => {
            return new Date(b.date) - new Date(a.date);
          });
      }
    }
  },

  mounted() {
    this.loadClients();
  },

  methods: {
    // Load clients from API
    async loadClients() {
      this.loading = true;

      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/clients?vendor_id=${this.vendorId}`);
        // this.clients = response.data;

        // Mock data for demonstration
        setTimeout(() => {
          this.clients = [
            {
              id: '1234abcd-5678-efgh-ijkl-mnopqrstuvwx',
              name: 'John Smith',
              company: 'Acme Corporation',
              email: '<EMAIL>',
              phone: '(*************',
              status: 'active',
              notes: 'Key decision maker for the new office project.',
              last_activity: '2023-05-20T14:45:00Z',
              date_created: '2023-01-15T09:30:00Z',
              tags: ['vip', 'commercial'],
              engagement: 85,
              activity: [
                {
                  type: 'visit',
                  date: '2023-05-20T14:45:00Z',
                  description: 'Visited the showroom and viewed the Modern Office collection.'
                },
                {
                  type: 'order',
                  date: '2023-05-15T10:30:00Z',
                  description: 'Placed an order for 5 Modern Sofas.'
                },
                {
                  type: 'message',
                  date: '2023-05-10T09:15:00Z',
                  description: 'Requested information about delivery timeframes.'
                }
              ]
            },
            {
              id: '2345bcde-6789-fghi-jklm-nopqrstuvwxy',
              name: 'Sarah Johnson',
              company: 'Johnson Interiors',
              email: '<EMAIL>',
              phone: '(*************',
              status: 'active',
              notes: 'Interior designer looking for modern furniture pieces.',
              last_activity: '2023-05-18T11:20:00Z',
              date_created: '2023-02-20T13:45:00Z',
              tags: ['designer', 'residential', 'returning'],
              engagement: 92,
              activity: [
                {
                  type: 'visit',
                  date: '2023-05-18T11:20:00Z',
                  description: 'Explored the VR showroom with focus on lighting fixtures.'
                },
                {
                  type: 'message',
                  date: '2023-05-05T16:45:00Z',
                  description: 'Inquired about custom furniture options.'
                }
              ]
            },
            {
              id: '3456cdef-7890-ghij-klmn-opqrstuvwxyz',
              name: 'Michael Brown',
              company: 'Brown & Associates',
              email: '<EMAIL>',
              phone: '(*************',
              status: 'inactive',
              notes: 'Former client, may reconnect for upcoming project.',
              last_activity: '2023-04-10T09:15:00Z',
              date_created: '2022-11-05T10:15:00Z',
              tags: ['architect', 'commercial'],
              engagement: 45,
              activity: [
                {
                  type: 'order',
                  date: '2023-04-10T09:15:00Z',
                  description: 'Placed final order for office renovation project.'
                },
                {
                  type: 'visit',
                  date: '2023-04-05T13:30:00Z',
                  description: 'Final showroom visit to confirm selections.'
                }
              ]
            },
            {
              id: '4567defg-8901-hijk-lmno-pqrstuvwxyza',
              name: 'Emily Davis',
              company: 'Davis Home Staging',
              email: '<EMAIL>',
              phone: '(*************',
              status: 'active',
              notes: 'Home stager who frequently needs furniture for staging projects.',
              last_activity: '2023-05-25T15:30:00Z',
              date_created: '2023-03-10T09:00:00Z',
              tags: ['designer', 'residential', 'new'],
              engagement: 78,
              activity: [
                {
                  type: 'order',
                  date: '2023-05-25T15:30:00Z',
                  description: 'Ordered furniture for a new staging project.'
                },
                {
                  type: 'message',
                  date: '2023-05-22T11:45:00Z',
                  description: 'Requested catalog of available items for quick delivery.'
                }
              ]
            },
            {
              id: '5678efgh-9012-ijkl-mnop-qrstuvwxyzab',
              name: 'Robert Wilson',
              company: 'Wilson Construction',
              email: '<EMAIL>',
              phone: '(*************',
              status: 'active',
              notes: 'Contractor working on multiple commercial projects.',
              last_activity: '2023-05-15T13:20:00Z',
              date_created: '2022-09-15T14:30:00Z',
              tags: ['contractor', 'commercial', 'returning'],
              engagement: 65,
              activity: [
                {
                  type: 'meeting',
                  date: '2023-05-15T13:20:00Z',
                  description: 'Met to discuss furniture needs for the new office complex project.'
                },
                {
                  type: 'call',
                  date: '2023-05-10T10:15:00Z',
                  description: 'Called to schedule a meeting about upcoming projects.'
                }
              ]
            }
          ];

          this.filterClients();
          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading clients:', error);
        this.loading = false;
      }
    },

    // Filter clients based on search query and filters
    filterClients() {
      let filtered = [...this.clients];

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(client =>
          client.name.toLowerCase().includes(query) ||
          (client.company && client.company.toLowerCase().includes(query)) ||
          (client.email && client.email.toLowerCase().includes(query)) ||
          (client.phone && client.phone.toLowerCase().includes(query)) ||
          (client.notes && client.notes.toLowerCase().includes(query))
        );
      }

      // Apply status filter
      if (this.statusFilter !== 'all') {
        filtered = filtered.filter(client => client.status === this.statusFilter);
      }

      // Apply activity date filter
      if (this.activityFilter !== 'all') {
        const now = new Date();
        let cutoffDate;

        switch (this.activityFilter) {
          case 'today':
            cutoffDate = new Date(now.setHours(0, 0, 0, 0));
            break;
          case 'week':
            cutoffDate = new Date(now.setDate(now.getDate() - 7));
            break;
          case 'month':
            cutoffDate = new Date(now.setDate(now.getDate() - 30));
            break;
          case 'quarter':
            cutoffDate = new Date(now.setDate(now.getDate() - 90));
            break;
          case 'year':
            cutoffDate = new Date(now.setFullYear(now.getFullYear() - 1));
            break;
        }

        filtered = filtered.filter(client => {
          const lastActivity = new Date(client.last_activity);
          return lastActivity >= cutoffDate;
        });
      }

      // Apply tag filters
      if (this.selectedTags.length > 0) {
        filtered = filtered.filter(client => {
          if (!client.tags) return false;
          return this.selectedTags.some(tag => client.tags.includes(tag));
        });
      }

      // Apply sorting
      filtered.sort((a, b) => {
        let valueA = a[this.sortBy];
        let valueB = b[this.sortBy];

        // Handle dates
        if (this.sortBy.includes('date') || this.sortBy === 'last_activity') {
          valueA = new Date(valueA);
          valueB = new Date(valueB);
        }

        // Handle missing values
        if (valueA === undefined || valueA === null) {
          return this.sortOrder === 'asc' ? -1 : 1;
        }
        if (valueB === undefined || valueB === null) {
          return this.sortOrder === 'asc' ? 1 : -1;
        }

        if (this.sortOrder === 'asc') {
          return valueA > valueB ? 1 : -1;
        } else {
          return valueA < valueB ? 1 : -1;
        }
      });

      this.filteredClients = filtered;
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    // Get initials from name
    getInitials(name) {
      if (!name) return '';

      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },

    // Get icon for activity type
    getActivityIcon(type) {
      switch (type) {
        case 'visit':
          return 'visibility';
        case 'order':
          return 'shopping_cart';
        case 'message':
          return 'message';
        case 'call':
          return 'call';
        case 'meeting':
          return 'people';
        default:
          return 'event';
      }
    },

    // Format date and time
    formatDateTime(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      return date.toLocaleString();
    },

    // Format activity type for display
    formatActivityType(type) {
      if (!type) return '';

      // Capitalize first letter
      return type.charAt(0).toUpperCase() + type.slice(1);
    },

    // Get client age in months/years
    getClientAge(dateString) {
      if (!dateString) return 'N/A';

      const startDate = new Date(dateString);
      const now = new Date();

      const monthDiff = (now.getFullYear() - startDate.getFullYear()) * 12 + (now.getMonth() - startDate.getMonth());

      if (monthDiff < 1) {
        return 'New';
      } else if (monthDiff < 12) {
        return `${monthDiff} month${monthDiff !== 1 ? 's' : ''}`;
      } else {
        const years = Math.floor(monthDiff / 12);
        return `${years} year${years !== 1 ? 's' : ''}`;
      }
    },

    // Format number with commas and decimal places
    formatNumber(number) {
      if (number === undefined || number === null) return '0.00';

      return number.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },

    // Copy text to clipboard
    copyToClipboard(text) {
      navigator.clipboard.writeText(text)
        .then(() => {
          // Show success message (could be implemented with a toast notification)
          console.log('Copied to clipboard');
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
        });
    },

    // Filter client activity by type
    filterClientActivity() {
      if (!this.selectedClient || !this.selectedClient.activity) {
        this.filteredClientActivity = [];
        return;
      }

      if (this.activityTypeFilter === 'all') {
        this.filteredClientActivity = [...this.selectedClient.activity];
      } else {
        this.filteredClientActivity = this.selectedClient.activity.filter(
          activity => activity.type === this.activityTypeFilter
        );
      }

      // Sort by date (newest first)
      this.filteredClientActivity.sort((a, b) => {
        return new Date(b.date) - new Date(a.date);
      });
    },

    // Edit client notes
    editNotes() {
      this.editingNotes = true;
      this.editedNotes = this.selectedClient.notes || '';
    },

    // Cancel editing notes
    cancelEditNotes() {
      this.editingNotes = false;
      this.editedNotes = '';
    },

    // Save client notes
    saveNotes() {
      if (this.selectedClient) {
        this.selectedClient.notes = this.editedNotes;

        // This would be replaced with actual API call
        // await axios.patch(`/api/clients/${this.selectedClient.id}`, { notes: this.editedNotes });

        this.editingNotes = false;
      }
    },

    // Open activity modal for editing
    editActivity(index) {
      this.editingActivity = index;
      const activity = this.filteredClientActivity[index];

      // Format date for datetime-local input
      const date = new Date(activity.date);
      const formattedDate = date.toISOString().slice(0, 16); // Format: YYYY-MM-DDThh:mm

      this.activityForm = {
        type: activity.type,
        date: formattedDate,
        description: activity.description
      };

      this.showAddActivityModal = true;
    },

    // Delete activity
    deleteActivity(index) {
      if (confirm('Are you sure you want to delete this activity?')) {
        const activityToDelete = this.filteredClientActivity[index];

        // Find the activity in the original array
        if (this.selectedClient && this.selectedClient.activity) {
          const originalIndex = this.selectedClient.activity.findIndex(
            a => a.date === activityToDelete.date && a.description === activityToDelete.description
          );

          if (originalIndex !== -1) {
            // This would be replaced with actual API call
            // await axios.delete(`/api/clients/${this.selectedClient.id}/activity/${activityId}`);

            // Remove from the array
            this.selectedClient.activity.splice(originalIndex, 1);

            // Update filtered activities
            this.filterClientActivity();
          }
        }
      }
    },

    // Close activity modal
    closeActivityModal() {
      this.showAddActivityModal = false;
      this.editingActivity = null;
      this.activityForm = {
        type: 'visit',
        date: '',
        description: ''
      };
    },

    // Save activity
    saveActivity() {
      if (!this.selectedClient) return;

      // Create activity object
      const activity = {
        type: this.activityForm.type,
        date: this.activityForm.date ? new Date(this.activityForm.date).toISOString() : new Date().toISOString(),
        description: this.activityForm.description
      };

      if (this.editingActivity !== null) {
        // Update existing activity
        const activityToUpdate = this.filteredClientActivity[this.editingActivity];

        // Find the activity in the original array
        if (this.selectedClient.activity) {
          const originalIndex = this.selectedClient.activity.findIndex(
            a => a.date === activityToUpdate.date && a.description === activityToUpdate.description
          );

          if (originalIndex !== -1) {
            // This would be replaced with actual API call
            // await axios.patch(`/api/clients/${this.selectedClient.id}/activity/${activityId}`, activity);

            // Update the activity
            this.selectedClient.activity[originalIndex] = activity;
          }
        }
      } else {
        // Add new activity
        // This would be replaced with actual API call
        // await axios.post(`/api/clients/${this.selectedClient.id}/activity`, activity);

        // Add to the array
        if (!this.selectedClient.activity) {
          this.selectedClient.activity = [];
        }

        this.selectedClient.activity.push(activity);

        // Update last activity date
        this.selectedClient.last_activity = activity.date;
      }

      // Update filtered activities
      this.filterClientActivity();

      // Close modal
      this.closeActivityModal();
    },

    // Save client preferences
    savePreferences() {
      // This would be replaced with actual API call
      // await axios.patch(`/api/clients/${this.selectedClient.id}/preferences`, this.clientPreferences);

      alert('Client preferences saved successfully');
    },

    // Get icon for communication type
    getCommunicationIcon(type) {
      switch (type) {
        case 'email':
          return 'email';
        case 'sms':
          return 'sms';
        case 'call':
          return 'call';
        default:
          return 'message';
      }
    },

    // Format communication type for display
    formatCommunicationType(type) {
      switch (type) {
        case 'email':
          return 'Email';
        case 'sms':
          return 'SMS';
        case 'call':
          return 'Phone Call';
        default:
          return type.charAt(0).toUpperCase() + type.slice(1);
      }
    },

    // Trigger file input click
    triggerFileInput() {
      this.$refs.fileInput.click();
    },

    // Handle file upload
    handleFileUpload(event) {
      const files = event.target.files;
      if (!files.length) return;

      for (let i = 0; i < files.length; i++) {
        this.emailForm.attachments.push(files[i]);
      }

      // Reset file input
      event.target.value = '';
    },

    // Remove attachment
    removeAttachment(index) {
      this.emailForm.attachments.splice(index, 1);
    },

    // Select email template
    selectEmailTemplate() {
      if (!this.emailForm.template || this.emailForm.template === 'custom') {
        return;
      }

      const template = this.emailTemplates[this.emailForm.template];
      if (template) {
        // Replace placeholders with actual values
        let subject = template.subject;
        let message = template.message;

        if (this.selectedClient) {
          subject = subject.replace(/{{clientName}}/g, this.selectedClient.name);
          message = message.replace(/{{clientName}}/g, this.selectedClient.name);
        }

        // Replace vendor name with current user (would come from auth in real app)
        message = message.replace(/{{vendorName}}/g, 'Your Name');

        // Replace order number if applicable
        if (this.clientOrders && this.clientOrders.length > 0) {
          const latestOrder = this.clientOrders[0];
          subject = subject.replace(/{{orderNumber}}/g, latestOrder.id);
          message = message.replace(/{{orderNumber}}/g, latestOrder.id);
        }

        this.emailForm.subject = subject;
        this.emailForm.message = message;
      }
    },

    // Select SMS template
    selectSmsTemplate() {
      if (!this.smsForm.template || this.smsForm.template === 'custom') {
        return;
      }

      let message = this.smsTemplates[this.smsForm.template];
      if (message) {
        // Replace placeholders with actual values
        if (this.selectedClient) {
          message = message.replace(/{{clientName}}/g, this.selectedClient.name);
        }

        // Replace order details if applicable
        if (this.clientOrders && this.clientOrders.length > 0) {
          const latestOrder = this.clientOrders[0];
          message = message.replace(/{{orderNumber}}/g, latestOrder.id);
          message = message.replace(/{{status}}/g, 'shipped');
          message = message.replace(/{{days}}/g, '3-5');
        }

        // Replace appointment time if applicable
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const timeStr = tomorrow.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        message = message.replace(/{{time}}/g, timeStr);

        this.smsForm.message = message;
      }
    },

    // Save email draft
    saveEmailDraft() {
      // This would be replaced with actual API call
      // await axios.post(`/api/clients/${this.selectedClient.id}/communications/drafts`, this.emailForm);

      alert('Email draft saved successfully');
    },

    // Send email message
    sendEmailMessage() {
      if (!this.selectedClient) return;

      // Create communication object
      const communication = {
        type: 'email',
        date: new Date().toISOString(),
        subject: this.emailForm.subject,
        message: this.emailForm.message,
        attachments: this.emailForm.attachments.map(file => file.name)
      };

      // This would be replaced with actual API call
      // await axios.post(`/api/clients/${this.selectedClient.id}/communications`, communication);

      // Add to communications array
      this.communications.unshift(communication);

      // Update last activity
      this.selectedClient.last_activity = communication.date;

      // Add activity
      if (!this.selectedClient.activity) {
        this.selectedClient.activity = [];
      }

      this.selectedClient.activity.push({
        type: 'message',
        date: communication.date,
        description: `Sent email: ${communication.subject}`
      });

      // Reset form
      this.emailForm = {
        template: '',
        subject: '',
        message: '',
        attachments: []
      };

      // Show success message
      alert('Email sent successfully');
    },

    // Send SMS message
    sendSmsMessage() {
      if (!this.selectedClient) return;

      // Create communication object
      const communication = {
        type: 'sms',
        date: new Date().toISOString(),
        message: this.smsForm.message
      };

      // This would be replaced with actual API call
      // await axios.post(`/api/clients/${this.selectedClient.id}/communications`, communication);

      // Add to communications array
      this.communications.unshift(communication);

      // Update last activity
      this.selectedClient.last_activity = communication.date;

      // Add activity
      if (!this.selectedClient.activity) {
        this.selectedClient.activity = [];
      }

      this.selectedClient.activity.push({
        type: 'message',
        date: communication.date,
        description: `Sent SMS: ${communication.message.substring(0, 30)}${communication.message.length > 30 ? '...' : ''}`
      });

      // Reset form
      this.smsForm = {
        template: '',
        message: ''
      };

      // Show success message
      alert('SMS sent successfully');
    },

    // Schedule event (call, meeting, etc.)
    scheduleEvent() {
      if (!this.selectedClient || !this.scheduleForm.dateTime) return;

      // Create communication object
      const communication = {
        type: this.scheduleForm.type,
        date: new Date().toISOString(),
        message: `Scheduled ${this.scheduleForm.type}: ${this.scheduleForm.notes || 'No notes provided'}`
      };

      // This would be replaced with actual API call
      // await axios.post(`/api/clients/${this.selectedClient.id}/communications`, communication);

      // Add to communications array
      this.communications.unshift(communication);

      // Add activity
      if (!this.selectedClient.activity) {
        this.selectedClient.activity = [];
      }

      const eventDate = new Date(this.scheduleForm.dateTime);
      const formattedDate = eventDate.toLocaleString();

      this.selectedClient.activity.push({
        type: this.scheduleForm.type,
        date: new Date().toISOString(),
        description: `Scheduled ${this.formatCommunicationType(this.scheduleForm.type).toLowerCase()} for ${formattedDate} (${this.scheduleForm.duration} minutes)`
      });

      // Send reminder if enabled
      if (this.scheduleForm.sendReminder) {
        // This would be replaced with actual API call to schedule a reminder
        console.log('Scheduling reminder for event');
      }

      // Reset form
      this.scheduleForm = {
        type: 'call',
        dateTime: '',
        duration: '30',
        notes: '',
        sendReminder: true
      };

      // Show success message
      alert(`${this.formatCommunicationType(this.scheduleForm.type)} scheduled successfully`);
    },

    // Send email to client (quick action)
    sendEmail() {
      if (this.selectedClient && this.selectedClient.email) {
        // Set active tab to communication
        this.activeClientTab = 'communication';

        // Focus on email form
        this.$nextTick(() => {
          document.getElementById('email-subject').focus();
        });
      }
    },

    // Schedule call with client (quick action)
    scheduleCall() {
      // Set active tab to communication
      this.activeClientTab = 'communication';

      // Set schedule form type to call
      this.scheduleForm.type = 'call';

      // Set default date/time (tomorrow at current time)
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      this.scheduleForm.dateTime = tomorrow.toISOString().slice(0, 16); // Format: YYYY-MM-DDThh:mm

      // Focus on schedule form
      this.$nextTick(() => {
        document.getElementById('schedule-date').focus();
      });
    },

    // Schedule meeting with client (quick action)
    scheduleMeeting() {
      // Set active tab to communication
      this.activeClientTab = 'communication';

      // Set schedule form type to meeting
      this.scheduleForm.type = 'meeting';

      // Set default date/time (tomorrow at current time)
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      this.scheduleForm.dateTime = tomorrow.toISOString().slice(0, 16); // Format: YYYY-MM-DDThh:mm

      // Focus on schedule form
      this.$nextTick(() => {
        document.getElementById('schedule-date').focus();
      });
    },

    // Toggle global analytics view
    toggleGlobalAnalytics() {
      this.showGlobalAnalytics = !this.showGlobalAnalytics;
    },

    // View client from analytics component
    viewClientFromAnalytics(client) {
      // Find the client in our list
      const existingClient = this.clients.find(c => c.id === client.id);

      if (existingClient) {
        this.viewClient(existingClient);
      } else {
        // If client doesn't exist in our list, add it
        this.clients.push(client);
        this.viewClient(client);
      }

      // Exit analytics view
      this.showGlobalAnalytics = false;
    },

    // View client details
    viewClient(client) {
      this.selectedClient = client;
      this.activeClientTab = 'overview';
      this.showClientDetailsModal = true;

      // Initialize filtered activities
      this.filterClientActivity();

      // Initialize communication tabs
      this.activeCommunicationTab = 'all';

      // Initialize email form with client name
      this.emailForm = {
        template: '',
        subject: '',
        message: '',
        attachments: []
      };

      // Initialize SMS form
      this.smsForm = {
        template: '',
        message: ''
      };

      // Initialize schedule form
      this.scheduleForm = {
        type: 'call',
        dateTime: '',
        duration: '30',
        notes: '',
        sendReminder: true
      };

      // Initialize client preferences (this would normally come from the API)
      this.clientPreferences = {
        emailUpdates: true,
        smsNotifications: false,
        phoneContact: true,
        furniture: client.tags?.includes('furniture') || false,
        lighting: client.tags?.includes('lighting') || false,
        decor: client.tags?.includes('decor') || false,
        outdoor: client.tags?.includes('outdoor') || false
      };
    },

    // Edit client
    editClient(client) {
      this.clientForm = {
        id: client.id,
        name: client.name,
        company: client.company || '',
        email: client.email || '',
        phone: client.phone || '',
        status: client.status,
        notes: client.notes || ''
      };

      this.showClientDetailsModal = false;
      this.showEditClientModal = true;
    },

    // Confirm delete client
    confirmDeleteClient(client) {
      this.clientToDelete = client;
      this.showDeleteModal = true;
    },

    // Delete client
    async deleteClient() {
      if (!this.clientToDelete) return;

      try {
        // This would be replaced with actual API call
        // await axios.delete(`/api/clients/${this.clientToDelete.id}`);

        // Mock deletion
        this.clients = this.clients.filter(c => c.id !== this.clientToDelete.id);
        this.filterClients();

        this.showDeleteModal = false;
        this.clientToDelete = null;
      } catch (error) {
        console.error('Error deleting client:', error);
      }
    },

    // Save client (create or update)
    async saveClient() {
      try {
        if (this.showEditClientModal) {
          // Update existing client
          // This would be replaced with actual API call
          // await axios.patch(`/api/clients/${this.clientForm.id}`, this.clientForm);

          // Mock update
          const index = this.clients.findIndex(c => c.id === this.clientForm.id);
          if (index !== -1) {
            this.clients[index] = {
              ...this.clients[index],
              ...this.clientForm,
              last_activity: new Date().toISOString()
            };
          }
        } else {
          // Create new client
          // This would be replaced with actual API call
          // const response = await axios.post('/api/clients', {
          //   ...this.clientForm,
          //   vendor_id: this.vendorId
          // });

          // Mock creation
          const newClient = {
            id: `new-${Date.now()}`,
            ...this.clientForm,
            vendor_id: this.vendorId,
            last_activity: new Date().toISOString(),
            activity: []
          };

          this.clients.push(newClient);
        }

        this.filterClients();
        this.closeModals();
      } catch (error) {
        console.error('Error saving client:', error);
      }
    },

    // Toggle tag selection
    toggleTag(tagId) {
      const index = this.selectedTags.indexOf(tagId);
      if (index === -1) {
        this.selectedTags.push(tagId);
      } else {
        this.selectedTags.splice(index, 1);
      }
      this.filterClients();
    },

    // Remove a specific tag
    removeTag(tagId) {
      const index = this.selectedTags.indexOf(tagId);
      if (index !== -1) {
        this.selectedTags.splice(index, 1);
        this.filterClients();
      }
    },

    // Get tag name by ID
    getTagName(tagId) {
      const tag = this.availableTags.find(t => t.id === tagId);
      return tag ? tag.name : tagId;
    },

    // Get activity filter label
    getActivityFilterLabel() {
      switch (this.activityFilter) {
        case 'today': return 'Today';
        case 'week': return 'Last 7 Days';
        case 'month': return 'Last 30 Days';
        case 'quarter': return 'Last 90 Days';
        case 'year': return 'Last Year';
        default: return 'All Time';
      }
    },

    // Clear a specific filter
    clearFilter(filterType) {
      switch (filterType) {
        case 'status':
          this.statusFilter = 'all';
          break;
        case 'activity':
          this.activityFilter = 'all';
          break;
        case 'search':
          this.searchQuery = '';
          break;
      }
      this.filterClients();
    },

    // Clear all filters
    clearAllFilters() {
      this.statusFilter = 'all';
      this.activityFilter = 'all';
      this.searchQuery = '';
      this.selectedTags = [];
      this.filterClients();
    },

    // Close all modals
    closeModals() {
      this.showAddClientModal = false;
      this.showEditClientModal = false;
      this.showDeleteModal = false;
      this.showClientDetailsModal = false;
      this.clientForm = {
        name: '',
        company: '',
        email: '',
        phone: '',
        status: 'active',
        notes: ''
      };
      this.clientToDelete = null;
      this.selectedClient = null;
    }
  }
};
</script>

<style scoped>
.client-manager {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.search {
  position: relative;
}

.search input {
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  width: 200px;
}

.search i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
  font-size: 18px;
}

.filters {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.filter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.filter select {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.filter-tags {
  flex-grow: 1;
}

.tag-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag:hover {
  background-color: var(--theme--border-color);
}

.tag.active {
  background-color: var(--theme--primary);
  color: white;
}

.view-toggle {
  display: flex;
  gap: 5px;
}

.view-toggle-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: var(--theme--border-radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.view-toggle-btn.active {
  background-color: var(--theme--primary);
  color: white;
}

.active-filters {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.active-filters-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--theme--foreground-subdued);
}

.active-filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex-grow: 1;
}

.active-filter {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
  font-size: 12px;
}

.clear-filter {
  background: none;
  border: none;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  color: var(--theme--primary);
}

.clear-filter:hover {
  background-color: rgba(var(--theme--primary-rgb), 0.2);
}

.clear-all-filters {
  background: none;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: var(--theme--danger);
}

.clear-all-filters:hover {
  background-color: var(--theme--danger-background);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 16px;
}

.empty-state h3 {
  margin-bottom: 8px;
  color: var(--theme--foreground);
}

.empty-state p {
  color: var(--theme--foreground-subdued);
  max-width: 400px;
  margin: 0 auto;
}

.client-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.client-card {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background);
  transition: all 0.2s ease;
}

.client-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.client-card--inactive {
  border-left: 4px solid var(--theme--danger);
  opacity: 0.7;
}

.client-avatar {
  height: 100px;
  background-color: var(--theme--background-subdued);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.client-avatar img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.placeholder-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--theme--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
}

.placeholder-avatar.large {
  width: 100px;
  height: 100px;
  font-size: 36px;
}

.client-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.client-status.active {
  background-color: var(--theme--primary);
  color: white;
}

.client-status.inactive {
  background-color: var(--theme--danger);
  color: white;
}

.client-info {
  padding: 16px;
}

.client-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.client-company {
  color: var(--theme--foreground-subdued);
  font-size: 14px;
  margin: 0 0 12px 0;
}

.client-contact {
  margin-bottom: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.contact-item i {
  font-size: 16px;
  color: var(--theme--primary);
}

.client-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-bottom: 10px;
}

.client-tag {
  padding: 3px 6px;
  border-radius: 4px;
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
  font-size: 11px;
  font-weight: 500;
}

.client-tag.small {
  font-size: 10px;
  padding: 2px 4px;
}

.client-tag.more {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
}

.client-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 10px;
}

.client-engagement {
  margin-top: 5px;
}

.engagement-label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 4px;
}

.engagement-bar {
  height: 6px;
  background-color: var(--theme--background-subdued);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 4px;
}

.engagement-bar.small {
  height: 4px;
  width: 80px;
}

.engagement-progress {
  height: 100%;
  border-radius: 3px;
}

.engagement-progress.high {
  background-color: var(--theme--primary);
}

.engagement-progress.medium {
  background-color: var(--theme--warning);
}

.engagement-progress.low {
  background-color: var(--theme--danger);
}

.engagement-score {
  font-size: 12px;
  font-weight: 600;
  color: var(--theme--foreground);
}

.client-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--border-color);
}

.btn-danger {
  background-color: var(--theme--danger);
  color: white;
}

.btn-danger:hover {
  background-color: var(--theme--danger-accent);
}

.btn-icon {
  padding: 6px;
  border-radius: var(--theme--border-radius);
  background-color: transparent;
}

.btn-icon i {
  margin-right: 0;
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-content-large {
  max-width: 800px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--theme--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.warning {
  color: var(--theme--danger);
  font-weight: 500;
}

.client-details-header {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
}

.client-details-avatar {
  flex-shrink: 0;
}

.client-details-info {
  flex-grow: 1;
}

.client-details-info h2 {
  margin: 0 0 8px 0;
}

.client-details-section {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.client-details-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.client-details-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: var(--theme--foreground-subdued);
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.empty-text {
  color: var(--theme--foreground-subdued);
  font-style: italic;
}

.activity-timeline {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  gap: 16px;
}

.activity-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--theme--primary-background);
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon i {
  color: var(--theme--primary);
  font-size: 20px;
}

.activity-content {
  flex-grow: 1;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.activity-type {
  font-weight: 600;
  text-transform: capitalize;
}

.activity-date {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.activity-description {
  margin: 0;
  color: var(--theme--foreground);
}

/* List View Styles */
.client-list {
  width: 100%;
  overflow-x: auto;
}

.client-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.client-table th {
  background-color: var(--theme--background-subdued);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
  border-bottom: 1px solid var(--theme--border-color);
  white-space: nowrap;
}

.client-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
  vertical-align: middle;
}

.client-table tr:last-child td {
  border-bottom: none;
}

.client-table tr:hover {
  background-color: var(--theme--background-subdued);
}

.row-inactive {
  background-color: rgba(var(--theme--danger-rgb), 0.05);
}

.list-name-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.list-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-avatar.small {
  width: 32px;
  height: 32px;
  font-size: 14px;
}

.list-contact {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.list-contact-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.list-contact-item i {
  font-size: 14px;
  color: var(--theme--primary);
}

.list-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.list-engagement {
  display: flex;
  align-items: center;
  gap: 8px;
}

.list-actions {
  display: flex;
  gap: 5px;
  justify-content: flex-end;
}

.col-name {
  min-width: 200px;
}

.col-company {
  min-width: 150px;
}

.col-contact {
  min-width: 200px;
}

.col-tags {
  min-width: 150px;
}

.col-activity {
  min-width: 100px;
  white-space: nowrap;
}

.col-engagement {
  min-width: 120px;
}

.col-status {
  min-width: 100px;
}

.col-actions {
  min-width: 120px;
}

/* Client Details Styles */
.modal-header-actions {
  display: flex;
  gap: 10px;
}

.client-details-header {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--theme--border-color);
}

.client-details-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--background-subdued);
}

.client-details-info {
  flex: 1;
  min-width: 200px;
}

.client-details-info h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
}

.client-meta-info {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.client-details-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
  width: 100%;
}

.metric-card {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  min-width: 120px;
  flex: 1;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.engagement-score-large {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}

.client-details-tabs {
  display: flex;
  border-bottom: 1px solid var(--theme--border-color);
  margin-bottom: 20px;
}

.client-details-tab {
  padding: 10px 20px;
  cursor: pointer;
  font-weight: 500;
  color: var(--theme--foreground-subdued);
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.client-details-tab:hover {
  color: var(--theme--foreground);
}

.client-details-tab.active {
  color: var(--theme--primary);
  border-bottom-color: var(--theme--primary);
}

.client-details-tab-content {
  margin-bottom: 20px;
}

.client-details-section {
  margin-bottom: 30px;
}

.client-details-section h4 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--theme--foreground);
}

.client-details-section h5 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme--foreground);
}

.section-header-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header-with-actions h4 {
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.client-notes {
  white-space: pre-line;
  line-height: 1.5;
}

.notes-editor {
  margin-bottom: 15px;
}

.notes-editor textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  margin-bottom: 10px;
  font-family: var(--theme--font-family-sans-serif);
}

.notes-editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.activity-actions {
  display: flex;
  gap: 5px;
  align-items: flex-start;
}

.view-all-link {
  text-align: center;
  margin-top: 10px;
}

.btn-text {
  background: none;
  border: none;
  color: var(--theme--primary);
  cursor: pointer;
  padding: 5px 10px;
  font-size: 14px;
}

.btn-text:hover {
  text-decoration: underline;
}

.orders-list {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.orders-header {
  display: flex;
  background-color: var(--theme--background-subdued);
  padding: 12px 16px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
}

.order-item {
  display: flex;
  padding: 12px 16px;
  border-top: 1px solid var(--theme--border-color);
  align-items: center;
}

.col-order-id {
  width: 120px;
}

.col-order-date {
  width: 120px;
}

.col-order-items {
  width: 100px;
}

.col-order-total {
  width: 100px;
}

.col-order-status {
  width: 120px;
}

.col-order-actions {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.order-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.order-status.completed {
  background-color: var(--theme--primary);
  color: white;
}

.order-status.pending {
  background-color: var(--theme--warning);
  color: white;
}

.order-status.cancelled {
  background-color: var(--theme--danger);
  color: white;
}

.preferences-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
}

.preference-group {
  margin-bottom: 20px;
}

.preference-item {
  margin-bottom: 10px;
}

.preference-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.modal-footer-actions {
  display: flex;
  gap: 10px;
}

/* Communication Tools Styles */
.communication-tools {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.communication-tool-card {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.communication-tool-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
}

.communication-tool-header i {
  font-size: 24px;
  color: var(--theme--primary);
}

.communication-tool-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.communication-tool-content {
  padding: 15px;
}

.file-upload {
  margin-bottom: 10px;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 10px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  font-size: 12px;
}

.attachment-item i {
  font-size: 16px;
  color: var(--theme--primary);
}

.attachment-item span {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.character-count {
  text-align: right;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-top: 5px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 15px;
}

.communication-history {
  margin-top: 30px;
}

.communication-tabs {
  display: flex;
  border-bottom: 1px solid var(--theme--border-color);
  margin-bottom: 15px;
}

.communication-tab {
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  border-bottom: 2px solid transparent;
}

.communication-tab:hover {
  color: var(--theme--foreground);
}

.communication-tab.active {
  color: var(--theme--primary);
  border-bottom-color: var(--theme--primary);
}

.communication-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.communication-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  border-left: 4px solid transparent;
}

.communication-item.email {
  border-left-color: var(--theme--primary);
}

.communication-item.sms {
  border-left-color: var(--theme--success);
}

.communication-item.call {
  border-left-color: var(--theme--warning);
}

.communication-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--theme--background);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground);
  flex-shrink: 0;
}

.communication-content {
  flex: 1;
}

.communication-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.communication-type {
  font-weight: 600;
  color: var(--theme--foreground);
}

.communication-date {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.communication-subject {
  font-weight: 500;
  margin-bottom: 5px;
}

.communication-message {
  white-space: pre-line;
  line-height: 1.5;
}

.communication-attachments {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 10px;
}

.communication-attachment {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 3px 6px;
  background-color: var(--theme--background);
  border-radius: 4px;
  font-size: 11px;
}

.communication-attachment i {
  font-size: 14px;
  color: var(--theme--primary);
}

.empty-state {
  padding: 20px;
  text-align: center;
  color: var(--theme--foreground-subdued);
  font-style: italic;
}

/* Analytics Styles */
.global-analytics-container {
  margin-top: 20px;
}

.analytics-overview {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.analytics-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.analytics-metric-card {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.analytics-metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--primary);
}

.analytics-metric-content {
  flex: 1;
}

.analytics-metric-value {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 5px;
}

.analytics-metric-label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.analytics-charts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.analytics-chart {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.analytics-chart h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.chart-placeholder {
  height: 200px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  gap: 10px;
  padding-bottom: 20px;
}

.chart-bar {
  flex: 1;
  background-color: var(--theme--primary);
  border-radius: 3px 3px 0 0;
  min-height: 4px;
}

.chart-line {
  flex: 1;
  padding-bottom: 20px;
}

.chart-line svg {
  width: 100%;
  height: 100%;
}

.chart-labels {
  height: 20px;
  display: flex;
  justify-content: space-between;
}

.chart-label {
  font-size: 10px;
  color: var(--theme--foreground-subdued);
  text-align: center;
  flex: 1;
}

.analytics-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.analytics-detail-section {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.analytics-detail-section h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.engagement-breakdown {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.engagement-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.engagement-item-label {
  width: 80px;
  font-size: 12px;
}

.engagement-item-bar {
  flex: 1;
  height: 6px;
  background-color: var(--theme--background);
  border-radius: 3px;
  overflow: hidden;
}

.engagement-item-progress {
  height: 100%;
  background-color: var(--theme--primary);
  border-radius: 3px;
}

.engagement-item-value {
  width: 40px;
  font-size: 12px;
  font-weight: 600;
  text-align: right;
}

.product-interests {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.product-interest-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-interest-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--primary);
}

.product-interest-icon i {
  font-size: 16px;
}

.product-interest-details {
  flex: 1;
}

.product-interest-name {
  font-size: 12px;
  margin-bottom: 5px;
}

.product-interest-bar {
  height: 6px;
  background-color: var(--theme--background);
  border-radius: 3px;
  overflow: hidden;
}

.product-interest-progress {
  height: 100%;
  background-color: var(--theme--primary);
  border-radius: 3px;
}

.product-interest-value {
  width: 40px;
  font-size: 12px;
  font-weight: 600;
  text-align: right;
}

.analytics-recommendations {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.analytics-recommendations h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
}

.recommendation-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  display: flex;
  gap: 15px;
  padding: 10px;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
}

.recommendation-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--primary);
  flex-shrink: 0;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.recommendation-description {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}
</style>
