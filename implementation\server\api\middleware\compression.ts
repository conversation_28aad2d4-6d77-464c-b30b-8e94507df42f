/**
 * Compression Middleware
 *
 * This middleware provides compression for API responses.
 * It supports gzip, deflate, and brotli compression algorithms.
 */

import { Request, Response, NextFunction } from 'express';
import compression from 'compression';
import { Logger } from '../../services/integration/logger';

// Create logger
const logger = new Logger();

// Define compression levels
export enum CompressionLevel {
  NONE = 0,
  LOW = 1,
  MEDIUM = 5,
  HIGH = 9,
}

// Define compression filter options
export interface CompressionFilterOptions {
  minSize?: number;
  contentTypes?: string[];
}

/**
 * Create a compression filter function
 *
 * @param options Compression filter options
 * @returns Compression filter function
 */
export const createCompressionFilter = (options: CompressionFilterOptions = {}) => {
  const minSize = options.minSize || 1024; // 1KB
  const contentTypes = options.contentTypes || [
    'text/plain',
    'text/html',
    'text/css',
    'text/javascript',
    'application/javascript',
    'application/json',
    'application/xml',
    'application/x-javascript',
    'application/xhtml+xml',
    'application/rss+xml',
    'application/atom+xml',
    'image/svg+xml',
  ];

  return (req: Request, res: Response): boolean => {
    // Skip compression for small responses
    if (
      res.getHeader('Content-Length') &&
      parseInt(res.getHeader('Content-Length') as string, 10) < minSize
    ) {
      return false;
    }

    // Skip compression for non-compressible content types
    const contentType = res.getHeader('Content-Type') as string;
    if (contentType && !contentTypes.some(type => contentType.includes(type))) {
      return false;
    }

    // Skip compression if client doesn't accept it
    const acceptEncoding = req.headers['accept-encoding'] as string;
    if (
      !acceptEncoding ||
      (!acceptEncoding.includes('gzip') &&
        !acceptEncoding.includes('deflate') &&
        !acceptEncoding.includes('br'))
    ) {
      return false;
    }

    // Apply compression
    return true;
  };
};

/**
 * Create compression middleware
 *
 * @param level Compression level
 * @param filterOptions Compression filter options
 * @returns Compression middleware
 */
export const createCompressionMiddleware = (
  level: CompressionLevel = CompressionLevel.MEDIUM,
  filterOptions?: CompressionFilterOptions,
) => {
  return compression({
    level,
    filter: createCompressionFilter(filterOptions),
    threshold: filterOptions?.minSize || 1024,
  });
};

/**
 * Default compression middleware
 */
export const compressionMiddleware = createCompressionMiddleware(CompressionLevel.MEDIUM);

/**
 * High compression middleware
 */
export const highCompressionMiddleware = createCompressionMiddleware(CompressionLevel.HIGH);

/**
 * Low compression middleware
 */
export const lowCompressionMiddleware = createCompressionMiddleware(CompressionLevel.LOW);

/**
 * Content negotiation middleware for compression
 *
 * This middleware selects the appropriate compression level based on the client's preferences.
 *
 * @param req Request
 * @param res Response
 * @param next Next function
 */
export const contentNegotiationMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  // Get the client's preferences
  const acceptEncoding = req.headers['accept-encoding'] as string;

  // Select the appropriate compression level
  if (acceptEncoding) {
    if (acceptEncoding.includes('br')) {
      // Brotli is the most efficient, use high compression
      highCompressionMiddleware(req, res, next);
    } else if (acceptEncoding.includes('gzip')) {
      // Gzip is efficient, use medium compression
      compressionMiddleware(req, res, next);
    } else if (acceptEncoding.includes('deflate')) {
      // Deflate is less efficient, use low compression
      lowCompressionMiddleware(req, res, next);
    } else {
      // No supported compression algorithm, skip compression
      next();
    }
  } else {
    // No compression preferences, skip compression
    next();
  }
};

export default {
  compressionMiddleware,
  highCompressionMiddleware,
  lowCompressionMiddleware,
  contentNegotiationMiddleware,
  createCompressionMiddleware,
  createCompressionFilter,
  CompressionLevel,
};
