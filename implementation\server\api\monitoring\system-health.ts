/**
 * System Health Monitoring API
 *
 * This module provides endpoints for monitoring system health metrics.
 */

import { Router, Request, Response } from 'express';
import { rateLimiter } from '../../middleware/rate-limiter';
import { logger } from '../../lib/logger';
import os from 'os';
import { supabase } from '../../lib/supabase';
import axios from 'axios';
import { promisify } from 'util';
import fs from 'fs';

const router = Router();

// Promisify fs.stat
const stat = promisify(fs.stat);

/**
 * Get system health metrics
 *
 * @param req - Request
 * @param res - Response
 */
export const getSystemHealth = async (_req: Request, res: Response): Promise<void> => {
  try {
    // Collect system metrics
    const systemMetrics = await collectSystemMetrics();

    // Collect service status
    const serviceStatus = await collectServiceStatus();

    // Collect alert summary
    const alertSummary = collectAlertSummary();

    res.status(200).json({
      success: true,
      data: {
        timestamp: new Date().toISOString(),
        system: systemMetrics,
        services: serviceStatus,
        alerts: alertSummary,
      },
    });
  } catch (error) {
    logger.error('Error getting system health:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting system health metrics',
      },
    });
  }
};

/**
 * Collect system metrics
 *
 * @returns System metrics
 */
async function collectSystemMetrics(): Promise<Record<string, unknown>> {
  // Calculate CPU usage
  const cpuUsage = (os.loadavg()[0] / os.cpus().length) * 100;

  // Calculate memory usage
  const totalMemory = os.totalmem();
  const freeMemory = os.freemem();
  const usedMemory = totalMemory - freeMemory;
  const memoryUsage = (usedMemory / totalMemory) * 100;

  // Calculate disk usage (for the current directory)
  let diskUsage = 0;
  try {
    const stats = await stat('.');
    // In production, you'd use a more robust method
    diskUsage = stats.size / (1024 * 1024 * 1024); // Convert to GB
  } catch (error) {
    logger.error('Error getting disk usage:', error);
  }

  // Get uptime
  const uptime = os.uptime();

  // Get network interfaces (excluding internal ones)
  const networkInterfaces = Object.entries(os.networkInterfaces())
    .filter(([name]) => !name.includes('lo') && !name.includes('internal'))
    .reduce(
      (acc, [name, interfaces]) => {
        acc[name] = interfaces;
        return acc;
      },
      {} as Record<string, os.NetworkInterfaceInfo[] | undefined>,
    );

  return {
    cpu: {
      usage: cpuUsage.toFixed(2),
      cores: os.cpus().length,
      model: os.cpus()[0].model,
    },
    memory: {
      total: formatBytes(totalMemory),
      used: formatBytes(usedMemory),
      free: formatBytes(freeMemory),
      usage: memoryUsage.toFixed(2),
    },
    disk: {
      usage: `${diskUsage.toFixed(2)} GB`,
    },
    os: {
      type: os.type(),
      platform: os.platform(),
      release: os.release(),
      hostname: os.hostname(),
    },
    uptime: formatUptime(uptime),
    network: {
      interfaces: networkInterfaces,
    },
  };
}

/**
 * Collect service status
 *
 * @returns Service status
 */
async function collectServiceStatus(): Promise<Record<string, unknown>> {
  // Check API Gateway status
  let apiGatewayStatus = 'unknown';
  try {
    const response = await axios.get(
      `${process.env.API_GATEWAY_URL || 'http://localhost:8080'}/health`,
      {
        timeout: 5000,
      },
    );
    apiGatewayStatus = response.status === 200 ? 'healthy' : 'degraded';
  } catch (error) {
    apiGatewayStatus = 'unhealthy';
    logger.error('Error checking API Gateway status:', error);
  }

  // Check Authentication service status
  let authServiceStatus = 'unknown';
  try {
    const { error } = await supabase.auth.getSession();
    authServiceStatus = error ? 'degraded' : 'healthy';
  } catch (error) {
    authServiceStatus = 'unhealthy';
    logger.error('Error checking Authentication service status:', error);
  }

  // Check Database service status
  let dbServiceStatus = 'unknown';
  try {
    const { error } = await supabase.from('health_check').select('id').limit(1);
    dbServiceStatus = error ? 'degraded' : 'healthy';
  } catch (error) {
    dbServiceStatus = 'unhealthy';
    logger.error('Error checking Database service status:', error);
  }

  // Check Storage service status
  let storageServiceStatus = 'unknown';
  try {
    const { error } = await supabase.storage.getBucket('health_check');
    storageServiceStatus = error ? 'degraded' : 'healthy';
  } catch (error) {
    storageServiceStatus = 'unhealthy';
    logger.error('Error checking Storage service status:', error);
  }

  return {
    api_gateway: {
      status: apiGatewayStatus,
      last_checked: new Date().toISOString(),
    },
    auth_service: {
      status: authServiceStatus,
      last_checked: new Date().toISOString(),
    },
    database_service: {
      status: dbServiceStatus,
      last_checked: new Date().toISOString(),
    },
    storage_service: {
      status: storageServiceStatus,
      last_checked: new Date().toISOString(),
    },
  };
}

/**
 * Collect alert summary
 *
 * @returns Alert summary
 */
function collectAlertSummary(): Record<string, unknown> {
  // In a real implementation, this would query an alert database
  // For now, we'll return mock data
  return {
    critical: 0,
    warning: 2,
    info: 5,
    recent_alerts: [
      {
        id: 'alert-001',
        severity: 'warning',
        message: 'High CPU usage detected',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        status: 'active',
      },
      {
        id: 'alert-002',
        severity: 'warning',
        message: 'API rate limit approaching threshold',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        status: 'active',
      },
      {
        id: 'alert-003',
        severity: 'info',
        message: 'New vendor registration pending approval',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        status: 'active',
      },
    ],
  };
}

/**
 * Format bytes to human-readable format
 *
 * @param bytes - Bytes to format
 * @returns Formatted string
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format uptime to human-readable format
 *
 * @param seconds - Uptime in seconds
 * @returns Formatted string
 */
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / (3600 * 24));
  const hours = Math.floor((seconds % (3600 * 24)) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  return `${days}d ${hours}h ${minutes}m ${remainingSeconds}s`;
}

// Register routes
router.get('/', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getSystemHealth);

export default router;
