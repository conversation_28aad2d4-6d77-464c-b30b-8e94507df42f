/**
 * API Integration Endpoints for Directus
 *
 * This extension provides custom endpoints for integrating Directus with the frontend and other services.
 * It includes endpoints for showroom layouts, product displays, and vendor branding.
 */

const { createClient } = require('@supabase/supabase-js');
const Joi = require('joi');

module.exports = function registerEndpoint(
  router,
  { services, exceptions, database, getSchema, logger, env },
) {
  const { ItemsService } = services;
  const { ServiceUnavailableException, ForbiddenException, InvalidPayloadException } = exceptions;

  // Initialize Supabase client
  const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    logger.warn(
      'Supabase credentials not set. API integration endpoints may not function correctly.',
    );
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  // Validation schemas
  const schemas = {
    showroomLayout: Joi.object({
      name: Joi.string().required(),
      description: Joi.string().allow('', null),
      layout_json: Joi.object().required(),
      vendor_id: Joi.string().required(),
      status: Joi.string().valid('published', 'draft', 'archived').default('draft'),
    }),
    productDisplay: Joi.object({
      name: Joi.string().required(),
      description: Joi.string().allow('', null),
      product_id: Joi.string().required(),
      vendor_id: Joi.string().required(),
      display_type: Joi.string()
        .valid('standard', 'featured', 'interactive', 'animated', 'custom')
        .default('standard'),
      display_json: Joi.object().required(),
      status: Joi.string().valid('published', 'draft', 'archived').default('draft'),
    }),
    vendorBranding: Joi.object({
      vendor_id: Joi.string().required(),
      vendor_name: Joi.string().required(),
      primary_color: Joi.string()
        .regex(/^#[0-9A-F]{6}$/i)
        .default('#000000'),
      secondary_color: Joi.string()
        .regex(/^#[0-9A-F]{6}$/i)
        .default('#FFFFFF'),
      accent_color: Joi.string()
        .regex(/^#[0-9A-F]{6}$/i)
        .allow('', null),
      font_family: Joi.string().default('Arial, sans-serif'),
      branding_json: Joi.object().allow(null),
      status: Joi.string().valid('published', 'draft', 'archived').default('draft'),
    }),
  };

  // Helper function to validate request payload
  function validatePayload(schema, payload) {
    const { error, value } = schema.validate(payload);
    if (error) {
      throw new InvalidPayloadException(error.message);
    }
    return value;
  }

  // Helper function to check user permissions
  async function checkPermissions(req, vendorId) {
    const user = req.accountability?.user;

    if (!user) {
      throw new ForbiddenException('Authentication required');
    }

    // Check if user is an admin
    if (req.accountability?.admin) {
      return true;
    }

    try {
      // Get user from Supabase
      const { data: userData, error } = await supabase
        .from('users')
        .select('id, role, vendor_id')
        .eq('id', user)
        .single();

      if (error) throw error;

      // Check if user belongs to the vendor
      if (
        userData.vendor_id === vendorId ||
        userData.role === 'super_admin' ||
        userData.role === 'admin'
      ) {
        return true;
      }

      throw new ForbiddenException('You do not have permission to access this resource');
    } catch (error) {
      logger.error(`Permission check error: ${error.message}`);
      throw new ForbiddenException('Permission check failed');
    }
  }

  // Endpoint: Get showroom layouts for a vendor
  router.get('/showroom-layouts/:vendorId', async (req, res, next) => {
    try {
      const vendorId = req.params.vendorId;

      // Check permissions
      await checkPermissions(req, vendorId);

      // Get showroom layouts
      const showroomLayoutsService = new ItemsService('showroom_layouts', {
        schema: await getSchema(),
        accountability: req.accountability,
      });

      const layouts = await showroomLayoutsService.readByQuery({
        filter: { vendor_id: { _eq: vendorId } },
        sort: ['-date_created'],
      });

      res.json({ data: layouts });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Create a new showroom layout
  router.post('/showroom-layouts', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.showroomLayout, req.body);

      // Check permissions
      await checkPermissions(req, payload.vendor_id);

      // Create showroom layout
      const showroomLayoutsService = new ItemsService('showroom_layouts', {
        schema: await getSchema(),
        accountability: req.accountability,
      });

      const layout = await showroomLayoutsService.createOne(payload);

      res.json({ data: layout });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Get product displays for a vendor
  router.get('/product-displays/:vendorId', async (req, res, next) => {
    try {
      const vendorId = req.params.vendorId;

      // Check permissions
      await checkPermissions(req, vendorId);

      // Get product displays
      const productDisplaysService = new ItemsService('product_displays', {
        schema: await getSchema(),
        accountability: req.accountability,
      });

      const displays = await productDisplaysService.readByQuery({
        filter: { vendor_id: { _eq: vendorId } },
        sort: ['-date_created'],
      });

      res.json({ data: displays });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Create a new product display
  router.post('/product-displays', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.productDisplay, req.body);

      // Check permissions
      await checkPermissions(req, payload.vendor_id);

      // Create product display
      const productDisplaysService = new ItemsService('product_displays', {
        schema: await getSchema(),
        accountability: req.accountability,
      });

      const display = await productDisplaysService.createOne(payload);

      res.json({ data: display });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Get vendor branding
  router.get('/vendor-branding/:vendorId', async (req, res, next) => {
    try {
      const vendorId = req.params.vendorId;

      // Check permissions
      await checkPermissions(req, vendorId);

      // Get vendor branding
      const vendorBrandingService = new ItemsService('vendor_branding', {
        schema: await getSchema(),
        accountability: req.accountability,
      });

      const branding = await vendorBrandingService.readByQuery({
        filter: { vendor_id: { _eq: vendorId } },
        limit: 1,
      });

      res.json({ data: branding[0] || null });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Create or update vendor branding
  router.post('/vendor-branding', async (req, res, next) => {
    try {
      const payload = validatePayload(schemas.vendorBranding, req.body);

      // Check permissions
      await checkPermissions(req, payload.vendor_id);

      // Get vendor branding service
      const vendorBrandingService = new ItemsService('vendor_branding', {
        schema: await getSchema(),
        accountability: req.accountability,
      });

      // Check if branding already exists
      const existingBranding = await vendorBrandingService.readByQuery({
        filter: { vendor_id: { _eq: payload.vendor_id } },
        limit: 1,
      });

      let branding;

      if (existingBranding.length > 0) {
        // Update existing branding
        branding = await vendorBrandingService.updateOne(existingBranding[0].id, payload);
      } else {
        // Create new branding
        branding = await vendorBrandingService.createOne(payload);
      }

      res.json({ data: branding });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Get all published showroom layouts
  router.get('/public/showroom-layouts', async (req, res, next) => {
    try {
      // Get showroom layouts
      const showroomLayoutsService = new ItemsService('showroom_layouts', {
        schema: await getSchema(),
        accountability: req.accountability,
      });

      const layouts = await showroomLayoutsService.readByQuery({
        filter: { status: { _eq: 'published' } },
        sort: ['-date_created'],
      });

      res.json({ data: layouts });
    } catch (error) {
      next(error);
    }
  });

  // Endpoint: Get all published product displays
  router.get('/public/product-displays', async (req, res, next) => {
    try {
      // Get product displays
      const productDisplaysService = new ItemsService('product_displays', {
        schema: await getSchema(),
        accountability: req.accountability,
      });

      const displays = await productDisplaysService.readByQuery({
        filter: { status: { _eq: 'published' } },
        sort: ['-date_created'],
      });

      res.json({ data: displays });
    } catch (error) {
      next(error);
    }
  });

  return router;
};
