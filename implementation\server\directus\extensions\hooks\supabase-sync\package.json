{"name": "directus-extension-supabase-sync", "version": "1.0.0", "description": "Synchronize data between Directus and Supabase", "main": "index.js", "keywords": ["directus", "directus-extension", "directus-custom-hook", "supabase"], "directus:extension": {"type": "hook", "path": "index.js", "source": "src/index.js", "host": "^10.0.0"}, "scripts": {"build": "directus-extension build"}, "dependencies": {"@supabase/supabase-js": "^2.38.0"}}