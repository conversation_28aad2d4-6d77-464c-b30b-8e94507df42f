import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { BlueprintInjectorService } from '../../services/blueprint/blueprint-injector';
import { validateRequest } from '../middleware/validation';

// Initialize blueprint injector service
const blueprintInjector = new BlueprintInjectorService(supabase);

/**
 * Inject a blueprint into a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const injectBlueprint = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id, blueprint_id } = req.params;
    const { position, rotation, scale, properties } = req.body;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!blueprint_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_BLUEPRINT_ID',
          message: 'Blueprint ID is required',
        },
      });
      return;
    }

    if (
      !position ||
      typeof position.x !== 'number' ||
      typeof position.y !== 'number' ||
      typeof position.z !== 'number'
    ) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_POSITION',
          message: 'Position must be an object with x, y, and z number properties',
        },
      });
      return;
    }

    // Inject blueprint
    const scene = await blueprintInjector.injectBlueprint(
      scene_id,
      blueprint_id,
      position,
      rotation,
      scale,
      properties,
    );

    if (!scene) {
      res.status(500).json({
        success: false,
        error: {
          code: 'INJECTION_FAILED',
          message: 'Failed to inject blueprint',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: scene,
    });
  } catch (error) {
    logger.error('Error injecting blueprint', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Update a blueprint instance
 *
 * @param req - Request
 * @param res - Response
 */
export const updateBlueprintInstance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id, instance_id } = req.params;
    const { position, rotation, scale, properties } = req.body;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!instance_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INSTANCE_ID',
          message: 'Instance ID is required',
        },
      });
      return;
    }

    // Update blueprint instance
    const scene = await blueprintInjector.updateBlueprintInstance(
      scene_id,
      instance_id,
      position,
      rotation,
      scale,
      properties,
    );

    if (!scene) {
      res.status(404).json({
        success: false,
        error: {
          code: 'UPDATE_FAILED',
          message: 'Failed to update blueprint instance',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: scene,
    });
  } catch (error) {
    logger.error('Error updating blueprint instance', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Remove a blueprint instance
 *
 * @param req - Request
 * @param res - Response
 */
export const removeBlueprintInstance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id, instance_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!instance_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INSTANCE_ID',
          message: 'Instance ID is required',
        },
      });
      return;
    }

    // Remove blueprint instance
    const scene = await blueprintInjector.removeBlueprintInstance(scene_id, instance_id);

    if (!scene) {
      res.status(404).json({
        success: false,
        error: {
          code: 'REMOVAL_FAILED',
          message: 'Failed to remove blueprint instance',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: scene,
    });
  } catch (error) {
    logger.error('Error removing blueprint instance', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get blueprint instances in a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const getBlueprintInstances = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get blueprint instances
    const instances = await blueprintInjector.getBlueprintInstances(scene_id);

    if (!instances) {
      res.status(404).json({
        success: false,
        error: {
          code: 'RETRIEVAL_FAILED',
          message: 'Failed to retrieve blueprint instances',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: instances,
    });
  } catch (error) {
    logger.error('Error getting blueprint instances', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get a blueprint instance
 *
 * @param req - Request
 * @param res - Response
 */
export const getBlueprintInstance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id, instance_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!instance_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INSTANCE_ID',
          message: 'Instance ID is required',
        },
      });
      return;
    }

    // Get blueprint instance
    const instance = await blueprintInjector.getBlueprintInstance(scene_id, instance_id);

    if (!instance) {
      res.status(404).json({
        success: false,
        error: {
          code: 'INSTANCE_NOT_FOUND',
          message: 'Blueprint instance not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: instance,
    });
  } catch (error) {
    logger.error('Error getting blueprint instance', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
