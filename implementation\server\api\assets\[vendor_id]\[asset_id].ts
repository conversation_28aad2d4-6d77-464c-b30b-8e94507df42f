import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../shared/utils/logger';
import { errorHandler } from '../../../shared/utils/error-handler';

// Define the response schema
const AssetResponseSchema = z.object({
  id: z.string().uuid(),
  vendor_id: z.string().uuid(),
  type: z.string(),
  name: z.string(),
  description: z.string().nullable(),
  url: z.string().url(),
  thumbnail_url: z.string().url().nullable(),
  hash: z.string(),
  size: z.number().int().positive(),
  version: z.string(),
  metadata: z.record(z.any()),
});

// Define the query parameters schema
const QueryParamsSchema = z.object({
  vendor_id: z.string().uuid(),
  asset_id: z.string().uuid(),
});

/**
 * Asset API endpoint
 *
 * This endpoint returns information about a specific asset.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse(req.query);
    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { vendor_id, asset_id } = queryResult.data;

    // Log the request
    logger.info('Asset request', {
      vendor_id,
      asset_id,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get vendor
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('id, name, api_key')
      .eq('id', vendor_id)
      .single();

    if (vendorError) {
      logger.error('Error fetching vendor', { error: vendorError, vendor_id });
      return res.status(500).json({ error: 'Error fetching vendor' });
    }

    if (!vendor) {
      return res.status(404).json({ error: 'Vendor not found' });
    }

    // Get asset
    const { data: asset, error: assetError } = await supabase
      .from('assets')
      .select('*')
      .eq('id', asset_id)
      .eq('vendor_id', vendor_id)
      .single();

    if (assetError) {
      logger.error('Error fetching asset', { error: assetError, vendor_id, asset_id });
      return res.status(500).json({ error: 'Error fetching asset' });
    }

    if (!asset) {
      return res.status(404).json({ error: 'Asset not found' });
    }

    // Validate response
    const responseResult = AssetResponseSchema.safeParse(asset);
    if (!responseResult.success) {
      logger.error('Invalid response format', { error: responseResult.error.format(), asset });
      return res.status(500).json({ error: 'Invalid response format' });
    }

    // Log success
    logger.info('Asset response', {
      vendor_id,
      asset_id,
      asset_type: asset.type,
    });

    // Return response
    return res.status(200).json(asset);
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
