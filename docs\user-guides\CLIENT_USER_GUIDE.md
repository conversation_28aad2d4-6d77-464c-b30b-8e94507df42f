# MVS-VR Client User Guide

This guide provides comprehensive instructions for clients using the MVS-VR platform. It covers the client portal overview, showroom browsing, product interaction, feedback submission, and account management.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Client Portal Overview](#client-portal-overview)
3. [Showroom Browsing](#showroom-browsing)
4. [Product Interaction](#product-interaction)
5. [Feedback Submission](#feedback-submission)
6. [Account Management](#account-management)
7. [Mobile Access](#mobile-access)
8. [VR Mode](#vr-mode)
9. [Troubleshooting](#troubleshooting)

## Getting Started

### Accessing the Client Portal

1. Navigate to `https://your-domain.com/client` in your web browser
2. Enter your client credentials (email and password)
3. If enabled, complete the two-factor authentication process
4. You will be redirected to the client dashboard

### First-Time Setup

If you're accessing the client portal for the first time, you'll need to complete the following steps:

1. Change your temporary password
2. Set up two-factor authentication (optional)
3. Complete your profile
4. Set your preferences
5. Take the guided tour (recommended)

### System Requirements

To use the MVS-VR platform, your system should meet the following requirements:

#### For Web Access:
- **Browser**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Operating System**: Windows 10+, macOS 10.14+, iOS 13+, Android 9+
- **Internet Connection**: 5 Mbps or faster
- **Graphics**: WebGL 2.0 compatible graphics card
- **RAM**: 4 GB or more

#### For VR Access:
- **VR Headset**: Oculus Quest 2+, HTC Vive, Valve Index, or other SteamVR compatible headset
- **Operating System**: Windows 10+
- **Processor**: Intel i5-7500 / AMD Ryzen 5 1500X or better
- **Graphics**: NVIDIA GTX 1060 / AMD Radeon RX 580 or better
- **RAM**: 8 GB or more
- **Internet Connection**: 10 Mbps or faster

### Navigation

The client portal has a sidebar navigation menu with the following sections:

- **Dashboard**: Overview of available showrooms and recent activity
- **Showrooms**: List of showrooms you have access to
- **Favorites**: Showrooms and products you've marked as favorites
- **Feedback**: Your feedback and comments
- **Account**: Your account settings and preferences
- **Help**: Help and support resources

## Client Portal Overview

The client dashboard provides a comprehensive overview of the showrooms you have access to and your recent activity.

### Dashboard Widgets

The dashboard consists of customizable widgets that display various information:

- **Showroom Overview**: Shows the showrooms you have access to
- **Recent Activity**: Displays your recent activity
- **Favorites**: Shows your favorite showrooms and products
- **Notifications**: Displays notifications from vendors
- **Upcoming Events**: Shows upcoming events and presentations

### Customizing the Dashboard

You can customize the dashboard to show the information most relevant to you:

1. Click the **Customize** button in the top-right corner of the dashboard
2. Drag and drop widgets to rearrange them
3. Click the **Add Widget** button to add new widgets
4. Click the gear icon on a widget to configure it or remove it
5. Click **Save** to save your dashboard layout

## Showroom Browsing

The showroom browsing section allows you to browse and explore virtual showrooms.

### Showroom List

The showroom list displays all the showrooms you have access to with the following information:

- **Name**: Showroom name
- **Vendor**: Vendor who created the showroom
- **Type**: Showroom type (Furniture, Appliances, etc.)
- **Last Visited**: Date and time you last visited the showroom
- **Actions**: Actions you can perform on the showroom

### Entering a Showroom

To enter a showroom:

1. Click on the showroom card or the "Enter" button
2. The showroom will load in your browser
3. If this is your first time entering the showroom, you'll see a brief tutorial

### Navigating Within a Showroom

Once inside a showroom, you can navigate using the following controls:

#### Desktop Controls:
- **Mouse Movement**: Look around
- **W/A/S/D Keys**: Move forward, left, backward, right
- **Spacebar**: Jump
- **Shift**: Sprint
- **E Key**: Interact with objects
- **F Key**: Toggle flashlight (if available)
- **Tab Key**: Open menu
- **Esc Key**: Exit menu or showroom

#### Mobile Controls:
- **Touch and Drag**: Look around
- **Virtual Joystick**: Move around
- **Tap**: Interact with objects
- **Menu Button**: Open menu

#### VR Controls:
- **Head Movement**: Look around
- **Controller Joysticks**: Move around
- **Trigger Buttons**: Interact with objects
- **Menu Button**: Open menu

### Showroom Features

Showrooms may include the following features:

- **Products**: Interactive 3D products you can examine
- **Information Points**: Points with additional information
- **Videos**: Video presentations about products
- **Configurators**: Tools to customize products
- **AI Assistant**: Virtual assistant to answer questions
- **Chat**: Live chat with vendor representatives
- **Annotations**: Notes and comments from you or the vendor

### Saving Favorites

To save a showroom or product as a favorite:

1. Click the heart icon on the showroom card or product
2. The item will be added to your favorites
3. You can access your favorites from the Favorites section in the navigation menu

### Sharing Showrooms

To share a showroom with colleagues:

1. Click the share icon on the showroom card or within the showroom
2. Enter the email addresses of the people you want to share with
3. Add an optional message
4. Click **Share** to send the invitation

## Product Interaction

The MVS-VR platform allows you to interact with products in various ways.

### Viewing Products

To view a product:

1. Navigate to the product within the showroom
2. Click or tap on the product to select it
3. The product will be highlighted, and information about it will be displayed

### Examining Products

To examine a product in detail:

1. Select the product
2. Click the "Examine" button or use the examine gesture
3. The product will be isolated for detailed examination
4. Use the following controls to examine the product:
   - **Click and Drag**: Rotate the product
   - **Scroll**: Zoom in and out
   - **Right-Click and Drag**: Pan the view
   - **Double-Click**: Reset the view

### Product Information

When examining a product, you can view the following information:

- **Name**: Product name
- **Description**: Product description
- **Specifications**: Technical specifications
- **Materials**: Available materials and finishes
- **Dimensions**: Product dimensions
- **Price**: Product price (if available)
- **Availability**: Product availability
- **Documents**: Related documents (datasheets, manuals, etc.)

### Product Customization

Some products can be customized:

1. Select the product
2. Click the "Customize" button
3. Use the customization interface to:
   - Change colors and materials
   - Adjust dimensions
   - Add or remove features
   - Configure options
4. Click "Apply" to see the changes in the showroom
5. Click "Save" to save your customization

### Product Placement

To place a product in the showroom:

1. Select the product
2. Click the "Place" button
3. Drag the product to the desired location
4. Use the rotation and elevation controls to adjust the position
5. Click "Confirm" to place the product

### Creating a Product List

To create a list of products you're interested in:

1. Select a product
2. Click the "Add to List" button
3. Select an existing list or create a new one
4. The product will be added to the list
5. You can access your lists from the Account section

## Feedback Submission

The MVS-VR platform allows you to provide feedback on showrooms and products.

### Providing Showroom Feedback

To provide feedback on a showroom:

1. Click the feedback button in the showroom
2. Rate the showroom on various aspects (design, usability, etc.)
3. Add comments in the text field
4. Click **Submit** to send your feedback

### Providing Product Feedback

To provide feedback on a product:

1. Select the product
2. Click the feedback button
3. Rate the product on various aspects (design, functionality, etc.)
4. Add comments in the text field
5. Click **Submit** to send your feedback

### Adding Annotations

To add annotations to specific points in the showroom:

1. Click the annotation button
2. Click on the point where you want to add the annotation
3. Enter your comment
4. Select the visibility (private, vendor only, public)
5. Click **Save** to add the annotation

### Viewing and Managing Feedback

To view and manage your feedback:

1. Go to the Feedback section in the navigation menu
2. View a list of all your feedback
3. Click on a feedback item to view details
4. Edit or delete your feedback if needed

## Account Management

The account management section allows you to manage your MVS-VR account.

### Profile Settings

To update your profile:

1. Go to the Account section in the navigation menu
2. Click the **Profile** tab
3. Update your information:
   - **Name**: Your full name
   - **Email**: Your email address
   - **Phone**: Your phone number
   - **Company**: Your company name
   - **Job Title**: Your job title
   - **Profile Picture**: Your profile picture
4. Click **Save** to save your changes

### Security Settings

To update your security settings:

1. Go to the Account section in the navigation menu
2. Click the **Security** tab
3. Update your security settings:
   - **Password**: Change your password
   - **Two-Factor Authentication**: Enable or disable two-factor authentication
   - **Login History**: View your login history
   - **Connected Devices**: Manage devices connected to your account
4. Click **Save** to save your changes

### Notification Settings

To update your notification settings:

1. Go to the Account section in the navigation menu
2. Click the **Notifications** tab
3. Update your notification settings:
   - **Email Notifications**: Enable or disable email notifications
   - **In-App Notifications**: Enable or disable in-app notifications
   - **Notification Types**: Select which types of notifications you want to receive
4. Click **Save** to save your changes

### Preferences

To update your preferences:

1. Go to the Account section in the navigation menu
2. Click the **Preferences** tab
3. Update your preferences:
   - **Language**: Select your preferred language
   - **Theme**: Select your preferred theme (Light, Dark, System)
   - **Units**: Select your preferred units (Metric, Imperial)
   - **Currency**: Select your preferred currency
   - **Time Zone**: Select your preferred time zone
4. Click **Save** to save your changes

## Mobile Access

The MVS-VR platform can be accessed on mobile devices through the web browser or the MVS-VR mobile app.

### Mobile Web Access

To access MVS-VR on a mobile device through the web browser:

1. Open your mobile browser
2. Navigate to `https://your-domain.com/client`
3. Log in with your credentials
4. The mobile-optimized interface will be displayed

### Mobile App

To access MVS-VR using the mobile app:

1. Download the MVS-VR app from the App Store (iOS) or Google Play Store (Android)
2. Open the app
3. Log in with your credentials
4. The app interface will be displayed

### Mobile Features

The mobile version of MVS-VR includes the following features:

- **Showroom Browsing**: Browse and explore showrooms
- **Product Interaction**: View and interact with products
- **Feedback Submission**: Provide feedback on showrooms and products
- **Account Management**: Manage your account settings
- **Offline Access**: Access previously visited showrooms offline
- **AR Mode**: View products in augmented reality (if supported by your device)

### AR Mode

To use AR mode on a mobile device:

1. Select a product
2. Click the "View in AR" button
3. Point your camera at a flat surface
4. Follow the on-screen instructions to place the product
5. Use the controls to move, rotate, and scale the product
6. Take a screenshot or record a video if desired

## VR Mode

The MVS-VR platform supports virtual reality (VR) mode for a more immersive experience.

### VR Requirements

To use VR mode, you need:

- A compatible VR headset (Oculus Quest 2+, HTC Vive, Valve Index, etc.)
- A VR-ready computer (for PC VR headsets)
- The MVS-VR VR app (for standalone headsets) or a VR-compatible browser

### Accessing VR Mode

#### For PC VR Headsets:
1. Connect your VR headset to your computer
2. Open a VR-compatible browser (Chrome, Firefox, Edge)
3. Navigate to `https://your-domain.com/client`
4. Log in with your credentials
5. Click the "Enter VR" button in a showroom

#### For Standalone VR Headsets:
1. Download the MVS-VR VR app from the headset's app store
2. Open the app
3. Log in with your credentials
4. Select a showroom to enter

### VR Controls

In VR mode, you can use the following controls:

- **Head Movement**: Look around
- **Controller Joysticks**: Move around
- **Teleportation**: Point and click to teleport
- **Grab**: Grab and manipulate objects
- **Trigger Buttons**: Interact with objects
- **Menu Button**: Open menu
- **Gesture Controls**: Use hand gestures for certain actions (if supported)

### VR Features

VR mode includes the following features:

- **Immersive Environment**: Fully immersive 3D environment
- **Natural Interaction**: Interact with products using natural hand movements
- **Spatial Audio**: 3D audio for a more immersive experience
- **Multiplayer**: Explore showrooms with colleagues or vendor representatives
- **Virtual Presentations**: Attend virtual presentations and demonstrations
- **VR Annotations**: Add annotations in VR

## Troubleshooting

### Common Issues

1. **Login Issues**
   - Check that your email and password are correct
   - Clear your browser cache and cookies
   - Try using a different browser
   - Contact your vendor if you continue to have issues

2. **Performance Issues**
   - Check that your device meets the system requirements
   - Close other applications to free up resources
   - Reduce the quality settings in the showroom
   - Try using a wired internet connection instead of Wi-Fi

3. **VR Issues**
   - Check that your VR headset is properly connected
   - Update your VR headset firmware
   - Update your graphics card drivers
   - Try restarting your computer and VR headset

4. **Mobile Issues**
   - Check that your device meets the system requirements
   - Clear your browser cache and cookies
   - Try using the mobile app instead of the web browser
   - Check your internet connection

### Getting Help

If you encounter issues not covered in this guide, you can:

- Click the **Help** button in the showroom
- Use the AI assistant to ask for help
- Contact your vendor for support
- Visit the [MVS-VR Support Portal](https://support.mvs-vr.com)
- Contact <NAME_EMAIL>
