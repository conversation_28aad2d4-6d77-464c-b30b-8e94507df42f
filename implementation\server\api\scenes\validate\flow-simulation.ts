import { Request, Response } from 'express';
import { logger } from '../../../shared/utils/logger';
import { SceneFlowSimulatorService } from '../../../services/scene/scene-flow-simulator';

// Initialize scene flow simulator service
const sceneFlowSimulator = new SceneFlowSimulatorService();

/**
 * Simulate scene flow
 *
 * @param req - Request
 * @param res - Response
 */
export const simulateSceneFlow = async (req: Request, res: Response): Promise<void> => {
  try {
    const { flow } = req.body;

    // Validate parameters
    if (!flow) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_FLOW',
          message: 'Scene flow is required',
        },
      });
      return;
    }

    // Simulate scene flow
    const result = sceneFlowSimulator.simulateFlow(flow);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error simulating scene flow', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
      return;
    }

    await simulateSceneFlow(req, res);
  } catch (error) {
    logger.error('Error in scene flow simulation handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
}
