# Scene Validator Service

## Overview

The Scene Validator Service is responsible for validating scenes, scene data, and scene flow configurations. It provides comprehensive validation capabilities to ensure that scenes are properly structured, optimized, and compatible with target environments.

## Features

### Scene Validation

The Scene Validator Service can validate a scene's structure, including:

- Basic scene properties (name, description, etc.)
- Scene data structure
- Blueprint instances and references
- Asset references

### Scene Flow Validation

The service can validate scene flow configurations, including:

- Flow structure validation using Zod schema
- Circular reference detection
- Missing space reference detection
- Dangling node detection

### Performance Analysis

The service can analyze a scene's performance impact, including:

- Asset count and size
- Scene complexity
- Estimated load time
- Estimated memory usage
- Recommendations for optimization

### Compatibility Checking

The service can check a scene's compatibility with different target environments, including:

- Quest 2
- Quest 3
- Pico 4
- SteamVR

## API Endpoints

### Validate Scene

```
GET /api/scene/validate/:id
```

Validates a scene's structure and returns validation results.

**Parameters:**
- `id` (path): Scene ID

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "errors": [],
    "warnings": []
  }
}
```

### Analyze Scene Performance

```
GET /api/scene/validate/:id/performance
```

Analyzes a scene's performance impact and returns metrics and recommendations.

**Parameters:**
- `id` (path): Scene ID

**Response:**
```json
{
  "success": true,
  "data": {
    "impact": "low",
    "metrics": {
      "assetCount": 10,
      "totalAssetSize": 5000000,
      "complexityScore": 50,
      "estimatedLoadTime": 2.5,
      "estimatedMemoryUsage": 7500000
    },
    "recommendations": [
      {
        "code": "LARGE_TEXTURES",
        "message": "Scene contains 2 large textures (>5MB), consider reducing texture size",
        "priority": "high"
      }
    ]
  }
}
```

### Check Scene Compatibility

```
GET /api/scene/validate/:id/compatibility
```

Checks a scene's compatibility with a target environment.

**Parameters:**
- `id` (path): Scene ID
- `target_environment` (query): Target environment (e.g., 'quest2', 'quest3', 'pico4', 'steamvr')

**Response:**
```json
{
  "success": true,
  "data": {
    "compatible": true,
    "targetEnvironment": "quest2",
    "issues": [
      {
        "code": "TEXTURE_TOO_LARGE",
        "message": "Texture asset_123 resolution (4096x4096) exceeds maximum for quest2",
        "severity": "warning",
        "path": "assets.asset_123"
      }
    ]
  }
}
```

### Validate Scene Data

```
POST /api/scene/validate/data
```

Validates scene data structure.

**Request Body:**
```json
{
  "data": {
    "objects": [],
    "settings": {}
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "errors": []
  }
}
```

### Validate Scene Flow

```
POST /api/scene/validate/flow
```

Validates a scene flow configuration.

**Request Body:**
```json
{
  "flow": {
    "startup": {
      "space": "space-1",
      "next": "node-2"
    },
    "node-2": {
      "space": "space-2",
      "options": {
        "option1": "node-3",
        "option2": "node-4"
      }
    },
    "node-3": {
      "space": "space-3"
    },
    "node-4": {
      "space": "space-4"
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "errors": [],
    "warnings": []
  }
}
```

## Error Codes

### Scene Validation Error Codes

- `SCENE_NOT_FOUND`: Scene not found
- `MISSING_NAME`: Scene name is required
- `MISSING_DATA`: Scene data is required
- `INVALID_DATA`: Scene data must be an object
- `MISSING_OBJECTS`: Scene data must have objects
- `INVALID_OBJECTS`: Scene objects must be an array
- `MISSING_SETTINGS`: Scene data should have settings
- `INVALID_SETTINGS`: Scene settings must be an object
- `MISSING_INSTANCE_ID`: Blueprint instance is missing id
- `MISSING_BLUEPRINT_ID`: Blueprint instance is missing blueprint_id
- `INVALID_BLUEPRINT_ID`: Blueprint with id does not exist
- `MISSING_POSITION`: Blueprint instance is missing position
- `INVALID_POSITION_X`: Blueprint instance has invalid position.x
- `INVALID_POSITION_Y`: Blueprint instance has invalid position.y
- `INVALID_POSITION_Z`: Blueprint instance has invalid position.z
- `MISSING_PROPERTIES`: Blueprint instance is missing properties

### Scene Flow Validation Error Codes

- `INVALID_FLOW_STRUCTURE`: Invalid flow structure
- `CIRCULAR_REFERENCE`: Circular reference detected
- `INVALID_SPACE_REFERENCE`: Space with ID does not exist
- `NO_STARTUP_NODE`: No startup node found in flow
- `DANGLING_NODE`: Node is not reachable from the startup node

### Performance Analysis Codes

- `TOO_MANY_ASSETS`: Scene contains too many assets
- `LARGE_ASSETS`: Total asset size is too large
- `HIGH_COMPLEXITY`: Scene complexity score is too high
- `LARGE_TEXTURES`: Scene contains large textures

### Compatibility Check Codes

- `ASSET_TOO_LARGE`: Asset is too large for target environment
- `TEXTURE_TOO_LARGE`: Texture resolution exceeds maximum for target environment
- `MODEL_TOO_COMPLEX`: Model is too complex for target environment
- `TOO_MANY_OBJECTS`: Scene contains too many objects for target environment
- `UNSUPPORTED_FEATURE`: Feature is not supported on target environment

## Environment Constraints

| Environment | Max Asset Size | Max Texture Size | Max Triangles | Max Objects | Max Lights |
|-------------|---------------|------------------|---------------|-------------|------------|
| Quest 2     | 50 MB         | 2048             | 1,000,000     | 1,000       | 8          |
| Quest 3     | 100 MB        | 4096             | 2,000,000     | 2,000       | 16         |
| Pico 4      | 75 MB         | 4096             | 1,500,000     | 1,500       | 12         |
| SteamVR     | 500 MB        | 8192             | 5,000,000     | 5,000       | 32         |
