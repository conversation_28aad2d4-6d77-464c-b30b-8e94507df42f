<template>
  <div class="team-member-management">
    <div class="team-header">
      <h2 class="team-title">Team Members</h2>
      <button class="invite-button" @click="showInviteModal = true">
        <i class="material-icons">person_add</i>
        Invite Member
      </button>
    </div>

    <div class="team-content">
      <div class="team-filters">
        <div class="search-container">
          <i class="material-icons search-icon">search</i>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search members..."
            class="search-input"
          />
          <button
            v-if="searchQuery"
            class="clear-search-button"
            @click="searchQuery = ''"
          >
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="filter-container">
          <div class="filter-group">
            <label for="role-filter">Role</label>
            <select id="role-filter" v-model="roleFilter">
              <option value="">All Roles</option>
              <option value="admin">Admin</option>
              <option value="editor">Editor</option>
              <option value="viewer">Viewer</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="status-filter">Status</label>
            <select id="status-filter" v-model="statusFilter">
              <option value="">All Statuses</option>
              <option value="active">Active</option>
              <option value="invited">Invited</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          <div class="filter-group">
            <label for="sort-by">Sort By</label>
            <select id="sort-by" v-model="sortBy">
              <option value="name">Name</option>
              <option value="email">Email</option>
              <option value="role">Role</option>
              <option value="lastActive">Last Active</option>
            </select>
          </div>

          <button
            class="sort-direction-button"
            @click="sortDirection = sortDirection === 'asc' ? 'desc' : 'asc'"
            :title="sortDirection === 'asc' ? 'Sort Ascending' : 'Sort Descending'"
          >
            <i class="material-icons">{{ sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward' }}</i>
          </button>
        </div>
      </div>

      <div class="team-table-container">
        <table class="team-table">
          <thead>
            <tr>
              <th class="member-column">Member</th>
              <th class="role-column">Role</th>
              <th class="status-column">Status</th>
              <th class="last-active-column">Last Active</th>
              <th class="actions-column">Actions</th>
            </tr>
          </thead>

          <tbody>
            <tr v-if="filteredMembers.length === 0">
              <td colspan="5" class="empty-state">
                <div class="empty-state-content">
                  <i class="material-icons">people</i>
                  <p>No team members found</p>
                </div>
              </td>
            </tr>

            <tr
              v-for="member in paginatedMembers"
              :key="member.id"
              :class="{ 'invited-row': member.status === 'invited' }"
            >
              <td class="member-column">
                <div class="member-info">
                  <div class="member-avatar" :style="{ backgroundColor: getAvatarColor(member.name) }">
                    {{ getInitials(member.name) }}
                  </div>
                  <div class="member-details">
                    <div class="member-name">{{ member.name }}</div>
                    <div class="member-email">{{ member.email }}</div>
                  </div>
                </div>
              </td>

              <td class="role-column">
                <div class="role-badge" :class="`role-${member.role}`">
                  {{ getRoleName(member.role) }}
                </div>
              </td>

              <td class="status-column">
                <div class="status-badge" :class="`status-${member.status}`">
                  {{ getStatusName(member.status) }}
                </div>
              </td>

              <td class="last-active-column">
                {{ formatLastActive(member.lastActive) }}
              </td>

              <td class="actions-column">
                <div class="actions-menu">
                  <button class="action-button" @click="editMember(member)">
                    <i class="material-icons">edit</i>
                  </button>

                  <button
                    v-if="member.status === 'invited'"
                    class="action-button"
                    @click="resendInvitation(member)"
                  >
                    <i class="material-icons">send</i>
                  </button>

                  <button class="action-button" @click="showDeleteConfirmation(member)">
                    <i class="material-icons">delete</i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="pagination-container">
        <div class="pagination-info">
          Showing {{ paginationStart }} to {{ paginationEnd }} of {{ filteredMembers.length }} members
        </div>

        <div class="pagination-controls">
          <button
            class="pagination-button"
            @click="currentPage = 1"
            :disabled="currentPage === 1"
          >
            <i class="material-icons">first_page</i>
          </button>

          <button
            class="pagination-button"
            @click="currentPage--"
            :disabled="currentPage === 1"
          >
            <i class="material-icons">chevron_left</i>
          </button>

          <div class="pagination-pages">
            <span
              v-for="page in visiblePages"
              :key="page"
              class="page-number"
              :class="{ active: currentPage === page }"
              @click="currentPage = page"
            >
              {{ page }}
            </span>
          </div>

          <button
            class="pagination-button"
            @click="currentPage++"
            :disabled="currentPage === totalPages"
          >
            <i class="material-icons">chevron_right</i>
          </button>

          <button
            class="pagination-button"
            @click="currentPage = totalPages"
            :disabled="currentPage === totalPages"
          >
            <i class="material-icons">last_page</i>
          </button>
        </div>

        <div class="pagination-size">
          <label for="page-size">Show</label>
          <select id="page-size" v-model="pageSize">
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
          <span>per page</span>
        </div>
      </div>
    </div>

    <!-- Invite Modal -->
    <div v-if="showInviteModal" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Invite Team Member</h3>
          <button class="close-button" @click="showInviteModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="modal-content">
          <div class="form-group">
            <label for="invite-email">Email Address</label>
            <input
              id="invite-email"
              v-model="inviteForm.email"
              type="email"
              placeholder="Enter email address"
            />
          </div>

          <div class="form-row">
            <div class="form-group half">
              <label for="invite-first-name">First Name</label>
              <input
                id="invite-first-name"
                v-model="inviteForm.firstName"
                type="text"
                placeholder="Enter first name"
              />
            </div>

            <div class="form-group half">
              <label for="invite-last-name">Last Name</label>
              <input
                id="invite-last-name"
                v-model="inviteForm.lastName"
                type="text"
                placeholder="Enter last name"
              />
            </div>
          </div>

          <div class="form-group">
            <label for="invite-role">Role</label>
            <select id="invite-role" v-model="inviteForm.role">
              <option value="admin">Admin</option>
              <option value="editor">Editor</option>
              <option value="viewer">Viewer</option>
            </select>
            <div class="help-text">
              <span v-if="inviteForm.role === 'admin'">Can manage team members and all content</span>
              <span v-else-if="inviteForm.role === 'editor'">Can create and edit content</span>
              <span v-else-if="inviteForm.role === 'viewer'">Can view content only</span>
            </div>
          </div>

          <div class="form-group">
            <label for="invite-message">Personal Message (Optional)</label>
            <textarea
              id="invite-message"
              v-model="inviteForm.message"
              placeholder="Add a personal message to the invitation email"
              rows="3"
            ></textarea>
          </div>
        </div>

        <div class="modal-footer">
          <button class="cancel-button" @click="showInviteModal = false">Cancel</button>
          <button class="invite-button" @click="sendInvitation" :disabled="!isInviteFormValid">
            Send Invitation
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="modal-overlay">
      <div class="modal-container delete-modal">
        <div class="modal-header">
          <h3>Remove Team Member</h3>
          <button class="close-button" @click="showDeleteModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="modal-content">
          <div class="delete-confirmation">
            <i class="material-icons warning-icon">warning</i>
            <p>
              Are you sure you want to remove <strong>{{ selectedMember?.name }}</strong> from the team?
              <span v-if="selectedMember?.status === 'active'">They will lose access to all projects and content.</span>
              <span v-else-if="selectedMember?.status === 'invited'">Their invitation will be canceled.</span>
            </p>
          </div>
        </div>

        <div class="modal-footer">
          <button class="cancel-button" @click="showDeleteModal = false">Cancel</button>
          <button class="delete-button" @click="deleteMember">
            Remove Member
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue';

export default {
  name: 'TeamMemberManagement',

  setup() {
    // Mock data for demonstration
    const teamMembers = ref([
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        lastActive: new Date(Date.now() - 1000 * 60 * 30) // 30 minutes ago
      },
      {
        id: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        role: 'editor',
        status: 'active',
        lastActive: new Date(Date.now() - 1000 * 60 * 60 * 2) // 2 hours ago
      },
      {
        id: 3,
        name: 'Bob Johnson',
        email: '<EMAIL>',
        role: 'viewer',
        status: 'active',
        lastActive: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3) // 3 days ago
      },
      {
        id: 4,
        name: 'Alice Williams',
        email: '<EMAIL>',
        role: 'editor',
        status: 'invited',
        lastActive: null
      }
    ]);

    // Filters
    const searchQuery = ref('');
    const roleFilter = ref('');
    const statusFilter = ref('');
    const sortBy = ref('name');
    const sortDirection = ref('asc');

    // Pagination
    const currentPage = ref(1);
    const pageSize = ref(10);

    // Modals
    const showInviteModal = ref(false);
    const showDeleteModal = ref(false);
    const selectedMember = ref(null);

    // Invite form
    const inviteForm = reactive({
      email: '',
      firstName: '',
      lastName: '',
      role: 'editor',
      message: ''
    });

    // Computed properties
    const filteredMembers = computed(() => {
      let result = [...teamMembers.value];

      // Apply search filter
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(member =>
          member.name.toLowerCase().includes(query) ||
          member.email.toLowerCase().includes(query)
        );
      }

      // Apply role filter
      if (roleFilter.value) {
        result = result.filter(member => member.role === roleFilter.value);
      }

      // Apply status filter
      if (statusFilter.value) {
        result = result.filter(member => member.status === statusFilter.value);
      }

      // Apply sorting
      result.sort((a, b) => {
        let valueA, valueB;

        if (sortBy.value === 'name') {
          valueA = a.name.toLowerCase();
          valueB = b.name.toLowerCase();
        } else if (sortBy.value === 'email') {
          valueA = a.email.toLowerCase();
          valueB = b.email.toLowerCase();
        } else if (sortBy.value === 'role') {
          valueA = a.role;
          valueB = b.role;
        } else if (sortBy.value === 'lastActive') {
          valueA = a.lastActive ? a.lastActive.getTime() : 0;
          valueB = b.lastActive ? b.lastActive.getTime() : 0;
        }

        if (sortDirection.value === 'asc') {
          return valueA > valueB ? 1 : -1;
        } else {
          return valueA < valueB ? 1 : -1;
        }
      });

      return result;
    });

    // Pagination
    const totalPages = computed(() => {
      return Math.ceil(filteredMembers.value.length / pageSize.value);
    });

    const paginatedMembers = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return filteredMembers.value.slice(start, end);
    });

    const paginationStart = computed(() => {
      return filteredMembers.value.length === 0 ? 0 : (currentPage.value - 1) * pageSize.value + 1;
    });

    const paginationEnd = computed(() => {
      return Math.min(currentPage.value * pageSize.value, filteredMembers.value.length);
    });

    const visiblePages = computed(() => {
      const pages = [];
      const maxVisiblePages = 5;

      if (totalPages.value <= maxVisiblePages) {
        // Show all pages
        for (let i = 1; i <= totalPages.value; i++) {
          pages.push(i);
        }
      } else {
        // Show a subset of pages
        let startPage = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
        let endPage = startPage + maxVisiblePages - 1;

        if (endPage > totalPages.value) {
          endPage = totalPages.value;
          startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
          pages.push(i);
        }
      }

      return pages;
    });

    // Form validation
    const isInviteFormValid = computed(() => {
      return (
        inviteForm.email &&
        isValidEmail(inviteForm.email) &&
        inviteForm.firstName &&
        inviteForm.lastName &&
        inviteForm.role
      );
    });

    // Methods
    const getInitials = (name) => {
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    };

    const getAvatarColor = (name) => {
      // Generate a deterministic color based on the name
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      const hue = hash % 360;
      return `hsl(${hue}, 70%, 60%)`;
    };

    const getRoleName = (role) => {
      switch (role) {
        case 'admin': return 'Admin';
        case 'editor': return 'Editor';
        case 'viewer': return 'Viewer';
        default: return role;
      }
    };

    const getStatusName = (status) => {
      switch (status) {
        case 'active': return 'Active';
        case 'invited': return 'Invited';
        case 'inactive': return 'Inactive';
        default: return status;
      }
    };

    const formatLastActive = (date) => {
      if (!date) return 'Never';

      const now = new Date();
      const diff = now - date;

      // Less than a minute
      if (diff < 60 * 1000) {
        return 'Just now';
      }

      // Less than an hour
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
      }

      // Less than a day
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
      }

      // Less than a week
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days} day${days !== 1 ? 's' : ''} ago`;
      }

      // Format as date
      return date.toLocaleDateString();
    };

    const isValidEmail = (email) => {
      const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return re.test(email);
    };

    const editMember = (member) => {
      // In a real implementation, this would open an edit modal
      console.log('Edit member:', member);
    };

    const resendInvitation = (member) => {
      // In a real implementation, this would resend the invitation
      console.log('Resend invitation to:', member);
    };

    const showDeleteConfirmation = (member) => {
      selectedMember.value = member;
      showDeleteModal.value = true;
    };

    const deleteMember = () => {
      // In a real implementation, this would call an API to delete the member
      if (selectedMember.value) {
        const index = teamMembers.value.findIndex(m => m.id === selectedMember.value.id);
        if (index !== -1) {
          teamMembers.value.splice(index, 1);
        }
      }

      showDeleteModal.value = false;
      selectedMember.value = null;
    };

    const sendInvitation = () => {
      // In a real implementation, this would call an API to send the invitation
      const newMember = {
        id: Date.now(),
        name: `${inviteForm.firstName} ${inviteForm.lastName}`,
        email: inviteForm.email,
        role: inviteForm.role,
        status: 'invited',
        lastActive: null
      };

      teamMembers.value.push(newMember);

      // Reset form
      inviteForm.email = '';
      inviteForm.firstName = '';
      inviteForm.lastName = '';
      inviteForm.role = 'editor';
      inviteForm.message = '';

      showInviteModal.value = false;
    };

    return {
      teamMembers,
      searchQuery,
      roleFilter,
      statusFilter,
      sortBy,
      sortDirection,
      currentPage,
      pageSize,
      showInviteModal,
      showDeleteModal,
      selectedMember,
      inviteForm,
      filteredMembers,
      totalPages,
      paginatedMembers,
      paginationStart,
      paginationEnd,
      visiblePages,
      isInviteFormValid,
      getInitials,
      getAvatarColor,
      getRoleName,
      getStatusName,
      formatLastActive,
      editMember,
      resendInvitation,
      showDeleteConfirmation,
      deleteMember,
      sendInvitation
    };
  }
};
</script>

<style scoped>
.team-member-management {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
}

.team-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.team-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.invite-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border: none;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.invite-button:hover {
  background-color: var(--theme--primary-dark);
}

.team-content {
  flex: 1;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: auto;
}

.team-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.search-container {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
}

.clear-search-button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.filter-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.filter-group select {
  padding: 8px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  min-width: 120px;
}

.sort-direction-button {
  align-self: flex-end;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.sort-direction-button:hover {
  background-color: var(--theme--background-subdued);
}

.team-table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
}

.team-table {
  width: 100%;
  border-collapse: collapse;
}

.team-table th,
.team-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid var(--theme--border-color);
}

.team-table th {
  background-color: var(--theme--background-subdued);
  font-weight: 600;
  color: var(--theme--foreground-subdued);
  position: sticky;
  top: 0;
  z-index: 1;
}

.team-table tr:last-child td {
  border-bottom: none;
}

.team-table tr:hover td {
  background-color: var(--theme--background-subdued);
}

.invited-row td {
  background-color: rgba(var(--theme--primary-rgb), 0.05);
}

.member-column {
  width: 30%;
}

.role-column,
.status-column {
  width: 15%;
}

.last-active-column {
  width: 20%;
}

.actions-column {
  width: 10%;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.member-details {
  display: flex;
  flex-direction: column;
}

.member-name {
  font-weight: 500;
}

.member-email {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.role-badge,
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.role-admin {
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  color: var(--theme--primary);
}

.role-editor {
  background-color: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.role-viewer {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.status-active {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4caf50;
}

.status-invited {
  background-color: rgba(255, 152, 0, 0.1);
  color: #ff9800;
}

.status-inactive {
  background-color: rgba(158, 158, 158, 0.1);
  color: #9e9e9e;
}

.actions-menu {
  display: flex;
  gap: 4px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.action-button:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.empty-state {
  padding: 48px 24px;
  text-align: center;
}

.empty-state-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--theme--foreground-subdued);
}

.empty-state-content i {
  font-size: 48px;
  margin-bottom: 16px;
}

.pagination-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
}

.pagination-info {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.pagination-button:hover:not(:disabled) {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 4px;
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.page-number:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.page-number.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border-color: var(--theme--primary);
}

.pagination-size {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.pagination-size select {
  padding: 4px 8px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.delete-modal {
  width: 400px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-button,
.delete-button {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  border: 1px solid var(--theme--border-color);
}

.cancel-button:hover {
  background-color: var(--theme--background-subdued);
}

.delete-button {
  background-color: var(--theme--danger);
  color: white;
  border: none;
}

.delete-button:hover {
  background-color: var(--theme--danger-dark);
}

/* Form */
.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group.half {
  flex: 1;
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
}

.help-text {
  margin-top: 4px;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.delete-confirmation {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.warning-icon {
  color: var(--theme--danger);
  font-size: 24px;
}

/* Responsive */
@media (max-width: 768px) {
  .team-filters {
    flex-direction: column;
  }

  .filter-container {
    flex-direction: column;
  }

  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .pagination-controls {
    order: -1;
  }
}
</style>
