import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { SceneValidatorService } from '../../services/scene/scene-validator';
import { validateRequest } from '../middleware/validation';

// Initialize scene validator service
const sceneValidator = new SceneValidatorService(supabase);

/**
 * Validate a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const validateScene = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Validate scene
    const result = await sceneValidator.validateScene(scene_id);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating scene', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Validate scene data
 *
 * @param req - Request
 * @param res - Response
 */
export const validateSceneData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { data } = req.body;

    // Validate parameters
    if (!data) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_DATA',
          message: 'Scene data is required',
        },
      });
      return;
    }

    // Validate scene data
    const errors = sceneValidator.validateSceneData(data);

    res.status(200).json({
      success: true,
      data: {
        valid: errors.length === 0,
        errors,
      },
    });
  } catch (error) {
    logger.error('Error validating scene data', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Validate scene flow
 *
 * @param req - Request
 * @param res - Response
 */
export const validateSceneFlow = async (req: Request, res: Response): Promise<void> => {
  try {
    const { flow } = req.body;

    // Validate parameters
    if (!flow) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_FLOW',
          message: 'Scene flow is required',
        },
      });
      return;
    }

    // Validate scene flow
    const result = await sceneValidator.validateSceneFlow(flow);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating scene flow', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Analyze scene performance impact
 *
 * @param req - Request
 * @param res - Response
 */
export const analyzeScenePerformance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Analyze scene performance
    const result = await sceneValidator.analyzeScenePerformance(scene_id);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error analyzing scene performance', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Check scene compatibility
 *
 * @param req - Request
 * @param res - Response
 */
export const checkSceneCompatibility = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { target_environment } = req.query;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Check scene compatibility
    const result = await sceneValidator.checkSceneCompatibility(
      scene_id,
      target_environment as string,
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error checking scene compatibility', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
