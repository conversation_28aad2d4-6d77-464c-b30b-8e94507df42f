/**
 * Real-time WebSocket API Endpoints
 * Provides HTTP endpoints for WebSocket management and real-time features
 */

import express from 'express';
import { WebSocketManager } from '../../services/realtime/websocket-manager.js';
import { EventDispatcher } from '../../services/realtime/event-dispatcher.js';
import { ConnectionPool } from '../../services/realtime/connection-pool.js';
import { MessageRouter } from '../../services/realtime/message-router.js';
import { SyncEngine } from '../../services/realtime/sync-engine.js';

const router = express.Router();

// Initialize services (these would typically be injected or imported from a service container)
let wsManager, eventDispatcher, connectionPool, messageRouter, syncEngine;

/**
 * Initialize real-time services
 */
export async function initializeRealtimeServices(options = {}) {
  try {
    // Initialize services with configuration
    wsManager = new WebSocketManager(options.websocket || {});
    eventDispatcher = new EventDispatcher(options.events || {});
    connectionPool = new ConnectionPool(options.connectionPool || {});
    messageRouter = new MessageRouter(options.messageRouter || {});
    syncEngine = new SyncEngine(options.sync || {});

    // Setup service integrations
    setupServiceIntegrations();

    console.log('✅ Real-time services initialized');
  } catch (error) {
    console.error('❌ Failed to initialize real-time services:', error);
    throw error;
  }
}

function setupServiceIntegrations() {
  // Connect WebSocket Manager to Event Dispatcher
  wsManager.on('message', ({ connection, message }) => {
    eventDispatcher.publish('websocket.message', {
      connectionId: connection.id,
      message,
      clientInfo: connection.clientInfo
    });
  });

  // Connect Event Dispatcher to Message Router
  eventDispatcher.subscribe('websocket.message', async (event) => {
    await messageRouter.routeMessage(event.data.message, {
      source: 'websocket',
      connectionId: event.data.connectionId
    });
  });

  // Connect Message Router to Sync Engine
  messageRouter.addRoute('type:sync', {
    target: 'sync-engine',
    priority: 8,
    transformers: ['sync-transformer']
  });

  // Add sync transformer
  messageRouter.addTransformer('sync-transformer', (message, context) => {
    return {
      ...message,
      syncContext: {
        connectionId: context.connectionId,
        timestamp: Date.now()
      }
    };
  });
}

/**
 * GET /api/websocket/status
 * Get WebSocket server status and metrics
 */
router.get('/status', (req, res) => {
  try {
    const status = {
      websocket: wsManager ? wsManager.getMetrics() : null,
      events: eventDispatcher ? eventDispatcher.getMetrics() : null,
      connectionPool: connectionPool ? connectionPool.getMetrics() : null,
      messageRouter: messageRouter ? messageRouter.getMetrics() : null,
      sync: syncEngine ? syncEngine.getMetrics() : null,
      timestamp: Date.now()
    };

    res.json({
      success: true,
      data: status
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/connections
 * Get active WebSocket connections
 */
router.get('/connections', (req, res) => {
  try {
    if (!wsManager) {
      return res.status(503).json({
        success: false,
        error: 'WebSocket manager not initialized'
      });
    }

    const connections = Array.from(wsManager.connections.keys()).map(connectionId => 
      wsManager.getConnectionInfo(connectionId)
    );

    res.json({
      success: true,
      data: {
        total: connections.length,
        connections: connections.slice(0, 100) // Limit response size
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/connections/:connectionId
 * Get specific connection information
 */
router.get('/connections/:connectionId', (req, res) => {
  try {
    if (!wsManager) {
      return res.status(503).json({
        success: false,
        error: 'WebSocket manager not initialized'
      });
    }

    const { connectionId } = req.params;
    const connectionInfo = wsManager.getConnectionInfo(connectionId);

    if (!connectionInfo) {
      return res.status(404).json({
        success: false,
        error: 'Connection not found'
      });
    }

    res.json({
      success: true,
      data: connectionInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/websocket/broadcast
 * Broadcast message to WebSocket connections
 */
router.post('/broadcast', async (req, res) => {
  try {
    if (!wsManager) {
      return res.status(503).json({
        success: false,
        error: 'WebSocket manager not initialized'
      });
    }

    const { channel, data, targetUsers, excludeConnection } = req.body;

    if (!channel || !data) {
      return res.status(400).json({
        success: false,
        error: 'Channel and data are required'
      });
    }

    const result = await wsManager.broadcast(channel, data, {
      targetUsers,
      excludeConnection
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/events/channels
 * Get event channels and their subscribers
 */
router.get('/events/channels', (req, res) => {
  try {
    if (!eventDispatcher) {
      return res.status(503).json({
        success: false,
        error: 'Event dispatcher not initialized'
      });
    }

    const stats = eventDispatcher.getRoutingStats();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/websocket/events/publish
 * Publish event to event dispatcher
 */
router.post('/events/publish', async (req, res) => {
  try {
    if (!eventDispatcher) {
      return res.status(503).json({
        success: false,
        error: 'Event dispatcher not initialized'
      });
    }

    const { channel, data, options } = req.body;

    if (!channel || !data) {
      return res.status(400).json({
        success: false,
        error: 'Channel and data are required'
      });
    }

    const eventId = await eventDispatcher.publish(channel, data, {
      ...options,
      source: 'api'
    });

    res.json({
      success: true,
      data: { eventId }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/events/history/:channel
 * Get event history for a channel
 */
router.get('/events/history/:channel', (req, res) => {
  try {
    if (!eventDispatcher) {
      return res.status(503).json({
        success: false,
        error: 'Event dispatcher not initialized'
      });
    }

    const { channel } = req.params;
    const { since, until, limit } = req.query;

    const options = {};
    if (since) options.since = parseInt(since);
    if (until) options.until = parseInt(until);
    if (limit) options.limit = parseInt(limit);

    const history = eventDispatcher.getEventHistory(channel, options);

    res.json({
      success: true,
      data: {
        channel,
        events: history,
        total: history.length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/pools
 * Get connection pool information
 */
router.get('/pools', (req, res) => {
  try {
    if (!connectionPool) {
      return res.status(503).json({
        success: false,
        error: 'Connection pool not initialized'
      });
    }

    const pools = connectionPool.getAllPools();
    res.json({
      success: true,
      data: pools
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/pools/:poolId
 * Get specific pool information
 */
router.get('/pools/:poolId', (req, res) => {
  try {
    if (!connectionPool) {
      return res.status(503).json({
        success: false,
        error: 'Connection pool not initialized'
      });
    }

    const { poolId } = req.params;
    const pool = connectionPool.getPool(poolId);

    if (!pool) {
      return res.status(404).json({
        success: false,
        error: 'Pool not found'
      });
    }

    res.json({
      success: true,
      data: pool
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/routing/stats
 * Get message routing statistics
 */
router.get('/routing/stats', (req, res) => {
  try {
    if (!messageRouter) {
      return res.status(503).json({
        success: false,
        error: 'Message router not initialized'
      });
    }

    const stats = messageRouter.getRoutingStats();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/websocket/routing/routes
 * Add a new routing rule
 */
router.post('/routing/routes', (req, res) => {
  try {
    if (!messageRouter) {
      return res.status(503).json({
        success: false,
        error: 'Message router not initialized'
      });
    }

    const { pattern, config } = req.body;

    if (!pattern || !config) {
      return res.status(400).json({
        success: false,
        error: 'Pattern and config are required'
      });
    }

    messageRouter.addRoute(pattern, config);

    res.json({
      success: true,
      data: { pattern, config }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * DELETE /api/websocket/routing/routes/:pattern
 * Remove a routing rule
 */
router.delete('/routing/routes/:pattern', (req, res) => {
  try {
    if (!messageRouter) {
      return res.status(503).json({
        success: false,
        error: 'Message router not initialized'
      });
    }

    const { pattern } = req.params;
    const removed = messageRouter.removeRoute(decodeURIComponent(pattern));

    if (!removed) {
      return res.status(404).json({
        success: false,
        error: 'Route not found'
      });
    }

    res.json({
      success: true,
      data: { pattern, removed: true }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/sync/entities
 * Get synchronized entities
 */
router.get('/sync/entities', (req, res) => {
  try {
    if (!syncEngine) {
      return res.status(503).json({
        success: false,
        error: 'Sync engine not initialized'
      });
    }

    const metrics = syncEngine.getMetrics();
    res.json({
      success: true,
      data: {
        totalEntities: metrics.entities,
        totalSubscriptions: metrics.subscriptions,
        pendingConflicts: metrics.pendingConflicts,
        metrics
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/sync/entities/:entityId
 * Get specific entity state
 */
router.get('/sync/entities/:entityId', (req, res) => {
  try {
    if (!syncEngine) {
      return res.status(503).json({
        success: false,
        error: 'Sync engine not initialized'
      });
    }

    const { entityId } = req.params;
    const entity = syncEngine.getEntity(entityId);

    if (!entity) {
      return res.status(404).json({
        success: false,
        error: 'Entity not found'
      });
    }

    res.json({
      success: true,
      data: entity
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * PUT /api/websocket/sync/entities/:entityId
 * Update entity through sync engine
 */
router.put('/sync/entities/:entityId', async (req, res) => {
  try {
    if (!syncEngine) {
      return res.status(503).json({
        success: false,
        error: 'Sync engine not initialized'
      });
    }

    const { entityId } = req.params;
    const { data, version, metadata } = req.body;

    if (!data) {
      return res.status(400).json({
        success: false,
        error: 'Data is required'
      });
    }

    const result = await syncEngine.updateEntity(entityId, data, {
      clientId: req.user?.id || 'api',
      version,
      metadata
    });

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/sync/entities/:entityId/history
 * Get entity version history
 */
router.get('/sync/entities/:entityId/history', (req, res) => {
  try {
    if (!syncEngine) {
      return res.status(503).json({
        success: false,
        error: 'Sync engine not initialized'
      });
    }

    const { entityId } = req.params;
    const { since, limit } = req.query;

    const options = {};
    if (since) options.since = parseInt(since);
    if (limit) options.limit = parseInt(limit);

    const history = syncEngine.getEntityHistory(entityId, options);

    res.json({
      success: true,
      data: {
        entityId,
        history,
        total: history.length
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * POST /api/websocket/sync/offline/:clientId/process
 * Process offline operations for a client
 */
router.post('/sync/offline/:clientId/process', async (req, res) => {
  try {
    if (!syncEngine) {
      return res.status(503).json({
        success: false,
        error: 'Sync engine not initialized'
      });
    }

    const { clientId } = req.params;
    const results = await syncEngine.processOfflineOperations(clientId);

    res.json({
      success: true,
      data: {
        clientId,
        processed: results.length,
        results
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * GET /api/websocket/health
 * Health check endpoint for real-time services
 */
router.get('/health', (req, res) => {
  try {
    const health = {
      status: 'healthy',
      services: {
        websocket: wsManager ? 'running' : 'stopped',
        events: eventDispatcher ? 'running' : 'stopped',
        connectionPool: connectionPool ? 'running' : 'stopped',
        messageRouter: messageRouter ? 'running' : 'stopped',
        sync: syncEngine ? 'running' : 'stopped'
      },
      timestamp: Date.now()
    };

    // Check if any critical services are down
    const criticalServices = ['websocket', 'events'];
    const downServices = criticalServices.filter(service => health.services[service] === 'stopped');
    
    if (downServices.length > 0) {
      health.status = 'unhealthy';
      health.issues = downServices.map(service => `${service} service is down`);
    }

    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json({
      success: health.status === 'healthy',
      data: health
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export { router as realtimeRouter };
export default router;
