import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../shared/utils/logger';
import { errorHandler } from '../../../shared/utils/error-handler';
import { SceneService } from '../../../services/scene-service';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  scene_id: z.string().uuid(),
  version: z.string().optional(),
});

/**
 * Scene Versions API endpoint
 *
 * This endpoint returns the versions of a scene.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse({
      scene_id: req.query.scene_id,
      version: req.query.version,
    });

    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { scene_id, version } = queryResult.data;

    // Log the request
    logger.info('Scene versions request', {
      scene_id,
      version,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create scene service
    const sceneService = new SceneService(supabase);

    // Get scene
    const scene = await sceneService.getScene(scene_id);
    if (!scene) {
      return res.status(404).json({ error: 'Scene not found' });
    }

    // Check if user has permission to access this scene
    if (session.user.id !== scene.vendor_id && session.user.role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // If version is specified, get that specific version
    if (version) {
      const sceneVersion = await sceneService.getSceneVersion(scene_id, version);
      if (!sceneVersion) {
        return res.status(404).json({ error: 'Scene version not found' });
      }

      // Return version
      return res.status(200).json({ version: sceneVersion });
    }

    // Otherwise, get all versions
    const versions = await sceneService.getSceneVersions(scene_id);

    // Return versions
    return res.status(200).json({ versions });
  } catch (error) {
    logger.error('Unexpected error in scene versions endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
