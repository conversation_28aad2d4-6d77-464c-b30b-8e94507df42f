import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createWrapper } from '../src/test-utils/component-test-utils.js';
import TeamMemberManagement from '../src/components/TeamMemberManagement.vue';
import CommentingFeedbackSystem from '../src/components/CommentingFeedbackSystem.vue';
import ActivityTracking from '../src/components/ActivityTracking.vue';

// Mock the child components
vi.mock('../src/components/TeamMemberManagement.vue', () => ({
  default: {
    name: 'TeamMemberManagement',
    render: h => h('div', { class: 'mock-team-member-management' }),
  },
}));

vi.mock('../src/components/CommentingFeedbackSystem.vue', () => ({
  default: {
    name: 'CommentingFeedbackSystem',
    render: h => h('div', { class: 'mock-commenting-feedback-system' }),
  },
}));

vi.mock('../src/components/ActivityTracking.vue', () => ({
  default: {
    name: 'ActivityTracking',
    render: h => h('div', { class: 'mock-activity-tracking' }),
  },
}));

// Create a minimal implementation of the component for testing
const CollaborationFeaturesMock = {
  name: 'CollaborationFeatures',
  components: {
    TeamMemberManagement,
    CommentingFeedbackSystem,
    ActivityTracking,
  },
  data() {
    return {
      activeTab: 'team',
    };
  },
  methods: {
    setActiveTab(tab) {
      this.activeTab = tab;
    },
  },
  template: `
    <div class="collaboration-features">
      <div class="tabs">
        <button class="tab-button" :class="{ active: activeTab === 'team' }" @click="setActiveTab('team')">Team Members</button>
        <button class="tab-button" :class="{ active: activeTab === 'comments' }" @click="setActiveTab('comments')">Comments & Feedback</button>
        <button class="tab-button" :class="{ active: activeTab === 'activity' }" @click="setActiveTab('activity')">Activity Tracking</button>
      </div>
      <div class="tab-content">
        <TeamMemberManagement v-if="activeTab === 'team'" />
        <CommentingFeedbackSystem v-if="activeTab === 'comments'" />
        <ActivityTracking v-if="activeTab === 'activity'" />
      </div>
    </div>
  `,
};

describe('CollaborationFeatures', () => {
  let wrapper;

  beforeEach(() => {
    // Use our custom createWrapper function
    wrapper = createWrapper(CollaborationFeaturesMock);
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.collaboration-features').exists()).toBe(true);
  });

  it('renders the tabs correctly', () => {
    const tabs = wrapper.findAll('.tab-button');
    expect(tabs.length).toBe(3);

    // Check tab names using textContent directly
    expect(tabs.at(0).element.textContent.trim()).toContain('Team Members');
    expect(tabs.at(1).element.textContent.trim()).toContain('Comments & Feedback');
    expect(tabs.at(2).element.textContent.trim()).toContain('Activity Tracking');
  });

  it('shows the TeamMemberManagement component by default', () => {
    expect(wrapper.findComponent(TeamMemberManagement).exists()).toBe(true);
    expect(wrapper.findComponent(CommentingFeedbackSystem).exists()).toBe(false);
    expect(wrapper.findComponent(ActivityTracking).exists()).toBe(false);
  });

  it('switches to CommentingFeedbackSystem when clicking the Comments & Feedback tab', async () => {
    // Directly call the method instead of triggering a click
    wrapper.vm.setActiveTab('comments');
    await wrapper.vm.$nextTick();

    // Check that the correct component is shown
    expect(wrapper.findComponent(TeamMemberManagement).exists()).toBe(false);
    expect(wrapper.findComponent(CommentingFeedbackSystem).exists()).toBe(true);
    expect(wrapper.findComponent(ActivityTracking).exists()).toBe(false);
  });

  it('switches to ActivityTracking when clicking the Activity Tracking tab', async () => {
    // Directly call the method instead of triggering a click
    wrapper.vm.setActiveTab('activity');
    await wrapper.vm.$nextTick();

    // Check that the correct component is shown
    expect(wrapper.findComponent(TeamMemberManagement).exists()).toBe(false);
    expect(wrapper.findComponent(CommentingFeedbackSystem).exists()).toBe(false);
    expect(wrapper.findComponent(ActivityTracking).exists()).toBe(true);
  });

  it('applies the active class to the selected tab', async () => {
    // Initially, the Team Members tab should be active
    wrapper.vm.setActiveTab('team');
    await wrapper.vm.$nextTick();

    let tabs = wrapper.findAll('.tab-button');
    expect(tabs.at(0).classes()).toContain('active');
    expect(tabs.at(1).classes()).not.toContain('active');
    expect(tabs.at(2).classes()).not.toContain('active');

    // Set Comments & Feedback tab as active
    wrapper.vm.setActiveTab('comments');
    await wrapper.vm.$nextTick();

    // Now the Comments & Feedback tab should be active
    tabs = wrapper.findAll('.tab-button');
    expect(tabs.at(0).classes()).not.toContain('active');
    expect(tabs.at(1).classes()).toContain('active');
    expect(tabs.at(2).classes()).not.toContain('active');

    // Set Activity Tracking tab as active
    wrapper.vm.setActiveTab('activity');
    await wrapper.vm.$nextTick();

    // Now the Activity Tracking tab should be active
    tabs = wrapper.findAll('.tab-button');
    expect(tabs.at(0).classes()).not.toContain('active');
    expect(tabs.at(1).classes()).not.toContain('active');
    expect(tabs.at(2).classes()).toContain('active');
  });
});
