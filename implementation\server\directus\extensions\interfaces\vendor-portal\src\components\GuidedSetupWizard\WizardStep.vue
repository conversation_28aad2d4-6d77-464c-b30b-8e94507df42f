<template>
  <div class="wizard-step">
    <div v-if="title" class="step-header">
      <h2 class="step-title">{{ title }}</h2>
      <p v-if="description" class="step-description">{{ description }}</p>
    </div>

    <div class="step-content">
      <slot></slot>
    </div>

    <div v-if="hasValidationErrors" class="validation-errors">
      <h3 class="validation-title">Please fix the following errors:</h3>
      <ul class="error-list">
        <li v-for="(error, index) in validationErrors" :key="index" class="error-item">
          {{ error }}
        </li>
      </ul>
    </div>

    <div v-if="showHelpTips && helpTips.length > 0" class="help-tips">
      <div
        v-for="(tip, index) in helpTips"
        :key="index"
        class="help-tip"
      >
        <div class="tip-icon">
          <i class="material-icons">lightbulb</i>
        </div>
        <div class="tip-content">
          <h4 class="tip-title">{{ tip.title }}</h4>
          <p class="tip-text">{{ tip.text }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WizardStep',

  props: {
    title: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    },
    stepData: {
      type: Object,
      default: () => ({})
    },
    validationSchema: {
      type: Object,
      default: null
    },
    helpTips: {
      type: Array,
      default: () => []
    },
    showHelpTips: {
      type: Boolean,
      default: true
    },
    validateOnMount: {
      type: Boolean,
      default: false
    },
    validateOnChange: {
      type: Boolean,
      default: true
    },
    disabledFields: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      localStepData: {},
      validationErrors: [],
      areTipsVisible: false,
      isInitialized: false
    };
  },

  computed: {
    hasValidationErrors() {
      return this.validationErrors.length > 0;
    }
  },

  watch: {
    stepData: {
      handler(newData) {
        this.localStepData = { ...newData };

        if (this.isInitialized && this.validateOnChange) {
          this.validate();
        }
      },
      immediate: true,
      deep: true
    }
  },

  mounted() {
    this.isInitialized = true;

    if (this.validateOnMount) {
      this.validate();
    }
  },

  methods: {
    updateStepData(field, value) {
      // Update a specific field in the step data
      this.localStepData[field] = value;

      // Emit the updated data
      this.$emit('update:step-data', this.localStepData);

      // Validate if needed
      if (this.validateOnChange) {
        this.validate();
      }
    },

    validate() {
      if (!this.validationSchema) {
        // If no validation schema is provided, consider the step valid
        this.validationErrors = [];
        this.$emit('validate', true);
        return true;
      }

      // Reset validation errors
      this.validationErrors = [];

      // Validate each field according to the schema
      for (const field in this.validationSchema) {
        const rules = this.validationSchema[field];
        const value = this.localStepData[field];

        // Check required rule
        if (rules.required && (value === undefined || value === null || value === '')) {
          this.validationErrors.push(`${rules.label || field} is required`);
          continue;
        }

        // Skip other validations if value is empty and not required
        if (value === undefined || value === null || value === '') {
          continue;
        }

        // Check min length rule
        if (rules.minLength && String(value).length < rules.minLength) {
          this.validationErrors.push(`${rules.label || field} must be at least ${rules.minLength} characters`);
        }

        // Check max length rule
        if (rules.maxLength && String(value).length > rules.maxLength) {
          this.validationErrors.push(`${rules.label || field} must be at most ${rules.maxLength} characters`);
        }

        // Check pattern rule
        if (rules.pattern && !new RegExp(rules.pattern).test(String(value))) {
          this.validationErrors.push(rules.patternMessage || `${rules.label || field} is invalid`);
        }

        // Check min value rule
        if (rules.min !== undefined && Number(value) < rules.min) {
          this.validationErrors.push(`${rules.label || field} must be at least ${rules.min}`);
        }

        // Check max value rule
        if (rules.max !== undefined && Number(value) > rules.max) {
          this.validationErrors.push(`${rules.label || field} must be at most ${rules.max}`);
        }

        // Check custom validation rule
        if (rules.validate && typeof rules.validate === 'function') {
          const result = rules.validate(value, this.localStepData);
          if (result !== true) {
            this.validationErrors.push(result || `${rules.label || field} is invalid`);
          }
        }
      }

      // Emit validation result
      const isValid = this.validationErrors.length === 0;
      this.$emit('validate', isValid);

      return isValid;
    },

    validateStep() {
      return this.validate();
    },

    resetValidation() {
      this.validationErrors = [];
    },

    toggleHelpTips() {
      this.areTipsVisible = !this.areTipsVisible;
    },

    isFieldDisabled(fieldName) {
      return this.disabledFields.includes(fieldName);
    }
  }
};
</script>

<style scoped>
.wizard-step {
  display: flex;
  flex-direction: column;
}

.step-header {
  margin-bottom: 24px;
}

.step-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.step-description {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin: 0;
}

.step-content {
  margin-bottom: 24px;
}

.validation-errors {
  margin-top: 24px;
  padding: 16px;
  background-color: rgba(var(--theme--danger-rgb), 0.1);
  border-left: 4px solid var(--theme--danger);
  border-radius: 4px;
}

.validation-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--theme--danger);
}

.error-list {
  margin: 0;
  padding-left: 20px;
}

.error-item {
  font-size: 14px;
  margin-bottom: 4px;
  color: var(--theme--danger);
}

.help-tips {
  margin-top: 24px;
  padding: 16px;
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  border-radius: 4px;
}

.help-tip {
  display: flex;
  margin-bottom: 16px;
}

.help-tip:last-child {
  margin-bottom: 0;
}

.tip-icon {
  margin-right: 12px;
  color: var(--theme--primary);
}

.tip-content {
  flex: 1;
}

.tip-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.tip-text {
  font-size: 14px;
  margin: 0;
  color: var(--theme--foreground-subdued);
}
</style>
