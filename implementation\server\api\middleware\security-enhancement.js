/**
 * Security Enhancement Middleware
 *
 * This middleware implements various security enhancements for the API,
 * including CSRF protection, content security policy, and security headers.
 */

const crypto = require('crypto');
const Redis = require('ioredis');
const { logger } = require('./auth-middleware');

// Initialize Redis client
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32;
const CSRF_TOKEN_TTL = 60 * 60; // 1 hour in seconds
const CSRF_TOKEN_PREFIX = 'csrf:token:';

/**
 * Generate a CSRF token
 * @returns {string} CSRF token
 */
function generateCsrfToken() {
  return crypto.randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
}

/**
 * CSRF protection middleware
 * @param {Object} options - CSRF options
 * @returns {Function} Express middleware
 *
 * @deprecated Use the iron-session based CSRF protection from csrf-express-adapter.ts instead
 */
function csrfProtection(options = {}) {
  const { csrfProtection } = require('./csrf-express-adapter.ts');
  return csrfProtection(options);
}

/**
 * Validate a CSRF token
 * @param {string} token - CSRF token
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} Whether the token is valid
 *
 * @deprecated Use the iron-session based validateCsrfToken from csrf-express-adapter.ts instead
 */
async function validateCsrfToken(token, userId = 'anonymous') {
  try {
    // Check if token exists in Redis
    const tokenKey = `${CSRF_TOKEN_PREFIX}${userId}:${token}`;
    const exists = await redis.exists(tokenKey);

    return exists === 1;
  } catch (error) {
    logger.error('Error validating CSRF token:', error);
    return false;
  }
}

/**
 * Store a CSRF token
 * @param {string} token - CSRF token
 * @param {string} userId - User ID
 * @returns {Promise<boolean>} Whether the token was stored
 *
 * @deprecated Use the iron-session based CSRF token storage from csrf-express-adapter.ts instead
 */
async function storeCsrfToken(token, userId = 'anonymous') {
  try {
    // Store token in Redis with expiry
    const tokenKey = `${CSRF_TOKEN_PREFIX}${userId}:${token}`;
    await redis.set(tokenKey, 'valid', 'EX', CSRF_TOKEN_TTL);

    return true;
  } catch (error) {
    logger.error('Error storing CSRF token:', error);
    return false;
  }
}

/**
 * CSRF token generator middleware
 * @param {Object} options - CSRF options
 * @returns {Function} Express middleware
 *
 * @deprecated Use the iron-session based CSRF token generator from csrf-express-adapter.ts instead
 */
function csrfTokenGenerator(options = {}) {
  const { csrfTokenGenerator } = require('./csrf-express-adapter.ts');
  return csrfTokenGenerator(options);
}

/**
 * Content Security Policy middleware
 * @param {Object} options - CSP options
 * @returns {Function} Express middleware
 */
function contentSecurityPolicy(options = {}) {
  const {
    directives = {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:', 'blob:'],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
      reportUri: '/api/csp-report',
    },
    reportOnly = false,
  } = options;

  return (_req, res, next) => {
    try {
      // Build CSP header value
      const cspValue = Object.entries(directives)
        .map(([key, values]) => {
          // Convert camelCase to kebab-case
          const directive = key.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase();
          return `${directive} ${Array.isArray(values) ? values.join(' ') : values}`;
        })
        .join('; ');

      // Set CSP header
      const headerName = reportOnly
        ? 'Content-Security-Policy-Report-Only'
        : 'Content-Security-Policy';

      res.set(headerName, cspValue);

      next();
    } catch (error) {
      logger.error('Error in Content Security Policy middleware:', error);
      next(error);
    }
  };
}

/**
 * Security headers middleware
 * @returns {Function} Express middleware
 */
function securityHeaders() {
  return (_req, res, next) => {
    try {
      // Set security headers
      res.set('X-Content-Type-Options', 'nosniff');
      res.set('X-Frame-Options', 'DENY');
      res.set('X-XSS-Protection', '1; mode=block');
      res.set('Referrer-Policy', 'strict-origin-when-cross-origin');
      res.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
      res.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');

      next();
    } catch (error) {
      logger.error('Error in security headers middleware:', error);
      next(error);
    }
  };
}

/**
 * Rate limiting middleware for security-sensitive endpoints
 * @param {Object} options - Rate limiting options
 * @returns {Function} Express middleware
 */
function securityRateLimit(options = {}) {
  const {
    windowMs = 15 * 60 * 1000, // 15 minutes
    max = 5, // 5 requests per windowMs
    message = 'Too many requests, please try again later',
  } = options;

  return async (req, res, next) => {
    try {
      const ip = req.ip || req.connection.remoteAddress;
      const key = `rate-limit:security:${ip}`;

      // Get current count
      const count = await redis.incr(key);

      // Set expiry on first request
      if (count === 1) {
        await redis.expire(key, Math.floor(windowMs / 1000));
      }

      // Set rate limit headers
      res.set('X-RateLimit-Limit', max.toString());
      res.set('X-RateLimit-Remaining', Math.max(0, max - count).toString());
      res.set(
        'X-RateLimit-Reset',
        (Math.floor(Date.now() / 1000) + (await redis.ttl(key))).toString(),
      );

      // If over limit, return error
      if (count > max) {
        return res.status(429).json({
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message,
          },
        });
      }

      next();
    } catch (error) {
      logger.error('Error in security rate limit middleware:', error);
      next(error);
    }
  };
}

module.exports = {
  csrfProtection,
  csrfTokenGenerator,
  contentSecurityPolicy,
  securityHeaders,
  securityRateLimit,
  generateCsrfToken,
  validateCsrfToken,
  storeCsrfToken,
};
