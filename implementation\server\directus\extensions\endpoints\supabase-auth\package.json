{"name": "directus-extension-supabase-auth", "version": "1.0.0", "description": "Supabase authentication integration for Directus", "main": "index.js", "keywords": ["directus", "directus-extension", "directus-custom-endpoint", "supabase", "authentication"], "directus:extension": {"type": "endpoint", "path": "index.js", "source": "src/index.js", "host": "^10.0.0"}, "scripts": {"build": "directus-extension build"}, "dependencies": {"axios": "^1.4.0", "jsonwebtoken": "^9.0.0"}}