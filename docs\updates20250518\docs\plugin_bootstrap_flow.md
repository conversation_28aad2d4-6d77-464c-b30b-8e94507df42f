#### **plugin_bootstrap_flow.md**

# Plugin Bootstrap Flow

## Purpose
Defines the automatic configuration logic activated when a VR plugin is installed in the client runtime (Unreal/Unity).

## Initialization Sequence
1. **Auth & Handshake**  
   - Plugin sends `vendorId`, `environmentKey`, and client version.  
   - Server validates and returns current config hashes.

2. **Download Assets & Config**  
   - `scene.flow.json`  
   - `store.config.json`  
   - `bundle.manifest.json`  
   - `materialLibrary.json`  
   - `assistant.prompt.json`

3. **Local Caching**  
   - Save each bundle to disk under hash-named folder.  
   - Validate checksums; on mismatch, retry download.

4. **Scene Setup**  
   - Instantiate **SceneLoader** to read `scene.flow.json`.  
   - Load Location → Exhibition → Spaces in order.

5. **Injection Phase**  
   - **BlueprintInjector** attaches behavior scripts.  
   - **AssetManager** binds meshes, materials, LODs.  
   - **LLMBridge** injects AI prompts and voice config.

6. **Startup Hook**  
   - Call `InitBootstrap()` on VR startup.  
   - On error: fallback to last-known-good config and log.

## Failure Handling
- Missing asset → fallback prefab  
- Invalid JSON → use safe defaults  
- Network down → use cached data, queue update

## Runtime Considerations
- Differential updates (only changed bundles)  
- Background downloading for non-critical assets  
- Version gating for breaking changes  
