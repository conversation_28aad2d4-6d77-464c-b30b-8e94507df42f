# Kanousei VR Platform – Phase 1 Spec

## Overview: Feature Summary

The Kanousei VR Platform is a modular, AI-driven immersive system built to enable vendors and admins to collaboratively create, configure, and deploy dynamic 3D environments with ease. It uses a plugin-based architecture to auto-configure environments from server-managed data.

This Phase 1 specification outlines all foundational capabilities required to support a fully extensible vendor-driven showroom experience including:

* Dynamic scene configuration via server-side plugin
* Modular asset management (regular vs interactive)
* Visual, AI, and behavioral control per vendor
* AI assistant customization and session-based learning
* Integrated reporting, QA tools, and asset flow logic

---

## Phase 1 Objectives

1. Establish a **plugin installer system** that supports full scene download, setup, and configuration from the server.
2. Define **scene flow**, space logic, and room types (location → exhibition center → spaces).
3. Implement **interactive object behavior tagging** and procedural asset generation.
4. Allow **vendor-level content control** via a web portal (layout, AI, asset uploads).
5. Provide **admin-level global controls** for material, lighting, voice assistant, and scene approval.
6. Integrate a real-time **reporting and analytics dashboard**.
7. Support immersive features such as **spatial audio**, **light-switch dependencies**, and **live avatar assistance**.

---

## Role Matrix (Admin vs Vendor)

| Feature                      | Admin Access      | Vendor Access     | Notes                               |
| ---------------------------- | ----------------- | ----------------- | ----------------------------------- |
| Plugin Flow Configuration    | ✅ Full            | ❌ None            | Server-controlled only              |
| Layout Editing               | ✅ All             | ✅ Own Store       | Per store or space                  |
| Material Assignment          | ✅ Global          | ✅ Presets         | Vendors can't edit master materials |
| AI Assistant Customization   | ✅ Core Prompts    | ✅ Persona & Voice | Prompt preview via panel            |
| Interactive Object Tagging   | ✅ Full            | ✅ Guided Tool     | Tags mapped to blueprints           |
| Asset Upload                 | ✅ Review          | ✅ Submit          | Goes through QA pipeline            |
| Light & Switch Linking       | ✅ Global          | ✅ Room-level      | Must assign via config UI           |
| Procedural Asset Builder     | ✅ Approve         | ✅ Request & Edit  | Human-in-the-loop supported         |
| Reporting & Analytics        | ✅ Global + Vendor | ✅ Own Store       | With export options                 |
| Review Mode / QA Environment | ✅ Access All      | ✅ Access Own      | Separate from active scene          |
| AI Prompt Preview            | ✅ All             | ✅ Store Assistant | Consent and visibility toggles      |

---

Next: System Architecture → `plugin_bootstrap_flow.md`
