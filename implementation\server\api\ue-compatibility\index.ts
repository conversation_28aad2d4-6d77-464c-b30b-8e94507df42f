/**
 * UE Compatibility Layer
 *
 * This module provides a compatibility layer for Unreal Engine 5.4+ integration.
 * It ensures that all API endpoints maintain backward compatibility while
 * enabling new features available in UE 5.4+.
 */

import { Router } from 'express';
import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '../../shared/utils/logger';
import { z } from 'zod';

// Import UE compatibility utilities
import {
  UEVersionSchema,
  UECompatibilityCheckSchema,
  getCompatibilityDetails,
  getRequiredFeatures,
  getDeprecatedFeatures,
} from '../../shared/utils/ue-compatibility-utils';

// Import UE error handler
import { ueErrorHandler } from '../../shared/utils/ue-error-handler';

// Create router
const router = Router();

/**
 * Check API compatibility with UE version
 *
 * @param req Request object
 * @param res Response object
 */
function checkCompatibility(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = UECompatibilityCheckSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract UE version
    const { ue_version, api_features } = bodyResult.data;

    // Log the request
    logger.info('UE compatibility check', {
      ue_version,
      api_features,
    });

    // Get compatibility details
    const compatibilityDetails = getCompatibilityDetails(ue_version, api_features);

    // Return compatibility information
    return res.status(200).json({
      compatible: true,
      details: compatibilityDetails,
      required_features: getRequiredFeatures(ue_version),
      deprecated_features: getDeprecatedFeatures(ue_version),
    });
  } catch (error) {
    return ueErrorHandler(error, req, res);
  }
}

/**
 * Get compatibility details for UE version
 *
 * @param version UE version
 * @param features Requested API features
 * @returns Compatibility details
 */
function getCompatibilityDetails(
  version: z.infer<typeof UEVersionSchema>,
  features?: string[],
): {
  version_support: string;
  limitations: string[];
  unsupported_features: string[];
} {
  const details = {
    version_support: 'full',
    limitations: [] as string[],
    unsupported_features: [] as string[],
  };

  // Check for UE 4.x limitations
  if (version.major === 4) {
    details.version_support = 'limited';
    details.limitations.push('Limited blueprint injection support');
    details.limitations.push('Limited scene template support');
    details.limitations.push('No differential asset updates');

    // Check for specific features
    if (features?.includes('blueprint_injection')) {
      details.unsupported_features.push('Advanced blueprint injection');
    }

    if (features?.includes('scene_templates')) {
      details.unsupported_features.push('Scene templates');
    }

    if (features?.includes('differential_updates')) {
      details.unsupported_features.push('Differential asset updates');
    }
  }

  // Check for UE 5.0-5.3 limitations
  if (version.major === 5 && version.minor < 4) {
    details.version_support = 'partial';

    // Check for specific features
    if (features?.includes('blueprint_injection_5_4')) {
      details.unsupported_features.push('UE 5.4 blueprint injection enhancements');
    }

    if (features?.includes('llm_tool_usage')) {
      details.unsupported_features.push('LLM tool usage');
    }
  }

  return details;
}

/**
 * Get required features for UE version
 *
 * @param version UE version
 * @returns Required features
 */
function getRequiredFeatures(version: z.infer<typeof UEVersionSchema>): string[] {
  const features: string[] = [];

  // UE 5.4+ features
  if (version.major === 5 && version.minor >= 4) {
    features.push('api_versioning');
    features.push('blueprint_validation');
    features.push('asset_versioning');
    features.push('scene_validation');
  }

  return features;
}

/**
 * Get deprecated features for UE version
 *
 * @param version UE version
 * @returns Deprecated features
 */
function getDeprecatedFeatures(version: z.infer<typeof UEVersionSchema>): string[] {
  const features: string[] = [];

  // UE 5.4+ deprecated features
  if (version.major === 5 && version.minor >= 4) {
    features.push('legacy_asset_loading');
    features.push('legacy_blueprint_format');
  }

  return features;
}

// Register routes
router.post('/check', checkCompatibility);

// Export router
export default router;
