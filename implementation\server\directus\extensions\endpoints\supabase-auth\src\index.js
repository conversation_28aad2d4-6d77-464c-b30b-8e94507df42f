/**
 * Supabase Authentication Integration for Directus
 *
 * This endpoint provides authentication integration between Directus and Supabase.
 * It allows users to authenticate with Supabase credentials and receive tokens for both systems.
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');

module.exports = function registerEndpoint(
  router,
  { services, exceptions, database, getSchema, logger, env },
) {
  const { UsersService, RolesService } = services;
  const { ServiceUnavailableException, InvalidCredentialsException, InvalidPayloadException } =
    exceptions;

  // Configuration
  const supabaseUrl = env.NEXT_PUBLIC_SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;
  const directusSecret = env.SECRET || process.env.SECRET;

  if (!supabaseUrl || !supabaseServiceKey) {
    logger.warn(
      'Supabase credentials not set. Auth integration endpoints may not function correctly.',
    );
  }

  if (!directusSecret) {
    logger.warn('Directus secret not set. Auth integration endpoints may not function correctly.');
  }

  // Role mapping between Supabase and Directus
  const ROLE_MAPPING = {
    admin: 'System Admin',
    vendor_admin: 'Vendor Admin',
    vendor_manager: 'Vendor Manager',
    vendor_editor: 'Vendor Editor',
    vendor_analyst: 'Vendor Analyst',
    client_admin: 'Client Admin',
    client_designer: 'Client Designer',
    client_registered: 'Client Registered',
  };

  // Helper function to get Directus role ID from name
  async function getDirectusRoleId(roleName) {
    try {
      const rolesService = new RolesService({
        schema: await getSchema(),
      });

      const roles = await rolesService.readByQuery({
        filter: { name: { _eq: roleName } },
        limit: 1,
      });

      if (roles.length === 0) {
        throw new Error(`Role not found: ${roleName}`);
      }

      return roles[0].id;
    } catch (error) {
      logger.error(`Error getting Directus role ID: ${error.message}`);
      throw error;
    }
  }

  // Login endpoint
  router.post('/login', async (req, res, next) => {
    try {
      // Validate request
      if (!req.body.email || !req.body.password) {
        throw new InvalidPayloadException('Email and password are required');
      }

      const { email, password } = req.body;

      // Authenticate with Supabase
      try {
        // Call Supabase PostgreSQL function to verify password
        const pgClient = database.client;
        const { rows } = await pgClient.raw(
          `
          SELECT * FROM auth.verify_password(?, ?)
        `,
          [email, password],
        );

        if (rows.length === 0) {
          throw new InvalidCredentialsException('Invalid email or password');
        }

        const supabaseUser = rows[0];

        // Generate Supabase JWT token
        const { rows: tokenRows } = await pgClient.raw(
          `
          SELECT auth.generate_jwt(?)
        `,
          [supabaseUser.id],
        );

        const supabaseToken = tokenRows[0].generate_jwt;

        // Get user data from Supabase
        const { rows: userRows } = await pgClient.raw(
          `
          SELECT u.id, u.first_name, u.last_name, a.role, u.vendor_id
          FROM public.users u
          JOIN auth.users a ON u.id = a.id
          WHERE u.id = ?
        `,
          [supabaseUser.id],
        );

        const userData = userRows[0];

        // Find or create user in Directus
        const usersService = new UsersService({
          schema: await getSchema(),
        });

        const existingUsers = await usersService.readByQuery({
          filter: { email: { _eq: email } },
          limit: 1,
        });

        let directusUser;

        if (existingUsers.length === 0) {
          // Get Directus role ID
          const directusRoleName = ROLE_MAPPING[userData.role] || 'Client Registered';
          const roleId = await getDirectusRoleId(directusRoleName);

          // Create user in Directus
          directusUser = await usersService.createOne({
            email,
            password: password, // Will be hashed by Directus
            first_name: userData.first_name || '',
            last_name: userData.last_name || '',
            role: roleId,
            status: 'active',
            provider: 'supabase',
            external_identifier: userData.id,
          });
        } else {
          directusUser = existingUsers[0];
        }

        // Generate Directus token
        const directusToken = jwt.sign(
          {
            id: directusUser.id,
            role: directusUser.role,
            app_access: true,
            admin_access: directusUser.role === 'System Admin',
          },
          directusSecret,
          { expiresIn: '8h' },
        );

        // Return both tokens
        res.json({
          data: {
            supabase: {
              access_token: supabaseToken,
              user: {
                id: userData.id,
                email: email,
                first_name: userData.first_name,
                last_name: userData.last_name,
                role: userData.role,
                vendor_id: userData.vendor_id,
              },
            },
            directus: {
              access_token: directusToken,
              user: directusUser,
            },
          },
        });
      } catch (error) {
        if (error instanceof InvalidCredentialsException) {
          throw error;
        }
        logger.error(`Supabase authentication error: ${error.message}`);
        throw new ServiceUnavailableException('Authentication failed');
      }
    } catch (error) {
      next(error);
    }
  });

  return router;
};
