/**
 * Business Continuity API Routes
 * 
 * API endpoints for business continuity monitoring, recovery planning,
 * and dashboard functionality.
 */

const express = require('express');
const { BusinessServiceMonitor } = require('../../services/continuity/business-service-monitor');
const { BusinessRecoveryPrioritizer } = require('../../services/continuity/business-recovery-prioritizer');
const { BusinessContinuityDashboard } = require('../../services/continuity/business-continuity-dashboard');
const { runBusinessContinuityTests } = require('../../tests/business-continuity-test-framework');

const logger = require('../../utils/logger').getLogger('business-continuity-api');

const router = express.Router();

// Initialize business continuity services
const serviceMonitor = new BusinessServiceMonitor({
  configPath: process.env.BUSINESS_SERVICES_CONFIG_PATH,
  outputPath: process.env.SERVICE_MONITORING_OUTPUT_PATH,
  checkInterval: parseInt(process.env.SERVICE_CHECK_INTERVAL || '60000', 10),
  healthTimeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT || '10000', 10)
});

const recoveryPrioritizer = new BusinessRecoveryPrioritizer({
  configPath: process.env.RECOVERY_PRIORITIES_CONFIG_PATH,
  outputPath: process.env.RECOVERY_PLANS_OUTPUT_PATH
});

const dashboard = new BusinessContinuityDashboard({
  outputPath: process.env.DASHBOARD_OUTPUT_PATH,
  reportPath: process.env.REPORTS_OUTPUT_PATH,
  refreshInterval: parseInt(process.env.DASHBOARD_REFRESH_INTERVAL || '30000', 10)
});

// Set up service references
dashboard.setServices({
  serviceMonitor,
  recoveryPrioritizer,
  alertManager: null // Will be set when alert manager is available
});

// Initialize services
Promise.all([
  serviceMonitor.initialize(),
  recoveryPrioritizer.initialize(),
  dashboard.initialize()
]).then(() => {
  // Start monitoring
  serviceMonitor.startMonitoring();
  logger.info('Business continuity services initialized and monitoring started');
}).catch(error => {
  logger.error('Failed to initialize business continuity services:', error);
});

/**
 * @swagger
 * /api/business-continuity/status:
 *   get:
 *     summary: Get overall business continuity status
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: Business continuity status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 systemStatus:
 *                   type: string
 *                   enum: [operational, degraded, outage]
 *                 overallHealth:
 *                   type: number
 *                 servicesTotal:
 *                   type: number
 *                 servicesHealthy:
 *                   type: number
 *                 activeIncidents:
 *                   type: number
 */
router.get('/status', async (req, res) => {
  try {
    const statusData = await dashboard.getStatusOverview();
    
    res.json({
      success: true,
      data: statusData
    });
  } catch (error) {
    logger.error('Error getting business continuity status:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STATUS_FAILED',
        message: 'Failed to get business continuity status'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/services:
 *   get:
 *     summary: Get all monitored services
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: List of monitored services
 */
router.get('/services', (req, res) => {
  try {
    const serviceHealth = serviceMonitor.getServiceHealth();
    
    res.json({
      success: true,
      data: {
        services: Object.values(serviceHealth),
        statistics: serviceMonitor.getStatistics()
      }
    });
  } catch (error) {
    logger.error('Error getting services:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVICES_FAILED',
        message: 'Failed to get services'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/services/{serviceId}:
 *   get:
 *     summary: Get specific service details
 *     tags: [Business Continuity]
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: Service ID
 *     responses:
 *       200:
 *         description: Service details
 *       404:
 *         description: Service not found
 */
router.get('/services/:serviceId', (req, res) => {
  try {
    const { serviceId } = req.params;
    const serviceData = serviceMonitor.getServiceHealth(serviceId);
    
    if (!serviceData) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'SERVICE_NOT_FOUND',
          message: 'Service not found'
        }
      });
    }
    
    res.json({
      success: true,
      data: serviceData
    });
  } catch (error) {
    logger.error('Error getting service details:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVICE_DETAILS_FAILED',
        message: 'Failed to get service details'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/dependencies:
 *   get:
 *     summary: Get service dependency graph
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: Service dependency graph
 */
router.get('/dependencies', (req, res) => {
  try {
    const dependencyGraph = serviceMonitor.getDependencyGraph();
    
    res.json({
      success: true,
      data: dependencyGraph
    });
  } catch (error) {
    logger.error('Error getting dependency graph:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DEPENDENCIES_FAILED',
        message: 'Failed to get dependency graph'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/recovery/priorities:
 *   get:
 *     summary: Get recovery priorities for all services
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: Recovery priorities
 */
router.get('/recovery/priorities', (req, res) => {
  try {
    const priorities = recoveryPrioritizer.getRecoveryPriorities();
    
    res.json({
      success: true,
      data: {
        priorities,
        statistics: recoveryPrioritizer.getStatistics()
      }
    });
  } catch (error) {
    logger.error('Error getting recovery priorities:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PRIORITIES_FAILED',
        message: 'Failed to get recovery priorities'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/recovery/plan:
 *   post:
 *     summary: Generate recovery plan for affected services
 *     tags: [Business Continuity]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - affectedServices
 *             properties:
 *               affectedServices:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: List of affected service IDs
 *     responses:
 *       201:
 *         description: Recovery plan generated
 */
router.post('/recovery/plan', async (req, res) => {
  try {
    const { affectedServices } = req.body;
    
    if (!Array.isArray(affectedServices) || affectedServices.length === 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'affectedServices must be a non-empty array'
        }
      });
    }
    
    const plan = recoveryPrioritizer.generateRecoveryPlan(affectedServices);
    
    // Save the plan
    await recoveryPrioritizer.saveRecoveryPlan(plan);
    
    res.status(201).json({
      success: true,
      data: plan,
      message: 'Recovery plan generated successfully'
    });
  } catch (error) {
    logger.error('Error generating recovery plan:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PLAN_GENERATION_FAILED',
        message: 'Failed to generate recovery plan'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/recovery/plans:
 *   get:
 *     summary: Get all recovery plans
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: List of recovery plans
 */
router.get('/recovery/plans', (req, res) => {
  try {
    const plans = recoveryPrioritizer.getAllRecoveryPlans();
    
    res.json({
      success: true,
      data: {
        plans,
        count: plans.length
      }
    });
  } catch (error) {
    logger.error('Error getting recovery plans:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PLANS_FAILED',
        message: 'Failed to get recovery plans'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/recovery/plans/{planId}:
 *   get:
 *     summary: Get specific recovery plan
 *     tags: [Business Continuity]
 *     parameters:
 *       - in: path
 *         name: planId
 *         required: true
 *         schema:
 *           type: string
 *         description: Recovery plan ID
 *     responses:
 *       200:
 *         description: Recovery plan details
 *       404:
 *         description: Plan not found
 */
router.get('/recovery/plans/:planId', (req, res) => {
  try {
    const { planId } = req.params;
    const plan = recoveryPrioritizer.getRecoveryPlan(planId);
    
    if (!plan) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'PLAN_NOT_FOUND',
          message: 'Recovery plan not found'
        }
      });
    }
    
    res.json({
      success: true,
      data: plan
    });
  } catch (error) {
    logger.error('Error getting recovery plan:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'PLAN_DETAILS_FAILED',
        message: 'Failed to get recovery plan details'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/dashboard:
 *   get:
 *     summary: Get dashboard data
 *     tags: [Business Continuity]
 *     parameters:
 *       - in: query
 *         name: widget
 *         schema:
 *           type: string
 *         description: Specific widget type to retrieve
 *     responses:
 *       200:
 *         description: Dashboard data
 */
router.get('/dashboard', (req, res) => {
  try {
    const { widget } = req.query;
    const dashboardData = dashboard.getDashboardData(widget);
    
    res.json({
      success: true,
      data: dashboardData
    });
  } catch (error) {
    logger.error('Error getting dashboard data:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DASHBOARD_FAILED',
        message: 'Failed to get dashboard data'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/kpis:
 *   get:
 *     summary: Get business continuity KPIs
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: Business continuity KPIs
 */
router.get('/kpis', (req, res) => {
  try {
    const kpis = dashboard.getKPIs();
    
    res.json({
      success: true,
      data: kpis
    });
  } catch (error) {
    logger.error('Error getting KPIs:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'KPIS_FAILED',
        message: 'Failed to get KPIs'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/reports:
 *   post:
 *     summary: Generate business continuity report
 *     tags: [Business Continuity]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [executive, operational, technical, compliance]
 *                 description: Type of report to generate
 *               period:
 *                 type: string
 *                 description: Report period (e.g., '24h', '7d', '30d')
 *     responses:
 *       201:
 *         description: Report generated
 */
router.post('/reports', async (req, res) => {
  try {
    const { type, period } = req.body;
    
    if (!type) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Report type is required'
        }
      });
    }
    
    const report = await dashboard.generateReport(type, { period });
    
    res.status(201).json({
      success: true,
      data: report,
      message: 'Report generated successfully'
    });
  } catch (error) {
    logger.error('Error generating report:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'REPORT_GENERATION_FAILED',
        message: 'Failed to generate report'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/reports:
 *   get:
 *     summary: Get all reports
 *     tags: [Business Continuity]
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by report type
 *     responses:
 *       200:
 *         description: List of reports
 */
router.get('/reports', (req, res) => {
  try {
    const { type } = req.query;
    const reports = dashboard.getReports(type);
    
    res.json({
      success: true,
      data: {
        reports,
        count: reports.length
      }
    });
  } catch (error) {
    logger.error('Error getting reports:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'REPORTS_FAILED',
        message: 'Failed to get reports'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/metrics:
 *   get:
 *     summary: Get business metrics
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: Business metrics
 */
router.get('/metrics', (req, res) => {
  try {
    const metrics = serviceMonitor.getBusinessMetrics();
    
    res.json({
      success: true,
      data: metrics
    });
  } catch (error) {
    logger.error('Error getting business metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'METRICS_FAILED',
        message: 'Failed to get business metrics'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/test:
 *   post:
 *     summary: Run business continuity tests
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: Test results
 */
router.post('/test', async (req, res) => {
  try {
    // Check if user has admin privileges
    if (!req.user || req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PRIVILEGES',
          message: 'Admin privileges required to run tests'
        }
      });
    }
    
    logger.info('Running business continuity tests');
    
    const results = await runBusinessContinuityTests();
    
    res.json({
      success: true,
      data: results,
      message: 'Business continuity tests completed'
    });
  } catch (error) {
    logger.error('Error running business continuity tests:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'TEST_FAILED',
        message: 'Failed to run business continuity tests'
      }
    });
  }
});

/**
 * @swagger
 * /api/business-continuity/health:
 *   get:
 *     summary: Get business continuity service health
 *     tags: [Business Continuity]
 *     responses:
 *       200:
 *         description: Service health status
 */
router.get('/health', (req, res) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      services: {
        serviceMonitor: {
          status: serviceMonitor.isMonitoring ? 'running' : 'stopped',
          statistics: serviceMonitor.getStatistics()
        },
        recoveryPrioritizer: {
          status: 'healthy',
          statistics: recoveryPrioritizer.getStatistics()
        },
        dashboard: {
          status: 'healthy',
          statistics: dashboard.getStatistics()
        }
      }
    };
    
    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    logger.error('Error getting health status:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Failed to get health status'
      }
    });
  }
});

// Export services for use in other modules
router.serviceMonitor = serviceMonitor;
router.recoveryPrioritizer = recoveryPrioritizer;
router.dashboard = dashboard;

module.exports = router;
