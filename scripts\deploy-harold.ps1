# Personalized deployment script for <PERSON>
# Pre-configured with your SSH credentials and server settings

param(
    [string]$ManifestPath = "",
    [switch]$DryRun,
    [switch]$Verbose,
    [switch]$Force,
    [switch]$SkipVerification,
    [switch]$Help
)

# Harold's configuration
$Config = @{
    SSHKey = "C:\Users\<USER>\mvs-vr"
    Username = "harold"
    ServerIP = "**************"
    Email = "<EMAIL>"
    SSHPassword = "vectorax"  # Note: Consider using SSH agent for security
}

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
}

function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Show-Help {
    @"
Personalized Deployment Script for Harold

This script is pre-configured with your credentials:
- SSH Key: $($Config.SSHKey)
- Username: $($Config.Username)
- Server: $($Config.ServerIP)
- Email: $($Config.Email)

Usage: .\deploy-harold.ps1 [OPTIONS]

OPTIONS:
    -ManifestPath <PATH>    Custom manifest file path
    -DryRun                 Show what would be done without executing
    -Verbose                Enable verbose output
    -Force                  Force transfer all files regardless of status
    -SkipVerification       Skip file verification after transfer
    -Help                   Show this help message

EXAMPLES:
    # Test deployment (dry run)
    .\deploy-harold.ps1 -DryRun -Verbose

    # Deploy with default manifest
    .\deploy-harold.ps1 -Verbose

    # Deploy with custom manifest
    .\deploy-harold.ps1 -ManifestPath "docker-exports\custom-manifest.json" -Verbose

    # Force deploy all files
    .\deploy-harold.ps1 -Force -Verbose

SECURITY NOTE:
    Your SSH key password is stored in this script for convenience.
    For production use, consider using SSH agent or removing the password
    and entering it manually when prompted.

"@
}

function Test-Configuration {
    Write-Info "Validating Harold's configuration..."
    
    # Check SSH key
    if (-not (Test-Path $Config.SSHKey)) {
        Write-Error "SSH key not found: $($Config.SSHKey)"
        Write-Error "Please ensure your SSH key is at the correct location."
        return $false
    }
    
    Write-Success "SSH key found: $($Config.SSHKey)"
    return $true
}

function Start-Deployment {
    Write-Host "========================================" -ForegroundColor $Colors.Magenta
    Write-Host "MVS-VR v2 Deployment for Harold" -ForegroundColor $Colors.Magenta
    Write-Host "========================================" -ForegroundColor $Colors.Magenta
    Write-Host ""
    
    Write-Info "Configuration:"
    Write-Info "  SSH Key: $($Config.SSHKey)"
    Write-Info "  Username: $($Config.Username)"
    Write-Info "  Server: $($Config.ServerIP)"
    Write-Info "  Email: $($Config.Email)"
    Write-Host ""
    
    # Validate configuration
    if (-not (Test-Configuration)) {
        exit 1
    }
    
    # Build parameters for main deployment script
    $deployParams = @{
        SSHKey = $Config.SSHKey
        Username = $Config.Username
        ServerIP = $Config.ServerIP
    }

    if ($ManifestPath) {
        $deployParams.ManifestPath = $ManifestPath
    }

    if ($DryRun) {
        $deployParams.DryRun = $true
    }

    if ($Verbose) {
        $deployParams.Verbose = $true
    }

    if ($Force) {
        $deployParams.Force = $true
    }

    if ($SkipVerification) {
        $deployParams.SkipVerification = $true
    }
    
    # Get script directory
    $ScriptDir = if ($MyInvocation.MyCommand.Path) {
        Split-Path -Parent $MyInvocation.MyCommand.Path
    } else {
        Join-Path $PWD.Path "scripts"
    }
    $MainScript = Join-Path $ScriptDir "smart-deploy-staging.ps1"
    
    if (-not (Test-Path $MainScript)) {
        Write-Error "Main deployment script not found: $MainScript"
        exit 1
    }
    
    Write-Info "Starting deployment with smart-deploy-staging.ps1..."
    Write-Host ""
    
    # Execute main deployment script
    try {
        & $MainScript @deployParams
        $exitCode = $LASTEXITCODE
        
        Write-Host ""
        if ($exitCode -eq 0) {
            Write-Success "Deployment completed successfully!"
        } else {
            Write-Error "Deployment failed with exit code: $exitCode"
        }
        
        exit $exitCode
    }
    catch {
        Write-Error "Failed to execute deployment script: $($_.Exception.Message)"
        exit 1
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Start-Deployment
