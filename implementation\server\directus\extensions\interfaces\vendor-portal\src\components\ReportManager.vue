<template>
  <div class="report-manager">
    <div class="report-manager-header">
      <h3>Custom Reports</h3>
      <div class="header-actions">
        <button class="btn btn-primary" @click="createNewReport">
          <i class="material-icons">add</i> New Report
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
      <span>Loading reports...</span>
    </div>

    <div v-else-if="creatingReport" class="report-builder-container">
      <CustomReportBuilder
        ref="reportBuilder"
        :vendorId="vendorId"
        @save="onReportSaved"
        @cancel="cancelReportCreation"
      />
    </div>

    <div v-else-if="savedReports.length === 0" class="no-reports">
      <div class="no-reports-content">
        <i class="material-icons">description</i>
        <h4>No Custom Reports Yet</h4>
        <p>Create your first custom report to gain deeper insights into your data.</p>
        <button class="btn btn-primary" @click="createNewReport">
          <i class="material-icons">add</i> Create New Report
        </button>
      </div>
    </div>

    <div v-else class="reports-list">
      <div class="reports-grid">
        <div
          v-for="report in savedReports"
          :key="report.id"
          class="report-card"
        >
          <div class="report-icon">
            <i class="material-icons">{{ getReportIcon(report) }}</i>
          </div>
          <div class="report-info">
            <div class="report-name">{{ report.name }}</div>
            <div class="report-description">{{ report.description || 'No description' }}</div>
            <div class="report-meta">
              <div class="report-date">
                <i class="material-icons">update</i>
                <span>{{ formatDate(report.updated_at) }}</span>
              </div>
              <div class="report-author">
                <i class="material-icons">person</i>
                <span>{{ report.created_by_user?.first_name || 'Unknown' }}</span>
              </div>
            </div>
          </div>
          <div class="report-actions">
            <button class="action-button" @click="runReport(report)">
              <i class="material-icons">play_arrow</i>
              <span>Run</span>
            </button>
            <button class="action-button" @click="editReport(report)">
              <i class="material-icons">edit</i>
              <span>Edit</span>
            </button>
            <button class="action-button" @click="duplicateReport(report)">
              <i class="material-icons">content_copy</i>
              <span>Duplicate</span>
            </button>
            <button class="action-button delete" @click="confirmDeleteReport(report)">
              <i class="material-icons">delete</i>
              <span>Delete</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <div v-if="showDeleteConfirmation" class="delete-confirmation-overlay">
      <div class="delete-confirmation-dialog">
        <h4>Delete Report</h4>
        <p>Are you sure you want to delete the report "{{ reportToDelete?.name }}"? This action cannot be undone.</p>
        <div class="dialog-actions">
          <button class="btn btn-secondary" @click="cancelDelete">Cancel</button>
          <button class="btn btn-danger" @click="deleteReport">Delete</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomReportBuilder from './CustomReportBuilder.vue';

export default {
  name: 'ReportManager',

  components: {
    CustomReportBuilder
  },

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      creatingReport: false,
      savedReports: [],
      showDeleteConfirmation: false,
      reportToDelete: null
    };
  },

  mounted() {
    // First try to load from local storage
    this.loadReportsFromStorage();

    // Then try to load from API
    if (this.savedReports.length === 0) {
      this.loadSavedReports();
    }
  },

  methods: {
    async loadSavedReports() {
      this.loading = true;

      try {
        // This will be replaced with actual API call
        // const response = await fetch(`/api/analytics/custom-reports?vendor_id=${this.vendorId}`);
        // const data = await response.json();

        // Mock data for now
        setTimeout(() => {
          this.savedReports = [
            {
              id: '1',
              name: 'Monthly Showroom Performance',
              description: 'Tracks showroom visits, engagement, and conversions on a monthly basis',
              created_at: '2023-06-15T10:30:00Z',
              updated_at: '2023-07-20T14:45:00Z',
              created_by_user: { first_name: 'John', last_name: 'Doe' },
              config: {
                dataSource: 'showrooms',
                metrics: ['visits', 'unique_visitors', 'avg_duration', 'conversion_rate'],
                filters: [],
                grouping: 'month'
              }
            },
            {
              id: '2',
              name: 'Product Engagement Analysis',
              description: 'Analyzes product views, interactions, and conversion rates',
              created_at: '2023-05-10T09:15:00Z',
              updated_at: '2023-07-18T11:20:00Z',
              created_by_user: { first_name: 'Jane', last_name: 'Smith' },
              config: {
                dataSource: 'products',
                metrics: ['views', 'interactions', 'avg_time', 'conversion_rate'],
                filters: [],
                grouping: 'category'
              }
            },
            {
              id: '3',
              name: 'Visitor Behavior Trends',
              description: 'Tracks visitor behavior patterns and engagement metrics',
              created_at: '2023-07-05T16:45:00Z',
              updated_at: '2023-07-15T13:30:00Z',
              created_by_user: { first_name: 'John', last_name: 'Doe' },
              config: {
                dataSource: 'visitors',
                metrics: ['sessions', 'avg_session_duration', 'bounce_rate', 'returning_visitors'],
                filters: [],
                grouping: 'week'
              }
            }
          ];

          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading saved reports:', error);
        this.loading = false;
      }
    },

    createNewReport() {
      this.creatingReport = true;
    },

    cancelReportCreation() {
      this.creatingReport = false;
    },

    onReportSaved(report) {
      // Generate ID if not provided
      if (!report.id) {
        report.id = Date.now().toString();
      }

      // Check if report already exists
      const existingIndex = this.savedReports.findIndex(r => r.id === report.id);

      if (existingIndex !== -1) {
        // Update existing report
        this.savedReports[existingIndex] = { ...report };
      } else {
        // Add new report
        this.savedReports.push(report);
      }

      // Sort reports by updated_at date
      this.savedReports.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));

      // Exit report creation mode
      this.creatingReport = false;

      // Save to local storage for persistence
      this.saveReportsToStorage();

      // Show success message
      this.$buefy.toast.open({
        message: `Report "${report.name}" saved successfully`,
        type: 'is-success'
      });
    },

    saveReportsToStorage() {
      try {
        localStorage.setItem(`reports_${this.vendorId}`, JSON.stringify(this.savedReports));
      } catch (error) {
        console.error('Error saving reports to storage:', error);
      }
    },

    loadReportsFromStorage() {
      try {
        const storedReports = localStorage.getItem(`reports_${this.vendorId}`);
        if (storedReports) {
          this.savedReports = JSON.parse(storedReports);
        }
      } catch (error) {
        console.error('Error loading reports from storage:', error);
      }
    },

    runReport(report) {
      // Create a new window or tab to run the report
      const reportConfig = encodeURIComponent(JSON.stringify(report.config));
      const reportUrl = `/analytics/reports/view?config=${reportConfig}&name=${encodeURIComponent(report.name)}`;

      window.open(reportUrl, '_blank');
    },

    editReport(report) {
      this.creatingReport = true;

      // Pass the report to the CustomReportBuilder component
      this.$nextTick(() => {
        const reportBuilder = this.$refs.reportBuilder;
        if (reportBuilder) {
          reportBuilder.loadReportConfig(report);
        }
      });
    },

    duplicateReport(report) {
      // Create a copy of the report with a new ID and name
      const duplicatedReport = {
        ...report,
        id: Date.now().toString(),
        name: `${report.name} (Copy)`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Add to saved reports
      this.savedReports.push(duplicatedReport);

      // Sort reports by updated_at date
      this.savedReports.sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));

      // Save to local storage
      this.saveReportsToStorage();

      // Show success message
      this.$buefy.toast.open({
        message: `Report "${report.name}" duplicated successfully`,
        type: 'is-success'
      });
    },

    confirmDeleteReport(report) {
      this.reportToDelete = report;
      this.showDeleteConfirmation = true;
    },

    cancelDelete() {
      this.reportToDelete = null;
      this.showDeleteConfirmation = false;
    },

    deleteReport() {
      // Remove the report from the list
      this.savedReports = this.savedReports.filter(r => r.id !== this.reportToDelete.id);

      // Save to local storage
      this.saveReportsToStorage();

      // Close the confirmation dialog
      this.cancelDelete();

      // Show success message
      this.$buefy.toast.open({
        message: `Report "${this.reportToDelete.name}" has been deleted`,
        type: 'is-success'
      });
    },

    getReportIcon(report) {
      const iconMap = {
        showrooms: 'store',
        products: 'inventory_2',
        visitors: 'people',
        conversions: 'shopping_cart'
      };

      return iconMap[report.config.dataSource] || 'assessment';
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  }
};
</script>

<style scoped>
.report-manager {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
  position: relative;
}

.report-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--background);
  border-top: 4px solid var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-reports {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
}

.no-reports-content {
  text-align: center;
  max-width: 400px;
}

.no-reports-content i {
  font-size: 64px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 20px;
}

.no-reports-content h4 {
  margin-bottom: 10px;
}

.no-reports-content p {
  margin-bottom: 20px;
  color: var(--theme--foreground-subdued);
}

.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.report-card {
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  transition: all 0.2s;
}

.report-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.report-icon {
  background-color: var(--theme--primary);
  color: white;
  padding: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.report-icon i {
  font-size: 32px;
}

.report-info {
  padding: 15px;
}

.report-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.report-description {
  color: var(--theme--foreground-subdued);
  margin-bottom: 10px;
  font-size: 14px;
  min-height: 40px;
}

.report-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.report-date,
.report-author {
  display: flex;
  align-items: center;
}

.report-date i,
.report-author i {
  font-size: 14px;
  margin-right: 5px;
}

.report-actions {
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid var(--theme--border-color);
}

.action-button {
  flex: 1;
  min-width: 25%;
  background: none;
  border: none;
  border-right: 1px solid var(--theme--border-color);
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button:last-child {
  border-right: none;
}

.action-button:hover {
  background-color: var(--theme--background-subdued);
}

.action-button.delete:hover {
  background-color: var(--theme--danger-background);
  color: var(--theme--danger);
}

.action-button i {
  margin-bottom: 5px;
}

.action-button span {
  font-size: 12px;
}

.delete-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delete-confirmation-dialog {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  width: 400px;
  max-width: 90%;
}

.delete-confirmation-dialog h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.report-builder-container {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  min-height: 600px;
}
</style>
