<template>
  <div class="report-table-view">
    <div class="table-controls">
      <div class="table-search">
        <i class="material-icons">search</i>
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Search in table..." 
          @input="onSearch"
        >
      </div>
      <div class="table-actions">
        <div class="page-size-selector">
          <label>Rows per page:</label>
          <select v-model="pageSize" @change="onPageSizeChange">
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
            <option value="100">100</option>
          </select>
        </div>
        <button class="btn btn-icon" @click="refreshData" title="Refresh">
          <i class="material-icons">refresh</i>
        </button>
        <button class="btn btn-icon" @click="toggleColumnSelector" title="Columns">
          <i class="material-icons">view_column</i>
        </button>
        <button class="btn btn-icon" @click="exportTable" title="Export">
          <i class="material-icons">download</i>
        </button>
      </div>
    </div>

    <div class="table-container">
      <table class="data-table">
        <thead>
          <tr>
            <th 
              v-for="column in visibleColumns" 
              :key="column.id"
              :class="{ 
                sortable: column.sortable, 
                'sort-asc': sortColumn === column.id && sortDirection === 'asc',
                'sort-desc': sortColumn === column.id && sortDirection === 'desc'
              }"
              @click="sortBy(column)"
            >
              <div class="th-content">
                <span>{{ column.name }}</span>
                <i v-if="column.sortable" class="material-icons sort-icon">
                  {{ getSortIcon(column.id) }}
                </i>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr 
            v-for="(row, rowIndex) in paginatedData" 
            :key="rowIndex"
            :class="{ 'selected': selectedRows.includes(rowIndex) }"
            @click="toggleRowSelection(rowIndex)"
          >
            <td 
              v-for="column in visibleColumns" 
              :key="column.id"
              :class="getCellClass(row, column)"
            >
              {{ formatCellValue(row[column.id], column) }}
            </td>
          </tr>
        </tbody>
      </table>

      <div v-if="filteredData.length === 0" class="no-data">
        <i class="material-icons">info</i>
        <p v-if="searchQuery">No results found for "{{ searchQuery }}"</p>
        <p v-else>No data available</p>
      </div>

      <div v-if="loading" class="loading-overlay">
        <div class="spinner"></div>
        <span>Loading data...</span>
      </div>
    </div>

    <div class="table-pagination" v-if="totalPages > 1">
      <button 
        class="btn btn-icon" 
        @click="goToPage(1)" 
        :disabled="currentPage === 1"
        title="First Page"
      >
        <i class="material-icons">first_page</i>
      </button>
      <button 
        class="btn btn-icon" 
        @click="goToPage(currentPage - 1)" 
        :disabled="currentPage === 1"
        title="Previous Page"
      >
        <i class="material-icons">chevron_left</i>
      </button>
      
      <div class="page-info">
        Page {{ currentPage }} of {{ totalPages }}
      </div>
      
      <button 
        class="btn btn-icon" 
        @click="goToPage(currentPage + 1)" 
        :disabled="currentPage === totalPages"
        title="Next Page"
      >
        <i class="material-icons">chevron_right</i>
      </button>
      <button 
        class="btn btn-icon" 
        @click="goToPage(totalPages)" 
        :disabled="currentPage === totalPages"
        title="Last Page"
      >
        <i class="material-icons">last_page</i>
      </button>
    </div>

    <div v-if="showColumnSelector" class="column-selector">
      <div class="column-selector-header">
        <h4>Table Columns</h4>
        <button class="btn btn-icon" @click="toggleColumnSelector">
          <i class="material-icons">close</i>
        </button>
      </div>
      <div class="column-selector-content">
        <div 
          v-for="column in allColumns" 
          :key="column.id"
          class="column-item"
        >
          <label :for="`column-${column.id}`" class="column-label">
            <input 
              type="checkbox" 
              :id="`column-${column.id}`" 
              v-model="column.visible"
              @change="updateVisibleColumns"
            >
            <span>{{ column.name }}</span>
          </label>
        </div>
      </div>
      <div class="column-selector-footer">
        <button class="btn btn-secondary" @click="resetColumns">Reset</button>
        <button class="btn btn-primary" @click="toggleColumnSelector">Apply</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReportTableView',

  props: {
    data: {
      type: Array,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      allColumns: this.initializeColumns(),
      visibleColumns: [],
      searchQuery: '',
      filteredData: [...this.data],
      selectedRows: [],
      currentPage: 1,
      pageSize: 25,
      sortColumn: null,
      sortDirection: 'asc',
      showColumnSelector: false
    };
  },

  computed: {
    totalPages() {
      return Math.ceil(this.filteredData.length / this.pageSize);
    },
    
    paginatedData() {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = startIndex + this.pageSize;
      return this.filteredData.slice(startIndex, endIndex);
    }
  },

  watch: {
    data: {
      handler(newData) {
        this.filteredData = [...newData];
        this.applySort();
        this.selectedRows = [];
        this.currentPage = 1;
      },
      deep: true
    },
    
    columns: {
      handler(newColumns) {
        this.allColumns = this.initializeColumns();
        this.updateVisibleColumns();
      },
      deep: true
    }
  },

  mounted() {
    this.updateVisibleColumns();
  },

  methods: {
    initializeColumns() {
      return this.columns.map(column => ({
        ...column,
        visible: column.visible !== false, // Default to visible if not specified
        sortable: column.sortable !== false // Default to sortable if not specified
      }));
    },
    
    updateVisibleColumns() {
      this.visibleColumns = this.allColumns.filter(column => column.visible);
    },
    
    onSearch() {
      if (!this.searchQuery) {
        this.filteredData = [...this.data];
      } else {
        const query = this.searchQuery.toLowerCase();
        this.filteredData = this.data.filter(row => {
          return this.visibleColumns.some(column => {
            const value = row[column.id];
            if (value === null || value === undefined) return false;
            return String(value).toLowerCase().includes(query);
          });
        });
      }
      
      this.currentPage = 1;
      this.applySort();
    },
    
    onPageSizeChange() {
      this.currentPage = 1;
    },
    
    goToPage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
      }
    },
    
    sortBy(column) {
      if (!column.sortable) return;
      
      if (this.sortColumn === column.id) {
        // Toggle direction if already sorting by this column
        this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
      } else {
        // Set new sort column and default to ascending
        this.sortColumn = column.id;
        this.sortDirection = 'asc';
      }
      
      this.applySort();
    },
    
    applySort() {
      if (!this.sortColumn) return;
      
      const column = this.allColumns.find(col => col.id === this.sortColumn);
      if (!column) return;
      
      const direction = this.sortDirection === 'asc' ? 1 : -1;
      
      this.filteredData.sort((a, b) => {
        const valueA = a[this.sortColumn];
        const valueB = b[this.sortColumn];
        
        // Handle null/undefined values
        if (valueA === null || valueA === undefined) return direction;
        if (valueB === null || valueB === undefined) return -direction;
        
        // Sort based on data type
        if (column.type === 'number') {
          return (Number(valueA) - Number(valueB)) * direction;
        } else if (column.type === 'date' || column.type === 'datetime') {
          return (new Date(valueA) - new Date(valueB)) * direction;
        } else {
          return String(valueA).localeCompare(String(valueB)) * direction;
        }
      });
    },
    
    getSortIcon(columnId) {
      if (this.sortColumn !== columnId) return 'unfold_more';
      return this.sortDirection === 'asc' ? 'arrow_upward' : 'arrow_downward';
    },
    
    formatCellValue(value, column) {
      if (value === null || value === undefined) return '-';
      
      if (column.formatter) {
        return column.formatter(value);
      }
      
      if (column.type === 'number') {
        const decimals = column.decimals !== undefined ? column.decimals : 2;
        return Number(value).toLocaleString(undefined, {
          minimumFractionDigits: decimals,
          maximumFractionDigits: decimals
        });
      } else if (column.type === 'date') {
        return new Date(value).toLocaleDateString();
      } else if (column.type === 'datetime') {
        return new Date(value).toLocaleString();
      } else if (column.type === 'boolean') {
        return value ? 'Yes' : 'No';
      }
      
      return value;
    },
    
    getCellClass(row, column) {
      const classes = [];
      
      // Add type-specific classes
      if (column.type) {
        classes.push(`cell-${column.type}`);
      }
      
      // Add conditional formatting classes
      if (column.conditionalFormatting && column.conditionalFormatting.enabled) {
        const value = row[column.id];
        const formatting = column.conditionalFormatting;
        
        if (formatting.condition === 'greater' && value > formatting.value1) {
          classes.push(`cell-${formatting.color}`);
        } else if (formatting.condition === 'less' && value < formatting.value1) {
          classes.push(`cell-${formatting.color}`);
        } else if (formatting.condition === 'equal' && value == formatting.value1) {
          classes.push(`cell-${formatting.color}`);
        } else if (formatting.condition === 'between' && value >= formatting.value1 && value <= formatting.value2) {
          classes.push(`cell-${formatting.color}`);
        }
      }
      
      return classes.join(' ');
    },
    
    toggleRowSelection(rowIndex) {
      const index = this.selectedRows.indexOf(rowIndex);
      if (index === -1) {
        this.selectedRows.push(rowIndex);
      } else {
        this.selectedRows.splice(index, 1);
      }
      
      this.$emit('row-select', this.selectedRows.map(i => this.paginatedData[i]));
    },
    
    refreshData() {
      this.$emit('refresh');
    },
    
    toggleColumnSelector() {
      this.showColumnSelector = !this.showColumnSelector;
    },
    
    resetColumns() {
      this.allColumns.forEach(column => {
        column.visible = true;
      });
      this.updateVisibleColumns();
    },
    
    exportTable() {
      this.$emit('export');
    }
  }
};
</script>

<style scoped>
.report-table-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.table-search {
  display: flex;
  align-items: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 5px 10px;
  width: 300px;
}

.table-search i {
  color: var(--theme--foreground-subdued);
  margin-right: 8px;
}

.table-search input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--theme--foreground);
  font-size: 14px;
  outline: none;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 5px;
}

.page-size-selector label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.page-size-selector select {
  padding: 5px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
}

.btn-icon:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  position: relative;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  position: sticky;
  top: 0;
  background-color: var(--theme--background-subdued);
  padding: 10px;
  text-align: left;
  font-weight: bold;
  border-bottom: 1px solid var(--theme--border-color);
  z-index: 1;
}

.data-table th.sortable {
  cursor: pointer;
}

.data-table th.sortable:hover {
  background-color: var(--theme--background-accent);
}

.th-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sort-icon {
  font-size: 16px;
  opacity: 0.5;
}

th.sort-asc .sort-icon,
th.sort-desc .sort-icon {
  opacity: 1;
  color: var(--theme--primary);
}

.data-table td {
  padding: 8px 10px;
  border-bottom: 1px solid var(--theme--border-color-subdued);
}

.data-table tr:hover {
  background-color: var(--theme--background-subdued);
}

.data-table tr.selected {
  background-color: var(--theme--primary-background);
}

.cell-number {
  text-align: right;
}

.cell-boolean {
  text-align: center;
}

.cell-green {
  color: #4caf50;
}

.cell-red {
  color: #f44336;
}

.cell-yellow {
  color: #ff9800;
}

.cell-blue {
  color: #2196f3;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 10px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--background);
  border-top: 4px solid var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  gap: 5px;
}

.page-info {
  margin: 0 10px;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.column-selector {
  position: absolute;
  top: 50px;
  right: 0;
  width: 300px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.column-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid var(--theme--border-color);
}

.column-selector-header h4 {
  margin: 0;
}

.column-selector-content {
  padding: 10px 15px;
  max-height: 300px;
  overflow-y: auto;
}

.column-item {
  margin-bottom: 8px;
}

.column-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.column-selector-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px 15px;
  border-top: 1px solid var(--theme--border-color);
}
</style>
