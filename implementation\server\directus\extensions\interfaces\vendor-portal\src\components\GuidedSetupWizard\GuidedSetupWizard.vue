<template>
  <div class="guided-setup-wizard">
    <wizard-container
      :title="title"
      :description="description"
      :steps="wizardSteps"
      :initial-step-index="initialStepIndex"
      :allow-step-navigation="allowStepNavigation"
      :show-back-button="showBackButton"
      :show-save-button="showSaveButton"
      :show-help-section="showHelpSection"
      :next-button-text="nextButtonText"
      :finish-button-text="finishButtonText"
      :storage-key="storageKey"
      @complete="handleWizardComplete"
      @save-progress="handleSaveProgress"
      @load-progress="handleLoadProgress"
      @analytics="handleAnalyticsEvent"
    />
    
    <div v-if="showExitButton" class="exit-button-container">
      <button class="exit-button" @click="handleExit">
        {{ exitButtonText }}
      </button>
    </div>
  </div>
</template>

<script>
import WizardContainer from './WizardContainer.vue';
import CompanyProfileStep from './steps/CompanyProfileStep.vue';
import UserAccountStep from './steps/UserAccountStep.vue';
import BrandingSetupStep from './steps/BrandingSetupStep.vue';
import ProductUploadStep from './steps/ProductUploadStep.vue';
import ShowroomConfigStep from './steps/ShowroomConfigStep.vue';
import CompletionStep from './steps/CompletionStep.vue';
import GuidedSetupService from '../../services/GuidedSetupService';

export default {
  name: 'GuidedSetupWizard',
  
  components: {
    WizardContainer
  },
  
  props: {
    title: {
      type: String,
      default: 'Vendor Onboarding Wizard'
    },
    description: {
      type: String,
      default: 'Complete the following steps to set up your vendor account'
    },
    initialStepIndex: {
      type: Number,
      default: 0
    },
    allowStepNavigation: {
      type: Boolean,
      default: true
    },
    showBackButton: {
      type: Boolean,
      default: true
    },
    showSaveButton: {
      type: Boolean,
      default: true
    },
    showHelpSection: {
      type: Boolean,
      default: true
    },
    showExitButton: {
      type: Boolean,
      default: true
    },
    nextButtonText: {
      type: String,
      default: 'Next'
    },
    finishButtonText: {
      type: String,
      default: 'Complete Setup'
    },
    exitButtonText: {
      type: String,
      default: 'Exit Setup'
    },
    storageKey: {
      type: String,
      default: 'vendor-onboarding-wizard'
    },
    vendorId: {
      type: String,
      default: null
    }
  },
  
  data() {
    return {
      wizardSteps: [
        {
          title: 'Company Profile',
          component: CompanyProfileStep,
          helpTitle: 'Setting Up Your Company Profile',
          helpContent: `
            <p>This step helps you set up your company's basic information that will be displayed to clients.</p>
            <ul>
              <li>Provide accurate company details for better client recognition</li>
              <li>Upload a high-resolution company logo (recommended size: 512x512px)</li>
              <li>Select the industry categories that best represent your business</li>
            </ul>
          `,
          videoTutorial: '/assets/tutorials/company-profile-setup.mp4'
        },
        {
          title: 'User Accounts',
          component: UserAccountStep,
          helpTitle: 'Setting Up User Accounts',
          helpContent: `
            <p>Configure user accounts for your team members who will access the platform.</p>
            <ul>
              <li>Set up your admin account with full access</li>
              <li>Add team members with appropriate roles</li>
              <li>Configure permissions for each user</li>
            </ul>
          `,
          videoTutorial: '/assets/tutorials/user-accounts-setup.mp4'
        },
        {
          title: 'Branding Setup',
          component: BrandingSetupStep,
          helpTitle: 'Setting Up Your Brand',
          helpContent: `
            <p>Configure your brand identity that will be displayed to clients.</p>
            <ul>
              <li>Choose brand colors that match your company identity</li>
              <li>Select fonts that represent your brand style</li>
              <li>Configure logo placement and visibility</li>
            </ul>
          `,
          videoTutorial: '/assets/tutorials/branding-setup.mp4'
        },
        {
          title: 'Product Upload',
          component: ProductUploadStep,
          helpTitle: 'Uploading Your Products',
          helpContent: `
            <p>Upload and configure your products for display in the virtual showroom.</p>
            <ul>
              <li>Upload product images and 3D models</li>
              <li>Configure product details and specifications</li>
              <li>Organize products into categories</li>
            </ul>
          `,
          videoTutorial: '/assets/tutorials/product-upload.mp4'
        },
        {
          title: 'Showroom Configuration',
          component: ShowroomConfigStep,
          helpTitle: 'Setting Up Your Virtual Showroom',
          helpContent: `
            <p>Configure how your virtual showroom looks and functions for clients.</p>
            <ul>
              <li>Choose a showroom layout template or create a custom one</li>
              <li>Arrange products within the showroom space</li>
              <li>Set up lighting, materials, and environment settings</li>
            </ul>
          `,
          videoTutorial: '/assets/tutorials/showroom-config.mp4'
        },
        {
          title: 'Completion',
          component: CompletionStep,
          helpTitle: 'Completing Setup',
          helpContent: `
            <p>Review your setup and complete the onboarding process.</p>
            <ul>
              <li>Verify all information is correct</li>
              <li>Review next steps for using the platform</li>
              <li>Get links to additional resources and support</li>
            </ul>
          `,
          videoTutorial: '/assets/tutorials/completion.mp4'
        }
      ],
      isLoading: false,
      error: null
    };
  },
  
  mounted() {
    // Load onboarding status when component is mounted
    if (this.vendorId) {
      this.loadOnboardingStatus();
    }
  },
  
  methods: {
    async handleWizardComplete(data) {
      this.isLoading = true;
      
      try {
        // Process the completed wizard data
        await GuidedSetupService.saveOnboardingStatus(this.vendorId, true, data);
        
        // Track wizard completion for analytics
        await this.trackWizardCompletion();
        
        // Emit completion event
        this.$emit('complete', {
          vendorId: this.vendorId,
          setupData: data,
          completedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error completing wizard:', error);
        this.error = 'Failed to save completion status. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },
    
    async handleSaveProgress(progressData) {
      this.isLoading = true;
      
      try {
        // Save progress to the database
        await GuidedSetupService.saveOnboardingStatus(this.vendorId, false, progressData);
        
        // Emit save progress event
        this.$emit('save-progress', {
          vendorId: this.vendorId,
          progressData,
          savedAt: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error saving progress:', error);
        this.error = 'Failed to save progress. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },
    
    async handleLoadProgress(progressData) {
      // Emit load progress event
      this.$emit('load-progress', {
        vendorId: this.vendorId,
        progressData,
        loadedAt: new Date().toISOString()
      });
    },
    
    handleAnalyticsEvent(eventData) {
      // Track analytics event
      this.trackAnalyticsEvent(eventData.type, eventData.data);
    },
    
    handleExit() {
      // Emit exit event
      this.$emit('exit');
    },
    
    async loadOnboardingStatus() {
      this.isLoading = true;
      
      try {
        const status = await GuidedSetupService.getOnboardingStatus(this.vendorId);
        
        if (status && status.progress_data) {
          // Parse progress data
          const progressData = typeof status.progress_data === 'string' 
            ? JSON.parse(status.progress_data) 
            : status.progress_data;
          
          // Emit load progress event
          this.$emit('load-progress', {
            vendorId: this.vendorId,
            progressData,
            loadedAt: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Error loading onboarding status:', error);
        this.error = 'Failed to load onboarding status. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },
    
    async trackAnalyticsEvent(eventType, eventData) {
      try {
        await GuidedSetupService.trackWizardAnalytics(this.vendorId, {
          eventType,
          eventData
        });
      } catch (error) {
        console.error('Error tracking analytics event:', error);
        // Don't show error to user for analytics failures
      }
    },
    
    async trackWizardCompletion() {
      try {
        await GuidedSetupService.trackWizardAnalytics(this.vendorId, {
          eventType: 'wizard_completed',
          eventData: {
            completedAt: new Date().toISOString()
          }
        });
      } catch (error) {
        console.error('Error tracking wizard completion:', error);
        // Don't show error to user for analytics failures
      }
    }
  }
};
</script>

<style scoped>
.guided-setup-wizard {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.exit-button-container {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
}

.exit-button {
  padding: 8px 16px;
  background-color: transparent;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.exit-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}
</style>
