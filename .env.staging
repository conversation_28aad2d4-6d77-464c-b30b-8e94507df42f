# MVS-VR v2 Staging Environment Configuration
# Configured for DigitalOcean deployment with remote Supabase

# Application
NODE_ENV=staging
PORT=3000
APP_URL=https://mvs.kanousai.com
API_URL=https://mvs.kanousai.com/api

# Supabase - Remote Staging Instance
SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyODI3MjMsImV4cCI6MjA1OTg1ODcyM30.v7jSHJ4sPwnPzuwbYTCGxkwyDD4N8Ei89awcuXhPqhc
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDI4MjcyNCwiZXhwIjoyMDU5ODU4NzI0fQ.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30

# Database - Using Supabase PostgreSQL
DATABASE_URL=************************************************************************************************/postgres
DB_HOST=aws-0-us-east-1.pooler.supabase.com
DB_PORT=6543
DB_NAME=postgres
DB_USER=postgres.hiyqiqbgiueyyvqoqhht
DB_PASSWORD=9elskdUeo@I!
SUPABASE_DB_PASSWORD=9elskdUeo@I!

# Redis - DigitalOcean Managed Redis or Docker
REDIS_URL=redis://:9elskdUeo@I!@redis:6379
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=9elskdUeo@I!

# Directus - CMS Configuration
DIRECTUS_URL=http://directus:8055
DIRECTUS_KEY=7f8a9b2c4d5e6f1a8b9c0d1e2f3a4b5c
DIRECTUS_SECRET=9c8b7a6f5e4d3c2b1a0f9e8d7c6b5a4f
DIRECTUS_DB=directus
DIRECTUS_ADMIN_EMAIL=<EMAIL>
DIRECTUS_ADMIN_PASSWORD=9elskdUeo@I!

# Authentication - Secure Keys for Staging
JWT_SECRET=3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c5b4a3f2e
SESSION_SECRET=8e7d6c5b4a3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d
ENCRYPTION_KEY=5a4b3c2d1e0f9a8b7c6d5e4f3a2b1c0d

# Storage - Supabase Storage
STORAGE_BUCKET=assets
STORAGE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co/storage/v1

# LLM Configuration
LLM_PROVIDER=openai
LLM_API_KEY=${OPENAI_API_KEY}
LLM_MODEL=gpt-3.5-turbo
LLM_FALLBACK_MODEL=gpt-3.5-turbo

# External APIs (Set these in DigitalOcean environment)
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=${SMTP_USER}
SMTP_PASSWORD=${SMTP_PASSWORD}
SMTP_FROM=<EMAIL>

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_METRICS=true
METRICS_PORT=9090

# Security Settings
CORS_ORIGIN=https://mvs.kanousai.com,https://admin.kanousai.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Asset Management
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov,avi,glb,gltf,fbx,obj

# Performance Settings
CACHE_TTL=3600
MAX_CONCURRENT_UPLOADS=5
CHUNK_SIZE=1MB

# Feature Flags
ENABLE_VISUAL_EDITORS=true
ENABLE_ANIMATION_EDITOR=true
ENABLE_VENDOR_PORTAL=true
ENABLE_ADMIN_PORTAL=true

# DigitalOcean Specific
DROPLET_SIZE=s-4vcpu-8gb
REGION=blr
DOMAIN=mvs.kanousai.com
SSL_EMAIL=<EMAIL>
DO_SERVER_IP=**************
DO_PRIVATE_IP=**********
DIGITALOCEAN_ACCESS_TOKEN=***********************************************************************
DO_PWD=vectorax