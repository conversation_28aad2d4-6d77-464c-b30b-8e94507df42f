<template>
  <div class="subscription-manager">
    <div class="header">
      <h2>Subscription Management</h2>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading subscription data...</span>
    </div>

    <div v-else class="subscription-content">
      <!-- Current Plan Section -->
      <div class="subscription-section">
        <h3>Current Plan</h3>
        <div class="current-plan-card" :class="'plan-' + subscription.plan.toLowerCase()">
          <div class="plan-header">
            <div class="plan-name">{{ subscription.plan }}</div>
            <div class="plan-status" :class="subscription.status">{{ subscription.status }}</div>
          </div>
          <div class="plan-details">
            <div class="plan-price">
              <span class="currency">$</span>
              <span class="amount">{{ subscription.price }}</span>
              <span class="period">/month</span>
            </div>
            <div class="plan-renewal">
              Next billing date: {{ formatDate(subscription.nextBillingDate) }}
            </div>
          </div>
          <div class="plan-features">
            <div class="feature-item" v-for="(feature, index) in subscription.features" :key="index">
              <i class="material-icons">check_circle</i>
              <span>{{ feature }}</span>
            </div>
          </div>
          <div class="plan-actions">
            <button class="btn btn-primary" @click="showUpgradeModal = true">
              Upgrade Plan
            </button>
            <button class="btn btn-secondary" @click="showCancelModal = true">
              Cancel Subscription
            </button>
          </div>
        </div>
      </div>

      <!-- Usage Metrics Section -->
      <div class="subscription-section">
        <div class="section-header">
          <h3>Usage Metrics</h3>
          <div class="section-actions">
            <div class="date-range">
              <select v-model="usageTimeframe" @change="loadUsageHistory">
                <option value="7days">Last 7 Days</option>
                <option value="30days">Last 30 Days</option>
                <option value="90days">Last 90 Days</option>
                <option value="12months">Last 12 Months</option>
              </select>
            </div>
            <button class="btn btn-icon" @click="loadUsageHistory(true)">
              <i class="material-icons">refresh</i>
            </button>
          </div>
        </div>
        <div class="usage-metrics">
          <div class="metric-card" v-for="(metric, key) in usageMetrics" :key="key">
            <div class="metric-header">
              <h4>{{ metric.name }}</h4>
              <div class="metric-info" @click="showMetricInfo(metric)">
                <i class="material-icons">info</i>
              </div>
            </div>
            <div class="metric-value">
              <span class="current">{{ metric.current }}</span>
              <span class="separator">/</span>
              <span class="limit">{{ metric.limit }}</span>
            </div>
            <div class="metric-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: calculatePercentage(metric.current, metric.limit) + '%' }"
                  :class="{ 'warning': calculatePercentage(metric.current, metric.limit) > 80 }"
                ></div>
              </div>
              <div class="progress-text">
                {{ calculatePercentage(metric.current, metric.limit) }}% used
              </div>
            </div>
            <div class="metric-actions">
              <button class="btn btn-text btn-sm" @click="viewUsageHistory(key)">
                View History
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Usage History Section -->
      <div v-if="showUsageHistory" class="subscription-section">
        <div class="section-header">
          <h3>Usage History: {{ selectedUsageMetric.name }}</h3>
          <div class="section-actions">
            <button class="btn btn-icon" @click="showUsageHistory = false">
              <i class="material-icons">close</i>
            </button>
          </div>
        </div>
        <div class="usage-history">
          <div class="chart-container">
            <canvas ref="usageHistoryChart"></canvas>
          </div>
          <div class="usage-stats">
            <div class="stat-card">
              <div class="stat-title">Average Usage</div>
              <div class="stat-value">{{ selectedUsageMetric.averageUsage }}</div>
              <div class="stat-trend" :class="selectedUsageMetric.trend">
                <i class="material-icons">{{ getTrendIcon(selectedUsageMetric.trend) }}</i>
                <span>{{ selectedUsageMetric.change }}</span>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Peak Usage</div>
              <div class="stat-value">{{ selectedUsageMetric.peakUsage }}</div>
              <div class="stat-subtitle">on {{ formatDate(selectedUsageMetric.peakDate) }}</div>
            </div>
            <div class="stat-card">
              <div class="stat-title">Projected Usage</div>
              <div class="stat-value">{{ selectedUsageMetric.projectedUsage }}</div>
              <div class="stat-subtitle">by {{ formatDate(selectedUsageMetric.projectionDate) }}</div>
            </div>
          </div>
          <div class="usage-recommendations">
            <h4>Recommendations</h4>
            <ul class="recommendation-list">
              <li v-for="(recommendation, index) in selectedUsageMetric.recommendations" :key="index">
                <i class="material-icons">lightbulb</i>
                <span>{{ recommendation }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Billing History Section -->
      <div class="subscription-section">
        <h3>Billing History</h3>
        <div class="billing-history">
          <table class="billing-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Description</th>
                <th>Amount</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="billingHistory.length === 0">
                <td colspan="5" class="empty-state">No billing history available</td>
              </tr>
              <tr v-for="(invoice, index) in billingHistory" :key="index">
                <td>{{ formatDate(invoice.date) }}</td>
                <td>{{ invoice.description }}</td>
                <td>${{ invoice.amount.toFixed(2) }}</td>
                <td>
                  <span class="invoice-status" :class="invoice.status.toLowerCase()">
                    {{ invoice.status }}
                  </span>
                </td>
                <td>
                  <button class="btn btn-icon" @click="viewInvoice(invoice)">
                    <i class="material-icons">receipt</i>
                  </button>
                  <button v-if="invoice.status === 'Paid'" class="btn btn-icon" @click="downloadInvoice(invoice)">
                    <i class="material-icons">download</i>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Subscription Analytics Section -->
      <div class="subscription-section">
        <div class="section-header">
          <h3>Subscription Analytics</h3>
          <div class="section-actions">
            <div class="date-range">
              <select v-model="analyticsTimeframe" @change="loadSubscriptionAnalytics">
                <option value="6months">Last 6 Months</option>
                <option value="12months">Last 12 Months</option>
                <option value="24months">Last 24 Months</option>
              </select>
            </div>
            <button class="btn btn-icon" @click="loadSubscriptionAnalytics(true)">
              <i class="material-icons">refresh</i>
            </button>
          </div>
        </div>
        <div class="analytics-content">
          <div class="analytics-charts">
            <div class="chart-container">
              <h4>Cost History</h4>
              <canvas ref="costHistoryChart"></canvas>
            </div>
            <div class="chart-container">
              <h4>Usage Efficiency</h4>
              <canvas ref="usageEfficiencyChart"></canvas>
            </div>
          </div>
          <div class="analytics-metrics">
            <div class="metric-row">
              <div class="analytics-metric">
                <div class="metric-title">Total Spend</div>
                <div class="metric-value">${{ subscriptionAnalytics.totalSpend }}</div>
                <div class="metric-subtitle">{{ analyticsTimeframe === '6months' ? 'Last 6 Months' : analyticsTimeframe === '12months' ? 'Last 12 Months' : 'Last 24 Months' }}</div>
              </div>
              <div class="analytics-metric">
                <div class="metric-title">Average Monthly Cost</div>
                <div class="metric-value">${{ subscriptionAnalytics.averageMonthlyCost }}</div>
                <div class="metric-trend" :class="subscriptionAnalytics.costTrend">
                  <i class="material-icons">{{ getTrendIcon(subscriptionAnalytics.costTrend) }}</i>
                  <span>{{ subscriptionAnalytics.costChange }}</span>
                </div>
              </div>
              <div class="analytics-metric">
                <div class="metric-title">Cost Projection</div>
                <div class="metric-value">${{ subscriptionAnalytics.costProjection }}</div>
                <div class="metric-subtitle">Next 12 months</div>
              </div>
            </div>
            <div class="metric-row">
              <div class="analytics-metric">
                <div class="metric-title">Resource Utilization</div>
                <div class="metric-value">{{ subscriptionAnalytics.resourceUtilization }}%</div>
                <div class="metric-subtitle">Average across all resources</div>
              </div>
              <div class="analytics-metric">
                <div class="metric-title">Cost per Showroom</div>
                <div class="metric-value">${{ subscriptionAnalytics.costPerShowroom }}</div>
                <div class="metric-trend" :class="subscriptionAnalytics.showroomCostTrend">
                  <i class="material-icons">{{ getTrendIcon(subscriptionAnalytics.showroomCostTrend) }}</i>
                  <span>{{ subscriptionAnalytics.showroomCostChange }}</span>
                </div>
              </div>
              <div class="analytics-metric">
                <div class="metric-title">Cost per Client</div>
                <div class="metric-value">${{ subscriptionAnalytics.costPerClient }}</div>
                <div class="metric-trend" :class="subscriptionAnalytics.clientCostTrend">
                  <i class="material-icons">{{ getTrendIcon(subscriptionAnalytics.clientCostTrend) }}</i>
                  <span>{{ subscriptionAnalytics.clientCostChange }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="cost-optimization">
            <h4>Cost Optimization Recommendations</h4>
            <ul class="recommendation-list">
              <li v-for="(recommendation, index) in subscriptionAnalytics.recommendations" :key="index">
                <i class="material-icons">savings</i>
                <div class="recommendation-content">
                  <div class="recommendation-title">{{ recommendation.title }}</div>
                  <div class="recommendation-description">{{ recommendation.description }}</div>
                  <div class="recommendation-savings">Potential savings: <strong>${{ recommendation.potentialSavings }}</strong></div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Payment Methods Section -->
      <div class="subscription-section">
        <h3>Payment Methods</h3>
        <div class="payment-methods">
          <div v-if="paymentMethods.length === 0" class="empty-state">
            No payment methods available
          </div>
          <div v-else class="payment-method-list">
            <div
              v-for="(method, index) in paymentMethods"
              :key="index"
              class="payment-method-card"
              :class="{ 'default': method.isDefault }"
            >
              <div class="payment-method-icon">
                <i class="material-icons">{{ getPaymentIcon(method.type) }}</i>
              </div>
              <div class="payment-method-details">
                <div class="payment-method-name">
                  {{ method.type }} •••• {{ method.lastFour }}
                </div>
                <div class="payment-method-expiry">
                  Expires: {{ method.expiryMonth }}/{{ method.expiryYear }}
                </div>
                <div v-if="method.isDefault" class="payment-method-default">
                  Default payment method
                </div>
              </div>
              <div class="payment-method-actions">
                <button
                  v-if="!method.isDefault"
                  class="btn btn-secondary btn-sm"
                  @click="setDefaultPaymentMethod(method)"
                >
                  Set as Default
                </button>
                <button class="btn btn-icon" @click="removePaymentMethod(method)">
                  <i class="material-icons">delete</i>
                </button>
              </div>
            </div>
          </div>
          <div class="payment-method-add">
            <button class="btn btn-primary" @click="showAddPaymentModal = true">
              <i class="material-icons">add</i> Add Payment Method
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Upgrade Plan Modal -->
    <div v-if="showUpgradeModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Upgrade Subscription</h3>
          <button class="btn btn-icon" @click="showUpgradeModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="plan-selection">
            <div
              v-for="(plan, index) in availablePlans"
              :key="index"
              class="plan-option"
              :class="{
                'selected': selectedPlan === plan.id,
                'current': plan.id === subscription.planId
              }"
              @click="selectedPlan = plan.id"
            >
              <div class="plan-option-header">
                <div class="plan-option-name">{{ plan.name }}</div>
                <div v-if="plan.id === subscription.planId" class="plan-option-current">Current Plan</div>
              </div>
              <div class="plan-option-price">
                <span class="currency">$</span>
                <span class="amount">{{ plan.price }}</span>
                <span class="period">/month</span>
              </div>
              <div class="plan-option-features">
                <div class="feature-item" v-for="(feature, featureIndex) in plan.features" :key="featureIndex">
                  <i class="material-icons">check_circle</i>
                  <span>{{ feature }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showUpgradeModal = false">Cancel</button>
          <button
            class="btn btn-primary"
            @click="upgradePlan"
            :disabled="!selectedPlan || selectedPlan === subscription.planId"
          >
            Confirm Upgrade
          </button>
        </div>
      </div>
    </div>

    <!-- Cancel Subscription Modal -->
    <div v-if="showCancelModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Cancel Subscription</h3>
          <button class="btn btn-icon" @click="showCancelModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <p class="warning">Are you sure you want to cancel your subscription?</p>
          <p>Your subscription will remain active until the end of the current billing period on {{ formatDate(subscription.nextBillingDate) }}.</p>
          <div class="form-group">
            <label for="cancel-reason">Reason for cancellation (optional)</label>
            <select id="cancel-reason" v-model="cancelReason">
              <option value="">Select a reason</option>
              <option value="too_expensive">Too expensive</option>
              <option value="missing_features">Missing features</option>
              <option value="switching_service">Switching to another service</option>
              <option value="not_using">Not using the service enough</option>
              <option value="other">Other</option>
            </select>
          </div>
          <div v-if="cancelReason === 'other'" class="form-group">
            <label for="cancel-reason-other">Please specify</label>
            <textarea
              id="cancel-reason-other"
              v-model="cancelReasonOther"
              placeholder="Please tell us why you're cancelling"
              rows="3"
            ></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showCancelModal = false">Keep Subscription</button>
          <button class="btn btn-danger" @click="cancelSubscription">Confirm Cancellation</button>
        </div>
      </div>
    </div>

    <!-- Add Payment Method Modal -->
    <div v-if="showAddPaymentModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Add Payment Method</h3>
          <button class="btn btn-icon" @click="showAddPaymentModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="card-number">Card Number</label>
            <input
              id="card-number"
              type="text"
              v-model="newPaymentMethod.cardNumber"
              placeholder="1234 5678 9012 3456"
              maxlength="19"
              @input="formatCardNumber"
            />
          </div>
          <div class="form-row">
            <div class="form-group half">
              <label for="expiry-date">Expiry Date</label>
              <input
                id="expiry-date"
                type="text"
                v-model="newPaymentMethod.expiryDate"
                placeholder="MM/YY"
                maxlength="5"
                @input="formatExpiryDate"
              />
            </div>
            <div class="form-group half">
              <label for="cvv">CVV</label>
              <input
                id="cvv"
                type="text"
                v-model="newPaymentMethod.cvv"
                placeholder="123"
                maxlength="4"
              />
            </div>
          </div>
          <div class="form-group">
            <label for="card-name">Name on Card</label>
            <input
              id="card-name"
              type="text"
              v-model="newPaymentMethod.cardName"
              placeholder="John Smith"
            />
          </div>
          <div class="form-group">
            <div class="checkbox">
              <input
                id="make-default"
                type="checkbox"
                v-model="newPaymentMethod.makeDefault"
              />
              <label for="make-default">Make this my default payment method</label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showAddPaymentModal = false">Cancel</button>
          <button
            class="btn btn-primary"
            @click="addPaymentMethod"
            :disabled="!isPaymentFormValid"
          >
            Add Payment Method
          </button>
        </div>
      </div>
    </div>

    <!-- Metric Info Modal -->
    <div v-if="showMetricInfoModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ selectedMetric.name }} Information</h3>
          <button class="btn btn-icon" @click="showMetricInfoModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <p>{{ selectedMetric.description }}</p>
          <div class="metric-details">
            <div class="metric-detail-item">
              <span class="label">Current Usage:</span>
              <span class="value">{{ selectedMetric.current }}</span>
            </div>
            <div class="metric-detail-item">
              <span class="label">Limit:</span>
              <span class="value">{{ selectedMetric.limit }}</span>
            </div>
            <div class="metric-detail-item">
              <span class="label">Reset Period:</span>
              <span class="value">{{ selectedMetric.resetPeriod }}</span>
            </div>
            <div class="metric-detail-item">
              <span class="label">Next Reset:</span>
              <span class="value">{{ formatDate(selectedMetric.nextReset) }}</span>
            </div>
          </div>
          <div class="metric-upgrade-info">
            <h4>Need more?</h4>
            <p>Upgrade your plan to increase your limits or contact support for custom options.</p>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showMetricInfoModal = false">Close</button>
          <button class="btn btn-primary" @click="showUpgradeFromMetric">Upgrade Plan</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SubscriptionManager',

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      subscription: null,
      usageMetrics: {},
      billingHistory: [],
      paymentMethods: [],
      showUpgradeModal: false,
      showCancelModal: false,
      showAddPaymentModal: false,
      showMetricInfoModal: false,
      selectedPlan: null,
      cancelReason: '',
      cancelReasonOther: '',
      selectedMetric: null,
      newPaymentMethod: {
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        cardName: '',
        makeDefault: false
      },
      availablePlans: [],
      // New data properties for usage tracking and analytics
      usageTimeframe: '30days',
      analyticsTimeframe: '12months',
      showUsageHistory: false,
      selectedUsageMetric: {
        name: '',
        data: [],
        labels: [],
        averageUsage: 0,
        peakUsage: 0,
        peakDate: null,
        projectedUsage: 0,
        projectionDate: null,
        trend: 'up',
        change: '+0%',
        recommendations: []
      },
      usageHistoryChart: null,
      costHistoryChart: null,
      usageEfficiencyChart: null,
      subscriptionAnalytics: {
        totalSpend: 0,
        averageMonthlyCost: 0,
        costProjection: 0,
        resourceUtilization: 0,
        costPerShowroom: 0,
        costPerClient: 0,
        costTrend: 'neutral',
        costChange: '0%',
        showroomCostTrend: 'neutral',
        showroomCostChange: '0%',
        clientCostTrend: 'neutral',
        clientCostChange: '0%',
        costHistory: {
          labels: [],
          data: []
        },
        usageEfficiency: {
          labels: [],
          data: []
        },
        recommendations: []
      }
    };
  },

  computed: {
    isPaymentFormValid() {
      return (
        this.newPaymentMethod.cardNumber.replace(/\s/g, '').length === 16 &&
        this.newPaymentMethod.expiryDate.length === 5 &&
        this.newPaymentMethod.cvv.length >= 3 &&
        this.newPaymentMethod.cardName.trim().length > 0
      );
    }
  },

  mounted() {
    this.loadSubscriptionData();
    this.loadSubscriptionAnalytics();
  },

  beforeDestroy() {
    // Destroy charts to prevent memory leaks
    if (this.usageHistoryChart) {
      this.usageHistoryChart.destroy();
    }
    if (this.costHistoryChart) {
      this.costHistoryChart.destroy();
    }
    if (this.usageEfficiencyChart) {
      this.usageEfficiencyChart.destroy();
    }
  },

  methods: {
    // Load subscription data from API
    async loadSubscriptionData() {
      this.loading = true;

      try {
        // This would be replaced with actual API calls
        // const subscriptionResponse = await axios.get(`/api/subscription?vendor_id=${this.vendorId}`);
        // const metricsResponse = await axios.get(`/api/subscription/metrics?vendor_id=${this.vendorId}`);
        // const billingResponse = await axios.get(`/api/subscription/billing?vendor_id=${this.vendorId}`);
        // const paymentResponse = await axios.get(`/api/subscription/payment-methods?vendor_id=${this.vendorId}`);
        // const plansResponse = await axios.get(`/api/subscription/plans`);

        // Mock data for demonstration
        setTimeout(() => {
          // Mock subscription data
          this.subscription = {
            id: 'sub_12345',
            planId: 'plan_professional',
            plan: 'Professional',
            status: 'Active',
            price: 49.99,
            startDate: '2023-01-15T00:00:00Z',
            nextBillingDate: '2023-06-15T00:00:00Z',
            features: [
              'Unlimited products',
              'Up to 10 team members',
              '50GB storage',
              'Priority support',
              'Advanced analytics'
            ]
          };

          // Mock usage metrics
          this.usageMetrics = {
            storage: {
              name: 'Storage',
              current: 25.7,
              limit: 50,
              unit: 'GB',
              description: 'Total storage used for all your products, assets, and data.',
              resetPeriod: 'Never',
              nextReset: null
            },
            api_calls: {
              name: 'API Calls',
              current: 8750,
              limit: 10000,
              unit: 'calls',
              description: 'Number of API calls made this month.',
              resetPeriod: 'Monthly',
              nextReset: '2023-06-01T00:00:00Z'
            },
            team_members: {
              name: 'Team Members',
              current: 6,
              limit: 10,
              unit: 'members',
              description: 'Number of team members with access to your account.',
              resetPeriod: 'Never',
              nextReset: null
            },
            showrooms: {
              name: 'Showrooms',
              current: 3,
              limit: 5,
              unit: 'showrooms',
              description: 'Number of active showrooms you can have at once.',
              resetPeriod: 'Never',
              nextReset: null
            }
          };

          // Mock billing history
          this.billingHistory = [
            {
              id: 'inv_12345',
              date: '2023-05-15T00:00:00Z',
              description: 'Professional Plan - Monthly Subscription',
              amount: 49.99,
              status: 'Paid'
            },
            {
              id: 'inv_12344',
              date: '2023-04-15T00:00:00Z',
              description: 'Professional Plan - Monthly Subscription',
              amount: 49.99,
              status: 'Paid'
            },
            {
              id: 'inv_12343',
              date: '2023-03-15T00:00:00Z',
              description: 'Professional Plan - Monthly Subscription',
              amount: 49.99,
              status: 'Paid'
            }
          ];

          // Mock payment methods
          this.paymentMethods = [
            {
              id: 'pm_12345',
              type: 'Visa',
              lastFour: '4242',
              expiryMonth: '12',
              expiryYear: '24',
              isDefault: true
            },
            {
              id: 'pm_12346',
              type: 'Mastercard',
              lastFour: '5555',
              expiryMonth: '10',
              expiryYear: '25',
              isDefault: false
            }
          ];

          // Mock available plans
          this.availablePlans = [
            {
              id: 'plan_starter',
              name: 'Starter',
              price: 19.99,
              features: [
                'Up to 50 products',
                'Up to 3 team members',
                '10GB storage',
                'Standard support',
                'Basic analytics'
              ]
            },
            {
              id: 'plan_professional',
              name: 'Professional',
              price: 49.99,
              features: [
                'Unlimited products',
                'Up to 10 team members',
                '50GB storage',
                'Priority support',
                'Advanced analytics'
              ]
            },
            {
              id: 'plan_enterprise',
              name: 'Enterprise',
              price: 99.99,
              features: [
                'Unlimited products',
                'Unlimited team members',
                '200GB storage',
                'Dedicated support',
                'Custom analytics',
                'White-label options'
              ]
            }
          ];

          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading subscription data:', error);
        this.loading = false;
      }
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    // Calculate percentage for progress bars
    calculatePercentage(current, limit) {
      if (!limit) return 0;
      const percentage = Math.round((current / limit) * 100);
      return Math.min(percentage, 100);
    },

    // Get icon for payment method type
    getPaymentIcon(type) {
      switch (type.toLowerCase()) {
        case 'visa':
          return 'credit_card';
        case 'mastercard':
          return 'credit_card';
        case 'amex':
          return 'credit_card';
        case 'discover':
          return 'credit_card';
        case 'paypal':
          return 'account_balance_wallet';
        default:
          return 'payment';
      }
    },

    // Show metric info modal
    showMetricInfo(metric) {
      this.selectedMetric = metric;
      this.showMetricInfoModal = true;
    },

    // Show upgrade modal from metric info
    showUpgradeFromMetric() {
      this.showMetricInfoModal = false;
      this.showUpgradeModal = true;
    },

    // Format card number with spaces
    formatCardNumber() {
      let value = this.newPaymentMethod.cardNumber.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
      let formattedValue = '';

      for (let i = 0; i < value.length; i++) {
        if (i > 0 && i % 4 === 0) {
          formattedValue += ' ';
        }
        formattedValue += value[i];
      }

      this.newPaymentMethod.cardNumber = formattedValue;
    },

    // Format expiry date with slash
    formatExpiryDate() {
      let value = this.newPaymentMethod.expiryDate.replace(/\D/g, '');

      if (value.length > 2) {
        this.newPaymentMethod.expiryDate = value.substring(0, 2) + '/' + value.substring(2);
      } else {
        this.newPaymentMethod.expiryDate = value;
      }
    },

    // View invoice details
    viewInvoice(invoice) {
      // This would open the invoice in a new tab or download it
      console.log('View invoice:', invoice.id);
    },

    // Download invoice
    downloadInvoice(invoice) {
      // This would download the invoice
      console.log('Download invoice:', invoice.id);
    },

    // Set default payment method
    setDefaultPaymentMethod(method) {
      // This would be replaced with actual API call
      // await axios.post(`/api/subscription/payment-methods/${method.id}/default`);

      // Mock update
      this.paymentMethods.forEach(m => {
        m.isDefault = m.id === method.id;
      });
    },

    // Remove payment method
    removePaymentMethod(method) {
      // This would be replaced with actual API call
      // await axios.delete(`/api/subscription/payment-methods/${method.id}`);

      // Mock removal
      this.paymentMethods = this.paymentMethods.filter(m => m.id !== method.id);
    },

    // Add new payment method
    addPaymentMethod() {
      // This would be replaced with actual API call
      // const response = await axios.post('/api/subscription/payment-methods', this.newPaymentMethod);

      // Mock addition
      const [month, year] = this.newPaymentMethod.expiryDate.split('/');
      const cardType = this.getCardType(this.newPaymentMethod.cardNumber);
      const lastFour = this.newPaymentMethod.cardNumber.replace(/\s/g, '').slice(-4);

      const newMethod = {
        id: `pm_${Date.now()}`,
        type: cardType,
        lastFour,
        expiryMonth: month,
        expiryYear: year,
        isDefault: this.newPaymentMethod.makeDefault
      };

      if (this.newPaymentMethod.makeDefault) {
        this.paymentMethods.forEach(m => {
          m.isDefault = false;
        });
      }

      this.paymentMethods.push(newMethod);

      // Reset form and close modal
      this.newPaymentMethod = {
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        cardName: '',
        makeDefault: false
      };

      this.showAddPaymentModal = false;
    },

    // Determine card type from number
    getCardType(cardNumber) {
      const number = cardNumber.replace(/\s+/g, '');

      if (/^4/.test(number)) return 'Visa';
      if (/^5[1-5]/.test(number)) return 'Mastercard';
      if (/^3[47]/.test(number)) return 'Amex';
      if (/^6(?:011|5)/.test(number)) return 'Discover';

      return 'Card';
    },

    // Upgrade subscription plan
    upgradePlan() {
      // This would be replaced with actual API call
      // await axios.post(`/api/subscription/upgrade`, { plan_id: this.selectedPlan });

      // Mock upgrade
      const newPlan = this.availablePlans.find(p => p.id === this.selectedPlan);

      if (newPlan) {
        this.subscription = {
          ...this.subscription,
          planId: newPlan.id,
          plan: newPlan.name,
          price: newPlan.price,
          features: [...newPlan.features]
        };
      }

      this.showUpgradeModal = false;
      this.selectedPlan = null;
    },

    // Cancel subscription
    cancelSubscription() {
      // This would be replaced with actual API call
      // await axios.post(`/api/subscription/cancel`, {
      //   reason: this.cancelReason,
      //   reason_details: this.cancelReasonOther
      // });

      // Mock cancellation
      this.subscription = {
        ...this.subscription,
        status: 'Cancelling'
      };

      this.showCancelModal = false;
      this.cancelReason = '';
      this.cancelReasonOther = '';
    },

    // Get trend icon based on trend direction
    getTrendIcon(trend) {
      switch (trend) {
        case 'up':
          return 'trending_up';
        case 'down':
          return 'trending_down';
        default:
          return 'trending_flat';
      }
    },

    // View usage history for a specific metric
    viewUsageHistory(metricKey) {
      const metric = this.usageMetrics[metricKey];

      if (!metric) return;

      this.selectedUsageMetric = {
        name: metric.name,
        data: [],
        labels: [],
        averageUsage: 0,
        peakUsage: 0,
        peakDate: null,
        projectedUsage: 0,
        projectionDate: null,
        trend: 'up',
        change: '+0%',
        recommendations: []
      };

      this.showUsageHistory = true;
      this.loadUsageHistory();
    },

    // Load usage history data
    async loadUsageHistory(forceRefresh = false) {
      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/subscription/usage-history/${metricKey}?timeframe=${this.usageTimeframe}`);

        // Mock data for demonstration
        setTimeout(() => {
          // Generate mock data based on the selected timeframe
          const labels = [];
          const data = [];
          let startDate;
          let dataPoints;

          switch (this.usageTimeframe) {
            case '7days':
              startDate = new Date();
              startDate.setDate(startDate.getDate() - 7);
              dataPoints = 7;
              break;
            case '30days':
              startDate = new Date();
              startDate.setDate(startDate.getDate() - 30);
              dataPoints = 30;
              break;
            case '90days':
              startDate = new Date();
              startDate.setDate(startDate.getDate() - 90);
              dataPoints = 15; // Every 6 days
              break;
            case '12months':
              startDate = new Date();
              startDate.setMonth(startDate.getMonth() - 12);
              dataPoints = 12; // Monthly
              break;
            default:
              startDate = new Date();
              startDate.setDate(startDate.getDate() - 30);
              dataPoints = 30;
          }

          // Generate data points
          const currentValue = this.selectedUsageMetric.name === 'Storage' ? 25.7 :
                              this.selectedUsageMetric.name === 'API Calls' ? 8750 :
                              this.selectedUsageMetric.name === 'Team Members' ? 6 : 3;

          const limit = this.selectedUsageMetric.name === 'Storage' ? 50 :
                       this.selectedUsageMetric.name === 'API Calls' ? 10000 :
                       this.selectedUsageMetric.name === 'Team Members' ? 10 : 5;

          const unit = this.selectedUsageMetric.name === 'Storage' ? 'GB' :
                      this.selectedUsageMetric.name === 'API Calls' ? 'calls' :
                      this.selectedUsageMetric.name === 'Team Members' ? 'members' : 'showrooms';

          // Generate slightly random data with an upward trend
          let total = 0;
          let peak = 0;
          let peakDate = null;

          for (let i = 0; i < dataPoints; i++) {
            const date = new Date(startDate);

            if (this.usageTimeframe === '12months') {
              date.setMonth(date.getMonth() + i);
            } else {
              const increment = this.usageTimeframe === '90days' ? 6 : 1;
              date.setDate(date.getDate() + (i * increment));
            }

            // Generate a value with some randomness but trending upward
            const randomFactor = Math.random() * 0.2 - 0.1; // -10% to +10%
            const trendFactor = i / dataPoints * 0.3; // 0% to 30% increase over time
            const value = currentValue * (0.7 + randomFactor + trendFactor);
            const roundedValue = this.selectedUsageMetric.name === 'Storage' ?
                                Math.round(value * 10) / 10 : // 1 decimal place for storage
                                Math.round(value); // Integer for others

            labels.push(this.formatDate(date));
            data.push(roundedValue);

            total += roundedValue;

            if (roundedValue > peak) {
              peak = roundedValue;
              peakDate = date;
            }
          }

          const average = total / dataPoints;
          const roundedAverage = this.selectedUsageMetric.name === 'Storage' ?
                               Math.round(average * 10) / 10 : // 1 decimal place for storage
                               Math.round(average); // Integer for others

          // Calculate projected usage (simple linear projection)
          const firstValue = data[0];
          const lastValue = data[data.length - 1];
          const growthRate = (lastValue - firstValue) / firstValue;
          const projectedValue = lastValue * (1 + growthRate);
          const roundedProjection = this.selectedUsageMetric.name === 'Storage' ?
                                  Math.round(projectedValue * 10) / 10 : // 1 decimal place for storage
                                  Math.round(projectedValue); // Integer for others

          // Calculate projection date (30 days from now)
          const projectionDate = new Date();
          projectionDate.setDate(projectionDate.getDate() + 30);

          // Generate recommendations based on usage patterns
          const recommendations = [];

          if (this.selectedUsageMetric.name === 'Storage') {
            if (roundedProjection > limit * 0.8) {
              recommendations.push('Consider upgrading your plan to avoid reaching storage limits.');
              recommendations.push('Archive older assets to free up storage space.');
            }
            recommendations.push('Optimize asset sizes by using compression techniques.');
          } else if (this.selectedUsageMetric.name === 'API Calls') {
            if (roundedProjection > limit * 0.8) {
              recommendations.push('Implement client-side caching to reduce API calls.');
              recommendations.push('Consider upgrading your plan for higher API call limits.');
            }
            recommendations.push('Batch API requests where possible to reduce total call count.');
          } else if (this.selectedUsageMetric.name === 'Team Members') {
            if (roundedProjection > limit * 0.8) {
              recommendations.push('Review team member access and remove inactive users.');
              recommendations.push('Consider upgrading to a plan with more team member slots.');
            }
            recommendations.push('Implement role-based access to optimize team member usage.');
          } else {
            if (roundedProjection > limit * 0.8) {
              recommendations.push('Archive inactive showrooms to stay within your plan limits.');
              recommendations.push('Consider upgrading to a plan with more showroom slots.');
            }
            recommendations.push('Consolidate similar showrooms to optimize usage.');
          }

          // Update the selected metric with the generated data
          this.selectedUsageMetric = {
            ...this.selectedUsageMetric,
            data,
            labels,
            averageUsage: roundedAverage + (unit === 'GB' ? ' GB' : unit === 'calls' ? ' calls' : ''),
            peakUsage: peak + (unit === 'GB' ? ' GB' : unit === 'calls' ? ' calls' : ''),
            peakDate,
            projectedUsage: roundedProjection + (unit === 'GB' ? ' GB' : unit === 'calls' ? ' calls' : ''),
            projectionDate,
            trend: growthRate > 0 ? 'up' : growthRate < 0 ? 'down' : 'neutral',
            change: (growthRate > 0 ? '+' : '') + Math.round(growthRate * 100) + '%',
            recommendations
          };

          // Initialize or update the chart
          this.$nextTick(() => {
            this.initUsageHistoryChart();
          });
        }, 500);
      } catch (error) {
        console.error('Error loading usage history:', error);
      }
    },

    // Initialize usage history chart
    initUsageHistoryChart() {
      const ctx = this.$refs.usageHistoryChart?.getContext('2d');
      if (!ctx) return;

      // Destroy existing chart if it exists
      if (this.usageHistoryChart) {
        this.usageHistoryChart.destroy();
      }

      // Create new chart
      this.usageHistoryChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.selectedUsageMetric.labels,
          datasets: [
            {
              label: this.selectedUsageMetric.name,
              data: this.selectedUsageMetric.data,
              borderColor: '#3498db',
              backgroundColor: 'rgba(52, 152, 219, 0.1)',
              tension: 0.4,
              fill: true
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
              labels: {
                font: {
                  family: 'var(--theme--font-family-sans-serif)',
                  size: 12
                },
                color: 'var(--theme--foreground)'
              }
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: 'var(--theme--foreground-subdued)'
              }
            },
            y: {
              grid: {
                color: 'var(--theme--border-color)',
                drawBorder: false
              },
              ticks: {
                color: 'var(--theme--foreground-subdued)'
              }
            }
          }
        }
      });
    },

    // Load subscription analytics data
    async loadSubscriptionAnalytics(forceRefresh = false) {
      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/subscription/analytics?timeframe=${this.analyticsTimeframe}`);

        // Mock data for demonstration
        setTimeout(() => {
          // Generate mock data based on the selected timeframe
          const costLabels = [];
          const costData = [];
          const efficiencyLabels = ['Storage', 'API Calls', 'Team Members', 'Showrooms'];
          const efficiencyData = [65, 87, 60, 75]; // Percentages

          let startDate;
          let dataPoints;

          switch (this.analyticsTimeframe) {
            case '6months':
              startDate = new Date();
              startDate.setMonth(startDate.getMonth() - 6);
              dataPoints = 6;
              break;
            case '12months':
              startDate = new Date();
              startDate.setMonth(startDate.getMonth() - 12);
              dataPoints = 12;
              break;
            case '24months':
              startDate = new Date();
              startDate.setMonth(startDate.getMonth() - 24);
              dataPoints = 12; // Every 2 months
              break;
            default:
              startDate = new Date();
              startDate.setMonth(startDate.getMonth() - 12);
              dataPoints = 12;
          }

          // Generate cost history data
          let totalSpend = 0;
          const basePrice = 49.99; // Professional plan

          for (let i = 0; i < dataPoints; i++) {
            const date = new Date(startDate);

            if (this.analyticsTimeframe === '24months') {
              date.setMonth(date.getMonth() + (i * 2));
            } else {
              date.setMonth(date.getMonth() + i);
            }

            // Generate a value with some randomness
            const randomFactor = Math.random() * 0.1 - 0.05; // -5% to +5%
            const cost = basePrice * (1 + randomFactor);
            const roundedCost = Math.round(cost * 100) / 100;

            const monthYear = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            costLabels.push(monthYear);
            costData.push(roundedCost);

            totalSpend += roundedCost;
          }

          // Calculate average monthly cost
          const averageMonthlyCost = totalSpend / dataPoints;

          // Calculate cost trends
          const firstHalfAvg = costData.slice(0, Math.floor(dataPoints / 2))
                              .reduce((sum, cost) => sum + cost, 0) / Math.floor(dataPoints / 2);
          const secondHalfAvg = costData.slice(Math.floor(dataPoints / 2))
                               .reduce((sum, cost) => sum + cost, 0) / (dataPoints - Math.floor(dataPoints / 2));
          const costTrendPercentage = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;

          // Calculate cost projection
          const costProjection = averageMonthlyCost * (1 + (costTrendPercentage / 100)) * 12;

          // Calculate resource utilization (average of efficiency data)
          const resourceUtilization = efficiencyData.reduce((sum, value) => sum + value, 0) / efficiencyData.length;

          // Calculate cost per showroom and client
          const showrooms = 3;
          const clients = 24;
          const costPerShowroom = averageMonthlyCost / showrooms;
          const costPerClient = averageMonthlyCost / clients;

          // Generate cost optimization recommendations
          const recommendations = [
            {
              title: 'Optimize Storage Usage',
              description: 'Implement asset compression to reduce storage costs.',
              potentialSavings: '5.99/month'
            },
            {
              title: 'Consolidate Showrooms',
              description: 'Merge similar showrooms to reduce overall costs.',
              potentialSavings: '10.00/month'
            },
            {
              title: 'Implement API Caching',
              description: 'Reduce API calls by implementing client-side caching.',
              potentialSavings: '3.50/month'
            }
          ];

          // Update subscription analytics data
          this.subscriptionAnalytics = {
            totalSpend: totalSpend.toFixed(2),
            averageMonthlyCost: averageMonthlyCost.toFixed(2),
            costProjection: costProjection.toFixed(2),
            resourceUtilization: Math.round(resourceUtilization),
            costPerShowroom: costPerShowroom.toFixed(2),
            costPerClient: costPerClient.toFixed(2),
            costTrend: costTrendPercentage > 0 ? 'up' : costTrendPercentage < 0 ? 'down' : 'neutral',
            costChange: (costTrendPercentage > 0 ? '+' : '') + Math.round(costTrendPercentage) + '%',
            showroomCostTrend: 'down',
            showroomCostChange: '-5%',
            clientCostTrend: 'down',
            clientCostChange: '-8%',
            costHistory: {
              labels: costLabels,
              data: costData
            },
            usageEfficiency: {
              labels: efficiencyLabels,
              data: efficiencyData
            },
            recommendations
          };

          // Initialize or update the charts
          this.$nextTick(() => {
            this.initCostHistoryChart();
            this.initUsageEfficiencyChart();
          });
        }, 500);
      } catch (error) {
        console.error('Error loading subscription analytics:', error);
      }
    },

    // Initialize cost history chart
    initCostHistoryChart() {
      const ctx = this.$refs.costHistoryChart?.getContext('2d');
      if (!ctx) return;

      // Destroy existing chart if it exists
      if (this.costHistoryChart) {
        this.costHistoryChart.destroy();
      }

      // Create new chart
      this.costHistoryChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: this.subscriptionAnalytics.costHistory.labels,
          datasets: [
            {
              label: 'Monthly Cost',
              data: this.subscriptionAnalytics.costHistory.data,
              backgroundColor: 'rgba(155, 89, 182, 0.7)'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top',
              labels: {
                font: {
                  family: 'var(--theme--font-family-sans-serif)',
                  size: 12
                },
                color: 'var(--theme--foreground)'
              }
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: 'var(--theme--foreground-subdued)'
              }
            },
            y: {
              grid: {
                color: 'var(--theme--border-color)',
                drawBorder: false
              },
              ticks: {
                color: 'var(--theme--foreground-subdued)',
                callback: function(value) {
                  return '$' + value;
                }
              }
            }
          }
        }
      });
    },

    // Initialize usage efficiency chart
    initUsageEfficiencyChart() {
      const ctx = this.$refs.usageEfficiencyChart?.getContext('2d');
      if (!ctx) return;

      // Destroy existing chart if it exists
      if (this.usageEfficiencyChart) {
        this.usageEfficiencyChart.destroy();
      }

      // Create new chart
      this.usageEfficiencyChart = new Chart(ctx, {
        type: 'radar',
        data: {
          labels: this.subscriptionAnalytics.usageEfficiency.labels,
          datasets: [
            {
              label: 'Resource Utilization',
              data: this.subscriptionAnalytics.usageEfficiency.data,
              backgroundColor: 'rgba(52, 152, 219, 0.2)',
              borderColor: 'rgba(52, 152, 219, 0.7)',
              pointBackgroundColor: 'rgba(52, 152, 219, 1)',
              pointBorderColor: '#fff',
              pointHoverBackgroundColor: '#fff',
              pointHoverBorderColor: 'rgba(52, 152, 219, 1)'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              angleLines: {
                color: 'var(--theme--border-color)'
              },
              grid: {
                color: 'var(--theme--border-color)'
              },
              pointLabels: {
                color: 'var(--theme--foreground)'
              },
              ticks: {
                color: 'var(--theme--foreground-subdued)',
                backdropColor: 'transparent',
                callback: function(value) {
                  return value + '%';
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'top',
              labels: {
                font: {
                  family: 'var(--theme--font-family-sans-serif)',
                  size: 12
                },
                color: 'var(--theme--foreground)'
              }
            }
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.subscription-manager {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.subscription-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.subscription-section {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  padding: 20px;
}

.subscription-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: var(--theme--foreground);
}

/* Current Plan Styles */
.current-plan-card {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  border-left: 4px solid var(--theme--primary);
}

.current-plan-card.plan-starter {
  border-left-color: #3498db;
}

.current-plan-card.plan-professional {
  border-left-color: #9b59b6;
}

.current-plan-card.plan-enterprise {
  border-left-color: #e74c3c;
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.plan-name {
  font-size: 20px;
  font-weight: 600;
}

.plan-status {
  padding: 4px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.plan-status.active {
  background-color: var(--theme--primary);
  color: white;
}

.plan-status.cancelling {
  background-color: var(--theme--warning);
  color: white;
}

.plan-status.cancelled {
  background-color: var(--theme--danger);
  color: white;
}

.plan-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.plan-price {
  font-size: 24px;
  font-weight: 600;
}

.plan-price .currency {
  font-size: 16px;
  vertical-align: top;
}

.plan-price .period {
  font-size: 14px;
  font-weight: normal;
  color: var(--theme--foreground-subdued);
}

.plan-renewal {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.plan-features {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-item i {
  color: var(--theme--primary);
  font-size: 18px;
}

.plan-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Usage Metrics Styles */
.usage-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.metric-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.metric-info {
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  transition: color 0.2s ease;
}

.metric-info:hover {
  color: var(--theme--primary);
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
}

.metric-value .separator {
  font-weight: normal;
  color: var(--theme--foreground-subdued);
  margin: 0 5px;
}

.metric-value .limit {
  font-weight: normal;
  color: var(--theme--foreground-subdued);
}

.metric-progress {
  margin-top: 10px;
}

.progress-bar {
  height: 8px;
  background-color: var(--theme--background);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background-color: var(--theme--primary);
  border-radius: 4px;
}

.progress-fill.warning {
  background-color: var(--theme--warning);
}

.progress-text {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  text-align: right;
}

/* Billing History Styles */
.billing-table {
  width: 100%;
  border-collapse: collapse;
}

.billing-table th {
  text-align: left;
  padding: 10px;
  border-bottom: 1px solid var(--theme--border-color);
  font-weight: 600;
  color: var(--theme--foreground-subdued);
}

.billing-table td {
  padding: 12px 10px;
  border-bottom: 1px solid var(--theme--border-color);
}

.billing-table tr:last-child td {
  border-bottom: none;
}

.invoice-status {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.invoice-status.paid {
  background-color: var(--theme--primary);
  color: white;
}

.invoice-status.pending {
  background-color: var(--theme--warning);
  color: white;
}

.invoice-status.failed {
  background-color: var(--theme--danger);
  color: white;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: var(--theme--foreground-subdued);
  font-style: italic;
}

/* Payment Methods Styles */
.payment-method-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.payment-method-card {
  display: flex;
  align-items: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  gap: 15px;
}

.payment-method-card.default {
  border-left: 4px solid var(--theme--primary);
}

.payment-method-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--theme--primary-background);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.payment-method-icon i {
  color: var(--theme--primary);
  font-size: 20px;
}

.payment-method-details {
  flex-grow: 1;
}

.payment-method-name {
  font-weight: 600;
  margin-bottom: 5px;
}

.payment-method-expiry {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.payment-method-default {
  font-size: 12px;
  color: var(--theme--primary);
  margin-top: 5px;
  font-weight: 600;
}

.payment-method-actions {
  display: flex;
  gap: 5px;
}

.payment-method-add {
  margin-top: 15px;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--theme--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Plan Selection Styles */
.plan-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
}

.plan-option {
  border: 2px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.plan-option:hover {
  border-color: var(--theme--primary);
}

.plan-option.selected {
  border-color: var(--theme--primary);
  background-color: var(--theme--primary-background);
}

.plan-option.current {
  border-style: dashed;
}

.plan-option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.plan-option-name {
  font-weight: 600;
}

.plan-option-current {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.plan-option-price {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
}

.plan-option-features {
  font-size: 14px;
}

.plan-option-features .feature-item {
  margin-bottom: 8px;
}

.plan-option-features .feature-item i {
  font-size: 16px;
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-row {
  display: flex;
  gap: 15px;
}

.form-group.half {
  flex: 1;
}

.checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox input {
  width: auto;
}

.warning {
  color: var(--theme--danger);
  font-weight: 500;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--border-color);
}

.btn-danger {
  background-color: var(--theme--danger);
  color: white;
}

.btn-danger:hover {
  background-color: var(--theme--danger-accent);
}

.btn-icon {
  padding: 6px;
  border-radius: var(--theme--border-radius);
  background-color: transparent;
}

.btn-icon i {
  margin-right: 0;
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Metric Info Modal Styles */
.metric-details {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  margin: 15px 0;
}

.metric-detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.metric-detail-item:last-child {
  margin-bottom: 0;
}

.metric-detail-item .label {
  font-weight: 500;
}

.metric-upgrade-info {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid var(--theme--border-color);
}

.metric-upgrade-info h4 {
  margin-top: 0;
  margin-bottom: 10px;
}

.metric-upgrade-info p {
  margin: 0;
  color: var(--theme--foreground-subdued);
}

/* Section Header Styles */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-range select {
  padding: 6px 10px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-size: 14px;
}

/* Metric Actions Styles */
.metric-actions {
  margin-top: 10px;
  text-align: right;
}

.btn-text {
  background-color: transparent;
  color: var(--theme--primary);
  padding: 4px 8px;
}

.btn-text:hover {
  background-color: var(--theme--primary-background);
}

/* Usage History Styles */
.usage-history {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-container {
  position: relative;
  height: 300px;
  margin-bottom: 20px;
}

.chart-container h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.usage-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.stat-title {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 5px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.stat-subtitle {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.stat-trend {
  display: flex;
  align-items: center;
  font-size: 14px;
  margin-top: 5px;
}

.stat-trend.up {
  color: var(--theme--primary);
}

.stat-trend.down {
  color: var(--theme--danger);
}

.stat-trend.neutral {
  color: var(--theme--foreground-subdued);
}

.stat-trend i {
  font-size: 16px;
  margin-right: 4px;
}

.usage-recommendations {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.usage-recommendations h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
}

.recommendation-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.recommendation-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--theme--border-color);
}

.recommendation-list li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.recommendation-list li i {
  color: var(--theme--primary);
  margin-right: 10px;
  margin-top: 2px;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.recommendation-description {
  color: var(--theme--foreground-subdued);
  margin-bottom: 5px;
  font-size: 14px;
}

.recommendation-savings {
  font-size: 12px;
  color: var(--theme--primary);
}

/* Analytics Styles */
.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.analytics-charts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.analytics-metrics {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.metric-row {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.analytics-metric {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.cost-optimization {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}
</style>
