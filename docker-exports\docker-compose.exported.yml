services:
  # Authentication Service
  auth-service:
    image: mvs-vr-v2-auth-service:test
    restart: always
    ports:
      - "3005:3005"
    command: ["node", "dist/services/auth/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=3005
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDI4MjcyNCwiZXhwIjoyMDU5ODU4NzI0fQ.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyODI3MjMsImV4cCI6MjA1OTg1ODcyM30.v7jSHJ4sPwnPzuwbYTCGxkwyDD4N8Ei89awcuXhPqhc
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
      - JWT_SECRET=3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c5b4a3f2e
    volumes:
      - auth-data:/app/data
    networks:
      - mvs-network
    depends_on:
      - redis

  # API Gateway
  api-gateway:
    image: mvs-vr-v2-api-gateway:test
    restart: always
    ports:
      - "4000:4000"
    command: ["node", "dist/api/gateway/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=4000
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDI4MjcyNCwiZXhwIjoyMDU5ODU4NzI0fQ.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQyODI3MjMsImV4cCI6MjA1OTg1ODcyM30.v7jSHJ4sPwnPzuwbYTCGxkwyDD4N8Ei89awcuXhPqhc
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
      - AUTH_SERVICE_URL=http://auth-service:3005
      - ASSET_SERVICE_URL=http://asset-service:5000
    volumes:
      - gateway-data:/app/data
    networks:
      - mvs-network
    depends_on:
      - auth-service
      - redis

  # Asset Service
  asset-service:
    image: mvs-vr-v2-asset-service:20250601-192616
    restart: always
    ports:
      - "5000:5000"
    command: ["node", "dist/services/asset/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=5000
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDI4MjcyNCwiZXhwIjoyMDU5ODU4NzI0fQ.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - STORAGE_BUCKET=assets
      - STORAGE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co/storage/v1
    volumes:
      - asset-data:/app/data
      - asset-uploads:/app/uploads
    networks:
      - mvs-network

  # Analytics Service
  analytics-service:
    image: mvs-vr-v2-analytics-service:20250601-192616
    restart: always
    ports:
      - "8000:8000"
    command: ["node", "dist/services/analytics/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=8000
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhpeXFpcWJnaXVleXl2cW9xaGh0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDI4MjcyNCwiZXhwIjoyMDU5ODU4NzI0fQ.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
    networks:
      - mvs-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass 9elskdUeo@I!
    volumes:
      - redis-data:/data
    networks:
      - mvs-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./nginx-simple.conf:/etc/nginx/nginx.conf:ro
    networks:
      - mvs-network

volumes:
  auth-data:
  gateway-data:
  asset-data:
  asset-uploads:
  redis-data:

networks:
  mvs-network:
    driver: bridge
