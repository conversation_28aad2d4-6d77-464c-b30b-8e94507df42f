version: '3.8'

services:
  # Authentication Service
  auth-service:
    image: mvs-vr-v2-auth-service:20250601-192616
    restart: always
    ports:
      - "3005:3005"
    command: ["node", "dist/services/auth/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=3005
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.v7jSHJ4sPwnPzuwbYTCGxkwyDD4N8Ei89awcuXhPqhc
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
      - JWT_SECRET=3f2e1d0c9b8a7f6e5d4c3b2a1f0e9d8c7b6a5f4e3d2c1b0a9f8e7d6c5b4a3f2e
    volumes:
      - auth-data:/app/data
    networks:
      - mvs-network
    depends_on:
      - redis
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3005/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway
  api-gateway:
    image: mvs-vr-v2-api-gateway:20250601-192616
    restart: always
    ports:
      - "4000:4000"
    command: ["node", "dist/api/gateway/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=4000
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.v7jSHJ4sPwnPzuwbYTCGxkwyDD4N8Ei89awcuXhPqhc
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
      - AUTH_SERVICE_URL=http://auth-service:3005
      - ASSET_SERVICE_URL=http://asset-service:5000
      - SCENE_SERVICE_URL=http://scene-service:6000
      - BLUEPRINT_SERVICE_URL=http://blueprint-service:3003
      - LLM_SERVICE_URL=http://llm-service:7000
      - ANALYTICS_SERVICE_URL=http://analytics-service:8000
      - MONITORING_SERVICE_URL=http://monitoring-service:9090
    volumes:
      - gateway-data:/app/data
    networks:
      - mvs-network
    depends_on:
      - auth-service
      - redis
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:4000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Asset Service
  asset-service:
    image: mvs-vr-v2-asset-service:20250601-192616
    restart: always
    ports:
      - "5000:5000"
    command: ["node", "dist/services/asset/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=5000
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - STORAGE_BUCKET=assets
      - STORAGE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co/storage/v1
    volumes:
      - asset-data:/app/data
      - asset-uploads:/app/uploads
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:5000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Scene Service
  scene-service:
    image: mvs-vr-v2-scene-service:20250601-192616
    restart: always
    ports:
      - "6000:6000"
    command: ["node", "dist/services/scene/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=6000
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
    volumes:
      - scene-data:/app/data
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:6000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Blueprint Service
  blueprint-service:
    image: mvs-vr-v2-blueprint-service:20250601-192616
    restart: always
    ports:
      - "3003:3003"
    command: ["node", "dist/services/blueprint/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=3003
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
    volumes:
      - blueprint-data:/app/data
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3003/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # LLM Service
  llm-service:
    image: mvs-vr-v2-llm-service:20250601-192616
    restart: always
    ports:
      - "7000:7000"
    command: ["node", "dist/services/llm/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=7000
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - OPENAI_API_KEY=sk-placeholder-key
      - ANTHROPIC_API_KEY=sk-placeholder-key
      - LLM_PROVIDER=openai
      - LLM_MODEL=gpt-3.5-turbo
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:7000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Service
  monitoring-service:
    image: mvs-vr-v2-monitoring-service:20250601-192616
    restart: always
    ports:
      - "9090:9090"
    command: ["node", "dist/services/monitoring/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=9090
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:9090/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Analytics Service
  analytics-service:
    image: mvs-vr-v2-analytics-service:20250601-192616
    restart: always
    ports:
      - "8000:8000"
    command: ["node", "dist/services/analytics/server.js"]
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=8000
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.uZUxTzkwlcBUsiOQF6FeUBiSvv9jiptfE4HQNLvHe30
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:8000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Directus CMS
  directus:
    image: mvs-vr-v2-directus:20250601-192616
    restart: always
    ports:
      - "8055:8055"
    environment:
      - KEY=7f8a9b2c4d5e6f1a8b9c0d1e2f3a4b5c
      - SECRET=9c8b7a6f5e4d3c2b1a0f9e8d7c6b5a4f
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=9elskdUeo@I!
      - DB_CLIENT=pg
      - DB_HOST=aws-0-us-east-1.pooler.supabase.com
      - DB_PORT=6543
      - DB_DATABASE=postgres
      - DB_USER=postgres.hiyqiqbgiueyyvqoqhht
      - DB_PASSWORD=9elskdUeo@I!
      - SUPABASE_URL=https://hiyqiqbgiueyyvqoqhht.supabase.co
      - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.v7jSHJ4sPwnPzuwbYTCGxkwyDD4N8Ei89awcuXhPqhc
      - PUBLIC_URL=https://admin.mvs.kanousai.com
      - CORS_ENABLED=true
      - CORS_ORIGIN=true
    volumes:
      - directus-uploads:/directus/uploads
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:8055/server/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass 9elskdUeo@I!
    volumes:
      - redis-data:/data
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - mvs-network
    depends_on:
      - api-gateway
      - directus
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  auth-data:
  gateway-data:
  asset-data:
  asset-uploads:
  scene-data:
  blueprint-data:
  directus-uploads:
  redis-data:

networks:
  mvs-network:
    driver: bridge
