[{"C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\admin\\index.tsx": "1", "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\admin\\login.tsx": "2", "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\admin\\vendors\\index.tsx": "3", "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\admin\\vendors\\new.tsx": "4", "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\api\\health.js": "5", "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\api\\metrics.js": "6", "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\lib\\logger.ts": "7", "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\lib\\supabase.ts": "8"}, {"size": 9099, "mtime": 1747579389847, "results": "9", "hashOfConfig": "10"}, {"size": 6015, "mtime": 1747579414556, "results": "11", "hashOfConfig": "10"}, {"size": 8416, "mtime": 1747579449021, "results": "12", "hashOfConfig": "10"}, {"size": 7875, "mtime": 1747579481278, "results": "13", "hashOfConfig": "10"}, {"size": 3910, "mtime": 1747619226142, "results": "14", "hashOfConfig": "10"}, {"size": 2037, "mtime": 1747619240171, "results": "15", "hashOfConfig": "10"}, {"size": 1659, "mtime": 1747807626863, "results": "16", "hashOfConfig": "10"}, {"size": 784, "mtime": 1747807614484, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 29, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 29, "fixableWarningCount": 0, "source": null}, "cuq<PERSON>e", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 17, "fixableWarningCount": 0, "source": null}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 28, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 27, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 20, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 17, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 36, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 35, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 19, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 19, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 5, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\admin\\index.tsx", ["42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64", "65", "66", "67", "68", "69", "70", "71"], [], "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\admin\\login.tsx", ["72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89"], [], "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\admin\\vendors\\index.tsx", ["90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113", "114", "115", "116", "117", "118"], [], "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\admin\\vendors\\new.tsx", ["119", "120", "121", "122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139"], [], "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\api\\health.js", ["140", "141", "142", "143", "144", "145", "146", "147", "148", "149", "150", "151", "152", "153", "154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172", "173", "174", "175"], [], "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\pages\\api\\metrics.js", ["176", "177", "178", "179", "180", "181", "182", "183", "184", "185", "186", "187", "188", "189", "190", "191", "192", "193", "194"], [], "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\lib\\logger.ts", ["195", "196", "197", "198", "199"], [], "C:\\Users\\<USER>\\projects\\MVS-VR\\mvs-vr-v2\\implementation\\server\\lib\\supabase.ts", [], [], {"ruleId": "200", "severity": 2, "message": "201", "line": 33, "column": 14, "nodeType": null, "messageId": "202", "endLine": 33, "endColumn": 32, "fix": "203"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 34, "column": 1, "nodeType": null, "messageId": "205", "endLine": 34, "endColumn": 7, "fix": "206"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 39, "column": 1, "nodeType": null, "messageId": "205", "endLine": 39, "endColumn": 7, "fix": "207"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 41, "column": 1, "nodeType": null, "messageId": "205", "endLine": 41, "endColumn": 7, "fix": "208"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 48, "column": 1, "nodeType": null, "messageId": "205", "endLine": 48, "endColumn": 7, "fix": "209"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 55, "column": 1, "nodeType": null, "messageId": "205", "endLine": 55, "endColumn": 7, "fix": "210"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 58, "column": 1, "nodeType": null, "messageId": "205", "endLine": 58, "endColumn": 7, "fix": "211"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 61, "column": 1, "nodeType": null, "messageId": "205", "endLine": 61, "endColumn": 5, "fix": "213"}, {"ruleId": "214", "severity": 1, "message": "215", "line": 63, "column": 6, "nodeType": "216", "endLine": 63, "endColumn": 8, "suggestions": "217"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 70, "column": 1, "nodeType": null, "messageId": "205", "endLine": 70, "endColumn": 5, "fix": "218"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 75, "column": 1, "nodeType": null, "messageId": "205", "endLine": 75, "endColumn": 5, "fix": "219"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 80, "column": 1, "nodeType": null, "messageId": "205", "endLine": 80, "endColumn": 5, "fix": "220"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 85, "column": 1, "nodeType": null, "messageId": "205", "endLine": 85, "endColumn": 5, "fix": "221"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 90, "column": 1, "nodeType": null, "messageId": "205", "endLine": 90, "endColumn": 5, "fix": "222"}, {"ruleId": "200", "severity": 2, "message": "223", "line": 147, "column": 18, "nodeType": null, "messageId": "202", "endLine": 147, "endColumn": 104, "fix": "224"}, {"ruleId": "200", "severity": 2, "message": "225", "line": 151, "column": 1, "nodeType": null, "messageId": "205", "endLine": 151, "endColumn": 11, "fix": "226"}, {"ruleId": "200", "severity": 2, "message": "227", "line": 153, "column": 87, "nodeType": null, "messageId": "202", "endLine": 153, "endColumn": 99, "fix": "228"}, {"ruleId": "200", "severity": 2, "message": "229", "line": 155, "column": 18, "nodeType": null, "messageId": "202", "endLine": 155, "endColumn": 109, "fix": "230"}, {"ruleId": "200", "severity": 2, "message": "225", "line": 159, "column": 1, "nodeType": null, "messageId": "205", "endLine": 159, "endColumn": 11, "fix": "231"}, {"ruleId": "200", "severity": 2, "message": "232", "line": 163, "column": 18, "nodeType": null, "messageId": "202", "endLine": 163, "endColumn": 103, "fix": "233"}, {"ruleId": "200", "severity": 2, "message": "225", "line": 167, "column": 1, "nodeType": null, "messageId": "205", "endLine": 167, "endColumn": 11, "fix": "234"}, {"ruleId": "200", "severity": 2, "message": "235", "line": 171, "column": 18, "nodeType": null, "messageId": "202", "endLine": 171, "endColumn": 103, "fix": "236"}, {"ruleId": "200", "severity": 2, "message": "225", "line": 175, "column": 1, "nodeType": null, "messageId": "205", "endLine": 175, "endColumn": 11, "fix": "237"}, {"ruleId": "200", "severity": 2, "message": "238", "line": 177, "column": 87, "nodeType": null, "messageId": "202", "endLine": 177, "endColumn": 97, "fix": "239"}, {"ruleId": "200", "severity": 2, "message": "240", "line": 179, "column": 18, "nodeType": null, "messageId": "202", "endLine": 179, "endColumn": 107, "fix": "241"}, {"ruleId": "200", "severity": 2, "message": "242", "line": 197, "column": 1, "nodeType": null, "messageId": "205", "endLine": 197, "endColumn": 13, "fix": "243"}, {"ruleId": "200", "severity": 2, "message": "242", "line": 204, "column": 1, "nodeType": null, "messageId": "205", "endLine": 204, "endColumn": 13, "fix": "244"}, {"ruleId": "200", "severity": 2, "message": "242", "line": 211, "column": 1, "nodeType": null, "messageId": "205", "endLine": 211, "endColumn": 13, "fix": "245"}, {"ruleId": "200", "severity": 2, "message": "242", "line": 218, "column": 1, "nodeType": null, "messageId": "205", "endLine": 218, "endColumn": 13, "fix": "246"}, {"ruleId": "200", "severity": 2, "message": "242", "line": 225, "column": 1, "nodeType": null, "messageId": "205", "endLine": 225, "endColumn": 13, "fix": "247"}, {"ruleId": "200", "severity": 2, "message": "201", "line": 19, "column": 14, "nodeType": null, "messageId": "202", "endLine": 19, "endColumn": 32, "fix": "248"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 20, "column": 1, "nodeType": null, "messageId": "205", "endLine": 20, "endColumn": 7, "fix": "249"}, {"ruleId": "200", "severity": 2, "message": "250", "line": 28, "column": 1, "nodeType": null, "messageId": "205", "endLine": 28, "endColumn": 9, "fix": "251"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 35, "column": 1, "nodeType": null, "messageId": "205", "endLine": 35, "endColumn": 5, "fix": "252"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 37, "column": 1, "nodeType": null, "messageId": "205", "endLine": 37, "endColumn": 5, "fix": "253"}, {"ruleId": "214", "severity": 1, "message": "254", "line": 42, "column": 6, "nodeType": "216", "endLine": 42, "endColumn": 8, "suggestions": "255"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 48, "column": 1, "nodeType": null, "messageId": "205", "endLine": 48, "endColumn": 5, "fix": "256"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 54, "column": 1, "nodeType": null, "messageId": "205", "endLine": 54, "endColumn": 7, "fix": "257"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 58, "column": 1, "nodeType": null, "messageId": "205", "endLine": 58, "endColumn": 7, "fix": "258"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 62, "column": 1, "nodeType": null, "messageId": "205", "endLine": 62, "endColumn": 7, "fix": "259"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 69, "column": 1, "nodeType": null, "messageId": "205", "endLine": 69, "endColumn": 7, "fix": "260"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 73, "column": 1, "nodeType": null, "messageId": "205", "endLine": 73, "endColumn": 7, "fix": "261"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 79, "column": 1, "nodeType": null, "messageId": "205", "endLine": 79, "endColumn": 7, "fix": "262"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 96, "column": 1, "nodeType": null, "messageId": "205", "endLine": 96, "endColumn": 7, "fix": "263"}, {"ruleId": "200", "severity": 2, "message": "225", "line": 117, "column": 1, "nodeType": null, "messageId": "205", "endLine": 117, "endColumn": 11, "fix": "264"}, {"ruleId": "200", "severity": 2, "message": "225", "line": 127, "column": 1, "nodeType": null, "messageId": "205", "endLine": 127, "endColumn": 11, "fix": "265"}, {"ruleId": "200", "severity": 2, "message": "266", "line": 141, "column": 29, "nodeType": null, "messageId": "202", "endLine": 141, "endColumn": 32, "fix": "267"}, {"ruleId": "200", "severity": 2, "message": "266", "line": 159, "column": 29, "nodeType": null, "messageId": "202", "endLine": 159, "endColumn": 32, "fix": "268"}, {"ruleId": "269", "severity": 2, "message": "270", "line": 19, "column": 10, "nodeType": "271", "messageId": "272", "endLine": 19, "endColumn": 14}, {"ruleId": "200", "severity": 2, "message": "201", "line": 29, "column": 14, "nodeType": null, "messageId": "202", "endLine": 29, "endColumn": 32, "fix": "273"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 30, "column": 1, "nodeType": null, "messageId": "205", "endLine": 30, "endColumn": 7, "fix": "274"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 35, "column": 1, "nodeType": null, "messageId": "205", "endLine": 35, "endColumn": 7, "fix": "275"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 37, "column": 1, "nodeType": null, "messageId": "205", "endLine": 37, "endColumn": 7, "fix": "276"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 44, "column": 1, "nodeType": null, "messageId": "205", "endLine": 44, "endColumn": 7, "fix": "277"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 51, "column": 1, "nodeType": null, "messageId": "205", "endLine": 51, "endColumn": 7, "fix": "278"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 54, "column": 1, "nodeType": null, "messageId": "205", "endLine": 54, "endColumn": 7, "fix": "279"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 57, "column": 1, "nodeType": null, "messageId": "205", "endLine": 57, "endColumn": 5, "fix": "280"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 59, "column": 1, "nodeType": null, "messageId": "205", "endLine": 59, "endColumn": 5, "fix": "281"}, {"ruleId": "214", "severity": 1, "message": "282", "line": 64, "column": 6, "nodeType": "216", "endLine": 64, "endColumn": 8, "suggestions": "283"}, {"ruleId": "200", "severity": 2, "message": "284", "line": 67, "column": 43, "nodeType": null, "messageId": "202", "endLine": 70, "endColumn": 7, "fix": "285"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 71, "column": 1, "nodeType": null, "messageId": "205", "endLine": 71, "endColumn": 5, "fix": "286"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 76, "column": 1, "nodeType": null, "messageId": "205", "endLine": 76, "endColumn": 5, "fix": "287"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 84, "column": 1, "nodeType": null, "messageId": "205", "endLine": 84, "endColumn": 5, "fix": "288"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 86, "column": 1, "nodeType": null, "messageId": "205", "endLine": 86, "endColumn": 5, "fix": "289"}, {"ruleId": "200", "severity": 2, "message": "290", "line": 87, "column": 37, "nodeType": null, "messageId": "202", "endLine": 90, "endColumn": 7, "fix": "291"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 91, "column": 1, "nodeType": null, "messageId": "205", "endLine": 91, "endColumn": 5, "fix": "292"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 99, "column": 1, "nodeType": null, "messageId": "205", "endLine": 99, "endColumn": 5, "fix": "293"}, {"ruleId": "200", "severity": 2, "message": "294", "line": 104, "column": 42, "nodeType": null, "messageId": "202", "endLine": 105, "endColumn": 1, "fix": "295"}, {"ruleId": "200", "severity": 2, "message": "296", "line": 106, "column": 5, "nodeType": null, "messageId": "202", "endLine": 106, "endColumn": 68, "fix": "297"}, {"ruleId": "200", "severity": 2, "message": "266", "line": 160, "column": 25, "nodeType": null, "messageId": "202", "endLine": 160, "endColumn": 28, "fix": "298"}, {"ruleId": "200", "severity": 2, "message": "225", "line": 164, "column": 1, "nodeType": null, "messageId": "205", "endLine": 164, "endColumn": 11, "fix": "299"}, {"ruleId": "200", "severity": 2, "message": "300", "line": 179, "column": 22, "nodeType": null, "messageId": "202", "endLine": 179, "endColumn": 125, "fix": "301"}, {"ruleId": "200", "severity": 2, "message": "300", "line": 182, "column": 22, "nodeType": null, "messageId": "202", "endLine": 182, "endColumn": 125, "fix": "302"}, {"ruleId": "200", "severity": 2, "message": "300", "line": 185, "column": 22, "nodeType": null, "messageId": "202", "endLine": 185, "endColumn": 125, "fix": "303"}, {"ruleId": "200", "severity": 2, "message": "300", "line": 188, "column": 22, "nodeType": null, "messageId": "202", "endLine": 188, "endColumn": 125, "fix": "304"}, {"ruleId": "200", "severity": 2, "message": "305", "line": 194, "column": 38, "nodeType": null, "messageId": "202", "endLine": 194, "endColumn": 46, "fix": "306"}, {"ruleId": "200", "severity": 2, "message": "307", "line": 235, "column": 26, "nodeType": null, "messageId": "202", "endLine": 235, "endColumn": 69, "fix": "308"}, {"ruleId": "269", "severity": 2, "message": "270", "line": 11, "column": 10, "nodeType": "271", "messageId": "272", "endLine": 11, "endColumn": 14}, {"ruleId": "200", "severity": 2, "message": "309", "line": 15, "column": 1, "nodeType": null, "messageId": "205", "endLine": 15, "endColumn": 3, "fix": "310"}, {"ruleId": "269", "severity": 2, "message": "311", "line": 18, "column": 10, "nodeType": "271", "messageId": "272", "endLine": 18, "endColumn": 24}, {"ruleId": "200", "severity": 2, "message": "201", "line": 24, "column": 14, "nodeType": null, "messageId": "202", "endLine": 24, "endColumn": 32, "fix": "312"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 25, "column": 1, "nodeType": null, "messageId": "205", "endLine": 25, "endColumn": 7, "fix": "313"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 30, "column": 1, "nodeType": null, "messageId": "205", "endLine": 30, "endColumn": 7, "fix": "314"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 32, "column": 1, "nodeType": null, "messageId": "205", "endLine": 32, "endColumn": 7, "fix": "315"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 39, "column": 1, "nodeType": null, "messageId": "205", "endLine": 39, "endColumn": 7, "fix": "316"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 46, "column": 1, "nodeType": null, "messageId": "205", "endLine": 46, "endColumn": 7, "fix": "317"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 49, "column": 1, "nodeType": null, "messageId": "205", "endLine": 49, "endColumn": 7, "fix": "318"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 52, "column": 1, "nodeType": null, "messageId": "205", "endLine": 52, "endColumn": 5, "fix": "319"}, {"ruleId": "214", "severity": 1, "message": "254", "line": 54, "column": 6, "nodeType": "216", "endLine": 54, "endColumn": 8, "suggestions": "320"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 66, "column": 1, "nodeType": null, "messageId": "205", "endLine": 66, "endColumn": 5, "fix": "321"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 72, "column": 1, "nodeType": null, "messageId": "205", "endLine": 72, "endColumn": 7, "fix": "322"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 76, "column": 1, "nodeType": null, "messageId": "205", "endLine": 76, "endColumn": 7, "fix": "323"}, {"ruleId": "269", "severity": 2, "message": "324", "line": 78, "column": 15, "nodeType": "271", "messageId": "272", "endLine": 78, "endColumn": 19}, {"ruleId": "200", "severity": 2, "message": "204", "line": 86, "column": 1, "nodeType": null, "messageId": "205", "endLine": 86, "endColumn": 7, "fix": "325"}, {"ruleId": "200", "severity": 2, "message": "204", "line": 90, "column": 1, "nodeType": null, "messageId": "205", "endLine": 90, "endColumn": 7, "fix": "326"}, {"ruleId": "200", "severity": 2, "message": "266", "line": 160, "column": 33, "nodeType": null, "messageId": "202", "endLine": 160, "endColumn": 36, "fix": "327"}, {"ruleId": "200", "severity": 2, "message": "328", "line": 166, "column": 61, "nodeType": null, "messageId": "202", "endLine": 168, "endColumn": 19, "fix": "329"}, {"ruleId": "200", "severity": 2, "message": "266", "line": 181, "column": 33, "nodeType": null, "messageId": "202", "endLine": 181, "endColumn": 36, "fix": "330"}, {"ruleId": "200", "severity": 2, "message": "331", "line": 3, "column": 3, "nodeType": null, "messageId": "205", "endLine": 3, "endColumn": 4, "fix": "332"}, {"ruleId": "200", "severity": 2, "message": "309", "line": 20, "column": 1, "nodeType": null, "messageId": "205", "endLine": 20, "endColumn": 3, "fix": "333"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 24, "column": 1, "nodeType": null, "messageId": "205", "endLine": 24, "endColumn": 5, "fix": "334"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 27, "column": 1, "nodeType": null, "messageId": "205", "endLine": 27, "endColumn": 5, "fix": "335"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 30, "column": 1, "nodeType": null, "messageId": "205", "endLine": 30, "endColumn": 5, "fix": "336"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 33, "column": 1, "nodeType": null, "messageId": "205", "endLine": 33, "endColumn": 5, "fix": "337"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 42, "column": 27, "nodeType": null, "messageId": "339", "endLine": 42, "endColumn": 27, "fix": "340"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 42, "column": 27, "nodeType": "343", "messageId": "344", "endLine": 43, "endColumn": 1, "fix": "345"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 43, "column": 8, "nodeType": null, "messageId": "339", "endLine": 43, "endColumn": 8, "fix": "346"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 43, "column": 8, "nodeType": "343", "messageId": "344", "endLine": 44, "endColumn": 1, "fix": "347"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 45, "column": 1, "nodeType": null, "messageId": "205", "endLine": 45, "endColumn": 5, "fix": "348"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 48, "column": 1, "nodeType": null, "messageId": "205", "endLine": 48, "endColumn": 5, "fix": "349"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 52, "column": 1, "nodeType": null, "messageId": "205", "endLine": 52, "endColumn": 5, "fix": "350"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 56, "column": 27, "nodeType": null, "messageId": "339", "endLine": 56, "endColumn": 27, "fix": "351"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 56, "column": 27, "nodeType": "343", "messageId": "344", "endLine": 57, "endColumn": 1, "fix": "352"}, {"ruleId": "269", "severity": 2, "message": "324", "line": 68, "column": 13, "nodeType": "271", "messageId": "272", "endLine": 68, "endColumn": 17}, {"ruleId": "200", "severity": 2, "message": "212", "line": 70, "column": 1, "nodeType": null, "messageId": "205", "endLine": 70, "endColumn": 5, "fix": "353"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 72, "column": 1, "nodeType": null, "messageId": "205", "endLine": 72, "endColumn": 5, "fix": "354"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 75, "column": 30, "nodeType": null, "messageId": "339", "endLine": 75, "endColumn": 30, "fix": "355"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 75, "column": 30, "nodeType": "343", "messageId": "344", "endLine": 76, "endColumn": 1, "fix": "356"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 79, "column": 1, "nodeType": null, "messageId": "205", "endLine": 79, "endColumn": 5, "fix": "357"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 82, "column": 27, "nodeType": null, "messageId": "339", "endLine": 82, "endColumn": 27, "fix": "358"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 82, "column": 27, "nodeType": "343", "messageId": "344", "endLine": 83, "endColumn": 1, "fix": "359"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 97, "column": 1, "nodeType": null, "messageId": "205", "endLine": 97, "endColumn": 5, "fix": "360"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 101, "column": 1, "nodeType": null, "messageId": "205", "endLine": 101, "endColumn": 5, "fix": "361"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 104, "column": 30, "nodeType": null, "messageId": "339", "endLine": 104, "endColumn": 30, "fix": "362"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 104, "column": 30, "nodeType": "343", "messageId": "344", "endLine": 105, "endColumn": 1, "fix": "363"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 108, "column": 1, "nodeType": null, "messageId": "205", "endLine": 108, "endColumn": 5, "fix": "364"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 111, "column": 27, "nodeType": null, "messageId": "339", "endLine": 111, "endColumn": 27, "fix": "365"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 111, "column": 27, "nodeType": "343", "messageId": "344", "endLine": 112, "endColumn": 1, "fix": "366"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 126, "column": 21, "nodeType": null, "messageId": "339", "endLine": 126, "endColumn": 21, "fix": "367"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 126, "column": 21, "nodeType": "343", "messageId": "344", "endLine": 127, "endColumn": 1, "fix": "368"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 128, "column": 1, "nodeType": null, "messageId": "205", "endLine": 128, "endColumn": 5, "fix": "369"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 148, "column": 1, "nodeType": null, "messageId": "205", "endLine": 148, "endColumn": 5, "fix": "370"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 151, "column": 27, "nodeType": null, "messageId": "339", "endLine": 151, "endColumn": 27, "fix": "371"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 151, "column": 27, "nodeType": "343", "messageId": "344", "endLine": 152, "endColumn": 1, "fix": "372"}, {"ruleId": "200", "severity": 2, "message": "331", "line": 3, "column": 3, "nodeType": null, "messageId": "205", "endLine": 3, "endColumn": 4, "fix": "373"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 17, "column": 43, "nodeType": null, "messageId": "339", "endLine": 17, "endColumn": 43, "fix": "374"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 17, "column": 43, "nodeType": "343", "messageId": "344", "endLine": 18, "endColumn": 1, "fix": "375"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 24, "column": 48, "nodeType": null, "messageId": "339", "endLine": 24, "endColumn": 48, "fix": "376"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 24, "column": 48, "nodeType": "343", "messageId": "344", "endLine": 25, "endColumn": 1, "fix": "377"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 31, "column": 43, "nodeType": null, "messageId": "339", "endLine": 31, "endColumn": 43, "fix": "378"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 31, "column": 43, "nodeType": "343", "messageId": "344", "endLine": 32, "endColumn": 1, "fix": "379"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 37, "column": 29, "nodeType": null, "messageId": "339", "endLine": 37, "endColumn": 29, "fix": "380"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 37, "column": 29, "nodeType": "343", "messageId": "344", "endLine": 38, "endColumn": 1, "fix": "381"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 43, "column": 29, "nodeType": null, "messageId": "339", "endLine": 43, "endColumn": 29, "fix": "382"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 43, "column": 29, "nodeType": "343", "messageId": "344", "endLine": 44, "endColumn": 1, "fix": "383"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 52, "column": 17, "nodeType": null, "messageId": "339", "endLine": 52, "endColumn": 17, "fix": "384"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 52, "column": 17, "nodeType": "343", "messageId": "344", "endLine": 53, "endColumn": 1, "fix": "385"}, {"ruleId": "200", "severity": 2, "message": "309", "line": 60, "column": 1, "nodeType": null, "messageId": "205", "endLine": 60, "endColumn": 3, "fix": "386"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 64, "column": 1, "nodeType": null, "messageId": "205", "endLine": 64, "endColumn": 5, "fix": "387"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 67, "column": 1, "nodeType": null, "messageId": "205", "endLine": 67, "endColumn": 5, "fix": "388"}, {"ruleId": "200", "severity": 2, "message": "212", "line": 71, "column": 1, "nodeType": null, "messageId": "205", "endLine": 71, "endColumn": 5, "fix": "389"}, {"ruleId": "200", "severity": 2, "message": "338", "line": 74, "column": 27, "nodeType": null, "messageId": "339", "endLine": 74, "endColumn": 27, "fix": "390"}, {"ruleId": "341", "severity": 2, "message": "342", "line": 74, "column": 27, "nodeType": "343", "messageId": "344", "endLine": 75, "endColumn": 1, "fix": "391"}, {"ruleId": "200", "severity": 2, "message": "392", "line": 14, "column": 34, "nodeType": null, "messageId": "202", "endLine": 17, "endColumn": 3, "fix": "393"}, {"ruleId": "200", "severity": 2, "message": "394", "line": 22, "column": 38, "nodeType": null, "messageId": "202", "endLine": 25, "endColumn": 7, "fix": "395"}, {"ruleId": "200", "severity": 2, "message": "396", "line": 32, "column": 14, "nodeType": null, "messageId": "202", "endLine": 34, "endColumn": 3, "fix": "397"}, {"ruleId": "200", "severity": 2, "message": "398", "line": 35, "column": 14, "nodeType": null, "messageId": "202", "endLine": 37, "endColumn": 3, "fix": "399"}, {"ruleId": "200", "severity": 2, "message": "400", "line": 44, "column": 15, "nodeType": null, "messageId": "202", "endLine": 44, "endColumn": 72, "fix": "401"}, "prettier/prettier", "Replace `·data:·{·session·}` with `⏎········data:·{·session·},⏎·····`", "replace", {"range": "402", "text": "403"}, "Delete `······`", "delete", {"range": "404", "text": "405"}, {"range": "406", "text": "405"}, {"range": "407", "text": "405"}, {"range": "408", "text": "405"}, {"range": "409", "text": "405"}, {"range": "410", "text": "405"}, "Delete `····`", {"range": "411", "text": "405"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadDashboardStats', 'router', and 'supabase'. Either include them or remove the dependency array.", "ArrayExpression", ["412"], {"range": "413", "text": "405"}, {"range": "414", "text": "405"}, {"range": "415", "text": "405"}, {"range": "416", "text": "405"}, {"range": "417", "text": "405"}, "Replace `·href=\"/admin/vendors\"·className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"` with `⏎··············href=\"/admin/vendors\"⏎··············className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"⏎············`", {"range": "418", "text": "419"}, "Delete `··········`", {"range": "420", "text": "405"}, "Replace `Environments` with `⏎··············Environments⏎············`", {"range": "421", "text": "422"}, "Replace `·href=\"/admin/environments\"·className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"` with `⏎··············href=\"/admin/environments\"⏎··············className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"⏎············`", {"range": "423", "text": "424"}, {"range": "425", "text": "405"}, "Replace `·href=\"/admin/assets\"·className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"` with `⏎··············href=\"/admin/assets\"⏎··············className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"⏎············`", {"range": "426", "text": "427"}, {"range": "428", "text": "405"}, "Replace `·href=\"/admin/scenes\"·className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"` with `⏎··············href=\"/admin/scenes\"⏎··············className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"⏎············`", {"range": "429", "text": "430"}, {"range": "431", "text": "405"}, "Replace `Blueprints` with `⏎··············Blueprints⏎············`", {"range": "432", "text": "433"}, "Replace `·href=\"/admin/blueprints\"·className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"` with `⏎··············href=\"/admin/blueprints\"⏎··············className=\"mt-2·inline-block·text-blue-600·hover:text-blue-800\"⏎············`", {"range": "434", "text": "435"}, "Delete `············`", {"range": "436", "text": "405"}, {"range": "437", "text": "405"}, {"range": "438", "text": "405"}, {"range": "439", "text": "405"}, {"range": "440", "text": "405"}, {"range": "441", "text": "403"}, {"range": "442", "text": "405"}, "Delete `········`", {"range": "443", "text": "405"}, {"range": "444", "text": "405"}, {"range": "445", "text": "405"}, "React Hook useEffect has missing dependencies: 'router' and 'supabase'. Either include them or remove the dependency array.", ["446"], {"range": "447", "text": "405"}, {"range": "448", "text": "405"}, {"range": "449", "text": "405"}, {"range": "450", "text": "405"}, {"range": "451", "text": "405"}, {"range": "452", "text": "405"}, {"range": "453", "text": "405"}, {"range": "454", "text": "405"}, {"range": "455", "text": "405"}, {"range": "456", "text": "405"}, "Replace `(e)` with `e`", {"range": "457", "text": "458"}, {"range": "459", "text": "458"}, "@typescript-eslint/no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", {"range": "460", "text": "403"}, {"range": "461", "text": "405"}, {"range": "462", "text": "405"}, {"range": "463", "text": "405"}, {"range": "464", "text": "405"}, {"range": "465", "text": "405"}, {"range": "466", "text": "405"}, {"range": "467", "text": "405"}, {"range": "468", "text": "405"}, "React Hook useEffect has missing dependencies: 'loadVendors', 'router', and 'supabase'. Either include them or remove the dependency array.", ["469"], "Replace `⏎······.from('vendors')⏎······.select('*')⏎······` with `.from('vendors').select('*')`", {"range": "470", "text": "471"}, {"range": "472", "text": "405"}, {"range": "473", "text": "405"}, {"range": "474", "text": "405"}, {"range": "475", "text": "405"}, "Replace `⏎······.from('vendors')⏎······.delete()⏎······` with `.from('vendors').delete()`", {"range": "476", "text": "477"}, {"range": "478", "text": "405"}, {"range": "479", "text": "405"}, "Replace `vendor·=>·⏎` with `⏎····vendor·=>⏎··`", {"range": "480", "text": "481"}, "Replace `vendor.api_key.toLowerCase().includes(searchTerm.toLowerCase())` with `··vendor.api_key.toLowerCase().includes(searchTerm.toLowerCase()),`", {"range": "482", "text": "483"}, {"range": "484", "text": "458"}, {"range": "485", "text": "405"}, "Replace `·scope=\"col\"·className=\"px-6·py-3·text-left·text-xs·font-medium·text-gray-500·uppercase·tracking-wider\"` with `⏎····················scope=\"col\"⏎····················className=\"px-6·py-3·text-left·text-xs·font-medium·text-gray-500·uppercase·tracking-wider\"⏎··················`", {"range": "486", "text": "487"}, {"range": "488", "text": "487"}, {"range": "489", "text": "487"}, {"range": "490", "text": "487"}, "Replace `(vendor)` with `vendor`", {"range": "491", "text": "492"}, "Replace `·?·'No·vendors·found·matching·your·search.'` with `⏎················?·'No·vendors·found·matching·your·search.'⏎···············`", {"range": "493", "text": "494"}, "Delete `··`", {"range": "495", "text": "405"}, "'generateApiKey' is assigned a value but never used.", {"range": "496", "text": "403"}, {"range": "497", "text": "405"}, {"range": "498", "text": "405"}, {"range": "499", "text": "405"}, {"range": "500", "text": "405"}, {"range": "501", "text": "405"}, {"range": "502", "text": "405"}, {"range": "503", "text": "405"}, ["504"], {"range": "505", "text": "405"}, {"range": "506", "text": "405"}, {"range": "507", "text": "405"}, "'data' is assigned a value but never used.", {"range": "508", "text": "405"}, {"range": "509", "text": "405"}, {"range": "510", "text": "458"}, "Replace `⏎····················The·name·of·the·vendor·organization.⏎··················` with `The·name·of·the·vendor·organization.`", {"range": "511", "text": "512"}, {"range": "513", "text": "458"}, "Delete `·`", {"range": "514", "text": "405"}, {"range": "515", "text": "405"}, {"range": "516", "text": "405"}, {"range": "517", "text": "405"}, {"range": "518", "text": "405"}, {"range": "519", "text": "405"}, "Insert `,`", "insert", {"range": "520", "text": "521"}, "comma-dangle", "Missing trailing comma.", "Property", "missing", {"range": "522", "text": "523"}, {"range": "524", "text": "521"}, {"range": "525", "text": "526"}, {"range": "527", "text": "405"}, {"range": "528", "text": "405"}, {"range": "529", "text": "405"}, {"range": "530", "text": "521"}, {"range": "531", "text": "532"}, {"range": "533", "text": "405"}, {"range": "534", "text": "405"}, {"range": "535", "text": "521"}, {"range": "536", "text": "537"}, {"range": "538", "text": "405"}, {"range": "539", "text": "521"}, {"range": "540", "text": "532"}, {"range": "541", "text": "405"}, {"range": "542", "text": "405"}, {"range": "543", "text": "521"}, {"range": "544", "text": "537"}, {"range": "545", "text": "405"}, {"range": "546", "text": "521"}, {"range": "547", "text": "532"}, {"range": "548", "text": "521"}, {"range": "549", "text": "550"}, {"range": "551", "text": "405"}, {"range": "552", "text": "405"}, {"range": "553", "text": "521"}, {"range": "554", "text": "532"}, {"range": "555", "text": "405"}, {"range": "556", "text": "521"}, {"range": "557", "text": "558"}, {"range": "559", "text": "521"}, {"range": "560", "text": "558"}, {"range": "561", "text": "521"}, {"range": "562", "text": "558"}, {"range": "563", "text": "521"}, {"range": "564", "text": "558"}, {"range": "565", "text": "521"}, {"range": "566", "text": "558"}, {"range": "567", "text": "521"}, {"range": "568", "text": "569"}, {"range": "570", "text": "405"}, {"range": "571", "text": "405"}, {"range": "572", "text": "405"}, {"range": "573", "text": "405"}, {"range": "574", "text": "521"}, {"range": "575", "text": "532"}, "Replace `⏎····winston.format.timestamp(),⏎····winston.format.json()⏎··` with `winston.format.timestamp(),·winston.format.json()`", {"range": "576", "text": "577"}, "Replace `⏎········winston.format.colorize(),⏎········winston.format.simple()⏎······` with `winston.format.colorize(),·winston.format.simple()`", {"range": "578", "text": "579"}, "Replace `⏎····new·winston.transports.File({·filename:·'logs/error.log',·level:·'error'·})⏎··` with `new·winston.transports.File({·filename:·'logs/error.log',·level:·'error'·})`", {"range": "580", "text": "581"}, "Replace `⏎····new·winston.transports.File({·filename:·'logs/combined.log'·})⏎··` with `new·winston.transports.File({·filename:·'logs/combined.log'·})`", {"range": "582", "text": "583"}, "Replace `private·level:·LogLevel·=·LogLevel.INFO,·context?:·string` with `⏎····private·level:·LogLevel·=·LogLevel.INFO,⏎····context?:·string,⏎··`", {"range": "584", "text": "585"}, [913, 931], "\n        data: { session },\n     ", [970, 976], "", [1060, 1066], [1096, 1102], [1296, 1302], [1505, 1511], [1576, 1582], [1614, 1618], {"desc": "586", "fix": "587"}, [1836, 1840], [2005, 2009], [2156, 2160], [2307, 2311], [2470, 2474], [4521, 4607], "\n              href=\"/admin/vendors\"\n              className=\"mt-2 inline-block text-blue-600 hover:text-blue-800\"\n            ", [4677, 4687], [4833, 4845], "\n              Environments\n            ", [4962, 5053], "\n              href=\"/admin/environments\"\n              className=\"mt-2 inline-block text-blue-600 hover:text-blue-800\"\n            ", [5128, 5138], [5401, 5486], "\n              href=\"/admin/assets\"\n              className=\"mt-2 inline-block text-blue-600 hover:text-blue-800\"\n            ", [5555, 5565], [5828, 5913], "\n              href=\"/admin/scenes\"\n              className=\"mt-2 inline-block text-blue-600 hover:text-blue-800\"\n            ", [5982, 5992], [6138, 6148], "\n              Blueprints\n            ", [6263, 6352], "\n              href=\"/admin/blueprints\"\n              className=\"mt-2 inline-block text-blue-600 hover:text-blue-800\"\n            ", [7143, 7155], [7519, 7531], [7895, 7907], [8279, 8291], [8662, 8674], [679, 697], [736, 742], [969, 977], [1159, 1163], [1181, 1185], {"desc": "588", "fix": "589"}, [1443, 1447], [1572, 1578], [1627, 1633], [1717, 1723], [1936, 1942], [2033, 2039], [2253, 2259], [2763, 2769], [3544, 3554], [3867, 3877], [4378, 4381], "e", [5136, 5139], [868, 886], [925, 931], [1015, 1021], [1051, 1057], [1251, 1257], [1460, 1466], [1516, 1522], [1554, 1558], [1576, 1580], {"desc": "590", "fix": "591"}, [1796, 1845], ".from('vendors').select('*')", [1861, 1865], [1957, 1961], [2163, 2167], [2190, 2194], [2231, 2277], ".from('vendors').delete()", [2292, 2296], [2519, 2523], [2634, 2645], "\n    vendor =>\n  ", [2717, 2780], "  vendor.api_key.toLowerCase().includes(searchTerm.toLowerCase()),", [4619, 4622], [4845, 4855], [5553, 5656], "\n                    scope=\"col\"\n                    className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\"\n                  ", [5728, 5831], [5906, 6009], [6084, 6187], [6391, 6399], "vendor", [8244, 8287], "\n                ? 'No vendors found matching your search.'\n               ", [592, 594], [859, 877], [916, 922], [1006, 1012], [1042, 1048], [1242, 1248], [1451, 1457], [1519, 1525], [1557, 1561], {"desc": "588", "fix": "592"}, [1855, 1859], [1981, 1987], [2072, 2078], [2301, 2307], [2356, 2362], [4814, 4817], [5176, 5252], "The name of the vendor organization.", [5720, 5723], [26, 27], [613, 615], [717, 721], [815, 819], [904, 908], [1026, 1030], [1335, 1335], ",", [1324, 1343], "redisStatus,\n      }", [1343, 1343], [1342, 1349], "},\n    }", [1351, 1355], [1441, 1445], [1565, 1569], [1696, 1696], [1689, 1702], "message,\n    }", [2004, 2008], [2037, 2041], [2105, 2105], [2101, 2111], "}ms`,\n    }", [2189, 2193], [2255, 2255], [2248, 2261], [2619, 2623], [2741, 2745], [2809, 2809], [2805, 2815], [2893, 2897], [2959, 2959], [2952, 2965], [3319, 3319], [3314, 3325], "'5ms',\n    }", [3327, 3331], [3830, 3834], [3896, 3896], [3889, 3902], [21, 22], [513, 513], [512, 515], "],\n}", [753, 753], [752, 755], [989, 989], [988, 991], [1131, 1131], [1130, 1133], [1277, 1277], [1276, 1279], [1465, 1465], [1451, 1467], "cacheMissTotal,\n}", [1638, 1640], [1753, 1757], [1823, 1827], [1934, 1938], [2022, 2022], [2015, 2028], [298, 359], "winston.format.timestamp(), winston.format.json()", [530, 604], "winston.format.colorize(), winston.format.simple()", [720, 803], "new winston.transports.File({ filename: 'logs/error.log', level: 'error' })", [819, 889], "new winston.transports.File({ filename: 'logs/combined.log' })", [1001, 1058], "\n    private level: LogLevel = LogLevel.INFO,\n    context?: string,\n  ", "Update the dependencies array to be: [loadDashboardStats, router, supabase]", {"range": "593", "text": "594"}, "Update the dependencies array to be: [router, supabase]", {"range": "595", "text": "596"}, "Update the dependencies array to be: [loadVendors, router, supabase]", {"range": "597", "text": "598"}, {"range": "599", "text": "596"}, [1641, 1643], "[loadDashboardStats, router, supabase]", [1320, 1322], "[router, supabase]", [1715, 1717], "[loadVendors, router, supabase]", [1584, 1586]]