-- Migration: Enhance Asset Bundling
-- Description: This migration adds tables and functions for enhanced asset bundling and optimization

-- Create asset_bundle_optimizations table
CREATE TABLE IF NOT EXISTS asset_bundle_optimizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bundle_id UUID NOT NULL,
  original_size INTEGER NOT NULL,
  optimized_size INTEGER NOT NULL,
  compression_ratio FLOAT NOT NULL,
  strategy TEXT NOT NULL,
  optimization_level INTEGER NOT NULL,
  asset_optimizations JSONB NOT NULL DEFAULT '[]',
  load_order TEXT[] NOT NULL DEFAULT '{}',
  preload_hints JSONB,
  metadata JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_asset_bundle_optimizations_bundle
    FOREIGN KEY (bundle_id)
    REFERENCES asset_bundles(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_bundle_optimization
    UNIQUE (bundle_id)
);

-- Create index on asset_bundle_optimizations
CREATE INDEX idx_asset_bundle_optimizations_bundle_id ON asset_bundle_optimizations(bundle_id);

-- Add columns to asset_bundles table
ALTER TABLE asset_bundles
  ADD COLUMN IF NOT EXISTS is_current BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS previous_version TEXT,
  ADD COLUMN IF NOT EXISTS delta_info JSONB;

-- Create asset_bundle_versions table to track version history
CREATE TABLE IF NOT EXISTS asset_bundle_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bundle_id UUID NOT NULL,
  version TEXT NOT NULL,
  hash TEXT NOT NULL,
  asset_ids UUID[] NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_asset_bundle_versions_bundle
    FOREIGN KEY (bundle_id)
    REFERENCES asset_bundles(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_bundle_version
    UNIQUE (bundle_id, version)
);

-- Create index on asset_bundle_versions
CREATE INDEX idx_asset_bundle_versions_bundle_id ON asset_bundle_versions(bundle_id);
CREATE INDEX idx_asset_bundle_versions_version ON asset_bundle_versions(bundle_id, version);

-- Create asset_bundle_deltas table to store delta information between versions
CREATE TABLE IF NOT EXISTS asset_bundle_deltas (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bundle_id UUID NOT NULL,
  base_version TEXT NOT NULL,
  target_version TEXT NOT NULL,
  added_assets UUID[] NOT NULL DEFAULT '{}',
  removed_assets UUID[] NOT NULL DEFAULT '{}',
  modified_assets UUID[] NOT NULL DEFAULT '{}',
  unchanged_assets UUID[] NOT NULL DEFAULT '{}',
  delta_size INTEGER NOT NULL,
  full_size INTEGER NOT NULL,
  compression_ratio FLOAT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_asset_bundle_deltas_bundle
    FOREIGN KEY (bundle_id)
    REFERENCES asset_bundles(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_bundle_delta
    UNIQUE (bundle_id, base_version, target_version)
);

-- Create index on asset_bundle_deltas
CREATE INDEX idx_asset_bundle_deltas_bundle_id ON asset_bundle_deltas(bundle_id);
CREATE INDEX idx_asset_bundle_deltas_versions ON asset_bundle_deltas(bundle_id, base_version, target_version);

-- Create function to update is_current flag when a new version is created
CREATE OR REPLACE FUNCTION update_bundle_current_version()
RETURNS TRIGGER AS $$
BEGIN
  -- Set is_current to false for all versions of this bundle
  UPDATE asset_bundles
  SET is_current = FALSE
  WHERE id = NEW.id;
  
  -- Set is_current to true for the new version
  NEW.is_current = TRUE;
  
  -- Set previous_version
  SELECT version INTO NEW.previous_version
  FROM asset_bundles
  WHERE id = NEW.id
  ORDER BY created_at DESC
  LIMIT 1;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update is_current flag
CREATE TRIGGER update_bundle_current_version_trigger
BEFORE UPDATE OF version ON asset_bundles
FOR EACH ROW
EXECUTE FUNCTION update_bundle_current_version();

-- Create function to store version history when a bundle is updated
CREATE OR REPLACE FUNCTION store_bundle_version_history()
RETURNS TRIGGER AS $$
BEGIN
  -- Only store history if version changed
  IF OLD.version IS DISTINCT FROM NEW.version THEN
    -- Store the old version in history
    INSERT INTO asset_bundle_versions (
      bundle_id,
      version,
      hash,
      asset_ids,
      created_at
    ) VALUES (
      OLD.id,
      OLD.version,
      OLD.hash,
      OLD.asset_ids,
      OLD.created_at
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to store version history
CREATE TRIGGER store_bundle_version_history_trigger
BEFORE UPDATE OF version ON asset_bundles
FOR EACH ROW
EXECUTE FUNCTION store_bundle_version_history();

-- Create function to get bundle delta information
CREATE OR REPLACE FUNCTION get_bundle_delta_info(
  p_bundle_id UUID,
  p_base_version TEXT,
  p_target_version TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_target_version TEXT;
  v_delta JSONB;
BEGIN
  -- If target version is not specified, use the current version
  IF p_target_version IS NULL THEN
    SELECT version INTO v_target_version
    FROM asset_bundles
    WHERE id = p_bundle_id AND is_current = TRUE;
  ELSE
    v_target_version := p_target_version;
  END IF;
  
  -- Get delta information
  SELECT jsonb_build_object(
    'bundle_id', bundle_id,
    'base_version', base_version,
    'target_version', target_version,
    'added_assets', added_assets,
    'removed_assets', removed_assets,
    'modified_assets', modified_assets,
    'unchanged_assets', unchanged_assets,
    'delta_size', delta_size,
    'full_size', full_size,
    'compression_ratio', compression_ratio,
    'created_at', created_at
  ) INTO v_delta
  FROM asset_bundle_deltas
  WHERE bundle_id = p_bundle_id
    AND base_version = p_base_version
    AND target_version = v_target_version;
  
  RETURN v_delta;
END;
$$;

-- Add RLS policies for asset_bundle_optimizations
ALTER TABLE asset_bundle_optimizations ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see optimizations for bundles they have access to
CREATE POLICY asset_bundle_optimizations_select_policy
  ON asset_bundle_optimizations
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM asset_bundles b
      WHERE b.id = bundle_id AND (
        b.vendor_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM vendors v
          WHERE v.id = b.vendor_id AND v.user_id = auth.uid()
        ) OR
        auth.jwt() ->> 'role' = 'admin'
      )
    )
  );

-- Add RLS policies for asset_bundle_versions
ALTER TABLE asset_bundle_versions ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see versions for bundles they have access to
CREATE POLICY asset_bundle_versions_select_policy
  ON asset_bundle_versions
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM asset_bundles b
      WHERE b.id = bundle_id AND (
        b.vendor_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM vendors v
          WHERE v.id = b.vendor_id AND v.user_id = auth.uid()
        ) OR
        auth.jwt() ->> 'role' = 'admin'
      )
    )
  );

-- Add RLS policies for asset_bundle_deltas
ALTER TABLE asset_bundle_deltas ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see deltas for bundles they have access to
CREATE POLICY asset_bundle_deltas_select_policy
  ON asset_bundle_deltas
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM asset_bundles b
      WHERE b.id = bundle_id AND (
        b.vendor_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM vendors v
          WHERE v.id = b.vendor_id AND v.user_id = auth.uid()
        ) OR
        auth.jwt() ->> 'role' = 'admin'
      )
    )
  );
