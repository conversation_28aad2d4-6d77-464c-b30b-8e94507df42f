/**
 * API Gateway Server
 * Entry point for all API requests with routing to microservices
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { logger } from '../../shared/utils/logger';
import { rateLimitMiddleware } from '../middleware/rate-limit';
import { authMiddleware } from '../middleware/auth';
import { validationMiddleware } from '../middleware/validation';

const app = express();
const PORT = process.env.SERVICE_PORT || 3000;

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
app.use(rateLimitMiddleware);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    service: 'api-gateway',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Service proxy configurations
const services = {
  '/api/assets': {
    target: process.env.ASSET_SERVICE_URL || 'http://asset-service:3001',
    changeOrigin: true,
    pathRewrite: { '^/api/assets': '' }
  },
  '/api/scenes': {
    target: process.env.SCENE_SERVICE_URL || 'http://scene-service:3002',
    changeOrigin: true,
    pathRewrite: { '^/api/scenes': '' }
  },
  '/api/blueprints': {
    target: process.env.BLUEPRINT_SERVICE_URL || 'http://blueprint-service:3003',
    changeOrigin: true,
    pathRewrite: { '^/api/blueprints': '' }
  },
  '/api/llm': {
    target: process.env.LLM_SERVICE_URL || 'http://llm-service:3004',
    changeOrigin: true,
    pathRewrite: { '^/api/llm': '' }
  },
  '/api/auth': {
    target: process.env.AUTH_SERVICE_URL || 'http://auth-service:3005',
    changeOrigin: true,
    pathRewrite: { '^/api/auth': '' }
  },
  '/api/analytics': {
    target: process.env.ANALYTICS_SERVICE_URL || 'http://analytics-service:3006',
    changeOrigin: true,
    pathRewrite: { '^/api/analytics': '' }
  },
  '/api/monitoring': {
    target: process.env.MONITORING_SERVICE_URL || 'http://monitoring-service:3007',
    changeOrigin: true,
    pathRewrite: { '^/api/monitoring': '' }
  }
};

// Setup service proxies
Object.entries(services).forEach(([path, config]) => {
  app.use(path, createProxyMiddleware(config));
  logger.info(`Proxy configured for ${path} -> ${config.target}`);
});

// Error handling middleware
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Gateway error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Start server
app.listen(PORT, () => {
  logger.info(`API Gateway running on port ${PORT}`);
  logger.info('Environment:', process.env.NODE_ENV);
  logger.info('Service URLs:', services);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export default app;
