# [ADR-0003] Edge Caching Implementation

## Status

Accepted

## Context

The MVS-VR platform needs to deliver content efficiently to users across different geographical locations. As the platform scales, we need to ensure low latency and high availability while minimizing server load. We need a strategy for caching content at the edge to improve performance and reduce costs.

## Decision

We will implement edge caching using a combination of:

1. Cache-Control headers for HTTP caching
2. A CDN (Cloudflare) for edge caching
3. Stale-while-revalidate caching strategy for API responses

## Consequences

### Positive

- Reduced latency for users by serving content from edge locations
- Decreased server load by offloading requests to the CDN
- Improved availability during traffic spikes
- Reduced bandwidth costs
- Better user experience with faster page loads

### Negative

- Additional complexity in cache invalidation
- Potential for stale data if not properly managed
- Need for careful configuration to avoid caching sensitive data

### Neutral

- Different caching strategies for different types of content
- Need for monitoring and metrics for cache performance

## Alternatives Considered

### Alternative 1: Server-Side Caching Only

Implement caching only at the server level using Redis or similar.

#### Pros

- Simpler implementation
- More control over cache invalidation
- No additional services required

#### Cons

- Higher latency for users far from the server
- Increased server load
- Less resilient to traffic spikes

### Alternative 2: Client-Side Caching Only

Rely on browser caching and service workers for caching.

#### Pros

- No server-side changes required
- Zero latency for cached content
- Works offline

#### Cons

- Limited to browser capabilities
- Less control over caching behavior
- Initial load still requires server request

## Related Decisions

- [ADR-0001] Use of GraphQL for API Layer
- [ADR-0002] GraphQL Persisted Queries

## Notes

We will implement edge caching with the following components:

1. Cache-Control middleware for setting appropriate cache headers:
   - Static assets: `public, max-age=604800, immutable`
   - API responses: `public, max-age=60, stale-while-revalidate=3600`
   - HTML pages: `no-store, max-age=0`

2. Cloudflare configuration:
   - Page Rules for specific caching behaviors
   - Cache Everything for static assets
   - Respect Origin Headers for API responses

3. Cache invalidation strategy:
   - Versioned URLs for static assets
   - Cache purge API for content updates
   - Time-based expiration for API responses

Example middleware implementation:

```javascript
function cacheControlMiddleware(req, res, next) {
  // Static assets: cache for 1 week
  if (req.path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg)$/)) {
    res.setHeader('Cache-Control', 'public, max-age=604800, immutable');
  }
  // API responses: stale-while-revalidate for 1 hour
  else if (req.path.startsWith('/api/')) {
    res.setHeader('Cache-Control', 'public, max-age=60, stale-while-revalidate=3600');
  }
  next();
}
```
