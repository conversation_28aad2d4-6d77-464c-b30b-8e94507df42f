import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../shared/utils/logger';
import { errorHandler } from '../../../shared/utils/error-handler';

// Define the response schema
const BootstrapResponseSchema = z.object({
  bootstrap_id: z.string().uuid(),
  vendor_id: z.string().uuid(),
  environment_key: z.string(),
  configurations: z.array(
    z.object({
      type: z.string(),
      url: z.string().url(),
      hash: z.string(),
      version: z.string(),
      required: z.boolean(),
    }),
  ),
  settings: z.record(z.any()),
});

// Define the query parameters schema
const QueryParamsSchema = z.object({
  vendor_id: z.string().uuid(),
  environment_key: z.string(),
});

// Define the header schema
const HeadersSchema = z.object({
  'x-client-version': z.string().optional(),
  'x-cached-hashes': z.string().optional(),
});

/**
 * Bootstrap API endpoint
 *
 * This endpoint returns the bootstrap configuration for a vendor and environment.
 * It includes the configurations that need to be downloaded by the client.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse(req.query);
    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Validate headers
    const headersResult = HeadersSchema.safeParse(req.headers);
    if (!headersResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid headers', details: headersResult.error.format() });
    }

    // Extract parameters
    const { vendor_id, environment_key } = queryResult.data;
    const client_version = headersResult.data['x-client-version'];
    const cached_hashes = headersResult.data['x-cached-hashes']
      ? JSON.parse(headersResult.data['x-cached-hashes'])
      : [];

    // Log the request
    logger.info('Bootstrap request', {
      vendor_id,
      environment_key,
      client_version,
      cached_hashes_count: cached_hashes.length,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get vendor
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('id, name, api_key')
      .eq('id', vendor_id)
      .single();

    if (vendorError) {
      logger.error('Error fetching vendor', { error: vendorError, vendor_id });
      return res.status(500).json({ error: 'Error fetching vendor' });
    }

    if (!vendor) {
      return res.status(404).json({ error: 'Vendor not found' });
    }

    // Get environment
    const { data: environment, error: environmentError } = await supabase
      .from('environments')
      .select('id, key, name')
      .eq('vendor_id', vendor_id)
      .eq('key', environment_key)
      .single();

    if (environmentError) {
      logger.error('Error fetching environment', {
        error: environmentError,
        vendor_id,
        environment_key,
      });
      return res.status(500).json({ error: 'Error fetching environment' });
    }

    if (!environment) {
      return res.status(404).json({ error: 'Environment not found' });
    }

    // Get bootstrap configuration
    const { data: bootstrapConfig, error: bootstrapError } = await supabase
      .from('bootstrap_configs')
      .select('id, vendor_id, environment_key, configuration_ids, settings')
      .eq('vendor_id', vendor_id)
      .eq('environment_key', environment_key)
      .single();

    if (bootstrapError) {
      logger.error('Error fetching bootstrap config', {
        error: bootstrapError,
        vendor_id,
        environment_key,
      });
      return res.status(500).json({ error: 'Error fetching bootstrap configuration' });
    }

    if (!bootstrapConfig) {
      return res.status(404).json({ error: 'Bootstrap configuration not found' });
    }

    // Get configurations
    const { data: configurations, error: configError } = await supabase
      .from('configurations')
      .select('id, type, url, hash, version, required')
      .in('id', bootstrapConfig.configuration_ids);

    if (configError) {
      logger.error('Error fetching configurations', {
        error: configError,
        configuration_ids: bootstrapConfig.configuration_ids,
      });
      return res.status(500).json({ error: 'Error fetching configurations' });
    }

    // Filter configurations based on cached hashes
    const updatedConfigurations = configurations.filter(config => {
      const cachedConfig = cached_hashes.find((hash: any) => hash.id === config.id);
      return !cachedConfig || cachedConfig.hash !== config.hash;
    });

    // Prepare response
    const response = {
      bootstrap_id: bootstrapConfig.id,
      vendor_id: bootstrapConfig.vendor_id,
      environment_key: bootstrapConfig.environment_key,
      configurations: updatedConfigurations.map(config => ({
        type: config.type,
        url: config.url,
        hash: config.hash,
        version: config.version,
        required: config.required,
      })),
      settings: bootstrapConfig.settings,
    };

    // Validate response
    const responseResult = BootstrapResponseSchema.safeParse(response);
    if (!responseResult.success) {
      logger.error('Invalid response format', { error: responseResult.error.format(), response });
      return res.status(500).json({ error: 'Invalid response format' });
    }

    // Log success
    logger.info('Bootstrap response', {
      vendor_id,
      environment_key,
      configurations_count: updatedConfigurations.length,
    });

    // Return response
    return res.status(200).json(response);
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
