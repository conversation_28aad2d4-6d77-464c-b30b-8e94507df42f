/**
 * UE Blueprint Compatibility
 *
 * This module provides endpoints for validating and managing blueprints
 * for Unreal Engine 5.4+ compatibility.
 */

import { Router } from 'express';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { z } from 'zod';
import { BlueprintService } from '../../services/blueprint-service';

// Create router
const router = Router();

// Define UE version schema
const UEVersionSchema = z.object({
  major: z.number(),
  minor: z.number(),
  patch: z.number(),
});

// Define blueprint validation schema
const BlueprintValidationSchema = z.object({
  blueprint_id: z.string().uuid(),
  ue_version: UEVersionSchema,
});

// Define blueprint compatibility check schema
const BlueprintCompatibilityCheckSchema = z.object({
  blueprint_id: z.string().uuid().optional(),
  blueprint_script: z.record(z.any()).optional(),
  ue_version: UEVersionSchema,
});

/**
 * Validate blueprint for UE compatibility
 *
 * @param req Request object
 * @param res Response object
 */
async function validateBlueprint(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = BlueprintValidationSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract blueprint ID and UE version
    const { blueprint_id, ue_version } = bodyResult.data;

    // Log the request
    logger.info('Blueprint validation request', {
      blueprint_id,
      ue_version,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get blueprint
    const { data: blueprint, error: blueprintError } = await supabase
      .from('blueprints')
      .select('id, vendor_id, name, description, tags, script, version, created_at, updated_at')
      .eq('id', blueprint_id)
      .single();

    if (blueprintError) {
      logger.error('Error fetching blueprint', { error: blueprintError, blueprint_id });
      return res.status(500).json({ error: 'Error fetching blueprint' });
    }

    if (!blueprint) {
      return res.status(404).json({ error: 'Blueprint not found' });
    }

    // Validate blueprint for UE compatibility
    const validationResult = validateBlueprintForUE(blueprint.script, ue_version);

    // Return validation result
    return res.status(200).json({
      valid: validationResult.valid,
      issues: validationResult.issues,
      warnings: validationResult.warnings,
      blueprint_id,
      ue_version,
    });
  } catch (error) {
    return errorHandler(error, req, res);
  }
}

/**
 * Check blueprint compatibility with UE version
 *
 * @param req Request object
 * @param res Response object
 */
async function checkBlueprintCompatibility(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = BlueprintCompatibilityCheckSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract blueprint ID, script, and UE version
    const { blueprint_id, blueprint_script, ue_version } = bodyResult.data;

    // Log the request
    logger.info('Blueprint compatibility check', {
      blueprint_id,
      has_script: !!blueprint_script,
      ue_version,
    });

    // If blueprint_id is provided, fetch the blueprint
    let script = blueprint_script;
    if (blueprint_id && !script) {
      // Create Supabase client
      const supabase = createServerSupabaseClient({ req, res });

      // Authenticate request
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        return res.status(401).json({ error: 'Unauthorized' });
      }

      // Get blueprint
      const { data: blueprint, error: blueprintError } = await supabase
        .from('blueprints')
        .select('script')
        .eq('id', blueprint_id)
        .single();

      if (blueprintError) {
        logger.error('Error fetching blueprint', { error: blueprintError, blueprint_id });
        return res.status(500).json({ error: 'Error fetching blueprint' });
      }

      if (!blueprint) {
        return res.status(404).json({ error: 'Blueprint not found' });
      }

      script = blueprint.script;
    }

    // Validate blueprint for UE compatibility
    if (!script) {
      return res.status(400).json({ error: 'Blueprint script is required' });
    }

    const compatibilityResult = checkBlueprintCompatibilityWithUE(script, ue_version);

    // Return compatibility result
    return res.status(200).json({
      compatible: compatibilityResult.compatible,
      issues: compatibilityResult.issues,
      warnings: compatibilityResult.warnings,
      required_features: compatibilityResult.required_features,
      blueprint_id,
      ue_version,
    });
  } catch (error) {
    return errorHandler(error, req, res);
  }
}

/**
 * Validate blueprint script for UE compatibility
 *
 * @param script Blueprint script
 * @param version UE version
 * @returns Validation result
 */
function validateBlueprintForUE(
  script: Record<string, any>,
  version: z.infer<typeof UEVersionSchema>,
): {
  valid: boolean;
  issues: Array<{ code: string; message: string; path: string }>;
  warnings: Array<{ code: string; message: string; path: string }>;
} {
  const issues: Array<{ code: string; message: string; path: string }> = [];
  const warnings: Array<{ code: string; message: string; path: string }> = [];

  // Check for required fields
  if (!script.actions || !Array.isArray(script.actions)) {
    issues.push({
      code: 'MISSING_ACTIONS',
      message: 'Blueprint script must have an actions array',
      path: 'actions',
    });
  }

  if (!script.triggers || !Array.isArray(script.triggers)) {
    issues.push({
      code: 'MISSING_TRIGGERS',
      message: 'Blueprint script must have a triggers array',
      path: 'triggers',
    });
  }

  // Check for UE 5.4+ specific features
  if (version.major === 5 && version.minor >= 4) {
    // Check for action dependencies
    if (script.actions) {
      script.actions.forEach((action: any, index: number) => {
        if (!action.id) {
          issues.push({
            code: 'MISSING_ACTION_ID',
            message: `Action at index ${index} is missing an ID`,
            path: `actions[${index}].id`,
          });
        }

        // Check for UE 5.4+ specific action types
        if (action.type === 'blueprint_injection' && !action.blueprint_class) {
          issues.push({
            code: 'MISSING_BLUEPRINT_CLASS',
            message: `Blueprint injection action at index ${index} is missing a blueprint_class`,
            path: `actions[${index}].blueprint_class`,
          });
        }
      });
    }

    // Check for trigger dependencies
    if (script.triggers) {
      script.triggers.forEach((trigger: any, index: number) => {
        if (!trigger.id) {
          issues.push({
            code: 'MISSING_TRIGGER_ID',
            message: `Trigger at index ${index} is missing an ID`,
            path: `triggers[${index}].id`,
          });
        }

        // Check for UE 5.4+ specific trigger types
        if (trigger.type === 'blueprint_event' && !trigger.event_name) {
          issues.push({
            code: 'MISSING_EVENT_NAME',
            message: `Blueprint event trigger at index ${index} is missing an event_name`,
            path: `triggers[${index}].event_name`,
          });
        }
      });
    }
  }

  return {
    valid: issues.length === 0,
    issues,
    warnings,
  };
}

/**
 * Check blueprint compatibility with UE version
 *
 * @param script Blueprint script
 * @param version UE version
 * @returns Compatibility result
 */
function checkBlueprintCompatibilityWithUE(
  script: Record<string, any>,
  version: z.infer<typeof UEVersionSchema>,
): {
  compatible: boolean;
  issues: Array<{ code: string; message: string; path: string }>;
  warnings: Array<{ code: string; message: string; path: string }>;
  required_features: string[];
} {
  const issues: Array<{ code: string; message: string; path: string }> = [];
  const warnings: Array<{ code: string; message: string; path: string }> = [];
  const required_features: string[] = [];

  // Check for UE 5.4+ specific features
  if (version.major === 5 && version.minor >= 4) {
    // Check for blueprint injection
    if (script.actions) {
      script.actions.forEach((action: any, index: number) => {
        if (action.type === 'blueprint_injection') {
          required_features.push('blueprint_injection_5_4');
        }
      });
    }
  }

  // Check for UE 4.x limitations
  if (version.major === 4) {
    // Check for unsupported features
    if (script.actions) {
      script.actions.forEach((action: any, index: number) => {
        if (action.type === 'blueprint_injection') {
          issues.push({
            code: 'UNSUPPORTED_ACTION_TYPE',
            message: `Blueprint injection action at index ${index} is not supported in UE ${version.major}.${version.minor}`,
            path: `actions[${index}].type`,
          });
        }
      });
    }
  }

  return {
    compatible: issues.length === 0,
    issues,
    warnings,
    required_features,
  };
}

// Register routes
router.post('/validate', validateBlueprint);
router.post('/check-compatibility', checkBlueprintCompatibility);

// Export router
export default router;
