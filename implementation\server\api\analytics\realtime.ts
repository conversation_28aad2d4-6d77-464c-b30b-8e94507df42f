/**
 * Real-time Analytics API
 *
 * This file contains API endpoints for real-time analytics data.
 */

import { Request, Response } from 'express';
import { supabase } from '../../lib/supabase';
import { logger } from '../../lib/logger';
import { authenticate } from '../../middleware/auth';
import { rateLimiter } from '../../middleware/rate-limiter';
import { validateRequest } from '../../middleware/validate-request';
import { Router } from 'express';

// Create router
const router = Router();

/**
 * Get real-time visitor data
 *
 * @param req - Request
 * @param res - Response
 */
export const getRealtimeVisitors = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, showroom_id } = req.query;

    // Validate vendor ID
    if (!vendor_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_VENDOR_ID',
          message: 'Vendor ID is required',
        },
      });
      return;
    }

    // Build query
    let query = supabase
      .from('active_sessions')
      .select('id, user_id, showroom_id, started_at, last_activity_at, device_type, location')
      .eq('vendor_id', vendor_id);

    // Add showroom filter if provided
    if (showroom_id) {
      query = query.eq('showroom_id', showroom_id);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error getting real-time visitors', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting real-time visitors',
        },
      });
      return;
    }

    // Return data
    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getRealtimeVisitors', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get interaction heatmap data
 *
 * @param req - Request
 * @param res - Response
 */
export const getInteractionHeatmap = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, showroom_id, view_type, start_date, end_date } = req.query;

    // Validate required parameters
    if (!vendor_id || !showroom_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID and showroom ID are required',
        },
      });
      return;
    }

    // Build query
    let query = supabase
      .from('interaction_events')
      .select(
        'id, showroom_id, product_id, position_x, position_y, position_z, event_type, created_at',
      )
      .eq('vendor_id', vendor_id)
      .eq('showroom_id', showroom_id);

    // Add date filters if provided
    if (start_date) {
      query = query.gte('created_at', start_date);
    }

    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error getting interaction heatmap data', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting interaction heatmap data',
        },
      });
      return;
    }

    // Process data based on view type
    const viewType = (view_type as string) || 'top';
    const processedData = processHeatmapData(data, viewType);

    // Return data
    res.status(200).json({
      success: true,
      data: processedData,
    });
  } catch (error) {
    logger.error('Error in getInteractionHeatmap', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Process heatmap data based on view type
 *
 * @param data - Raw interaction data
 * @param viewType - View type (top, front, side, 3d)
 * @returns Processed heatmap data
 */
const processHeatmapData = (data: any[], viewType: string) => {
  // Process data based on view type
  switch (viewType) {
    case 'top':
      // For top view, use x and z coordinates
      return data.map(item => ({
        x: item.position_x,
        y: item.position_z,
        value: 1,
        id: item.id,
        event_type: item.event_type,
        product_id: item.product_id,
        created_at: item.created_at,
      }));
    case 'front':
      // For front view, use x and y coordinates
      return data.map(item => ({
        x: item.position_x,
        y: item.position_y,
        value: 1,
        id: item.id,
        event_type: item.event_type,
        product_id: item.product_id,
        created_at: item.created_at,
      }));
    case 'side':
      // For side view, use z and y coordinates
      return data.map(item => ({
        x: item.position_z,
        y: item.position_y,
        value: 1,
        id: item.id,
        event_type: item.event_type,
        product_id: item.product_id,
        created_at: item.created_at,
      }));
    case '3d':
      // For 3D view, use all coordinates
      return data.map(item => ({
        x: item.position_x,
        y: item.position_y,
        z: item.position_z,
        value: 1,
        id: item.id,
        event_type: item.event_type,
        product_id: item.product_id,
        created_at: item.created_at,
      }));
    default:
      // Default to top view
      return data.map(item => ({
        x: item.position_x,
        y: item.position_z,
        value: 1,
        id: item.id,
        event_type: item.event_type,
        product_id: item.product_id,
        created_at: item.created_at,
      }));
  }
};

// Register routes
router.get(
  '/realtime/visitors',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 30 }),
  getRealtimeVisitors,
);
router.get(
  '/realtime/heatmap',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 20 }),
  getInteractionHeatmap,
);

export default router;
