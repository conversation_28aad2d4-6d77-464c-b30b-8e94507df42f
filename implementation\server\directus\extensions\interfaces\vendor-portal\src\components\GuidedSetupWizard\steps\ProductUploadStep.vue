<template>
  <wizard-step
    title="Product Upload"
    description="Upload and configure your products for display in the virtual showroom."
    :step-data="stepData"
    :validation-schema="validationSchema"
    :help-tips="helpTips"
    @update:step-data="updateStepData"
    @validate="validateStep"
  >
    <div class="product-upload-form">
      <div class="form-section">
        <h3 class="section-title">Product Categories</h3>
        <p class="section-description">
          Create categories to organize your products in the virtual showroom.
        </p>
        
        <div v-if="localStepData.categories.length === 0" class="empty-state">
          <p>No categories added yet</p>
        </div>
        
        <div v-else class="categories-list">
          <div 
            v-for="(category, index) in localStepData.categories" 
            :key="index"
            class="category-item"
          >
            <div class="category-info">
              <div class="category-name">{{ category.name }}</div>
              <div class="category-description">{{ category.description }}</div>
            </div>
            
            <div class="category-actions">
              <button 
                class="edit-button"
                @click="editCategory(index)"
              >
                <i class="material-icons">edit</i>
              </button>
              
              <button 
                class="delete-button"
                @click="removeCategory(index)"
              >
                <i class="material-icons">delete</i>
              </button>
            </div>
          </div>
        </div>
        
        <div class="add-category">
          <button 
            class="add-button"
            @click="showAddCategoryForm = true"
          >
            <i class="material-icons">add</i>
            <span>Add Category</span>
          </button>
        </div>
        
        <div v-if="showAddCategoryForm" class="category-form">
          <h4 class="form-title">
            {{ isEditingCategory ? 'Edit Category' : 'Add Category' }}
          </h4>
          
          <div class="form-group">
            <label for="category-name">Category Name *</label>
            <input
              id="category-name"
              type="text"
              v-model="newCategory.name"
              placeholder="Enter category name"
            />
          </div>
          
          <div class="form-group">
            <label for="category-description">Description</label>
            <textarea
              id="category-description"
              v-model="newCategory.description"
              placeholder="Enter category description"
              rows="3"
            ></textarea>
          </div>
          
          <div class="form-actions">
            <button 
              class="cancel-button"
              @click="cancelCategoryForm"
            >
              Cancel
            </button>
            
            <button 
              class="save-button"
              @click="saveCategory"
              :disabled="!newCategory.name"
            >
              {{ isEditingCategory ? 'Update' : 'Add' }}
            </button>
          </div>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Product Upload</h3>
        <p class="section-description">
          Upload your products for display in the virtual showroom.
        </p>
        
        <div v-if="localStepData.products.length === 0" class="empty-state">
          <p>No products added yet</p>
        </div>
        
        <div v-else class="products-list">
          <div 
            v-for="(product, index) in localStepData.products" 
            :key="index"
            class="product-item"
          >
            <div class="product-image">
              <img 
                v-if="product.image && product.image.preview" 
                :src="product.image.preview" 
                alt="Product Image" 
              />
              <div v-else class="image-placeholder">
                <i class="material-icons">image</i>
              </div>
            </div>
            
            <div class="product-info">
              <div class="product-name">{{ product.name }}</div>
              <div class="product-category">
                {{ getCategoryName(product.categoryId) }}
              </div>
              <div class="product-price">{{ formatPrice(product.price) }}</div>
            </div>
            
            <div class="product-actions">
              <button 
                class="edit-button"
                @click="editProduct(index)"
              >
                <i class="material-icons">edit</i>
              </button>
              
              <button 
                class="delete-button"
                @click="removeProduct(index)"
              >
                <i class="material-icons">delete</i>
              </button>
            </div>
          </div>
        </div>
        
        <div class="add-product">
          <button 
            class="add-button"
            @click="showAddProductForm = true"
          >
            <i class="material-icons">add</i>
            <span>Add Product</span>
          </button>
        </div>
        
        <div v-if="showAddProductForm" class="product-form">
          <h4 class="form-title">
            {{ isEditingProduct ? 'Edit Product' : 'Add Product' }}
          </h4>
          
          <div class="form-group">
            <label for="product-name">Product Name *</label>
            <input
              id="product-name"
              type="text"
              v-model="newProduct.name"
              placeholder="Enter product name"
            />
          </div>
          
          <div class="form-group">
            <label for="product-category">Category *</label>
            <select
              id="product-category"
              v-model="newProduct.categoryId"
            >
              <option value="">Select a category</option>
              <option 
                v-for="(category, index) in localStepData.categories" 
                :key="index"
                :value="category.id"
              >
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="product-description">Description</label>
            <textarea
              id="product-description"
              v-model="newProduct.description"
              placeholder="Enter product description"
              rows="3"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label for="product-price">Price *</label>
            <input
              id="product-price"
              type="number"
              v-model.number="newProduct.price"
              placeholder="Enter product price"
              min="0"
              step="0.01"
            />
          </div>
          
          <div class="form-group">
            <label>Product Image</label>
            <div class="image-upload">
              <div 
                class="image-preview" 
                :class="{ 'has-image': newProduct.image && newProduct.image.preview }"
              >
                <img 
                  v-if="newProduct.image && newProduct.image.preview" 
                  :src="newProduct.image.preview" 
                  alt="Product Image Preview" 
                />
                <div v-else class="image-placeholder">
                  <i class="material-icons">image</i>
                  <span>No image uploaded</span>
                </div>
              </div>
              
              <div class="image-actions">
                <label class="upload-button">
                  <input 
                    type="file" 
                    accept="image/*" 
                    @change="handleProductImageUpload" 
                    hidden
                  />
                  <i class="material-icons">upload</i>
                  <span>Upload Image</span>
                </label>
                
                <button 
                  v-if="newProduct.image && newProduct.image.preview" 
                  class="remove-button"
                  @click="removeProductImage"
                >
                  <i class="material-icons">delete</i>
                  <span>Remove</span>
                </button>
              </div>
            </div>
          </div>
          
          <div class="form-actions">
            <button 
              class="cancel-button"
              @click="cancelProductForm"
            >
              Cancel
            </button>
            
            <button 
              class="save-button"
              @click="saveProduct"
              :disabled="!isProductFormValid"
            >
              {{ isEditingProduct ? 'Update' : 'Add' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </wizard-step>
</template>

<script>
import WizardStep from '../WizardStep.vue';

export default {
  name: 'ProductUploadStep',
  
  components: {
    WizardStep
  },
  
  props: {
    stepData: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      localStepData: {
        categories: [],
        products: [],
        ...this.stepData
      },
      validationSchema: {
        categories: {
          validate: (value) => {
            return value && value.length > 0 ? true : 'At least one category is required';
          },
          label: 'Categories'
        },
        products: {
          validate: (value) => {
            return value && value.length > 0 ? true : 'At least one product is required';
          },
          label: 'Products'
        }
      },
      helpTips: [
        {
          title: 'Categories',
          text: 'Create categories to organize your products in the virtual showroom. Each product must belong to a category.'
        },
        {
          title: 'Product Images',
          text: 'Upload high-quality product images for the best visual representation in the showroom. Recommended size: 1024x1024 pixels.'
        }
      ],
      showAddCategoryForm: false,
      isEditingCategory: false,
      editingCategoryIndex: -1,
      newCategory: {
        id: null,
        name: '',
        description: ''
      },
      showAddProductForm: false,
      isEditingProduct: false,
      editingProductIndex: -1,
      newProduct: {
        id: null,
        name: '',
        categoryId: '',
        description: '',
        price: 0,
        image: null
      }
    };
  },
  
  computed: {
    isProductFormValid() {
      return (
        this.newProduct.name.trim() !== '' &&
        this.newProduct.categoryId !== '' &&
        this.newProduct.price > 0
      );
    }
  },
  
  methods: {
    updateStepData(data) {
      this.localStepData = { ...data };
      this.$emit('update:step-data', this.localStepData);
    },
    
    validateStep(isValid) {
      this.$emit('validate', isValid);
    },
    
    getCategoryName(categoryId) {
      const category = this.localStepData.categories.find(cat => cat.id === categoryId);
      return category ? category.name : 'Uncategorized';
    },
    
    formatPrice(price) {
      return `$${price.toFixed(2)}`;
    },
    
    // Category methods
    editCategory(index) {
      const category = this.localStepData.categories[index];
      
      this.newCategory = { ...category };
      this.isEditingCategory = true;
      this.editingCategoryIndex = index;
      this.showAddCategoryForm = true;
    },
    
    removeCategory(index) {
      // Check if any products are using this category
      const categoryId = this.localStepData.categories[index].id;
      const productsUsingCategory = this.localStepData.products.filter(
        product => product.categoryId === categoryId
      );
      
      if (productsUsingCategory.length > 0) {
        alert(`Cannot delete this category because ${productsUsingCategory.length} product(s) are using it.`);
        return;
      }
      
      this.localStepData.categories.splice(index, 1);
      this.$emit('update:step-data', this.localStepData);
    },
    
    saveCategory() {
      if (!this.newCategory.name) return;
      
      if (this.isEditingCategory) {
        // Update existing category
        this.localStepData.categories[this.editingCategoryIndex] = { ...this.newCategory };
      } else {
        // Add new category
        this.localStepData.categories.push({
          id: `category_${Date.now()}`,
          name: this.newCategory.name,
          description: this.newCategory.description
        });
      }
      
      this.$emit('update:step-data', this.localStepData);
      this.cancelCategoryForm();
    },
    
    cancelCategoryForm() {
      this.showAddCategoryForm = false;
      this.isEditingCategory = false;
      this.editingCategoryIndex = -1;
      this.newCategory = {
        id: null,
        name: '',
        description: ''
      };
    },
    
    // Product methods
    editProduct(index) {
      const product = this.localStepData.products[index];
      
      this.newProduct = { ...product };
      this.isEditingProduct = true;
      this.editingProductIndex = index;
      this.showAddProductForm = true;
    },
    
    removeProduct(index) {
      this.localStepData.products.splice(index, 1);
      this.$emit('update:step-data', this.localStepData);
    },
    
    saveProduct() {
      if (!this.isProductFormValid) return;
      
      if (this.isEditingProduct) {
        // Update existing product
        this.localStepData.products[this.editingProductIndex] = { ...this.newProduct };
      } else {
        // Add new product
        this.localStepData.products.push({
          id: `product_${Date.now()}`,
          name: this.newProduct.name,
          categoryId: this.newProduct.categoryId,
          description: this.newProduct.description,
          price: this.newProduct.price,
          image: this.newProduct.image
        });
      }
      
      this.$emit('update:step-data', this.localStepData);
      this.cancelProductForm();
    },
    
    cancelProductForm() {
      this.showAddProductForm = false;
      this.isEditingProduct = false;
      this.editingProductIndex = -1;
      this.newProduct = {
        id: null,
        name: '',
        categoryId: '',
        description: '',
        price: 0,
        image: null
      };
    },
    
    handleProductImageUpload(event) {
      const file = event.target.files[0];
      
      if (!file) return;
      
      // Check file type
      if (!file.type.match('image.*')) {
        alert('Please upload an image file');
        return;
      }
      
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size should not exceed 5MB');
        return;
      }
      
      // Create file preview
      const reader = new FileReader();
      
      reader.onload = (e) => {
        this.newProduct.image = {
          file: file,
          preview: e.target.result,
          name: file.name,
          size: file.size,
          type: file.type
        };
      };
      
      reader.readAsDataURL(file);
    },
    
    removeProductImage() {
      this.newProduct.image = null;
    }
  }
};
</script>

<style scoped>
.product-upload-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--theme--foreground);
}

.section-description {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin: 0 0 16px 0;
}

.empty-state {
  padding: 24px;
  text-align: center;
  color: var(--theme--foreground-subdued);
  background-color: var(--theme--background);
  border-radius: 4px;
  margin-bottom: 16px;
}

.categories-list,
.products-list {
  margin-bottom: 16px;
}

.category-item,
.product-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--theme--background);
  border-radius: 4px;
  margin-bottom: 8px;
}

.category-info,
.product-info {
  flex: 1;
}

.category-name,
.product-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.category-description,
.product-category,
.product-price {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.product-item {
  display: flex;
  gap: 16px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.image-placeholder i {
  font-size: 24px;
}

.image-placeholder span {
  font-size: 10px;
  margin-top: 4px;
}

.category-actions,
.product-actions {
  display: flex;
  gap: 8px;
}

.edit-button,
.delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--primary);
}

.delete-button:hover {
  background-color: rgba(var(--theme--danger-rgb), 0.1);
  color: var(--theme--danger);
}

.add-category,
.add-product {
  margin-top: 16px;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px dashed var(--theme--border-color);
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.category-form,
.product-form {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--theme--background);
  border-radius: 4px;
}

.form-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--theme--foreground);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--theme--primary);
  outline: none;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.image-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.image-preview {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--theme--border-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.image-preview.has-image {
  border: none;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.upload-button,
.remove-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-button {
  background-color: var(--theme--primary);
  border: none;
  color: var(--theme--primary-background);
}

.remove-button {
  background-color: transparent;
  border: 1px solid var(--theme--danger);
  color: var(--theme--danger);
}

.upload-button:hover {
  background-color: var(--theme--primary-accent);
}

.remove-button:hover {
  background-color: rgba(var(--theme--danger-rgb), 0.1);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.cancel-button,
.save-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid var(--theme--border-color);
  color: var(--theme--foreground);
}

.save-button {
  background-color: var(--theme--primary);
  border: none;
  color: var(--theme--primary-background);
}

.cancel-button:hover {
  background-color: var(--theme--background-accent);
}

.save-button:hover {
  background-color: var(--theme--primary-accent);
}

.save-button:disabled {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
  cursor: not-allowed;
}
</style>
