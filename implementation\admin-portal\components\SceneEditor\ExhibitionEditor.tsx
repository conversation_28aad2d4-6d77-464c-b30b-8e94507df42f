import React, { useState } from 'react';
import { Box, Button, Typography, TextField, Grid, Paper, IconButton, Accordion, AccordionSummary, AccordionDetails, List, ListItem, ListItemText, ListItemSecondaryAction, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, ExpandMore as ExpandMoreIcon, Edit as EditIcon } from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import { Exhibition, Space } from '../../types/scene';
import VectorInput from './VectorInput';
import SpaceEditor from './SpaceEditor';

interface ExhibitionEditorProps {
  exhibition: Exhibition;
  onSave: (exhibition: Exhibition) => void;
  onCancel: () => void;
}

const ExhibitionEditor: React.FC<ExhibitionEditorProps> = ({ exhibition, onSave, onCancel }) => {
  const [editExhibition, setEditExhibition] = useState<Exhibition>({ ...exhibition });
  const [currentSpace, setCurrentSpace] = useState<Space | null>(null);
  const [showSpaceDialog, setShowSpaceDialog] = useState(false);

  const handleAddSpace = () => {
    const newSpace: Space = {
      id: uuidv4(),
      name: 'New Space',
      description: '',
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scale: [1, 1, 1],
      assets: [],
      properties: {}
    };
    setCurrentSpace(newSpace);
    setShowSpaceDialog(true);
  };

  const handleEditSpace = (space: Space) => {
    setCurrentSpace({ ...space });
    setShowSpaceDialog(true);
  };

  const handleDeleteSpace = (spaceId: string) => {
    setEditExhibition({
      ...editExhibition,
      spaces: editExhibition.spaces.filter(space => space.id !== spaceId)
    });
  };

  const handleSaveSpace = () => {
    if (!currentSpace) return;

    const updatedSpaces = [...editExhibition.spaces];
    const index = updatedSpaces.findIndex(space => space.id === currentSpace.id);

    if (index >= 0) {
      updatedSpaces[index] = currentSpace;
    } else {
      updatedSpaces.push(currentSpace);
    }

    setEditExhibition({
      ...editExhibition,
      spaces: updatedSpaces
    });

    setShowSpaceDialog(false);
    setCurrentSpace(null);
  };

  return (
    <Paper sx={{ p: 2, mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        {exhibition.id ? 'Edit Exhibition' : 'Add Exhibition'}
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <TextField
            label="Name"
            fullWidth
            value={editExhibition.name}
            onChange={(e) => setEditExhibition({...editExhibition, name: e.target.value})}
            margin="normal"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            label="Description"
            fullWidth
            value={editExhibition.description}
            onChange={(e) => setEditExhibition({...editExhibition, description: e.target.value})}
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <VectorInput
            label="Position"
            value={editExhibition.position}
            onChange={(value) => setEditExhibition({...editExhibition, position: value})}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <VectorInput
            label="Rotation"
            value={editExhibition.rotation}
            onChange={(value) => setEditExhibition({...editExhibition, rotation: value})}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <VectorInput
            label="Scale"
            value={editExhibition.scale}
            onChange={(value) => setEditExhibition({...editExhibition, scale: value})}
          />
        </Grid>
      </Grid>

      <Box sx={{ mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1">Spaces ({editExhibition.spaces.length})</Typography>
          <Button 
            size="small" 
            startIcon={<AddIcon />}
            onClick={handleAddSpace}
          >
            Add Space
          </Button>
        </Box>

        {editExhibition.spaces.length === 0 ? (
          <Typography variant="body2" color="textSecondary">
            No spaces added yet. Click "Add Space" to create your first space.
          </Typography>
        ) : (
          <List>
            {editExhibition.spaces.map((space) => (
              <Accordion key={space.id}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography>{space.name}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2">Description: {space.description}</Typography>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="body2">Position: [{space.position.join(', ')}]</Typography>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="body2">Rotation: [{space.rotation.join(', ')}]</Typography>
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Typography variant="body2">Scale: [{space.scale.join(', ')}]</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2">Assets: {space.assets.length}</Typography>
                    </Grid>
                  </Grid>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                    <Button 
                      color="error" 
                      startIcon={<DeleteIcon />}
                      onClick={() => handleDeleteSpace(space.id)}
                      sx={{ mr: 1 }}
                    >
                      Delete
                    </Button>
                    <Button 
                      variant="outlined" 
                      startIcon={<EditIcon />}
                      onClick={() => handleEditSpace(space)}
                    >
                      Edit
                    </Button>
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}
          </List>
        )}
      </Box>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button onClick={onCancel} sx={{ mr: 1 }}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={() => onSave(editExhibition)}
        >
          Save Exhibition
        </Button>
      </Box>

      <Dialog open={showSpaceDialog} onClose={() => setShowSpaceDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{currentSpace?.id ? 'Edit Space' : 'Add Space'}</DialogTitle>
        <DialogContent>
          {currentSpace && (
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Name"
                  fullWidth
                  value={currentSpace.name}
                  onChange={(e) => setCurrentSpace({...currentSpace, name: e.target.value})}
                  margin="normal"
                  required
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Description"
                  fullWidth
                  value={currentSpace.description}
                  onChange={(e) => setCurrentSpace({...currentSpace, description: e.target.value})}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <VectorInput
                  label="Position"
                  value={currentSpace.position}
                  onChange={(value) => setCurrentSpace({...currentSpace, position: value})}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <VectorInput
                  label="Rotation"
                  value={currentSpace.rotation}
                  onChange={(value) => setCurrentSpace({...currentSpace, rotation: value})}
                />
              </Grid>
              <Grid item xs={12} md={4}>
                <VectorInput
                  label="Scale"
                  value={currentSpace.scale}
                  onChange={(value) => setCurrentSpace({...currentSpace, scale: value})}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Lighting"
                  fullWidth
                  value={currentSpace.lighting || ''}
                  onChange={(e) => setCurrentSpace({...currentSpace, lighting: e.target.value})}
                  margin="normal"
                  helperText="Lighting preset for this space"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  label="Audio"
                  fullWidth
                  value={currentSpace.audio || ''}
                  onChange={(e) => setCurrentSpace({...currentSpace, audio: e.target.value})}
                  margin="normal"
                  helperText="Audio preset for this space"
                />
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSpaceDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveSpace} variant="contained" color="primary">Save</Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default ExhibitionEditor;
