{"sshfs.configs": [{"name": "mvs.ka<PERSON><PERSON>", "label": "staging server", "host": "**********", "port": 22, "privateKeyPath": "c:\\Users\\<USER>\\mvs-vr.pub", "newFileMode": "0o764", "root": "/"}, {"name": "VectoraxDemo", "privateKeyPath": "$USERNAME/.ssh/id_ed25519", "username": "$USERNAME", "passphrase": true}, {"name": "Vectorax", "passphrase": true, "privateKeyPath": "$USERPROFILE/.ssh/id_ed25519"}, {"name": "Vectorax-MVS", "host": "**********", "privateKeyPath": "c:\\Users\\<USER>\\mvs-vr", "passphrase": true}]}