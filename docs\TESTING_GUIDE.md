# MVS-VR Testing Guide

This guide provides comprehensive information about testing the MVS-VR platform, including how to run tests, add new tests, and interpret test results.

## Test System Overview

The MVS-VR platform includes a comprehensive test system that verifies the functionality of all major components. The test system uses Vitest for JavaScript/TypeScript tests and Playwright for end-to-end tests.

## Test Categories

The test system is organized into the following categories:

- **Unit Tests**: Tests for individual components and functions
- **Integration Tests**: Tests for component interactions
- **End-to-End Tests**: Tests for complete workflows
- **Performance Tests**: Tests for performance characteristics
- **Security Tests**: Tests for security vulnerabilities
- **Property Tests**: Tests for property-based testing
- **Chaos Tests**: Tests for resilience to failures

## Running Tests

### Server Tests

To run all server tests:

```bash
cd mvs-vr-v2/implementation/server
npm test
```

To run specific test categories:

```bash
# Unit tests
npm run test:unit

# Integration tests
npm run test:integration

# End-to-end tests
npm run test:e2e

# Performance tests
npm run test:performance

# Security tests
npm run test:security

# Property tests
npm run test:property

# Chaos tests
npm run test:chaos
```

To run tests with coverage:

```bash
npm run test:coverage
```

To run tests in watch mode:

```bash
npm run test:watch
```

### Frontend Tests

To run all frontend tests:

```bash
cd mvs-vr-v2/implementation/frontend
npm test
```

To run specific test categories:

```bash
# Unit tests
npm run test:unit

# End-to-end tests
npm run test:e2e
```

## Writing Tests

### Unit Tests

Unit tests should be placed in the same directory as the code they test, with a `.test.ts` or `.spec.ts` extension.

Example:

```typescript
// src/utils/formatDate.test.ts
import { describe, it, expect } from 'vitest';
import { formatDate } from './formatDate';

describe('formatDate', () => {
  it('formats a date correctly', () => {
    const date = new Date('2023-01-01T00:00:00.000Z');
    expect(formatDate(date)).toBe('01/01/2023');
  });

  it('handles invalid dates', () => {
    expect(formatDate(null)).toBe('Invalid Date');
  });
});
```

### Integration Tests

Integration tests should be placed in the `tests/integration` directory.

Example:

```typescript
// tests/integration/api.test.ts
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { createServer } from '../../src/server';
import request from 'supertest';

describe('API Integration Tests', () => {
  let server;

  beforeAll(async () => {
    server = await createServer();
  });

  afterAll(async () => {
    await server.close();
  });

  it('returns 200 for GET /', async () => {
    const response = await request(server).get('/');
    expect(response.status).toBe(200);
  });
});
```

### End-to-End Tests

End-to-end tests should be placed in the `tests/e2e` directory and use Playwright.

Example:

```typescript
// tests/e2e/login.test.ts
import { test, expect } from '@playwright/test';

test('user can log in', async ({ page }) => {
  await page.goto('/login');
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.fill('input[name="password"]', 'password');
  await page.click('button[type="submit"]');
  await expect(page).toHaveURL('/dashboard');
});
```

## Test Configuration

### Vitest Configuration

Vitest is configured in the `vitest.config.ts` file:

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    environment: 'node',
    globals: true,
    setupFiles: ['./tests/vitest.setup.ts'],
    include: ['**/*.test.ts', '**/*.spec.ts'],
    exclude: ['**/node_modules/**', '**/dist/**', '**/playwright/**'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './'),
    },
  },
});
```

### Playwright Configuration

Playwright is configured in the `playwright.config.ts` file:

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  timeout: 60_000,
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],

  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
});
```

## CI/CD Integration

Tests are automatically run as part of the CI/CD pipeline. The configuration is in the `.github/workflows/server-ci.yml` file.

## Troubleshooting

### Common Issues

1. **Tests failing due to environment variables**:
   - Make sure all required environment variables are set in the `.env` file
   - For CI/CD, make sure all required environment variables are set in the GitHub Actions secrets

2. **Tests failing due to database connection**:
   - Make sure the database is running
   - Make sure the database connection string is correct

3. **Tests failing due to timeouts**:
   - Increase the timeout in the test configuration
   - Check for performance issues in the code

## Resources

- [Vitest Documentation](https://vitest.dev/)
- [Playwright Documentation](https://playwright.dev/)
- [Jest to Vitest Migration Guide](./JEST_TO_VITEST_MIGRATION.md)
