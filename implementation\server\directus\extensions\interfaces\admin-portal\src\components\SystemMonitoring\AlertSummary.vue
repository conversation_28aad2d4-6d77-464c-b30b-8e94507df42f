<template>
  <v-card class="alert-summary">
    <v-card-title class="d-flex justify-space-between">
      <span>Alert Summary</span>
      <v-btn
        color="primary"
        text
        @click="$emit('view-all')"
      >
        View All Alerts
      </v-btn>
    </v-card-title>
    
    <v-card-text>
      <v-skeleton-loader
        v-if="loading"
        type="card"
        class="mx-auto"
      ></v-skeleton-loader>
      
      <div v-else class="alerts-content">
        <v-row class="alert-counts">
          <v-col cols="12" md="4">
            <v-card
              outlined
              :class="['alert-count-card', { 'has-alerts': alerts?.critical > 0 }]"
              :color="alerts?.critical > 0 ? 'error' : ''"
            >
              <v-card-text class="text-center">
                <div class="alert-count-icon">
                  <v-icon :color="alerts?.critical > 0 ? 'white' : 'error'" size="36">
                    mdi-alert-circle
                  </v-icon>
                </div>
                <div class="alert-count-value" :class="{ 'white--text': alerts?.critical > 0 }">
                  {{ alerts?.critical || 0 }}
                </div>
                <div class="alert-count-label" :class="{ 'white--text': alerts?.critical > 0 }">
                  Critical Alerts
                </div>
              </v-card-text>
            </v-card>
          </v-col>
          
          <v-col cols="12" md="4">
            <v-card
              outlined
              :class="['alert-count-card', { 'has-alerts': alerts?.warning > 0 }]"
              :color="alerts?.warning > 0 ? 'warning' : ''"
            >
              <v-card-text class="text-center">
                <div class="alert-count-icon">
                  <v-icon :color="alerts?.warning > 0 ? 'white' : 'warning'" size="36">
                    mdi-alert
                  </v-icon>
                </div>
                <div class="alert-count-value" :class="{ 'white--text': alerts?.warning > 0 }">
                  {{ alerts?.warning || 0 }}
                </div>
                <div class="alert-count-label" :class="{ 'white--text': alerts?.warning > 0 }">
                  Warning Alerts
                </div>
              </v-card-text>
            </v-card>
          </v-col>
          
          <v-col cols="12" md="4">
            <v-card
              outlined
              :class="['alert-count-card', { 'has-alerts': alerts?.info > 0 }]"
              :color="alerts?.info > 0 ? 'info' : ''"
            >
              <v-card-text class="text-center">
                <div class="alert-count-icon">
                  <v-icon :color="alerts?.info > 0 ? 'white' : 'info'" size="36">
                    mdi-information
                  </v-icon>
                </div>
                <div class="alert-count-value" :class="{ 'white--text': alerts?.info > 0 }">
                  {{ alerts?.info || 0 }}
                </div>
                <div class="alert-count-label" :class="{ 'white--text': alerts?.info > 0 }">
                  Info Alerts
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        
        <div class="recent-alerts">
          <h3 class="recent-alerts-title">Recent Alerts</h3>
          
          <v-list v-if="alerts?.recent_alerts && alerts.recent_alerts.length > 0">
            <v-list-item
              v-for="(alert, index) in alerts.recent_alerts"
              :key="index"
              class="alert-item"
            >
              <v-list-item-avatar>
                <v-icon :color="getAlertSeverityColor(alert.severity)">
                  {{ getAlertSeverityIcon(alert.severity) }}
                </v-icon>
              </v-list-item-avatar>
              
              <v-list-item-content>
                <v-list-item-title class="alert-message">
                  {{ alert.message }}
                </v-list-item-title>
                <v-list-item-subtitle class="alert-timestamp">
                  {{ formatTimestamp(alert.timestamp) }}
                </v-list-item-subtitle>
              </v-list-item-content>
              
              <v-list-item-action>
                <v-chip
                  small
                  :color="getAlertStatusColor(alert.status)"
                  text-color="white"
                >
                  {{ formatAlertStatus(alert.status) }}
                </v-chip>
              </v-list-item-action>
            </v-list-item>
          </v-list>
          
          <div v-else class="no-alerts">
            <v-icon large color="success">mdi-check-circle</v-icon>
            <p>No recent alerts</p>
          </div>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'AlertSummary',
  
  props: {
    alerts: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    // Format timestamp
    formatTimestamp(timestamp) {
      if (!timestamp) return 'N/A';
      
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      
      if (diffMins < 1) {
        return 'Just now';
      } else if (diffMins < 60) {
        return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
      } else if (diffMins < 1440) {
        const hours = Math.floor(diffMins / 60);
        return `${hours} hour${hours === 1 ? '' : 's'} ago`;
      } else {
        return date.toLocaleString();
      }
    },
    
    // Get alert severity color
    getAlertSeverityColor(severity) {
      switch (severity) {
        case 'critical':
          return 'error';
        case 'warning':
          return 'warning';
        case 'info':
          return 'info';
        default:
          return 'grey';
      }
    },
    
    // Get alert severity icon
    getAlertSeverityIcon(severity) {
      switch (severity) {
        case 'critical':
          return 'mdi-alert-circle';
        case 'warning':
          return 'mdi-alert';
        case 'info':
          return 'mdi-information';
        default:
          return 'mdi-help-circle';
      }
    },
    
    // Get alert status color
    getAlertStatusColor(status) {
      switch (status) {
        case 'active':
          return 'error';
        case 'acknowledged':
          return 'warning';
        case 'resolved':
          return 'success';
        default:
          return 'grey';
      }
    },
    
    // Format alert status
    formatAlertStatus(status) {
      if (!status) return 'Unknown';
      return status.charAt(0).toUpperCase() + status.slice(1);
    }
  }
};
</script>

<style scoped>
.alert-summary {
  margin-bottom: 20px;
}

.alerts-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.alert-counts {
  margin-bottom: 16px;
}

.alert-count-card {
  transition: transform 0.2s;
  height: 100%;
}

.alert-count-card:hover {
  transform: translateY(-5px);
}

.alert-count-card.has-alerts {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.alert-count-icon {
  margin-bottom: 8px;
}

.alert-count-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 4px;
}

.alert-count-label {
  font-size: 0.875rem;
}

.recent-alerts-title {
  margin-bottom: 16px;
  font-size: 1.1rem;
  font-weight: 500;
}

.alert-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  transition: background-color 0.2s;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
}

.alert-message {
  font-weight: 500;
}

.alert-timestamp {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
}

.no-alerts {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: rgba(0, 0, 0, 0.6);
}

.no-alerts p {
  margin-top: 16px;
}
</style>
