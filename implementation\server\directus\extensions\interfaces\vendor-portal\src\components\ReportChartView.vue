<template>
  <div class="report-chart-view">
    <div class="chart-controls">
      <div class="chart-type-selector">
        <label>Chart Type:</label>
        <div class="chart-type-buttons">
          <button 
            v-for="type in availableChartTypes" 
            :key="type.id"
            class="chart-type-button"
            :class="{ active: chartType === type.id }"
            @click="setChartType(type.id)"
            :title="type.name"
          >
            <i class="material-icons">{{ type.icon }}</i>
          </button>
        </div>
      </div>
      
      <div class="chart-actions">
        <button class="btn btn-icon" @click="refreshData" title="Refresh">
          <i class="material-icons">refresh</i>
        </button>
        <button class="btn btn-icon" @click="toggleChartOptions" title="Chart Options">
          <i class="material-icons">settings</i>
        </button>
        <button class="btn btn-icon" @click="exportChart" title="Export">
          <i class="material-icons">download</i>
        </button>
      </div>
    </div>

    <div class="chart-container">
      <canvas ref="chartCanvas"></canvas>
      
      <div v-if="!hasData" class="no-data">
        <i class="material-icons">bar_chart</i>
        <p>No data available for chart visualization</p>
      </div>
      
      <div v-if="loading" class="loading-overlay">
        <div class="spinner"></div>
        <span>Loading chart data...</span>
      </div>
    </div>

    <div v-if="showChartOptions" class="chart-options-panel">
      <div class="options-header">
        <h4>Chart Options</h4>
        <button class="btn btn-icon" @click="toggleChartOptions">
          <i class="material-icons">close</i>
        </button>
      </div>
      
      <div class="options-content">
        <div class="option-group">
          <label>Title:</label>
          <input type="text" v-model="chartOptions.title" @change="updateChart">
        </div>
        
        <div class="option-group">
          <label>X-Axis Label:</label>
          <input type="text" v-model="chartOptions.xAxisLabel" @change="updateChart">
        </div>
        
        <div class="option-group">
          <label>Y-Axis Label:</label>
          <input type="text" v-model="chartOptions.yAxisLabel" @change="updateChart">
        </div>
        
        <div class="option-group">
          <label>Legend Position:</label>
          <select v-model="chartOptions.legendPosition" @change="updateChart">
            <option value="top">Top</option>
            <option value="right">Right</option>
            <option value="bottom">Bottom</option>
            <option value="left">Left</option>
            <option value="none">None</option>
          </select>
        </div>
        
        <div class="option-group">
          <label>Color Scheme:</label>
          <select v-model="chartOptions.colorScheme" @change="updateChart">
            <option value="default">Default</option>
            <option value="pastel">Pastel</option>
            <option value="vibrant">Vibrant</option>
            <option value="monochrome">Monochrome</option>
          </select>
        </div>
        
        <div class="option-group">
          <label>Start Y-Axis at Zero:</label>
          <input type="checkbox" v-model="chartOptions.beginAtZero" @change="updateChart">
        </div>
        
        <div class="option-group" v-if="chartType === 'bar' || chartType === 'line'">
          <label>Stacked:</label>
          <input type="checkbox" v-model="chartOptions.stacked" @change="updateChart">
        </div>
        
        <div class="option-group" v-if="chartType === 'pie' || chartType === 'doughnut'">
          <label>Show Percentage:</label>
          <input type="checkbox" v-model="chartOptions.showPercentage" @change="updateChart">
        </div>
        
        <div class="option-group" v-if="chartType === 'line'">
          <label>Line Type:</label>
          <select v-model="chartOptions.lineType" @change="updateChart">
            <option value="linear">Linear</option>
            <option value="stepped">Stepped</option>
            <option value="curved">Curved</option>
          </select>
        </div>
        
        <div class="option-group" v-if="chartType === 'line'">
          <label>Fill Area:</label>
          <input type="checkbox" v-model="chartOptions.fillArea" @change="updateChart">
        </div>
      </div>
      
      <div class="options-footer">
        <button class="btn btn-secondary" @click="resetChartOptions">Reset</button>
        <button class="btn btn-primary" @click="toggleChartOptions">Apply</button>
      </div>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'ReportChartView',

  props: {
    data: {
      type: Array,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    grouping: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      chart: null,
      chartType: 'bar',
      showChartOptions: false,
      chartOptions: {
        title: '',
        xAxisLabel: '',
        yAxisLabel: '',
        legendPosition: 'top',
        colorScheme: 'default',
        beginAtZero: true,
        stacked: false,
        showPercentage: true,
        lineType: 'linear',
        fillArea: false
      }
    };
  },

  computed: {
    availableChartTypes() {
      return [
        { id: 'bar', name: 'Bar Chart', icon: 'bar_chart' },
        { id: 'line', name: 'Line Chart', icon: 'show_chart' },
        { id: 'pie', name: 'Pie Chart', icon: 'pie_chart' },
        { id: 'doughnut', name: 'Doughnut Chart', icon: 'donut_large' },
        { id: 'polarArea', name: 'Polar Area Chart', icon: 'radar' },
        { id: 'radar', name: 'Radar Chart', icon: 'track_changes' }
      ];
    },
    
    hasData() {
      return this.data && this.data.length > 0;
    },
    
    chartData() {
      if (!this.hasData) return null;
      
      // Get dimension column (for labels)
      let labelColumn = this.columns[0]; // Default to first column
      
      if (this.grouping) {
        // If grouping is defined, use the grouping field as label
        if (this.grouping.dimension === 'time') {
          labelColumn = { id: 'time_period', name: this.grouping.unit.charAt(0).toUpperCase() + this.grouping.unit.slice(1) };
        } else if (this.grouping.dimension === 'category' || this.grouping.dimension === 'field') {
          const field = this.columns.find(col => col.id === this.grouping.field);
          if (field) {
            labelColumn = field;
          }
        } else if (this.grouping.dimension === 'location') {
          labelColumn = { id: 'location', name: this.grouping.groupBy.charAt(0).toUpperCase() + this.grouping.groupBy.slice(1) };
        }
      }
      
      // Get metric columns (for values)
      const metricColumns = this.columns.filter(col => 
        col.id !== labelColumn.id && 
        col.type === 'number'
      );
      
      if (metricColumns.length === 0) {
        // If no numeric columns, try to use the first non-label column
        const nonLabelColumns = this.columns.filter(col => col.id !== labelColumn.id);
        if (nonLabelColumns.length > 0) {
          metricColumns.push(nonLabelColumns[0]);
        }
      }
      
      // Extract labels and datasets
      const labels = this.data.map(row => this.formatLabel(row[labelColumn.id]));
      
      const datasets = metricColumns.map(column => {
        const colorScheme = this.getColorScheme(metricColumns.indexOf(column));
        
        return {
          label: column.name,
          data: this.data.map(row => row[column.id]),
          backgroundColor: this.chartType === 'line' ? colorScheme.backgroundColor : colorScheme.backgroundColors,
          borderColor: colorScheme.borderColor,
          borderWidth: 1,
          fill: this.chartOptions.fillArea,
          tension: this.chartOptions.lineType === 'curved' ? 0.4 : 0,
          stepped: this.chartOptions.lineType === 'stepped'
        };
      });
      
      return {
        labels,
        datasets
      };
    }
  },

  watch: {
    data: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    
    columns: {
      handler() {
        this.updateChart();
      },
      deep: true
    },
    
    grouping: {
      handler() {
        this.updateChart();
      },
      deep: true
    }
  },

  mounted() {
    this.initChart();
  },
  
  beforeUnmount() {
    if (this.chart) {
      this.chart.destroy();
    }
  },

  methods: {
    initChart() {
      const canvas = this.$refs.chartCanvas;
      if (!canvas || !this.hasData) return;
      
      if (this.chart) {
        this.chart.destroy();
      }
      
      const ctx = canvas.getContext('2d');
      
      this.chart = new Chart(ctx, {
        type: this.chartType,
        data: this.chartData,
        options: this.getChartOptions()
      });
    },
    
    updateChart() {
      if (!this.chart || !this.hasData) {
        this.initChart();
        return;
      }
      
      this.chart.data = this.chartData;
      this.chart.options = this.getChartOptions();
      this.chart.update();
    },
    
    getChartOptions() {
      const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: !!this.chartOptions.title,
            text: this.chartOptions.title
          },
          legend: {
            display: this.chartOptions.legendPosition !== 'none',
            position: this.chartOptions.legendPosition
          },
          tooltip: {
            enabled: true
          }
        },
        scales: {}
      };
      
      // Add scales for cartesian charts
      if (['bar', 'line'].includes(this.chartType)) {
        options.scales = {
          x: {
            title: {
              display: !!this.chartOptions.xAxisLabel,
              text: this.chartOptions.xAxisLabel
            },
            stacked: this.chartOptions.stacked
          },
          y: {
            title: {
              display: !!this.chartOptions.yAxisLabel,
              text: this.chartOptions.yAxisLabel
            },
            beginAtZero: this.chartOptions.beginAtZero,
            stacked: this.chartOptions.stacked
          }
        };
      }
      
      // Add percentage formatting for pie/doughnut
      if (['pie', 'doughnut'].includes(this.chartType) && this.chartOptions.showPercentage) {
        options.plugins.tooltip = {
          callbacks: {
            label: (context) => {
              const label = context.label || '';
              const value = context.raw || 0;
              const sum = context.chart.data.datasets[0].data.reduce((a, b) => a + b, 0);
              const percentage = Math.round((value / sum) * 100);
              return `${label}: ${value} (${percentage}%)`;
            }
          }
        };
      }
      
      return options;
    },
    
    setChartType(type) {
      this.chartType = type;
      this.updateChart();
    },
    
    formatLabel(value) {
      if (value === null || value === undefined) return '-';
      return String(value);
    },
    
    getColorScheme(index) {
      const schemes = {
        default: {
          backgroundColors: [
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)',
            'rgba(255, 159, 64, 0.6)'
          ],
          borderColors: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)'
          ]
        },
        pastel: {
          backgroundColors: [
            'rgba(187, 214, 234, 0.6)',
            'rgba(245, 183, 177, 0.6)',
            'rgba(249, 231, 159, 0.6)',
            'rgba(169, 223, 191, 0.6)',
            'rgba(187, 143, 206, 0.6)',
            'rgba(240, 178, 122, 0.6)'
          ],
          borderColors: [
            'rgba(133, 193, 233, 1)',
            'rgba(236, 112, 99, 1)',
            'rgba(244, 208, 63, 1)',
            'rgba(88, 214, 141, 1)',
            'rgba(155, 89, 182, 1)',
            'rgba(230, 126, 34, 1)'
          ]
        },
        vibrant: {
          backgroundColors: [
            'rgba(46, 204, 113, 0.6)',
            'rgba(231, 76, 60, 0.6)',
            'rgba(241, 196, 15, 0.6)',
            'rgba(52, 152, 219, 0.6)',
            'rgba(155, 89, 182, 0.6)',
            'rgba(230, 126, 34, 0.6)'
          ],
          borderColors: [
            'rgba(39, 174, 96, 1)',
            'rgba(192, 57, 43, 1)',
            'rgba(243, 156, 18, 1)',
            'rgba(41, 128, 185, 1)',
            'rgba(142, 68, 173, 1)',
            'rgba(211, 84, 0, 1)'
          ]
        },
        monochrome: {
          backgroundColors: [
            'rgba(52, 73, 94, 0.9)',
            'rgba(52, 73, 94, 0.8)',
            'rgba(52, 73, 94, 0.7)',
            'rgba(52, 73, 94, 0.6)',
            'rgba(52, 73, 94, 0.5)',
            'rgba(52, 73, 94, 0.4)'
          ],
          borderColors: [
            'rgba(52, 73, 94, 1)',
            'rgba(52, 73, 94, 1)',
            'rgba(52, 73, 94, 1)',
            'rgba(52, 73, 94, 1)',
            'rgba(52, 73, 94, 1)',
            'rgba(52, 73, 94, 1)'
          ]
        }
      };
      
      const scheme = schemes[this.chartOptions.colorScheme] || schemes.default;
      const colorIndex = index % scheme.backgroundColors.length;
      
      return {
        backgroundColor: scheme.backgroundColors[colorIndex],
        backgroundColors: scheme.backgroundColors,
        borderColor: scheme.borderColors[colorIndex],
        borderColors: scheme.borderColors
      };
    },
    
    toggleChartOptions() {
      this.showChartOptions = !this.showChartOptions;
    },
    
    resetChartOptions() {
      this.chartOptions = {
        title: '',
        xAxisLabel: '',
        yAxisLabel: '',
        legendPosition: 'top',
        colorScheme: 'default',
        beginAtZero: true,
        stacked: false,
        showPercentage: true,
        lineType: 'linear',
        fillArea: false
      };
      
      this.updateChart();
    },
    
    refreshData() {
      this.$emit('refresh');
    },
    
    exportChart() {
      if (!this.chart) return;
      
      // Create a temporary link element
      const link = document.createElement('a');
      link.href = this.chart.toBase64Image();
      link.download = `chart-${new Date().toISOString().split('T')[0]}.png`;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      this.$emit('export');
    }
  }
};
</script>

<style scoped>
.report-chart-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.chart-type-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chart-type-selector label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.chart-type-buttons {
  display: flex;
  gap: 5px;
}

.chart-type-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  cursor: pointer;
  transition: all 0.2s;
}

.chart-type-button:hover {
  background-color: var(--theme--background-subdued);
}

.chart-type-button.active {
  background-color: var(--theme--primary);
  color: white;
  border-color: var(--theme--primary);
}

.chart-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
}

.chart-container {
  flex: 1;
  position: relative;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  min-height: 300px;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.no-data i {
  font-size: 48px;
  margin-bottom: 10px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--background);
  border-top: 4px solid var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-options-panel {
  position: absolute;
  top: 50px;
  right: 0;
  width: 300px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid var(--theme--border-color);
}

.options-header h4 {
  margin: 0;
}

.options-content {
  padding: 10px 15px;
  max-height: 400px;
  overflow-y: auto;
}

.option-group {
  margin-bottom: 15px;
}

.option-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.option-group input[type="text"],
.option-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.options-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px 15px;
  border-top: 1px solid var(--theme--border-color);
}
</style>
