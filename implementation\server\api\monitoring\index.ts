/**
 * System Monitoring API
 *
 * This module provides endpoints for system monitoring and health checks.
 */

import { Router } from 'express';
import { authenticate } from '../../middleware/auth';
import { rateLimiter } from '../../middleware/rate-limiter';
import { roleCheck } from '../../middleware/role-check';
import { logger } from '../../lib/logger';

// Import sub-routers
import systemHealthRouter from './system-health';
import apiMetricsRouter from './api-metrics';
import databaseMetricsRouter from './database-metrics';
import logsRouter from './logs';
import userActivityRouter from './user-activity';
import alertsRouter from './alerts';

// Create main router
const router = Router();

// Register sub-routers
router.use(
  '/system-health',
  authenticate,
  roleCheck(['admin', 'system_admin']),
  systemHealthRouter,
);
router.use('/api-metrics', authenticate, roleCheck(['admin', 'system_admin']), apiMetricsRouter);
router.use(
  '/database-metrics',
  authenticate,
  roleCheck(['admin', 'system_admin']),
  databaseMetricsRouter,
);
router.use('/logs', authenticate, roleCheck(['admin', 'system_admin']), logsRouter);
router.use(
  '/user-activity',
  authenticate,
  roleCheck(['admin', 'system_admin']),
  userActivityRouter,
);
router.use('/alerts', authenticate, roleCheck(['admin', 'system_admin']), alertsRouter);

// Public health check endpoint (no authentication required)
router.get('/health', (req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  });
});

// Log router initialization
logger.info('Monitoring API router initialized');

export default router;
