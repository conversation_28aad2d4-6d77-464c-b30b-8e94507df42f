<template>
  <v-card class="dashboard-widget">
    <v-card-title class="widget-header">
      <div class="widget-drag-handle">
        <v-icon small class="mr-2 drag-handle-icon">mdi-drag</v-icon>
      </div>
      <span>{{ widgetTitle }}</span>
      <v-spacer></v-spacer>
      <div class="widget-actions">
        <v-btn icon x-small @click="$emit('refresh')">
          <v-icon small>mdi-refresh</v-icon>
        </v-btn>
        <v-btn icon x-small @click="$emit('edit')">
          <v-icon small>mdi-cog</v-icon>
        </v-btn>
        <v-btn icon x-small @click="$emit('remove')">
          <v-icon small>mdi-close</v-icon>
        </v-btn>
      </div>
    </v-card-title>
    
    <v-divider></v-divider>
    
    <v-card-text class="widget-content">
      <v-skeleton-loader
        v-if="loading"
        type="card"
        class="mx-auto"
      ></v-skeleton-loader>
      
      <div v-else>
        <!-- Stats Widget -->
        <stats-widget 
          v-if="widgetType === 'stats'" 
          :data="widgetData" 
          :settings="widgetSettings"
        />
        
        <!-- Chart Widget -->
        <chart-widget 
          v-else-if="widgetType === 'chart'" 
          :data="widgetData" 
          :settings="widgetSettings"
        />
        
        <!-- Activity Widget -->
        <activity-widget 
          v-else-if="widgetType === 'activity'" 
          :data="widgetData" 
          :settings="widgetSettings"
        />
        
        <!-- System Widget -->
        <system-widget 
          v-else-if="widgetType === 'system'" 
          :data="widgetData" 
          :settings="widgetSettings"
        />
        
        <!-- Default Widget -->
        <div v-else class="text-center py-4">
          <v-icon size="48" color="grey lighten-1">mdi-widgets</v-icon>
          <p class="mt-2 grey--text">Unknown widget type</p>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
import StatsWidget from './StatsWidget.vue';
import ChartWidget from './ChartWidget.vue';
import ActivityWidget from './ActivityWidget.vue';
import SystemWidget from './SystemWidget.vue';

export default {
  name: 'DashboardWidget',
  
  components: {
    StatsWidget,
    ChartWidget,
    ActivityWidget,
    SystemWidget
  },
  
  props: {
    widgetType: {
      type: String,
      required: true,
      validator: value => ['stats', 'chart', 'activity', 'system'].includes(value)
    },
    widgetTitle: {
      type: String,
      required: true
    },
    widgetData: {
      type: Object,
      default: () => ({})
    },
    widgetSettings: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['refresh', 'edit', 'remove']
};
</script>

<style scoped>
.dashboard-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.widget-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
}

.widget-drag-handle {
  cursor: move;
  opacity: 0.2;
  transition: opacity 0.2s;
}

.dashboard-widget:hover .widget-drag-handle {
  opacity: 1;
}

.widget-content {
  flex: 1;
  overflow: auto;
}

.widget-actions {
  display: flex;
  gap: 4px;
}
</style>
