# Spatial Audio Logic (Room/Object-Based)

## Overview

This document defines how spatial audio is managed in the Kanousei VR platform. Audio is dynamically routed based on user position, object interactions, and room context to enhance immersion.

---

## Objectives

* Deliver realistic 3D audio positioning
* Reduce noise clutter in multi-sound environments
* Allow vendors to attach sound to products or rooms

---

## Audio Categories

### 1. **Ambient Audio (Room-Level)**

* Background sounds based on room environment
* Examples:

  * City ambience in Dubai main hall
  * Birds/fountains in nature room

### 2. **Product Audio (Object-Level)**

* Sounds triggered by interacting with products
* Examples:

  * Drawer opening/closing
  * Fridge hum
  * Chair creak

### 3. **Assistant/Avatar Voice**

* Follows avatar/object position
* Pans based on user head direction
* Volume scaled by proximity or user settings

---

## Audio Metadata Structure

```json
{
  "audioId": "drawer_slide_sfx",
  "type": "product",
  "attachedTo": "drawer_004",
  "position": [1.2, 0.5, -2.1],
  "spatial": true,
  "volume": 0.85,
  "loop": false
}
```

---

## Room-Level Audio Routing

Each space/room defines:

* One ambient loop (default)
* Optional event-based overlays (e.g., music change on vendor promo)
* Optional "global music off" toggle per user

### Example:

```json
{
  "roomId": "dubai_main",
  "ambient": "dubai_market_ambience",
  "overlays": ["event_jingle", "holiday_theme"],
  "volume": 0.6
}
```

---

## Audio Optimization

* Audio sources are pooled and culled based on distance.
* Max concurrent 3D audio sources per room = 10 (configurable).
* Volumes are balanced to prevent spikes.

---

## Vendor Controls

* Attach audio via drag-and-drop in layout editor
* Upload audio (must be <5MB, ogg/mp3)
* Set distance falloff + spatial toggle

---

## Admin Controls

* Override volume limits
* Assign ambient presets to rooms
* Track audio usage per scene for optimization

---

## User Controls

* Toggle ambient audio
* Adjust assistant voice volume
* Mute all sound (silent mode)

---

## Accessibility

* Closed captions available for assistant audio
* Audio descriptions embedded in AI prompt replies
