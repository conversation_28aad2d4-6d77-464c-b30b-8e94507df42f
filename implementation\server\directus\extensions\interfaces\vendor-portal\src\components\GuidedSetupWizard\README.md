# Guided Setup Wizard

The Guided Setup Wizard is a component that helps vendors set up their account and configure their virtual showroom in a step-by-step process.

## Features

- Multi-step wizard with progress tracking
- Form validation with error messages
- Auto-save functionality to prevent data loss
- Contextual help with tooltips and video tutorials
- Analytics tracking for user interactions
- Responsive design for all device sizes

## Components

### Main Components

- **GuidedSetupWizard**: The main container component that manages the wizard state and API interactions
- **WizardContainer**: Handles the wizard navigation, progress tracking, and step transitions
- **WizardStep**: Base component for all wizard steps with validation and help functionality

### Step Components

- **CompanyProfileStep**: Configure company information and upload logo
- **UserAccountStep**: Set up admin account and team members
- **BrandingSetupStep**: Configure brand colors, fonts, and theme settings
- **ProductUploadStep**: Upload and organize products into categories
- **ShowroomConfigStep**: Configure the virtual showroom layout and settings
- **CompletionStep**: Summary of the setup process with next steps

## API Integration

The wizard integrates with the following API endpoints:

- `/items/vendor_onboarding`: Store and retrieve onboarding progress
- `/items/vendors`: Update vendor information
- `/items/vendor_branding`: Store branding configuration
- `/items/wizard_analytics`: Track wizard usage analytics
- `/users`: Manage user accounts
- `/files`: Upload and manage files (logos, product images)

## Database Schema

The wizard uses the following database tables:

- `vendor_onboarding`: Stores onboarding progress and completion status
- `vendor_branding`: Stores branding configuration
- `wizard_analytics`: Stores analytics data for wizard usage

## Usage

```vue
<GuidedSetupWizard
  :vendor-id="vendorId"
  @complete="handleWizardComplete"
  @save-progress="handleSaveProgress"
  @load-progress="handleLoadProgress"
  @exit="handleExit"
/>
```

### Props

- `title`: The title of the wizard (default: 'Vendor Onboarding Wizard')
- `description`: The description of the wizard (default: 'Complete the following steps to set up your vendor account')
- `initialStepIndex`: The initial step index to show (default: 0)
- `allowStepNavigation`: Whether to allow navigation between steps (default: true)
- `showBackButton`: Whether to show the back button (default: true)
- `showSaveButton`: Whether to show the save button (default: true)
- `showHelpSection`: Whether to show the help section (default: true)
- `showExitButton`: Whether to show the exit button (default: true)
- `nextButtonText`: The text for the next button (default: 'Next')
- `finishButtonText`: The text for the finish button (default: 'Complete Setup')
- `exitButtonText`: The text for the exit button (default: 'Exit Setup')
- `storageKey`: The key to use for local storage (default: 'vendor-onboarding-wizard')
- `vendorId`: The ID of the vendor (required)

### Events

- `complete`: Emitted when the wizard is completed
- `save-progress`: Emitted when progress is saved
- `load-progress`: Emitted when progress is loaded
- `exit`: Emitted when the exit button is clicked

## Development

### Adding a New Step

1. Create a new step component in the `steps` directory
2. Extend the `WizardStep` component
3. Implement the required methods: `updateStepData`, `validateStep`
4. Add the step to the `wizardSteps` array in `GuidedSetupWizard.vue`

### Customizing Validation

Each step can define its own validation schema:

```js
validationSchema: {
  fieldName: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: '^[a-zA-Z0-9]+$',
    patternMessage: 'Custom error message',
    validate: (value, allValues) => {
      // Custom validation logic
      return true or 'Error message';
    }
  }
}
```

### Adding Help Content

Each step can define its own help content:

```js
helpTips: [
  {
    title: 'Tip Title',
    text: 'Tip content'
  }
]
```

## Testing

Run the tests with:

```bash
npm run test
```

The wizard has comprehensive test coverage:

- Unit tests for all components
- Integration tests for API interactions
- End-to-end tests for the complete wizard flow

## License

This component is part of the MVS-VR project and is subject to the same license terms.
