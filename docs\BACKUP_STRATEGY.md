# MVS-VR Backup Strategy

This document outlines the comprehensive backup strategy for the MVS-VR platform, including database backups, file storage backups, configuration backups, and backup validation procedures.

## Table of Contents

1. [Overview](#overview)
2. [Database Backup Strategy](#database-backup-strategy)
3. [File Storage Backup Strategy](#file-storage-backup-strategy)
4. [Configuration Backup Strategy](#configuration-backup-strategy)
5. [Backup Validation Strategy](#backup-validation-strategy)
6. [Backup Retention Policy](#backup-retention-policy)
7. [Backup Security](#backup-security)
8. [Backup Monitoring](#backup-monitoring)
9. [Disaster Recovery](#disaster-recovery)

## Overview

The MVS-VR backup strategy is designed to ensure data integrity, availability, and recoverability in case of system failures, data corruption, or disasters. The strategy follows the 3-2-1 backup rule:

- 3 copies of data (production + 2 backups)
- 2 different storage media
- 1 off-site backup

## Database Backup Strategy

### PostgreSQL Database

The PostgreSQL database is the primary data store for the MVS-VR platform, containing user data, showroom configurations, analytics, and system settings.

#### Backup Types

1. **Full Backups**: Complete database dumps using `pg_dump`
   - Frequency: Daily at 2:00 AM UTC
   - Retention: 30 days

2. **Incremental Backups**: WAL (Write-Ahead Log) archiving
   - Frequency: Continuous (every 5 minutes)
   - Retention: 7 days

3. **Point-in-Time Recovery (PITR)**: Combination of full backups and WAL archives
   - Recovery window: 7 days

#### Backup Process

```bash
# Full backup
pg_dump -Fc -v -h $DB_HOST -U $DB_USER -d $DB_NAME -f /backups/postgres/full_$(date +%Y%m%d_%H%M%S).dump

# WAL archiving (configured in postgresql.conf)
archive_mode = on
archive_command = 'cp %p /backups/postgres/wal/%f'
```

### Supabase Database

The Supabase database contains authentication data, user profiles, and real-time collaboration information.

#### Backup Types

1. **Full Backups**: Using Supabase backup API
   - Frequency: Daily at 3:00 AM UTC
   - Retention: 30 days

2. **Point-in-Time Recovery**: Using Supabase PITR feature
   - Recovery window: 7 days (Premium plan)

#### Backup Process

```bash
# Using Supabase API
curl -X POST "https://api.supabase.io/v1/projects/$PROJECT_ID/database/backups" \
  -H "Authorization: Bearer $SUPABASE_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"name":"backup_'$(date +%Y%m%d_%H%M%S)'"}'
```

## File Storage Backup Strategy

### Asset Files

Asset files include 3D models, textures, images, and other media files stored in the system.

#### Backup Types

1. **Full Backups**: Complete copy of all asset files
   - Frequency: Weekly on Sunday at 4:00 AM UTC
   - Retention: 90 days

2. **Incremental Backups**: Only new or modified files since the last backup
   - Frequency: Daily at 4:00 AM UTC
   - Retention: 30 days

#### Backup Process

```bash
# Incremental backup using rsync
rsync -avz --delete /data/assets/ /backups/assets/incremental/$(date +%Y%m%d)/

# Full backup using tar
tar -czf /backups/assets/full/assets_full_$(date +%Y%m%d).tar.gz /data/assets/
```

### User-Generated Content

User-generated content includes uploaded files, custom configurations, and user-specific data.

#### Backup Types

1. **Full Backups**: Complete copy of all user content
   - Frequency: Weekly on Sunday at 5:00 AM UTC
   - Retention: 90 days

2. **Incremental Backups**: Only new or modified files since the last backup
   - Frequency: Daily at 5:00 AM UTC
   - Retention: 30 days

#### Backup Process

```bash
# Incremental backup using rsync
rsync -avz --delete /data/user_content/ /backups/user_content/incremental/$(date +%Y%m%d)/

# Full backup using tar
tar -czf /backups/user_content/full/user_content_full_$(date +%Y%m%d).tar.gz /data/user_content/
```

## Configuration Backup Strategy

### System Configuration

System configuration includes environment variables, configuration files, and system settings.

#### Backup Types

1. **Full Configuration Backups**: All configuration files and settings
   - Frequency: Daily at 1:00 AM UTC
   - Retention: 90 days

2. **Version-Controlled Configurations**: Git repository for configuration files
   - Frequency: On every change (commit-based)
   - Retention: Indefinite (version history)

#### Backup Process

```bash
# Configuration backup
tar -czf /backups/config/config_$(date +%Y%m%d_%H%M%S).tar.gz /etc/mvs-vr/ /opt/mvs-vr/config/

# Git-based configuration
git -C /opt/mvs-vr/config add .
git -C /opt/mvs-vr/config commit -m "Configuration update $(date +%Y%m%d_%H%M%S)"
git -C /opt/mvs-vr/config push origin main
```

### Infrastructure Configuration

Infrastructure configuration includes Terraform state, Kubernetes manifests, and cloud provider settings.

#### Backup Types

1. **Terraform State Backups**: Backup of Terraform state files
   - Frequency: On every Terraform apply
   - Retention: 90 days

2. **Kubernetes Manifests**: Backup of Kubernetes configuration
   - Frequency: Daily at 1:30 AM UTC
   - Retention: 90 days

#### Backup Process

```bash
# Terraform state backup (configured in Terraform backend)
terraform {
  backend "s3" {
    bucket = "mvs-vr-terraform-state"
    key    = "terraform.tfstate"
    region = "us-east-1"
    versioning = true
  }
}

# Kubernetes manifests backup
kubectl get all --all-namespaces -o yaml > /backups/kubernetes/all_resources_$(date +%Y%m%d_%H%M%S).yaml
```

## Backup Validation Strategy

### Automated Validation

1. **Backup Integrity Checks**: Verify backup files are not corrupted
   - Frequency: After each backup
   - Method: Checksum verification (SHA-256)

2. **Backup Completeness Checks**: Verify all required data is included
   - Frequency: After each backup
   - Method: File count and size comparison

3. **Restore Tests**: Automated restore to test environment
   - Frequency: Weekly
   - Method: Restore to isolated environment and run validation tests

### Manual Validation

1. **Quarterly Full Restore Tests**: Complete restore to test environment
   - Frequency: Quarterly
   - Method: Full system restore and functionality testing

2. **Annual Disaster Recovery Drill**: Simulated disaster recovery
   - Frequency: Annually
   - Method: Full DR procedure execution with timing and success metrics

## Backup Retention Policy

| Backup Type | Daily Retention | Weekly Retention | Monthly Retention | Yearly Retention |
|-------------|-----------------|------------------|-------------------|------------------|
| Database Full | 30 days | 12 weeks | 12 months | 7 years |
| Database Incremental | 7 days | N/A | N/A | N/A |
| Asset Files Full | N/A | 12 weeks | 12 months | 7 years |
| Asset Files Incremental | 30 days | N/A | N/A | N/A |
| User Content Full | N/A | 12 weeks | 12 months | 7 years |
| User Content Incremental | 30 days | N/A | N/A | N/A |
| Configuration | 90 days | 12 weeks | 12 months | 7 years |

## Backup Security

1. **Encryption**: All backups are encrypted using AES-256 encryption
2. **Access Control**: Strict access controls with multi-factor authentication
3. **Audit Logging**: Comprehensive logging of all backup and restore operations
4. **Secure Transport**: Encrypted data transfer using TLS 1.3
5. **Key Management**: Secure key management with rotation and secure storage

## Backup Monitoring

1. **Backup Success Monitoring**: Alert on backup failures
2. **Backup Size Monitoring**: Track backup size trends and alert on anomalies
3. **Backup Time Monitoring**: Track backup duration and alert on anomalies
4. **Storage Capacity Monitoring**: Alert on low backup storage capacity
5. **Backup Validation Monitoring**: Alert on failed validation checks

## Disaster Recovery

The backup strategy is closely integrated with the disaster recovery plan. See the [DISASTER_RECOVERY.md](DISASTER_RECOVERY.md) document for detailed information on disaster recovery procedures.
