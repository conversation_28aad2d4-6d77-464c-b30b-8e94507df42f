# 🚨 Cloudflare 521 Error Fix Guide

## 🔍 **What is a 521 Error?**

**Error 521**: "Web server is down" - <PERSON>flar<PERSON> can reach your domain but cannot connect to your origin server (**************).

### **Common Causes:**
1. **Origin server is down** or not responding
2. **Port 80/443 blocked** by firewall
3. **Docker containers not running** (nginx not serving content)
4. **Cloudflare IP addresses blocked** by server
5. **Server overloaded** or crashed

## 🔧 **Immediate Diagnosis Steps**

### **Step 1: Run Automated Diagnosis**
```powershell
# Run the automated fix script
.\Fix-521-Error.ps1
```

### **Step 2: Manual Server Check**
```bash
# Test if server is responding
ping **************

# Test if port 80 is open
telnet ************** 80
# Or use PowerShell:
Test-NetConnection -ComputerName ************** -Port 80
```

### **Step 3: Check Server Status**
1. **Log into DigitalOcean Dashboard**
2. **Check droplet status** - should show "Active"
3. **Check CPU/Memory usage** - should not be at 100%
4. **Check network activity** - should show traffic

## 🛠️ **Fix Methods**

### **Method 1: Restart Docker Services (Most Common Fix)**

If you have SSH access:
```bash
# SSH into server
ssh -i C:\Users\<USER>\.ssh\id_rsa root@**************

# Check Docker status
systemctl status docker

# Start Docker if stopped
systemctl start docker
systemctl enable docker

# Navigate to project directory
cd /home/<USER>/mvs-vr-deployment

# Restart containers
docker-compose -f docker-compose.exported.yml down
docker-compose -f docker-compose.exported.yml up -d

# Check if containers are running
docker ps

# Test locally
curl http://localhost/
```

### **Method 2: Use DigitalOcean Console (If SSH Fails)**

1. **Go to DigitalOcean Dashboard**
2. **Click on your droplet**
3. **Click "Console" tab**
4. **Log in as root** (use reset password if needed)
5. **Run the same Docker commands above**

### **Method 3: Fix Firewall Issues**

```bash
# Allow HTTP/HTTPS traffic
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp

# Enable firewall
ufw --force enable

# Check firewall status
ufw status

# Allow Cloudflare IP ranges (if using Cloudflare)
# Add Cloudflare IPs to whitelist
```

### **Method 4: Redeploy Services**

If containers are missing or corrupted:
```bash
# Remove all containers
docker-compose -f docker-compose.exported.yml down --volumes

# Pull fresh images (if available)
docker-compose -f docker-compose.exported.yml pull

# Start fresh
docker-compose -f docker-compose.exported.yml up -d

# Monitor logs
docker-compose -f docker-compose.exported.yml logs -f
```

## 🌐 **Cloudflare Configuration Check**

### **DNS Settings:**
1. **Log into Cloudflare Dashboard**
2. **Check DNS records:**
   ```
   mvs.kanousai.com      A    **************  (Proxied ☁️)
   adminmvs.kanousai.com A    **************  (Proxied ☁️)
   stagingmvs.kanousai.com A  **************  (DNS Only 🌐)
   ```

### **SSL/TLS Settings:**
1. **Go to SSL/TLS tab**
2. **Set to "Full" or "Flexible"** (not "Full (Strict)" until you have SSL)
3. **Disable "Always Use HTTPS"** temporarily

### **Security Settings:**
1. **Go to Security tab**
2. **Set Security Level to "Medium"** or "Low" temporarily
3. **Disable "Bot Fight Mode"** temporarily
4. **Check if any IP addresses are blocked**

### **Speed Settings:**
1. **Go to Speed tab**
2. **Disable "Rocket Loader"** temporarily
3. **Disable "Mirage"** temporarily

## 🧪 **Testing Steps**

### **1. Test Origin Server Directly**
```bash
# Test without Cloudflare
curl -I http://**************/
# Should return: HTTP/1.1 200 OK
```

### **2. Test Through Cloudflare**
```bash
# Test with Cloudflare
curl -I http://mvs.kanousai.com/
# Should return: HTTP/1.1 200 OK
```

### **3. Check Cloudflare Analytics**
1. **Go to Analytics tab** in Cloudflare
2. **Check for error rates**
3. **Look for traffic patterns**

## 🚨 **Emergency Procedures**

### **If Server is Completely Down:**
1. **Reboot server** from DigitalOcean dashboard
2. **Wait 2-3 minutes** for boot
3. **Check if services auto-start**
4. **Manually start Docker** if needed

### **If SSH is Completely Broken:**
1. **Use DigitalOcean Console** (web interface)
2. **Reset root password** if needed
3. **Recreate SSH keys** if necessary

### **If Cloudflare is the Issue:**
1. **Temporarily disable proxy** (set to DNS Only 🌐)
2. **Test direct IP access**
3. **Re-enable proxy** once server is working

## 📊 **Monitoring Commands**

### **Server Health:**
```bash
# Check system resources
htop
df -h
free -m

# Check Docker
docker ps
docker stats

# Check logs
docker-compose logs nginx
journalctl -u docker
```

### **Network Connectivity:**
```bash
# Test outbound connectivity
curl -I https://google.com

# Test inbound connectivity
netstat -tulpn | grep :80
ss -tulpn | grep :80
```

## ✅ **Success Indicators**

### **Server is Working When:**
- ✅ `ping **************` responds
- ✅ `curl http://**************/` returns 200
- ✅ `docker ps` shows nginx container running
- ✅ Cloudflare shows no 521 errors
- ✅ Website loads in browser

### **Common Success Messages:**
```
HTTP/1.1 200 OK
Server: nginx/1.x.x
Content-Type: text/html
```

## 🔄 **Prevention Tips**

1. **Monitor server resources** regularly
2. **Set up automated backups**
3. **Configure health checks**
4. **Monitor Docker container status**
5. **Keep Cloudflare settings simple** initially

## 📞 **When to Contact Support**

**Contact DigitalOcean Support if:**
- Server won't respond to ping
- Console access fails
- Hardware issues suspected
- Network connectivity problems

**Contact Cloudflare Support if:**
- DNS propagation issues
- SSL certificate problems
- Persistent 521 errors after server is confirmed working

## 🎯 **Quick Fix Checklist**

- [ ] **Server responds to ping**
- [ ] **Port 80 is open** (Test-NetConnection)
- [ ] **SSH access working**
- [ ] **Docker service running**
- [ ] **Nginx container running**
- [ ] **Firewall allows port 80**
- [ ] **Local HTTP test passes** (curl localhost)
- [ ] **External HTTP test passes** (curl IP)
- [ ] **Cloudflare DNS correct**
- [ ] **Cloudflare SSL settings appropriate**

**Most 521 errors are fixed by restarting Docker containers! 🚀**
