<template>
  <v-dialog
    :value="visible"
    @input="$emit('close')"
    max-width="800px"
    scrollable
  >
    <v-card v-if="user">
      <v-card-title class="headline d-flex justify-space-between align-center">
        <span>User Details</span>
        <v-btn icon @click="$emit('close')">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      
      <v-divider></v-divider>
      
      <v-card-text class="user-detail-content">
        <!-- User Header -->
        <div class="user-header mb-6 d-flex align-center">
          <v-avatar size="80" color="primary" class="white--text mr-4">
            <span v-if="!user.avatar">{{ getInitials(user.first_name, user.last_name) }}</span>
            <img v-else :src="user.avatar" alt="Avatar">
          </v-avatar>
          
          <div>
            <h2 class="text-h5 mb-1">{{ user.first_name }} {{ user.last_name }}</h2>
            <div class="d-flex align-center">
              <v-chip
                :color="getRoleColor(user.role)"
                text-color="white"
                small
                class="mr-2"
              >
                {{ user.role }}
              </v-chip>
              
              <v-chip
                :color="getStatusColor(user.status)"
                text-color="white"
                small
              >
                {{ user.status }}
              </v-chip>
            </div>
          </div>
        </div>
        
        <!-- Tabs -->
        <v-tabs v-model="activeTab" background-color="transparent" grow>
          <v-tab>Profile</v-tab>
          <v-tab>Activity</v-tab>
          <v-tab>Security</v-tab>
        </v-tabs>
        
        <v-divider></v-divider>
        
        <v-tabs-items v-model="activeTab">
          <!-- Profile Tab -->
          <v-tab-item>
            <div class="pa-4">
              <h3 class="text-h6 mb-4">Basic Information</h3>
              
              <v-row>
                <v-col cols="12" md="6">
                  <v-list-item>
                    <v-list-item-icon>
                      <v-icon>mdi-email</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>Email</v-list-item-title>
                      <v-list-item-subtitle>{{ user.email }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-list-item>
                    <v-list-item-icon>
                      <v-icon>mdi-calendar</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>Created</v-list-item-title>
                      <v-list-item-subtitle>{{ formatDate(user.created_at) }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-list-item>
                    <v-list-item-icon>
                      <v-icon>mdi-login</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>Last Login</v-list-item-title>
                      <v-list-item-subtitle>{{ formatDate(user.last_login) || 'Never' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-list-item>
                    <v-list-item-icon>
                      <v-icon>mdi-shield-account</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>Role</v-list-item-title>
                      <v-list-item-subtitle>{{ user.role }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-col>
              </v-row>
              
              <v-divider class="my-4"></v-divider>
              
              <h3 class="text-h6 mb-4">Additional Information</h3>
              
              <v-row>
                <v-col cols="12" md="6">
                  <v-list-item>
                    <v-list-item-icon>
                      <v-icon>mdi-phone</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>Phone</v-list-item-title>
                      <v-list-item-subtitle>{{ user.phone || 'Not provided' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-list-item>
                    <v-list-item-icon>
                      <v-icon>mdi-map-marker</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>Location</v-list-item-title>
                      <v-list-item-subtitle>{{ user.location || 'Not provided' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-list-item>
                    <v-list-item-icon>
                      <v-icon>mdi-domain</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>Company</v-list-item-title>
                      <v-list-item-subtitle>{{ user.company || 'Not provided' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-col>
                
                <v-col cols="12" md="6">
                  <v-list-item>
                    <v-list-item-icon>
                      <v-icon>mdi-web</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>Website</v-list-item-title>
                      <v-list-item-subtitle>{{ user.website || 'Not provided' }}</v-list-item-subtitle>
                    </v-list-item-content>
                  </v-list-item>
                </v-col>
              </v-row>
            </div>
          </v-tab-item>
          
          <!-- Activity Tab -->
          <v-tab-item>
            <div class="pa-4">
              <h3 class="text-h6 mb-4">Recent Activity</h3>
              
              <v-timeline dense>
                <v-timeline-item
                  v-for="(activity, index) in userActivity"
                  :key="index"
                  :color="getActivityColor(activity.type)"
                  small
                >
                  <div class="d-flex justify-space-between">
                    <div>
                      <div class="font-weight-medium">{{ activity.message }}</div>
                      <div class="text-caption">{{ activity.details }}</div>
                    </div>
                    <div class="text-caption">{{ formatDate(activity.timestamp) }}</div>
                  </div>
                </v-timeline-item>
                
                <v-timeline-item v-if="userActivity.length === 0" color="grey" small>
                  <div class="font-weight-medium">No activity recorded</div>
                </v-timeline-item>
              </v-timeline>
            </div>
          </v-tab-item>
          
          <!-- Security Tab -->
          <v-tab-item>
            <div class="pa-4">
              <h3 class="text-h6 mb-4">Security Settings</h3>
              
              <v-list>
                <v-list-item>
                  <v-list-item-icon>
                    <v-icon>mdi-lock</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title>Password</v-list-item-title>
                    <v-list-item-subtitle>Last changed: {{ formatDate(user.password_changed_at) || 'Never' }}</v-list-item-subtitle>
                  </v-list-item-content>
                  <v-list-item-action>
                    <v-btn text color="primary">Reset</v-btn>
                  </v-list-item-action>
                </v-list-item>
                
                <v-list-item>
                  <v-list-item-icon>
                    <v-icon>mdi-two-factor-authentication</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title>Two-Factor Authentication</v-list-item-title>
                    <v-list-item-subtitle>{{ user.mfa_enabled ? 'Enabled' : 'Disabled' }}</v-list-item-subtitle>
                  </v-list-item-content>
                  <v-list-item-action>
                    <v-btn text color="primary">{{ user.mfa_enabled ? 'Disable' : 'Enable' }}</v-btn>
                  </v-list-item-action>
                </v-list-item>
                
                <v-list-item>
                  <v-list-item-icon>
                    <v-icon>mdi-email-check</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title>Email Verification</v-list-item-title>
                    <v-list-item-subtitle>{{ user.email_verified ? 'Verified' : 'Not verified' }}</v-list-item-subtitle>
                  </v-list-item-content>
                  <v-list-item-action>
                    <v-btn text color="primary" :disabled="user.email_verified">Verify</v-btn>
                  </v-list-item-action>
                </v-list-item>
                
                <v-list-item>
                  <v-list-item-icon>
                    <v-icon>mdi-account-lock</v-icon>
                  </v-list-item-icon>
                  <v-list-item-content>
                    <v-list-item-title>Account Status</v-list-item-title>
                    <v-list-item-subtitle>{{ user.status }}</v-list-item-subtitle>
                  </v-list-item-content>
                  <v-list-item-action>
                    <v-btn 
                      text 
                      :color="user.status === 'active' ? 'error' : 'success'"
                    >
                      {{ user.status === 'active' ? 'Deactivate' : 'Activate' }}
                    </v-btn>
                  </v-list-item-action>
                </v-list-item>
              </v-list>
            </div>
          </v-tab-item>
        </v-tabs-items>
      </v-card-text>
      
      <v-divider></v-divider>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="primary" text @click="$emit('close')">Close</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'UserDetailDialog',
  
  props: {
    user: {
      type: Object,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      activeTab: 0,
      userActivity: [
        {
          type: 'login',
          message: 'User logged in',
          details: 'From IP: ***********',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          type: 'profile',
          message: 'Profile updated',
          details: 'Changed phone number',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          type: 'password',
          message: 'Password changed',
          details: 'From IP: ***********',
          timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]
    };
  },
  
  methods: {
    getInitials(firstName, lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    },
    
    getRoleColor(role) {
      switch (role) {
        case 'admin':
          return 'red';
        case 'manager':
          return 'orange';
        case 'editor':
          return 'blue';
        case 'user':
          return 'green';
        default:
          return 'grey';
      }
    },
    
    getStatusColor(status) {
      switch (status) {
        case 'active':
          return 'success';
        case 'inactive':
          return 'grey';
        case 'pending':
          return 'warning';
        case 'suspended':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    getActivityColor(type) {
      switch (type) {
        case 'login':
          return 'green';
        case 'logout':
          return 'orange';
        case 'profile':
          return 'blue';
        case 'password':
          return 'purple';
        case 'security':
          return 'red';
        default:
          return 'grey';
      }
    },
    
    formatDate(dateString) {
      if (!dateString) return '';
      
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
  }
};
</script>

<style scoped>
.user-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.user-header {
  padding: 16px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.02);
}
</style>
