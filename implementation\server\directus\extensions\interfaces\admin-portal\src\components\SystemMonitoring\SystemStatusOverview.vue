<template>
  <v-card class="system-status-overview">
    <v-card-title class="d-flex justify-space-between">
      <span>System Status Overview</span>
      <v-chip
        :color="statusColor"
        text-color="white"
        class="status-chip"
      >
        {{ statusText }}
      </v-chip>
    </v-card-title>
    
    <v-card-text>
      <v-skeleton-loader
        v-if="loading"
        type="card"
        class="mx-auto"
      ></v-skeleton-loader>
      
      <div v-else class="status-content">
        <div class="timestamp">
          Last updated: {{ formattedTimestamp }}
        </div>
        
        <v-row class="status-summary">
          <v-col cols="12" md="3">
            <v-card outlined class="status-card">
              <v-card-text class="text-center">
                <div class="status-icon">
                  <v-icon :color="cpuColor" size="36">mdi-cpu-64-bit</v-icon>
                </div>
                <div class="status-value">{{ systemHealth?.system?.cpu?.usage || '0' }}%</div>
                <div class="status-label">CPU Usage</div>
              </v-card-text>
            </v-card>
          </v-col>
          
          <v-col cols="12" md="3">
            <v-card outlined class="status-card">
              <v-card-text class="text-center">
                <div class="status-icon">
                  <v-icon :color="memoryColor" size="36">mdi-memory</v-icon>
                </div>
                <div class="status-value">{{ systemHealth?.system?.memory?.usage || '0' }}%</div>
                <div class="status-label">Memory Usage</div>
              </v-card-text>
            </v-card>
          </v-col>
          
          <v-col cols="12" md="3">
            <v-card outlined class="status-card">
              <v-card-text class="text-center">
                <div class="status-icon">
                  <v-icon color="primary" size="36">mdi-clock-outline</v-icon>
                </div>
                <div class="status-value">{{ systemHealth?.system?.uptime || '0' }}</div>
                <div class="status-label">System Uptime</div>
              </v-card-text>
            </v-card>
          </v-col>
          
          <v-col cols="12" md="3">
            <v-card outlined class="status-card">
              <v-card-text class="text-center">
                <div class="status-icon">
                  <v-icon :color="alertColor" size="36">mdi-alert-circle</v-icon>
                </div>
                <div class="status-value">{{ totalAlerts }}</div>
                <div class="status-label">Active Alerts</div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        
        <v-row class="service-summary">
          <v-col cols="12">
            <div class="service-status-header">
              <h3>Service Status</h3>
            </div>
            <div class="service-status-chips">
              <v-chip
                v-for="(service, key) in systemHealth?.services"
                :key="key"
                :color="getServiceStatusColor(service.status)"
                text-color="white"
                class="ma-1"
              >
                {{ formatServiceName(key) }}: {{ formatStatus(service.status) }}
              </v-chip>
            </div>
          </v-col>
        </v-row>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'SystemStatusOverview',
  
  props: {
    systemHealth: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    // Format timestamp
    formattedTimestamp() {
      if (!this.systemHealth || !this.systemHealth.timestamp) {
        return 'N/A';
      }
      
      return new Date(this.systemHealth.timestamp).toLocaleString();
    },
    
    // Get overall system status
    systemStatus() {
      if (!this.systemHealth) {
        return 'unknown';
      }
      
      // Check if any service is unhealthy
      const services = this.systemHealth.services || {};
      const hasUnhealthyService = Object.values(services).some(
        service => service.status === 'unhealthy'
      );
      
      if (hasUnhealthyService) {
        return 'unhealthy';
      }
      
      // Check if any service is degraded
      const hasDegradedService = Object.values(services).some(
        service => service.status === 'degraded'
      );
      
      if (hasDegradedService) {
        return 'degraded';
      }
      
      // Check if there are critical alerts
      const alerts = this.systemHealth.alerts || {};
      if (alerts.critical && alerts.critical > 0) {
        return 'unhealthy';
      }
      
      // Check if there are warning alerts
      if (alerts.warning && alerts.warning > 0) {
        return 'degraded';
      }
      
      // Check CPU and memory usage
      const system = this.systemHealth.system || {};
      const cpuUsage = system.cpu?.usage ? parseFloat(system.cpu.usage) : 0;
      const memoryUsage = system.memory?.usage ? parseFloat(system.memory.usage) : 0;
      
      if (cpuUsage > 90 || memoryUsage > 90) {
        return 'degraded';
      }
      
      return 'healthy';
    },
    
    // Get status text
    statusText() {
      switch (this.systemStatus) {
        case 'healthy':
          return 'Healthy';
        case 'degraded':
          return 'Degraded';
        case 'unhealthy':
          return 'Unhealthy';
        default:
          return 'Unknown';
      }
    },
    
    // Get status color
    statusColor() {
      switch (this.systemStatus) {
        case 'healthy':
          return 'success';
        case 'degraded':
          return 'warning';
        case 'unhealthy':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    // Get CPU usage color
    cpuColor() {
      if (!this.systemHealth || !this.systemHealth.system || !this.systemHealth.system.cpu) {
        return 'grey';
      }
      
      const cpuUsage = parseFloat(this.systemHealth.system.cpu.usage);
      
      if (cpuUsage > 90) {
        return 'error';
      } else if (cpuUsage > 70) {
        return 'warning';
      } else {
        return 'success';
      }
    },
    
    // Get memory usage color
    memoryColor() {
      if (!this.systemHealth || !this.systemHealth.system || !this.systemHealth.system.memory) {
        return 'grey';
      }
      
      const memoryUsage = parseFloat(this.systemHealth.system.memory.usage);
      
      if (memoryUsage > 90) {
        return 'error';
      } else if (memoryUsage > 70) {
        return 'warning';
      } else {
        return 'success';
      }
    },
    
    // Get alert color
    alertColor() {
      if (!this.systemHealth || !this.systemHealth.alerts) {
        return 'grey';
      }
      
      const alerts = this.systemHealth.alerts;
      
      if (alerts.critical && alerts.critical > 0) {
        return 'error';
      } else if (alerts.warning && alerts.warning > 0) {
        return 'warning';
      } else {
        return 'success';
      }
    },
    
    // Get total alerts
    totalAlerts() {
      if (!this.systemHealth || !this.systemHealth.alerts) {
        return 0;
      }
      
      const alerts = this.systemHealth.alerts;
      return (alerts.critical || 0) + (alerts.warning || 0) + (alerts.info || 0);
    }
  },
  
  methods: {
    // Format service name
    formatServiceName(key) {
      return key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    },
    
    // Format status
    formatStatus(status) {
      return status.charAt(0).toUpperCase() + status.slice(1);
    },
    
    // Get service status color
    getServiceStatusColor(status) {
      switch (status) {
        case 'healthy':
          return 'success';
        case 'degraded':
          return 'warning';
        case 'unhealthy':
          return 'error';
        default:
          return 'grey';
      }
    }
  }
};
</script>

<style scoped>
.system-status-overview {
  margin-bottom: 20px;
}

.status-chip {
  font-weight: bold;
}

.timestamp {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
  margin-bottom: 16px;
}

.status-summary {
  margin-bottom: 20px;
}

.status-card {
  height: 100%;
  transition: transform 0.2s;
}

.status-card:hover {
  transform: translateY(-5px);
}

.status-icon {
  margin-bottom: 8px;
}

.status-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 4px;
}

.status-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.service-status-header {
  margin-bottom: 12px;
}

.service-status-chips {
  display: flex;
  flex-wrap: wrap;
}
</style>
