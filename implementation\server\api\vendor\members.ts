import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';

// Validation schema for creating a member
const createMemberSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(1, 'Name is required'),
  role: z.enum(['admin', 'editor', 'viewer'], {
    errorMap: () => ({ message: 'Role must be admin, editor, or viewer' }),
  }),
});

// Validation schema for updating a member
const updateMemberSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  role: z
    .enum(['admin', 'editor', 'viewer'], {
      errorMap: () => ({ message: 'Role must be admin, editor, or viewer' }),
    })
    .optional(),
  status: z
    .enum(['invited', 'active', 'suspended'], {
      errorMap: () => ({ message: 'Status must be invited, active, or suspended' }),
    })
    .optional(),
});

/**
 * Vendor members API endpoint
 *
 * This endpoint handles CRUD operations for vendor team members.
 * It supports:
 * - GET: List all members for the vendor
 * - POST: Create a new member
 * - PUT: Update an existing member
 * - DELETE: Remove a member
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Check if user is authenticated
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get vendor ID for the authenticated user
    const { data: vendorData, error: vendorError } = await supabase
      .from('vendors')
      .select('id')
      .eq('user_id', session.user.id)
      .single();

    if (vendorError || !vendorData) {
      logger.error('Vendor members API - not a vendor', {
        userId: session.user.id,
        error: vendorError?.message,
      });
      return res.status(403).json({ error: 'User is not a vendor' });
    }

    const vendorId = vendorData.id;

    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        return handleGetMembers(req, res, supabase, vendorId);
      case 'POST':
        return handleCreateMember(req, res, supabase, vendorId);
      case 'PUT':
        return handleUpdateMember(req, res, supabase, vendorId);
      case 'DELETE':
        return handleDeleteMember(req, res, supabase, vendorId);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    logger.error('Unexpected error in vendor members API', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle GET request to list all members
 */
async function handleGetMembers(
  req: NextApiRequest,
  res: NextApiResponse,
  supabase: any,
  vendorId: string,
) {
  try {
    // Parse query parameters
    const page = parseInt(req.query.page as string) || 0;
    const pageSize = parseInt(req.query.pageSize as string) || 10;
    const from = page * pageSize;
    const to = from + pageSize - 1;

    // Get total count
    const { count, error: countError } = await supabase
      .from('vendor_members')
      .select('*', { count: 'exact', head: true })
      .eq('vendor_id', vendorId);

    if (countError) {
      logger.error('Error getting vendor members count', {
        vendorId,
        error: countError.message,
      });
      return res.status(500).json({ error: 'Failed to get members count' });
    }

    // Get members with pagination
    const { data, error } = await supabase
      .from('vendor_members')
      .select('id, email, name, role, created_at, last_login, status')
      .eq('vendor_id', vendorId)
      .order('created_at', { ascending: false })
      .range(from, to);

    if (error) {
      logger.error('Error getting vendor members', {
        vendorId,
        error: error.message,
      });
      return res.status(500).json({ error: 'Failed to get members' });
    }

    return res.status(200).json({
      members: data,
      totalCount: count,
      page,
      pageSize,
    });
  } catch (error) {
    logger.error('Unexpected error getting vendor members', { vendorId, error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle POST request to create a new member
 */
async function handleCreateMember(
  req: NextApiRequest,
  res: NextApiResponse,
  supabase: any,
  vendorId: string,
) {
  try {
    // Validate request body
    const validationResult = createMemberSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.errors,
      });
    }

    const { email, name, role } = validationResult.data;

    // Check if member already exists
    const { data: existingMember, error: checkError } = await supabase
      .from('vendor_members')
      .select('id')
      .eq('vendor_id', vendorId)
      .eq('email', email)
      .single();

    if (existingMember) {
      return res.status(409).json({ error: 'Member with this email already exists' });
    }

    // Create new member
    const { data, error } = await supabase
      .from('vendor_members')
      .insert([
        {
          vendor_id: vendorId,
          email,
          name,
          role,
          status: 'invited',
        },
      ])
      .select()
      .single();

    if (error) {
      logger.error('Error creating vendor member', {
        vendorId,
        email,
        error: error.message,
      });
      return res.status(500).json({ error: 'Failed to create member' });
    }

    // Send invitation email (this would be implemented in a real system)
    // For now, we'll just log it
    logger.info('Vendor member invitation created', {
      vendorId,
      memberId: data.id,
      email,
    });

    return res.status(201).json({ member: data });
  } catch (error) {
    logger.error('Unexpected error creating vendor member', { vendorId, error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle PUT request to update an existing member
 */
async function handleUpdateMember(
  req: NextApiRequest,
  res: NextApiResponse,
  supabase: any,
  vendorId: string,
) {
  try {
    // Get member ID from query
    const memberId = req.query.id as string;
    if (!memberId) {
      return res.status(400).json({ error: 'Member ID is required' });
    }

    // Validate request body
    const validationResult = updateMemberSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: 'Validation failed',
        details: validationResult.error.errors,
      });
    }

    // Check if member exists and belongs to this vendor
    const { data: existingMember, error: checkError } = await supabase
      .from('vendor_members')
      .select('id')
      .eq('vendor_id', vendorId)
      .eq('id', memberId)
      .single();

    if (checkError || !existingMember) {
      return res.status(404).json({ error: 'Member not found' });
    }

    // Update member
    const { data, error } = await supabase
      .from('vendor_members')
      .update(validationResult.data)
      .eq('id', memberId)
      .eq('vendor_id', vendorId)
      .select()
      .single();

    if (error) {
      logger.error('Error updating vendor member', {
        vendorId,
        memberId,
        error: error.message,
      });
      return res.status(500).json({ error: 'Failed to update member' });
    }

    logger.info('Vendor member updated', {
      vendorId,
      memberId,
      updates: validationResult.data,
    });

    return res.status(200).json({ member: data });
  } catch (error) {
    logger.error('Unexpected error updating vendor member', { vendorId, error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Handle DELETE request to remove a member
 */
async function handleDeleteMember(
  req: NextApiRequest,
  res: NextApiResponse,
  supabase: any,
  vendorId: string,
) {
  try {
    // Get member ID from query
    const memberId = req.query.id as string;
    if (!memberId) {
      return res.status(400).json({ error: 'Member ID is required' });
    }

    // Check if member exists and belongs to this vendor
    const { data: existingMember, error: checkError } = await supabase
      .from('vendor_members')
      .select('id, email')
      .eq('vendor_id', vendorId)
      .eq('id', memberId)
      .single();

    if (checkError || !existingMember) {
      return res.status(404).json({ error: 'Member not found' });
    }

    // Delete member
    const { error } = await supabase
      .from('vendor_members')
      .delete()
      .eq('id', memberId)
      .eq('vendor_id', vendorId);

    if (error) {
      logger.error('Error deleting vendor member', {
        vendorId,
        memberId,
        error: error.message,
      });
      return res.status(500).json({ error: 'Failed to delete member' });
    }

    logger.info('Vendor member deleted', {
      vendorId,
      memberId,
      email: existingMember.email,
    });

    return res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Unexpected error deleting vendor member', { vendorId, error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
