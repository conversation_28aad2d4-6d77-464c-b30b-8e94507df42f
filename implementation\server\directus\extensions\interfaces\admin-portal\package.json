{"name": "admin-portal", "version": "1.0.0", "description": "Admin Portal for MVS-VR Platform", "main": "index.js", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"chart.js": "^3.9.1", "core-js": "^3.26.1", "vue": "^2.7.14", "vue-router": "^3.6.5", "vuetify": "^2.6.12"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-service": "~5.0.8", "babel-eslint": "^10.1.0", "eslint": "^8.28.0", "eslint-plugin-vue": "^9.8.0", "sass": "^1.56.1", "sass-loader": "^13.2.0", "vue-cli-plugin-vuetify": "~2.5.8", "vue-template-compiler": "^2.7.14", "vuetify-loader": "^1.9.2"}}