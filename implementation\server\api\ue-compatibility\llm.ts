/**
 * UE LLM Compatibility
 *
 * This module provides endpoints for LLM integration
 * for Unreal Engine 5.4+ compatibility.
 */

import { Router } from 'express';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { z } from 'zod';
import { LLMService } from '../../services/llm-service';

// Create router
const router = Router();

// Define UE version schema
const UEVersionSchema = z.object({
  major: z.number(),
  minor: z.number(),
  patch: z.number(),
});

// Define LLM query schema
const LLMQuerySchema = z.object({
  query: z.string(),
  context: z.record(z.any()).optional(),
  conversation_id: z.string().optional(),
  ue_version: UEVersionSchema,
  use_local_fallback: z.boolean().optional(),
});

// Define LLM tool usage schema
const LLMToolUsageSchema = z.object({
  conversation_id: z.string(),
  tool_name: z.string(),
  tool_input: z.record(z.any()),
  ue_version: UEVersionSchema,
});

/**
 * Query LLM for UE compatibility
 *
 * @param req Request object
 * @param res Response object
 */
async function queryLLM(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = LLMQuerySchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract query, context, conversation ID, UE version, and fallback flag
    const { query, context, conversation_id, ue_version, use_local_fallback } = bodyResult.data;

    // Log the request
    logger.info('LLM query request', {
      query_length: query.length,
      has_context: !!context,
      conversation_id,
      ue_version,
      use_local_fallback,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create LLM service
    const llmService = new LLMService(supabase);

    // Query LLM
    const response = await llmService.query(
      query,
      context || {},
      conversation_id,
      use_local_fallback || false,
    );

    // Return response
    return res.status(200).json({
      response: response.text,
      conversation_id: response.conversation_id,
      has_tool_calls: response.has_tool_calls,
      available_tools: response.available_tools,
      ue_version,
    });
  } catch (error) {
    return errorHandler(error, req, res);
  }
}

/**
 * Use LLM tool for UE compatibility
 *
 * @param req Request object
 * @param res Response object
 */
async function useLLMTool(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = LLMToolUsageSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract conversation ID, tool name, tool input, and UE version
    const { conversation_id, tool_name, tool_input, ue_version } = bodyResult.data;

    // Log the request
    logger.info('LLM tool usage request', {
      conversation_id,
      tool_name,
      ue_version,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if tool usage is supported for this UE version
    if (!isToolUsageSupportedForUE(ue_version)) {
      return res.status(400).json({
        error: 'Tool usage not supported',
        message: `LLM tool usage is not supported for UE ${ue_version.major}.${ue_version.minor}.${ue_version.patch}`,
      });
    }

    // Create LLM service
    const llmService = new LLMService(supabase);

    // Use tool
    const response = await llmService.useTool(conversation_id, tool_name, tool_input);

    // Return response
    return res.status(200).json({
      response: response.text,
      conversation_id: response.conversation_id,
      tool_name,
      tool_result: response.tool_result,
      ue_version,
    });
  } catch (error) {
    return errorHandler(error, req, res);
  }
}

/**
 * Check if tool usage is supported for UE version
 *
 * @param version UE version
 * @returns True if supported, false otherwise
 */
function isToolUsageSupportedForUE(version: z.infer<typeof UEVersionSchema>): boolean {
  // Tool usage is supported in UE 5.4+
  if (version.major === 5 && version.minor >= 4) {
    return true;
  }

  // Not supported in other versions
  return false;
}

// Register routes
router.post('/query', queryLLM);
router.post('/tool', useLLMTool);

// Export router
export default router;
