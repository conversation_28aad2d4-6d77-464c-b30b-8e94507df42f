# Load exported Docker images on target server
param([string]$ManifestPath = "export-manifest.json")

if (-not (Test-Path $ManifestPath)) {
    Write-Error "export-manifest.json not found"
    exit 1
}

Write-Host "Loading Docker images from exports..."

# Extract version from manifest
$manifest = Get-Content $ManifestPath | ConvertFrom-Json
$version = $manifest.version
Write-Host "Loading images for version: $version"

# Load each image
Get-ChildItem "*.tar*" | ForEach-Object {
    Write-Host "Loading $($_.Name)..."

    if ($_.Extension -eq ".zip") {
        # Handle compressed files
        Expand-Archive -Path $_.FullName -DestinationPath "temp" -Force
        $tarFile = Get-ChildItem "temp/*.tar" | Select-Object -First 1
        docker load -i $tarFile.FullName
        Remove-Item "temp" -Recurse -Force
    }
    else {
        docker load -i $_.FullName
    }

    Write-Host "Loaded $($_.Name)"
}

Write-Host "All images loaded successfully!"
Write-Host "Run 'docker images' to see loaded images"
