-- Create extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create vendors table
CREATE TABLE vendors (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  api_key TEXT NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create environments table
CREATE TABLE environments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  key TEXT NOT NULL,
  name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(vendor_id, key)
);

-- Create bootstrap_configs table
CREATE TABLE bootstrap_configs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  environment_key TEXT NOT NULL,
  configuration_ids UUID[] NOT NULL,
  settings JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(vendor_id, environment_key)
);

-- Create configurations table
CREATE TABLE configurations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  type TEXT NOT NULL,
  url TEXT NOT NULL,
  hash TEXT NOT NULL,
  version TEXT NOT NULL,
  required BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create asset_bundles table
CREATE TABLE asset_bundles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  hash TEXT NOT NULL,
  version TEXT NOT NULL,
  asset_ids UUID[] NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create assets table
CREATE TABLE assets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  url TEXT NOT NULL,
  thumbnail_url TEXT,
  hash TEXT NOT NULL,
  size INTEGER NOT NULL,
  version TEXT NOT NULL,
  metadata JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create scenes table
CREATE TABLE scenes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  flow JSONB NOT NULL,
  version TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create blueprints table
CREATE TABLE blueprints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  tags TEXT[] NOT NULL DEFAULT '{}',
  script JSONB NOT NULL,
  version TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create llm_usage_metrics table
CREATE TABLE llm_usage_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  llm_monthly_token_limit INTEGER NOT NULL DEFAULT 100000,
  llm_tokens_used INTEGER NOT NULL DEFAULT 0,
  llm_reset_date DATE NOT NULL DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create telemetry_events table
CREATE TABLE telemetry_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_data JSONB NOT NULL,
  client_version TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at trigger to all tables
CREATE TRIGGER update_vendors_updated_at
BEFORE UPDATE ON vendors
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_environments_updated_at
BEFORE UPDATE ON environments
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bootstrap_configs_updated_at
BEFORE UPDATE ON bootstrap_configs
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_configurations_updated_at
BEFORE UPDATE ON configurations
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_asset_bundles_updated_at
BEFORE UPDATE ON asset_bundles
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_assets_updated_at
BEFORE UPDATE ON assets
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scenes_updated_at
BEFORE UPDATE ON scenes
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blueprints_updated_at
BEFORE UPDATE ON blueprints
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_llm_usage_metrics_updated_at
BEFORE UPDATE ON llm_usage_metrics
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create RLS policies

-- Enable RLS on all tables
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE environments ENABLE ROW LEVEL SECURITY;
ALTER TABLE bootstrap_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE configurations ENABLE ROW LEVEL SECURITY;
ALTER TABLE asset_bundles ENABLE ROW LEVEL SECURITY;
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE scenes ENABLE ROW LEVEL SECURITY;
ALTER TABLE blueprints ENABLE ROW LEVEL SECURITY;
ALTER TABLE llm_usage_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE telemetry_events ENABLE ROW LEVEL SECURITY;

-- Create policies for vendors table
CREATE POLICY "Vendors are viewable by authenticated users" ON vendors
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Vendors are editable by admins" ON vendors
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for environments table
CREATE POLICY "Environments are viewable by authenticated users" ON environments
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Environments are editable by admins" ON environments
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for bootstrap_configs table
CREATE POLICY "Bootstrap configs are viewable by authenticated users" ON bootstrap_configs
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Bootstrap configs are editable by admins" ON bootstrap_configs
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for configurations table
CREATE POLICY "Configurations are viewable by authenticated users" ON configurations
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Configurations are editable by admins" ON configurations
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for asset_bundles table
CREATE POLICY "Asset bundles are viewable by authenticated users" ON asset_bundles
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Asset bundles are editable by admins" ON asset_bundles
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for assets table
CREATE POLICY "Assets are viewable by authenticated users" ON assets
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Assets are editable by admins" ON assets
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for scenes table
CREATE POLICY "Scenes are viewable by authenticated users" ON scenes
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Scenes are editable by admins" ON scenes
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for blueprints table
CREATE POLICY "Blueprints are viewable by authenticated users" ON blueprints
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Blueprints are editable by admins" ON blueprints
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for llm_usage_metrics table
CREATE POLICY "LLM usage metrics are viewable by authenticated users" ON llm_usage_metrics
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "LLM usage metrics are editable by admins" ON llm_usage_metrics
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for telemetry_events table
CREATE POLICY "Telemetry events are viewable by admins" ON telemetry_events
  FOR SELECT USING (auth.role() = 'admin');

CREATE POLICY "Telemetry events are insertable by authenticated users" ON telemetry_events
  FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- Create indexes for performance
CREATE INDEX idx_vendors_api_key ON vendors(api_key);
CREATE INDEX idx_environments_vendor_id ON environments(vendor_id);
CREATE INDEX idx_environments_key ON environments(key);
CREATE INDEX idx_bootstrap_configs_vendor_id ON bootstrap_configs(vendor_id);
CREATE INDEX idx_bootstrap_configs_environment_key ON bootstrap_configs(environment_key);
CREATE INDEX idx_asset_bundles_vendor_id ON asset_bundles(vendor_id);
CREATE INDEX idx_assets_vendor_id ON assets(vendor_id);
CREATE INDEX idx_assets_type ON assets(type);
CREATE INDEX idx_scenes_vendor_id ON scenes(vendor_id);
CREATE INDEX idx_blueprints_vendor_id ON blueprints(vendor_id);
CREATE INDEX idx_blueprints_tags ON blueprints USING GIN(tags);
CREATE INDEX idx_llm_usage_metrics_vendor_id ON llm_usage_metrics(vendor_id);
CREATE INDEX idx_telemetry_events_vendor_id ON telemetry_events(vendor_id);
CREATE INDEX idx_telemetry_events_event_type ON telemetry_events(event_type);
CREATE INDEX idx_telemetry_events_created_at ON telemetry_events(created_at);
