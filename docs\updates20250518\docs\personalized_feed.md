# Personalized Product Feed in VR

## Overview

This document outlines the structure and behavior of a personalized product recommendation system within the Kanousei VR platform. It dynamically adjusts the product feed shown to users based on their interaction history, profile, or preferences.

---

## Objectives

* Enhance user engagement through curated content.
* Increase discoverability of relevant vendor products.
* Ensure real-time performance with minimal latency.

---

## Feed Structure

The product feed is rendered in 3D as:

* A dynamic **carousel wall**, **scrollable shelf**, or **grid gallery**.
* Displayed on entry in designated spaces (e.g., Startup Lobby, Vendor Spotlight Area).

---

## Personalization Criteria

### Input Signals:

* Rooms/spaces visited
* Products interacted with (hover, inspect, favorite)
* Vendor follow/subscription
* Time spent near certain product categories

### Optional Inputs:

* Language preference
* Style tags: modern, minimalist, traditional
* AI Assistant tags based on past queries

---

## Scoring Logic

Each product receives a relevance score from 0 to 1.0:

```json
{
  "productId": "lamp_amber_301",
  "relevance": 0.92,
  "tags": ["lighting", "modern", "warm"]
}
```

Ranking is calculated using a weighted blend of recentness, frequency, and session behavior.

---

## Feed API Response Sample

```json
{
  "userId": "guest_771",
  "recommendedProducts": [
    "chair_modern_04",
    "sofa_lounge_12",
    "table_glass_round"
  ],
  "renderType": "wallGallery",
  "config": {
    "maxItems": 12,
    "layout": "centered-carousel"
  }
}
```

---

## Runtime Integration

* Feed is updated when user enters a new space or on defined intervals.
* Stored in local memory per session.
* If user disables personalization, fallback to trending + random products.

---

## Admin Tools

* System Admin can:

  * View feed logs per user/session
  * Override or blacklist product types per scene
  * Assign promotional weighting to certain vendor SKUs

---

## Vendor Insights

Vendors can:

* See how often their products are recommended
* View click-through rates and favorite saves
* Submit feed optimization tags

---

## Performance Notes

* Feed system must preload thumbnails and mesh proxies.
* Initial recommendations cached client-side for smooth entry animation.
* Server feed response should complete in <150ms for VR fluidity.
