/**
 * CSRF Protection Middleware
 *
 * This middleware implements CSRF protection using iron-session for secure
 * token storage. It replaces the deprecated csurf package with a more modern
 * and secure approach.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { getIronSession, IronSession, IronSessionOptions } from 'iron-session';
import crypto from 'crypto';
import { logger } from '../../lib/logger';

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32;

// Iron session configuration
const ironOptions: IronSessionOptions = {
  cookieName: 'mvs_session',
  password: process.env.SESSION_SECRET || 'complex_password_at_least_32_characters_long',
  cookieOptions: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict',
    path: '/',
  },
};

// Interface for session with CSRF token
interface SessionData {
  csrfToken?: string;
  userId?: string;
}

/**
 * Generate a CSRF token
 * @returns {string} CSRF token
 */
export function generateCsrfToken(): string {
  return crypto.randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
}

/**
 * Get or create a CSRF token in the session
 * @param req - Request object
 * @param res - Response object
 * @returns {Promise<string>} CSRF token
 */
export async function getOrCreateCsrfToken(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<string> {
  const session = await getIronSession<SessionData>(req, res, ironOptions);

  // Create a new token if one doesn't exist
  if (!session.csrfToken) {
    session.csrfToken = generateCsrfToken();
    await session.save();
  }

  return session.csrfToken;
}

/**
 * Validate a CSRF token
 * @param req - Request object
 * @param res - Response object
 * @param token - Token to validate
 * @returns {Promise<boolean>} Whether the token is valid
 */
export async function validateCsrfToken(
  req: NextApiRequest,
  res: NextApiResponse,
  token: string,
): Promise<boolean> {
  const session = await getIronSession<SessionData>(req, res, ironOptions);
  return session.csrfToken === token;
}

/**
 * CSRF protection middleware for Next.js API routes
 * @param options - Options
 * @returns {Function} Next.js API middleware
 */
export function csrfProtection(
  options: {
    headerName?: string;
    cookieName?: string;
    methods?: string[];
    ignorePaths?: string[];
  } = {},
) {
  const {
    headerName = 'X-CSRF-Token',
    methods = ['POST', 'PUT', 'DELETE', 'PATCH'],
    ignorePaths = [],
  } = options;

  return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
    try {
      // Skip CSRF check for non-mutating methods
      if (!methods.includes(req.method || '')) {
        return next();
      }

      // Skip CSRF check for ignored paths
      const path = req.url || '';
      if (ignorePaths.some(ignorePath => path.startsWith(ignorePath))) {
        return next();
      }

      // Get CSRF token from request
      const token = (req.headers[headerName.toLowerCase()] as string) || req.body?._csrf;

      // If no token, reject the request
      if (!token) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'CSRF_TOKEN_MISSING',
            message: 'CSRF token is missing',
          },
        });
      }

      // Check if token is valid
      const isValid = await validateCsrfToken(req, res, token);

      if (!isValid) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'CSRF_TOKEN_INVALID',
            message: 'CSRF token is invalid or expired',
          },
        });
      }

      // Token is valid, continue
      next();
    } catch (error) {
      logger.error('Error in CSRF protection middleware:', error);
      next();
    }
  };
}

/**
 * CSRF token generator middleware for Express
 * @param options - Options
 * @returns {Function} Express middleware
 */
export function csrfTokenGenerator(
  options: {
    cookieName?: string;
  } = {},
) {
  return async (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
    try {
      // Generate a new CSRF token
      const token = await getOrCreateCsrfToken(req, res);

      // Add token to response locals for templates
      (res as any).locals = (res as any).locals || {};
      (res as any).locals.csrfToken = token;

      next();
    } catch (error) {
      logger.error('Error in CSRF token generator middleware:', error);
      next();
    }
  };
}

export default {
  csrfProtection,
  csrfTokenGenerator,
  generateCsrfToken,
  getOrCreateCsrfToken,
  validateCsrfToken,
};
