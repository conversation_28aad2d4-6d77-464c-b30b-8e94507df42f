# DigitalOcean Deployment Troubleshooting Guide

## Common Issues and Solutions

### 1. Volume Mount Issues

**Problem**: Volume not mounting or permission denied
```bash
# Symptoms
mount: /mnt/mvs-data: permission denied
ls: cannot access '/mnt/mvs-data': Permission denied
```

**Solutions**:
```bash
# Check if volume is properly attached
lsblk

# Check volume status in DigitalOcean console
# Ensure volume is attached to correct droplet

# Remount volume
umount /mnt/mvs-data
mount -o discard,defaults /dev/disk/by-id/scsi-0DO_Volume_mvs-staging-data /mnt/mvs-data

# Fix permissions
chown -R root:root /mnt/mvs-data
chmod 755 /mnt/mvs-data

# Verify fstab entry
cat /etc/fstab | grep mvs-data
```

### 2. Docker Permission Issues

**Problem**: Permission denied when running Docker commands
```bash
# Symptoms
docker: Got permission denied while trying to connect to the Docker daemon socket
```

**Solutions**:
```bash
# Add user to docker group
usermod -aG docker $USER

# Apply group changes
newgrp docker

# Or logout and login again
exit
ssh root@YOUR_SERVER_IP

# Verify docker works
docker --version
docker ps
```

### 3. Docker Compose Build Failures

**Problem**: Docker images fail to build
```bash
# Symptoms
ERROR: failed to solve: process "/bin/sh -c npm install" did not complete successfully
```

**Solutions**:
```bash
# Clear Docker cache
docker system prune -a

# Build with no cache
docker-compose -f docker-compose.staging.yml build --no-cache

# Check available disk space
df -h

# Free up space if needed
docker system prune -a --volumes

# Check memory usage
free -h

# If low memory, add swap
fallocate -l 2G /swapfile
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile
echo '/swapfile none swap sw 0 0' >> /etc/fstab
```

### 4. Service Startup Issues

**Problem**: Services fail to start or keep restarting
```bash
# Symptoms
mvs-api-staging    Exit 1
mvs-postgres-staging    Restarting
```

**Solutions**:
```bash
# Check service logs
docker-compose -f docker-compose.staging.yml logs api
docker-compose -f docker-compose.staging.yml logs postgres

# Check service status
docker-compose -f docker-compose.staging.yml ps

# Restart specific service
docker-compose -f docker-compose.staging.yml restart api

# Check environment variables
docker-compose -f docker-compose.staging.yml exec api env | grep -E "(DATABASE|REDIS|SUPABASE)"

# Verify .env.staging file
cat .env.staging | grep -v "^#" | grep -v "^$"

# Check port conflicts
netstat -tulpn | grep -E "(3000|8055|5432|6379)"
```

### 5. Database Connection Issues

**Problem**: Cannot connect to PostgreSQL
```bash
# Symptoms
ECONNREFUSED 127.0.0.1:5432
could not connect to server: Connection refused
```

**Solutions**:
```bash
# Check PostgreSQL container
docker-compose -f docker-compose.staging.yml logs postgres

# Check if PostgreSQL is ready
docker-compose -f docker-compose.staging.yml exec postgres pg_isready -U postgres

# Connect to database manually
docker-compose -f docker-compose.staging.yml exec postgres psql -U postgres -d mvs_staging

# Check database exists
docker-compose -f docker-compose.staging.yml exec postgres psql -U postgres -c "\l"

# Recreate database if needed
docker-compose -f docker-compose.staging.yml exec postgres psql -U postgres -c "DROP DATABASE IF EXISTS mvs_staging;"
docker-compose -f docker-compose.staging.yml exec postgres psql -U postgres -c "CREATE DATABASE mvs_staging;"
```

### 6. SSL Certificate Issues

**Problem**: SSL certificates fail to install or renew
```bash
# Symptoms
Challenge failed for domain api.mvs.kanousai.com
The following errors were reported by the server: Domain validation failed
```

**Solutions**:
```bash
# Check DNS propagation
nslookup api.mvs.kanousai.com
dig api.mvs.kanousai.com

# Verify Nginx is running
systemctl status nginx
nginx -t

# Check if port 80 is accessible
curl -I http://api.mvs.kanousai.com

# Stop services temporarily for certificate generation
docker-compose -f docker-compose.staging.yml stop nginx

# Generate certificates manually
certbot certonly --standalone -d api.mvs.kanousai.com
certbot certonly --standalone -d admin.mvs.kanousai.com
certbot certonly --standalone -d staging.mvs.kanousai.com

# Restart services
docker-compose -f docker-compose.staging.yml start nginx

# Test certificate renewal
certbot renew --dry-run
```

### 7. Nginx Configuration Issues

**Problem**: Nginx fails to start or proxy requests
```bash
# Symptoms
nginx: [emerg] bind() to 0.0.0.0:80 failed (98: Address already in use)
502 Bad Gateway
```

**Solutions**:
```bash
# Check Nginx configuration
nginx -t

# Check what's using port 80
netstat -tulpn | grep :80

# Kill processes using port 80
fuser -k 80/tcp

# Check Nginx error logs
tail -f /var/log/nginx/error.log

# Restart Nginx
systemctl restart nginx

# Check upstream services
curl http://localhost:3000/health
curl http://localhost:8055/server/health

# Verify proxy configuration
cat /etc/nginx/sites-enabled/mvs-staging
```

### 8. Memory and Disk Space Issues

**Problem**: Server running out of resources
```bash
# Symptoms
Cannot allocate memory
No space left on device
```

**Solutions**:
```bash
# Check disk usage
df -h
du -sh /opt/mvs-vr/*
du -sh /mnt/mvs-data/*

# Check memory usage
free -h
top

# Clean up Docker
docker system prune -a
docker volume prune

# Clean up logs
journalctl --vacuum-time=7d
find /var/log -name "*.log" -type f -mtime +7 -delete

# Add swap if needed
fallocate -l 4G /swapfile
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile

# Monitor resource usage
htop
iotop
```

### 9. Network Connectivity Issues

**Problem**: Services cannot reach external APIs
```bash
# Symptoms
ENOTFOUND hiyqiqbgiueyyvqoqhht.supabase.co
Connection timeout
```

**Solutions**:
```bash
# Test external connectivity
ping google.com
curl -I https://hiyqiqbgiueyyvqoqhht.supabase.co

# Check DNS resolution
nslookup hiyqiqbgiueyyvqoqhht.supabase.co
cat /etc/resolv.conf

# Test from container
docker-compose -f docker-compose.staging.yml exec api curl -I https://hiyqiqbgiueyyvqoqhht.supabase.co

# Check firewall rules
ufw status
iptables -L

# Check Docker network
docker network ls
docker network inspect mvs-vr-v2_mvs-network
```

### 10. Environment Variable Issues

**Problem**: Services cannot read environment variables
```bash
# Symptoms
Environment variable POSTGRES_PASSWORD is not set
Invalid configuration
```

**Solutions**:
```bash
# Check .env.staging file exists
ls -la .env.staging

# Verify environment variables are loaded
docker-compose -f docker-compose.staging.yml config

# Check specific service environment
docker-compose -f docker-compose.staging.yml exec api env

# Recreate services with new environment
docker-compose -f docker-compose.staging.yml down
docker-compose -f docker-compose.staging.yml up -d

# Generate missing secrets
openssl rand -base64 32
```

## Diagnostic Commands

### System Health Check
```bash
#!/bin/bash
echo "=== System Health Check ==="
echo "Date: $(date)"
echo "Uptime: $(uptime)"
echo "Disk Usage:"
df -h
echo "Memory Usage:"
free -h
echo "Docker Status:"
docker --version
docker-compose --version
echo "Services Status:"
docker-compose -f docker-compose.staging.yml ps
echo "Network Connectivity:"
ping -c 3 google.com
echo "SSL Certificates:"
certbot certificates
```

### Service Logs Collection
```bash
#!/bin/bash
LOG_DIR="/tmp/mvs-logs-$(date +%Y%m%d_%H%M%S)"
mkdir -p "$LOG_DIR"

# Collect Docker logs
docker-compose -f docker-compose.staging.yml logs > "$LOG_DIR/docker-compose.log"

# Collect individual service logs
for service in api directus postgres redis nginx; do
    docker-compose -f docker-compose.staging.yml logs "$service" > "$LOG_DIR/${service}.log" 2>&1
done

# Collect system logs
journalctl -u nginx > "$LOG_DIR/nginx-system.log"
journalctl -u docker > "$LOG_DIR/docker-system.log"

# Collect Nginx logs
cp /var/log/nginx/*.log "$LOG_DIR/" 2>/dev/null

echo "Logs collected in: $LOG_DIR"
tar -czf "$LOG_DIR.tar.gz" -C /tmp "$(basename $LOG_DIR)"
echo "Archive created: $LOG_DIR.tar.gz"
```

## Emergency Recovery

### Complete Service Restart
```bash
# Stop all services
docker-compose -f docker-compose.staging.yml down

# Clean up containers and networks
docker system prune -f

# Restart services
docker-compose -f docker-compose.staging.yml up -d

# Wait for services
sleep 60

# Check health
curl https://api.mvs.kanousai.com/health
curl https://admin.mvs.kanousai.com/server/health
```

### Restore from Backup
```bash
# Stop services
docker-compose -f docker-compose.staging.yml down

# Find latest backup
ls -la /mnt/mvs-data/backups/

# Restore database
BACKUP_DIR="/mnt/mvs-data/backups/LATEST_BACKUP"
docker-compose -f docker-compose.staging.yml up -d postgres
sleep 30
docker-compose -f docker-compose.staging.yml exec -T postgres psql -U postgres -d mvs_staging < "$BACKUP_DIR/database.sql"

# Restore volumes
tar -xzf "$BACKUP_DIR/volumes.tar.gz" -C /mnt/mvs-data/docker-volumes/

# Restart all services
docker-compose -f docker-compose.staging.yml up -d
```

## Getting Help

### Log Analysis
1. **Check service logs**: `docker-compose logs service_name`
2. **Check system logs**: `journalctl -u service_name`
3. **Check application logs**: `/opt/mvs-vr/mvs-vr-v2/logs/`

### Support Resources
- **DigitalOcean Community**: https://www.digitalocean.com/community
- **Docker Documentation**: https://docs.docker.com
- **Nginx Documentation**: https://nginx.org/en/docs/
- **Let's Encrypt Community**: https://community.letsencrypt.org

### Contact Information
- **DigitalOcean Support**: Available 24/7 via ticket system
- **Emergency Escalation**: Use DigitalOcean phone support for critical issues
