# Visual Editors User Guide

## Introduction

The Visual Editors are a powerful set of tools within the MVS-VR Vendor Portal that allow you to create and customize your virtual showrooms. This guide will walk you through each editor and explain how to use them effectively.

## Getting Started

To access the Visual Editors:

1. Log in to the Vendor Portal
2. Navigate to the "Visual Editors" tab in the main navigation
3. You'll see five different editors available via tabs at the top of the interface:
   - Showroom Layout Editor
   - Product Configurator
   - Materials Editor
   - Lighting Editor
   - Animation Editor

## Common Interface Elements

All Visual Editors share these common interface elements:

- **Auto-save toggle**: Enable or disable automatic saving of your changes
- **Last saved indicator**: Shows when your changes were last saved
- **Selection dropdown**: Choose which item to edit (showroom, product, material, or animation)
- **New item button**: Create a new item to edit

## Showroom Layout Editor

The Showroom Layout Editor allows you to design the physical layout of your virtual showroom.

### Key Features

- **Drag-and-drop interface**: Easily position products and furniture
- **Grid and snap functionality**: Align items precisely
- **Layout templates**: Start with pre-designed layouts
- **3D preview**: See your changes in real-time

### How to Use

1. Select an existing showroom from the dropdown or click "New Showroom"
2. Use the toolbar to add products, walls, floors, and other elements
3. Drag items to position them in the layout
4. Use the properties panel to adjust item settings
5. Click "Save" to save your changes

## Product Configurator

The Product Configurator allows you to create customizable products with different options and configurations.

### Key Features

- **Option management**: Create and organize product options
- **Real-time preview**: See configuration changes instantly
- **Pricing calculation**: Automatically calculate prices based on options
- **Configuration rules**: Set up dependencies between options

### How to Use

1. Select an existing product from the dropdown or click "New Configuration"
2. Add option groups (e.g., "Color", "Size", "Material")
3. Add options to each group with prices and properties
4. Set up any dependencies between options
5. Preview the configuration to ensure it works correctly
6. Click "Save" to save your changes

## Materials Editor

The Materials Editor allows you to create and edit materials for your products and showroom elements.

### Key Features

- **Material property editing**: Adjust color, reflectivity, roughness, etc.
- **Texture upload and mapping**: Add textures to materials
- **Material preview**: See changes in real-time
- **Material library**: Browse and use existing materials

### How to Use

1. Select an existing material from the dropdown or click "New Material"
2. Adjust the material properties using the sliders and color pickers
3. Upload textures for different maps (diffuse, normal, roughness, etc.)
4. Adjust texture mapping settings if needed
5. Preview the material on a sample object
6. Click "Save" to save your changes

## Lighting Editor

The Lighting Editor allows you to create and customize lighting for your showroom.

### Key Features

- **Light type management**: Add different types of lights (point, spot, directional, ambient)
- **Light property editing**: Adjust color, intensity, range, etc.
- **Lighting presets**: Apply pre-configured lighting setups
- **Environment lighting**: Set up global illumination

### How to Use

1. Select an existing showroom from the dropdown
2. Add lights using the light type buttons
3. Position lights in the showroom
4. Adjust light properties using the properties panel
5. Preview the lighting effect in the 3D view
6. Click "Save" to save your changes

## Animation Editor

The Animation Editor allows you to create animations for products and other elements in your showroom.

### Key Features

- **Timeline interface**: Visualize and edit animations over time
- **Keyframe management**: Create and edit animation keyframes
- **Animation preview**: Play back animations in real-time
- **Animation presets**: Apply pre-configured animations

### How to Use

1. Select an existing animation from the dropdown or click "New Animation"
2. Select the object to animate
3. Use the timeline to add keyframes at different points in time
4. Adjust object properties (position, rotation, scale, etc.) at each keyframe
5. Preview the animation using the playback controls
6. Click "Save" to save your changes

## Tips and Best Practices

### Performance Optimization

- **Limit complex animations**: Too many animations can impact performance
- **Optimize materials**: Complex materials can slow down rendering
- **Be mindful of lighting**: Too many lights can reduce performance
- **Test on target devices**: Always test your showroom on the devices your customers will use

### Design Consistency

- **Use a consistent color scheme**: Maintain brand consistency
- **Standardize lighting**: Use similar lighting setups across showrooms
- **Create material libraries**: Reuse materials to ensure consistency
- **Document your configurations**: Keep track of successful setups

### Workflow Efficiency

- **Start with templates**: Use templates as starting points
- **Save frequently**: Even with auto-save, manually save important milestones
- **Name items descriptively**: Use clear naming conventions
- **Group related items**: Organize your assets logically

## Troubleshooting

### Common Issues

- **Changes not saving**: Ensure auto-save is enabled or manually save
- **Slow performance**: Reduce complexity of materials, lighting, or animations
- **Items not appearing**: Check visibility settings and positioning
- **Preview not updating**: Try refreshing the preview or switching tabs

### Getting Help

If you encounter issues not covered in this guide:

1. Check the FAQ section in the Help Center
2. Use the "Help" button in the editor for context-specific guidance
3. Contact support through the Vendor Portal

## Video Tutorials

For visual demonstrations of the Visual Editors, please visit our [Video Tutorial Library](https://mvs-vr.com/tutorials) where you'll find detailed walkthroughs of each editor.

## Keyboard Shortcuts

| Action | Shortcut |
|--------|----------|
| Save | Ctrl+S (Windows) / Cmd+S (Mac) |
| Undo | Ctrl+Z (Windows) / Cmd+Z (Mac) |
| Redo | Ctrl+Y (Windows) / Cmd+Shift+Z (Mac) |
| Delete selected item | Delete |
| Duplicate selected item | Ctrl+D (Windows) / Cmd+D (Mac) |
| Toggle grid snap | G |
| Toggle preview mode | P |
| Play/pause animation | Space |

## Additional Resources

- [MVS-VR Knowledge Base](https://mvs-vr.com/kb)
- [Community Forums](https://community.mvs-vr.com)
- [API Documentation](https://api.mvs-vr.com/docs)
- [Sample Showrooms](https://mvs-vr.com/samples)
