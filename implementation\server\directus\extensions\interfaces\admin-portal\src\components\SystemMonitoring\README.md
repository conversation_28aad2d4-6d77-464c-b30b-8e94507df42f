# System Monitoring Components

This directory contains the components for the System Monitoring feature of the Admin Portal. The System Monitoring feature provides administrators with real-time insights into the health and performance of the MVS-VR platform.

## Components

### SystemMonitoringDashboard

The main dashboard component that integrates all the monitoring components. It provides a comprehensive view of the system's health and performance.

**Features:**

- Auto-refresh with configurable intervals
- Tabbed interface for different monitoring aspects
- Centralized data fetching and management

### SystemStatusOverview

Provides a high-level overview of the system's status, including CPU usage, memory usage, uptime, and active alerts.

**Features:**

- Real-time status indicators
- Visual representation of system health
- Quick access to critical metrics

### SystemMetrics

Displays detailed system metrics, including CPU, memory, disk, and network usage.

**Features:**

- Detailed breakdown of system resources
- Visual indicators for resource usage
- Historical trends for resource usage

### ServiceStatus

Shows the status of various services in the system, including API Gateway, Authentication Service, Database Service, and Storage Service.

**Features:**

- Real-time service status indicators
- Service details and descriptions
- Historical service status

### AlertSummary

Provides a summary of active alerts in the system, categorized by severity.

**Features:**

- Alert counts by severity
- Recent alerts list
- Alert details and acknowledgment

### ApiMetrics

Displays metrics related to API usage, performance, and rate limiting.

**Features:**

- API usage statistics
- Performance trends
- Rate limiting visualization
- Endpoint performance breakdown

### DatabaseMetrics

Shows metrics related to database performance, health, and table statistics.

**Features:**

- Database performance metrics
- Health indicators
- Table size and growth statistics
- Query optimization suggestions

### LogViewer

Provides a centralized view of system logs with filtering and search capabilities.

**Features:**

- Log filtering by level, service, and time
- Full-text search
- Log statistics and trends
- Error tracking

### UserActivity

Displays user activity metrics, including active sessions, authentication events, and user statistics.

**Features:**

- Active session tracking
- Authentication event monitoring
- User activity statistics
- Session details

## API Integration

These components integrate with the following API endpoints:

- `/api/monitoring/system-health` - System health metrics
- `/api/monitoring/api-metrics/usage` - API usage metrics
- `/api/monitoring/api-metrics/performance` - API performance metrics
- `/api/monitoring/api-metrics/rate-limiting` - Rate limiting metrics
- `/api/monitoring/database-metrics/performance` - Database performance metrics
- `/api/monitoring/database-metrics/health` - Database health metrics
- `/api/monitoring/database-metrics/tables` - Database table metrics
- `/api/monitoring/logs` - System logs
- `/api/monitoring/logs/statistics` - Log statistics
- `/api/monitoring/user-activity/sessions` - Active sessions
- `/api/monitoring/user-activity/auth-events` - Authentication events
- `/api/monitoring/user-activity/statistics` - User activity statistics

## Usage

To use these components, import them from the `SystemMonitoring` directory:

```javascript
import {
  SystemMonitoringDashboard,
  SystemStatusOverview,
  SystemMetrics,
  ServiceStatus,
  AlertSummary,
  ApiMetrics,
  DatabaseMetrics,
  LogViewer,
  UserActivity,
} from './components/SystemMonitoring';
```

The main entry point is the `SystemMonitoringDashboard` component, which can be used as follows:

```javascript
<SystemMonitoringDashboard />
```

Individual components can also be used independently:

```javascript
<SystemStatusOverview :systemHealth="systemHealth" :loading="loading" @refresh="fetchSystemHealth" />
```

## Dependencies

- Vue.js 2.x
- Vuetify 2.x
- Chart.js 3.x

## Future Enhancements

1. **Real-time Updates**

   - Implement WebSocket integration for real-time updates
   - Add push notifications for critical alerts

2. **Advanced Visualization**

   - Add more chart types for better data visualization
   - Implement interactive dashboards with drill-down capabilities

3. **Predictive Analytics**

   - Implement anomaly detection for system metrics
   - Add predictive maintenance alerts

4. **Custom Dashboards**
   - Allow administrators to create custom dashboards
   - Add dashboard sharing and export capabilities
