/**
 * Product Detail API
 *
 * This module provides API endpoints for product detail operations.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase, executeSupabaseQuery } from '../../shared/utils/supabase-client';
import { validateRequest } from '../middleware/validation';
import { authenticateRequest } from '../middleware/auth';
import {
  errorMiddleware,
  notFoundError,
  badRequestError,
  unauthorizedError,
} from '../middleware/error';
import { Logger } from '../../services/integration/logger';

// Create logger
const logger = new Logger();

/**
 * Get product handler
 *
 * @param req Request
 * @param res Response
 */
async function getProduct(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      const { id } = req.query;

      // Get product
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('*')
        .eq('id', id)
        .single();

      if (productError) {
        logger.error('Error getting product', { error: productError });
        res.status(404).json({
          success: false,
          error: {
            code: 'PRODUCT_NOT_FOUND',
            message: 'Product not found',
          },
        });
        return;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: productData,
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Update product handler
 *
 * @param req Request
 * @param res Response
 */
async function updateProduct(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('updateProduct')(req, res, async () => {
        const { id } = req.query;
        const { name, description, metadata, status } = req.body;

        // Get product to check authorization
        const { data: productData, error: productError } = await supabase
          .from('products')
          .select('vendor_id')
          .eq('id', id)
          .single();

        if (productError) {
          logger.error('Error getting product for authorization check', { error: productError });
          res.status(404).json({
            success: false,
            error: {
              code: 'PRODUCT_NOT_FOUND',
              message: 'Product not found',
            },
          });
          return;
        }

        // Check if user is authorized to update this product
        const { data: vendorData, error: vendorError } = await supabase
          .from('vendors')
          .select('user_id')
          .eq('id', productData.vendor_id)
          .single();

        if (vendorError) {
          logger.error('Error getting vendor for authorization check', { error: vendorError });
          res.status(500).json({
            success: false,
            error: {
              code: 'VENDOR_NOT_FOUND',
              message: 'Vendor not found',
            },
          });
          return;
        }

        if (req.user?.id !== vendorData.user_id && req.user?.role !== 'admin') {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to update this product',
            },
          });
          return;
        }

        // Update product
        const { data: updatedProductData, error: updateError } = await supabase
          .from('products')
          .update({
            ...(name && { name }),
            ...(description !== undefined && { description }),
            ...(metadata && { metadata }),
            ...(status && { status }),
          })
          .eq('id', id)
          .select()
          .single();

        if (updateError) {
          logger.error('Error updating product', { error: updateError });
          res.status(500).json({
            success: false,
            error: {
              code: 'UPDATE_ERROR',
              message: 'Failed to update product',
            },
          });
          return;
        }

        // Return success response
        res.status(200).json({
          success: true,
          data: updatedProductData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Delete product handler
 *
 * @param req Request
 * @param res Response
 */
async function deleteProduct(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      const { id } = req.query;

      // Get product to check authorization
      const { data: productData, error: productError } = await supabase
        .from('products')
        .select('vendor_id')
        .eq('id', id)
        .single();

      if (productError) {
        logger.error('Error getting product for authorization check', { error: productError });
        res.status(404).json({
          success: false,
          error: {
            code: 'PRODUCT_NOT_FOUND',
            message: 'Product not found',
          },
        });
        return;
      }

      // Check if user is authorized to delete this product
      const { data: vendorData, error: vendorError } = await supabase
        .from('vendors')
        .select('user_id')
        .eq('id', productData.vendor_id)
        .single();

      if (vendorError) {
        logger.error('Error getting vendor for authorization check', { error: vendorError });
        res.status(500).json({
          success: false,
          error: {
            code: 'VENDOR_NOT_FOUND',
            message: 'Vendor not found',
          },
        });
        return;
      }

      if (req.user?.id !== vendorData.user_id && req.user?.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'You are not authorized to delete this product',
          },
        });
        return;
      }

      // Delete product
      const { error: deleteError } = await supabase.from('products').delete().eq('id', id);

      if (deleteError) {
        logger.error('Error deleting product', { error: deleteError });
        res.status(500).json({
          success: false,
          error: {
            code: 'DELETE_ERROR',
            message: 'Failed to delete product',
          },
        });
        return;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: {
          id,
          deleted: true,
        },
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Handler for product detail API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'GET':
      await getProduct(req, res);
      break;
    case 'PUT':
      await updateProduct(req, res);
      break;
    case 'DELETE':
      await deleteProduct(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
