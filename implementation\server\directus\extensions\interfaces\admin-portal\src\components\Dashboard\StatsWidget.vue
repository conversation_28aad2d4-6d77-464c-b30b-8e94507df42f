<template>
  <div class="stats-widget">
    <v-row>
      <v-col v-for="(stat, index) in stats" :key="index" cols="12" sm="6">
        <div class="stat-item">
          <div class="stat-icon">
            <v-icon :color="stat.color" size="36">{{ stat.icon }}</v-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">
              {{ stat.value }}
              <span v-if="stat.trend" class="stat-trend" :class="getTrendClass(stat.trend)">
                <v-icon small>{{ getTrendIcon(stat.trend) }}</v-icon>
                {{ stat.trendValue }}%
              </span>
            </div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </v-col>
    </v-row>
    
    <v-divider v-if="settings.showChart && chartData" class="my-3"></v-divider>
    
    <div v-if="settings.showChart && chartData" class="stats-chart">
      <v-sparkline
        :value="chartData.values"
        :labels="chartData.labels"
        :gradient="chartData.gradient"
        :smooth="settings.smoothChart"
        :padding="8"
        :line-width="2"
        auto-draw
      ></v-sparkline>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StatsWidget',
  
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    settings: {
      type: Object,
      default: () => ({
        showTrend: true,
        showChart: true,
        smoothChart: true,
        dataPoints: 7
      })
    }
  },
  
  computed: {
    stats() {
      if (!this.data.stats) {
        return this.getDefaultStats();
      }
      
      return this.data.stats;
    },
    
    chartData() {
      if (!this.data.chartData) {
        return this.getDefaultChartData();
      }
      
      return this.data.chartData;
    }
  },
  
  methods: {
    getTrendClass(trend) {
      if (trend > 0) return 'trend-up';
      if (trend < 0) return 'trend-down';
      return 'trend-neutral';
    },
    
    getTrendIcon(trend) {
      if (trend > 0) return 'mdi-arrow-up';
      if (trend < 0) return 'mdi-arrow-down';
      return 'mdi-minus';
    },
    
    getDefaultStats() {
      return [
        {
          label: 'Total',
          value: '1,254',
          icon: 'mdi-account-group',
          color: 'primary',
          trend: 12,
          trendValue: 12
        },
        {
          label: 'Active',
          value: '1,180',
          icon: 'mdi-account-check',
          color: 'success',
          trend: 8,
          trendValue: 8
        },
        {
          label: 'New (30d)',
          value: '245',
          icon: 'mdi-account-plus',
          color: 'info',
          trend: 24,
          trendValue: 24
        },
        {
          label: 'Inactive',
          value: '74',
          icon: 'mdi-account-off',
          color: 'error',
          trend: -5,
          trendValue: 5
        }
      ];
    },
    
    getDefaultChartData() {
      return {
        values: [28, 40, 36, 52, 38, 60, 55],
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        gradient: ['#00c6ff', '#0072ff']
      };
    }
  }
};
</script>

<style scoped>
.stats-widget {
  padding: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.02);
  transition: background-color 0.2s;
}

.stat-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.stat-icon {
  margin-right: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.stat-trend {
  font-size: 0.875rem;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
}

.trend-up {
  color: #4caf50;
}

.trend-down {
  color: #f44336;
}

.trend-neutral {
  color: #9e9e9e;
}

.stat-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.stats-chart {
  margin-top: 16px;
  height: 100px;
}
</style>
