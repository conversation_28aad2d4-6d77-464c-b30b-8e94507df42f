#### **`runtime_deployment_logic.md`**

# Runtime Deployment Logic

## Components
- **SceneLoader**: orchestrates space loading.
- **AssetManager**: fetches, caches, and mounts assets.
- **BlueprintInjector**: wires interactivity.
- **RoleGatekeeper**: applies user/vendor permissions.

## Workflow
1. **Bootstrap** via plugin.  
2. **Verify** manifest hashes.  
3. **Load** scene graph.  
4. **Inject** behaviors and AI prompts.  
5. **Activate** environment with applied settings.

## Caching & Fallback
- Cached bundles used if offline.  
- Safe-mode defaults for critical failures.  
- Error logs sent on next successful connection.

