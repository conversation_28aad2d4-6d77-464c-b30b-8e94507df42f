{"name": "directus-extension-role-sync", "version": "1.0.0", "description": "Synchronize user roles between Supabase and Directus with optimized performance", "main": "index.js", "keywords": ["directus", "directus-extension", "directus-custom-hook", "supabase", "role-sync"], "directus:extension": {"type": "hook", "path": "index.js", "source": "src/index.js", "host": "^10.0.0"}, "scripts": {"build": "directus-extension build"}, "dependencies": {"@supabase/supabase-js": "^2.38.0"}}