# Review Mode (QA Preview System)

## Overview

This document outlines the logic and structure of the Review Mode feature, enabling vendors and system admins to preview a space exactly as users would, without initiating a public session.

---

## Objectives

* Allow QA and visual inspection before public deployment
* Enable sandbox testing of interactivity, layout, and logic
* Support admins in verifying brand compliance, performance, and UI behavior

---

## Key Properties

| Attribute                   | Value                                             |
| --------------------------- | ------------------------------------------------- |
| **Always Active**           | Yes – Room is always in standby mode              |
| **Triggers Public Session** | No – Does not log analytics or start user session |
| **Interactivity**           | Fully enabled                                     |
| **Visibility**              | Only vendor, admin, or testers can access         |

---

## Access Logic

* Only visible in the admin/vendor dashboard ("Preview Room")
* Requires approval state: `QA_pending`, `QA_in_progress`, or `Ready_for_review`
* Can be locked by admin during QA pass

---

## UI Integration

* Preview button on layout editor
* Optional badge overlay in viewport (`QA MODE`)
* QA checklist interface on the right panel

---

## Editable in Review Mode?

* Layout: ✅ Yes
* Material: ✅ Yes
* Assistant Prompt: ✅ Yes
* Flow Routing: ❌ No (read-only)
* Live Support Triggers: ❌ Disabled

---

## Typical Use Cases

* Vendor reviews material setup and animations
* Admin checks interactivity and flow
* Marketing team previews space before campaign launch

---

## Exit Options

* "Exit QA" returns user to dashboard
* "Publish Space" triggers flow routing and public sync
* "Send for Admin Review" notifies QA team

---

## Backend Notes

* Review Mode logs changes, but no public session IDs are generated
* Uses separate environment variable: `isQA = true`
* Reuses user state from vendor role, with restricted API writes

---

## Future Enhancements

* Add screenshot mode / export QA notes
* Shared link for internal previews
* Automated QA scoring plugin (flag FPS, missing tags, etc.)
