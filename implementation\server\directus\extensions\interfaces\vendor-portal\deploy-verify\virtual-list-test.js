/**
 * Simple test script for VirtualListRenderer
 * This script tests the core functionality without requiring a full build
 */

// Import the VirtualListRenderer class
const { VirtualListRenderer } = require('./src/utils/PerformanceOptimizer');
const { logger } = require('../shared/utils/logger');

// Mock data
const mockItems = Array.from({ length: 100 }, (_, i) => ({
  id: `item-${i}`,
  name: `Item ${i}`,
  value: i
}));

// Mock load more function
const mockLoadMoreItems = async (page, pageSize) => {
  logger.info(`Loading page ${page} with pageSize ${pageSize}`);
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return Array.from({ length: pageSize }, (_, i) => ({
    id: `item-${(page - 1) * pageSize + i + 100}`,
    name: `Item ${(page - 1) * pageSize + i + 100}`,
    value: (page - 1) * pageSize + i + 100
  }));
};

// Create a new VirtualListRenderer
const renderer = new VirtualListRenderer(
  mockItems,
  40, // Item height
  400, // Container height
  5, // Buffer
  {
    lazyLoad: true,
    loadMoreItems: mockLoadMoreItems,
    loadThreshold: 0.8,
    pageSize: 20,
    totalItems: 200,
    prefetch: true,
    prefetchThreshold: 0.5
  }
);

logger.info('=== Testing VirtualListRenderer ===');

// Test 1: Initial state
logger.info('\nTest 1: Initial state');
logger.info(`Items count: ${renderer.items.length}`);
logger.info(`Current page: ${renderer.currentPage}`);
logger.info(`Has more items: ${renderer.hasMoreItems}`);
logger.info(`Is prefetching: ${renderer.isPrefetching}`);

// Test 2: Get visible items
logger.info('\nTest 2: Get visible items');
const result1 = renderer.getVisibleItems();
logger.info(`Visible items count: ${result1.visibleItems.length}`);
logger.info(`Render time: ${result1.metrics.renderTime}ms`);

// Test 3: Scroll and trigger lazy loading
logger.info('\nTest 3: Scroll and trigger lazy loading');
// Scroll to 80% of the current items
const scrollPosition = renderer.items.length * renderer.itemHeight * 0.8;
logger.info(`Scrolling to position: ${scrollPosition}`);
renderer.updateScroll(scrollPosition);

// Wait for lazy loading to complete
setTimeout(async () => {
  logger.info(`Items after scrolling: ${renderer.items.length}`);
  logger.info(`Current page after scrolling: ${renderer.currentPage}`);
  
  // Test 4: Prefetching
  logger.info('\nTest 4: Prefetching');
  logger.info(`Is prefetching: ${renderer.isPrefetching}`);
  logger.info(`Prefetched data available: ${!!renderer.prefetchedData}`);
  
  // Manually trigger prefetching
  logger.info('Manually triggering prefetch...');
  renderer.prefetchNextPage();
  
  // Wait for prefetching to complete
  await new Promise(resolve => setTimeout(resolve, 200));
  
  logger.info(`Is prefetching after manual trigger: ${renderer.isPrefetching}`);
  logger.info(`Prefetched data available after manual trigger: ${!!renderer.prefetchedData}`);
  
  // Test 5: Get metrics
  logger.info('\nTest 5: Get metrics');
  logger.info('Metrics:', renderer.getMetrics(););
  
  // Test 6: Cleanup
  logger.info('\nTest 6: Cleanup');
  renderer.dispose();
  logger.info('Renderer disposed');
  
  logger.info('\n=== All tests completed ===');
}, 300);
