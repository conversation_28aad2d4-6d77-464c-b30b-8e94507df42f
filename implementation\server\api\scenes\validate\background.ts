import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { Redis } from 'ioredis';
import {
  JobType,
  SceneValidatorWorkerService,
} from '../../../services/scene/scene-validator-worker';

// Initialize Redis client
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// Initialize scene validator worker service
const sceneValidatorWorker = new SceneValidatorWorkerService(supabase, redis);

/**
 * Start background validation
 *
 * @param req - Request
 * @param res - Response
 */
export const startBackgroundValidation = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { job_type, params, options } = req.body;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!job_type || !Object.values(JobType).includes(job_type)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_JOB_TYPE',
          message: 'Valid job type is required',
        },
      });
      return;
    }

    // Add job to queue
    const jobId = await sceneValidatorWorker.addJob(job_type, {
      sceneId: scene_id,
      params,
      options,
    });

    res.status(200).json({
      success: true,
      data: {
        job_id: jobId,
        status: 'queued',
      },
    });
  } catch (error) {
    logger.error('Error starting background validation', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get job status
 *
 * @param req - Request
 * @param res - Response
 */
export const getJobStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { job_id } = req.params;

    // Validate parameters
    if (!job_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_JOB_ID',
          message: 'Job ID is required',
        },
      });
      return;
    }

    // Get job status
    const status = await sceneValidatorWorker.getJobStatus(job_id);

    res.status(200).json({
      success: true,
      data: status,
    });
  } catch (error) {
    logger.error('Error getting job status', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Start validate all job
 *
 * @param req - Request
 * @param res - Response
 */
export const validateAll = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { options } = req.body;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Add job to queue
    const jobId = await sceneValidatorWorker.addJob(JobType.VALIDATE_ALL, {
      sceneId: scene_id,
      options,
    });

    res.status(200).json({
      success: true,
      data: {
        job_id: jobId,
        status: 'queued',
      },
    });
  } catch (error) {
    logger.error('Error starting validate all job', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on method and path
    if (req.method === 'POST' && req.path.endsWith('/validate-all')) {
      await validateAll(req, res);
    } else if (req.method === 'POST') {
      await startBackgroundValidation(req, res);
    } else if (req.method === 'GET' && req.params.job_id) {
      await getJobStatus(req, res);
    } else {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
    }
  } catch (error) {
    logger.error('Error in background validation handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
}
