import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { SceneValidatorService } from '../../../services/scene/scene-validator';
import { validateRequest } from '../../middleware/validation';

// Initialize scene validator service
const sceneValidator = new SceneValidatorService(supabase);

/**
 * Validate scene flow
 *
 * @param req - Request
 * @param res - Response
 */
export const validateSceneFlow = async (req: Request, res: Response): Promise<void> => {
  try {
    const { flow } = req.body;

    // Validate parameters
    if (!flow) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_FLOW',
          message: 'Scene flow is required',
        },
      });
      return;
    }

    // Validate scene flow
    const result = await sceneValidator.validateSceneFlow(flow);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating scene flow', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
      return;
    }

    await validateSceneFlow(req, res);
  } catch (error) {
    logger.error('Error in scene flow validation handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
}
