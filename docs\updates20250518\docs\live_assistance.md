# Live Support / Avatar Assistance System

## Overview

This document outlines the logic and implementation plan for integrating real-time avatar-based assistance and/or live support within the Kanousei VR platform. This support system provides users with contextual help, onboarding, and sales guidance.

---

## Objectives

* Improve user guidance in complex or large environments.
* Enable vendors to offer concierge-level service.
* Support both automated and human-in-the-loop interactions.

---

## Modes of Operation

### 1. **AI-Powered Avatar Guide (Default)**

* Appears as a floating or humanoid avatar.
* Responds to user voice or gestures.
* Supports commands like:

  * "Where am I?"
  * "Show me a preview room"
  * "Add this to my favorites"
* Can be positioned globally or per room.

### 2. **Live Vendor Support (Optional Upgrade)**

* Connects to a live vendor rep (video, voice, or text chat overlay).
* Can be triggered by:

  * Help button
  * Inactivity or confusion signals
  * High-value interactions (e.g. previewing premium items)
* Optionally includes co-browsing, product walkthroughs.

---

## Avatar AI Configuration

* Each vendor/store can define their avatar’s:

  * Name
  * Voice (if TTS enabled)
  * Greeting message
  * Tone of speech (casual, professional)
* Avatar logic powered by the same prompt injection system used by the AI Assistant.

---

## Sample Avatar Config

```json
{
  "name": "Lina",
  "voice": "female_en_warm",
  "triggerPhrases": ["help", "where is"],
  "appearance": "floating_bot_orb",
  "vendorId": "vendor_A_001"
}
```

---

## Integration

* Avatar loads only in designated zones.
* Icon or voice cue can summon it anytime.
* AI fallback: If live support unavailable, assistant defaults to scripted AI prompts.

---

## Privacy Notes

* For live support, users are notified when a rep is joining.
* Option to disable or mute avatar always available.
* Vendor reps cannot view unrelated sessions.

---

## UX Guidelines

* Use subtle entrance animations.
* Allow user to shrink, dismiss, or reposition avatar.
* Add minimal sound cues when avatar is triggered or responds.

---

## Admin Dashboard Features

* Monitor active support sessions.
* Log usage stats (calls made, resolved, dropped).
* Enable/disable per vendor or product tier.

---

## Performance

* Avatar is pooled prefab.
* Only one instance per room active.
* Text-based fallback available if TTS not supported on platform.


# Spatial Audio Logic (Room/Object-Based)

## Overview

This document defines how spatial audio is managed in the Kanousei VR platform. Audio is dynamically routed based on user position, object interactions, and room context to enhance immersion.

---

## Objectives

* Deliver realistic 3D audio positioning
* Reduce noise clutter in multi-sound environments
* Allow vendors to attach sound to products or rooms

---

## Audio Categories

### 1. **Ambient Audio (Room-Level)**

* Background sounds based on room environment
* Examples:

  * City ambience in Dubai main hall
  * Birds/fountains in nature room

### 2. **Product Audio (Object-Level)**

* Sounds triggered by interacting with products
* Examples:

  * Drawer opening/closing
  * Fridge hum
  * Chair creak

### 3. **Assistant/Avatar Voice**

* Follows avatar/object position
* Pans based on user head direction
* Volume scaled by proximity or user settings

---

## Audio Metadata Structure

```json
{
  "audioId": "drawer_slide_sfx",
  "type": "product",
  "attachedTo": "drawer_004",
  "position": [1.2, 0.5, -2.1],
  "spatial": true,
  "volume": 0.85,
  "loop": false
}
```

---

## Room-Level Audio Routing

Each space/room defines:

* One ambient loop (default)
* Optional event-based overlays (e.g., music change on vendor promo)
* Optional "global music off" toggle per user

### Example:

```json
{
  "roomId": "dubai_main",
  "ambient": "dubai_market_ambience",
  "overlays": ["event_jingle", "holiday_theme"],
  "volume": 0.6
}
```

---

## Audio Optimization

* Audio sources are pooled and culled based on distance.
* Max concurrent 3D audio sources per room = 10 (configurable).
* Volumes are balanced to prevent spikes.

---

## Vendor Controls

* Attach audio via drag-and-drop in layout editor
* Upload audio (must be <5MB, ogg/mp3)
* Set distance falloff + spatial toggle

---

## Admin Controls

* Override volume limits
* Assign ambient presets to rooms
* Track audio usage per scene for optimization

---

## User Controls

* Toggle ambient audio
* Adjust assistant voice volume
* Mute all sound (silent mode)

---

## Accessibility

* Closed captions available for assistant audio
* Audio descriptions embedded in AI prompt replies


# Review Mode (QA Preview System)

## Overview

This document outlines the logic and structure of the Review Mode feature, enabling vendors and system admins to preview a space exactly as users would, without initiating a public session.

---

## Objectives

* Allow QA and visual inspection before public deployment
* Enable sandbox testing of interactivity, layout, and logic
* Support admins in verifying brand compliance, performance, and UI behavior

---

## Key Properties

| Attribute                   | Value                                             |
| --------------------------- | ------------------------------------------------- |
| **Always Active**           | Yes – Room is always in standby mode              |
| **Triggers Public Session** | No – Does not log analytics or start user session |
| **Interactivity**           | Fully enabled                                     |
| **Visibility**              | Only vendor, admin, or testers can access         |

---

## Access Logic

* Only visible in the admin/vendor dashboard ("Preview Room")
* Requires approval state: `QA_pending`, `QA_in_progress`, or `Ready_for_review`
* Can be locked by admin during QA pass

---

## UI Integration

* Preview button on layout editor
* Optional badge overlay in viewport (`QA MODE`)
* QA checklist interface on the right panel

---

## Editable in Review Mode?

* Layout: ✅ Yes
* Material: ✅ Yes
* Assistant Prompt: ✅ Yes
* Flow Routing: ❌ No (read-only)
* Live Support Triggers: ❌ Disabled

---

## Typical Use Cases

* Vendor reviews material setup and animations
* Admin checks interactivity and flow
* Marketing team previews space before campaign launch

---

## Exit Options

* "Exit QA" returns user to dashboard
* "Publish Space" triggers flow routing and public sync
* "Send for Admin Review" notifies QA team

---

## Backend Notes

* Review Mode logs changes, but no public session IDs are generated
* Uses separate environment variable: `isQA = true`
* Reuses user state from vendor role, with restricted API writes

---

## Future Enhancements

* Add screenshot mode / export QA notes
* Shared link for internal previews
* Automated QA scoring plugin (flag FPS, missing tags, etc.)


# Reporting & Insights (Vendor Analytics System)

## Overview

This document defines the reporting and analytics system available to vendors within the Kanousei VR platform. It provides actionable insights into user behavior, product engagement, and space performance.

---

## Objectives

* Help vendors understand user interaction trends
* Support optimization of product layout and design
* Enable performance-based decisions for marketing and inventory

---

## Dashboard Sections

### 1. **Product Insights**

* Views per product (unique + total)
* Hover time / focus duration
* Favorites added
* Click-to-preview actions

### 2. **Space Performance**

* Entry count per room
* Average time spent per visitor
* Drop-off points (e.g. areas with no interactions)
* Navigation flow heatmap

### 3. **Engagement Funnels**

* First-touch → Interaction → Favorite → Add to Request
* Conversion ratios by category

### 4. **Device Breakdown**

* VR Headset, Desktop, WebGL, Mobile
* Geo + language detection (if enabled)

---

## Sample Data Schema

```json
{
  "vendorId": "ven_dubai_sofas_01",
  "reportDate": "2025-05-14",
  "productStats": [
    {
      "productId": "sofa_modern_004",
      "views": 134,
      "hoverTimeAvg": 4.2,
      "favorites": 12,
      "clicks": 28
    }
  ],
  "roomStats": [
    {
      "roomId": "dubai_main",
      "entries": 98,
      "avgTime": 93.2,
      "exitBeforeExplore": 22
    }
  ]
}
```

---

## Visualizations

* Bar graphs for top products
* Heatmap overlay on space layout
* Line chart for engagement trends
* Pie chart for device types

---

## Vendor Tools

* Export reports (CSV, PDF)
* Filter by date, room, product
* Compare performance between layouts or campaigns

---

## Admin Tools

* View analytics across all vendors
* Flag underperforming or low-engagement spaces
* Trigger QA audit or optimization suggestions

---

## Update Frequency

* Data updated hourly
* Realtime for live visitor count + current room occupancy

---

## Future Enhancements

* AI-powered suggestions: "Swap product A with trending B"
* Predictive layout scoring based on interaction history
* Vendor benchmarking against industry/category peers


---

### 📄 **ai\_memory\_control.md**

````markdown
# AI Memory Control Specification

## Overview
This document defines how the AI assistant embedded within the Kanousei VR ecosystem will handle memory persistence across sessions, ensuring continuity when desired and discretion when required.

---

## Objectives
- Enable the AI to retain context about user interactions across sessions (when allowed).
- Offer users and vendors full control over what is remembered and when.
- Support privacy-by-design defaults with explicit consent.

---

## Modes of Operation

### 1. **Session-Based (Default)**
- Memory is **wiped after each session**.
- Ideal for anonymous users or temporary logins.

### 2. **Persistent Memory Mode**
- AI can retain knowledge of:
  - Products viewed
  - Spaces visited
  - User preferences (e.g., color themes, brands)
  - Assistant conversations or favorite suggestions
- Enabled **only with explicit opt-in**.

---

## Control Points

### Admin/Vendor:
- Toggle persistence for specific assistants.
- Define max memory scope (per user, per room, global).
- View/edit/delete AI memory via panel.

### User:
- View assistant memory
- Clear specific items or full history
- Toggle persistent memory on/off anytime

---

## Implementation Notes

### Data Storage
- Memory is stored in a secure user-specific context bucket.
- Tied to user ID or guest session token.

### Metadata Sample:
```json
{
  "userId": "visitor_2351",
  "memory": {
    "favorites": ["glass_table_001", "sofa_modern_003"],
    "visitedRooms": ["dubai_mainhall", "vendorA_preview"],
    "lastPrompt": "show me more modern chairs"
  },
  "updatedAt": "2025-05-15T09:43:00Z"
}
````

### AI Runtime Access

* The AI has scoped read access only to what the user has approved.
* On memory-enabled sessions, this data is injected at prompt runtime.

---

## Security & Compliance

* Memory is encrypted in transit and at rest.
* Auto-expiry rules can be configured (e.g., forget after 30 days).
* GDPR-compliant structure with right to be forgotten.

---

## UX Summary

* Assistant will notify users when memory is active.
* Suggests “Would you like me to remember this?” after notable queries.
* Always offers a “forget this” option in interactions.

```


### 📄 **personalized\_feed.md**

````markdown
# Personalized Product Feed in VR

## Overview
This document outlines the structure and behavior of a personalized product recommendation system within the Kanousei VR platform. It dynamically adjusts the product feed shown to users based on their interaction history, profile, or preferences.

---

## Objectives
- Enhance user engagement through curated content.
- Increase discoverability of relevant vendor products.
- Ensure real-time performance with minimal latency.

---

## Feed Structure
The product feed is rendered in 3D as:
- A dynamic **carousel wall**, **scrollable shelf**, or **grid gallery**.
- Displayed on entry in designated spaces (e.g., Startup Lobby, Vendor Spotlight Area).

---

## Personalization Criteria

### Input Signals:
- Rooms/spaces visited
- Products interacted with (hover, inspect, favorite)
- Vendor follow/subscription
- Time spent near certain product categories

### Optional Inputs:
- Language preference
- Style tags: modern, minimalist, traditional
- AI Assistant tags based on past queries

---

## Scoring Logic
Each product receives a relevance score from 0 to 1.0:

```json
{
  "productId": "lamp_amber_301",
  "relevance": 0.92,
  "tags": ["lighting", "modern", "warm"]
}
````

Ranking is calculated using a weighted blend of recentness, frequency, and session behavior.

---

## Feed API Response Sample

```json
{
  "userId": "guest_771",
  "recommendedProducts": [
    "chair_modern_04",
    "sofa_lounge_12",
    "table_glass_round"
  ],
  "renderType": "wallGallery",
  "config": {
    "maxItems": 12,
    "layout": "centered-carousel"
  }
}
```

---

## Runtime Integration

* Feed is updated when user enters a new space or on defined intervals.
* Stored in local memory per session.
* If user disables personalization, fallback to trending + random products.

---

## Admin Tools

* System Admin can:

  * View feed logs per user/session
  * Override or blacklist product types per scene
  * Assign promotional weighting to certain vendor SKUs

---

## Vendor Insights

Vendors can:

* See how often their products are recommended
* View click-through rates and favorite saves
* Submit feed optimization tags

---

## Performance Notes

* Feed system must preload thumbnails and mesh proxies.
* Initial recommendations cached client-side for smooth entry animation.
* Server feed response should complete in <150ms for VR fluidity.

```


# Live Support / Avatar Assistance System

## Overview
This document outlines the logic and implementation plan for integrating real-time avatar-based assistance and/or live support within the Kanousei VR platform. This support system provides users with contextual help, onboarding, and sales guidance.

---

## Objectives
- Improve user guidance in complex or large environments.
- Enable vendors to offer concierge-level service.
- Support both automated and human-in-the-loop interactions.

---

## Modes of Operation

### 1. **AI-Powered Avatar Guide (Default)**
- Appears as a floating or humanoid avatar.
- Responds to user voice or gestures.
- Supports commands like:
  - "Where am I?"
  - "Show me a preview room"
  - "Add this to my favorites"
- Can be positioned globally or per room.

### 2. **Live Vendor Support (Optional Upgrade)**
- Connects to a live vendor rep (video, voice, or text chat overlay).
- Can be triggered by:
  - Help button
  - Inactivity or confusion signals
  - High-value interactions (e.g., previewing premium items)
- Optionally includes co-browsing, product walkthroughs.

---

## Avatar AI Configuration
- Each vendor/store can define their avatar’s:
  - Name
  - Voice (if TTS enabled)
  - Greeting message
  - Tone of speech (casual, professional)
- Avatar logic powered by the same prompt injection system used by the AI Assistant.

---

## Sample Avatar Config
```json
{
  "name": "Lina",
  "voice": "female_en_warm",
  "triggerPhrases": ["help", "where is"],
  "appearance": "floating_bot_orb",
  "vendorId": "vendor_A_001"
}
````

---

## Integration

* Avatar loads only in designated zones.
* Icon or voice cue can summon it anytime.
* AI fallback: If live support unavailable, assistant defaults to scripted AI prompts.

---

## Privacy Notes

* For live support, users are notified when a rep is joining.
* Option to disable or mute avatar always available.
* Vendor reps cannot view unrelated sessions.

---

## UX Guidelines

* Use subtle entrance animations.
* Allow user to shrink, dismiss, or reposition avatar.
* Add minimal sound cues when avatar is triggered or responds.

---

## Admin Dashboard Features

* Monitor active support sessions.
* Log usage stats (calls made, resolved, dropped).
* Enable/disable per vendor or product tier.

---

## Performance

* Avatar is pooled prefab.
* Only one instance per room active.
* Text-based fallback available if TTS not supported on platform.

```

Ready for **spatial_audio_logic.md**?
```
