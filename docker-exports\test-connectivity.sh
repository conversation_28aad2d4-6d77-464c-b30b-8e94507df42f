#!/bin/bash

# MVS-VR Connectivity Test Script
# Tests basic connectivity and Supabase connection

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 Testing MVS-VR Connectivity..."
echo ""

# Test 1: Port 80 accessibility
print_status "Testing port 80 accessibility..."
if curl -s -f "http://localhost/" > /dev/null 2>&1; then
    print_success "Port 80 is accessible!"
    echo "Response:"
    curl -s "http://localhost/" | head -3
else
    print_error "Port 80 is not accessible"
fi
echo ""

# Test 2: Health endpoint
print_status "Testing health endpoint..."
if curl -s -f "http://localhost/health" > /dev/null 2>&1; then
    print_success "Health endpoint is working!"
    echo "Response:"
    curl -s "http://localhost/health"
else
    print_error "Health endpoint is not working"
fi
echo ""

# Test 3: Supabase connectivity
print_status "Testing Supabase connectivity..."
SUPABASE_URL="https://hiyqiqbgiueyyvqoqhht.supabase.co"

if curl -s -f "$SUPABASE_URL/rest/v1/" > /dev/null 2>&1; then
    print_success "Supabase REST API is accessible!"
else
    print_error "Cannot connect to Supabase REST API"
fi

if curl -s -f "$SUPABASE_URL/auth/v1/settings" > /dev/null 2>&1; then
    print_success "Supabase Auth API is accessible!"
else
    print_error "Cannot connect to Supabase Auth API"
fi

if curl -s -f "$SUPABASE_URL/storage/v1/bucket" > /dev/null 2>&1; then
    print_success "Supabase Storage API is accessible!"
else
    print_warning "Supabase Storage API may require authentication"
fi
echo ""

# Test 4: Individual service health checks
print_status "Testing individual services..."

services=(
    "auth-service:3005"
    "api-gateway:4000"
    "asset-service:5000"
    "scene-service:6000"
    "blueprint-service:3003"
    "llm-service:7000"
    "analytics-service:8000"
    "monitoring-service:9090"
    "directus:8055"
)

for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    if curl -s -f "http://localhost:$port/health" > /dev/null 2>&1; then
        print_success "$name (port $port) is healthy"
    else
        print_warning "$name (port $port) health check failed"
    fi
done
echo ""

# Test 5: DNS resolution (if running on server)
print_status "Testing DNS resolution..."
if nslookup mvs.kanousai.com > /dev/null 2>&1; then
    print_success "mvs.kanousai.com resolves correctly"
    echo "IP Address:"
    nslookup mvs.kanousai.com | grep "Address:" | tail -1
else
    print_warning "DNS resolution test skipped (nslookup not available or domain not configured)"
fi
echo ""

# Test 6: Docker container status
print_status "Checking Docker container status..."
if command -v docker-compose > /dev/null 2>&1; then
    echo "Container Status:"
    docker-compose -f docker-compose.exported.yml ps
else
    print_warning "docker-compose not available"
fi
echo ""

# Summary
print_status "🎯 Connectivity Test Summary:"
echo "  ✓ Test basic port 80 access"
echo "  ✓ Test health endpoints"
echo "  ✓ Test Supabase connectivity"
echo "  ✓ Test individual service health"
echo "  ✓ Test DNS resolution"
echo "  ✓ Check container status"
echo ""
print_success "Connectivity testing completed!"
echo ""
echo "Next steps:"
echo "1. Ensure mvs.kanousai.com points to **************"
echo "2. Configure SSL certificates if needed"
echo "3. Test from external network"
echo "4. Monitor service logs for any issues"
