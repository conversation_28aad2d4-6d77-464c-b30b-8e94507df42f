/**
 * <PERSON><PERSON><PERSON> to run tests for the Guided Setup Wizard
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the directory of this script
const scriptDir = __dirname;

// Define test directories
const testDirs = [
  path.join(scriptDir, 'tests'),
  path.join(scriptDir, 'steps', 'tests'),
  path.join(scriptDir, '..', '..', 'services', 'tests')
];

// Define test files
const testFiles = [
  path.join(scriptDir, 'tests', 'GuidedSetupWizard.test.js'),
  path.join(scriptDir, 'tests', 'WizardContainer.test.js'),
  path.join(scriptDir, 'tests', 'WizardStep.test.js'),
  path.join(scriptDir, 'steps', 'tests', 'CompanyProfileStep.test.js'),
  path.join(scriptDir, '..', '..', 'services', 'tests', 'GuidedSetupService.test.js'),
  path.join(scriptDir, '..', '..', 'tests', 'interface-wizard-integration.test.js')
];

// Create test directories if they don't exist
testDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
});

// Check if test files exist
const missingFiles = testFiles.filter(file => !fs.existsSync(file));
if (missingFiles.length > 0) {
  console.error('The following test files are missing:');
  missingFiles.forEach(file => console.error(`- ${file}`));
  process.exit(1);
}

// Run tests
console.log('Running tests for Guided Setup Wizard...');

try {
  // Run Jest with coverage
  const result = execSync('npx jest --coverage', {
    cwd: scriptDir,
    stdio: 'inherit'
  });
  
  console.log('All tests passed!');
} catch (error) {
  console.error('Tests failed:', error.message);
  process.exit(1);
}
