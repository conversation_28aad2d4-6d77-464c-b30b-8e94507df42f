<template>
  <div class="documentation-reference">
    <div class="documentation-header">
      <h3 class="documentation-title">{{ title }}</h3>
      <button 
        class="documentation-toggle"
        @click="toggleExpanded"
        :aria-expanded="isExpanded.toString()"
        :aria-controls="`doc-content-${_uid}`"
      >
        {{ isExpanded ? 'Hide Documentation' : 'Show Documentation' }}
      </button>
    </div>
    
    <transition name="slide">
      <div 
        v-if="isExpanded"
        :id="`doc-content-${_uid}`"
        class="documentation-content"
      >
        <div v-if="isLoading" class="documentation-loading">
          <div class="loading-spinner"></div>
          <p>Loading documentation...</p>
        </div>
        
        <div v-else-if="error" class="documentation-error">
          <p>{{ error }}</p>
          <button @click="loadDocumentation" class="retry-button">Retry</button>
        </div>
        
        <div v-else class="documentation-body">
          <div v-if="documentationContent" v-html="documentationContent"></div>
          <div v-else class="documentation-placeholder">
            <p>No documentation available for this section.</p>
          </div>
          
          <div v-if="relatedTopics.length > 0" class="related-topics">
            <h4>Related Topics</h4>
            <ul class="topic-list">
              <li v-for="(topic, index) in relatedTopics" :key="index" class="topic-item">
                <a 
                  :href="topic.url" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  class="topic-link"
                >
                  {{ topic.title }}
                </a>
                <span class="topic-description">{{ topic.description }}</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div class="documentation-footer">
          <div class="documentation-actions">
            <button 
              v-if="documentationContent" 
              @click="printDocumentation" 
              class="print-button"
              aria-label="Print documentation"
            >
              <i class="material-icons">print</i>
              Print
            </button>
            
            <button 
              v-if="documentationUrl" 
              @click="openExternalDocs" 
              class="external-button"
              aria-label="Open in documentation site"
            >
              <i class="material-icons">open_in_new</i>
              View in Docs
            </button>
          </div>
          
          <div class="documentation-feedback">
            <p>Was this documentation helpful?</p>
            <div class="feedback-buttons">
              <button 
                @click="sendFeedback(true)" 
                class="feedback-button"
                :class="{ 'active': feedback === true }"
                aria-label="Yes, this was helpful"
              >
                <i class="material-icons">thumb_up</i>
              </button>
              <button 
                @click="sendFeedback(false)" 
                class="feedback-button"
                :class="{ 'active': feedback === false }"
                aria-label="No, this was not helpful"
              >
                <i class="material-icons">thumb_down</i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'DocumentationReference',
  
  props: {
    title: {
      type: String,
      default: 'Documentation'
    },
    topic: {
      type: String,
      required: true
    },
    context: {
      type: Object,
      default: () => ({})
    },
    expanded: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      isExpanded: this.expanded,
      isLoading: false,
      documentationContent: null,
      documentationUrl: null,
      relatedTopics: [],
      error: null,
      feedback: null
    };
  },
  
  watch: {
    expanded(newValue) {
      this.isExpanded = newValue;
    },
    
    isExpanded(newValue) {
      this.$emit('update:expanded', newValue);
      
      if (newValue && !this.documentationContent && !this.isLoading) {
        this.loadDocumentation();
      }
    },
    
    topic() {
      // Reset when topic changes
      this.documentationContent = null;
      this.documentationUrl = null;
      this.relatedTopics = [];
      this.error = null;
      this.feedback = null;
      
      if (this.isExpanded) {
        this.loadDocumentation();
      }
    }
  },
  
  mounted() {
    if (this.isExpanded) {
      this.loadDocumentation();
    }
  },
  
  methods: {
    toggleExpanded() {
      this.isExpanded = !this.isExpanded;
    },
    
    async loadDocumentation() {
      this.isLoading = true;
      this.error = null;
      
      try {
        // Simulate API call to fetch documentation
        // In a real implementation, this would call an actual API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock response data
        const mockData = {
          content: `<h4>Documentation for ${this.topic}</h4>
            <p>This is sample documentation content for the ${this.topic} topic.</p>
            <p>It includes information relevant to the current context and step in the wizard.</p>
            <ul>
              <li>Important point 1</li>
              <li>Important point 2</li>
              <li>Important point 3</li>
            </ul>`,
          url: `https://docs.example.com/wizard/${this.topic.toLowerCase().replace(/\s+/g, '-')}`,
          relatedTopics: [
            {
              title: 'Getting Started Guide',
              description: 'Complete overview of the platform',
              url: 'https://docs.example.com/getting-started'
            },
            {
              title: `Advanced ${this.topic}`,
              description: 'Detailed technical information',
              url: `https://docs.example.com/advanced/${this.topic.toLowerCase().replace(/\s+/g, '-')}`
            }
          ]
        };
        
        this.documentationContent = mockData.content;
        this.documentationUrl = mockData.url;
        this.relatedTopics = mockData.relatedTopics;
      } catch (err) {
        console.error('Error loading documentation:', err);
        this.error = 'Failed to load documentation. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },
    
    printDocumentation() {
      const printWindow = window.open('', '_blank');
      
      if (!printWindow) {
        alert('Please allow pop-ups to print documentation');
        return;
      }
      
      const content = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${this.title} - ${this.topic}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; }
            h1 { color: #333; }
            .content { margin: 20px; }
            .footer { margin-top: 30px; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="content">
            <h1>${this.title} - ${this.topic}</h1>
            ${this.documentationContent}
            <div class="footer">
              <p>Printed from MVS-VR Guided Setup Wizard</p>
              <p>For the latest documentation, visit: ${this.documentationUrl}</p>
            </div>
          </div>
        </body>
        </html>
      `;
      
      printWindow.document.open();
      printWindow.document.write(content);
      printWindow.document.close();
      
      // Wait for content to load before printing
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 250);
    },
    
    openExternalDocs() {
      if (this.documentationUrl) {
        window.open(this.documentationUrl, '_blank');
      }
    },
    
    sendFeedback(isHelpful) {
      this.feedback = isHelpful;
      
      // In a real implementation, this would send feedback to an API
      this.$emit('feedback', {
        topic: this.topic,
        isHelpful,
        context: this.context
      });
    }
  }
};
</script>

<style scoped>
.documentation-reference {
  margin-top: 24px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
}

.documentation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--theme--background);
  border-bottom: 1px solid var(--theme--border-color);
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.documentation-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.documentation-toggle {
  background: none;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  color: var(--theme--foreground);
  transition: all 0.2s ease;
}

.documentation-toggle:hover {
  background-color: var(--theme--background-accent);
}

.documentation-content {
  padding: 16px;
  overflow: hidden;
}

.documentation-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(var(--theme--primary-rgb), 0.3);
  border-radius: 50%;
  border-top-color: var(--theme--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.documentation-error {
  padding: 16px;
  color: var(--theme--danger);
  text-align: center;
}

.retry-button {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 8px;
}

.documentation-body {
  font-size: 14px;
  line-height: 1.6;
  color: var(--theme--foreground);
}

.documentation-placeholder {
  padding: 16px;
  text-align: center;
  color: var(--theme--foreground-subdued);
}

.related-topics {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--theme--border-color);
}

.related-topics h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.topic-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.topic-item {
  margin-bottom: 8px;
}

.topic-link {
  color: var(--theme--primary);
  text-decoration: none;
  font-weight: 500;
}

.topic-link:hover {
  text-decoration: underline;
}

.topic-description {
  display: block;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-top: 2px;
}

.documentation-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--theme--border-color);
}

.documentation-actions {
  display: flex;
  gap: 8px;
}

.print-button,
.external-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  color: var(--theme--foreground);
  transition: all 0.2s ease;
}

.print-button:hover,
.external-button:hover {
  background-color: var(--theme--background-accent);
}

.documentation-feedback {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.feedback-buttons {
  display: flex;
  gap: 4px;
}

.feedback-button {
  background: none;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  transition: all 0.2s ease;
}

.feedback-button:hover {
  background-color: var(--theme--background-accent);
}

.feedback-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  border-color: var(--theme--primary);
}

/* Transitions */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
  max-height: 1000px;
}

.slide-enter,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}
</style>
