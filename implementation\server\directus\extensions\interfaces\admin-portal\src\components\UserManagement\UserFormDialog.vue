<template>
  <v-dialog
    :value="visible"
    @input="$emit('close')"
    max-width="800px"
    scrollable
    persistent
  >
    <v-card>
      <v-card-title class="headline d-flex justify-space-between align-center">
        <span>{{ isNew ? 'Create User' : 'Edit User' }}</span>
        <v-btn icon @click="$emit('close')">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      
      <v-divider></v-divider>
      
      <v-card-text class="user-form-content">
        <v-form ref="form" v-model="valid" lazy-validation>
          <!-- Stepper -->
          <v-stepper v-model="currentStep" vertical>
            <!-- Step 1: Basic Information -->
            <v-stepper-step :complete="currentStep > 1" step="1">
              Basic Information
              <small>User profile details</small>
            </v-stepper-step>
            
            <v-stepper-content step="1">
              <v-card flat>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="6">
                      <v-text-field
                        v-model="formData.first_name"
                        label="First Name"
                        :rules="[rules.required]"
                        required
                      ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12" md="6">
                      <v-text-field
                        v-model="formData.last_name"
                        label="Last Name"
                        :rules="[rules.required]"
                        required
                      ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12">
                      <v-text-field
                        v-model="formData.email"
                        label="Email"
                        :rules="[rules.required, rules.email]"
                        required
                      ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12" md="6">
                      <v-text-field
                        v-model="formData.phone"
                        label="Phone"
                        placeholder="Optional"
                      ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12" md="6">
                      <v-text-field
                        v-model="formData.location"
                        label="Location"
                        placeholder="Optional"
                      ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12" md="6">
                      <v-text-field
                        v-model="formData.company"
                        label="Company"
                        placeholder="Optional"
                      ></v-text-field>
                    </v-col>
                    
                    <v-col cols="12" md="6">
                      <v-text-field
                        v-model="formData.website"
                        label="Website"
                        placeholder="Optional"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
                
                <v-card-actions>
                  <v-spacer></v-spacer>
                  <v-btn
                    color="primary"
                    @click="validateStep(1)"
                  >
                    Continue
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-stepper-content>
            
            <!-- Step 2: Role and Permissions -->
            <v-stepper-step :complete="currentStep > 2" step="2">
              Role and Permissions
              <small>Assign user role and permissions</small>
            </v-stepper-step>
            
            <v-stepper-content step="2">
              <v-card flat>
                <v-card-text>
                  <v-row>
                    <v-col cols="12">
                      <v-select
                        v-model="formData.role"
                        :items="roleOptions"
                        label="Role"
                        :rules="[rules.required]"
                        required
                      ></v-select>
                      
                      <v-alert
                        v-if="formData.role"
                        text
                        outlined
                        :color="getRoleColor(formData.role)"
                        class="mt-3"
                      >
                        <div class="font-weight-medium">{{ getRoleDescription(formData.role) }}</div>
                      </v-alert>
                    </v-col>
                    
                    <v-col cols="12">
                      <v-select
                        v-model="formData.status"
                        :items="statusOptions"
                        label="Status"
                        :rules="[rules.required]"
                        required
                      ></v-select>
                    </v-col>
                  </v-row>
                </v-card-text>
                
                <v-card-actions>
                  <v-btn text @click="currentStep = 1">Back</v-btn>
                  <v-spacer></v-spacer>
                  <v-btn
                    color="primary"
                    @click="validateStep(2)"
                  >
                    Continue
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-stepper-content>
            
            <!-- Step 3: Security -->
            <v-stepper-step step="3">
              Security
              <small>Set password and security options</small>
            </v-stepper-step>
            
            <v-stepper-content step="3">
              <v-card flat>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" v-if="isNew">
                      <v-radio-group
                        v-model="passwordOption"
                        label="Password Option"
                        :rules="[rules.required]"
                        required
                      >
                        <v-radio
                          label="Set password now"
                          value="set"
                        ></v-radio>
                        <v-radio
                          label="Send password reset email"
                          value="reset"
                        ></v-radio>
                      </v-radio-group>
                    </v-col>
                    
                    <template v-if="passwordOption === 'set' || !isNew">
                      <v-col cols="12" v-if="!isNew">
                        <v-checkbox
                          v-model="changePassword"
                          label="Change Password"
                        ></v-checkbox>
                      </v-col>
                      
                      <template v-if="isNew || changePassword">
                        <v-col cols="12">
                          <v-text-field
                            v-model="formData.password"
                            label="Password"
                            :append-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
                            :type="showPassword ? 'text' : 'password'"
                            :rules="[rules.required, rules.password]"
                            required
                            @click:append="showPassword = !showPassword"
                          ></v-text-field>
                          
                          <v-progress-linear
                            :value="passwordStrength"
                            :color="passwordStrengthColor"
                            height="8"
                            class="mt-2"
                          ></v-progress-linear>
                          
                          <div class="caption mt-1" :class="`${passwordStrengthColor}--text`">
                            {{ passwordStrengthText }}
                          </div>
                        </v-col>
                        
                        <v-col cols="12">
                          <v-text-field
                            v-model="confirmPassword"
                            label="Confirm Password"
                            :append-icon="showConfirmPassword ? 'mdi-eye' : 'mdi-eye-off'"
                            :type="showConfirmPassword ? 'text' : 'password'"
                            :rules="[rules.required, rules.passwordMatch]"
                            required
                            @click:append="showConfirmPassword = !showConfirmPassword"
                          ></v-text-field>
                        </v-col>
                      </template>
                    </template>
                    
                    <v-col cols="12">
                      <v-checkbox
                        v-model="formData.email_verified"
                        label="Email Verified"
                      ></v-checkbox>
                    </v-col>
                    
                    <v-col cols="12">
                      <v-checkbox
                        v-model="formData.mfa_enabled"
                        label="Enable Two-Factor Authentication"
                      ></v-checkbox>
                    </v-col>
                  </v-row>
                </v-card-text>
                
                <v-card-actions>
                  <v-btn text @click="currentStep = 2">Back</v-btn>
                  <v-spacer></v-spacer>
                  <v-btn
                    color="primary"
                    @click="validateAndSubmit"
                  >
                    {{ isNew ? 'Create User' : 'Save Changes' }}
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-stepper-content>
          </v-stepper>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'UserFormDialog',
  
  props: {
    user: {
      type: Object,
      default: null
    },
    visible: {
      type: Boolean,
      default: false
    },
    isNew: {
      type: Boolean,
      default: true
    }
  },
  
  data() {
    return {
      valid: false,
      currentStep: 1,
      formData: {
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        location: '',
        company: '',
        website: '',
        role: 'user',
        status: 'active',
        password: '',
        email_verified: false,
        mfa_enabled: false
      },
      passwordOption: 'set',
      changePassword: false,
      showPassword: false,
      showConfirmPassword: false,
      confirmPassword: '',
      
      // Form validation rules
      rules: {
        required: v => !!v || 'This field is required',
        email: v => /.+@.+\..+/.test(v) || 'Email must be valid',
        password: v => {
          if (!v) return 'Password is required';
          if (v.length < 8) return 'Password must be at least 8 characters';
          return true;
        },
        passwordMatch: v => v === this.formData.password || 'Passwords do not match'
      },
      
      // Options for select fields
      roleOptions: [
        { text: 'Admin', value: 'admin' },
        { text: 'Manager', value: 'manager' },
        { text: 'Editor', value: 'editor' },
        { text: 'User', value: 'user' }
      ],
      
      statusOptions: [
        { text: 'Active', value: 'active' },
        { text: 'Inactive', value: 'inactive' },
        { text: 'Pending', value: 'pending' },
        { text: 'Suspended', value: 'suspended' }
      ]
    };
  },
  
  computed: {
    passwordStrength() {
      const password = this.formData.password || '';
      
      if (!password) return 0;
      
      let strength = 0;
      
      // Length check
      if (password.length >= 8) strength += 20;
      if (password.length >= 12) strength += 10;
      
      // Character type checks
      if (/[A-Z]/.test(password)) strength += 20; // Uppercase
      if (/[a-z]/.test(password)) strength += 20; // Lowercase
      if (/[0-9]/.test(password)) strength += 20; // Numbers
      if (/[^A-Za-z0-9]/.test(password)) strength += 20; // Special characters
      
      return Math.min(strength, 100);
    },
    
    passwordStrengthColor() {
      if (this.passwordStrength < 30) return 'error';
      if (this.passwordStrength < 70) return 'warning';
      return 'success';
    },
    
    passwordStrengthText() {
      if (this.passwordStrength < 30) return 'Weak';
      if (this.passwordStrength < 70) return 'Medium';
      return 'Strong';
    }
  },
  
  watch: {
    user: {
      immediate: true,
      handler(newUser) {
        if (newUser) {
          this.initFormData();
        }
      }
    },
    
    visible(newValue) {
      if (newValue) {
        this.currentStep = 1;
        this.initFormData();
      }
    }
  },
  
  methods: {
    initFormData() {
      if (this.user) {
        // Edit mode - populate form with user data
        this.formData = {
          id: this.user.id,
          first_name: this.user.first_name || '',
          last_name: this.user.last_name || '',
          email: this.user.email || '',
          phone: this.user.phone || '',
          location: this.user.location || '',
          company: this.user.company || '',
          website: this.user.website || '',
          role: this.user.role || 'user',
          status: this.user.status || 'active',
          password: '',
          email_verified: this.user.email_verified || false,
          mfa_enabled: this.user.mfa_enabled || false
        };
      } else {
        // Create mode - reset form
        this.formData = {
          first_name: '',
          last_name: '',
          email: '',
          phone: '',
          location: '',
          company: '',
          website: '',
          role: 'user',
          status: 'active',
          password: '',
          email_verified: false,
          mfa_enabled: false
        };
        this.passwordOption = 'set';
        this.changePassword = false;
      }
      
      this.confirmPassword = '';
      this.showPassword = false;
      this.showConfirmPassword = false;
    },
    
    validateStep(step) {
      if (this.$refs.form.validate()) {
        this.currentStep = step + 1;
      }
    },
    
    validateAndSubmit() {
      if (this.$refs.form.validate()) {
        // Prepare data for submission
        const userData = { ...this.formData };
        
        // Handle password
        if (this.isNew) {
          if (this.passwordOption === 'reset') {
            userData.password = null;
            userData.send_reset_email = true;
          }
        } else {
          if (!this.changePassword) {
            delete userData.password;
          }
        }
        
        // Emit save event with user data
        this.$emit('save', userData);
      }
    },
    
    getRoleColor(role) {
      switch (role) {
        case 'admin':
          return 'red';
        case 'manager':
          return 'orange';
        case 'editor':
          return 'blue';
        case 'user':
          return 'green';
        default:
          return 'grey';
      }
    },
    
    getRoleDescription(role) {
      switch (role) {
        case 'admin':
          return 'Administrators have full access to all features and settings.';
        case 'manager':
          return 'Managers can manage users, vendors, and content but cannot change system settings.';
        case 'editor':
          return 'Editors can create and edit content but cannot manage users or system settings.';
        case 'user':
          return 'Regular users have limited access to features based on their permissions.';
        default:
          return 'Role description not available.';
      }
    }
  }
};
</script>

<style scoped>
.user-form-content {
  max-height: 70vh;
  overflow-y: auto;
}
</style>
