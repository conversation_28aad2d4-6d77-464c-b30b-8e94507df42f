{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/unit/ProductConfigurator.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 87660, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 87660, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 672, "endOffset": 1531, "count": 7}], "isBlockCoverage": true}, {"functionName": "totalOptionsPrice", "ranges": [{"startOffset": 1551, "endOffset": 2369, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1646, "endOffset": 2340, "count": 3}, {"startOffset": 1773, "endOffset": 1780, "count": 0}, {"startOffset": 1829, "endOffset": 2089, "count": 1}, {"startOffset": 2089, "endOffset": 2332, "count": 2}, {"startOffset": 2304, "endOffset": 2308, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1915, "endOffset": 2077, "count": 2}, {"startOffset": 2045, "endOffset": 2049, "count": 0}], "isBlockCoverage": true}, {"functionName": "totalPrice", "ranges": [{"startOffset": 2375, "endOffset": 2540, "count": 0}], "isBlockCoverage": false}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2546, "endOffset": 2718, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadProducts", "ranges": [{"startOffset": 2742, "endOffset": 2879, "count": 0}], "isBlockCoverage": false}, {"functionName": "filterProducts", "ranges": [{"startOffset": 2885, "endOffset": 3263, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectProduct", "ranges": [{"startOffset": 3269, "endOffset": 3604, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadProductConfiguration", "ranges": [{"startOffset": 3610, "endOffset": 3908, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveConfiguration", "ranges": [{"startOffset": 3914, "endOffset": 4148, "count": 1}, {"startOffset": 3977, "endOffset": 3984, "count": 0}], "isBlockCoverage": true}, {"functionName": "addOptionGroup", "ranges": [{"startOffset": 4154, "endOffset": 4397, "count": 1}], "isBlockCoverage": true}, {"functionName": "removeOptionGroup", "ranges": [{"startOffset": 4403, "endOffset": 4501, "count": 1}], "isBlockCoverage": true}, {"functionName": "addOption", "ranges": [{"startOffset": 4507, "endOffset": 4767, "count": 1}], "isBlockCoverage": true}, {"functionName": "removeOption", "ranges": [{"startOffset": 4773, "endOffset": 4900, "count": 1}], "isBlockCoverage": true}, {"functionName": "selectOption", "ranges": [{"startOffset": 4906, "endOffset": 7207, "count": 10}, {"startOffset": 5046, "endOffset": 5961, "count": 0}, {"startOffset": 6107, "endOffset": 6136, "count": 1}, {"startOffset": 6136, "endOffset": 6306, "count": 9}, {"startOffset": 6306, "endOffset": 6375, "count": 7}, {"startOffset": 6375, "endOffset": 6759, "count": 2}, {"startOffset": 6530, "endOffset": 6749, "count": 1}, {"startOffset": 6759, "endOffset": 6950, "count": 9}, {"startOffset": 6950, "endOffset": 7193, "count": 1}, {"startOffset": 7193, "endOffset": 7201, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5645, "endOffset": 5716, "count": 0}], "isBlockCoverage": false}, {"functionName": "isOptionSelected", "ranges": [{"startOffset": 7213, "endOffset": 7542, "count": 4}, {"startOffset": 7302, "endOffset": 7456, "count": 2}, {"startOffset": 7456, "endOffset": 7536, "count": 0}], "isBlockCoverage": true}, {"functionName": "isOptionDisabled", "ranges": [{"startOffset": 7548, "endOffset": 7658, "count": 1}], "isBlockCoverage": true}, {"functionName": "canSelectOption", "ranges": [{"startOffset": 7664, "endOffset": 8811, "count": 11}, {"startOffset": 7900, "endOffset": 8785, "count": 6}, {"startOffset": 8292, "endOffset": 8327, "count": 2}, {"startOffset": 8327, "endOffset": 8742, "count": 4}, {"startOffset": 8742, "endOffset": 8777, "count": 0}, {"startOffset": 8785, "endOffset": 8810, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8197, "endOffset": 8268, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8593, "endOffset": 8718, "count": 0}], "isBlockCoverage": false}, {"functionName": "addDependency", "ranges": [{"startOffset": 8817, "endOffset": 9476, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeDependency", "ranges": [{"startOffset": 9482, "endOffset": 9733, "count": 0}], "isBlockCoverage": false}, {"functionName": "addIncompatibility", "ranges": [{"startOffset": 9739, "endOffset": 10455, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeIncompatibility", "ranges": [{"startOffset": 10461, "endOffset": 10732, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetConfiguration", "ranges": [{"startOffset": 10738, "endOffset": 10899, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12510, "endOffset": 17034, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12569, "endOffset": 13022, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13089, "endOffset": 13329, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13408, "endOffset": 14010, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14082, "endOffset": 14646, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14723, "endOffset": 15346, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15410, "endOffset": 15885, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15960, "endOffset": 16446, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16515, "endOffset": 17030, "count": 1}], "isBlockCoverage": true}]}]}