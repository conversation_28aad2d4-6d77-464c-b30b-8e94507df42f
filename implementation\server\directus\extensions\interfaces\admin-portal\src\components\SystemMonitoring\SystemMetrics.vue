<template>
  <v-card class="system-metrics">
    <v-card-title>System Metrics</v-card-title>
    
    <v-card-text>
      <v-skeleton-loader
        v-if="loading"
        type="card"
        class="mx-auto"
      ></v-skeleton-loader>
      
      <div v-else class="metrics-content">
        <!-- CPU Metrics -->
        <div class="metric-section">
          <div class="metric-header">
            <v-icon color="primary" class="mr-2">mdi-cpu-64-bit</v-icon>
            <h3>CPU</h3>
          </div>
          
          <v-row>
            <v-col cols="12">
              <div class="metric-item">
                <div class="metric-label">Usage</div>
                <v-progress-linear
                  :value="parseFloat(metrics?.cpu?.usage || 0)"
                  :color="getCpuColor(metrics?.cpu?.usage)"
                  height="20"
                  striped
                >
                  <template v-slot:default>
                    <strong>{{ metrics?.cpu?.usage || '0' }}%</strong>
                  </template>
                </v-progress-linear>
              </div>
            </v-col>
            
            <v-col cols="6">
              <div class="metric-item">
                <div class="metric-label">Cores</div>
                <div class="metric-value">{{ metrics?.cpu?.cores || 'N/A' }}</div>
              </div>
            </v-col>
            
            <v-col cols="6">
              <div class="metric-item">
                <div class="metric-label">Model</div>
                <div class="metric-value text-truncate" :title="metrics?.cpu?.model">
                  {{ metrics?.cpu?.model || 'N/A' }}
                </div>
              </div>
            </v-col>
          </v-row>
        </div>
        
        <!-- Memory Metrics -->
        <div class="metric-section">
          <div class="metric-header">
            <v-icon color="primary" class="mr-2">mdi-memory</v-icon>
            <h3>Memory</h3>
          </div>
          
          <v-row>
            <v-col cols="12">
              <div class="metric-item">
                <div class="metric-label">Usage</div>
                <v-progress-linear
                  :value="parseFloat(metrics?.memory?.usage || 0)"
                  :color="getMemoryColor(metrics?.memory?.usage)"
                  height="20"
                  striped
                >
                  <template v-slot:default>
                    <strong>{{ metrics?.memory?.usage || '0' }}%</strong>
                  </template>
                </v-progress-linear>
              </div>
            </v-col>
            
            <v-col cols="4">
              <div class="metric-item">
                <div class="metric-label">Total</div>
                <div class="metric-value">{{ metrics?.memory?.total || 'N/A' }}</div>
              </div>
            </v-col>
            
            <v-col cols="4">
              <div class="metric-item">
                <div class="metric-label">Used</div>
                <div class="metric-value">{{ metrics?.memory?.used || 'N/A' }}</div>
              </div>
            </v-col>
            
            <v-col cols="4">
              <div class="metric-item">
                <div class="metric-label">Free</div>
                <div class="metric-value">{{ metrics?.memory?.free || 'N/A' }}</div>
              </div>
            </v-col>
          </v-row>
        </div>
        
        <!-- Disk Metrics -->
        <div class="metric-section">
          <div class="metric-header">
            <v-icon color="primary" class="mr-2">mdi-harddisk</v-icon>
            <h3>Disk</h3>
          </div>
          
          <v-row>
            <v-col cols="12">
              <div class="metric-item">
                <div class="metric-label">Usage</div>
                <div class="metric-value">{{ metrics?.disk?.usage || 'N/A' }}</div>
              </div>
            </v-col>
          </v-row>
        </div>
        
        <!-- OS Info -->
        <div class="metric-section">
          <div class="metric-header">
            <v-icon color="primary" class="mr-2">mdi-desktop-tower-monitor</v-icon>
            <h3>Operating System</h3>
          </div>
          
          <v-row>
            <v-col cols="6">
              <div class="metric-item">
                <div class="metric-label">Type</div>
                <div class="metric-value">{{ metrics?.os?.type || 'N/A' }}</div>
              </div>
            </v-col>
            
            <v-col cols="6">
              <div class="metric-item">
                <div class="metric-label">Platform</div>
                <div class="metric-value">{{ metrics?.os?.platform || 'N/A' }}</div>
              </div>
            </v-col>
            
            <v-col cols="6">
              <div class="metric-item">
                <div class="metric-label">Release</div>
                <div class="metric-value">{{ metrics?.os?.release || 'N/A' }}</div>
              </div>
            </v-col>
            
            <v-col cols="6">
              <div class="metric-item">
                <div class="metric-label">Hostname</div>
                <div class="metric-value">{{ metrics?.os?.hostname || 'N/A' }}</div>
              </div>
            </v-col>
          </v-row>
        </div>
        
        <!-- Uptime -->
        <div class="metric-section">
          <div class="metric-header">
            <v-icon color="primary" class="mr-2">mdi-clock-outline</v-icon>
            <h3>Uptime</h3>
          </div>
          
          <v-row>
            <v-col cols="12">
              <div class="metric-item">
                <div class="metric-value uptime">{{ metrics?.uptime || 'N/A' }}</div>
              </div>
            </v-col>
          </v-row>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'SystemMetrics',
  
  props: {
    metrics: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    // Get CPU usage color
    getCpuColor(usage) {
      if (!usage) return 'grey';
      
      const cpuUsage = parseFloat(usage);
      
      if (cpuUsage > 90) {
        return 'error';
      } else if (cpuUsage > 70) {
        return 'warning';
      } else {
        return 'success';
      }
    },
    
    // Get memory usage color
    getMemoryColor(usage) {
      if (!usage) return 'grey';
      
      const memoryUsage = parseFloat(usage);
      
      if (memoryUsage > 90) {
        return 'error';
      } else if (memoryUsage > 70) {
        return 'warning';
      } else {
        return 'success';
      }
    }
  }
};
</script>

<style scoped>
.system-metrics {
  height: 100%;
}

.metrics-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metric-section {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  padding-bottom: 16px;
}

.metric-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.metric-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.metric-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.metric-item {
  margin-bottom: 8px;
}

.metric-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
  margin-bottom: 4px;
}

.metric-value {
  font-weight: 500;
}

.uptime {
  font-size: 1.25rem;
  color: #1976d2;
}
</style>
