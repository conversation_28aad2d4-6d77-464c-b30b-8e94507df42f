import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';

/**
 * Logout API endpoint
 *
 * This endpoint logs out a user by invalidating their session.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Log the request
    logger.info('Logout request');

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Get current session
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'No active session' });
    }

    // Log out
    const { error } = await supabase.auth.signOut();

    if (error) {
      logger.error('Logout error', { error });
      return res.status(500).json({ error: 'Error logging out' });
    }

    // Log success
    logger.info('Logout successful', {
      user_id: session.user.id,
    });

    // Return success
    return res.status(200).json({ success: true });
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
