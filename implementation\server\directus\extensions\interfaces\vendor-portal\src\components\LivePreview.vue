<template>
  <div class="live-preview" :class="{ 'split-mode': isSplitMode }">
    <!-- Edit Mode -->
    <div 
      v-if="isEditMode || isSplitMode" 
      class="edit-container"
      :class="{ 'full': isEditMode, 'split': isSplitMode }"
    >
      <div class="edit-header">
        <h3>Edit</h3>
      </div>
      
      <div class="edit-content">
        <slot name="editor"></slot>
      </div>
    </div>
    
    <!-- Preview Mode -->
    <div 
      v-if="isPreviewMode || isSplitMode" 
      class="preview-container"
      :class="{ 'full': isPreviewMode, 'split': isSplitMode }"
    >
      <div class="preview-header">
        <h3>Preview</h3>
      </div>
      
      <div class="preview-content">
        <PreviewFrame :src="previewUrl" />
      </div>
    </div>
    
    <!-- Preview Controls -->
    <div class="controls-container">
      <PreviewControls />
    </div>
  </div>
</template>

<script>
import { computed, onMounted, watch } from 'vue';
import { providePreviewContext, usePreviewContext } from '../contexts/PreviewContext';
import PreviewFrame from './PreviewFrame.vue';
import PreviewControls from './PreviewControls.vue';

export default {
  name: 'LivePreview',
  
  components: {
    PreviewFrame,
    PreviewControls
  },
  
  props: {
    initialMode: {
      type: String,
      default: 'edit',
      validator: (value) => ['edit', 'preview', 'split'].includes(value)
    },
    initialDevice: {
      type: String,
      default: 'desktop',
      validator: (value) => ['desktop', 'tablet', 'mobile', 'tv'].includes(value)
    },
    initialOrientation: {
      type: String,
      default: 'portrait',
      validator: (value) => ['portrait', 'landscape'].includes(value)
    },
    initialScale: {
      type: Number,
      default: 1
    },
    initialUrl: {
      type: String,
      default: ''
    },
    initialData: {
      type: Object,
      default: () => ({})
    },
    onChange: {
      type: Function,
      default: null
    },
    debounceMs: {
      type: Number,
      default: 500
    }
  },
  
  setup(props, { emit }) {
    // Provide preview context
    const previewContext = providePreviewContext({
      initialMode: props.initialMode,
      initialDevice: props.initialDevice,
      initialOrientation: props.initialOrientation,
      initialScale: props.initialScale,
      initialUrl: props.initialUrl,
      initialData: props.initialData
    });
    
    // Get preview context
    const {
      previewMode,
      previewData,
      previewUrl,
      isEditMode,
      isPreviewMode,
      isSplitMode,
      lastUpdated,
      setPreviewData
    } = previewContext;
    
    // Debounce timer
    let debounceTimer = null;
    
    // Watch for changes and emit events
    watch([previewMode, previewData, previewUrl, lastUpdated], () => {
      // Clear previous timer
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
      
      // Set new timer
      debounceTimer = setTimeout(() => {
        // Emit change event
        emit('change', {
          mode: previewMode.value,
          data: previewData,
          url: previewUrl.value
        });
        
        // Call onChange prop if provided
        if (props.onChange) {
          props.onChange({
            mode: previewMode.value,
            data: previewData,
            url: previewUrl.value
          });
        }
      }, props.debounceMs);
    }, { deep: true });
    
    // Lifecycle hooks
    onMounted(() => {
      // Initialize with props
      if (props.initialData) {
        setPreviewData(props.initialData);
      }
    });
    
    return {
      previewMode,
      previewUrl,
      isEditMode,
      isPreviewMode,
      isSplitMode
    };
  }
};
</script>

<style scoped>
.live-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background);
}

.edit-container,
.preview-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.edit-header,
.preview-header {
  padding: 8px 12px;
  border-bottom: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
}

.edit-header h3,
.preview-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.edit-content,
.preview-content {
  flex: 1;
  overflow: auto;
}

.controls-container {
  border-top: 1px solid var(--theme--border-color);
}

/* Split mode */
.live-preview.split-mode {
  flex-direction: row;
}

.edit-container.split,
.preview-container.split {
  width: 50%;
}

.edit-container.split {
  border-right: 1px solid var(--theme--border-color);
}

/* Full mode */
.edit-container.full,
.preview-container.full {
  width: 100%;
}
</style>
