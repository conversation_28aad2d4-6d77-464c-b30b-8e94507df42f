{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/LivePreview.vitest.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24471, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24471, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 352, "endOffset": 487, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 412, "endOffset": 458, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 539, "endOffset": 660, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 602, "endOffset": 651, "count": 6}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 1628, "endOffset": 1638, "count": 0}], "isBlockCoverage": false}, {"functionName": "default", "ranges": [{"startOffset": 1700, "endOffset": 1708, "count": 0}], "isBlockCoverage": false}, {"functionName": "data", "ranges": [{"startOffset": 1789, "endOffset": 2019, "count": 6}], "isBlockCoverage": true}, {"functionName": "handlePreviewChange", "ranges": [{"startOffset": 2038, "endOffset": 2482, "count": 1}, {"startOffset": 2106, "endOffset": 2118, "count": 0}, {"startOffset": 2152, "endOffset": 2163, "count": 0}, {"startOffset": 2199, "endOffset": 2211, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2876, "endOffset": 4973, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3168, "endOffset": 3391, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3425, "endOffset": 3540, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3587, "endOffset": 3678, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3728, "endOffset": 3819, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3872, "endOffset": 4022, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4066, "endOffset": 4211, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4268, "endOffset": 4969, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/test-utils/component-test-utils.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 29501, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 29501, "count": 1}], "isBlockCoverage": true}, {"functionName": "createWrapper", "ranges": [{"startOffset": 902, "endOffset": 1797, "count": 6}, {"startOffset": 995, "endOffset": 1031, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1899, "endOffset": 1928, "count": 6}], "isBlockCoverage": true}, {"functionName": "nextTick", "ranges": [{"startOffset": 2060, "endOffset": 2134, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2231, "endOffset": 2255, "count": 0}], "isBlockCoverage": false}, {"functionName": "wait", "ranges": [{"startOffset": 2440, "endOffset": 2519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2612, "endOffset": 2632, "count": 0}], "isBlockCoverage": false}, {"functionName": "findAll", "ranges": [{"startOffset": 2982, "endOffset": 3573, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3669, "endOffset": 3692, "count": 0}], "isBlockCoverage": false}, {"functionName": "trigger", "ranges": [{"startOffset": 4111, "endOffset": 4704, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4800, "endOffset": 4823, "count": 0}], "isBlockCoverage": false}, {"functionName": "setValue", "ranges": [{"startOffset": 5049, "endOffset": 5320, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5417, "endOffset": 5441, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasClass", "ranges": [{"startOffset": 5652, "endOffset": 6149, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6246, "endOffset": 6270, "count": 0}], "isBlockCoverage": false}]}]}