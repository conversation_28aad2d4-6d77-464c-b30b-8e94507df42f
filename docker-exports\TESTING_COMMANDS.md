# Testing Commands for MVS-VR Deployment

## Quick Test Commands

### 1. Test Port 80 Access
```bash
curl -v http://localhost/
curl -v http://localhost/health
```

### 2. Test Supabase Connectivity
```bash
curl -v https://hiyqiqbgiueyyvqoqhht.supabase.co/rest/v1/
curl -v https://hiyqiqbgiueyyvqoqhht.supabase.co/auth/v1/settings
```

### 3. Test Individual Services
```bash
# Auth Service
curl -v http://localhost:3005/health

# API Gateway
curl -v http://localhost:4000/health

# Asset Service
curl -v http://localhost:5000/health

# Scene Service
curl -v http://localhost:6000/health

# Blueprint Service
curl -v http://localhost:3003/health

# LLM Service
curl -v http://localhost:7000/health

# Analytics Service
curl -v http://localhost:8000/health

# Monitoring Service
curl -v http://localhost:9090/health

# Directus
curl -v http://localhost:8055/server/health
```

### 4. Test API Endpoints
```bash
# Main API through Nginx
curl -v http://localhost/api/health

# Direct API Gateway
curl -v http://localhost:4000/health

# Auth endpoints
curl -v http://localhost/auth/health
```

### 5. Check Docker Status
```bash
# Container status
docker-compose -f docker-compose.exported.yml ps

# Service logs
docker-compose -f docker-compose.exported.yml logs -f

# Specific service logs
docker-compose -f docker-compose.exported.yml logs -f nginx
docker-compose -f docker-compose.exported.yml logs -f api-gateway
docker-compose -f docker-compose.exported.yml logs -f auth-service
```

### 6. Network Tests
```bash
# Test internal network connectivity
docker exec -it $(docker-compose -f docker-compose.exported.yml ps -q nginx) wget -qO- http://api-gateway:4000/health

# Test Redis connectivity
docker exec -it $(docker-compose -f docker-compose.exported.yml ps -q redis) redis-cli -a 9elskdUeo@I! ping
```

### 7. DNS Tests (when deployed)
```bash
# Test domain resolution
nslookup mvs.kanousai.com
dig mvs.kanousai.com

# Test domain access
curl -v http://mvs.kanousai.com/
curl -v http://mvs.kanousai.com/health
curl -v http://admin.mvs.kanousai.com/
```

## Expected Responses

### Successful Health Check
```
HTTP/1.1 200 OK
Content-Type: text/plain

healthy
```

### Successful Main Page
```
HTTP/1.1 200 OK
Content-Type: text/plain

MVS-VR API Server - Running
API endpoints available at /api/
Admin panel at admin.mvs.kanousai.com
```

### Successful Supabase Connection
```
HTTP/1.1 200 OK
Content-Type: application/json

{"message":"ok"}
```

## Troubleshooting Commands

### If Services Won't Start
```bash
# Check for port conflicts
sudo netstat -tulpn | grep :80
sudo netstat -tulpn | grep :3005

# Check Docker images
docker images | grep mvs-vr-v2

# Restart specific service
docker-compose -f docker-compose.exported.yml restart nginx
```

### If Supabase Connection Fails
```bash
# Test direct connection
curl -I https://hiyqiqbgiueyyvqoqhht.supabase.co

# Check environment variables
docker-compose -f docker-compose.exported.yml exec auth-service env | grep SUPABASE
```

### If Port 80 Not Accessible
```bash
# Check if nginx is running
docker-compose -f docker-compose.exported.yml ps nginx

# Check nginx configuration
docker-compose -f docker-compose.exported.yml exec nginx nginx -t

# Check nginx logs
docker-compose -f docker-compose.exported.yml logs nginx
```
