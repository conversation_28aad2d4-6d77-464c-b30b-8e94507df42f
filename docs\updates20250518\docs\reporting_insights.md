# Reporting & Insights (Vendor Analytics System)

## Overview

This document defines the reporting and analytics system available to vendors within the Kanousei VR platform. It provides actionable insights into user behavior, product engagement, and space performance.

---

## Objectives

* Help vendors understand user interaction trends
* Support optimization of product layout and design
* Enable performance-based decisions for marketing and inventory

---

## Dashboard Sections

### 1. **Product Insights**

* Views per product (unique + total)
* Hover time / focus duration
* Favorites added
* Click-to-preview actions

### 2. **Space Performance**

* Entry count per room
* Average time spent per visitor
* Drop-off points (e.g. areas with no interactions)
* Navigation flow heatmap

### 3. **Engagement Funnels**

* First-touch → Interaction → Favorite → Add to Request
* Conversion ratios by category

### 4. **Device Breakdown**

* VR Headset, Desktop, WebGL, Mobile
* Geo + language detection (if enabled)

---

## Sample Data Schema

```json
{
  "vendorId": "ven_dubai_sofas_01",
  "reportDate": "2025-05-14",
  "productStats": [
    {
      "productId": "sofa_modern_004",
      "views": 134,
      "hoverTimeAvg": 4.2,
      "favorites": 12,
      "clicks": 28
    }
  ],
  "roomStats": [
    {
      "roomId": "dubai_main",
      "entries": 98,
      "avgTime": 93.2,
      "exitBeforeExplore": 22
    }
  ]
}
```

---

## Visualizations

* Bar graphs for top products
* Heatmap overlay on space layout
* Line chart for engagement trends
* Pie chart for device types

---

## Vendor Tools

* Export reports (CSV, PDF)
* Filter by date, room, product
* Compare performance between layouts or campaigns

---

## Admin Tools

* View analytics across all vendors
* Flag underperforming or low-engagement spaces
* Trigger QA audit or optimization suggestions

---

## Update Frequency

* Data updated hourly
* Realtime for live visitor count + current room occupancy

---

## Future Enhancements

* AI-powered suggestions: "Swap product A with trending B"
* Predictive layout scoring based on interaction history
* Vendor benchmarking against industry/category peers
