const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the directory of this script
const scriptDir = __dirname;
const projectRoot = path.resolve(scriptDir, '../..');

// Create the Playwright configuration file if it doesn't exist
const playwrightConfigPath = path.join(projectRoot, 'playwright.config.js');
if (!fs.existsSync(playwrightConfigPath)) {
  const configContent = `
// @ts-check
const { defineConfig, devices } = require('@playwright/test');

/**
 * @see https://playwright.dev/docs/test-configuration
 */
module.exports = defineConfig({
  testDir: './tests/e2e',
  timeout: 30 * 1000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    actionTimeout: 0,
    baseURL: 'http://localhost:8055',
    trace: 'on-first-retry',
    video: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
    {
      name: 'Tablet',
      use: { ...devices['iPad (gen 7)'] },
    },
  ],
  webServer: {
    command: 'cd ../../.. && npm run dev',
    port: 8055,
    reuseExistingServer: !process.env.CI,
  },
});
`;
  fs.writeFileSync(playwrightConfigPath, configContent);
  console.log(`Created Playwright config at ${playwrightConfigPath}`);
}

// Ensure the e2e directory exists
if (!fs.existsSync(scriptDir)) {
  fs.mkdirSync(scriptDir, { recursive: true });
  console.log(`Created e2e test directory at ${scriptDir}`);
}

// Install Playwright if not already installed
try {
  execSync('npx playwright --version', { stdio: 'ignore' });
  console.log('Playwright is already installed');
} catch (error) {
  console.log('Installing Playwright...');
  execSync('npm install -D @playwright/test', { stdio: 'inherit', cwd: projectRoot });
  execSync('npx playwright install', { stdio: 'inherit', cwd: projectRoot });
}

// Run the tests
console.log('Running Visual Editors E2E tests...');
try {
  const command = `cd "${projectRoot}" && npx playwright test visual-editors.e2e.js --project=chromium`;
  execSync(command, { stdio: 'inherit' });
  console.log('E2E tests completed successfully!');
} catch (error) {
  console.error('E2E tests failed with error:', error.message);
  process.exit(1);
}
