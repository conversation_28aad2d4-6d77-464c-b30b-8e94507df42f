// @ts-check
const { test, expect } = require('@playwright/test');

test.describe('Visual Editors E2E Tests', () => {
  let vendorId;

  test.beforeEach(async ({ page }) => {
    // Login to the vendor portal
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await page.waitForSelector('.vendor-dashboard');
    
    // Get the vendor ID from the page
    vendorId = await page.evaluate(() => {
      return localStorage.getItem('current_vendor_id') || '1';
    });
    
    // Navigate to Visual Editors
    await page.click('a[href*="visual-editors"]');
    
    // Wait for Visual Editors to load
    await page.waitForSelector('.visual-editors');
  });

  test('should load all tabs correctly', async ({ page }) => {
    // Check that all tabs are present
    await expect(page.locator('.tab-button')).toHaveCount(5);
    
    // Check tab labels
    const tabLabels = await page.$$eval('.tab-button', tabs => 
      tabs.map(tab => tab.textContent.trim())
    );
    
    expect(tabLabels).toContain('Showroom Layout');
    expect(tabLabels).toContain('Product Config');
    expect(tabLabels).toContain('Materials');
    expect(tabLabels).toContain('Lighting');
    expect(tabLabels).toContain('Animation');
    
    // Check that the first tab is active by default
    await expect(page.locator('.tab-button.active')).toHaveText(/Showroom Layout/);
  });

  test('should switch between tabs', async ({ page }) => {
    // Click on each tab and verify it becomes active
    const tabs = await page.$$('.tab-button');
    
    for (let i = 0; i < tabs.length; i++) {
      await tabs[i].click();
      await page.waitForTimeout(500); // Wait for tab content to load
      
      // Verify the correct tab is active
      await expect(page.locator('.tab-button.active')).toHaveText(
        new RegExp(await tabs[i].textContent())
      );
      
      // Verify the correct editor is displayed
      const editorContainers = await page.$$('.editor-container');
      expect(editorContainers.length).toBe(1);
    }
  });

  test('should load showroom data', async ({ page }) => {
    // Ensure we're on the Showroom Layout tab
    await page.click('.tab-button:has-text("Showroom Layout")');
    
    // Wait for showroom data to load
    await page.waitForSelector('.selection-controls select');
    
    // Check if showrooms are loaded in the dropdown
    const showroomOptions = await page.$$eval('#showroom-select option', options => 
      options.map(option => option.textContent.trim())
    );
    
    expect(showroomOptions.length).toBeGreaterThan(0);
    
    // Select a showroom
    await page.selectOption('#showroom-select', { index: 0 });
    
    // Wait for the editor to load the selected showroom
    await page.waitForTimeout(1000);
    
    // Verify the showroom editor is displaying content
    await expect(page.locator('.mock-showroom-layout-editor, .showroom-layout-editor')).toBeVisible();
  });

  test('should create a new showroom', async ({ page }) => {
    // Ensure we're on the Showroom Layout tab
    await page.click('.tab-button:has-text("Showroom Layout")');
    
    // Click the "New Showroom" button
    await page.click('.new-item-button:has-text("New Showroom")');
    
    // Wait for the editor to reset
    await page.waitForTimeout(1000);
    
    // Verify the editor is in "new" mode
    await expect(page.locator('.mock-showroom-layout-editor, .showroom-layout-editor')).toBeVisible();
    
    // Verify the dropdown doesn't have a selection
    const selectedValue = await page.$eval('#showroom-select', select => select.value);
    expect(selectedValue).toBeFalsy();
  });

  test('should toggle auto-save', async ({ page }) => {
    // Check that auto-save is enabled by default
    const isChecked = await page.$eval('.auto-save-toggle input', input => input.checked);
    expect(isChecked).toBe(true);
    
    // Toggle auto-save off
    await page.click('.auto-save-toggle input');
    
    // Verify it's toggled off
    const isCheckedAfterToggle = await page.$eval('.auto-save-toggle input', input => input.checked);
    expect(isCheckedAfterToggle).toBe(false);
  });

  test('should handle errors gracefully', async ({ page }) => {
    // Force an error by manipulating the API
    await page.evaluate(() => {
      // Override the API get method to simulate an error
      window.$api = {
        ...window.$api,
        get: () => Promise.reject(new Error('Simulated API error'))
      };
      
      // Trigger a reload
      document.querySelector('.retry-button')?.click();
    });
    
    // Wait for error message
    await page.waitForSelector('.error-container');
    
    // Verify error message is displayed
    await expect(page.locator('.error-message')).toHaveText(/Failed to load data/);
    
    // Verify retry button is present
    await expect(page.locator('.retry-button')).toBeVisible();
  });

  test('should be responsive on different screen sizes', async ({ page }) => {
    // Test desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(500);
    
    // Verify all tabs are visible
    await expect(page.locator('.tab-button')).toHaveCount(5);
    
    // Test tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);
    
    // Verify tabs are still visible
    await expect(page.locator('.tab-button')).toHaveCount(5);
    
    // Test mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    // Verify tabs are still accessible (might be scrollable)
    await expect(page.locator('.tab-button')).toHaveCount(5);
  });

  test('should perform well with large datasets', async ({ page }) => {
    // Inject a large dataset
    await page.evaluate(() => {
      // Generate a large dataset
      const largeDataset = Array.from({ length: 100 }, (_, i) => ({
        id: `showroom${i}`,
        name: `Test Showroom ${i}`,
        vendor_id: '1'
      }));
      
      // Override the showrooms data
      document.querySelector('.visual-editors').__vue__.showrooms = largeDataset;
    });
    
    // Measure performance of tab switching
    const startTime = Date.now();
    
    // Switch tabs multiple times
    for (const tabName of ['Product Config', 'Materials', 'Lighting', 'Animation', 'Showroom Layout']) {
      await page.click(`.tab-button:has-text("${tabName}")`);
      await page.waitForTimeout(200);
    }
    
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    // Verify performance is acceptable (less than 3 seconds for 5 tab switches)
    expect(totalTime).toBeLessThan(3000);
  });
});
