# MVS-VR Admin User Guide

This guide provides comprehensive instructions for system administrators of the MVS-VR platform. It covers dashboard usage, user management, vendor management, system monitoring, and settings configuration.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [User Management](#user-management)
4. [Vendor Management](#vendor-management)
5. [System Monitoring](#system-monitoring)
6. [Settings Configuration](#settings-configuration)
7. [Analytics and Reporting](#analytics-and-reporting)
8. [Troubleshooting](#troubleshooting)

## Getting Started

### Accessing the Admin Portal

1. Navigate to `https://your-domain.com/admin` in your web browser
2. Enter your admin credentials (email and password)
3. If enabled, complete the two-factor authentication process
4. You will be redirected to the admin dashboard

### First-Time Setup

If you're accessing the admin portal for the first time, you'll need to complete the following steps:

1. Change your temporary password
2. Set up two-factor authentication (recommended)
3. Review and update system settings
4. Create additional admin users (if needed)

### Navigation

The admin portal has a sidebar navigation menu with the following sections:

- **Dashboard**: Overview of system status and key metrics
- **Users**: User management
- **Vendors**: Vendor management
- **Clients**: Client management
- **Assets**: Asset management
- **Analytics**: Analytics and reporting
- **System**: System monitoring and settings
- **Settings**: Admin portal settings

## Dashboard Overview

The admin dashboard provides a comprehensive overview of the MVS-VR platform's status and key metrics.

### Dashboard Widgets

The dashboard consists of customizable widgets that display various metrics and information:

- **System Health**: Shows the status of key system components
- **User Activity**: Displays recent user activity
- **Vendor Activity**: Shows recent vendor activity
- **Asset Usage**: Displays asset usage metrics
- **Storage Usage**: Shows storage usage across the platform
- **Recent Alerts**: Displays recent system alerts

### Customizing the Dashboard

You can customize the dashboard to show the information most relevant to you:

1. Click the **Customize** button in the top-right corner of the dashboard
2. Drag and drop widgets to rearrange them
3. Click the **Add Widget** button to add new widgets
4. Click the gear icon on a widget to configure it or remove it
5. Click **Save** to save your dashboard layout

### Dashboard Settings

To access dashboard settings:

1. Click the gear icon in the top-right corner of the dashboard
2. Adjust the following settings:
   - **Refresh Interval**: How often the dashboard data refreshes
   - **Default Date Range**: The default date range for dashboard metrics
   - **Alert Notifications**: Whether to show alert notifications on the dashboard
   - **Widget Density**: The density of widgets on the dashboard (compact, normal, or spacious)

## User Management

The user management section allows you to manage all users of the MVS-VR platform, including admins, vendor users, and clients.

### User List

The user list displays all users in the system with the following information:

- **Name**: User's full name
- **Email**: User's email address
- **Role**: User's role (Admin, Vendor, Client)
- **Status**: User's status (Active, Inactive, Pending)
- **Last Login**: Date and time of the user's last login
- **Actions**: Actions you can perform on the user

### Creating a New User

To create a new user:

1. Click the **Create User** button in the top-right corner of the user list
2. Fill in the following information:
   - **First Name**: User's first name
   - **Last Name**: User's last name
   - **Email**: User's email address
   - **Role**: User's role (Admin, Vendor, Client)
   - **Vendor**: If the role is Vendor, select the vendor the user belongs to
   - **Password**: User's password (or select "Send email invitation" to let the user set their own password)
3. Click **Create** to create the user

### Editing a User

To edit a user:

1. Click the edit icon in the Actions column for the user you want to edit
2. Update the user's information
3. Click **Save** to save your changes

### Deactivating a User

To deactivate a user:

1. Click the deactivate icon in the Actions column for the user you want to deactivate
2. Confirm that you want to deactivate the user
3. The user's status will change to Inactive, and they will no longer be able to log in

### Reactivating a User

To reactivate a user:

1. Click the activate icon in the Actions column for the user you want to reactivate
2. The user's status will change to Active, and they will be able to log in again

### Deleting a User

To delete a user:

1. Click the delete icon in the Actions column for the user you want to delete
2. Confirm that you want to delete the user
3. The user will be permanently deleted from the system

### User Roles and Permissions

The MVS-VR platform has the following user roles:

- **Admin**: Full access to the admin portal and all features
- **Vendor**: Access to the vendor portal and vendor-specific features
- **Client**: Access to the client portal and client-specific features

You can customize permissions for each role in the Settings section.

## Vendor Management

The vendor management section allows you to manage all vendors on the MVS-VR platform.

### Vendor List

The vendor list displays all vendors in the system with the following information:

- **Name**: Vendor's name
- **Status**: Vendor's status (Active, Inactive, Pending)
- **Users**: Number of users associated with the vendor
- **Assets**: Number of assets owned by the vendor
- **Created**: Date and time the vendor was created
- **Actions**: Actions you can perform on the vendor

### Creating a New Vendor

To create a new vendor:

1. Click the **Create Vendor** button in the top-right corner of the vendor list
2. Fill in the following information:
   - **Name**: Vendor's name
   - **Description**: Vendor's description
   - **Contact Email**: Vendor's contact email
   - **Contact Phone**: Vendor's contact phone
   - **Logo**: Vendor's logo (optional)
   - **Website**: Vendor's website (optional)
   - **Address**: Vendor's address (optional)
3. Click **Create** to create the vendor

### Editing a Vendor

To edit a vendor:

1. Click the edit icon in the Actions column for the vendor you want to edit
2. Update the vendor's information
3. Click **Save** to save your changes

### Deactivating a Vendor

To deactivate a vendor:

1. Click the deactivate icon in the Actions column for the vendor you want to deactivate
2. Confirm that you want to deactivate the vendor
3. The vendor's status will change to Inactive, and vendor users will no longer be able to log in

### Reactivating a Vendor

To reactivate a vendor:

1. Click the activate icon in the Actions column for the vendor you want to reactivate
2. The vendor's status will change to Active, and vendor users will be able to log in again

### Deleting a Vendor

To delete a vendor:

1. Click the delete icon in the Actions column for the vendor you want to delete
2. Confirm that you want to delete the vendor
3. The vendor and all associated data will be permanently deleted from the system

### Vendor Details

To view detailed information about a vendor:

1. Click the vendor's name in the vendor list
2. The vendor details page will display the following information:
   - **Overview**: General information about the vendor
   - **Users**: Users associated with the vendor
   - **Assets**: Assets owned by the vendor
   - **Analytics**: Analytics and metrics for the vendor
   - **Settings**: Vendor-specific settings

### Managing Vendor Users

To manage users associated with a vendor:

1. Click the vendor's name in the vendor list
2. Click the **Users** tab
3. From here, you can:
   - View all users associated with the vendor
   - Create new vendor users
   - Edit existing vendor users
   - Deactivate or reactivate vendor users
   - Delete vendor users

## System Monitoring

The system monitoring section allows you to monitor the health and performance of the MVS-VR platform.

### System Health Dashboard

The system health dashboard provides a real-time overview of the system's health with the following information:

- **API Status**: Status of the API (Up, Degraded, Down)
- **Database Status**: Status of the database (Up, Degraded, Down)
- **Storage Status**: Status of the storage system (Up, Degraded, Down)
- **Cache Status**: Status of the cache system (Up, Degraded, Down)
- **Worker Status**: Status of background workers (Up, Degraded, Down)

### API Monitoring

The API monitoring section provides detailed information about API usage and performance:

- **Request Volume**: Number of API requests over time
- **Response Time**: Average API response time
- **Error Rate**: Percentage of API requests that result in errors
- **Top Endpoints**: Most frequently used API endpoints
- **Rate Limiting**: Information about rate-limited requests

### Database Monitoring

The database monitoring section provides detailed information about database performance:

- **Query Volume**: Number of database queries over time
- **Query Time**: Average database query time
- **Connection Pool**: Database connection pool usage
- **Disk Usage**: Database disk usage
- **Index Usage**: Database index usage

### Log Management

The log management section allows you to view and search system logs:

1. Select the log type (API, Database, Worker, etc.)
2. Set the date range
3. Set the log level (Debug, Info, Warning, Error, Critical)
4. Enter search terms (optional)
5. Click **Search** to view matching logs

### Alerts

The alerts section displays system alerts and allows you to configure alert rules:

- **Active Alerts**: Currently active alerts
- **Alert History**: History of past alerts
- **Alert Rules**: Rules that trigger alerts
- **Alert Channels**: Channels for alert notifications (Email, SMS, Slack, etc.)

## Settings Configuration

The settings section allows you to configure various aspects of the MVS-VR platform.

### General Settings

The general settings section includes:

- **Site Name**: The name of your MVS-VR instance
- **Site URL**: The URL of your MVS-VR instance
- **Admin Email**: The email address for system notifications
- **Default Language**: The default language for the platform
- **Default Timezone**: The default timezone for the platform
- **Date Format**: The default date format
- **Time Format**: The default time format

### Security Settings

The security settings section includes:

- **Password Policy**: Requirements for user passwords
- **Session Timeout**: How long user sessions last before requiring re-authentication
- **Two-Factor Authentication**: Whether two-factor authentication is required
- **API Rate Limiting**: Rate limits for API requests
- **CORS Settings**: Cross-Origin Resource Sharing settings
- **Content Security Policy**: Content Security Policy settings

### Email Settings

The email settings section includes:

- **SMTP Server**: SMTP server for sending emails
- **SMTP Port**: SMTP port
- **SMTP Username**: SMTP username
- **SMTP Password**: SMTP password
- **From Email**: Email address that appears in the From field
- **From Name**: Name that appears in the From field
- **Email Templates**: Templates for system emails

### Storage Settings

The storage settings section includes:

- **Storage Provider**: Provider for file storage (Local, S3, Google Cloud Storage, etc.)
- **Storage Credentials**: Credentials for the storage provider
- **Storage Bucket**: Storage bucket or container
- **CDN Settings**: Content Delivery Network settings
- **File Size Limits**: Maximum file size for uploads
- **Allowed File Types**: File types that can be uploaded

### Integration Settings

The integration settings section includes:

- **LLM Provider**: Provider for Large Language Model integration (OpenAI, Anthropic, etc.)
- **LLM Credentials**: Credentials for the LLM provider
- **Analytics Integration**: Integration with analytics platforms (Google Analytics, etc.)
- **Payment Gateway**: Integration with payment gateways (Stripe, etc.)
- **Webhook Settings**: Settings for outgoing webhooks

## Analytics and Reporting

The analytics and reporting section provides insights into platform usage and performance.

### Dashboard

The analytics dashboard provides an overview of key metrics:

- **Active Users**: Number of active users over time
- **New Users**: Number of new users over time
- **Asset Usage**: Asset usage metrics
- **Storage Usage**: Storage usage metrics
- **API Usage**: API usage metrics

### Reports

The reports section allows you to generate and view reports:

1. Select the report type (User Activity, Asset Usage, etc.)
2. Set the date range
3. Set any additional filters
4. Click **Generate Report** to create the report
5. View the report or export it in various formats (CSV, PDF, Excel)

### Custom Reports

The custom reports section allows you to create and save custom reports:

1. Click **Create Custom Report**
2. Select the data source
3. Select the metrics to include
4. Set filters and grouping
5. Set the visualization type (Table, Chart, etc.)
6. Save the report for future use

## Troubleshooting

### Common Issues

1. **User Cannot Log In**
   - Check that the user's status is Active
   - Verify that the user's email and password are correct
   - Check if the user's account is locked due to too many failed login attempts
   - Verify that the user has the necessary permissions

2. **API Errors**
   - Check the API logs for detailed error information
   - Verify that the API is running and accessible
   - Check if the API is rate-limited
   - Verify that the API request is properly formatted

3. **Storage Issues**
   - Check the storage provider's status
   - Verify that the storage credentials are correct
   - Check if the storage bucket exists and is accessible
   - Verify that the file size and type are allowed

### Getting Help

If you encounter issues not covered in this guide, you can:

- Check the [Admin FAQ](./ADMIN_FAQ.md)
- Visit the [MVS-VR Support Portal](https://support.mvs-vr.com)
- Contact <NAME_EMAIL>
