<template>
  <div class="variant-manager">
    <div class="variant-header">
      <h4>Product Variants</h4>
      <button class="btn btn-secondary" @click="addVariant">
        <i class="material-icons">add</i> Add Variant
      </button>
    </div>
    
    <div v-if="variants.length === 0" class="empty-variants">
      <p>No variants added yet. Add variants to create different versions of this product.</p>
    </div>
    
    <div v-else class="variants-list">
      <div v-for="(variant, index) in variants" :key="index" class="variant-item">
        <div class="variant-info">
          <div class="variant-name">
            <span class="variant-label">Name:</span>
            <input 
              type="text" 
              v-model="variant.name" 
              placeholder="Variant name"
              @input="updateVariant(index)"
            />
          </div>
          
          <div class="variant-attributes">
            <div v-for="(option, attrIndex) in variant.attributes" :key="attrIndex" class="variant-attribute">
              <div class="attribute-type">
                <select v-model="option.type" @change="updateVariant(index)">
                  <option value="">-- Select Type --</option>
                  <option v-for="attr in availableAttributes" :key="attr.id" :value="attr.id">
                    {{ attr.name }}
                  </option>
                </select>
              </div>
              <div class="attribute-value">
                <input 
                  type="text" 
                  v-model="option.value" 
                  :placeholder="`${getAttributeName(option.type)} value`"
                  @input="updateVariant(index)"
                />
              </div>
              <button class="btn btn-icon" @click="removeAttribute(index, attrIndex)" title="Remove Attribute">
                <i class="material-icons">close</i>
              </button>
            </div>
            
            <button class="btn btn-text" @click="addAttribute(index)">
              <i class="material-icons">add</i> Add Attribute
            </button>
          </div>
          
          <div class="variant-price">
            <div class="price-field">
              <span class="variant-label">Price:</span>
              <input 
                type="number" 
                v-model="variant.price" 
                placeholder="Price"
                min="0"
                step="0.01"
                @input="updateVariant(index)"
              />
            </div>
            
            <div class="stock-field">
              <span class="variant-label">Stock:</span>
              <input 
                type="number" 
                v-model="variant.stock" 
                placeholder="Stock"
                min="0"
                step="1"
                @input="updateVariant(index)"
              />
            </div>
            
            <div class="sku-field">
              <span class="variant-label">SKU:</span>
              <input 
                type="text" 
                v-model="variant.sku" 
                placeholder="SKU"
                @input="updateVariant(index)"
              />
            </div>
          </div>
        </div>
        
        <div class="variant-image">
          <div class="image-preview">
            <img v-if="variant.image" :src="variant.image" alt="Variant image" />
            <div v-else class="placeholder-image">
              <i class="material-icons">image</i>
            </div>
          </div>
          <label class="btn btn-secondary btn-sm">
            <i class="material-icons">upload</i> Upload
            <input 
              type="file" 
              accept="image/*" 
              @change="e => handleImageUpload(e, index)" 
              style="display: none;"
            />
          </label>
        </div>
        
        <div class="variant-actions">
          <button class="btn btn-icon" @click="removeVariant(index)" title="Remove Variant">
            <i class="material-icons">delete</i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductVariantManager',
  
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  
  data() {
    return {
      variants: [],
      availableAttributes: [
        { id: 'color', name: 'Color' },
        { id: 'size', name: 'Size' },
        { id: 'material', name: 'Material' },
        { id: 'style', name: 'Style' },
        { id: 'finish', name: 'Finish' },
        { id: 'weight', name: 'Weight' },
        { id: 'dimensions', name: 'Dimensions' }
      ]
    };
  },
  
  created() {
    // Clone the variants to avoid direct mutation
    this.variants = JSON.parse(JSON.stringify(this.value || []));
  },
  
  watch: {
    value: {
      handler(newValue) {
        // Update local variants when prop changes
        this.variants = JSON.parse(JSON.stringify(newValue || []));
      },
      deep: true
    }
  },
  
  methods: {
    // Add a new variant
    addVariant() {
      this.variants.push({
        name: '',
        attributes: [{ type: '', value: '' }],
        price: '',
        stock: '',
        sku: '',
        image: null
      });
      this.emitChange();
    },
    
    // Remove a variant
    removeVariant(index) {
      this.variants.splice(index, 1);
      this.emitChange();
    },
    
    // Add an attribute to a variant
    addAttribute(variantIndex) {
      this.variants[variantIndex].attributes.push({ type: '', value: '' });
      this.emitChange();
    },
    
    // Remove an attribute from a variant
    removeAttribute(variantIndex, attributeIndex) {
      this.variants[variantIndex].attributes.splice(attributeIndex, 1);
      this.emitChange();
    },
    
    // Update a variant
    updateVariant(index) {
      // This method is called on input events to trigger the emitChange
      this.emitChange();
    },
    
    // Handle image upload for a variant
    handleImageUpload(event, index) {
      const file = event.target.files[0];
      if (!file) return;
      
      // Create a preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        this.variants[index].image = e.target.result;
        this.variants[index].imageFile = file;
        this.emitChange();
      };
      reader.readAsDataURL(file);
    },
    
    // Get attribute name by ID
    getAttributeName(attributeId) {
      const attribute = this.availableAttributes.find(attr => attr.id === attributeId);
      return attribute ? attribute.name : 'Attribute';
    },
    
    // Emit change event
    emitChange() {
      this.$emit('input', JSON.parse(JSON.stringify(this.variants)));
    }
  }
};
</script>

<style scoped>
.variant-manager {
  margin-top: 20px;
}

.variant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.variant-header h4 {
  margin: 0;
  font-size: 16px;
}

.empty-variants {
  padding: 20px;
  text-align: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  color: var(--theme--foreground-subdued);
}

.variants-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.variant-item {
  display: flex;
  gap: 15px;
  padding: 15px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  position: relative;
}

.variant-info {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.variant-name {
  display: flex;
  align-items: center;
  gap: 10px;
}

.variant-label {
  font-weight: 500;
  min-width: 50px;
}

.variant-attributes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 5px;
}

.variant-attribute {
  display: flex;
  gap: 10px;
  align-items: center;
}

.attribute-type {
  width: 120px;
}

.attribute-value {
  flex-grow: 1;
}

.variant-price {
  display: flex;
  gap: 15px;
  margin-top: 5px;
}

.price-field, .stock-field, .sku-field {
  display: flex;
  align-items: center;
  gap: 10px;
}

.variant-image {
  width: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.image-preview {
  width: 80px;
  height: 80px;
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background);
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.placeholder-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.placeholder-image i {
  font-size: 24px;
}

.variant-actions {
  position: absolute;
  top: 10px;
  right: 10px;
}

.btn-text {
  background: none;
  border: none;
  color: var(--theme--primary);
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.btn-text i {
  font-size: 16px;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-sm i {
  font-size: 16px;
}

input, select {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

input:focus, select:focus {
  outline: none;
  border-color: var(--theme--primary);
}
</style>
