<template>
  <div class="showroom-configurator">
    <div class="configurator-header">
      <h3>{{ showroom ? 'Edit Showroom: ' + showroom.name : 'Create New Showroom' }}</h3>
      <button class="btn btn-icon" @click="$emit('close')">
        <i class="material-icons">close</i>
      </button>
    </div>

    <div class="configurator-content">
      <div class="configurator-sidebar">
        <div class="sidebar-section">
          <h4>Configuration</h4>
          <ul class="sidebar-nav">
            <li
              v-for="(section, index) in configSections"
              :key="index"
              :class="{ active: activeSection === section.id }"
              @click="activeSection = section.id"
            >
              <i class="material-icons">{{ section.icon }}</i>
              <span>{{ section.name }}</span>
            </li>
          </ul>
        </div>

        <div class="sidebar-section">
          <h4>Preview</h4>
          <div class="preview-container">
            <div class="preview-image">
              <img v-if="previewImage" :src="previewImage" alt="Showroom Preview">
              <div v-else class="preview-placeholder">
                <i class="material-icons">view_in_ar</i>
                <span>Preview will update as you configure your showroom</span>
              </div>
            </div>
          </div>

          <div class="preview-actions">
            <button class="btn btn-secondary btn-small" @click="generatePreview">
              <i class="material-icons">refresh</i> Update Preview
            </button>
          </div>
        </div>
      </div>

      <div class="configurator-main">
        <!-- Basic Information Section -->
        <div v-if="activeSection === 'basic'" class="config-section">
          <h4>Basic Information</h4>
          <div class="form-group">
            <label for="showroom-name">Showroom Name</label>
            <input
              id="showroom-name"
              type="text"
              v-model="formData.name"
              placeholder="Enter showroom name"
              required
            />
          </div>

          <div class="form-group">
            <label for="showroom-description">Description</label>
            <textarea
              id="showroom-description"
              v-model="formData.description"
              placeholder="Enter showroom description"
              rows="3"
            ></textarea>
          </div>

          <div class="form-group">
            <label for="showroom-status">Status</label>
            <select id="showroom-status" v-model="formData.status">
              <option value="draft">Draft</option>
              <option value="published">Published</option>
              <option value="archived">Archived</option>
            </select>
          </div>

          <div class="form-group">
            <label for="showroom-thumbnail">Thumbnail</label>
            <div class="thumbnail-upload">
              <div
                class="thumbnail-preview"
                :class="{ 'has-thumbnail': thumbnailPreview }"
                @click="triggerFileInput"
              >
                <img v-if="thumbnailPreview" :src="thumbnailPreview" alt="Thumbnail Preview">
                <div v-else class="thumbnail-placeholder">
                  <i class="material-icons">add_photo_alternate</i>
                  <span>Click to upload</span>
                </div>
              </div>
              <input
                id="showroom-thumbnail"
                type="file"
                ref="thumbnailInput"
                accept="image/*"
                @change="handleThumbnailUpload"
                style="display: none"
              />
              <button
                v-if="thumbnailPreview"
                class="btn btn-text"
                @click="removeThumbnail"
              >
                Remove
              </button>
            </div>
          </div>
        </div>

        <!-- Layout Section -->
        <div v-if="activeSection === 'layout'" class="config-section">
          <h4>Layout Configuration</h4>

          <div class="form-group">
            <label>Template</label>
            <div class="template-grid">
              <div
                v-for="template in templates"
                :key="template.id"
                class="template-card"
                :class="{ active: formData.template === template.id }"
                @click="selectTemplate(template.id)"
              >
                <div class="template-thumbnail">
                  <img v-if="template.thumbnail" :src="template.thumbnail" :alt="template.name">
                  <div v-else class="template-placeholder">
                    <i class="material-icons">view_in_ar</i>
                  </div>
                </div>
                <div class="template-info">
                  <h5>{{ template.name }}</h5>
                  <p>{{ template.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="layout-style">Layout Style</label>
            <select id="layout-style" v-model="formData.layoutStyle">
              <option value="open">Open Space</option>
              <option value="gallery">Gallery</option>
              <option value="rooms">Separate Rooms</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div class="form-group">
            <label for="layout-size">Size</label>
            <select id="layout-size" v-model="formData.layoutSize">
              <option value="small">Small (up to 20 products)</option>
              <option value="medium">Medium (up to 50 products)</option>
              <option value="large">Large (up to 100 products)</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div class="form-group">
            <label>Navigation Style</label>
            <div class="radio-group">
              <label class="radio-label">
                <input type="radio" v-model="formData.navigation" value="teleport">
                <span>Teleportation</span>
              </label>
              <label class="radio-label">
                <input type="radio" v-model="formData.navigation" value="continuous">
                <span>Continuous Movement</span>
              </label>
              <label class="radio-label">
                <input type="radio" v-model="formData.navigation" value="both">
                <span>Both</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Appearance Section -->
        <div v-if="activeSection === 'appearance'" class="config-section">
          <h4>Appearance</h4>

          <div class="form-group">
            <label for="appearance-theme">Theme</label>
            <select id="appearance-theme" v-model="formData.theme">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="modern">Modern</option>
              <option value="classic">Classic</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          <div class="form-group">
            <label>Color Scheme</label>
            <div class="color-picker-group">
              <div class="color-picker">
                <label>Primary Color</label>
                <input type="color" v-model="formData.primaryColor">
              </div>
              <div class="color-picker">
                <label>Secondary Color</label>
                <input type="color" v-model="formData.secondaryColor">
              </div>
              <div class="color-picker">
                <label>Accent Color</label>
                <input type="color" v-model="formData.accentColor">
              </div>
            </div>
          </div>

          <div class="form-group">
            <label>Lighting</label>
            <div class="slider-group">
              <label>Brightness</label>
              <input
                type="range"
                v-model="formData.brightness"
                min="0"
                max="100"
                step="5"
              >
              <div class="slider-value">{{ formData.brightness }}%</div>
            </div>
            <div class="slider-group">
              <label>Contrast</label>
              <input
                type="range"
                v-model="formData.contrast"
                min="0"
                max="100"
                step="5"
              >
              <div class="slider-value">{{ formData.contrast }}%</div>
            </div>
          </div>

          <div class="form-group">
            <label>Environment</label>
            <div class="environment-options">
              <div
                v-for="env in environments"
                :key="env.id"
                class="environment-option"
                :class="{ active: formData.environment === env.id }"
                @click="formData.environment = env.id"
              >
                <div class="environment-thumbnail">
                  <img v-if="env.thumbnail" :src="env.thumbnail" :alt="env.name">
                  <div v-else class="environment-placeholder">
                    <i class="material-icons">landscape</i>
                  </div>
                </div>
                <div class="environment-name">{{ env.name }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Products Section -->
        <div v-if="activeSection === 'products'" class="config-section">
          <h4>Products</h4>

          <div class="form-group">
            <label>Product Selection</label>
            <div class="selection-tabs">
              <div
                class="selection-tab"
                :class="{ active: productSelectionMode === 'categories' }"
                @click="productSelectionMode = 'categories'"
              >
                By Category
              </div>
              <div
                class="selection-tab"
                :class="{ active: productSelectionMode === 'individual' }"
                @click="productSelectionMode = 'individual'"
              >
                Individual Products
              </div>
            </div>

            <div v-if="productSelectionMode === 'categories'" class="category-selection">
              <div
                v-for="category in categories"
                :key="category.id"
                class="category-item"
              >
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    :value="category.id"
                    v-model="formData.selectedCategories"
                  >
                  <span>{{ category.name }}</span>
                </label>
              </div>
            </div>

            <div v-else class="product-selection">
              <div class="search-products">
                <input
                  type="text"
                  v-model="productSearchQuery"
                  placeholder="Search products..."
                >
                <i class="material-icons">search</i>
              </div>

              <div class="product-list">
                <div
                  v-for="product in filteredProducts"
                  :key="product.id"
                  class="product-item"
                >
                  <label class="checkbox-label">
                    <input
                      type="checkbox"
                      :value="product.id"
                      v-model="formData.selectedProducts"
                    >
                    <div class="product-info">
                      <div class="product-thumbnail">
                        <img v-if="product.thumbnail" :src="product.thumbnail" :alt="product.name">
                        <div v-else class="product-placeholder">
                          <i class="material-icons">inventory_2</i>
                        </div>
                      </div>
                      <div class="product-details">
                        <div class="product-name">{{ product.name }}</div>
                        <div class="product-category">{{ getCategoryName(product.category) }}</div>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Features Section -->
        <div v-if="activeSection === 'features'" class="config-section">
          <h4>Interactive Features</h4>

          <div class="form-group">
            <label>Enable Features</label>
            <div class="feature-list">
              <div class="feature-item">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.features.productInfo">
                  <span>Product Information Panels</span>
                </label>
              </div>
              <div class="feature-item">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.features.productInteraction">
                  <span>Product Interaction (rotate, scale)</span>
                </label>
              </div>
              <div class="feature-item">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.features.addToCart">
                  <span>Add to Cart Functionality</span>
                </label>
              </div>
              <div class="feature-item">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.features.customization">
                  <span>Product Customization</span>
                </label>
              </div>
              <div class="feature-item">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.features.aiAssistant">
                  <span>AI Assistant</span>
                </label>
              </div>
              <div class="feature-item">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.features.multiUser">
                  <span>Multi-User Experience</span>
                </label>
              </div>
              <div class="feature-item">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.features.analytics">
                  <span>User Behavior Analytics</span>
                </label>
              </div>
              <div class="feature-item">
                <label class="checkbox-label">
                  <input type="checkbox" v-model="formData.features.guidedTour">
                  <span>Guided Tour</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Publishing Section -->
        <div v-if="activeSection === 'publishing'" class="config-section">
          <h4>Publishing Options</h4>

          <div class="form-group">
            <label>Access Control</label>
            <div class="radio-group">
              <label class="radio-label">
                <input type="radio" v-model="formData.access" value="public">
                <span>Public (Anyone can access)</span>
              </label>
              <label class="radio-label">
                <input type="radio" v-model="formData.access" value="private">
                <span>Private (Invitation only)</span>
              </label>
              <label class="radio-label">
                <input type="radio" v-model="formData.access" value="password">
                <span>Password Protected</span>
              </label>
            </div>
          </div>

          <div v-if="formData.access === 'password'" class="form-group">
            <label for="access-password">Password</label>
            <input
              id="access-password"
              type="password"
              v-model="formData.password"
              placeholder="Enter access password"
            />
          </div>

          <div class="form-group">
            <label for="publish-date">Publish Date</label>
            <input
              id="publish-date"
              type="datetime-local"
              v-model="formData.publishDate"
            />
          </div>

          <div class="form-group">
            <label for="expiry-date">Expiry Date (Optional)</label>
            <input
              id="expiry-date"
              type="datetime-local"
              v-model="formData.expiryDate"
            />
          </div>

          <div class="form-group">
            <label>Sharing Options</label>
            <div class="checkbox-group">
              <label class="checkbox-label">
                <input type="checkbox" v-model="formData.sharing.website">
                <span>Embed on Website</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" v-model="formData.sharing.social">
                <span>Social Media Sharing</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" v-model="formData.sharing.email">
                <span>Email Invitations</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="configurator-footer">
      <button class="btn btn-secondary" @click="$emit('close')">Cancel</button>
      <div class="action-buttons">
        <button
          v-if="showroom && showroom.status !== 'published'"
          class="btn btn-primary"
          @click="publishShowroom"
        >
          Publish
        </button>
        <button
          v-else-if="showroom && showroom.status === 'published'"
          class="btn btn-warning"
          @click="unpublishShowroom"
        >
          Unpublish
        </button>
        <button class="btn btn-primary" @click="saveShowroom">
          {{ showroom ? 'Update Showroom' : 'Create Showroom' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShowroomConfigurator',

  props: {
    showroom: {
      type: Object,
      default: null
    },
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      activeSection: 'basic',
      thumbnailPreview: null,
      thumbnailFile: null,
      previewImage: null,
      productSelectionMode: 'categories',
      productSearchQuery: '',

      configSections: [
        { id: 'basic', name: 'Basic Information', icon: 'info' },
        { id: 'layout', name: 'Layout', icon: 'dashboard' },
        { id: 'appearance', name: 'Appearance', icon: 'palette' },
        { id: 'products', name: 'Products', icon: 'inventory_2' },
        { id: 'features', name: 'Features', icon: 'widgets' },
        { id: 'publishing', name: 'Publishing', icon: 'publish' }
      ],

      templates: [
        {
          id: 'template_1',
          name: 'Modern Showroom',
          description: 'A clean, modern layout for showcasing products with minimalist design.',
          thumbnail: null
        },
        {
          id: 'template_2',
          name: 'Classic Gallery',
          description: 'Traditional gallery layout with focus on product details and specifications.',
          thumbnail: null
        },
        {
          id: 'template_3',
          name: 'Interactive Experience',
          description: 'Highly interactive showroom with customer engagement features.',
          thumbnail: null
        },
        {
          id: 'template_4',
          name: 'Retail Store',
          description: 'Simulates a physical retail store environment with aisles and sections.',
          thumbnail: null
        }
      ],

      environments: [
        { id: 'neutral', name: 'Neutral Studio', thumbnail: null },
        { id: 'modern', name: 'Modern Interior', thumbnail: null },
        { id: 'classic', name: 'Classic Interior', thumbnail: null },
        { id: 'outdoor', name: 'Outdoor Setting', thumbnail: null },
        { id: 'abstract', name: 'Abstract Space', thumbnail: null }
      ],

      categories: [
        { id: 'furniture', name: 'Furniture' },
        { id: 'lighting', name: 'Lighting' },
        { id: 'decor', name: 'Decor' },
        { id: 'outdoor', name: 'Outdoor' },
        { id: 'office', name: 'Office' }
      ],

      products: [
        { id: 'prod1', name: 'Modern Sofa', category: 'furniture', thumbnail: null },
        { id: 'prod2', name: 'Pendant Light', category: 'lighting', thumbnail: null },
        { id: 'prod3', name: 'Wall Art', category: 'decor', thumbnail: null },
        { id: 'prod4', name: 'Patio Set', category: 'outdoor', thumbnail: null },
        { id: 'prod5', name: 'Office Chair', category: 'office', thumbnail: null }
      ],

      formData: {
        name: '',
        description: '',
        status: 'draft',
        template: 'template_1',
        layoutStyle: 'open',
        layoutSize: 'medium',
        navigation: 'both',
        theme: 'light',
        primaryColor: '#3498db',
        secondaryColor: '#2ecc71',
        accentColor: '#e74c3c',
        brightness: 70,
        contrast: 50,
        environment: 'neutral',
        selectedCategories: [],
        selectedProducts: [],
        features: {
          productInfo: true,
          productInteraction: true,
          addToCart: true,
          customization: false,
          aiAssistant: false,
          multiUser: false,
          analytics: true,
          guidedTour: false
        },
        access: 'public',
        password: '',
        publishDate: '',
        expiryDate: '',
        sharing: {
          website: true,
          social: true,
          email: true
        }
      }
    };
  },

  computed: {
    filteredProducts() {
      if (!this.productSearchQuery) {
        return this.products;
      }

      const query = this.productSearchQuery.toLowerCase();
      return this.products.filter(product =>
        product.name.toLowerCase().includes(query) ||
        this.getCategoryName(product.category).toLowerCase().includes(query)
      );
    }
  },

  mounted() {
    this.initializeForm();
  },

  methods: {
    initializeForm() {
      if (this.showroom) {
        // Populate form with showroom data
        this.formData.name = this.showroom.name;
        this.formData.description = this.showroom.description || '';
        this.formData.status = this.showroom.status;

        // Set thumbnail preview if available
        if (this.showroom.thumbnail) {
          this.thumbnailPreview = this.showroom.thumbnail;
        }

        // Additional properties would be populated here
        // This is a simplified example
      } else {
        // Set default values for new showroom
        const now = new Date();
        this.formData.publishDate = now.toISOString().slice(0, 16);
      }
    },

    triggerFileInput() {
      this.$refs.thumbnailInput.click();
    },

    handleThumbnailUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      this.thumbnailFile = file;

      // Create a preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.thumbnailPreview = e.target.result;
      };
      reader.readAsDataURL(file);
    },

    removeThumbnail() {
      this.thumbnailPreview = null;
      this.thumbnailFile = null;
    },

    selectTemplate(templateId) {
      this.formData.template = templateId;
      this.generatePreview();
    },

    getCategoryName(categoryId) {
      const category = this.categories.find(c => c.id === categoryId);
      return category ? category.name : 'Uncategorized';
    },

    generatePreview() {
      // This would be replaced with actual API call to generate preview
      console.log('Generating preview for configuration:', this.formData);

      // Mock preview generation
      setTimeout(() => {
        // In a real implementation, this would be a URL to a generated preview image
        this.previewImage = 'https://via.placeholder.com/300x200?text=Showroom+Preview';
      }, 1000);
    },

    publishShowroom() {
      this.formData.status = 'published';
      this.saveShowroom();
    },

    unpublishShowroom() {
      this.formData.status = 'draft';
      this.saveShowroom();
    },

    saveShowroom() {
      // This would be replaced with actual API call
      console.log('Saving showroom configuration:', this.formData);

      // Emit event with form data
      this.$emit('save', {
        ...this.formData,
        thumbnail: this.thumbnailPreview,
        thumbnailFile: this.thumbnailFile
      });
    }
  }
};
</script>

<style scoped>
.showroom-configurator {
  display: flex;
  flex-direction: column;
  height: 100%;
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.configurator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--theme--border-color);
}

.configurator-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.configurator-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.configurator-sidebar {
  width: 280px;
  border-right: 1px solid var(--theme--border-color);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-section {
  padding: 15px;
  border-bottom: 1px solid var(--theme--border-color);
}

.sidebar-section:last-child {
  border-bottom: none;
}

.sidebar-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
  text-transform: uppercase;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  display: flex;
  align-items: center;
  padding: 10px;
  margin-bottom: 5px;
  border-radius: var(--theme--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
}

.sidebar-nav li:hover {
  background-color: var(--theme--background-subdued);
}

.sidebar-nav li.active {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
}

.sidebar-nav li i {
  margin-right: 10px;
  font-size: 20px;
}

.preview-container {
  margin-bottom: 15px;
}

.preview-image {
  width: 100%;
  height: 150px;
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background-subdued);
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px;
  text-align: center;
}

.preview-placeholder i {
  font-size: 32px;
  margin-bottom: 10px;
  color: var(--theme--foreground-subdued);
}

.preview-placeholder span {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.preview-actions {
  display: flex;
  justify-content: center;
}

.configurator-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 30px;
}

.config-section h4 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--theme--foreground);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="datetime-local"],
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.thumbnail-upload {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.thumbnail-preview {
  width: 200px;
  height: 120px;
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background-subdued);
  cursor: pointer;
  margin-bottom: 10px;
}

.thumbnail-preview.has-thumbnail {
  border: 2px solid var(--theme--primary);
}

.thumbnail-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.thumbnail-placeholder i {
  font-size: 32px;
  margin-bottom: 5px;
  color: var(--theme--foreground-subdued);
}

.thumbnail-placeholder span {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.template-card {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.active {
  border-color: var(--theme--primary);
  box-shadow: 0 0 0 2px var(--theme--primary-background);
}

.template-thumbnail {
  height: 120px;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-placeholder i {
  font-size: 32px;
  color: var(--theme--foreground-subdued);
}

.template-info {
  padding: 10px;
}

.template-info h5 {
  margin: 0 0 5px 0;
  font-size: 14px;
  font-weight: 600;
}

.template-info p {
  margin: 0;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  line-height: 1.4;
}

.radio-group,
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.radio-label,
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-label input,
.checkbox-label input {
  margin-right: 10px;
}

.color-picker-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.color-picker {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.color-picker label {
  font-size: 12px;
  margin-bottom: 5px;
}

.color-picker input[type="color"] {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  padding: 0;
  overflow: hidden;
}

.slider-group {
  margin-bottom: 15px;
}

.slider-group label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.slider-group input[type="range"] {
  width: 100%;
  margin-bottom: 5px;
}

.slider-value {
  text-align: right;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.environment-options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
}

.environment-option {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.environment-option:hover {
  transform: translateY(-2px);
}

.environment-option.active {
  border-color: var(--theme--primary);
  box-shadow: 0 0 0 2px var(--theme--primary-background);
}

.environment-thumbnail {
  height: 70px;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.environment-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.environment-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.environment-placeholder i {
  font-size: 24px;
  color: var(--theme--foreground-subdued);
}

.environment-name {
  padding: 5px;
  text-align: center;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.selection-tabs {
  display: flex;
  border-bottom: 1px solid var(--theme--border-color);
  margin-bottom: 15px;
}

.selection-tab {
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  border-bottom: 2px solid transparent;
}

.selection-tab:hover {
  color: var(--theme--primary);
}

.selection-tab.active {
  color: var(--theme--primary);
  border-bottom-color: var(--theme--primary);
}

.category-selection,
.product-selection {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 10px;
}

.category-item,
.product-item {
  padding: 8px;
  border-bottom: 1px solid var(--theme--border-color-subdued);
}

.category-item:last-child,
.product-item:last-child {
  border-bottom: none;
}

.search-products {
  position: relative;
  margin-bottom: 10px;
}

.search-products input {
  width: 100%;
  padding: 8px 12px 8px 35px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
}

.search-products i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
}

.product-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-thumbnail {
  width: 40px;
  height: 40px;
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-placeholder i {
  font-size: 20px;
  color: var(--theme--foreground-subdued);
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.product-category {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 10px;
}

.feature-item {
  padding: 10px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
}

.configurator-footer {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  border-top: 1px solid var(--theme--border-color);
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--background-accent);
}

.btn-warning {
  background-color: var(--theme--warning);
  color: white;
}

.btn-warning:hover {
  background-color: var(--theme--warning-accent);
}

.btn-icon {
  padding: 6px;
  border-radius: var(--theme--border-radius);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-icon i {
  font-size: 20px;
  margin-right: 0;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-text {
  background: none;
  border: none;
  color: var(--theme--primary);
  cursor: pointer;
  padding: 0;
  font-size: 14px;
}

.btn-text:hover {
  text-decoration: underline;
}
</style>
