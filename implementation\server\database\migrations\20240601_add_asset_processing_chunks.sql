-- Add asset processing chunks table
CREATE TABLE IF NOT EXISTS asset_processing_chunks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_id UUID NOT NULL REFERENCES asset_processing_jobs(id) ON DELETE CASCADE,
  chunk_index INTEGER NOT NULL,
  total_chunks INTEGER NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  progress INTEGER NOT NULL DEFAULT 0,
  error_message TEXT,
  result JSONB,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  UNIQUE(job_id, chunk_index)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS asset_processing_chunks_job_id_idx ON asset_processing_chunks(job_id);
CREATE INDEX IF NOT EXISTS asset_processing_chunks_status_idx ON asset_processing_chunks(status);

-- Add new columns to asset_processing_jobs table
ALTER TABLE asset_processing_jobs ADD COLUMN IF NOT EXISTS chunked BOOLEAN DEFAULT FALSE;
ALTER TABLE asset_processing_jobs ADD COLUMN IF NOT EXISTS parallel_processing BOOLEAN DEFAULT FALSE;
ALTER TABLE asset_processing_jobs ADD COLUMN IF NOT EXISTS chunk_size INTEGER;
ALTER TABLE asset_processing_jobs ADD COLUMN IF NOT EXISTS max_parallel_chunks INTEGER;

-- Add RLS policies
ALTER TABLE asset_processing_chunks ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to view their own chunks
CREATE POLICY asset_processing_chunks_select_policy ON asset_processing_chunks
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM asset_processing_jobs j
      JOIN assets a ON j.asset_id = a.id
      WHERE j.id = asset_processing_chunks.job_id
      AND (
        a.vendor_id IN (
          SELECT vendor_id FROM vendor_members
          WHERE user_id = auth.uid()
        )
        OR auth.role() = 'admin'
      )
    )
  );

-- Allow service role to manage chunks
CREATE POLICY asset_processing_chunks_all_policy ON asset_processing_chunks
  FOR ALL
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Add function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_asset_processing_chunks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_asset_processing_chunks_updated_at
BEFORE UPDATE ON asset_processing_chunks
FOR EACH ROW
EXECUTE FUNCTION update_asset_processing_chunks_updated_at();
