-- Migration: Enhance Asset Versioning
-- Description: This migration adds tables and functions for enhanced asset versioning and diff tracking

-- Create asset_diffs table
CREATE TABLE IF NOT EXISTS asset_diffs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  asset_id UUID NOT NULL,
  base_version TEXT NOT NULL,
  target_version TEXT NOT NULL,
  method TEXT NOT NULL DEFAULT 'binary',
  changes JSONB NOT NULL DEFAULT '{}',
  diff_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_asset_diffs_asset
    FOREIGN KEY (asset_id)
    REFERENCES assets(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_asset_diff
    UNIQUE (asset_id, base_version, target_version)
);

-- Create index on asset_diffs
CREATE INDEX idx_asset_diffs_asset_id ON asset_diffs(asset_id);
CREATE INDEX idx_asset_diffs_versions ON asset_diffs(asset_id, base_version, target_version);

-- Add columns to asset_versions table
ALTER TABLE asset_versions
  ADD COLUMN IF NOT EXISTS diff_from_previous BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS diff_size INTEGER,
  ADD COLUMN IF NOT EXISTS diff_url TEXT;

-- Create asset_chunks table for chunk-based versioning
CREATE TABLE IF NOT EXISTS asset_chunks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  asset_id UUID NOT NULL,
  version TEXT NOT NULL,
  chunk_index INTEGER NOT NULL,
  chunk_hash TEXT NOT NULL,
  chunk_size INTEGER NOT NULL,
  chunk_url TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_asset_chunks_asset
    FOREIGN KEY (asset_id)
    REFERENCES assets(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_asset_chunk
    UNIQUE (asset_id, version, chunk_index)
);

-- Create index on asset_chunks
CREATE INDEX idx_asset_chunks_asset_id ON asset_chunks(asset_id);
CREATE INDEX idx_asset_chunks_version ON asset_chunks(asset_id, version);

-- Create function to generate asset version hash
CREATE OR REPLACE FUNCTION generate_asset_version_hash(
  p_asset_id UUID,
  p_version TEXT
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
  v_hash TEXT;
BEGIN
  -- Get the hash from the asset version
  SELECT hash INTO v_hash
  FROM asset_versions
  WHERE asset_id = p_asset_id AND version = p_version;
  
  RETURN v_hash;
END;
$$;

-- Create function to get asset version diff info
CREATE OR REPLACE FUNCTION get_asset_version_diff_info(
  p_asset_id UUID,
  p_base_version TEXT,
  p_target_version TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_diff JSONB;
BEGIN
  -- Get the diff info
  SELECT changes INTO v_diff
  FROM asset_diffs
  WHERE asset_id = p_asset_id 
    AND base_version = p_base_version 
    AND target_version = p_target_version;
  
  IF v_diff IS NULL THEN
    RETURN jsonb_build_object(
      'exists', false,
      'error', 'Diff not found'
    );
  END IF;
  
  RETURN jsonb_build_object(
    'exists', true,
    'diff', v_diff
  );
END;
$$;

-- Create function to get asset versions with diffs
CREATE OR REPLACE FUNCTION get_asset_versions_with_diffs(
  p_asset_id UUID
)
RETURNS TABLE (
  id UUID,
  version TEXT,
  hash TEXT,
  url TEXT,
  size INTEGER,
  is_current BOOLEAN,
  metadata JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  has_diff_from_previous BOOLEAN,
  diff_size INTEGER,
  diff_url TEXT,
  previous_version TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  WITH versions AS (
    SELECT 
      v.*,
      LAG(v.version) OVER (ORDER BY v.created_at) AS previous_version
    FROM asset_versions v
    WHERE v.asset_id = p_asset_id
    ORDER BY v.created_at DESC
  )
  SELECT
    v.id,
    v.version,
    v.hash,
    v.url,
    v.size,
    v.is_current,
    v.metadata,
    v.created_at,
    v.updated_at,
    COALESCE(d.id IS NOT NULL, false) AS has_diff_from_previous,
    COALESCE(v.diff_size, 0) AS diff_size,
    v.diff_url,
    v.previous_version
  FROM versions v
  LEFT JOIN asset_diffs d ON 
    d.asset_id = p_asset_id AND 
    d.base_version = v.previous_version AND 
    d.target_version = v.version
  ORDER BY v.created_at DESC;
END;
$$;

-- Add RLS policies for asset_diffs
ALTER TABLE asset_diffs ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see diffs for assets they have access to
CREATE POLICY asset_diffs_select_policy
  ON asset_diffs
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM assets a
      WHERE a.id = asset_id AND (
        a.vendor_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM vendors v
          WHERE v.id = a.vendor_id AND v.user_id = auth.uid()
        ) OR
        auth.jwt() ->> 'role' = 'admin'
      )
    )
  );

-- Add RLS policies for asset_chunks
ALTER TABLE asset_chunks ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see chunks for assets they have access to
CREATE POLICY asset_chunks_select_policy
  ON asset_chunks
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM assets a
      WHERE a.id = asset_id AND (
        a.vendor_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM vendors v
          WHERE v.id = a.vendor_id AND v.user_id = auth.uid()
        ) OR
        auth.jwt() ->> 'role' = 'admin'
      )
    )
  );
