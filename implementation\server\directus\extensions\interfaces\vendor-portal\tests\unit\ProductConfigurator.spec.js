import { describe, it, expect, beforeEach, vi } from 'vitest';
import { shallowMount } from '@vue/test-utils';

// Mock the ProductConfigurator component
const ProductConfiguratorMock = {
  name: 'ProductConfigurator',
  template: '<div></div>', // Minimal template to avoid errors
  data() {
    return {
      products: [],
      categories: [],
      filteredProducts: [],
      searchQuery: '',
      selectedProductId: null,
      selectedProduct: null,
      configuration: {
        productId: null,
        optionGroups: [],
      },
      originalConfiguration: null,
      selectedOptions: {},
      optionCategories: [
        { id: 'appearance', name: 'Appearance' },
        { id: 'functionality', name: 'Functionality' },
        { id: 'accessories', name: 'Accessories' },
        { id: 'customization', name: 'Customization' },
      ],
      dependencies: [],
      compatibilityRules: [],
      configRules: [],
      showDependencyModal: false,
      showIncompatibilityModal: false,
      currentSourceGroup: null,
      currentSourceOption: null,
      currentTargetGroup: null,
      currentTargetOption: null,
    };
  },
  computed: {
    totalOptionsPrice() {
      let total = 0;

      Object.entries(this.selectedOptions).forEach(([groupIndex, optionIndexOrArray]) => {
        const group = this.configuration.optionGroups[groupIndex];
        if (!group) return;

        if (Array.isArray(optionIndexOrArray)) {
          // Handle multi-select option groups
          optionIndexOrArray.forEach(optionIndex => {
            if (group.options[optionIndex]) {
              total += parseFloat(group.options[optionIndex].price || 0);
            }
          });
        } else {
          // Handle single-select option groups
          const optionIndex = optionIndexOrArray;
          if (group.options[optionIndex]) {
            total += parseFloat(group.options[optionIndex].price || 0);
          }
        }
      });

      return total;
    },
    totalPrice() {
      const basePrice = this.selectedProduct ? parseFloat(this.selectedProduct.price || 0) : 0;
      return basePrice + this.totalOptionsPrice;
    },
    hasChanges() {
      return (
        this.originalConfiguration &&
        JSON.stringify(this.configuration) !== JSON.stringify(this.originalConfiguration)
      );
    },
  },
  methods: {
    async loadProducts() {
      this.products = await this.apiService.getProducts();
      this.filteredProducts = [...this.products];
    },
    filterProducts() {
      if (!this.searchQuery) {
        this.filteredProducts = [...this.products];
        return;
      }

      const query = this.searchQuery.toLowerCase();
      this.filteredProducts = this.products.filter(
        product =>
          product.name.toLowerCase().includes(query) ||
          product.category.toLowerCase().includes(query),
      );
    },
    selectProduct(product) {
      if (typeof product === 'number') {
        this.selectedProductId = product;
        this.selectedProduct = this.products.find(p => p.id === product);
      } else {
        this.selectedProductId = product.id;
        this.selectedProduct = product;
      }

      this.loadProductConfiguration();
    },
    async loadProductConfiguration() {
      if (!this.selectedProductId) return;

      this.configuration = await this.apiService.getProductConfiguration(this.selectedProductId);
      this.originalConfiguration = JSON.parse(JSON.stringify(this.configuration));
      this.selectedOptions = {};
    },
    async saveConfiguration() {
      if (!this.selectedProductId) return;

      await this.apiService.saveProductConfiguration(this.configuration);
      this.originalConfiguration = JSON.parse(JSON.stringify(this.configuration));
    },
    addOptionGroup() {
      this.configuration.optionGroups.push({
        name: '',
        category: this.optionCategories[0].id,
        options: [],
        required: false,
        multiSelect: false,
        description: '',
      });
    },
    removeOptionGroup(groupIndex) {
      this.configuration.optionGroups.splice(groupIndex, 1);
    },
    addOption(groupIndex) {
      this.configuration.optionGroups[groupIndex].options.push({
        name: '',
        price: 0,
        sku: '',
        description: '',
        image: null,
        dependencies: [],
        incompatibilities: [],
      });
    },
    removeOption(groupIndex, optionIndex) {
      this.configuration.optionGroups[groupIndex].options.splice(optionIndex, 1);
    },
    selectOption(groupIndex, optionIndex) {
      // Check if this is a deselection
      if (this.selectedOptions[groupIndex] === optionIndex) {
        // Check if this option is required by any other selected option
        for (const [selectedGroupIndex, selectedOptionIndex] of Object.entries(
          this.selectedOptions,
        )) {
          // Skip the current option
          if (parseInt(selectedGroupIndex) === groupIndex) continue;

          const selectedOption =
            this.configuration.optionGroups[selectedGroupIndex].options[selectedOptionIndex];

          // Check if the selected option depends on the option we're trying to deselect
          if (
            selectedOption.dependencies.some(
              dep => dep.groupIndex === groupIndex && dep.optionIndex === optionIndex,
            )
          ) {
            // Can't deselect because another option depends on it
            return;
          }
        }

        // If we get here, it's safe to deselect
        delete this.selectedOptions[groupIndex];
      } else {
        // Check if selecting this option would violate any incompatibilities
        if (!this.canSelectOption(groupIndex, optionIndex)) {
          return;
        }

        // If the option group doesn't allow multiple selections, deselect any existing selection
        if (!this.configuration.optionGroups[groupIndex].multiSelect) {
          this.selectedOptions[groupIndex] = optionIndex;
        } else {
          // For multi-select groups, we need to handle arrays of selected options
          if (!Array.isArray(this.selectedOptions[groupIndex])) {
            this.selectedOptions[groupIndex] = [optionIndex];
          } else if (!this.selectedOptions[groupIndex].includes(optionIndex)) {
            this.selectedOptions[groupIndex].push(optionIndex);
          }
        }

        // Auto-select dependencies if needed
        const option = this.configuration.optionGroups[groupIndex].options[optionIndex];
        for (const dependency of option.dependencies) {
          // Only auto-select if not already selected
          if (this.selectedOptions[dependency.groupIndex] !== dependency.optionIndex) {
            this.selectOption(dependency.groupIndex, dependency.optionIndex);
          }
        }
      }
    },
    isOptionSelected(groupIndex, optionIndex) {
      if (!this.selectedOptions[groupIndex]) return false;

      if (Array.isArray(this.selectedOptions[groupIndex])) {
        return this.selectedOptions[groupIndex].includes(optionIndex);
      } else {
        return this.selectedOptions[groupIndex] === optionIndex;
      }
    },
    isOptionDisabled(groupIndex, optionIndex) {
      return !this.canSelectOption(groupIndex, optionIndex);
    },
    canSelectOption(groupIndex, optionIndex) {
      // Check if selecting this option would violate any incompatibilities
      for (const [selectedGroupIndex, selectedOptionIndex] of Object.entries(
        this.selectedOptions,
      )) {
        const selectedOption =
          this.configuration.optionGroups[selectedGroupIndex].options[selectedOptionIndex];

        // Check if the selected option has an incompatibility with the option we want to select
        if (
          selectedOption.incompatibilities.some(
            inc => inc.groupIndex === groupIndex && inc.optionIndex === optionIndex,
          )
        ) {
          return false;
        }

        // Check if the option we want to select has an incompatibility with the selected option
        const targetOption = this.configuration.optionGroups[groupIndex].options[optionIndex];
        if (
          targetOption.incompatibilities.some(
            inc =>
              inc.groupIndex === parseInt(selectedGroupIndex) &&
              inc.optionIndex === selectedOptionIndex,
          )
        ) {
          return false;
        }
      }

      return true;
    },
    addDependency(sourceGroupIndex, sourceOptionIndex, targetGroupIndex, targetOptionIndex) {
      const sourceOption =
        this.configuration.optionGroups[sourceGroupIndex].options[sourceOptionIndex];

      // Create dependency object
      const dependency = {
        groupIndex: targetGroupIndex,
        optionIndex: targetOptionIndex,
      };

      // Add dependency if it doesn't already exist
      if (
        !sourceOption.dependencies.some(
          dep =>
            dep.groupIndex === dependency.groupIndex && dep.optionIndex === dependency.optionIndex,
        )
      ) {
        sourceOption.dependencies.push(dependency);
      }
    },
    removeDependency(sourceGroupIndex, sourceOptionIndex, dependencyIndex) {
      const sourceOption =
        this.configuration.optionGroups[sourceGroupIndex].options[sourceOptionIndex];
      sourceOption.dependencies.splice(dependencyIndex, 1);
    },
    addIncompatibility(sourceGroupIndex, sourceOptionIndex, targetGroupIndex, targetOptionIndex) {
      const sourceOption =
        this.configuration.optionGroups[sourceGroupIndex].options[sourceOptionIndex];

      // Create incompatibility object
      const incompatibility = {
        groupIndex: targetGroupIndex,
        optionIndex: targetOptionIndex,
      };

      // Add incompatibility if it doesn't already exist
      if (
        !sourceOption.incompatibilities.some(
          inc =>
            inc.groupIndex === incompatibility.groupIndex &&
            inc.optionIndex === incompatibility.optionIndex,
        )
      ) {
        sourceOption.incompatibilities.push(incompatibility);
      }
    },
    removeIncompatibility(sourceGroupIndex, sourceOptionIndex, incompatibilityIndex) {
      const sourceOption =
        this.configuration.optionGroups[sourceGroupIndex].options[sourceOptionIndex];
      sourceOption.incompatibilities.splice(incompatibilityIndex, 1);
    },
    resetConfiguration() {
      if (this.originalConfiguration) {
        this.configuration = JSON.parse(JSON.stringify(this.originalConfiguration));
      }
    },
  },
};

// Mock data
const mockProducts = [
  { id: 1, name: 'Test Product 1', category: 'Furniture' },
  { id: 2, name: 'Test Product 2', category: 'Lighting' },
];

const mockConfiguration = {
  productId: 1,
  optionGroups: [
    {
      name: 'Color',
      category: 'appearance',
      options: [
        { name: 'Red', price: 10, sku: 'RED-001', dependencies: [], incompatibilities: [] },
        { name: 'Blue', price: 15, sku: 'BLUE-001', dependencies: [], incompatibilities: [] },
      ],
      required: true,
      multiSelect: false,
    },
    {
      name: 'Material',
      category: 'appearance',
      options: [
        { name: 'Wood', price: 20, sku: 'WOOD-001', dependencies: [], incompatibilities: [] },
        { name: 'Metal', price: 30, sku: 'METAL-001', dependencies: [], incompatibilities: [] },
      ],
      required: false,
      multiSelect: false,
    },
    {
      name: 'Accessories',
      category: 'accessories',
      options: [
        { name: 'Cushion', price: 5, sku: 'CUSH-001', dependencies: [], incompatibilities: [] },
        { name: 'Cover', price: 8, sku: 'COVER-001', dependencies: [], incompatibilities: [] },
      ],
      required: false,
      multiSelect: true,
    },
  ],
};

// Mock API service
const mockApiService = {
  getProducts: vi.fn().mockResolvedValue(mockProducts),
  getProductConfiguration: vi.fn().mockResolvedValue(mockConfiguration),
  saveProductConfiguration: vi.fn().mockResolvedValue({ success: true }),
};

describe('ProductConfigurator', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = shallowMount(ProductConfiguratorMock, {
      mocks: {
        apiService: mockApiService,
      },
    });

    // Initialize with mock data
    wrapper.vm.products = [...mockProducts];
    wrapper.vm.filteredProducts = [...mockProducts];
    wrapper.vm.configuration = JSON.parse(JSON.stringify(mockConfiguration));
    wrapper.vm.selectedProductId = 1;
    wrapper.vm.selectedProduct = mockProducts[0];
  });

  it('calculates total price correctly', () => {
    // Select options
    wrapper.vm.selectedOptions = {};
    wrapper.vm.selectOption(0, 0); // Red: $10
    wrapper.vm.selectOption(1, 1); // Metal: $30

    expect(wrapper.vm.totalOptionsPrice).toBe(40);
  });

  it('handles multi-select option groups correctly', () => {
    // Set up multi-select group
    wrapper.vm.configuration.optionGroups[2].multiSelect = true;
    wrapper.vm.selectedOptions = {};

    // Select multiple options in a multi-select group
    wrapper.vm.selectOption(2, 0); // Cushion: $5
    wrapper.vm.selectOption(2, 1); // Cover: $8

    // Check if both options are selected
    expect(wrapper.vm.isOptionSelected(2, 0)).toBe(true);
    expect(wrapper.vm.isOptionSelected(2, 1)).toBe(true);

    // Check total price
    expect(wrapper.vm.totalOptionsPrice).toBe(13);
  });

  it('handles option dependencies correctly', () => {
    // Add a dependency: Metal requires Red
    const metalOption = wrapper.vm.configuration.optionGroups[1].options[1];
    metalOption.dependencies = [{ groupIndex: 0, optionIndex: 0 }];

    wrapper.vm.selectedOptions = {};

    // Select Metal
    wrapper.vm.selectOption(1, 1);

    // Red should be automatically selected due to dependency
    expect(wrapper.vm.isOptionSelected(0, 0)).toBe(true);

    // Total price should include both Metal and Red
    expect(wrapper.vm.totalOptionsPrice).toBe(40);
  });

  it('handles option incompatibilities correctly', () => {
    // Add an incompatibility: Wood is incompatible with Blue
    const woodOption = wrapper.vm.configuration.optionGroups[1].options[0];
    woodOption.incompatibilities = [{ groupIndex: 0, optionIndex: 1 }];

    wrapper.vm.selectedOptions = {};

    // Select Wood
    wrapper.vm.selectOption(1, 0);

    // Blue should be disabled
    expect(wrapper.vm.isOptionDisabled(0, 1)).toBe(true);

    // Try to select Blue (should not work)
    wrapper.vm.selectOption(0, 1);

    // Blue should not be selected
    expect(wrapper.vm.isOptionSelected(0, 1)).toBe(false);
  });

  it('saves configuration correctly', async () => {
    wrapper.vm.selectedOptions = {};

    // Make some selections
    wrapper.vm.selectOption(0, 0);
    wrapper.vm.selectOption(1, 0);

    // Save configuration
    await wrapper.vm.saveConfiguration();

    expect(mockApiService.saveProductConfiguration).toHaveBeenCalledWith(
      expect.objectContaining({
        productId: 1,
        optionGroups: expect.any(Array),
      }),
    );
  });

  it('adds and removes option groups correctly', () => {
    const initialGroupCount = wrapper.vm.configuration.optionGroups.length;

    // Add a new option group
    wrapper.vm.addOptionGroup();

    expect(wrapper.vm.configuration.optionGroups.length).toBe(initialGroupCount + 1);

    // Remove the last option group
    wrapper.vm.removeOptionGroup(wrapper.vm.configuration.optionGroups.length - 1);

    expect(wrapper.vm.configuration.optionGroups.length).toBe(initialGroupCount);
  });

  it('adds and removes options correctly', () => {
    const initialOptionCount = wrapper.vm.configuration.optionGroups[0].options.length;

    // Add a new option
    wrapper.vm.addOption(0);

    expect(wrapper.vm.configuration.optionGroups[0].options.length).toBe(initialOptionCount + 1);

    // Remove the last option
    wrapper.vm.removeOption(0, wrapper.vm.configuration.optionGroups[0].options.length - 1);

    expect(wrapper.vm.configuration.optionGroups[0].options.length).toBe(initialOptionCount);
  });
});
