import React, { useRef, useState } from 'react';
import Editor, { Monaco } from '@monaco-editor/react';
import { Box, CircularProgress } from '@mui/material';
import { editor } from 'monaco-editor';

interface MonacoJsonEditorProps {
  value: any;
  onChange: (value: any) => void;
  height?: string | number;
  width?: string | number;
  readOnly?: boolean;
  options?: editor.IStandaloneEditorConstructionOptions;
}

export const MonacoJsonEditor: React.FC<MonacoJsonEditorProps> = ({
  value,
  onChange,
  height = '500px',
  width = '100%',
  readOnly = false,
  options = {},
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);
  const [isEditorReady, setIsEditorReady] = useState(false);

  // Format the JSON value as a string
  const jsonString = typeof value === 'string' ? value : JSON.stringify(value, null, 2);

  // Handle editor mount
  const handleEditorDidMount = (editor: editor.IStandaloneCodeEditor, monaco: Monaco) => {
    editorRef.current = editor;
    setIsEditorReady(true);

    // Set up JSON schema validation
    monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
      validate: true,
      allowComments: false,
      schemas: [],
    });
  };

  // Handle editor change
  const handleEditorChange = (value: string | undefined) => {
    if (!value) return;

    try {
      const parsedValue = JSON.parse(value);
      onChange(parsedValue);
    } catch (error) {
      // Don't update if JSON is invalid
      console.error('Invalid JSON:', error);
    }
  };

  // Default editor options
  const defaultOptions: editor.IStandaloneEditorConstructionOptions = {
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    lineNumbers: 'on',
    glyphMargin: false,
    folding: true,
    lineDecorationsWidth: 10,
    automaticLayout: true,
    readOnly,
    formatOnPaste: true,
    formatOnType: true,
  };

  return (
    <Box sx={{ width, height, position: 'relative' }}>
      {!isEditorReady && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            zIndex: 1,
          }}
        >
          <CircularProgress />
        </Box>
      )}
      <Editor
        height={height}
        width={width}
        language="json"
        value={jsonString}
        options={{ ...defaultOptions, ...options }}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
      />
    </Box>
  );
};

export default MonacoJsonEditor;
