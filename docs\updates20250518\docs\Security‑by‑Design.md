# Security‑by‑Design Integration Guide

## 1  Threat Modeling & Risk Assessment

* **Action 1.1** Run STRIDE analysis on every new feature.
* **Action 1.2** Maintain a living risk register in the repo (Markdown + severity labels).

## 2  Zero‑Trust Architecture

1. **Short‑lived JWTs** issued by Auth Service (15 min expiry).
2. **mTLS** for internal microservices (Envoy sidecars).
3. **Scope** tokens to **vendorId** and **role**; deny all by default.

## 3  Data Protection

| Layer      | Measure                 | Tooling              |
| ---------- | ----------------------- | -------------------- |
| At Rest    | AES‑256, per‑bundle key | AWS KMS + S3 SSE‑KMS |
| In Transit | TLS 1.3                 | CloudFront + ALB     |

## 4  CI/CD Security Gates

* Static analysis (Semgrep) on every PR.
* IaC scans (tfsec) for Terraform modules.
* Container images signed (cosign) and scanned (Trivy).

## 5  Compliance Controls

* **GDPR:** implement `/user/{id}/forget` endpoint.
* **ISO 27001:** map controls to Annex A spreadsheet; owner = CISO.
* **OWASP ASVS v4‑L2:** grep checklist enforced at release.

## 6  Monitoring & Incident Response

* Centralized logs → SIEM (Splunk) with anomaly alerts.
* PagerDuty escalation matrix.
* Run quarterly red‑team drills & tabletop exercises.

## 7  Admin Workflows

* Fine‑grained IAM roles: `sys_admin`, `content_admin`, `support_read`.
* Mandatory MFA (FIDO2) for admin portal.

## 8  Deliverables & KPIs

* **D1** Security Architecture Doc ✅
* **D2** Pentest Report ✅ (quarterly)
* **KPI:** Mean Time to Detect < 5 min; MTTR < 30 min.
