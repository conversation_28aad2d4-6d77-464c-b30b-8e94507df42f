import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { AssetService } from '../../services/asset-service';
import { AssetBundleCreationRequest } from '../../shared/models/asset-management';

// Define the query parameters schema for GET
const GetQueryParamsSchema = z.object({
  vendor_id: z.string().uuid().optional(),
  limit: z.string().transform(Number).optional(),
  offset: z.string().transform(Number).optional(),
});

// Define the request body schema for POST
const PostBodySchema = z.object({
  vendor_id: z.string().uuid(),
  name: z.string(),
  description: z.string().optional(),
  asset_ids: z.array(z.string().uuid()),
});

/**
 * Asset Bundles API endpoint
 *
 * This endpoint handles listing and creating asset bundles.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create asset service
    const assetService = new AssetService(supabase);

    // Handle GET request (list bundles)
    if (req.method === 'GET') {
      // Validate query parameters
      const queryResult = GetQueryParamsSchema.safeParse(req.query);
      if (!queryResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
      }

      // Extract parameters
      const { vendor_id, limit = 100, offset = 0 } = queryResult.data;

      // Log the request
      logger.info('Asset bundles request', {
        vendor_id,
        limit,
        offset,
      });

      // Build query
      let query = supabase.from('asset_bundles').select('*');

      if (vendor_id) {
        query = query.eq('vendor_id', vendor_id);
      }

      // Add pagination
      query = query.range(offset, offset + limit - 1);

      // Execute query
      const { data: bundles, error, count } = await query;

      if (error) {
        logger.error('Error fetching asset bundles', { error });
        return errorHandler(error, res);
      }

      // Get total count
      const { count: totalCount, error: countError } = await supabase
        .from('asset_bundles')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        logger.error('Error getting total count', { error: countError });
        // Continue anyway, just don't return total count
      }

      // Return bundles
      return res.status(200).json({
        bundles,
        pagination: {
          limit,
          offset,
          total: totalCount,
        },
      });
    }

    // Handle POST request (create bundle)
    if (req.method === 'POST') {
      // Validate request body
      const bodyResult = PostBodySchema.safeParse(req.body);
      if (!bodyResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid request body', details: bodyResult.error.format() });
      }

      // Extract request data
      const bundleRequest: AssetBundleCreationRequest = bodyResult.data;

      // Log the request
      logger.info('Create asset bundle request', {
        vendor_id: bundleRequest.vendor_id,
        name: bundleRequest.name,
        asset_count: bundleRequest.asset_ids.length,
      });

      // Check if user has permission to create bundles for this vendor
      // In a real implementation, we would check if the user is an admin or belongs to the vendor
      // For now, we'll just check if the user is authenticated

      // Create bundle
      const bundle = await assetService.createAssetBundle(bundleRequest);
      if (!bundle) {
        return res.status(500).json({ error: 'Failed to create asset bundle' });
      }

      // Return created bundle
      return res.status(201).json({ bundle });
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Unexpected error in asset bundles endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
