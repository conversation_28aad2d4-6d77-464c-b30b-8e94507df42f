/**
 * Alerts Monitoring API
 *
 * This module provides endpoints for monitoring system alerts.
 */

import { Router, Request, Response } from 'express';
import { rateLimiter } from '../../middleware/rate-limiter';
import { logger } from '../../lib/logger';
import { supabase } from '../../lib/supabase';

const router = Router();

/**
 * Get alerts with filtering
 *
 * @param req - Request
 * @param res - Response
 */
export const getAlerts = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      severity = 'all',
      status = 'all',
      start_time,
      end_time,
      limit = 100,
      offset = 0,
    } = req.query;

    // Get alerts with filtering
    const alerts = await fetchAlerts({
      severity: severity as string,
      status: status as string,
      startTime: start_time as string,
      endTime: end_time as string,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
    });

    res.status(200).json({
      success: true,
      data: alerts,
    });
  } catch (error) {
    logger.error('Error getting alerts:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting alerts',
      },
    });
  }
};

/**
 * Get alert statistics
 *
 * @param req - Request
 * @param res - Response
 */
export const getAlertStatistics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { period = '24h' } = req.query;

    // Get alert statistics
    const alertStats = await fetchAlertStats(period as string);

    res.status(200).json({
      success: true,
      data: alertStats,
    });
  } catch (error) {
    logger.error('Error getting alert statistics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting alert statistics',
      },
    });
  }
};

/**
 * Update alert status
 *
 * @param req - Request
 * @param res - Response
 */
export const updateAlertStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { alert_id } = req.params;
    const { status, comment } = req.body;

    // Validate input
    if (!alert_id || !status) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_INPUT',
          message: 'Alert ID and status are required',
        },
      });
      return;
    }

    // Update alert status
    const updatedAlert = await updateAlert(alert_id, status, comment);

    res.status(200).json({
      success: true,
      data: updatedAlert,
    });
  } catch (error) {
    logger.error('Error updating alert status:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error updating alert status',
      },
    });
  }
};

/**
 * Fetch alerts with filtering
 *
 * @param options - Filter options
 * @returns Filtered alerts
 */
async function fetchAlerts(options: {
  severity: string;
  status: string;
  startTime?: string;
  endTime?: string;
  limit: number;
  offset: number;
}): Promise<any> {
  // In a real implementation, this would query an alerts database
  // For now, we'll return mock data

  // Generate mock alerts
  const mockAlerts = generateMockAlerts(options);

  return {
    alerts: mockAlerts,
    total: 87,
    limit: options.limit,
    offset: options.offset,
  };
}

/**
 * Fetch alert statistics
 *
 * @param period - Time period
 * @returns Alert statistics
 */
async function fetchAlertStats(period: string): Promise<any> {
  // In a real implementation, this would query an alerts database
  // For now, we'll return mock data

  // Calculate start time based on period
  const now = new Date();
  let startTime: Date;

  switch (period) {
    case '1h':
      startTime = new Date(now.getTime() - 60 * 60 * 1000);
      break;
    case '6h':
      startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
      break;
    case '24h':
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  // Generate time points for the chart
  const timePoints = generateTimePoints(startTime, now, period);

  // Generate mock data for alert severities
  const alertSeverityData = {
    critical: {
      name: 'Critical',
      color: '#F44336',
      data: timePoints.map(point => ({
        time: point,
        value: Math.floor(Math.random() * 3), // 0-2 alerts
      })),
    },
    warning: {
      name: 'Warning',
      color: '#FFC107',
      data: timePoints.map(point => ({
        time: point,
        value: Math.floor(Math.random() * 5) + 1, // 1-5 alerts
      })),
    },
    info: {
      name: 'Info',
      color: '#2196F3',
      data: timePoints.map(point => ({
        time: point,
        value: Math.floor(Math.random() * 10) + 2, // 2-11 alerts
      })),
    },
  };

  // Calculate totals
  const totalCritical = alertSeverityData.critical.data.reduce(
    (sum, point) => sum + point.value,
    0,
  );
  const totalWarning = alertSeverityData.warning.data.reduce((sum, point) => sum + point.value, 0);
  const totalInfo = alertSeverityData.info.data.reduce((sum, point) => sum + point.value, 0);
  const totalAlerts = totalCritical + totalWarning + totalInfo;

  // Generate mock data for alert categories
  const alertCategories = [
    { category: 'System', count: Math.floor(Math.random() * 20) + 10 }, // 10-30 alerts
    { category: 'Database', count: Math.floor(Math.random() * 15) + 5 }, // 5-20 alerts
    { category: 'API', count: Math.floor(Math.random() * 25) + 15 }, // 15-40 alerts
    { category: 'Security', count: Math.floor(Math.random() * 10) + 5 }, // 5-15 alerts
    { category: 'Network', count: Math.floor(Math.random() * 12) + 8 }, // 8-20 alerts
  ];

  // Calculate total category alerts
  const totalCategoryAlerts = alertCategories.reduce((sum, category) => sum + category.count, 0);

  // Calculate percentages
  alertCategories.forEach(category => {
    category['percentage'] = ((category.count / totalCategoryAlerts) * 100).toFixed(2);
  });

  return {
    total_alerts: totalAlerts,
    alert_severities: {
      critical: totalCritical,
      warning: totalWarning,
      info: totalInfo,
    },
    alert_severity_trends: alertSeverityData,
    alert_categories: alertCategories,
    period: period,
    start_time: startTime.toISOString(),
    end_time: now.toISOString(),
  };
}

/**
 * Update alert status
 *
 * @param alertId - Alert ID
 * @param status - New status
 * @param comment - Optional comment
 * @returns Updated alert
 */
async function updateAlert(alertId: string, status: string, comment?: string): Promise<any> {
  // In a real implementation, this would update an alert in the database
  // For now, we'll return mock data

  return {
    id: alertId,
    status: status,
    updated_at: new Date().toISOString(),
    updated_by: 'admin',
    comment: comment || null,
  };
}

/**
 * Generate mock alerts
 *
 * @param options - Filter options
 * @returns Mock alerts
 */
function generateMockAlerts(options: {
  severity: string;
  status: string;
  startTime?: string;
  endTime?: string;
  limit: number;
  offset: number;
}): any[] {
  const alerts = [];
  const now = new Date();
  const severities = ['critical', 'warning', 'info'];
  const statuses = ['active', 'acknowledged', 'resolved'];

  // Filter severities based on options
  const filteredSeverities = options.severity === 'all' ? severities : [options.severity];

  // Filter statuses based on options
  const filteredStatuses = options.status === 'all' ? statuses : [options.status];

  // Generate alerts
  for (let i = 0; i < options.limit; i++) {
    const severity = filteredSeverities[Math.floor(Math.random() * filteredSeverities.length)];
    const status = filteredStatuses[Math.floor(Math.random() * filteredStatuses.length)];
    const timestamp = new Date(now.getTime() - Math.floor(Math.random() * 24 * 60 * 60 * 1000));

    let message = '';
    let category = '';

    switch (severity) {
      case 'critical':
        category = ['System', 'Database', 'API', 'Security', 'Network'][
          Math.floor(Math.random() * 5)
        ];
        message = `${category} critical error: ${['Service unavailable', 'Database connection lost', 'High CPU usage', 'Memory leak detected'][Math.floor(Math.random() * 4)]}`;
        break;
      case 'warning':
        category = ['System', 'Database', 'API', 'Security', 'Network'][
          Math.floor(Math.random() * 5)
        ];
        message = `${category} warning: ${['High resource usage', 'Slow response time', 'Rate limit approaching', 'Disk space low'][Math.floor(Math.random() * 4)]}`;
        break;
      case 'info':
        category = ['System', 'Database', 'API', 'Security', 'Network'][
          Math.floor(Math.random() * 5)
        ];
        message = `${category} info: ${['Service restarted', 'Backup completed', 'User logged in', 'Configuration changed'][Math.floor(Math.random() * 4)]}`;
        break;
    }

    alerts.push({
      id: `alert-${i + options.offset}`,
      timestamp: timestamp.toISOString(),
      severity: severity,
      category: category,
      message: message,
      status: status,
      acknowledged_at:
        status === 'acknowledged' || status === 'resolved'
          ? new Date(timestamp.getTime() + Math.floor(Math.random() * 60 * 60 * 1000)).toISOString()
          : null,
      resolved_at:
        status === 'resolved'
          ? new Date(
              timestamp.getTime() + Math.floor(Math.random() * 2 * 60 * 60 * 1000),
            ).toISOString()
          : null,
      source: [
        'API Gateway',
        'Authentication Service',
        'Database Service',
        'Storage Service',
        'Analytics Service',
      ][Math.floor(Math.random() * 5)],
    });
  }

  // Sort alerts by timestamp (newest first)
  alerts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  return alerts;
}

/**
 * Generate time points for charts
 *
 * @param startTime - Start time
 * @param endTime - End time
 * @param period - Time period
 * @returns Array of time points
 */
function generateTimePoints(startTime: Date, endTime: Date, period: string): string[] {
  const timePoints: string[] = [];
  let interval: number;
  let format: string;

  // Determine interval and format based on period
  switch (period) {
    case '1h':
      interval = 5 * 60 * 1000; // 5 minutes
      format = 'HH:mm';
      break;
    case '6h':
      interval = 15 * 60 * 1000; // 15 minutes
      format = 'HH:mm';
      break;
    case '24h':
      interval = 60 * 60 * 1000; // 1 hour
      format = 'HH:mm';
      break;
    case '7d':
      interval = 6 * 60 * 60 * 1000; // 6 hours
      format = 'MM-DD HH:mm';
      break;
    case '30d':
      interval = 24 * 60 * 60 * 1000; // 1 day
      format = 'MM-DD';
      break;
    default:
      interval = 60 * 60 * 1000; // 1 hour
      format = 'HH:mm';
  }

  // Generate time points
  for (let time = startTime.getTime(); time <= endTime.getTime(); time += interval) {
    timePoints.push(new Date(time).toISOString());
  }

  return timePoints;
}

// Register routes
router.get('/', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getAlerts);
router.get('/statistics', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getAlertStatistics);
router.put('/:alert_id', rateLimiter({ windowMs: 60 * 1000, max: 10 }), updateAlertStatus);

export default router;
