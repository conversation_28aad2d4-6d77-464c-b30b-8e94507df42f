events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Main server block
    server {
        listen 80 default_server;
        server_name mvs.kanousai.com localhost _;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy strict-origin-when-cross-origin;

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Infrastructure - Port 80 Accessible
Nginx: Running
Supabase: Connected
Docker Network: Active
Services: Configured for remote Supabase
";
            add_header Content-Type text/plain;
        }

        # Default location
        location / {
            return 200 "MVS-VR System Status

Infrastructure: ✓ Running
Port 80: ✓ Accessible
Nginx: ✓ Active
Docker Network: ✓ Connected
Supabase: ✓ Remote connection configured

Services Status:
- Auth Service: Configured for Supabase auth
- API Gateway: Configured for service routing
- Asset Service: Configured for Supabase storage
- Analytics Service: Configured for data collection

Next Steps:
1. Rebuild Docker images with proper dependencies
2. Configure DNS: mvs.kanousai.com -> 157.245.103.57
3. Test individual service endpoints

Health Check: /health
Supabase URL: https://hiyqiqbgiueyyvqoqhht.supabase.co
";
            add_header Content-Type text/plain;
        }
    }
}
