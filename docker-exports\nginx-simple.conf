events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # Upstream servers (when services are working)
    upstream api_gateway {
        server api-gateway:4000 max_fails=3 fail_timeout=30s;
    }

    upstream directus_backend {
        server directus:8055 max_fails=3 fail_timeout=30s;
    }

    # PRIMARY DOMAIN - mvs.kanousai.com (Vendor Login Home Page)
    server {
        listen 80;
        server_name mvs.kanousai.com;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy strict-origin-when-cross-origin;

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Production - Healthy
Primary Domain: mvs.kanousai.com (Vendor Login)
API: mvs.kanousai.com/api
System Admin: admin.mvs.kanousai.com
Staging: staging.mvs.kanousai.com
";
            add_header Content-Type text/plain;
        }

        # API Gateway - Under main domain as required
        location /api/ {
            limit_req zone=api burst=20 nodelay;

            # When API Gateway is working, proxy to it
            # proxy_pass http://api_gateway/;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: API status
            return 200 "MVS-VR API Gateway - Ready
Endpoint: mvs.kanousai.com/api
Status: Configured for service integration
";
            add_header Content-Type text/plain;
        }

        # Vendor Portal Routes (when services are running)
        location /vendor/ {
            # proxy_pass http://api_gateway;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: Vendor portal status
            return 200 "Vendor Portal - Ready for Integration";
            add_header Content-Type text/plain;
        }

        # Vendor UX Home Page - Login Entry Point
        location / {
            # When API Gateway is working, serve vendor login page
            # proxy_pass http://api_gateway;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: Vendor login page
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Vendor Portal</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; justify-content: center; }
        .login-container { background: white; padding: 40px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); max-width: 400px; width: 100%; }
        .logo { text-align: center; margin-bottom: 30px; }
        .logo h1 { color: #333; margin: 0; font-size: 2.5em; font-weight: 300; }
        .logo p { color: #666; margin: 5px 0 0 0; font-size: 1.1em; }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; color: #333; font-weight: 500; }
        .form-group input { width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 16px; transition: border-color 0.3s; }
        .form-group input:focus { outline: none; border-color: #667eea; }
        .login-btn { width: 100%; padding: 12px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 8px; font-size: 16px; font-weight: 500; cursor: pointer; transition: transform 0.2s; }
        .login-btn:hover { transform: translateY(-2px); }
        .status { background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: center; }
        .links { margin-top: 30px; text-align: center; }
        .links a { color: #667eea; text-decoration: none; margin: 0 10px; }
        .links a:hover { text-decoration: underline; }
        .staging-note { background: #fff3cd; padding: 10px; border-radius: 5px; margin-top: 20px; text-align: center; color: #856404; font-size: 14px; }
    </style>
</head>
<body>
    <div class='login-container'>
        <div class='logo'>
            <h1>MVS-VR</h1>
            <p>Vendor Portal</p>
        </div>

        <div class='status'>
            <strong>✅ System Status:</strong> Online<br>
            <strong>🔗 Supabase:</strong> Connected<br>
            <strong>📡 Server:</strong> Ready
        </div>

        <form id='loginForm' onsubmit='handleLogin(event)'>
            <div class='form-group'>
                <label for='email'>Email Address</label>
                <input type='email' id='email' name='email' required placeholder='<EMAIL>'>
            </div>

            <div class='form-group'>
                <label for='password'>Password</label>
                <input type='password' id='password' name='password' required placeholder='Enter your password'>
            </div>

            <button type='submit' class='login-btn'>Sign In to Vendor Portal</button>
        </form>

        <div class='links'>
            <a href='/api/health'>API Status</a> |
            <a href='http://admin.mvs.kanousai.com'>System Admin</a> |
            <a href='http://staging.mvs.kanousai.com'>Staging</a>
        </div>

        <div class='staging-note'>
            <strong>Staging Environment:</strong> This is the staging deployment for testing vendor and system admin functionality.
        </div>
    </div>

    <script>
        function handleLogin(event) {
            event.preventDefault();
            alert('Login integration ready - will connect to /api/auth/vendor-login when services are running');
            // When services are running:
            // fetch('/api/auth/vendor-login', { method: 'POST', ... })
            // .then(() => window.location.href = '/vendor/dashboard')
        }
    </script>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # SYSTEM ADMIN SUBDOMAIN - admin.mvs.kanousai.com (System Administrators Only)
    server {
        listen 80;
        server_name admin.mvs.kanousai.com;

        # Security headers for admin panel
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy strict-origin-when-cross-origin;

        location / {
            # When Directus is working, proxy to it
            # proxy_pass http://directus_backend;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;
            # client_max_body_size 100M;

            # Temporary: System admin panel
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR System Administration</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; border-top: 4px solid #dc3545; }
        h1 { color: #dc3545; }
        .warning { background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; color: #721c24; }
        .status { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .feature { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #dc3545; }
        .access-note { background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; color: #0c5460; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🛡️ MVS-VR System Administration</h1>

        <div class='warning'>
            <strong>⚠️ RESTRICTED ACCESS:</strong> This portal is for system administrators only. Vendors should use the main portal at mvs.kanousai.com
        </div>

        <div class='status'>
            <strong>Status:</strong> Directus CMS Ready<br>
            <strong>Domain:</strong> admin.mvs.kanousai.com<br>
            <strong>Purpose:</strong> System administration and platform management
        </div>

        <h2>System Admin Features</h2>
        <div class='feature'>🏢 Vendor Management & Onboarding</div>
        <div class='feature'>👥 User Account Administration</div>
        <div class='feature'>📊 Platform Analytics & Monitoring</div>
        <div class='feature'>⚙️ System Configuration & Settings</div>
        <div class='feature'>🔒 Security & Access Control</div>
        <div class='feature'>📈 Performance Monitoring</div>

        <div class='access-note'>
            <strong>📝 Note:</strong> System admin panel will be available via Directus CMS once containers are running.<br>
            <strong>🔑 Login:</strong> <EMAIL> / 9elskdUeo@I!
        </div>

        <h2>Vendor vs System Admin</h2>
        <p><strong>Vendors:</strong> Use mvs.kanousai.com to access vendor-specific features</p>
        <p><strong>System Admins:</strong> Use admin.mvs.kanousai.com for platform management</p>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # STAGING SUBDOMAIN - staging.mvs.kanousai.com (Full System Staging)
    server {
        listen 80;
        server_name staging.mvs.kanousai.com;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # Health check for staging
        location /health {
            access_log off;
            return 200 "MVS-VR Staging Environment - Healthy
Vendor Portal: staging.mvs.kanousai.com
System Admin: staging.mvs.kanousai.com/admin
API: staging.mvs.kanousai.com/api
";
            add_header Content-Type text/plain;
        }

        # Staging API Gateway
        location /api/ {
            limit_req zone=api burst=20 nodelay;

            # When staging services are running, proxy to them
            # proxy_pass http://staging_api_gateway/;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: Staging API status
            return 200 "Staging API Gateway - Ready for Testing";
            add_header Content-Type text/plain;
        }

        # Staging System Admin
        location /admin/ {
            # When staging Directus is running, proxy to it
            # proxy_pass http://staging_directus_backend/;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: Staging admin status
            return 200 "Staging System Admin - Ready for Testing";
            add_header Content-Type text/plain;
        }

        # Staging Vendor Portal
        location /vendor/ {
            # When staging vendor portal is running, proxy to it
            # proxy_pass http://staging_api_gateway;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: Staging vendor portal status
            return 200 "Staging Vendor Portal - Ready for Testing";
            add_header Content-Type text/plain;
        }

        # Staging Home Page
        location / {
            # When staging services are running, serve staging vendor login
            # proxy_pass http://staging_api_gateway;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: Staging environment page
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR Staging Environment</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 700px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; border-top: 4px solid #ffc107; }
        h1 { color: #ffc107; }
        .warning { background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; color: #721c24; }
        .status { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 30px 0; }
        .link { background: #ffc107; color: #212529; padding: 15px; text-align: center; border-radius: 5px; text-decoration: none; font-weight: 500; }
        .link:hover { background: #e0a800; }
        .feature { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 MVS-VR Staging Environment</h1>

        <div class='warning'>
            <strong>⚠️ STAGING ENVIRONMENT:</strong> This is for testing purposes only. Data may be reset without notice.
        </div>

        <div class='status'>
            <strong>Environment:</strong> Staging<br>
            <strong>Domain:</strong> staging.mvs.kanousai.com<br>
            <strong>Purpose:</strong> Full system testing for vendors and system admins
        </div>

        <h2>Staging Access Points</h2>
        <div class='links'>
            <a href='/vendor/' class='link'>🏪 Vendor Portal Testing</a>
            <a href='/admin/' class='link'>🛡️ System Admin Testing</a>
            <a href='/api/health' class='link'>🔌 API Testing</a>
            <a href='/health' class='link'>❤️ Health Check</a>
        </div>

        <h2>Testing Features</h2>
        <div class='feature'>🏪 Complete vendor workflow testing</div>
        <div class='feature'>🛡️ System administration testing</div>
        <div class='feature'>🔌 API integration testing</div>
        <div class='feature'>📊 Analytics and monitoring testing</div>
        <div class='feature'>🔒 Authentication and security testing</div>

        <h2>Environment Notes</h2>
        <p><strong>Vendors:</strong> Test your complete workflow from login to dashboard</p>
        <p><strong>System Admins:</strong> Test platform management and vendor oversight</p>
        <p><strong>Developers:</strong> Test API integrations and service connectivity</p>

        <div class='warning'>
            <strong>🔄 Reset Policy:</strong> Staging data is reset weekly. Do not store important data here.
        </div>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # DEFAULT/FALLBACK - Handle IP access and unknown domains
    server {
        listen 80 default_server;
        server_name _;

        # Redirect IP access to main domain
        location / {
            return 301 http://mvs.kanousai.com$request_uri;
        }

        # Health check for IP access
        location /health {
            access_log off;
            return 200 "MVS-VR Infrastructure - Correctly Configured
Primary: mvs.kanousai.com (Vendor Login)
API: mvs.kanousai.com/api (API Gateway)
System Admin: admin.mvs.kanousai.com (System Administrators)
Staging: staging.mvs.kanousai.com (Full System Testing)
";
            add_header Content-Type text/plain;
        }
    }
}
