events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;

    # Upstream servers (when services are working)
    upstream api_gateway {
        server api-gateway:4000 max_fails=3 fail_timeout=30s;
    }

    upstream directus_backend {
        server directus:8055 max_fails=3 fail_timeout=30s;
    }

    # PRIMARY DOMAIN - mvs.kanousai.com (Vendor UX Home Page)
    server {
        listen 80;
        server_name mvs.kanousai.com;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy strict-origin-when-cross-origin;

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Production - Healthy
Primary Domain: mvs.kanousai.com
API: api.mvs.kanousai.com
Admin: admin.mvs.kanousai.com
Staging: staging.mvs.kanousai.com
";
            add_header Content-Type text/plain;
        }

        # Vendor UX Home Page - Main Entry Point
        location / {
            # When API Gateway is working, proxy to it
            # proxy_pass http://api_gateway;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: Show vendor landing page
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Virtual Reality Platform</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .links { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 30px 0; }
        .link { background: #007bff; color: white; padding: 15px; text-align: center; border-radius: 5px; text-decoration: none; }
        .link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 MVS-VR Platform</h1>
        <div class='status'>
            <strong>✅ System Status:</strong> Online<br>
            <strong>🌐 Primary Domain:</strong> mvs.kanousai.com<br>
            <strong>📡 Server:</strong> 157.245.103.57<br>
            <strong>🔗 Supabase:</strong> Connected
        </div>

        <h2>Platform Access Points</h2>
        <div class='links'>
            <a href='http://api.mvs.kanousai.com' class='link'>🔌 API Gateway</a>
            <a href='http://admin.mvs.kanousai.com' class='link'>⚙️ Admin Panel</a>
            <a href='http://staging.mvs.kanousai.com' class='link'>🧪 Staging Environment</a>
            <a href='/health' class='link'>❤️ Health Check</a>
        </div>

        <h2>Vendor UX Entry Point</h2>
        <p>This is the main entry point for the MVS-VR vendor experience. The platform provides:</p>
        <ul>
            <li>Virtual Reality content management</li>
            <li>Asset management and storage</li>
            <li>Scene creation and editing</li>
            <li>Analytics and monitoring</li>
        </ul>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # API SUBDOMAIN - api.mvs.kanousai.com
    server {
        listen 80;
        server_name api.mvs.kanousai.com;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy strict-origin-when-cross-origin;

        # Rate limiting for API
        location / {
            limit_req zone=api burst=20 nodelay;

            # When API Gateway is working, proxy to it
            # proxy_pass http://api_gateway;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;

            # Temporary: API status page
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR API Gateway</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        h1 { color: #007bff; }
        .status { background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .endpoint { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔌 MVS-VR API Gateway</h1>
        <div class='status'>
            <strong>Status:</strong> Ready for API services<br>
            <strong>Domain:</strong> api.mvs.kanousai.com<br>
            <strong>Rate Limit:</strong> 10 requests/second
        </div>

        <h2>Available Endpoints</h2>
        <div class='endpoint'><strong>GET</strong> /health - Health check</div>
        <div class='endpoint'><strong>POST</strong> /auth/* - Authentication services</div>
        <div class='endpoint'><strong>GET/POST</strong> /assets/* - Asset management</div>
        <div class='endpoint'><strong>GET/POST</strong> /scenes/* - Scene management</div>
        <div class='endpoint'><strong>GET</strong> /analytics/* - Analytics data</div>

        <p><strong>Note:</strong> API services will be available once backend containers are running.</p>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }

        # API Health check
        location /health {
            access_log off;
            return 200 "API Gateway - Ready";
            add_header Content-Type text/plain;
        }
    }

    # ADMIN SUBDOMAIN - admin.mvs.kanousai.com (Directus CMS)
    server {
        listen 80;
        server_name admin.mvs.kanousai.com;

        # Security headers
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        location / {
            # When Directus is working, proxy to it
            # proxy_pass http://directus_backend;
            # proxy_set_header Host $host;
            # proxy_set_header X-Real-IP $remote_addr;
            # proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            # proxy_set_header X-Forwarded-Proto $scheme;
            # client_max_body_size 100M;

            # Temporary: Admin panel status
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR Admin Panel</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        h1 { color: #dc3545; }
        .status { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .feature { background: #f8f9fa; padding: 10px; margin: 10px 0; border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>⚙️ MVS-VR Admin Panel</h1>
        <div class='status'>
            <strong>Status:</strong> Preparing Directus CMS<br>
            <strong>Domain:</strong> admin.mvs.kanousai.com<br>
            <strong>Backend:</strong> Directus CMS
        </div>

        <h2>Admin Features</h2>
        <div class='feature'>📊 Content Management System</div>
        <div class='feature'>👥 User Management</div>
        <div class='feature'>🎨 Asset Library Management</div>
        <div class='feature'>📈 Analytics Dashboard</div>
        <div class='feature'>⚙️ System Configuration</div>

        <p><strong>Note:</strong> Admin panel will be available once Directus container is running.</p>
        <p><strong>Login:</strong> <EMAIL> / 9elskdUeo@I!</p>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # STAGING SUBDOMAIN - staging.mvs.kanousai.com
    server {
        listen 80;
        server_name staging.mvs.kanousai.com;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        location / {
            # Staging environment - proxy to staging services when available
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR Staging Environment</title>
    <meta charset='utf-8'>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f8f9fa; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; }
        h1 { color: #ffc107; }
        .status { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .warning { background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; color: #721c24; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🧪 MVS-VR Staging Environment</h1>
        <div class='status'>
            <strong>Environment:</strong> Staging<br>
            <strong>Domain:</strong> staging.mvs.kanousai.com<br>
            <strong>Purpose:</strong> Testing and development
        </div>

        <div class='warning'>
            <strong>⚠️ Warning:</strong> This is a staging environment. Data may be reset without notice.
        </div>

        <h2>Staging Features</h2>
        <ul>
            <li>Latest development builds</li>
            <li>Feature testing environment</li>
            <li>Integration testing</li>
            <li>Performance testing</li>
        </ul>

        <p><strong>Note:</strong> Staging services will be deployed separately from production.</p>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }

        # Staging health check
        location /health {
            access_log off;
            return 200 "Staging Environment - Ready";
            add_header Content-Type text/plain;
        }
    }

    # DEFAULT/FALLBACK - Handle IP access and unknown domains
    server {
        listen 80 default_server;
        server_name _;

        # Redirect IP access to main domain
        location / {
            return 301 http://mvs.kanousai.com$request_uri;
        }

        # Health check for IP access
        location /health {
            access_log off;
            return 200 "MVS-VR Infrastructure - All Subdomains Configured
Primary: mvs.kanousai.com
API: api.mvs.kanousai.com
Admin: admin.mvs.kanousai.com
Staging: staging.mvs.kanousai.com
";
            add_header Content-Type text/plain;
        }
    }
}
