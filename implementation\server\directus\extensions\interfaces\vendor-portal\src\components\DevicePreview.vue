<template>
  <div class="device-preview">
    <div class="device-selector">
      <div class="device-categories">
        <button
          v-for="category in deviceCategories"
          :key="category.id"
          class="category-button"
          :class="{ active: activeCategory === category.id }"
          @click="activeCategory = category.id"
        >
          <i class="material-icons">{{ category.icon }}</i>
          <span>{{ category.name }}</span>
        </button>
      </div>

      <div class="device-list">
        <button
          v-for="device in filteredDevices"
          :key="device.id"
          class="device-button"
          :class="{ active: selectedDevice === device.id }"
          @click="selectDevice(device.id)"
        >
          <i class="material-icons">{{ device.icon }}</i>
          <span>{{ device.name }}</span>
          <span class="device-dimensions">{{ device.width }}×{{ device.height }}</span>
        </button>

        <button
          class="device-button custom-device"
          :class="{ active: isCustomDevice }"
          @click="toggleCustomDevicePanel"
        >
          <i class="material-icons">add_circle_outline</i>
          <span>Custom Size</span>
        </button>
      </div>
    </div>

    <div class="device-frame-container">
      <!-- Mobile Frame -->
      <div
        v-if="currentDevice.type === 'mobile'"
        class="device-frame mobile-frame"
        :class="[`orientation-${deviceOrientation}`]"
        :style="deviceFrameStyle"
      >
        <div class="device-frame-header">
          <div class="device-notch"></div>
        </div>

        <div class="device-frame-screen">
          <iframe
            :src="previewUrl"
            frameborder="0"
            class="device-iframe"
          ></iframe>
        </div>

        <div class="device-frame-footer">
          <div class="device-home-button"></div>
        </div>
      </div>

      <!-- Tablet Frame -->
      <div
        v-else-if="currentDevice.type === 'tablet'"
        class="device-frame tablet-frame"
        :class="[`orientation-${deviceOrientation}`]"
        :style="deviceFrameStyle"
      >
        <div class="device-frame-header">
          <div class="device-camera"></div>
        </div>

        <div class="device-frame-screen">
          <iframe
            :src="previewUrl"
            frameborder="0"
            class="device-iframe"
          ></iframe>
        </div>

        <div class="device-frame-footer">
          <div class="device-home-button"></div>
        </div>
      </div>

      <!-- Desktop Frame -->
      <div
        v-else-if="currentDevice.type === 'desktop'"
        class="device-frame desktop-frame"
        :style="deviceFrameStyle"
      >
        <div class="device-frame-header">
          <div class="device-browser-controls">
            <span class="browser-button"></span>
            <span class="browser-button"></span>
            <span class="browser-button"></span>
            <div class="browser-address-bar">{{ previewUrl }}</div>
          </div>
        </div>

        <div class="device-frame-screen">
          <iframe
            :src="previewUrl"
            frameborder="0"
            class="device-iframe"
          ></iframe>
        </div>
      </div>

      <!-- TV Frame -->
      <div
        v-else-if="currentDevice.type === 'tv'"
        class="device-frame tv-frame"
        :style="deviceFrameStyle"
      >
        <div class="device-frame-bezel"></div>

        <div class="device-frame-screen">
          <iframe
            :src="previewUrl"
            frameborder="0"
            class="device-iframe"
          ></iframe>
        </div>

        <div class="device-frame-stand"></div>
      </div>

      <!-- Custom Frame -->
      <div
        v-else
        class="device-frame custom-frame"
        :style="deviceFrameStyle"
      >
        <div class="device-frame-screen">
          <iframe
            :src="previewUrl"
            frameborder="0"
            class="device-iframe"
          ></iframe>
        </div>
      </div>

      <!-- Device Controls -->
      <div class="device-controls">
        <button
          v-if="canRotate"
          class="control-button"
          @click="toggleOrientation"
          title="Rotate device"
        >
          <i class="material-icons">screen_rotation</i>
        </button>

        <button
          class="control-button"
          @click="refreshPreview"
          :disabled="previewLoading"
          title="Refresh preview"
        >
          <i class="material-icons">refresh</i>
        </button>

        <div class="network-simulator">
          <select
            v-model="networkCondition"
            @change="setNetworkCondition(networkCondition)"
          >
            <option value="online">Online</option>
            <option value="4g">Fast 4G</option>
            <option value="3g">Slow 3G</option>
            <option value="slow-2g">Offline</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Custom Device Panel -->
    <div v-if="showCustomDevicePanel" class="custom-device-panel">
      <div class="panel-header">
        <h3>Custom Device</h3>
        <button @click="toggleCustomDevicePanel" class="close-button">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="panel-content">
        <div class="form-group">
          <label for="custom-name">Name</label>
          <input
            id="custom-name"
            v-model="customDevice.name"
            type="text"
            placeholder="My Custom Device"
          />
        </div>

        <div class="form-group">
          <label for="custom-width">Width (px)</label>
          <input
            id="custom-width"
            v-model.number="customDevice.width"
            type="number"
            min="320"
            max="3840"
          />
        </div>

        <div class="form-group">
          <label for="custom-height">Height (px)</label>
          <input
            id="custom-height"
            v-model.number="customDevice.height"
            type="number"
            min="240"
            max="2160"
          />
        </div>

        <div class="form-group">
          <label for="custom-dpr">Device Pixel Ratio</label>
          <input
            id="custom-dpr"
            v-model.number="customDevice.devicePixelRatio"
            type="number"
            min="1"
            max="4"
            step="0.1"
          />
        </div>

        <div class="form-actions">
          <button @click="applyCustomDevice" class="btn-primary">Apply</button>
          <button @click="saveCustomDevice" class="btn-secondary">Save Preset</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { usePreviewContext } from '../contexts/PreviewContext';

export default {
  name: 'DevicePreview',

  props: {
    src: {
      type: String,
      default: ''
    }
  },

  setup(props) {
    // Get preview context
    const {
      previewDevice,
      previewOrientation,
      previewUrl,
      previewLoading,
      previewNetworkCondition,
      setPreviewDevice,
      setPreviewOrientation,
      setPreviewUrl,
      setNetworkCondition,
      refreshPreview
    } = usePreviewContext();

    // Set initial URL from props
    if (props.src && !previewUrl.value) {
      setPreviewUrl(props.src);
    }

    // Device categories
    const deviceCategories = [
      { id: 'mobile', name: 'Mobile', icon: 'smartphone' },
      { id: 'tablet', name: 'Tablet', icon: 'tablet' },
      { id: 'desktop', name: 'Desktop', icon: 'desktop_windows' },
      { id: 'tv', name: 'TV', icon: 'tv' }
    ];

    // Active category
    const activeCategory = ref('mobile');

    // Selected device
    const selectedDevice = ref('iphone-13');

    // Device orientation
    const deviceOrientation = computed(() => previewOrientation.value);

    // Network condition
    const networkCondition = computed({
      get: () => previewNetworkCondition.value,
      set: (value) => setNetworkCondition(value)
    });

    // Custom device panel
    const showCustomDevicePanel = ref(false);

    // Custom device
    const customDevice = ref({
      name: 'Custom Device',
      width: 375,
      height: 667,
      devicePixelRatio: 2
    });

    // Is custom device
    const isCustomDevice = computed(() => selectedDevice.value === 'custom');

    // Can rotate
    const canRotate = computed(() => {
      return ['mobile', 'tablet'].includes(currentDevice.value.type);
    });

    // Device list
    const devices = [
      // Mobile devices
      { id: 'iphone-13', name: 'iPhone 13', type: 'mobile', width: 390, height: 844, devicePixelRatio: 3, icon: 'smartphone' },
      { id: 'iphone-se', name: 'iPhone SE', type: 'mobile', width: 375, height: 667, devicePixelRatio: 2, icon: 'smartphone' },
      { id: 'pixel-6', name: 'Pixel 6', type: 'mobile', width: 393, height: 851, devicePixelRatio: 2.75, icon: 'smartphone' },
      { id: 'galaxy-s21', name: 'Galaxy S21', type: 'mobile', width: 360, height: 800, devicePixelRatio: 3, icon: 'smartphone' },

      // Tablet devices
      { id: 'ipad', name: 'iPad', type: 'tablet', width: 768, height: 1024, devicePixelRatio: 2, icon: 'tablet' },
      { id: 'ipad-pro', name: 'iPad Pro', type: 'tablet', width: 1024, height: 1366, devicePixelRatio: 2, icon: 'tablet' },
      { id: 'galaxy-tab', name: 'Galaxy Tab', type: 'tablet', width: 800, height: 1280, devicePixelRatio: 1.5, icon: 'tablet' },

      // Desktop devices
      { id: 'laptop', name: 'Laptop', type: 'desktop', width: 1280, height: 800, devicePixelRatio: 1, icon: 'laptop' },
      { id: 'desktop', name: 'Desktop', type: 'desktop', width: 1920, height: 1080, devicePixelRatio: 1, icon: 'desktop_windows' },
      { id: 'desktop-4k', name: 'Desktop 4K', type: 'desktop', width: 3840, height: 2160, devicePixelRatio: 1, icon: 'desktop_windows' },

      // TV devices
      { id: 'tv-hd', name: 'TV HD', type: 'tv', width: 1280, height: 720, devicePixelRatio: 1, icon: 'tv' },
      { id: 'tv-full-hd', name: 'TV Full HD', type: 'tv', width: 1920, height: 1080, devicePixelRatio: 1, icon: 'tv' },
      { id: 'tv-4k', name: 'TV 4K', type: 'tv', width: 3840, height: 2160, devicePixelRatio: 1, icon: 'tv' }
    ];

    // Filtered devices
    const filteredDevices = computed(() => {
      return devices.filter(device => device.type === activeCategory.value);
    });

    // Current device
    const currentDevice = computed(() => {
      if (selectedDevice.value === 'custom') {
        return {
          ...customDevice.value,
          type: 'custom',
          icon: 'devices_other'
        };
      }

      return devices.find(device => device.id === selectedDevice.value) || devices[0];
    });

    // Device frame style
    const deviceFrameStyle = computed(() => {
      const { width, height } = currentDevice.value;

      // For mobile and tablet, swap dimensions if in landscape
      let frameWidth = width;
      let frameHeight = height;

      if (['mobile', 'tablet'].includes(currentDevice.value.type) && deviceOrientation.value === 'landscape') {
        frameWidth = height;
        frameHeight = width;
      }

      return {
        '--device-width': `${frameWidth}px`,
        '--device-height': `${frameHeight}px`,
        '--device-pixel-ratio': currentDevice.value.devicePixelRatio
      };
    });

    // Select device
    const selectDevice = (deviceId) => {
      selectedDevice.value = deviceId;

      // Update preview device
      const device = devices.find(d => d.id === deviceId);
      if (device) {
        setPreviewDevice(device.type);
      }
    };

    // Toggle orientation
    const toggleOrientation = () => {
      setPreviewOrientation(deviceOrientation.value === 'portrait' ? 'landscape' : 'portrait');
    };

    // Toggle custom device panel
    const toggleCustomDevicePanel = () => {
      showCustomDevicePanel.value = !showCustomDevicePanel.value;

      if (showCustomDevicePanel.value) {
        selectedDevice.value = 'custom';
        setPreviewDevice('custom');
      }
    };

    // Apply custom device
    const applyCustomDevice = () => {
      selectedDevice.value = 'custom';
      setPreviewDevice('custom');
    };

    // Save custom device
    const saveCustomDevice = () => {
      // In a real implementation, this would save the custom device to user preferences
      console.log('Saving custom device:', customDevice.value);
    };

    // Lifecycle hooks
    onMounted(() => {
      // Set initial device
      const device = devices.find(d => d.id === selectedDevice.value);
      if (device) {
        setPreviewDevice(device.type);
      }
    });

    return {
      previewUrl,
      previewLoading,
      deviceCategories,
      activeCategory,
      devices,
      filteredDevices,
      selectedDevice,
      deviceOrientation,
      networkCondition,
      showCustomDevicePanel,
      customDevice,
      isCustomDevice,
      currentDevice,
      deviceFrameStyle,
      canRotate,
      selectDevice,
      toggleOrientation,
      setNetworkCondition,
      refreshPreview,
      toggleCustomDevicePanel,
      applyCustomDevice,
      saveCustomDevice
    };
  }
};
</script>

<style scoped>
.device-preview {
  display: flex;
  height: 100%;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background);
}

.device-selector {
  width: 250px;
  border-right: 1px solid var(--theme--border-color);
  display: flex;
  flex-direction: column;
  background-color: var(--theme--background-subdued);
}

.device-categories {
  display: flex;
  border-bottom: 1px solid var(--theme--border-color);
}

.category-button {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.category-button:hover {
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.category-button.active {
  border-bottom-color: var(--theme--primary);
  color: var(--theme--primary);
}

.category-button i {
  font-size: 20px;
  margin-bottom: 4px;
}

.category-button span {
  font-size: 12px;
}

.device-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.device-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 4px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.device-button:hover {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
}

.device-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border-color: var(--theme--primary);
}

.device-button i {
  margin-right: 8px;
  font-size: 18px;
}

.device-dimensions {
  margin-left: auto;
  font-size: 12px;
  opacity: 0.7;
}

.device-button.custom-device {
  margin-top: 12px;
  border-style: dashed;
}

.device-frame-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  background-color: var(--theme--background-subdued);
}

.device-frame {
  position: relative;
  width: var(--device-width);
  height: var(--device-height);
  background-color: #000;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: width 0.3s, height 0.3s;
}

.device-frame-screen {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  background-color: white;
}

.device-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

/* Mobile Frame */
.mobile-frame {
  padding: 40px 10px;
  border-radius: 36px;
}

.mobile-frame .device-frame-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mobile-frame .device-notch {
  width: 150px;
  height: 24px;
  background-color: #000;
  border-radius: 0 0 12px 12px;
}

.mobile-frame .device-frame-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mobile-frame .device-home-button {
  width: 100px;
  height: 4px;
  background-color: #333;
  border-radius: 2px;
}

/* Tablet Frame */
.tablet-frame {
  padding: 50px 20px;
  border-radius: 20px;
}

.tablet-frame .device-frame-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tablet-frame .device-camera {
  width: 8px;
  height: 8px;
  background-color: #333;
  border-radius: 50%;
}

.tablet-frame .device-frame-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tablet-frame .device-home-button {
  width: 40px;
  height: 40px;
  border: 2px solid #333;
  border-radius: 50%;
}

/* Desktop Frame */
.desktop-frame {
  border-radius: 8px;
  padding-top: 36px;
}

.desktop-frame .device-frame-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 36px;
  background-color: #f0f0f0;
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.desktop-frame .device-browser-controls {
  display: flex;
  align-items: center;
  width: 100%;
}

.desktop-frame .browser-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 6px;
}

.desktop-frame .browser-button:nth-child(1) {
  background-color: #ff5f57;
}

.desktop-frame .browser-button:nth-child(2) {
  background-color: #ffbd2e;
}

.desktop-frame .browser-button:nth-child(3) {
  background-color: #28c940;
}

.desktop-frame .browser-address-bar {
  flex: 1;
  margin-left: 12px;
  padding: 4px 12px;
  background-color: white;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* TV Frame */
.tv-frame {
  padding: 20px;
  border-radius: 8px;
}

.tv-frame .device-frame-bezel {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 20px solid #000;
  border-radius: 8px;
  pointer-events: none;
}

.tv-frame .device-frame-stand {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 40px;
  background-color: #333;
  border-radius: 0 0 8px 8px;
}

/* Custom Frame */
.custom-frame {
  border-radius: 8px;
  border: 2px solid #333;
}

/* Device Controls */
.device-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.control-button:hover {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
}

.network-simulator select {
  padding: 6px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

/* Custom Device Panel */
.custom-device-panel {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
}

.panel-content {
  padding: 16px;
}

.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.form-group input {
  width: 100%;
  padding: 8px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.form-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.btn-primary,
.btn-secondary {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border-color: var(--theme--primary);
}

.btn-secondary:hover {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
}

/* Orientation */
.orientation-landscape {
  transform: rotate(90deg);
}
</style>
