<template>
  <div class="material-texture-editor">
    <div class="editor-header">
      <div class="editor-title-section">
        <h3 class="editor-title">Material & Texture Editor</h3>
        <div class="editor-subtitle">Create and edit materials for your products</div>
      </div>
      <div class="editor-actions">
        <button class="action-button" @click="saveMaterial" :disabled="!hasChanges">
          <i class="material-icons">save</i>
          <span>Save</span>
        </button>
        <button class="action-button" @click="resetMaterial" :disabled="!hasChanges">
          <i class="material-icons">refresh</i>
          <span>Reset</span>
        </button>
      </div>
    </div>

    <div class="editor-content">
      <div class="editor-sidebar">
        <div class="sidebar-section">
          <h4 class="sidebar-title">Material Library</h4>
          <div class="search-box">
            <input
              type="text"
              placeholder="Search materials..."
              v-model="searchQuery"
              @input="filterMaterials"
            >
            <i class="material-icons">search</i>
          </div>

          <div class="material-categories">
            <button
              v-for="category in materialCategories"
              :key="category.id"
              class="category-button"
              :class="{ active: selectedCategoryId === category.id }"
              @click="selectCategory(category.id)"
            >
              {{ category.name }}
            </button>
          </div>

          <div class="material-list">
            <div
              v-for="material in filteredMaterials"
              :key="material.id"
              class="material-item"
              :class="{ active: selectedMaterialId === material.id }"
              @click="selectMaterial(material)"
            >
              <div class="material-thumbnail">
                <img v-if="material.thumbnail" :src="material.thumbnail" :alt="material.name">
                <div v-else class="material-placeholder">
                  <i class="material-icons">texture</i>
                </div>
              </div>
              <div class="material-info">
                <div class="material-name">{{ material.name }}</div>
                <div class="material-type">{{ material.type || 'Standard' }}</div>
              </div>
            </div>
          </div>

          <button class="add-material-button" @click="createNewMaterial">
            <i class="material-icons">add</i>
            <span>Create New Material</span>
          </button>
        </div>
      </div>

      <div class="editor-main">
        <div v-if="!selectedMaterial" class="no-material-selected">
          <i class="material-icons">texture</i>
          <p>Select a material to edit or create a new one</p>
        </div>

        <div v-else class="material-editor">
          <div class="material-form">
            <div class="form-section">
              <h4 class="form-section-title">Basic Properties</h4>
              <div class="form-row">
                <div class="form-group">
                  <label for="material-name">Name</label>
                  <input
                    type="text"
                    id="material-name"
                    v-model="material.name"
                    placeholder="Enter material name"
                  >
                </div>
                <div class="form-group">
                  <label for="material-type">Type</label>
                  <select id="material-type" v-model="material.type">
                    <option value="standard">Standard</option>
                    <option value="metal">Metal</option>
                    <option value="glass">Glass</option>
                    <option value="fabric">Fabric</option>
                    <option value="wood">Wood</option>
                    <option value="plastic">Plastic</option>
                    <option value="ceramic">Ceramic</option>
                    <option value="stone">Stone</option>
                    <option value="custom">Custom</option>
                  </select>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="material-category">Category</label>
                  <select id="material-category" v-model="material.categoryId">
                    <option v-for="category in materialCategories" :key="category.id" :value="category.id">
                      {{ category.name }}
                    </option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="material-tags">Tags</label>
                  <input
                    type="text"
                    id="material-tags"
                    v-model="material.tags"
                    placeholder="Enter tags separated by commas"
                  >
                </div>
              </div>
            </div>

            <div class="form-section">
              <h4 class="form-section-title">Material Properties</h4>
              <div class="form-row">
                <div class="form-group">
                  <label for="material-color">Base Color</label>
                  <div class="color-input">
                    <input type="color" id="material-color" v-model="material.properties.baseColor">
                    <input
                      type="text"
                      v-model="material.properties.baseColor"
                      placeholder="#FFFFFF"
                      class="color-text-input"
                    >
                  </div>
                </div>
                <div class="form-group">
                  <label for="material-metallic">Metallic</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="material-metallic"
                      v-model.number="material.properties.metallic"
                      min="0"
                      max="1"
                      step="0.01"
                    >
                    <span class="slider-value">{{ material.properties?.metallic?.toFixed(2) || '0.00' }}</span>
                  </div>
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="material-roughness">Roughness</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="material-roughness"
                      v-model.number="material.properties.roughness"
                      min="0"
                      max="1"
                      step="0.01"
                    >
                    <span class="slider-value">{{ material.properties?.roughness?.toFixed(2) || '0.50' }}</span>
                  </div>
                </div>
                <div class="form-group">
                  <label for="material-normal-strength">Normal Strength</label>
                  <div class="slider-input">
                    <input
                      type="range"
                      id="material-normal-strength"
                      v-model.number="material.properties.normalStrength"
                      min="0"
                      max="2"
                      step="0.01"
                    >
                    <span class="slider-value">{{ material.properties?.normalStrength?.toFixed(2) || '1.00' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-section">
              <h4 class="form-section-title">Textures</h4>
              <div class="texture-grid">
                <div v-for="(texture, type) in textureTypes" :key="type" class="texture-item">
                  <div class="texture-label">{{ texture.label }}</div>
                  <div
                    class="texture-dropzone"
                    :class="{ 'has-texture': material.textures[type] }"
                    @click="openTextureUpload(type)"
                    @dragover.prevent
                    @drop="onTextureDrop($event, type)"
                  >
                    <img
                      v-if="material.textures[type]"
                      :src="material.textures[type]"
                      :alt="texture.label"
                      class="texture-preview"
                    >
                    <div v-else class="texture-placeholder">
                      <i class="material-icons">{{ texture.icon }}</i>
                      <span>{{ texture.placeholder }}</span>
                    </div>
                  </div>
                  <div class="texture-actions">
                    <button
                      v-if="material.textures[type]"
                      class="texture-action-button"
                      @click.stop="removeTexture(type)"
                    >
                      <i class="material-icons">delete</i>
                    </button>
                  </div>
                </div>
              </div>
              <input
                type="file"
                ref="textureUpload"
                style="display: none"
                accept="image/*"
                @change="onTextureSelected"
              >
            </div>
          </div>

          <div class="material-preview">
            <h4 class="preview-title">Material Preview</h4>
            <div class="preview-container">
              <div class="preview-object">
                <div class="preview-sphere" :style="previewStyle"></div>
              </div>
              <div class="preview-controls">
                <button class="preview-control-button" @click="changePreviewShape('sphere')">
                  <i class="material-icons">panorama_fish_eye</i>
                </button>
                <button class="preview-control-button" @click="changePreviewShape('cube')">
                  <i class="material-icons">crop_square</i>
                </button>
                <button class="preview-control-button" @click="changePreviewShape('cylinder')">
                  <i class="material-icons">radio_button_unchecked</i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MaterialTextureEditor',

  props: {
    vendorId: {
      type: String,
      required: true
    },
    materialId: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      materials: [],
      filteredMaterials: [],
      materialCategories: [
        { id: 'all', name: 'All Materials' },
        { id: 'metal', name: 'Metals' },
        { id: 'wood', name: 'Woods' },
        { id: 'fabric', name: 'Fabrics' },
        { id: 'plastic', name: 'Plastics' },
        { id: 'stone', name: 'Stones' },
        { id: 'glass', name: 'Glass' }
      ],
      selectedCategoryId: 'all',
      searchQuery: '',
      selectedMaterialId: null,
      selectedMaterial: null,
      material: {
        id: null,
        name: '',
        type: 'standard',
        categoryId: 'metal',
        tags: '',
        properties: {
          baseColor: '#FFFFFF',
          metallic: 0,
          roughness: 0.5,
          normalStrength: 1
        },
        textures: {}
      },
      originalMaterial: null,
      textureTypes: {
        baseColor: {
          label: 'Base Color',
          icon: 'palette',
          placeholder: 'Drop base color texture'
        },
        normal: {
          label: 'Normal Map',
          icon: 'grain',
          placeholder: 'Drop normal map texture'
        },
        roughness: {
          label: 'Roughness',
          icon: 'blur_on',
          placeholder: 'Drop roughness texture'
        },
        metallic: {
          label: 'Metallic',
          icon: 'opacity',
          placeholder: 'Drop metallic texture'
        },
        height: {
          label: 'Height Map',
          icon: 'terrain',
          placeholder: 'Drop height map texture'
        },
        occlusion: {
          label: 'Ambient Occlusion',
          icon: 'tonality',
          placeholder: 'Drop AO texture'
        }
      },
      currentTextureType: null,
      previewShape: 'sphere'
    };
  },

  computed: {
    hasChanges() {
      return this.originalMaterial &&
             JSON.stringify(this.material) !== JSON.stringify(this.originalMaterial);
    },

    previewStyle() {
      const baseColor = this.material.textures.baseColor || this.material.properties.baseColor;

      return {
        backgroundColor: this.material.properties.baseColor,
        backgroundImage: this.material.textures.baseColor ? `url(${this.material.textures.baseColor})` : 'none',
        opacity: this.material.properties.metallic > 0.5 ? 0.9 : 1,
        filter: `brightness(${1 + this.material.properties.metallic * 0.5}) blur(${this.material.properties.roughness * 2}px)`
      };
    }
  },

  created() {
    this.loadData();
  },

  methods: {
    async loadData() {
      try {
        // Load materials
        const materialsResponse = await this.$api.get(`/items/materials?filter[vendor_id][_eq]=${this.vendorId}`);
        this.materials = materialsResponse.data.data || [];
        this.filteredMaterials = [...this.materials];

        // If materialId is provided, select that material
        if (this.materialId) {
          this.selectedMaterialId = this.materialId;
          this.selectMaterialById(this.materialId);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    },

    filterMaterials() {
      let filtered = [...this.materials];

      // Filter by category
      if (this.selectedCategoryId !== 'all') {
        filtered = filtered.filter(material =>
          material.categoryId === this.selectedCategoryId ||
          material.type === this.selectedCategoryId
        );
      }

      // Filter by search query
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(material =>
          material.name.toLowerCase().includes(query) ||
          (material.tags && material.tags.toLowerCase().includes(query))
        );
      }

      this.filteredMaterials = filtered;
    },

    selectCategory(categoryId) {
      this.selectedCategoryId = categoryId;
      this.filterMaterials();
    },

    selectMaterialById(materialId) {
      const material = this.materials.find(m => m.id === materialId);
      if (material) {
        this.selectMaterial(material);
      }
    },

    selectMaterial(material) {
      this.selectedMaterialId = material.id;
      this.selectedMaterial = material;

      // Clone material for editing
      this.material = JSON.parse(JSON.stringify(material));
      this.originalMaterial = JSON.parse(JSON.stringify(material));
    },

    createNewMaterial() {
      this.selectedMaterialId = null;
      this.selectedMaterial = {};

      // Reset material to defaults
      this.material = {
        id: null,
        name: 'New Material',
        type: 'standard',
        categoryId: this.selectedCategoryId !== 'all' ? this.selectedCategoryId : 'metal',
        tags: '',
        properties: {
          baseColor: '#FFFFFF',
          metallic: 0,
          roughness: 0.5,
          normalStrength: 1
        },
        textures: {}
      };

      this.originalMaterial = null;
    },

    openTextureUpload(textureType) {
      this.currentTextureType = textureType;
      this.$refs.textureUpload.click();
    },

    onTextureSelected(event) {
      const file = event.target.files[0];
      if (file && this.currentTextureType) {
        this.uploadTexture(file, this.currentTextureType);
      }
    },

    onTextureDrop(event, textureType) {
      event.preventDefault();

      const file = event.dataTransfer.files[0];
      if (file) {
        this.uploadTexture(file, textureType);
      }
    },

    async uploadTexture(file, textureType) {
      try {
        // Create form data
        const formData = new FormData();
        formData.append('file', file);

        // Upload file
        const response = await this.$api.post('/files', formData);

        if (response.data.data && response.data.data.id) {
          const fileData = response.data.data;

          // Set texture URL
          this.material.textures[textureType] = fileData.full_url;
        }
      } catch (error) {
        console.error('Error uploading texture:', error);
      }
    },

    removeTexture(textureType) {
      if (this.material.textures[textureType]) {
        this.$delete(this.material.textures, textureType);
      }
    },

    changePreviewShape(shape) {
      this.previewShape = shape;
    },

    async saveMaterial() {
      try {
        let response;

        if (this.material.id) {
          // Update existing material
          response = await this.$api.patch(
            `/items/materials/${this.material.id}`,
            this.material
          );
        } else {
          // Create new material
          this.material.vendor_id = this.vendorId;
          response = await this.$api.post('/items/materials', this.material);
        }

        if (response.data.data) {
          this.material = response.data.data;
          this.originalMaterial = JSON.parse(JSON.stringify(this.material));

          // Update materials list
          const index = this.materials.findIndex(m => m.id === this.material.id);
          if (index !== -1) {
            this.materials[index] = this.material;
          } else {
            this.materials.push(this.material);
          }

          this.filterMaterials();
          this.$emit('update', this.material);
        }
      } catch (error) {
        console.error('Error saving material:', error);
      }
    },

    resetMaterial() {
      if (this.originalMaterial) {
        this.material = JSON.parse(JSON.stringify(this.originalMaterial));
      }
    }
  }
};
</script>

<style scoped>
.material-texture-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.editor-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.editor-subtitle {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  cursor: pointer;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editor-sidebar {
  width: 280px;
  border-right: 1px solid var(--theme--border-color);
  overflow-y: auto;
  background-color: var(--theme--background-subdued);
}

.sidebar-section {
  padding: 16px;
}

.sidebar-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.search-box {
  position: relative;
  margin-bottom: 16px;
}

.search-box input {
  width: 100%;
  padding: 8px 32px 8px 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.search-box i {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
}

.material-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.category-button {
  padding: 4px 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
  font-size: 12px;
}

.category-button:hover {
  background-color: var(--theme--background-accent);
}

.category-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  border-color: var(--theme--primary);
}

.material-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.material-item {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  overflow: hidden;
  background-color: var(--theme--background);
  cursor: pointer;
}

.material-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.material-item.active {
  box-shadow: 0 0 0 2px var(--theme--primary);
}

.material-thumbnail {
  height: 80px;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.material-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.material-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.material-info {
  padding: 8px;
}

.material-name {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.material-type {
  font-size: 10px;
  color: var(--theme--foreground-subdued);
}

.add-material-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 8px;
  border: 1px dashed var(--theme--border-color);
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.add-material-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.editor-main {
  flex: 1;
  overflow: auto;
  background-color: var(--theme--background);
}

.no-material-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.no-material-selected i {
  font-size: 48px;
  margin-bottom: 16px;
}

.material-editor {
  display: flex;
  padding: 24px;
  gap: 24px;
}

.material-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-section {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 16px;
}

.form-section-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-row:last-child {
  margin-bottom: 0;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.color-input {
  display: flex;
  gap: 8px;
}

.color-input input[type="color"] {
  width: 40px;
  height: 32px;
  padding: 0;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
}

.color-text-input {
  flex: 1;
}

.slider-input {
  display: flex;
  align-items: center;
  gap: 8px;
}

.slider-input input[type="range"] {
  flex: 1;
}

.slider-value {
  width: 40px;
  text-align: right;
  font-size: 14px;
}

.texture-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.texture-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.texture-label {
  font-size: 14px;
}

.texture-dropzone {
  height: 100px;
  border: 2px dashed var(--theme--border-color);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.texture-dropzone:hover {
  border-color: var(--theme--primary);
}

.texture-dropzone.has-texture {
  border-style: solid;
}

.texture-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.texture-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
  text-align: center;
  padding: 8px;
}

.texture-placeholder i {
  font-size: 24px;
  margin-bottom: 8px;
}

.texture-placeholder span {
  font-size: 12px;
}

.texture-actions {
  display: flex;
  justify-content: flex-end;
}

.texture-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
}

.texture-action-button:hover {
  background-color: var(--theme--danger);
  color: var(--theme--danger-background);
}

.material-preview {
  width: 300px;
}

.preview-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.preview-container {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-object {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-sphere {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background-color: #FFFFFF;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2);
}

.preview-controls {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.preview-control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
}

.preview-control-button:hover {
  background-color: var(--theme--background-accent);
}
</style>
