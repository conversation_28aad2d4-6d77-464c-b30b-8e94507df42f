import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { v4 as uuidv4 } from 'uuid';
import { createHash } from 'crypto';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { promisify } from 'util';
import { ChunkUpload, ChunkUploadResult } from '../../shared/models/asset-management';
import { validateRequest } from '../middleware/validation';

// Promisify fs functions
const writeFile = promisify(fs.writeFile);
const readFile = promisify(fs.readFile);
const mkdir = promisify(fs.mkdir);
const access = promisify(fs.access);
const readdir = promisify(fs.readdir);

// Temp directory for chunks
const TEMP_DIR = path.join(os.tmpdir(), 'mvs-vr-chunks');

// Create temp directory if it doesn't exist
try {
  if (!fs.existsSync(TEMP_DIR)) {
    fs.mkdirSync(TEMP_DIR, { recursive: true });
  }
} catch (error) {
  logger.error('Error creating temp directory', { error });
}

// Map to track uploaded chunks
const uploadedChunks = new Map<string, Set<number>>();

/**
 * Upload a chunk of a file
 *
 * @param req - Request
 * @param res - Response
 */
export const uploadChunk = async (req: Request, res: Response): Promise<void> => {
  try {
    // Validate request
    const { assetId, chunkIndex, totalChunks, chunkSize, totalSize, hash } =
      req.body as ChunkUpload;
    const file = req.file;

    if (!file) {
      res.status(400).json({
        success: false,
        error: {
          code: 'NO_FILE',
          message: 'No file provided',
        },
      });
      return;
    }

    // Validate chunk data
    if (!assetId || chunkIndex === undefined || !totalChunks || !chunkSize || !totalSize || !hash) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CHUNK_DATA',
          message: 'Invalid chunk data',
        },
      });
      return;
    }

    // Create asset directory if it doesn't exist
    const assetDir = path.join(TEMP_DIR, assetId);
    try {
      await access(assetDir);
    } catch (error) {
      await mkdir(assetDir, { recursive: true });
    }

    // Save chunk to temp directory
    const chunkPath = path.join(assetDir, `chunk-${chunkIndex}`);
    await writeFile(chunkPath, file.buffer);

    // Track uploaded chunks
    if (!uploadedChunks.has(assetId)) {
      uploadedChunks.set(assetId, new Set());
    }
    uploadedChunks.get(assetId)?.add(chunkIndex);

    // Check if all chunks are uploaded
    const uploadedChunksSet = uploadedChunks.get(assetId) || new Set();
    const isComplete = uploadedChunksSet.size === totalChunks;

    // If all chunks are uploaded, combine them
    let url: string | undefined;
    if (isComplete) {
      url = await combineChunks(assetId, totalChunks, hash);
    }

    // Return result
    const result: ChunkUploadResult = {
      assetId,
      chunkIndex,
      received: true,
      uploadedChunks: Array.from(uploadedChunksSet),
      completed: isComplete,
      url,
    };

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error uploading chunk', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'UPLOAD_ERROR',
        message: 'Error uploading chunk',
      },
    });
  }
};

/**
 * Combine chunks into a single file
 *
 * @param assetId - Asset ID
 * @param totalChunks - Total number of chunks
 * @param expectedHash - Expected hash of the combined file
 * @returns URL of the uploaded file
 */
async function combineChunks(
  assetId: string,
  totalChunks: number,
  expectedHash: string,
): Promise<string> {
  try {
    const assetDir = path.join(TEMP_DIR, assetId);
    const outputPath = path.join(assetDir, 'combined');
    const outputStream = fs.createWriteStream(outputPath);

    // Combine chunks
    for (let i = 0; i < totalChunks; i++) {
      const chunkPath = path.join(assetDir, `chunk-${i}`);
      const chunkData = await readFile(chunkPath);
      outputStream.write(chunkData);
    }

    // Close the stream
    outputStream.end();

    // Wait for the stream to finish
    await new Promise<void>((resolve, reject) => {
      outputStream.on('finish', resolve);
      outputStream.on('error', reject);
    });

    // Verify hash
    const fileData = await readFile(outputPath);
    const fileHash = createHash('sha256').update(fileData).digest('hex');

    if (fileHash !== expectedHash) {
      throw new Error('Hash mismatch');
    }

    // Upload to Supabase storage
    const { data, error } = await supabase.storage
      .from('assets')
      .upload(`${assetId}/file`, fileData, {
        contentType: 'application/octet-stream',
        upsert: true,
      });

    if (error) {
      throw error;
    }

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from('assets').getPublicUrl(`${assetId}/file`);

    // Clean up temp files
    for (let i = 0; i < totalChunks; i++) {
      const chunkPath = path.join(assetDir, `chunk-${i}`);
      fs.unlinkSync(chunkPath);
    }
    fs.unlinkSync(outputPath);

    return publicUrl;
  } catch (error) {
    logger.error('Error combining chunks', { error, assetId });
    throw error;
  }
}
