{"version": "1.0.0", "lastUpdated": "2025-05-22T00:00:00Z", "contacts": [{"id": "contact-1", "name": "<PERSON>", "role": "IT Manager", "email": "<EMAIL>", "phone": "+1234567890", "teams": ["team-it"], "notifyFor": ["DATABASE_FAILURE", "SERVICE_FAILURE", "NETWORK_FAILURE", "SECURITY_BREACH"], "notificationPreferences": ["email", "sms", "slack"]}, {"id": "contact-2", "name": "<PERSON>", "role": "Database Administrator", "email": "<EMAIL>", "phone": "+1234567891", "teams": ["team-it", "team-db"], "notifyFor": ["DATABASE_FAILURE"], "notificationPreferences": ["email", "sms", "slack"]}, {"id": "contact-3", "name": "<PERSON>", "role": "Security Officer", "email": "<EMAIL>", "phone": "+1234567892", "teams": ["team-security"], "notifyFor": ["SECURITY_BREACH"], "notificationPreferences": ["email", "sms", "slack"]}, {"id": "contact-4", "name": "<PERSON>", "role": "Operations Manager", "email": "<EMAIL>", "phone": "+1234567893", "teams": ["team-ops"], "notifyFor": ["SERVICE_FAILURE", "NETWORK_FAILURE"], "notificationPreferences": ["email", "slack"]}], "teams": [{"id": "team-it", "name": "IT Team", "description": "Information Technology team responsible for overall IT infrastructure", "notifyFor": ["DATABASE_FAILURE", "SERVICE_FAILURE", "NETWORK_FAILURE", "SECURITY_BREACH"]}, {"id": "team-db", "name": "Database Team", "description": "Database administration team", "notifyFor": ["DATABASE_FAILURE"]}, {"id": "team-security", "name": "Security Team", "description": "Security team responsible for security incidents", "notifyFor": ["SECURITY_BREACH"]}, {"id": "team-ops", "name": "Operations Team", "description": "Operations team responsible for service availability", "notifyFor": ["SERVICE_FAILURE", "NETWORK_FAILURE"]}], "procedures": [{"id": "procedure-db-failure", "name": "Database Failure Recovery", "description": "Procedure for recovering from database failures", "applicableIncidentTypes": ["DATABASE_FAILURE"], "priority": 1, "steps": [{"id": "step-db-1", "type": "NOTIFICATION", "description": "Notify incident response team", "critical": true}, {"id": "step-db-2", "type": "AUTOMATED", "action": "RECOVER_DATABASE", "description": "Recover database from latest backup", "critical": true}, {"id": "step-db-3", "type": "MANUAL", "description": "Verify database integrity", "critical": true, "instructions": "Run database integrity checks and verify data consistency"}, {"id": "step-db-4", "type": "AUTOMATED", "action": "RECOVER_SERVICES", "description": "Restart dependent services", "critical": false}]}, {"id": "procedure-service-failure", "name": "Service Failure Recovery", "description": "Procedure for recovering from service failures", "applicableIncidentTypes": ["SERVICE_FAILURE"], "priority": 1, "steps": [{"id": "step-svc-1", "type": "NOTIFICATION", "description": "Notify incident response team", "critical": true}, {"id": "step-svc-2", "type": "AUTOMATED", "action": "RECOVER_SERVICES", "description": "Restart failed services", "critical": true}, {"id": "step-svc-3", "type": "MANUAL", "description": "Verify service functionality", "critical": true, "instructions": "Verify that all services are functioning correctly"}]}, {"id": "procedure-network-failure", "name": "Network Failure Recovery", "description": "Procedure for recovering from network failures", "applicableIncidentTypes": ["NETWORK_FAILURE"], "priority": 1, "steps": [{"id": "step-net-1", "type": "NOTIFICATION", "description": "Notify incident response team", "critical": true}, {"id": "step-net-2", "type": "MANUAL", "description": "Verify network connectivity", "critical": true, "instructions": "Check network connectivity and identify failure points"}, {"id": "step-net-3", "type": "MANUAL", "description": "Restore network connectivity", "critical": true, "instructions": "Restore network connectivity by fixing identified issues"}, {"id": "step-net-4", "type": "AUTOMATED", "action": "RECOVER_SERVICES", "description": "Restart network-dependent services", "critical": false}]}, {"id": "procedure-security-breach", "name": "Security Breach Response", "description": "Procedure for responding to security breaches", "applicableIncidentTypes": ["SECURITY_BREACH"], "priority": 1, "steps": [{"id": "step-sec-1", "type": "NOTIFICATION", "description": "Notify security team", "critical": true}, {"id": "step-sec-2", "type": "MANUAL", "description": "Isolate affected systems", "critical": true, "instructions": "Isolate affected systems to prevent further damage"}, {"id": "step-sec-3", "type": "MANUAL", "description": "Assess damage and identify breach vector", "critical": true, "instructions": "Assess the extent of the breach and identify how it occurred"}, {"id": "step-sec-4", "type": "MANUAL", "description": "Remediate security issues", "critical": true, "instructions": "Fix security issues that allowed the breach"}, {"id": "step-sec-5", "type": "AUTOMATED", "action": "RECOVER_ALL", "description": "Restore systems from clean backups", "critical": true}]}, {"id": "procedure-complete-recovery", "name": "Complete System Recovery", "description": "Procedure for recovering all system components", "applicableIncidentTypes": ["COMPLETE_FAILURE"], "priority": 1, "steps": [{"id": "step-all-1", "type": "NOTIFICATION", "description": "Notify all teams", "critical": true}, {"id": "step-all-2", "type": "AUTOMATED", "action": "RECOVER_DATABASE", "description": "Recover database from latest backup", "critical": true}, {"id": "step-all-3", "type": "AUTOMATED", "action": "RECOVER_FILES", "description": "Recover file storage from latest backup", "critical": true}, {"id": "step-all-4", "type": "AUTOMATED", "action": "RECOVER_SERVICES", "description": "Restart all services", "critical": true}, {"id": "step-all-5", "type": "MANUAL", "description": "Verify system functionality", "critical": true, "instructions": "Verify that all system components are functioning correctly"}]}], "recoveryPriorities": [{"component": "database.main", "priority": 1, "description": "Main database", "rto": 15, "dependencies": []}, {"component": "storage.assets", "priority": 2, "description": "Asset storage", "rto": 30, "dependencies": []}, {"component": "application.api", "priority": 3, "description": "API services", "rto": 20, "dependencies": ["database.main", "storage.assets"]}, {"component": "application.vendor", "priority": 4, "description": "Vendor portal", "rto": 25, "dependencies": ["application.api"]}, {"component": "application.admin", "priority": 5, "description": "Admin portal", "rto": 30, "dependencies": ["application.api"]}], "communicationPlan": {"internal": [{"audience": "IT Team", "channel": "email", "template": "internal-incident-notification", "frequency": "immediate"}, {"audience": "IT Team", "channel": "slack", "template": "internal-incident-notification", "frequency": "immediate"}, {"audience": "Management", "channel": "email", "template": "management-incident-notification", "frequency": "hourly"}], "external": [{"audience": "Customers", "channel": "status-page", "template": "customer-incident-notification", "frequency": "as-needed"}, {"audience": "Vend<PERSON>", "channel": "email", "template": "vendor-incident-notification", "frequency": "as-needed"}]}}