{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/AnimationEditor.manual-test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 37827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 37827, "count": 1}], "isBlockCoverage": true}, {"functionName": "mockApiResponse", "ranges": [{"startOffset": 578, "endOffset": 1172, "count": 0}], "isBlockCoverage": false}, {"functionName": "MockAnimationEditor", "ranges": [{"startOffset": 1239, "endOffset": 1807, "count": 0}], "isBlockCoverage": false}, {"functionName": "initVirtual<PERSON><PERSON><PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 1813, "endOffset": 2234, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadAnimationsWithPagination", "ranges": [{"startOffset": 2240, "endOffset": 4060, "count": 0}], "isBlockCoverage": false}, {"functionName": "loadData", "ranges": [{"startOffset": 4066, "endOffset": 4691, "count": 0}], "isBlockCoverage": false}, {"functionName": "simulateScroll", "ranges": [{"startOffset": 4697, "endOffset": 5068, "count": 0}], "isBlockCoverage": false}, {"functionName": "cleanup", "ranges": [{"startOffset": 5074, "endOffset": 5424, "count": 0}], "isBlockCoverage": false}, {"functionName": "runTest", "ranges": [{"startOffset": 5444, "endOffset": 6770, "count": 0}], "isBlockCoverage": false}]}]}