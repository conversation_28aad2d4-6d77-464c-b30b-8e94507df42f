# 🚨 URGENT: MVS-VR Deployment Fix Guide

## 🔍 **Current Issues Identified**

Based on the diagnosis, here are the problems:

### ❌ **Issue 1: SSH Authentication Failed**
- SSH key authentication is not working
- Tried both `vectorax` and `root` users
- Server is online (ping successful) but SSH access denied

### ❌ **Issue 2: Port 80 Not Accessible**
- `curl http://**************/` returns "Connection refused"
- This means either:
  - Docker containers are not running
  - Nginx is not started
  - Firewall is blocking port 80
  - Services were never deployed

### ❌ **Issue 3: Domain Resolution Issues**
- `mvs.kanousai.com` shows "ERR_CONNECTION_REFUSED"
- 307 redirects suggest server configuration issues

## 🔧 **Immediate Fix Steps**

### **Step 1: Fix SSH Access**

You need to access your DigitalOcean server. Try these methods:

#### **Method A: DigitalOcean Console**
1. Log into your DigitalOcean account
2. Go to Droplets → Your MVS-VR server
3. Click "Console" to access the server directly
4. This bypasses SSH and gives you direct terminal access

#### **Method B: Reset SSH Keys**
1. In DigitalOcean console, go to your droplet
2. Click "Recovery" → "Reset Root Password"
3. Use the emailed password to log in as root
4. Add your SSH key manually:
```bash
# Create .ssh directory
mkdir -p ~/.ssh
chmod 700 ~/.ssh

# Add your public key (copy from C:\Users\<USER>\.ssh\id_rsa.pub)
echo "your-public-key-content-here" >> ~/.ssh/authorized_keys
chmod 600 ~/.ssh/authorized_keys
```

#### **Method C: Check SSH Key Format**
Your SSH key might need the correct format. Check if it starts with:
- `ssh-rsa` (RSA key)
- `ssh-ed25519` (Ed25519 key)

### **Step 2: Once You Have Server Access**

Run these commands to fix the deployment:

```bash
# Check if Docker is installed
docker --version

# If Docker is not installed:
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl start docker
systemctl enable docker

# Install Docker Compose if missing:
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Check current directory and files
pwd
ls -la

# If project files are missing, create directory:
mkdir -p /home/<USER>/mvs-vr-deployment
cd /home/<USER>/mvs-vr-deployment

# You'll need to upload the docker-exports files here
```

### **Step 3: Deploy the Services**

Once you have the project files on the server:

```bash
# Navigate to project directory
cd /home/<USER>/mvs-vr-deployment

# Stop any existing containers
docker-compose -f docker-compose.exported.yml down

# Start the services
docker-compose -f docker-compose.exported.yml up -d

# Check container status
docker ps

# Check logs if containers aren't starting
docker-compose -f docker-compose.exported.yml logs
```

### **Step 4: Configure Firewall**

```bash
# Allow HTTP and HTTPS traffic
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp

# Check firewall status
ufw status

# If UFW is inactive, enable it:
ufw --force enable
```

### **Step 5: Test Connectivity**

```bash
# Test locally on server
curl http://localhost/
curl http://localhost/health

# Check nginx status
docker logs $(docker ps -q --filter name=nginx)

# Restart nginx if needed
docker-compose -f docker-compose.exported.yml restart nginx
```

## 📁 **File Upload Methods**

Since SSH isn't working, you'll need to get the files to the server:

### **Method A: DigitalOcean Console + Git**
```bash
# Install git if not available
apt update && apt install git -y

# Clone or download your files
# You can create a GitHub repo with your docker-exports files
git clone https://github.com/your-username/mvs-vr-deployment.git
```

### **Method B: Copy-Paste via Console**
1. Use DigitalOcean console
2. Create files manually using `nano` or `vim`
3. Copy-paste content from your local files

### **Method C: Fix SSH First, Then SCP**
Once SSH is working:
```cmd
scp -r docker-exports/* root@**************:/home/<USER>/mvs-vr-deployment/
```

## 🔍 **Diagnostic Commands**

Run these to check what's wrong:

```bash
# Check if services are running
systemctl status docker
docker ps -a

# Check port 80 specifically
netstat -tulpn | grep :80
ss -tulpn | grep :80

# Check if nginx is binding to port 80
docker exec $(docker ps -q --filter name=nginx) netstat -tulpn

# Check nginx configuration
docker exec $(docker ps -q --filter name=nginx) nginx -t

# Check firewall
ufw status verbose
iptables -L -n
```

## 🌐 **DNS Configuration**

For the domain issues, you need to configure DNS:

1. **Log into your DNS provider** (where you bought mvs.kanousai.com)
2. **Add/Update A Record**:
   - Name: `@` (or blank for root domain)
   - Type: `A`
   - Value: `**************`
   - TTL: `300` (5 minutes)
3. **Add CNAME for www** (optional):
   - Name: `www`
   - Type: `CNAME`
   - Value: `mvs.kanousai.com`

## ⚡ **Quick Fix Checklist**

- [ ] Access server via DigitalOcean console
- [ ] Check if Docker is installed and running
- [ ] Upload/create project files in `/home/<USER>/mvs-vr-deployment/`
- [ ] Run `docker-compose -f docker-compose.exported.yml up -d`
- [ ] Configure firewall: `ufw allow 80/tcp`
- [ ] Test locally: `curl http://localhost/`
- [ ] Configure DNS A record: `mvs.kanousai.com → **************`
- [ ] Test externally: `curl http://**************/`

## 📞 **If You Need Help**

1. **Check DigitalOcean Console**: Most issues can be resolved through the web console
2. **Review Docker Logs**: `docker-compose logs` will show what's failing
3. **Test Step by Step**: Don't skip the local testing (`curl localhost`)
4. **DNS Takes Time**: DNS changes can take up to 24 hours to propagate

## 🎯 **Expected Results After Fix**

- `curl http://localhost/` should return MVS-VR status page
- `curl http://**************/` should return the same
- `http://mvs.kanousai.com/` should work in browser (after DNS propagation)
- All Docker containers should be running: `docker ps`

**Priority: Fix SSH access first, then deploy services, then configure DNS.**
