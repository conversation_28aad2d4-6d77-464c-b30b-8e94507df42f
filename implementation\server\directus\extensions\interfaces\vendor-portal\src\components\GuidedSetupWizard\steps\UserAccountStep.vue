<template>
  <wizard-step
    title="User Account Configuration"
    description="Set up user accounts for your team members who will access the platform."
    :step-data="stepData"
    :validation-schema="validationSchema"
    :help-tips="helpTips"
    @update:step-data="updateStepData"
    @validate="validateStep"
  >
    <div class="user-account-form">
      <div class="form-section">
        <h3 class="section-title">Admin Account</h3>
        <p class="section-description">
          This account will have full access to manage your vendor portal.
        </p>
        
        <div class="form-group">
          <label for="admin-first-name">First Name *</label>
          <input
            id="admin-first-name"
            type="text"
            v-model="localStepData.adminUser.firstName"
            @input="updateAdminField('firstName', $event.target.value)"
            placeholder="Enter first name"
          />
        </div>
        
        <div class="form-group">
          <label for="admin-last-name">Last Name *</label>
          <input
            id="admin-last-name"
            type="text"
            v-model="localStepData.adminUser.lastName"
            @input="updateAdminField('lastName', $event.target.value)"
            placeholder="Enter last name"
          />
        </div>
        
        <div class="form-group">
          <label for="admin-email">Email *</label>
          <input
            id="admin-email"
            type="email"
            v-model="localStepData.adminUser.email"
            @input="updateAdminField('email', $event.target.value)"
            placeholder="Enter email address"
          />
        </div>
        
        <div class="form-group">
          <label for="admin-role">Role *</label>
          <select
            id="admin-role"
            v-model="localStepData.adminUser.role"
            @change="updateAdminField('role', $event.target.value)"
          >
            <option value="admin">Admin</option>
            <option value="manager">Manager</option>
          </select>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Team Members</h3>
        <p class="section-description">
          Add team members who will have access to your vendor portal.
        </p>
        
        <div v-if="localStepData.teamMembers.length === 0" class="empty-state">
          <p>No team members added yet</p>
        </div>
        
        <div v-else class="team-members-list">
          <div 
            v-for="(member, index) in localStepData.teamMembers" 
            :key="index"
            class="team-member-item"
          >
            <div class="team-member-info">
              <div class="team-member-name">
                {{ member.firstName }} {{ member.lastName }}
              </div>
              <div class="team-member-email">
                {{ member.email }}
              </div>
              <div class="team-member-role">
                {{ getRoleName(member.role) }}
              </div>
            </div>
            
            <div class="team-member-actions">
              <button 
                class="edit-button"
                @click="editTeamMember(index)"
              >
                <i class="material-icons">edit</i>
              </button>
              
              <button 
                class="delete-button"
                @click="removeTeamMember(index)"
              >
                <i class="material-icons">delete</i>
              </button>
            </div>
          </div>
        </div>
        
        <div class="add-team-member">
          <button 
            class="add-button"
            @click="showAddMemberForm = true"
          >
            <i class="material-icons">add</i>
            <span>Add Team Member</span>
          </button>
        </div>
        
        <div v-if="showAddMemberForm" class="team-member-form">
          <h4 class="form-title">
            {{ isEditingMember ? 'Edit Team Member' : 'Add Team Member' }}
          </h4>
          
          <div class="form-group">
            <label for="member-first-name">First Name *</label>
            <input
              id="member-first-name"
              type="text"
              v-model="newMember.firstName"
              placeholder="Enter first name"
            />
          </div>
          
          <div class="form-group">
            <label for="member-last-name">Last Name *</label>
            <input
              id="member-last-name"
              type="text"
              v-model="newMember.lastName"
              placeholder="Enter last name"
            />
          </div>
          
          <div class="form-group">
            <label for="member-email">Email *</label>
            <input
              id="member-email"
              type="email"
              v-model="newMember.email"
              placeholder="Enter email address"
            />
          </div>
          
          <div class="form-group">
            <label for="member-role">Role *</label>
            <select
              id="member-role"
              v-model="newMember.role"
            >
              <option value="editor">Editor</option>
              <option value="viewer">Viewer</option>
            </select>
          </div>
          
          <div class="form-actions">
            <button 
              class="cancel-button"
              @click="cancelMemberForm"
            >
              Cancel
            </button>
            
            <button 
              class="save-button"
              @click="saveMember"
              :disabled="!isNewMemberValid"
            >
              {{ isEditingMember ? 'Update' : 'Add' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </wizard-step>
</template>

<script>
import WizardStep from '../WizardStep.vue';

export default {
  name: 'UserAccountStep',
  
  components: {
    WizardStep
  },
  
  props: {
    stepData: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      localStepData: {
        adminUser: {
          id: null,
          firstName: '',
          lastName: '',
          email: '',
          role: 'admin'
        },
        teamMembers: [],
        ...this.stepData
      },
      validationSchema: {
        'adminUser.firstName': {
          required: true,
          minLength: 2,
          maxLength: 50,
          label: 'Admin First Name'
        },
        'adminUser.lastName': {
          required: true,
          minLength: 2,
          maxLength: 50,
          label: 'Admin Last Name'
        },
        'adminUser.email': {
          required: true,
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          patternMessage: 'Please enter a valid email address',
          label: 'Admin Email'
        },
        'adminUser.role': {
          required: true,
          label: 'Admin Role'
        }
      },
      helpTips: [
        {
          title: 'Admin Account',
          text: 'The admin account has full access to manage your vendor portal, including adding and removing team members.'
        },
        {
          title: 'Team Member Roles',
          text: 'Editors can modify content but cannot manage users. Viewers can only view content without making changes.'
        }
      ],
      showAddMemberForm: false,
      isEditingMember: false,
      editingMemberIndex: -1,
      newMember: {
        id: null,
        firstName: '',
        lastName: '',
        email: '',
        role: 'editor'
      }
    };
  },
  
  computed: {
    isNewMemberValid() {
      return (
        this.newMember.firstName.trim() !== '' &&
        this.newMember.lastName.trim() !== '' &&
        this.newMember.email.trim() !== '' &&
        this.newMember.role !== ''
      );
    }
  },
  
  methods: {
    updateStepData(data) {
      this.localStepData = { ...data };
      this.$emit('update:step-data', this.localStepData);
    },
    
    validateStep(isValid) {
      this.$emit('validate', isValid);
    },
    
    updateAdminField(field, value) {
      this.localStepData.adminUser[field] = value;
      this.$emit('update:step-data', this.localStepData);
    },
    
    getRoleName(role) {
      const roles = {
        admin: 'Administrator',
        manager: 'Manager',
        editor: 'Editor',
        viewer: 'Viewer'
      };
      
      return roles[role] || role;
    },
    
    editTeamMember(index) {
      const member = this.localStepData.teamMembers[index];
      
      this.newMember = { ...member };
      this.isEditingMember = true;
      this.editingMemberIndex = index;
      this.showAddMemberForm = true;
    },
    
    removeTeamMember(index) {
      this.localStepData.teamMembers.splice(index, 1);
      this.$emit('update:step-data', this.localStepData);
    },
    
    saveMember() {
      if (!this.isNewMemberValid) return;
      
      if (this.isEditingMember) {
        // Update existing member
        this.localStepData.teamMembers[this.editingMemberIndex] = { ...this.newMember };
      } else {
        // Add new member
        this.localStepData.teamMembers.push({ ...this.newMember });
      }
      
      this.$emit('update:step-data', this.localStepData);
      this.cancelMemberForm();
    },
    
    cancelMemberForm() {
      this.showAddMemberForm = false;
      this.isEditingMember = false;
      this.editingMemberIndex = -1;
      this.newMember = {
        id: null,
        firstName: '',
        lastName: '',
        email: '',
        role: 'editor'
      };
    }
  }
};
</script>

<style scoped>
.user-account-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--theme--foreground);
}

.section-description {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin: 0 0 16px 0;
}

.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--theme--foreground);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  border-color: var(--theme--primary);
  outline: none;
}

.empty-state {
  padding: 24px;
  text-align: center;
  color: var(--theme--foreground-subdued);
  background-color: var(--theme--background);
  border-radius: 4px;
  margin-bottom: 16px;
}

.team-members-list {
  margin-bottom: 16px;
}

.team-member-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--theme--background);
  border-radius: 4px;
  margin-bottom: 8px;
}

.team-member-info {
  display: flex;
  flex-direction: column;
}

.team-member-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.team-member-email {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 4px;
}

.team-member-role {
  font-size: 12px;
  color: var(--theme--primary);
  font-weight: 500;
}

.team-member-actions {
  display: flex;
  gap: 8px;
}

.edit-button,
.delete-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: none;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--primary);
}

.delete-button:hover {
  background-color: rgba(var(--theme--danger-rgb), 0.1);
  color: var(--theme--danger);
}

.add-team-member {
  margin-top: 16px;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px dashed var(--theme--border-color);
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.team-member-form {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--theme--background);
  border-radius: 4px;
}

.form-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.cancel-button,
.save-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid var(--theme--border-color);
  color: var(--theme--foreground);
}

.save-button {
  background-color: var(--theme--primary);
  border: none;
  color: var(--theme--primary-background);
}

.cancel-button:hover {
  background-color: var(--theme--background-accent);
}

.save-button:hover {
  background-color: var(--theme--primary-accent);
}

.save-button:disabled {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
  cursor: not-allowed;
}
</style>
