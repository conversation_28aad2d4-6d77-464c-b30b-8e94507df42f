import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import PreviewTestingTools from '../src/components/PreviewTestingTools.vue';
import LivePreview from '../src/components/LivePreview.vue';
import DevicePreview from '../src/components/DevicePreview.vue';
import ABTestingFramework from '../src/components/ABTestingFramework.vue';
import PerformanceTestingTools from '../src/components/PerformanceTestingTools.vue';

// Mock the child components
vi.mock('../src/components/LivePreview.vue', () => ({
  default: {
    name: 'LivePreview',
    render: h => h('div', { class: 'mock-live-preview' }),
    props: ['initialMode', 'initialUrl', 'initialData'],
  },
}));

vi.mock('../src/components/DevicePreview.vue', () => ({
  default: {
    name: 'DevicePreview',
    render: h => h('div', { class: 'mock-device-preview' }),
    props: ['src'],
  },
}));

vi.mock('../src/components/ABTestingFramework.vue', () => ({
  default: {
    name: 'ABTestingFramework',
    render: h => h('div', { class: 'mock-ab-testing-framework' }),
  },
}));

vi.mock('../src/components/PerformanceTestingTools.vue', () => ({
  default: {
    name: 'PerformanceTestingTools',
    render: h => h('div', { class: 'mock-performance-testing-tools' }),
  },
}));

describe('PreviewTestingTools', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(PreviewTestingTools, {
      propsData: {
        initialUrl: 'https://test.example.com',
        initialData: { test: 'data' },
      },
    });
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.preview-testing-tools').exists()).toBe(true);
  });

  it('renders the tabs correctly', () => {
    const tabs = wrapper.findAll('.tab-button');
    expect(tabs.length).toBe(4);

    // Check tab names using .at() method for Vue Test Utils v1
    expect(tabs.at(0).text()).toContain('Live Preview');
    expect(tabs.at(1).text()).toContain('Device Preview');
    expect(tabs.at(2).text()).toContain('A/B Testing');
    expect(tabs.at(3).text()).toContain('Performance');
  });

  it('shows the LivePreview component by default', () => {
    expect(wrapper.findComponent(LivePreview).exists()).toBe(true);
    expect(wrapper.findComponent(DevicePreview).exists()).toBe(false);
    expect(wrapper.findComponent(ABTestingFramework).exists()).toBe(false);
    expect(wrapper.findComponent(PerformanceTestingTools).exists()).toBe(false);
  });

  it('passes the correct props to LivePreview', () => {
    const livePreview = wrapper.findComponent(LivePreview);
    expect(livePreview.props('initialMode')).toBe('edit');
    expect(livePreview.props('initialUrl')).toBe('https://test.example.com');
    expect(livePreview.props('initialData')).toEqual({ test: 'data' });
  });

  it('switches to DevicePreview when clicking the Device Preview tab', async () => {
    // Find and click the Device Preview tab
    const devicePreviewTab = wrapper.findAll('.tab-button').at(1);
    await devicePreviewTab.trigger('click');

    // Check that the correct component is shown
    expect(wrapper.findComponent(LivePreview).exists()).toBe(false);
    expect(wrapper.findComponent(DevicePreview).exists()).toBe(true);
    expect(wrapper.findComponent(ABTestingFramework).exists()).toBe(false);
    expect(wrapper.findComponent(PerformanceTestingTools).exists()).toBe(false);

    // Check that the correct props are passed
    const devicePreview = wrapper.findComponent(DevicePreview);
    expect(devicePreview.props('src')).toBe('https://test.example.com');
  });

  it('switches to ABTestingFramework when clicking the A/B Testing tab', async () => {
    // Find and click the A/B Testing tab
    const abTestingTab = wrapper.findAll('.tab-button').at(2);
    await abTestingTab.trigger('click');

    // Check that the correct component is shown
    expect(wrapper.findComponent(LivePreview).exists()).toBe(false);
    expect(wrapper.findComponent(DevicePreview).exists()).toBe(false);
    expect(wrapper.findComponent(ABTestingFramework).exists()).toBe(true);
    expect(wrapper.findComponent(PerformanceTestingTools).exists()).toBe(false);
  });

  it('switches to PerformanceTestingTools when clicking the Performance tab', async () => {
    // Find and click the Performance tab
    const performanceTab = wrapper.findAll('.tab-button').at(3);
    await performanceTab.trigger('click');

    // Check that the correct component is shown
    expect(wrapper.findComponent(LivePreview).exists()).toBe(false);
    expect(wrapper.findComponent(DevicePreview).exists()).toBe(false);
    expect(wrapper.findComponent(ABTestingFramework).exists()).toBe(false);
    expect(wrapper.findComponent(PerformanceTestingTools).exists()).toBe(true);
  });

  it('emits preview-change event when LivePreview emits change', async () => {
    const livePreview = wrapper.findComponent(LivePreview);
    const testData = {
      mode: 'preview',
      data: { test: 'updated' },
      url: 'https://updated.example.com',
    };

    // Simulate the LivePreview component emitting a change event
    await livePreview.vm.$emit('change', testData);

    // Check that the PreviewTestingTools component re-emits the event
    expect(wrapper.emitted('preview-change')).toBeTruthy();
    expect(wrapper.emitted('preview-change')[0][0]).toEqual(testData);
  });
});
