import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { AssetService } from '../../services/asset-service';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  asset_id: z.string().uuid(),
  include_versions: z.enum(['true', 'false']).optional().default('false'),
  include_tags: z.enum(['true', 'false']).optional().default('false'),
});

/**
 * Asset API endpoint
 *
 * This endpoint returns information about a specific asset.
 * It can optionally include versions and tags.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse({
      ...req.query,
      asset_id: req.query.asset_id,
    });

    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { asset_id, include_versions, include_tags } = queryResult.data;
    const includeVersions = include_versions === 'true';
    const includeTags = include_tags === 'true';

    // Log the request
    logger.info('Asset request', {
      asset_id,
      include_versions: includeVersions,
      include_tags: includeTags,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create asset service
    const assetService = new AssetService(supabase);

    // Get asset
    const asset = await assetService.getAsset(asset_id);
    if (!asset) {
      return res.status(404).json({ error: 'Asset not found' });
    }

    // Prepare response
    const response: any = { asset };

    // Include versions if requested
    if (includeVersions) {
      const versions = await assetService.getAssetVersions(asset_id);
      response.versions = versions;
    }

    // Include tags if requested
    if (includeTags) {
      const { data: tags, error: tagsError } = await supabase
        .from('asset_tags')
        .select('*')
        .eq('asset_id', asset_id);

      if (tagsError) {
        logger.error('Error fetching asset tags', { error: tagsError, asset_id });
        // Continue anyway, just don't include tags
      } else {
        response.tags = tags;
      }
    }

    // Return asset
    return res.status(200).json(response);
  } catch (error) {
    logger.error('Unexpected error in asset endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
