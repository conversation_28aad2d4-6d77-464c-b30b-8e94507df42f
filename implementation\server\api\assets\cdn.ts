/**
 * CDN Integration API
 *
 * This API provides endpoints for interacting with CDN providers for asset delivery.
 */

import { Router } from 'express';
import { createClient } from '@supabase/supabase-js';
import {
  CdnIntegrationService,
  CdnProviderType,
  CacheControlConfig,
  CdnPurgeOptions,
} from '../../services/asset/cdn-integration-service';
import { logger } from '../../shared/utils/logger';
import { supabase } from '../../shared/utils/supabase-client';

const router = Router();
const cdnService = new CdnIntegrationService(supabase);

/**
 * Get CDN URL for an asset
 *
 * @route GET /api/assets/cdn/:assetId
 * @param {string} assetId - Asset ID
 * @query {string} providerId - Provider ID (optional)
 * @returns {object} CDN URL for the asset
 */
router.get('/:assetId', async (req, res) => {
  try {
    const { assetId } = req.params;
    const { providerId } = req.query;

    // Validate asset ID
    if (!assetId) {
      return res.status(400).json({ error: 'Asset ID is required' });
    }

    // Get CDN URL
    const cdnUrl = cdnService.getCdnUrl(assetId, providerId as string);

    return res.json({ cdnUrl });
  } catch (error) {
    logger.error('Error getting CDN URL', { error });
    return res.status(500).json({ error: 'Error getting CDN URL' });
  }
});

/**
 * Get regional CDN URL for an asset
 *
 * @route GET /api/assets/cdn/:assetId/regional
 * @param {string} assetId - Asset ID
 * @query {string} region - User region
 * @returns {object} CDN URL for the asset optimized for the user's region
 */
router.get('/:assetId/regional', async (req, res) => {
  try {
    const { assetId } = req.params;
    const { region } = req.query;

    // Validate asset ID and region
    if (!assetId) {
      return res.status(400).json({ error: 'Asset ID is required' });
    }
    if (!region) {
      return res.status(400).json({ error: 'Region is required' });
    }

    // Get regional CDN URL
    const cdnUrl = cdnService.getRegionalCdnUrl(assetId, region as string);

    return res.json({ cdnUrl });
  } catch (error) {
    logger.error('Error getting regional CDN URL', { error });
    return res.status(500).json({ error: 'Error getting regional CDN URL' });
  }
});

/**
 * Generate cache control header
 *
 * @route POST /api/assets/cdn/cache-control
 * @body {object} config - Cache control configuration
 * @returns {object} Cache control header value
 */
router.post('/cache-control', async (req, res) => {
  try {
    const config = req.body as Partial<CacheControlConfig>;

    // Generate cache control header
    const cacheControl = cdnService.generateCacheControlHeader(config);

    return res.json({ cacheControl });
  } catch (error) {
    logger.error('Error generating cache control header', { error });
    return res.status(500).json({ error: 'Error generating cache control header' });
  }
});

/**
 * Purge CDN cache
 *
 * @route POST /api/assets/cdn/purge
 * @body {object} options - Purge options
 * @body {string} providerId - Provider ID (optional)
 * @returns {object} Result of purge operation
 */
router.post('/purge', async (req, res) => {
  try {
    const { options, providerId } = req.body;

    // Validate options
    if (!options) {
      return res.status(400).json({ error: 'Purge options are required' });
    }

    // Purge cache
    const result = await cdnService.purgeCache(options as CdnPurgeOptions, providerId);

    return res.json(result);
  } catch (error) {
    logger.error('Error purging CDN cache', { error });
    return res.status(500).json({ error: 'Error purging CDN cache' });
  }
});

/**
 * Configure origin shield
 *
 * @route POST /api/assets/cdn/origin-shield
 * @body {string} providerId - Provider ID
 * @body {string} region - Region for origin shield
 * @returns {object} Result of configuration
 */
router.post('/origin-shield', async (req, res) => {
  try {
    const { providerId, region } = req.body;

    // Validate provider ID and region
    if (!providerId) {
      return res.status(400).json({ error: 'Provider ID is required' });
    }
    if (!region) {
      return res.status(400).json({ error: 'Region is required' });
    }

    // Configure origin shield
    const result = await cdnService.configureOriginShield(providerId, region);

    return res.json(result);
  } catch (error) {
    logger.error('Error configuring origin shield', { error });
    return res.status(500).json({ error: 'Error configuring origin shield' });
  }
});

/**
 * Get CDN providers
 *
 * @route GET /api/assets/cdn/providers
 * @returns {object} List of CDN providers
 */
router.get('/providers', async (req, res) => {
  try {
    // Get providers from database
    const { data, error } = await supabase.from('cdn_providers').select('*');

    if (error) {
      throw error;
    }

    return res.json({ providers: data });
  } catch (error) {
    logger.error('Error getting CDN providers', { error });
    return res.status(500).json({ error: 'Error getting CDN providers' });
  }
});

/**
 * Create CDN provider
 *
 * @route POST /api/assets/cdn/providers
 * @body {object} provider - Provider configuration
 * @returns {object} Created provider
 */
router.post('/providers', async (req, res) => {
  try {
    const provider = req.body;

    // Validate provider
    if (
      !provider.name ||
      !provider.type ||
      !provider.baseUrl ||
      !provider.apiUrl ||
      !provider.apiKey
    ) {
      return res
        .status(400)
        .json({ error: 'Provider name, type, baseUrl, apiUrl, and apiKey are required' });
    }

    // Create provider in database
    const { data, error } = await supabase
      .from('cdn_providers')
      .insert({
        name: provider.name,
        type: provider.type,
        base_url: provider.baseUrl,
        api_url: provider.apiUrl,
        api_key: provider.apiKey,
        zone_id: provider.zoneId,
        service_id: provider.serviceId,
        distribution_id: provider.distributionId,
        origin_shield_region: provider.originShieldRegion,
        additional_config: provider.additionalConfig,
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return res.json({ provider: data });
  } catch (error) {
    logger.error('Error creating CDN provider', { error });
    return res.status(500).json({ error: 'Error creating CDN provider' });
  }
});

export default router;
