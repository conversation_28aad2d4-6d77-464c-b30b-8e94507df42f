# Authentication Integration Endpoints for Directus

This extension provides custom endpoints for integrating authentication between Directus and Supabase. It enables seamless authentication flow between the two systems, allowing users to authenticate with Supabase and use the same credentials to access Directus.

## Features

- Login with Supabase credentials and get Directus token
- Create users in both Supabase and Directus
- Update user information in both systems
- Validate Supabase token and get Directus token
- Role synchronization between Supabase and Directus

## Endpoints

### Login

**POST /auth-integration/login**

Authenticates a user with Supabase and returns tokens for both Supabase and Directus.

**Request:**

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**

```json
{
  "data": {
    "supabase": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expires_at": 1630000000
    },
    "directus": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": null
    },
    "user": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "role": "vendor_admin",
      "vendor_id": "456e7890-e12b-34d5-a678-426614174000"
    }
  }
}
```

### Create User

**POST /auth-integration/users**

Creates a new user in both Supabase and Directus.

**Request:**

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "Jane",
  "last_name": "Smith",
  "role": "vendor_editor",
  "vendor_id": "456e7890-e12b-34d5-a678-426614174000"
}
```

**Response:**

```json
{
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Smith",
    "role": "vendor_editor",
    "vendor_id": "456e7890-e12b-34d5-a678-426614174000"
  }
}
```

### Update User

**PATCH /auth-integration/users/:id**

Updates a user in both Supabase and Directus.

**Request:**

```json
{
  "first_name": "Jane",
  "last_name": "Doe",
  "role": "vendor_admin"
}
```

**Response:**

```json
{
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "first_name": "Jane",
    "last_name": "Doe",
    "role": "vendor_admin",
    "vendor_id": "456e7890-e12b-34d5-a678-426614174000"
  }
}
```

### Validate Token

**POST /auth-integration/validate-token**

Validates a Supabase token and returns a Directus token.

**Request:**

```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**

```json
{
  "data": {
    "directus": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": null
    },
    "user": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "role": "vendor_admin",
      "vendor_id": "456e7890-e12b-34d5-a678-426614174000"
    }
  }
}
```

## Role Mapping

The extension maps Supabase roles to Directus roles as follows:

| Supabase Role     | Directus Role     |
| ----------------- | ----------------- |
| super_admin       | System Admin      |
| admin             | Zone Admin        |
| support_admin     | Support Admin     |
| vendor_admin      | Vendor Admin      |
| vendor_manager    | Vendor Manager    |
| vendor_editor     | Vendor Editor     |
| vendor_analyst    | Vendor Analyst    |
| client_admin      | Client Admin      |
| client_designer   | Client Designer   |
| client_registered | Client Registered |

## Installation

1. Copy the extension files to your Directus extensions directory:

```
extensions/
└── endpoints/
    └── auth-integration/
        ├── package.json
        └── src/
            └── index.js
```

2. Install dependencies:

```bash
cd extensions/endpoints/auth-integration
npm install
```

3. Build the extension:

```bash
npm run build
```

4. Restart Directus

## Configuration

The extension requires the following environment variables:

- `NEXT_PUBLIC_SUPABASE_URL` - Supabase URL
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key
- `SECRET` - Directus secret key

## Security Considerations

- The extension uses Supabase's service role key, which has full access to the database. Make sure to keep this key secure.
- The extension validates user permissions before allowing certain operations.
- Only administrators can create new users or update user roles.
- Users can only update their own information unless they are administrators.

## Error Handling

The extension handles various error scenarios:

- Invalid credentials
- User not found
- Permission denied
- Service unavailable
- Invalid payload

Each error returns an appropriate HTTP status code and error message.

## License

This extension is licensed under the MIT License.
