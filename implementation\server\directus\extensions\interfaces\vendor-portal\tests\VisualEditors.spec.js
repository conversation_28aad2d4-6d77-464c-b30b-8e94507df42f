import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import VisualEditors from '../src/components/VisualEditors/VisualEditors.vue';

// Mock the API
const mockApi = {
  get: vi.fn(),
};

describe('VisualEditors', () => {
  let wrapper;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock API responses
    mockApi.get.mockImplementation(url => {
      if (url.includes('showroom_layouts')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'showroom1', name: 'Showroom 1' },
              { id: 'showroom2', name: 'Showroom 2' },
            ],
          },
        });
      } else if (url.includes('products')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'product1', name: 'Product 1' },
              { id: 'product2', name: 'Product 2' },
            ],
          },
        });
      } else if (url.includes('materials')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'material1', name: 'Material 1' },
              { id: 'material2', name: 'Material 2' },
            ],
          },
        });
      } else if (url.includes('animations')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'animation1', name: 'Animation 1' },
              { id: 'animation2', name: 'Animation 2' },
            ],
          },
        });
      }
      return Promise.resolve({ data: { data: [] } });
    });

    // Mount component
    wrapper = mount(VisualEditors, {
      propsData: {
        vendorId: 'vendor1',
      },
      mocks: {
        $api: mockApi,
      },
      stubs: {
        ShowroomLayoutEditor: true,
        ProductConfigurator: true,
        MaterialTextureEditor: true,
        LightingEditor: true,
        AnimationEditor: true,
      },
    });
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.visual-editors').exists()).toBe(true);
  });

  it('renders the tabs correctly', () => {
    const tabs = wrapper.findAll('.tab-button');
    expect(tabs.length).toBe(5);

    // Check tab names
    expect(tabs[0].text()).toContain('Showroom Layout');
    expect(tabs[1].text()).toContain('Product Config');
    expect(tabs[2].text()).toContain('Materials');
    expect(tabs[3].text()).toContain('Lighting');
    expect(tabs[4].text()).toContain('Animation');
  });

  it('loads initial data on mount', async () => {
    // Wait for promises to resolve
    await wrapper.vm.$nextTick();

    // Check API calls
    expect(mockApi.get).toHaveBeenCalledTimes(4);
    expect(mockApi.get).toHaveBeenCalledWith(
      `/items/showroom_layouts?filter[vendor_id][_eq]=vendor1`,
    );
    expect(mockApi.get).toHaveBeenCalledWith(`/items/products?filter[vendor_id][_eq]=vendor1`);
    expect(mockApi.get).toHaveBeenCalledWith(`/items/materials?filter[vendor_id][_eq]=vendor1`);
    expect(mockApi.get).toHaveBeenCalledWith(`/items/animations?filter[vendor_id][_eq]=vendor1`);

    // Check data is loaded
    expect(wrapper.vm.showrooms.length).toBe(2);
    expect(wrapper.vm.products.length).toBe(2);
    expect(wrapper.vm.materials.length).toBe(2);
    expect(wrapper.vm.animations.length).toBe(2);
  });

  it('changes active tab when tab is clicked', async () => {
    // Initial tab should be 'layout'
    expect(wrapper.vm.activeTab).toBe('layout');

    // Click on the product tab
    await wrapper.findAll('.tab-button')[1].trigger('click');
    expect(wrapper.vm.activeTab).toBe('product');

    // Click on the material tab
    await wrapper.findAll('.tab-button')[2].trigger('click');
    expect(wrapper.vm.activeTab).toBe('material');

    // Click on the lighting tab
    await wrapper.findAll('.tab-button')[3].trigger('click');
    expect(wrapper.vm.activeTab).toBe('lighting');

    // Click on the animation tab
    await wrapper.findAll('.tab-button')[4].trigger('click');
    expect(wrapper.vm.activeTab).toBe('animation');
  });

  it('shows the correct editor component based on active tab', async () => {
    // Initial tab is 'layout', so ShowroomLayoutEditor should be visible
    expect(wrapper.find('.mock-showroom-layout-editor').exists()).toBe(true);

    // Change to product tab
    await wrapper.findAll('.tab-button')[1].trigger('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.find('.mock-product-configurator').exists()).toBe(true);

    // Change to material tab
    await wrapper.findAll('.tab-button')[2].trigger('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.find('.mock-material-texture-editor').exists()).toBe(true);

    // Change to lighting tab
    await wrapper.findAll('.tab-button')[3].trigger('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.find('.mock-lighting-editor').exists()).toBe(true);

    // Change to animation tab
    await wrapper.findAll('.tab-button')[4].trigger('click');
    await wrapper.vm.$nextTick();
    expect(wrapper.find('.mock-animation-editor').exists()).toBe(true);
  });

  it('handles update events from child components', async () => {
    const mockEmit = vi.fn();
    wrapper.vm.$emit = mockEmit;

    // Test layout update
    wrapper.vm.handleLayoutUpdate({ id: 'layout1', name: 'Updated Layout' });
    expect(mockEmit).toHaveBeenCalledWith('update', {
      type: 'layout',
      data: { id: 'layout1', name: 'Updated Layout' },
    });

    // Test product update
    wrapper.vm.handleProductUpdate({ id: 'product1', name: 'Updated Product' });
    expect(mockEmit).toHaveBeenCalledWith('update', {
      type: 'product',
      data: { id: 'product1', name: 'Updated Product' },
    });

    // Test material update
    wrapper.vm.handleMaterialUpdate({ id: 'material1', name: 'Updated Material' });
    expect(mockEmit).toHaveBeenCalledWith('update', {
      type: 'material',
      data: { id: 'material1', name: 'Updated Material' },
    });

    // Test lighting update
    wrapper.vm.handleLightingUpdate({ id: 'lighting1', name: 'Updated Lighting' });
    expect(mockEmit).toHaveBeenCalledWith('update', {
      type: 'lighting',
      data: { id: 'lighting1', name: 'Updated Lighting' },
    });

    // Test animation update
    wrapper.vm.handleAnimationUpdate({ id: 'animation1', name: 'Updated Animation' });
    expect(mockEmit).toHaveBeenCalledWith('update', {
      type: 'animation',
      data: { id: 'animation1', name: 'Updated Animation' },
    });
  });

  it('toggles auto-save when checkbox is clicked', async () => {
    // Auto-save should be enabled by default
    expect(wrapper.vm.autoSaveEnabled).toBe(true);

    // Click the auto-save toggle
    await wrapper.find('.auto-save-toggle input').trigger('change');
    expect(wrapper.vm.autoSaveEnabled).toBe(false);

    // Click it again
    await wrapper.find('.auto-save-toggle input').trigger('change');
    expect(wrapper.vm.autoSaveEnabled).toBe(true);
  });

  it('updates the last saved timestamp when an update occurs', async () => {
    // Initially lastSaved should be null
    expect(wrapper.vm.lastSaved).toBe(null);

    // Trigger an update
    wrapper.vm.handleLayoutUpdate({ id: 'layout1', name: 'Updated Layout' });

    // lastSaved should now be a Date object
    expect(wrapper.vm.lastSaved).toBeInstanceOf(Date);
  });

  it('handles API errors gracefully', async () => {
    // Reset the wrapper
    if (wrapper && wrapper.destroy) {
      wrapper.destroy();
    }

    // Mock API to throw an error
    mockApi.get.mockRejectedValue(new Error('API Error'));

    // Mount component again
    wrapper = mount(VisualEditors, {
      propsData: {
        vendorId: 'vendor1',
      },
      mocks: {
        $api: mockApi,
      },
      stubs: {
        ShowroomLayoutEditor: {
          name: 'ShowroomLayoutEditor',
          props: ['vendorId', 'showroomId'],
          template: '<div class="mock-showroom-layout-editor">Showroom Layout Editor Mock</div>',
        },
        ProductConfigurator: {
          name: 'ProductConfigurator',
          props: ['vendorId', 'productId'],
          template: '<div class="mock-product-configurator">Product Configurator Mock</div>',
        },
        MaterialTextureEditor: {
          name: 'MaterialTextureEditor',
          props: ['vendorId', 'materialId'],
          template: '<div class="mock-material-texture-editor">Material Texture Editor Mock</div>',
        },
        LightingEditor: {
          name: 'LightingEditor',
          props: ['vendorId', 'showroomId'],
          template: '<div class="mock-lighting-editor">Lighting Editor Mock</div>',
        },
        AnimationEditor: {
          name: 'AnimationEditor',
          props: ['vendorId', 'animationId'],
          template: '<div class="mock-animation-editor">Animation Editor Mock</div>',
        },
      },
    });

    // Wait for promises to resolve
    await wrapper.vm.$nextTick();

    // Check error state
    expect(wrapper.vm.error).toBe('Failed to load data. Please try again.');
    expect(wrapper.find('.error-container').exists()).toBe(true);
    expect(wrapper.find('.error-message').text()).toBe('Failed to load data. Please try again.');
  });
});
