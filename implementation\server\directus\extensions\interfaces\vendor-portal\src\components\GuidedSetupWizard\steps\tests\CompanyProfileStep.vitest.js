import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import CompanyProfileStep from '../CompanyProfileStep.vue';

// Mock the WizardStep component
vi.mock('../../WizardStep.vue', () => ({
  name: 'WizardStep',
  render: h => h('div', { class: 'wizard-step-mock' }, [h('slot')]),
  props: ['title', 'description', 'stepIndex', 'isActive', 'isCompleted']
}));

describe('CompanyProfileStep', () => {
  let wrapper;
  const mockStepData = {
    companyName: '',
    companyWebsite: '',
    companyLogo: null,
    industry: '',
    companySize: '',
    address: {
      street: '',
      city: '',
      state: '',
      postalCode: '',
      country: ''
    },
    contactInfo: {
      phone: '',
      email: ''
    }
  };

  beforeEach(() => {
    wrapper = mount(CompanyProfileStep, {
      propsData: {
        stepData: { ...mockStepData }
      }
    });
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.wizard-step-mock').exists()).toBe(true);
  });

  it('renders all form fields', () => {
    expect(wrapper.find('[data-test="company-name"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="company-website"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="company-logo"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="industry"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="company-size"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="address-street"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="address-city"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="address-state"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="address-postal-code"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="address-country"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="contact-phone"]').exists()).toBe(true);
    expect(wrapper.find('[data-test="contact-email"]').exists()).toBe(true);
  });

  it('updates stepData when form fields change', async () => {
    // Update company name
    const companyNameInput = wrapper.find('[data-test="company-name"] input');
    await companyNameInput.setValue('Test Company');
    expect(wrapper.vm.stepData.companyName).toBe('Test Company');

    // Update company website
    const companyWebsiteInput = wrapper.find('[data-test="company-website"] input');
    await companyWebsiteInput.setValue('https://example.com');
    expect(wrapper.vm.stepData.companyWebsite).toBe('https://example.com');

    // Update industry
    const industrySelect = wrapper.find('[data-test="industry"] select');
    await industrySelect.setValue('technology');
    expect(wrapper.vm.stepData.industry).toBe('technology');

    // Update company size
    const companySizeSelect = wrapper.find('[data-test="company-size"] select');
    await companySizeSelect.setValue('medium');
    expect(wrapper.vm.stepData.companySize).toBe('medium');

    // Update address fields
    const streetInput = wrapper.find('[data-test="address-street"] input');
    await streetInput.setValue('123 Main St');
    expect(wrapper.vm.stepData.address.street).toBe('123 Main St');

    const cityInput = wrapper.find('[data-test="address-city"] input');
    await cityInput.setValue('Anytown');
    expect(wrapper.vm.stepData.address.city).toBe('Anytown');

    const stateInput = wrapper.find('[data-test="address-state"] input');
    await stateInput.setValue('CA');
    expect(wrapper.vm.stepData.address.state).toBe('CA');

    const postalCodeInput = wrapper.find('[data-test="address-postal-code"] input');
    await postalCodeInput.setValue('12345');
    expect(wrapper.vm.stepData.address.postalCode).toBe('12345');

    const countrySelect = wrapper.find('[data-test="address-country"] select');
    await countrySelect.setValue('US');
    expect(wrapper.vm.stepData.address.country).toBe('US');

    // Update contact info
    const phoneInput = wrapper.find('[data-test="contact-phone"] input');
    await phoneInput.setValue('************');
    expect(wrapper.vm.stepData.contactInfo.phone).toBe('************');

    const emailInput = wrapper.find('[data-test="contact-email"] input');
    await emailInput.setValue('<EMAIL>');
    expect(wrapper.vm.stepData.contactInfo.email).toBe('<EMAIL>');
  });

  it('validates form fields correctly', async () => {
    // Initially form should be invalid
    expect(wrapper.vm.isValid).toBe(false);

    // Fill in required fields
    await wrapper.find('[data-test="company-name"] input').setValue('Test Company');
    await wrapper.find('[data-test="industry"] select').setValue('technology');
    await wrapper.find('[data-test="company-size"] select').setValue('medium');
    await wrapper.find('[data-test="address-street"] input').setValue('123 Main St');
    await wrapper.find('[data-test="address-city"] input').setValue('Anytown');
    await wrapper.find('[data-test="address-state"] input').setValue('CA');
    await wrapper.find('[data-test="address-postal-code"] input').setValue('12345');
    await wrapper.find('[data-test="address-country"] select').setValue('US');
    await wrapper.find('[data-test="contact-phone"] input').setValue('************');
    await wrapper.find('[data-test="contact-email"] input').setValue('<EMAIL>');

    // Now form should be valid
    expect(wrapper.vm.isValid).toBe(true);

    // Test invalid email
    await wrapper.find('[data-test="contact-email"] input').setValue('invalid-email');
    expect(wrapper.vm.isValid).toBe(false);

    // Fix email
    await wrapper.find('[data-test="contact-email"] input').setValue('<EMAIL>');
    expect(wrapper.vm.isValid).toBe(true);

    // Test invalid website
    await wrapper.find('[data-test="company-website"] input').setValue('invalid-website');
    expect(wrapper.vm.isValid).toBe(false);

    // Fix website
    await wrapper.find('[data-test="company-website"] input').setValue('https://example.com');
    expect(wrapper.vm.isValid).toBe(true);
  });

  it('emits update:isValid event when validity changes', async () => {
    // Initially form is invalid
    expect(wrapper.emitted()['update:isValid']).toBeTruthy();
    expect(wrapper.emitted()['update:isValid'][0]).toEqual([false]);

    // Fill in required fields
    await wrapper.find('[data-test="company-name"] input').setValue('Test Company');
    await wrapper.find('[data-test="industry"] select').setValue('technology');
    await wrapper.find('[data-test="company-size"] select').setValue('medium');
    await wrapper.find('[data-test="address-street"] input').setValue('123 Main St');
    await wrapper.find('[data-test="address-city"] input').setValue('Anytown');
    await wrapper.find('[data-test="address-state"] input').setValue('CA');
    await wrapper.find('[data-test="address-postal-code"] input').setValue('12345');
    await wrapper.find('[data-test="address-country"] select').setValue('US');
    await wrapper.find('[data-test="contact-phone"] input').setValue('************');
    await wrapper.find('[data-test="contact-email"] input').setValue('<EMAIL>');

    // Check that update:isValid was emitted with true
    const updateIsValidEvents = wrapper.emitted()['update:isValid'];
    expect(updateIsValidEvents[updateIsValidEvents.length - 1]).toEqual([true]);
  });
});
