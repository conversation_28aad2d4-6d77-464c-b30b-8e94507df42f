# Upload and Deploy MVS-VR to DigitalOcean
# Run this script once SSH access is restored

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",  # Try root first, then vectorax
    [string]$SSHKeyPath = "C:\Users\<USER>\.ssh\id_rsa",
    [string]$ProjectPath = "/home/<USER>/mvs-vr-deployment"
)

Write-Host "🚀 MVS-VR Upload and Deploy Script" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

# Test SSH connection first
Write-Host "Testing SSH connection..." -ForegroundColor Yellow
try {
    $sshTest = ssh -i $SSHKeyPath -o ConnectTimeout=10 -o StrictHostKeyChecking=no $Username@$ServerIP "echo 'SSH_OK'" 2>&1
    if ($sshTest -match "SSH_OK") {
        Write-Host "✅ SSH connection successful!" -ForegroundColor Green
    } else {
        Write-Host "❌ SSH connection failed. Try these alternatives:" -ForegroundColor Red
        Write-Host "   1. Use DigitalOcean console instead" -ForegroundColor Yellow
        Write-Host "   2. Try with username 'vectorax': -Username vectorax" -ForegroundColor Yellow
        Write-Host "   3. Check SSH key path: $SSHKeyPath" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "❌ SSH error: $_" -ForegroundColor Red
    exit 1
}

# Create project directory
Write-Host "Creating project directory..." -ForegroundColor Yellow
ssh -i $SSHKeyPath $Username@$ServerIP "mkdir -p $ProjectPath"

# Upload files
Write-Host "Uploading project files..." -ForegroundColor Yellow
try {
    # Upload all files from current directory
    scp -i $SSHKeyPath -r * "$Username@$ServerIP`:$ProjectPath/"
    Write-Host "✅ Files uploaded successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ File upload failed: $_" -ForegroundColor Red
    exit 1
}

# Install Docker if needed
Write-Host "Checking Docker installation..." -ForegroundColor Yellow
$dockerCheck = ssh -i $SSHKeyPath $Username@$ServerIP "docker --version" 2>&1
if ($dockerCheck -match "Docker version") {
    Write-Host "✅ Docker is installed: $dockerCheck" -ForegroundColor Green
} else {
    Write-Host "Installing Docker..." -ForegroundColor Yellow
    ssh -i $SSHKeyPath $Username@$ServerIP @"
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
systemctl start docker
systemctl enable docker
"@
}

# Install Docker Compose if needed
Write-Host "Checking Docker Compose..." -ForegroundColor Yellow
$composeCheck = ssh -i $SSHKeyPath $Username@$ServerIP "docker-compose --version" 2>&1
if ($composeCheck -match "docker-compose version") {
    Write-Host "✅ Docker Compose is installed: $composeCheck" -ForegroundColor Green
} else {
    Write-Host "Installing Docker Compose..." -ForegroundColor Yellow
    ssh -i $SSHKeyPath $Username@$ServerIP @"
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-`$(uname -s)-`$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
"@
}

# Configure firewall
Write-Host "Configuring firewall..." -ForegroundColor Yellow
ssh -i $SSHKeyPath $Username@$ServerIP @"
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp
ufw --force enable
"@

# Deploy services
Write-Host "Deploying services..." -ForegroundColor Yellow
ssh -i $SSHKeyPath $Username@$ServerIP @"
cd $ProjectPath
docker-compose -f docker-compose.exported.yml down
docker-compose -f docker-compose.exported.yml up -d
"@

# Wait for services to start
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep 15

# Test deployment
Write-Host "Testing deployment..." -ForegroundColor Yellow

# Check containers
$containerStatus = ssh -i $SSHKeyPath $Username@$ServerIP "cd $ProjectPath && docker-compose -f docker-compose.exported.yml ps"
Write-Host "Container Status:" -ForegroundColor Gray
Write-Host $containerStatus -ForegroundColor Gray

# Test local connectivity
$localTest = ssh -i $SSHKeyPath $Username@$ServerIP "curl -s -o /dev/null -w '%{http_code}' http://localhost/"
if ($localTest -eq "200") {
    Write-Host "✅ Local port 80 test: SUCCESS" -ForegroundColor Green
} else {
    Write-Host "❌ Local port 80 test: FAILED (HTTP $localTest)" -ForegroundColor Red
}

# Test external connectivity
Write-Host "Testing external connectivity..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://$ServerIP/" -TimeoutSec 10
    Write-Host "✅ External port 80 test: SUCCESS (HTTP $($response.StatusCode))" -ForegroundColor Green
} catch {
    Write-Host "❌ External port 80 test: FAILED - $_" -ForegroundColor Red
}

# Show final status
Write-Host ""
Write-Host "🎉 Deployment completed!" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host ""
Write-Host "Test URLs:" -ForegroundColor Cyan
Write-Host "  Local:    http://localhost/" -ForegroundColor White
Write-Host "  External: http://$ServerIP/" -ForegroundColor White
Write-Host "  Domain:   http://mvs.kanousai.com/ (after DNS setup)" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Configure DNS: mvs.kanousai.com → $ServerIP" -ForegroundColor White
Write-Host "2. Test from browser: http://$ServerIP/" -ForegroundColor White
Write-Host "3. Monitor logs: ssh -i $SSHKeyPath $Username@$ServerIP 'cd $ProjectPath && docker-compose logs -f'" -ForegroundColor White

Write-Host ""
Write-Host "✅ Script completed!" -ForegroundColor Green
