# User‑Experience Strategy (Vendor & End‑User)

## 1  Research & Persona Validation

* **Vendor Personas:** <PERSON> Maker, SME Marketer, Enterprise Brand Mgr.
* **End‑User Personas:** Explorer, Goal‑Oriented Buyer, Casual Visitor.
* **Action 1.1** Conduct 5 moderated VR sessions per persona.

## 2  Iterative Testing Cadence

| Sprint Week | Activity         | Deliverable                  |
| ----------- | ---------------- | ---------------------------- |
| W‑1         | Prototype build  | Clickable Figma & VR build   |
| W‑2         | Lab test (8 pax) | Observation notes + heatmaps |
| W‑3         | Synthesize       | UX tickets in JIRA           |
| W‑4         | Implement fixes  | Release notes                |

## 3  Performance Budgets

* Mobile / Quest 3 target: **90 fps**.
* Desktop VR target: **120 fps**.
* Asset rule‑of‑thumb: ≤ 100k tris per room, ≤ 4 K texture per hero asset.

## 4  Accessibility Checklist

1. High‑contrast UI toggle.
2. Voice navigation duplicate to all menu flows.
3. Subtitles for avatar / TTS content.
4. Seated‑mode locomotion option.

## 5  Feedback Collection Loop

* **In‑App NPS Widget** (appears after 3 min of use).
* **Vendor Dashboard Surveys** monthly.
* Auto‑tag feedback to `design`, `performance`, `content`.


