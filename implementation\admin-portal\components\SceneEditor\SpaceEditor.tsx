import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, TextField, Grid, Paper, IconButton, List, ListItem, ListItemText, ListItemSecondaryAction, Dialog, DialogTitle, DialogContent, DialogActions, MenuItem, Select, FormControl, InputLabel } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { Space, AssetPlacement } from '../../types/scene';
import VectorInput from './VectorInput';

interface SpaceEditorProps {
  space: Space;
  onSave: (space: Space) => void;
  onCancel: () => void;
}

const SpaceEditor: React.FC<SpaceEditorProps> = ({ space, onSave, onCancel }) => {
  const supabase = useSupabaseClient();
  const [editSpace, setEditSpace] = useState<Space>({ ...space });
  const [assets, setAssets] = useState<any[]>([]);
  const [currentAsset, setCurrentAsset] = useState<AssetPlacement | null>(null);
  const [selectedAssetId, setSelectedAssetId] = useState<string>('');
  const [showAssetDialog, setShowAssetDialog] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadAssets();
  }, []);

  const loadAssets = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('assets')
        .select('id, name, type, vendor_id')
        .order('name');

      if (error) {
        console.error('Error loading assets:', error);
        return;
      }

      setAssets(data || []);
    } catch (err) {
      console.error('Error loading assets:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAddAsset = () => {
    if (assets.length === 0) {
      alert('No assets available. Please add assets first.');
      return;
    }

    setSelectedAssetId(assets[0].id);
    setCurrentAsset({
      id: '',
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scale: [1, 1, 1],
      properties: {}
    });
    setShowAssetDialog(true);
  };

  const handleEditAsset = (asset: AssetPlacement) => {
    setCurrentAsset({ ...asset });
    setSelectedAssetId(asset.id);
    setShowAssetDialog(true);
  };

  const handleDeleteAsset = (assetId: string) => {
    setEditSpace({
      ...editSpace,
      assets: editSpace.assets.filter(asset => asset.id !== assetId)
    });
  };

  const handleSaveAsset = () => {
    if (!currentAsset || !selectedAssetId) return;

    const updatedAssets = [...editSpace.assets];
    const newAsset = {
      ...currentAsset,
      id: selectedAssetId
    };

    const index = updatedAssets.findIndex(asset => asset.id === selectedAssetId);

    if (index >= 0) {
      updatedAssets[index] = newAsset;
    } else {
      updatedAssets.push(newAsset);
    }

    setEditSpace({
      ...editSpace,
      assets: updatedAssets
    });

    setShowAssetDialog(false);
    setCurrentAsset(null);
    setSelectedAssetId('');
  };

  const getAssetName = (assetId: string) => {
    const asset = assets.find(a => a.id === assetId);
    return asset ? asset.name : assetId;
  };

  return (
    <Paper sx={{ p: 2, mt: 3 }}>
      <Typography variant="h6" gutterBottom>
        {space.id ? 'Edit Space' : 'Add Space'}
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <TextField
            label="Name"
            fullWidth
            value={editSpace.name}
            onChange={(e) => setEditSpace({...editSpace, name: e.target.value})}
            margin="normal"
            required
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            label="Description"
            fullWidth
            value={editSpace.description}
            onChange={(e) => setEditSpace({...editSpace, description: e.target.value})}
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <VectorInput
            label="Position"
            value={editSpace.position}
            onChange={(value) => setEditSpace({...editSpace, position: value})}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <VectorInput
            label="Rotation"
            value={editSpace.rotation}
            onChange={(value) => setEditSpace({...editSpace, rotation: value})}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <VectorInput
            label="Scale"
            value={editSpace.scale}
            onChange={(value) => setEditSpace({...editSpace, scale: value})}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            label="Lighting"
            fullWidth
            value={editSpace.lighting || ''}
            onChange={(e) => setEditSpace({...editSpace, lighting: e.target.value})}
            margin="normal"
            helperText="Lighting preset for this space"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            label="Audio"
            fullWidth
            value={editSpace.audio || ''}
            onChange={(e) => setEditSpace({...editSpace, audio: e.target.value})}
            margin="normal"
            helperText="Audio preset for this space"
          />
        </Grid>
      </Grid>

      <Box sx={{ mt: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1">Assets ({editSpace.assets.length})</Typography>
          <Button 
            size="small" 
            startIcon={<AddIcon />}
            onClick={handleAddAsset}
          >
            Add Asset
          </Button>
        </Box>

        {editSpace.assets.length === 0 ? (
          <Typography variant="body2" color="textSecondary">
            No assets added yet. Click "Add Asset" to place assets in this space.
          </Typography>
        ) : (
          <List>
            {editSpace.assets.map((asset) => (
              <ListItem key={asset.id}>
                <ListItemText 
                  primary={getAssetName(asset.id)} 
                  secondary={`Position: [${asset.position.join(', ')}] | Rotation: [${asset.rotation.join(', ')}] | Scale: [${asset.scale.join(', ')}]`} 
                />
                <ListItemSecondaryAction>
                  <IconButton edge="end" onClick={() => handleDeleteAsset(asset.id)} sx={{ mr: 1 }}>
                    <DeleteIcon />
                  </IconButton>
                  <IconButton edge="end" onClick={() => handleEditAsset(asset)}>
                    <EditIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}
      </Box>

      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button onClick={onCancel} sx={{ mr: 1 }}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={() => onSave(editSpace)}
        >
          Save Space
        </Button>
      </Box>

      <Dialog open={showAssetDialog} onClose={() => setShowAssetDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{currentAsset?.id ? 'Edit Asset Placement' : 'Add Asset'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="asset-select-label">Asset</InputLabel>
                <Select
                  labelId="asset-select-label"
                  value={selectedAssetId}
                  onChange={(e) => setSelectedAssetId(e.target.value)}
                  label="Asset"
                >
                  {assets.map((asset) => (
                    <MenuItem key={asset.id} value={asset.id}>
                      {asset.name} ({asset.type})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            {currentAsset && (
              <>
                <Grid item xs={12} md={4}>
                  <VectorInput
                    label="Position"
                    value={currentAsset.position}
                    onChange={(value) => setCurrentAsset({...currentAsset, position: value})}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <VectorInput
                    label="Rotation"
                    value={currentAsset.rotation}
                    onChange={(value) => setCurrentAsset({...currentAsset, rotation: value})}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <VectorInput
                    label="Scale"
                    value={currentAsset.scale}
                    onChange={(value) => setCurrentAsset({...currentAsset, scale: value})}
                  />
                </Grid>
              </>
            )}
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAssetDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleSaveAsset} 
            variant="contained" 
            color="primary"
            disabled={!selectedAssetId}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default SpaceEditor;
