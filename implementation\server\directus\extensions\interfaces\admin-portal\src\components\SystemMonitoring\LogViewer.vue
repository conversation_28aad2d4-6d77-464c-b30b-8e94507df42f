<template>
  <div class="log-viewer">
    <v-skeleton-loader
      v-if="loading"
      type="card"
      class="mx-auto"
    ></v-skeleton-loader>
    
    <div v-else class="logs-content">
      <!-- Log Statistics -->
      <v-card class="mb-4">
        <v-card-title>Log Statistics</v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-text-box-multiple</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ logStats?.total_logs || 0 }}</div>
                  <div class="metric-label">Total Logs</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="9">
              <div class="log-level-distribution">
                <div class="log-level-item">
                  <div class="log-level-label">Info</div>
                  <v-progress-linear
                    :value="getLogLevelPercentage('info')"
                    color="info"
                    height="20"
                    striped
                  >
                    <template v-slot:default>
                      <strong>{{ logStats?.log_levels?.info || 0 }}</strong>
                    </template>
                  </v-progress-linear>
                </div>
                
                <div class="log-level-item">
                  <div class="log-level-label">Warning</div>
                  <v-progress-linear
                    :value="getLogLevelPercentage('warn')"
                    color="warning"
                    height="20"
                    striped
                  >
                    <template v-slot:default>
                      <strong>{{ logStats?.log_levels?.warn || 0 }}</strong>
                    </template>
                  </v-progress-linear>
                </div>
                
                <div class="log-level-item">
                  <div class="log-level-label">Error</div>
                  <v-progress-linear
                    :value="getLogLevelPercentage('error')"
                    color="error"
                    height="20"
                    striped
                  >
                    <template v-slot:default>
                      <strong>{{ logStats?.log_levels?.error || 0 }}</strong>
                    </template>
                  </v-progress-linear>
                </div>
                
                <div class="log-level-item">
                  <div class="log-level-label">Debug</div>
                  <v-progress-linear
                    :value="getLogLevelPercentage('debug')"
                    color="grey"
                    height="20"
                    striped
                  >
                    <template v-slot:default>
                      <strong>{{ logStats?.log_levels?.debug || 0 }}</strong>
                    </template>
                  </v-progress-linear>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <div class="period-selector">
                <v-btn-toggle
                  v-model="selectedPeriod"
                  mandatory
                  @change="periodChanged"
                >
                  <v-btn value="1h">1h</v-btn>
                  <v-btn value="6h">6h</v-btn>
                  <v-btn value="24h">24h</v-btn>
                  <v-btn value="7d">7d</v-btn>
                  <v-btn value="30d">30d</v-btn>
                </v-btn-toggle>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <div class="chart-container">
                <h3 class="chart-title">Log Trends</h3>
                <div class="chart-wrapper">
                  <canvas ref="logTrendsChart"></canvas>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      
      <!-- Log Viewer -->
      <v-card>
        <v-card-title>Log Viewer</v-card-title>
        
        <v-card-text>
          <!-- Log Filters -->
          <v-row>
            <v-col cols="12" md="3">
              <v-select
                v-model="filters.level"
                :items="logLevels"
                label="Log Level"
                outlined
                dense
                @change="applyFilters"
              ></v-select>
            </v-col>
            
            <v-col cols="12" md="3">
              <v-select
                v-model="filters.service"
                :items="serviceOptions"
                label="Service"
                outlined
                dense
                @change="applyFilters"
              ></v-select>
            </v-col>
            
            <v-col cols="12" md="4">
              <v-text-field
                v-model="filters.search"
                label="Search"
                outlined
                dense
                clearable
                append-icon="mdi-magnify"
                @click:append="applyFilters"
                @keyup.enter="applyFilters"
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="2">
              <v-btn
                color="primary"
                block
                @click="applyFilters"
              >
                Apply Filters
              </v-btn>
            </v-col>
          </v-row>
          
          <!-- Log Table -->
          <v-data-table
            :headers="logHeaders"
            :items="logs"
            :items-per-page="10"
            :loading="loading"
            class="elevation-1"
            @click:row="showLogDetails"
          >
            <template v-slot:item.timestamp="{ item }">
              {{ formatTimestamp(item.timestamp) }}
            </template>
            
            <template v-slot:item.level="{ item }">
              <v-chip
                small
                :color="getLogLevelColor(item.level)"
                text-color="white"
              >
                {{ item.level.toUpperCase() }}
              </v-chip>
            </template>
            
            <template v-slot:item.message="{ item }">
              <div class="log-message text-truncate">{{ item.message }}</div>
            </template>
          </v-data-table>
          
          <!-- Pagination -->
          <div class="pagination-controls">
            <v-btn
              :disabled="filters.offset === 0"
              @click="previousPage"
              icon
            >
              <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
            
            <span class="pagination-info">
              Showing {{ filters.offset + 1 }} - {{ Math.min(filters.offset + filters.limit, totalLogs) }} of {{ totalLogs }}
            </span>
            
            <v-btn
              :disabled="filters.offset + filters.limit >= totalLogs"
              @click="nextPage"
              icon
            >
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
      
      <!-- Log Details Dialog -->
      <v-dialog
        v-model="detailsDialog"
        max-width="800"
      >
        <v-card>
          <v-card-title class="headline">
            Log Details
          </v-card-title>
          
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Timestamp</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ formatTimestamp(selectedLog?.timestamp, true) }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Level</v-list-item-subtitle>
                    <v-list-item-title>
                      <v-chip
                        :color="getLogLevelColor(selectedLog?.level)"
                        text-color="white"
                        small
                      >
                        {{ selectedLog?.level?.toUpperCase() }}
                      </v-chip>
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Service</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ selectedLog?.service }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Request ID</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ selectedLog?.metadata?.request_id || 'N/A' }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Message</v-list-item-subtitle>
                    <v-list-item-title class="log-message-full">
                      {{ selectedLog?.message }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" v-if="selectedLog?.metadata">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Metadata</v-list-item-subtitle>
                    <v-list-item-title>
                      <pre class="metadata-json">{{ JSON.stringify(selectedLog.metadata, null, 2) }}</pre>
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
            </v-row>
          </v-card-text>
          
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="primary"
              text
              @click="detailsDialog = false"
            >
              Close
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'LogViewer',
  
  props: {
    logs: {
      type: Array,
      default: () => []
    },
    logStats: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      selectedPeriod: '24h',
      logTrendsChart: null,
      logLevels: [
        { text: 'All Levels', value: 'all' },
        { text: 'Info', value: 'info' },
        { text: 'Warning', value: 'warn' },
        { text: 'Error', value: 'error' },
        { text: 'Debug', value: 'debug' }
      ],
      serviceOptions: [
        { text: 'All Services', value: 'all' },
        { text: 'API Gateway', value: 'API Gateway' },
        { text: 'Authentication Service', value: 'Authentication Service' },
        { text: 'Database Service', value: 'Database Service' },
        { text: 'Storage Service', value: 'Storage Service' },
        { text: 'Analytics Service', value: 'Analytics Service' }
      ],
      filters: {
        level: 'all',
        service: 'all',
        search: '',
        limit: 10,
        offset: 0
      },
      totalLogs: 0,
      logHeaders: [
        { text: 'Timestamp', value: 'timestamp', width: '20%' },
        { text: 'Level', value: 'level', width: '10%' },
        { text: 'Service', value: 'service', width: '20%' },
        { text: 'Message', value: 'message', width: '50%' }
      ],
      detailsDialog: false,
      selectedLog: null
    };
  },
  
  watch: {
    logStats() {
      this.$nextTick(() => {
        this.initLogTrendsChart();
      });
    }
  },
  
  mounted() {
    this.$nextTick(() => {
      if (this.logStats) {
        this.initLogTrendsChart();
      }
      
      // Set total logs from props
      if (this.logs && this.logs.length > 0) {
        this.totalLogs = this.logs.length;
      }
    });
  },
  
  beforeDestroy() {
    if (this.logTrendsChart) {
      this.logTrendsChart.destroy();
    }
  },
  
  methods: {
    // Initialize log trends chart
    initLogTrendsChart() {
      if (!this.logStats || !this.logStats.log_level_trends) return;
      
      const ctx = this.$refs.logTrendsChart?.getContext('2d');
      if (!ctx) return;
      
      // Destroy existing chart if it exists
      if (this.logTrendsChart) {
        this.logTrendsChart.destroy();
      }
      
      // Prepare data for chart
      const datasets = [];
      
      for (const [level, data] of Object.entries(this.logStats.log_level_trends)) {
        datasets.push({
          label: data.name,
          data: data.data.map(point => point.value),
          borderColor: data.color,
          backgroundColor: data.color + '20',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        });
      }
      
      // Create chart
      this.logTrendsChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.logStats.log_level_trends.info.data.map(point => {
            const date = new Date(point.time);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          }),
          datasets: datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              stacked: true,
              title: {
                display: true,
                text: 'Log Count'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Time'
              }
            }
          }
        }
      });
    },
    
    // Get log level percentage
    getLogLevelPercentage(level) {
      if (!this.logStats || !this.logStats.log_levels || !this.logStats.total_logs) {
        return 0;
      }
      
      return (this.logStats.log_levels[level] / this.logStats.total_logs) * 100;
    },
    
    // Get log level color
    getLogLevelColor(level) {
      switch (level) {
        case 'info':
          return 'info';
        case 'warn':
          return 'warning';
        case 'error':
          return 'error';
        case 'debug':
          return 'grey';
        default:
          return 'grey';
      }
    },
    
    // Format timestamp
    formatTimestamp(timestamp, detailed = false) {
      if (!timestamp) return 'N/A';
      
      const date = new Date(timestamp);
      
      if (detailed) {
        return date.toLocaleString();
      }
      
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      
      if (diffMins < 60) {
        return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
      } else if (diffMins < 1440) {
        const hours = Math.floor(diffMins / 60);
        return `${hours} hour${hours === 1 ? '' : 's'} ago`;
      } else {
        return date.toLocaleDateString();
      }
    },
    
    // Apply filters
    applyFilters() {
      // Reset offset when applying new filters
      this.filters.offset = 0;
      
      this.$emit('search', this.filters);
    },
    
    // Previous page
    previousPage() {
      if (this.filters.offset >= this.filters.limit) {
        this.filters.offset -= this.filters.limit;
        this.$emit('search', this.filters);
      }
    },
    
    // Next page
    nextPage() {
      this.filters.offset += this.filters.limit;
      this.$emit('search', this.filters);
    },
    
    // Show log details
    showLogDetails(log) {
      this.selectedLog = log;
      this.detailsDialog = true;
    },
    
    // Handle period change
    periodChanged() {
      this.$emit('refresh', { period: this.selectedPeriod });
    }
  }
};
</script>

<style scoped>
.log-viewer {
  padding: 16px;
}

.logs-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  height: 100%;
}

.metric-icon {
  margin-right: 16px;
}

.metric-details {
  flex: 1;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 4px;
}

.metric-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.log-level-distribution {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.log-level-item {
  margin-bottom: 8px;
}

.log-level-label {
  margin-bottom: 4px;
  font-weight: 500;
}

.period-selector {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.chart-container {
  margin-top: 16px;
}

.chart-title {
  margin-bottom: 16px;
  font-size: 1.1rem;
  font-weight: 500;
}

.chart-wrapper {
  height: 300px;
}

.log-message {
  max-width: 100%;
}

.log-message-full {
  white-space: pre-wrap;
  word-break: break-word;
}

.metadata-json {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 0.875rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
}

.pagination-info {
  margin: 0 16px;
  color: rgba(0, 0, 0, 0.6);
}
</style>
