<template>
  <div class="tooltip-container">
    <div class="tooltip-trigger" @mouseenter="showTooltip" @mouseleave="hideTooltip">
      <slot name="trigger">
        <i class="material-icons tooltip-icon">help_outline</i>
      </slot>
    </div>
    <div v-if="isVisible" class="tooltip-content" :class="position">
      <slot>
        <div v-html="content"></div>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Tooltip',
  props: {
    content: {
      type: String,
      default: ''
    },
    position: {
      type: String,
      default: 'bottom',
      validator: (value) => ['top', 'right', 'bottom', 'left'].includes(value)
    },
    delay: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      isVisible: false,
      timeout: null
    };
  },
  methods: {
    showTooltip() {
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.isVisible = true;
      }, this.delay);
    },
    hideTooltip() {
      clearTimeout(this.timeout);
      this.isVisible = false;
    }
  },
  beforeDestroy() {
    clearTimeout(this.timeout);
  }
};
</script>

<style scoped>
.tooltip-container {
  position: relative;
  display: inline-block;
}

.tooltip-trigger {
  display: inline-block;
  cursor: help;
}

.tooltip-icon {
  font-size: 16px;
  color: var(--theme--foreground-subdued);
  vertical-align: middle;
}

.tooltip-content {
  position: absolute;
  z-index: 1000;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  padding: 8px 12px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  font-size: 14px;
  line-height: 1.4;
  white-space: normal;
  text-align: left;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
  border: 1px solid var(--theme--border-color);
}

.tooltip-content.top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
}

.tooltip-content.right {
  top: 50%;
  left: 100%;
  transform: translateY(-50%) translateX(8px);
}

.tooltip-content.bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(8px);
}

.tooltip-content.left {
  top: 50%;
  right: 100%;
  transform: translateY(-50%) translateX(-8px);
}

/* Arrow styles */
.tooltip-content::after {
  content: '';
  position: absolute;
  border-width: 5px;
  border-style: solid;
}

.tooltip-content.top::after {
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-color: var(--theme--border-color) transparent transparent transparent;
}

.tooltip-content.right::after {
  top: 50%;
  right: 100%;
  margin-top: -5px;
  border-color: transparent var(--theme--border-color) transparent transparent;
}

.tooltip-content.bottom::after {
  bottom: 100%;
  left: 50%;
  margin-left: -5px;
  border-color: transparent transparent var(--theme--border-color) transparent;
}

.tooltip-content.left::after {
  top: 50%;
  left: 100%;
  margin-top: -5px;
  border-color: transparent transparent transparent var(--theme--border-color);
}
</style>
