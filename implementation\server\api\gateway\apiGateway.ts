/**
 * API Gateway
 *
 * This module provides the main API Gateway for the MVS-VR platform.
 * It handles all incoming requests, routes them to the appropriate services,
 * and provides a unified interface for the platform.
 */

import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import path from 'path';
import { IntegrationManager } from '../../services/integration';
import { authenticateRequest } from '../middleware/auth';
import { validateRequest } from '../middleware/validation';
import { errorHandler } from '../../middleware/error-handler';

import requestQueue from '../../middleware/request-queue';
import createClientCapabilityDetectionMiddleware from '../middleware/client-capability-detection';
import { Logger, LogLevel } from '../../services/integration/logger';
import {
  apiLimiter,
  authLimiter,
  userLimiter,
  assetLimiter,
  isIpBlocked,
} from '../middleware/rate-limit-middleware';
import { setupApiDocs } from '../middleware/api-docs';
import { setupInteractiveApiDocs } from '../middleware/interactive-api-docs';

/**
 * API Gateway class
 */
export class ApiGateway {
  private app: express.Application;
  private integrationManager: IntegrationManager;
  private logger: Logger;
  private port: number;

  /**
   * Constructor
   *
   * @param port Port to listen on
   */
  constructor(port: number = 3000) {
    this.app = express();
    this.logger = new Logger(LogLevel.INFO);
    this.integrationManager = new IntegrationManager();
    this.port = port;

    // Configure middleware
    this.configureMiddleware();

    // Configure routes
    this.configureRoutes();

    // Configure error handling
    this.configureErrorHandling();
  }

  /**
   * Configure middleware
   */
  private configureMiddleware(): void {
    // Enable CORS
    this.app.use(cors());

    // Enable security headers
    this.app.use(helmet());

    // Add request queue for high concurrency
    this.app.use(
      requestQueue({
        name: 'api-gateway',
        concurrency: parseInt(process.env.REQUEST_QUEUE_CONCURRENCY || '100', 10),
        maxQueueSize: parseInt(process.env.REQUEST_QUEUE_MAX_SIZE || '1000', 10),
        timeout: parseInt(process.env.REQUEST_QUEUE_TIMEOUT || '30000', 10),
        priorityFn: context => {
          // Prioritize health checks and authentication requests
          if (context.path === '/health' || context.path === '/healthz') {
            return 100;
          }
          if (context.path === '/auth/login' || context.path === '/auth/token') {
            return 90;
          }
          // Prioritize API requests with valid API keys
          if (context.apiKey) {
            return 80;
          }
          // Default priority
          return 0;
        },
      }),
    );

    // Parse JSON bodies
    this.app.use(express.json());

    // Parse URL-encoded bodies
    this.app.use(express.urlencoded({ extended: true }));

    // Add comprehensive endpoint security suite
    const {
      applyEndpointSecuritySuite,
      SECURITY_LEVELS,
    } = require('../../middleware/endpoint-security-suite');

    // Determine security level based on environment
    const securityLevel =
      process.env.NODE_ENV === 'production' ? SECURITY_LEVELS.ENHANCED : SECURITY_LEVELS.STANDARD;

    applyEndpointSecuritySuite(this.app, {
      securityLevel,
      customConfig: {
        responseSanitization: {
          additionalSensitiveFields: [/api[-_]?key/i, /access[-_]?token/i, /refresh[-_]?token/i],
          logSanitization: process.env.NODE_ENV === 'development',
        },
      },
    });

    // Add global rate limiting
    this.app.use(apiLimiter);

    // Add specific rate limiters for different endpoint types
    this.app.use('/api/auth', authLimiter);
    this.app.use('/api/users', userLimiter);
    this.app.use('/api/assets', assetLimiter);

    // Block IPs with excessive abuse
    this.app.use(async (req: Request, res: Response, next: NextFunction) => {
      const clientIp = req.ip || req.connection.remoteAddress;
      if (await isIpBlocked(clientIp)) {
        // Track IP block event in monitoring service
        const { trackRateLimitEvent } = require('../middleware/rate-limit-middleware');
        const {
          trackRateLimitEvent: trackEvent,
        } = require('../../services/monitoring/rate-limit-monitor');

        await trackEvent('IP_BLOCKED', {
          ip: clientIp,
          path: req.originalUrl,
          method: req.method,
          userId: req.user?.id,
          apiKeyId: req.apiKey?.id,
          timestamp: Date.now(),
        });

        return res.status(403).json({
          success: false,
          error: {
            code: 'IP_BLOCKED',
            message:
              'Your IP address has been temporarily blocked due to excessive requests. Please try again later.',
          },
        });
      }
      next();
    });

    // Add client capability detection
    this.app.use(
      createClientCapabilityDetectionMiddleware({
        enableClientHints: true,
        enableUserAgentParsing: true,
        enableNetworkInformation: true,
        enableSaveToSession: true,
      }),
    );

    // Add request logging
    this.app.use((req: Request, _res: Response, next: NextFunction) => {
      this.logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        clientCapabilities: req.clientCapabilities,
      });
      next();
    });
  }

  /**
   * Configure routes
   */
  private configureRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_req: Request, res: Response) => {
      res.status(200).json({ status: 'ok' });
    });

    // API endpoints
    this.app.use('/api', this.configureApiRoutes());

    // API Documentation
    this.configureApiDocumentation();
  }

  /**
   * Configure API documentation
   */
  private configureApiDocumentation(): void {
    // Set up standard Swagger UI documentation
    setupApiDocs(this.app, {
      basePath: '/api-docs',
      yamlPath: path.join(__dirname, '../../api/docs/openapi.yaml'),
      swaggerOptions: {
        explorer: true,
        docExpansion: 'list',
        filter: true,
        showExtensions: true,
      },
      customCss: '.swagger-ui .topbar { display: none }',
    });

    // Set up interactive API documentation with Stoplight Elements
    setupInteractiveApiDocs(this.app, {
      basePath: '/api-docs/interactive',
      yamlPath: path.join(__dirname, '../../api/docs/openapi.yaml'),
      elementsOptions: {
        router: 'hash',
        layout: 'sidebar',
      },
    });

    // Serve static documentation assets
    this.app.use('/api-docs/assets', express.static(path.join(__dirname, '../../public/api-docs')));

    // Redirect root to API documentation
    this.app.get('/docs', (_req: Request, res: Response) => {
      res.redirect('/api-docs/interactive');
    });
  }

  /**
   * Configure API routes
   *
   * @returns Router for API routes
   */
  private configureApiRoutes(): express.Router {
    const router = express.Router();

    // Bootstrap endpoints
    router.use('/bootstrap', this.configureBootstrapRoutes());

    // Asset endpoints
    router.use('/asset', this.configureAssetRoutes());

    // Scene endpoints
    router.use('/scene', this.configureSceneRoutes());

    // Blueprint endpoints
    router.use('/blueprint', this.configureBlueprintRoutes());

    // Recovery dashboard endpoints
    router.use('/recovery-dashboard', this.configureRecoveryDashboardRoutes());

    // Business continuity endpoints
    router.use('/business-continuity', this.configureBusinessContinuityRoutes());

    return router;
  }

  /**
   * Configure bootstrap routes
   *
   * @returns Router for bootstrap routes
   */
  private configureBootstrapRoutes(): express.Router {
    const router = express.Router();

    // Authenticate all bootstrap requests
    router.use(authenticateRequest);

    // Get bootstrap configuration
    router.get(
      '/config',
      validateRequest('getBootstrapConfig'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: '/bootstrap/config',
            method: 'GET',
            query: req.query,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    return router;
  }

  /**
   * Configure asset routes
   *
   * @returns Router for asset routes
   */
  private configureAssetRoutes(): express.Router {
    const router = express.Router();

    // Authenticate all asset requests
    router.use(authenticateRequest);

    // Get asset bundle
    router.get(
      '/bundle/:id',
      validateRequest('getAssetBundle'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/asset/bundle/${req.params.id}`,
            method: 'GET',
            params: req.params,
            query: req.query,
            headers: req.headers,
            user: req.user,
            clientCapabilities: req.clientCapabilities,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Get asset metadata
    router.get(
      '/:id/metadata',
      validateRequest('getAssetMetadata'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/asset/${req.params.id}/metadata`,
            method: 'GET',
            params: req.params,
            query: req.query,
            headers: req.headers,
            user: req.user,
            clientCapabilities: req.clientCapabilities,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    return router;
  }

  /**
   * Configure scene routes
   *
   * @returns Router for scene routes
   */
  private configureSceneRoutes(): express.Router {
    const router = express.Router();

    // Authenticate all scene requests
    router.use(authenticateRequest);

    // Get scene configuration
    router.get(
      '/config/:id',
      validateRequest('getSceneConfig'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/config/${req.params.id}`,
            method: 'GET',
            params: req.params,
            query: req.query,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate scene
    router.get(
      '/validate/:id',
      validateRequest('validateScene'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/${req.params.id}`,
            method: 'GET',
            params: req.params,
            query: req.query,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Analyze scene performance
    router.get(
      '/validate/:id/performance',
      validateRequest('validateScenePerformance'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/${req.params.id}`,
            method: 'GET',
            params: req.params,
            query: { ...req.query, action: 'performance' },
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Check scene compatibility
    router.get(
      '/validate/:id/compatibility',
      validateRequest('validateSceneCompatibility'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/${req.params.id}`,
            method: 'GET',
            params: req.params,
            query: { ...req.query, action: 'compatibility' },
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate scene data
    router.post(
      '/validate/data',
      validateRequest('validateSceneData'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: '/scene/validate/data',
            method: 'POST',
            body: req.body,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate scene flow
    router.post(
      '/validate/flow',
      validateRequest('validateSceneFlow'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: '/scene/validate/flow',
            method: 'POST',
            body: req.body,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Simulate scene flow
    router.post(
      '/validate/flow-simulation',
      validateRequest('simulateSceneFlow'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: '/scene/validate/flow-simulation',
            method: 'POST',
            body: req.body,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate scene assets
    router.get(
      '/validate/:scene_id/assets',
      validateRequest('validateSceneAssets'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/${req.params.scene_id}/assets`,
            method: 'GET',
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate single asset
    router.get(
      '/validate/asset/:asset_id',
      validateRequest('validateAsset'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/asset/${req.params.asset_id}`,
            method: 'GET',
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate multiple assets
    router.post(
      '/validate/assets',
      validateRequest('validateAssets'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: '/scene/validate/assets',
            method: 'POST',
            body: req.body,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate scene blueprints
    router.get(
      '/validate/:scene_id/blueprints',
      validateRequest('validateSceneBlueprints'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/${req.params.scene_id}/blueprints`,
            method: 'GET',
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Check blueprint compatibility
    router.get(
      '/validate/:scene_id/blueprints/compatibility',
      validateRequest('checkBlueprintCompatibility'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/${req.params.scene_id}/blueprints`,
            method: 'GET',
            params: req.params,
            query: { ...req.query, action: 'compatibility' },
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate blueprint instances
    router.post(
      '/validate/blueprints',
      validateRequest('validateBlueprintInstances'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: '/scene/validate/blueprints',
            method: 'POST',
            body: req.body,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Start background validation
    router.post(
      '/validate/:scene_id/background',
      validateRequest('startBackgroundValidation'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/${req.params.scene_id}/background`,
            method: 'POST',
            body: req.body,
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Get job status
    router.get(
      '/validate/job/:job_id',
      validateRequest('getJobStatus'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/job/${req.params.job_id}`,
            method: 'GET',
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate all
    router.post(
      '/validate/:scene_id/validate-all',
      validateRequest('validateAll'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scene/validate/${req.params.scene_id}/validate-all`,
            method: 'POST',
            body: req.body,
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Get scene phase state
    router.get(
      '/scenes/:scene_id/phases',
      validateRequest('getScenePhaseState'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scenes/${req.params.scene_id}/phases`,
            method: 'GET',
            params: req.params,
            query: req.query,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Initialize scene phase state
    router.post(
      '/scenes/:scene_id/phases/initialize',
      validateRequest('initializeScenePhaseState'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scenes/${req.params.scene_id}/phases/initialize`,
            method: 'POST',
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Transition to next phase
    router.post(
      '/scenes/:scene_id/phases/next',
      validateRequest('transitionToNextPhase'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scenes/${req.params.scene_id}/phases/next`,
            method: 'POST',
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Transition to specific phase
    router.post(
      '/scenes/:scene_id/phases/transition',
      validateRequest('transitionToPhase'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scenes/${req.params.scene_id}/phases/transition`,
            method: 'POST',
            body: req.body,
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Validate phase
    router.get(
      '/scenes/:scene_id/phases/validate',
      validateRequest('validatePhase'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scenes/${req.params.scene_id}/phases`,
            method: 'GET',
            params: req.params,
            query: { ...req.query, validate: 'true' },
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Record validation result
    router.post(
      '/scenes/:scene_id/phases/validation',
      validateRequest('recordValidationResult'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scenes/${req.params.scene_id}/phases/validation`,
            method: 'POST',
            body: req.body,
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Skip phase
    router.post(
      '/scenes/:scene_id/phases/skip',
      validateRequest('skipPhase'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scenes/${req.params.scene_id}/phases/skip`,
            method: 'POST',
            body: req.body,
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    // Get phase progress
    router.get(
      '/scenes/:scene_id/phases/progress',
      validateRequest('getPhaseProgress'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/scenes/${req.params.scene_id}/phases/progress`,
            method: 'GET',
            params: req.params,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    return router;
  }

  /**
   * Configure blueprint routes
   *
   * @returns Router for blueprint routes
   */
  private configureBlueprintRoutes(): express.Router {
    const router = express.Router();

    // Authenticate all blueprint requests
    router.use(authenticateRequest);

    // Get blueprint configuration
    router.get(
      '/config/:id',
      validateRequest('getBlueprintConfig'),
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const response = await this.integrationManager.handleRequest({
            path: `/blueprint/config/${req.params.id}`,
            method: 'GET',
            params: req.params,
            query: req.query,
            headers: req.headers,
            user: req.user,
          });

          res.status(200).json(response);
        } catch (error) {
          next(error);
        }
      },
    );

    return router;
  }

  /**
   * Configure recovery dashboard routes
   *
   * @returns Router for recovery dashboard routes
   */
  private configureRecoveryDashboardRoutes(): express.Router {
    // Import recovery dashboard routes
    const recoveryDashboardRoutes = require('../routes/recovery-dashboard');

    // Serve recovery dashboard UI
    this.app.get('/recovery-dashboard', (_req: Request, res: Response) => {
      res.sendFile(path.join(__dirname, '../../public/recovery-dashboard.html'));
    });

    return recoveryDashboardRoutes;
  }

  /**
   * Configure business continuity routes
   *
   * @returns Router for business continuity routes
   */
  private configureBusinessContinuityRoutes(): express.Router {
    // Import business continuity routes
    const businessContinuityRoutes = require('../../controllers/business-continuity');

    // Authenticate all business continuity requests
    businessContinuityRoutes.use(authenticateRequest);

    // Serve business continuity dashboard UI
    this.app.get('/business-continuity', (_req: Request, res: Response) => {
      res.sendFile(path.join(__dirname, '../../public/business-continuity/index.html'));
    });

    return businessContinuityRoutes;
  }

  /**
   * Configure error handling
   */
  private configureErrorHandling(): void {
    // Add error middleware
    this.app.use(
      errorHandler({
        logErrors: true,
        includeStackTrace: process.env.NODE_ENV === 'development',
      }),
    );

    // Handle 404 errors
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        error: 'NotFoundError',
        message: `Route ${req.method} ${req.path} not found`,
        details: null,
      });
    });
  }

  /**
   * Initialize the API Gateway
   */
  public async initialize(): Promise<void> {
    try {
      // Initialize the integration manager
      await this.integrationManager.initialize();

      this.logger.info('API Gateway initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize API Gateway', { error });
      throw error;
    }
  }

  /**
   * Start the API Gateway
   */
  public async start(): Promise<void> {
    try {
      // Initialize the API Gateway
      await this.initialize();

      // Start the server
      this.app.listen(this.port, () => {
        this.logger.info(`API Gateway listening on port ${this.port}`);
      });
    } catch (error) {
      this.logger.error('Failed to start API Gateway', { error });
      throw error;
    }
  }

  /**
   * Get the Express application
   *
   * @returns Express application
   */
  public getApp(): express.Application {
    return this.app;
  }
}
