# MVS-VR Developer Getting Started Guide

This guide provides a comprehensive introduction to the MVS-VR platform for developers. It covers environment setup, authentication, basic operations, error handling, and best practices.

## Table of Contents

1. [System Architecture Overview](#system-architecture-overview)
2. [Environment Setup](#environment-setup)
3. [Authentication](#authentication)
4. [Basic Operations](#basic-operations)
5. [Error Handling](#error-handling)
6. [Best Practices](#best-practices)
7. [Next Steps](#next-steps)

## System Architecture Overview

The MVS-VR platform consists of several key components:

- **Backend Server**: Built with Node.js, Express, and TypeScript, providing RESTful APIs
- **Database**: Supabase (PostgreSQL) for data storage with Row Level Security (RLS)
- **Authentication**: JWT-based authentication with role-based access control
- **Asset Management**: Services for handling 3D models, textures, and other assets
- **LLM Integration**: Services for integrating with Large Language Models
- **Offline Mode**: Support for offline operation with synchronization
- **Admin Portal**: Web interface for system administration
- **Vendor Portal**: Web interface for vendors to manage their products
- **Client Portal**: Web interface for clients to browse products

The system follows a microservices architecture with the following services:

- API Gateway
- Authentication Service
- Asset Management Service
- Scene Management Service
- LLM Integration Service
- Analytics Service
- Notification Service

## Environment Setup

### Prerequisites

- Node.js (v18 or later)
- Docker and Docker Compose
- Git
- Supabase CLI
- Visual Studio Code (recommended)

### Local Development Setup

1. **Clone the repository**

```bash
git clone https://github.com/your-organization/mvs-vr.git
cd mvs-vr
```

2. **Install dependencies**

```bash
cd mvs-vr-v2/implementation/server
npm install
```

3. **Set up environment variables**

Create a `.env` file in the `mvs-vr-v2/implementation/server` directory with the following content:

```
# Server
PORT=3000
NODE_ENV=development

# Supabase
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# JWT
JWT_SECRET=your-jwt-secret
JWT_EXPIRY=1d
REFRESH_TOKEN_EXPIRY=7d

# Storage
STORAGE_BUCKET=assets
```

4. **Start Supabase locally**

```bash
cd mvs-vr-v2/implementation/server
npx supabase start
```

5. **Run database migrations**

```bash
cd mvs-vr-v2/implementation/server
npm run migrate
```

6. **Start the development server**

```bash
cd mvs-vr-v2/implementation/server
npm run dev
```

The server will be available at `http://localhost:3000`.

### Docker Setup

Alternatively, you can use Docker to run the entire stack:

```bash
cd mvs-vr-v2/implementation
docker-compose up -d
```

This will start the following services:

- Server: `http://localhost:3000`
- Supabase: `http://localhost:54321`
- Admin Portal: `http://localhost:3001`
- Vendor Portal: `http://localhost:3002`

## Authentication

The MVS-VR platform uses JWT-based authentication with role-based access control. There are three main roles:

- **Admin**: System administrators with full access
- **Vendor**: Vendors who manage their products
- **Client**: Clients who browse products

### Authentication Flow

1. **Login**

```typescript
// Example login request
const response = await fetch('http://localhost:3000/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password',
  }),
});

const { accessToken, refreshToken } = await response.json();
```

2. **Using the access token**

```typescript
// Example authenticated request
const response = await fetch('http://localhost:3000/api/protected-resource', {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
  },
});
```

3. **Refreshing the token**

```typescript
// Example token refresh
const response = await fetch('http://localhost:3000/api/auth/refresh', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    refreshToken,
  }),
});

const { accessToken: newAccessToken } = await response.json();
```

## Basic Operations

### Asset Management

1. **Upload an asset**

```typescript
// Example asset upload
const formData = new FormData();
formData.append('file', fileBlob);
formData.append('metadata', JSON.stringify({
  name: 'Example Asset',
  type: 'model',
  tags: ['furniture', 'chair'],
}));

const response = await fetch('http://localhost:3000/api/assets', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
  },
  body: formData,
});

const { assetId } = await response.json();
```

2. **Get an asset**

```typescript
// Example asset retrieval
const response = await fetch(`http://localhost:3000/api/assets/${assetId}`, {
  headers: {
    'Authorization': `Bearer ${accessToken}`,
  },
});

const asset = await response.json();
```

### Scene Management

1. **Create a scene**

```typescript
// Example scene creation
const response = await fetch('http://localhost:3000/api/scenes', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'Example Scene',
    description: 'An example scene',
    assets: [assetId],
    configuration: {
      // Scene configuration
    },
  }),
});

const { sceneId } = await response.json();
```

## Error Handling

The MVS-VR API uses standard HTTP status codes and returns error responses in the following format:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error details
    }
  }
}
```

Common error codes include:

- `AUTHENTICATION_ERROR`: Authentication failed
- `AUTHORIZATION_ERROR`: User does not have permission
- `VALIDATION_ERROR`: Request validation failed
- `RESOURCE_NOT_FOUND`: Requested resource not found
- `INTERNAL_SERVER_ERROR`: Server error

### Error Handling Example

```typescript
try {
  const response = await fetch('http://localhost:3000/api/assets', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${accessToken}`,
    },
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`API Error: ${errorData.error.message}`);
  }

  const data = await response.json();
  // Process data
} catch (error) {
  console.error('Error:', error.message);
  // Handle error
}
```

## Best Practices

1. **Use TypeScript**: TypeScript provides type safety and better developer experience.
2. **Handle Errors Properly**: Always handle errors and provide meaningful error messages.
3. **Use Environment Variables**: Store configuration in environment variables, not in code.
4. **Follow Security Best Practices**: Use HTTPS, validate input, and follow OWASP guidelines.
5. **Write Tests**: Write unit tests, integration tests, and end-to-end tests.
6. **Use Linting and Formatting**: Use ESLint and Prettier to maintain code quality.
7. **Document Your Code**: Use JSDoc comments to document your code.
8. **Follow Git Workflow**: Use feature branches, pull requests, and code reviews.

## Next Steps

Now that you have a basic understanding of the MVS-VR platform, you can explore the following guides:

- [API Reference](./API_REFERENCE.md)
- [Asset Management Guide](./ASSET_MANAGEMENT.md)
- [Scene Management Guide](./SCENE_MANAGEMENT.md)
- [LLM Integration Guide](./LLM_INTEGRATION.md)
- [Offline Mode Guide](./OFFLINE_MODE.md)
- [Security Best Practices](./SECURITY_BEST_PRACTICES.md)
- [Performance Optimization Guide](./PERFORMANCE_OPTIMIZATION.md)
- [Custom Extension Development Guide](./CUSTOM_EXTENSION.md)
