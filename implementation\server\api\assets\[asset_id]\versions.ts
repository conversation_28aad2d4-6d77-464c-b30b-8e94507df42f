import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../shared/utils/logger';
import { errorHandler } from '../../../shared/utils/error-handler';
import { AssetService } from '../../../services/asset-service';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  asset_id: z.string().uuid(),
});

/**
 * Asset Versions API endpoint
 *
 * This endpoint returns all versions of a specific asset.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse({
      asset_id: req.query.asset_id,
    });

    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { asset_id } = queryResult.data;

    // Log the request
    logger.info('Asset versions request', {
      asset_id,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create asset service
    const assetService = new AssetService(supabase);

    // Check if asset exists
    const asset = await assetService.getAsset(asset_id);
    if (!asset) {
      return res.status(404).json({ error: 'Asset not found' });
    }

    // Get asset versions
    const versions = await assetService.getAssetVersions(asset_id);

    // Return versions
    return res.status(200).json({ versions });
  } catch (error) {
    logger.error('Unexpected error in asset versions endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
