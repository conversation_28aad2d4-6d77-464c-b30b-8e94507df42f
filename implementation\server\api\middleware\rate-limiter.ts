import { Request, Response, NextFunction } from 'express';
import { logger } from '../../shared/utils/logger';
import { RateLimiterMemory } from 'rate-limiter-flexible';

// Rate limiter options
interface RateLimiterOptions {
  windowMs: number;
  max: number;
  keyPrefix?: string;
  message?: string;
}

// Rate limiter map
const rateLimiters = new Map<string, RateLimiterMemory>();

/**
 * Rate limiter middleware
 *
 * @param options - Rate limiter options
 * @returns Middleware function
 */
export const rateLimiter = (options: RateLimiterOptions) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Generate key prefix based on route
      const keyPrefix = options.keyPrefix || `rate-limit:${req.method}:${req.path}`;

      // Get or create rate limiter
      let limiter = rateLimiters.get(keyPrefix);

      if (!limiter) {
        limiter = new RateLimiterMemory({
          points: options.max,
          duration: options.windowMs / 1000,
          keyPrefix,
        });

        rateLimiters.set(keyPrefix, limiter);
      }

      // Get client IP
      const ip = req.ip || req.socket.remoteAddress || 'unknown';

      // Check rate limit
      await limiter.consume(ip);

      // Continue
      next();
    } catch (error) {
      // Rate limit exceeded
      logger.warn('Rate limit exceeded', {
        ip: req.ip || req.socket.remoteAddress || 'unknown',
        path: req.path,
        method: req.method,
      });

      res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: options.message || 'Too many requests, please try again later',
        },
      });
    }
  };
};
