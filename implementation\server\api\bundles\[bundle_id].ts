import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { AssetService } from '../../services/asset-service';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  bundle_id: z.string().uuid(),
  include_assets: z.enum(['true', 'false']).optional().default('false'),
});

// Define the request body schema for PATCH
const PatchBodySchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  asset_ids: z.array(z.string().uuid()).optional(),
});

/**
 * Asset Bundle API endpoint
 *
 * This endpoint handles getting, updating, and deleting a specific asset bundle.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse({
      bundle_id: req.query.bundle_id,
      include_assets: req.query.include_assets,
    });

    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { bundle_id, include_assets } = queryResult.data;
    const includeAssets = include_assets === 'true';

    // Log the request
    logger.info('Asset bundle request', {
      bundle_id,
      include_assets: includeAssets,
      method: req.method,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create asset service
    const assetService = new AssetService(supabase);

    // Handle GET request
    if (req.method === 'GET') {
      // Get bundle
      let bundle;
      if (includeAssets) {
        bundle = await assetService.getAssetBundleWithAssets(bundle_id);
      } else {
        bundle = await assetService.getAssetBundle(bundle_id);
      }

      if (!bundle) {
        return res.status(404).json({ error: 'Asset bundle not found' });
      }

      // Return bundle
      return res.status(200).json({ bundle });
    }

    // Handle PATCH request (update bundle)
    if (req.method === 'PATCH') {
      // Validate request body
      const bodyResult = PatchBodySchema.safeParse(req.body);
      if (!bodyResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid request body', details: bodyResult.error.format() });
      }

      // Extract request data
      const { name, description, asset_ids } = bodyResult.data;

      // Check if bundle exists
      const existingBundle = await assetService.getAssetBundle(bundle_id);
      if (!existingBundle) {
        return res.status(404).json({ error: 'Asset bundle not found' });
      }

      // Check if user has permission to update this bundle
      // In a real implementation, we would check if the user is an admin or belongs to the vendor
      // For now, we'll just check if the user is authenticated

      // Update bundle
      let updatedBundle;
      if (asset_ids) {
        // If asset_ids are provided, update the bundle with new assets
        updatedBundle = await assetService.updateAssetBundle(
          bundle_id,
          asset_ids,
          name,
          description,
        );
      } else {
        // If only name or description is provided, update just those fields
        const updateData: any = {};
        if (name) {
          updateData.name = name;
        }
        if (description !== undefined) {
          updateData.description = description;
        }

        const { data, error } = await supabase
          .from('asset_bundles')
          .update(updateData)
          .eq('id', bundle_id)
          .select()
          .single();

        if (error) {
          logger.error('Error updating asset bundle', { error, bundle_id });
          return errorHandler(error, res);
        }

        updatedBundle = data;
      }

      if (!updatedBundle) {
        return res.status(500).json({ error: 'Failed to update asset bundle' });
      }

      // Return updated bundle
      return res.status(200).json({ bundle: updatedBundle });
    }

    // Handle DELETE request
    if (req.method === 'DELETE') {
      // Check if bundle exists
      const existingBundle = await assetService.getAssetBundle(bundle_id);
      if (!existingBundle) {
        return res.status(404).json({ error: 'Asset bundle not found' });
      }

      // Check if user has permission to delete this bundle
      // In a real implementation, we would check if the user is an admin or belongs to the vendor
      // For now, we'll just check if the user is authenticated

      // Delete bundle
      const { error } = await supabase.from('asset_bundles').delete().eq('id', bundle_id);

      if (error) {
        logger.error('Error deleting asset bundle', { error, bundle_id });
        return errorHandler(error, res);
      }

      // Return success
      return res.status(204).end();
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Unexpected error in asset bundle endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
