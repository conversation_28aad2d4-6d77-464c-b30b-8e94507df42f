import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { SceneEditorService } from '../../../services/scene/scene-editor-service';
import { validateRequest } from '../../middleware/validation';

// Initialize scene editor service
const sceneEditorService = new SceneEditorService(supabase);

// Clean up stale sessions every 5 minutes
setInterval(() => {
  sceneEditorService.cleanupStaleSessions();
}, 300000);

/**
 * Start editing a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const startEditing = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { user_id } = req.body;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!user_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_USER_ID',
          message: 'User ID is required',
        },
      });
      return;
    }

    // Start editing
    const sessionId = await sceneEditorService.startEditing(scene_id, user_id);

    if (!sessionId) {
      res.status(409).json({
        success: false,
        error: {
          code: 'SCENE_LOCKED',
          message: 'Scene is already being edited by another user',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        session_id: sessionId,
      },
    });
  } catch (error) {
    logger.error('Error starting scene editing', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * End editing a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const endEditing = async (req: Request, res: Response): Promise<void> => {
  try {
    const { session_id } = req.params;

    // Validate parameters
    if (!session_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SESSION_ID',
          message: 'Session ID is required',
        },
      });
      return;
    }

    // End editing
    const success = sceneEditorService.endEditing(session_id);

    if (!success) {
      res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Edit session not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        message: 'Editing ended successfully',
      },
    });
  } catch (error) {
    logger.error('Error ending scene editing', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Save scene changes
 *
 * @param req - Request
 * @param res - Response
 */
export const saveChanges = async (req: Request, res: Response): Promise<void> => {
  try {
    const { session_id } = req.params;
    const { scene_data } = req.body;

    // Validate parameters
    if (!session_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SESSION_ID',
          message: 'Session ID is required',
        },
      });
      return;
    }

    if (!scene_data) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_DATA',
          message: 'Scene data is required',
        },
      });
      return;
    }

    // Save changes
    const scene = await sceneEditorService.saveChanges(session_id, scene_data);

    if (!scene) {
      res.status(404).json({
        success: false,
        error: {
          code: 'SESSION_NOT_FOUND',
          message: 'Edit session not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: scene,
    });
  } catch (error) {
    logger.error('Error saving scene changes', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
