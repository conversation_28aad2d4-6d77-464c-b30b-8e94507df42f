import { mount } from '@vue/test-utils';
import Wizard<PERSON>ontainer from '../WizardContainer.vue';
import StepDependencyManager from '../utils/StepDependencyManager';

// Create a mock step component
const MockStepComponent = {
  name: 'MockStepComponent',
  template: '<div class="mock-step">Mock Step</div>',
  props: ['stepData'],
  methods: {
    updateStepData(data) {
      this.$emit('update:step-data', data);
    },
    validate() {
      this.$emit('validate', true);
      return true;
    },
  },
};

describe('WizardContainer', () => {
  let wrapper;
  const mockSteps = [
    {
      title: 'Step 1',
      component: MockStepComponent,
      helpTitle: 'Help for Step 1',
      helpContent: '<p>This is help content for step 1</p>',
    },
    {
      title: 'Step 2',
      component: MockStepComponent,
      helpTitle: 'Help for Step 2',
      helpContent: '<p>This is help content for step 2</p>',
    },
    {
      title: 'Step 3',
      component: MockStepComponent,
      helpTitle: 'Help for Step 3',
      helpContent: '<p>This is help content for step 3</p>',
    },
  ];

  beforeEach(() => {
    wrapper = mount(WizardContainer, {
      propsData: {
        title: 'Test Wizard',
        description: 'Test Description',
        steps: mockSteps,
        initialStepIndex: 0,
        allowStepNavigation: true,
        showBackButton: true,
        showSaveButton: true,
        showHelpSection: true,
        nextButtonText: 'Next',
        finishButtonText: 'Finish',
        storageKey: 'test-wizard',
      },
      stubs: {
        transition: false,
      },
    });
  });

  afterEach(() => {
    wrapper.destroy();
    localStorage.clear();
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('displays the correct title and description', () => {
    expect(wrapper.find('.wizard-title').text()).toBe('Test Wizard');
    expect(wrapper.find('.wizard-description').text()).toBe('Test Description');
  });

  it('renders the progress steps', () => {
    const progressSteps = wrapper.findAll('.progress-step');
    expect(progressSteps.length).toBe(3);

    // First step should be active
    expect(progressSteps.at(0).classes()).toContain('active');

    // Step titles should be correct
    expect(progressSteps.at(0).find('.step-title').text()).toBe('Step 1');
    expect(progressSteps.at(1).find('.step-title').text()).toBe('Step 2');
    expect(progressSteps.at(2).find('.step-title').text()).toBe('Step 3');
  });

  it('renders the current step component', () => {
    expect(wrapper.findComponent(MockStepComponent).exists()).toBe(true);
  });

  it('navigates to the next step when Next button is clicked', async () => {
    // Arrange
    // Make the current step valid
    wrapper.vm.validateCurrentStep(true);
    await wrapper.vm.$nextTick();

    // Act
    const nextButton = wrapper.find('.wizard-next-button');
    await nextButton.trigger('click');

    // Assert
    expect(wrapper.vm.currentStepIndex).toBe(1);
    expect(wrapper.findAll('.progress-step').at(1).classes()).toContain('active');
  });

  it('does not navigate to the next step if current step is invalid', async () => {
    // Arrange
    wrapper.vm.validateCurrentStep(false);
    await wrapper.vm.$nextTick();

    // Act
    const nextButton = wrapper.find('.wizard-next-button');
    await nextButton.trigger('click');

    // Assert
    expect(wrapper.vm.currentStepIndex).toBe(0);
  });

  it('navigates to the previous step when Back button is clicked', async () => {
    // Arrange
    // First go to step 1
    wrapper.vm.validateCurrentStep(true);
    await wrapper.vm.$nextTick();
    wrapper.vm.goToNextStep();
    await wrapper.vm.$nextTick();

    // Act
    const backButton = wrapper.find('.wizard-back-button');
    await backButton.trigger('click');

    // Assert
    expect(wrapper.vm.currentStepIndex).toBe(0);
  });

  it('completes the wizard when Finish button is clicked on the last step', async () => {
    // Arrange
    // Go to the last step
    wrapper.vm.validateCurrentStep(true);
    await wrapper.vm.$nextTick();
    wrapper.vm.goToStep(2);
    await wrapper.vm.$nextTick();

    // Make the last step valid
    wrapper.vm.validateCurrentStep(true);
    await wrapper.vm.$nextTick();

    // Act
    const finishButton = wrapper.find('.wizard-finish-button');
    await finishButton.trigger('click');

    // Assert
    expect(wrapper.emitted('complete')).toBeTruthy();
  });

  it('saves progress when Save Progress button is clicked', async () => {
    // Act
    const saveButton = wrapper.find('.wizard-save-button');
    await saveButton.trigger('click');

    // Assert
    expect(wrapper.emitted('save-progress')).toBeTruthy();

    // Check that localStorage was updated
    const savedProgress = JSON.parse(localStorage.getItem('test-wizard'));
    expect(savedProgress).toBeTruthy();
    expect(savedProgress.currentStepIndex).toBe(0);
  });

  it('loads saved progress from localStorage', async () => {
    // Arrange
    const savedProgress = {
      stepsData: {
        0: { testData: 'Step 1 Data' },
        1: { testData: 'Step 2 Data' },
      },
      stepsValidity: {
        0: true,
        1: false,
      },
      currentStepIndex: 1,
    };

    localStorage.setItem('test-wizard', JSON.stringify(savedProgress));

    // Act
    const newWrapper = mount(WizardContainer, {
      propsData: {
        title: 'Test Wizard',
        description: 'Test Description',
        steps: mockSteps,
        initialStepIndex: 0,
        allowStepNavigation: true,
        showBackButton: true,
        showSaveButton: true,
        showHelpSection: true,
        nextButtonText: 'Next',
        finishButtonText: 'Finish',
        storageKey: 'test-wizard',
      },
      stubs: {
        transition: false,
      },
    });

    // Assert
    expect(newWrapper.vm.currentStepIndex).toBe(1);
    expect(newWrapper.vm.stepsData[0]).toEqual({ testData: 'Step 1 Data' });
    expect(newWrapper.vm.stepsData[1]).toEqual({ testData: 'Step 2 Data' });
    expect(newWrapper.vm.stepsValidity[0]).toBe(true);
    expect(newWrapper.vm.stepsValidity[1]).toBe(false);

    // Clean up
    newWrapper.destroy();
  });

  it('toggles help section when help toggle is clicked', async () => {
    // Arrange
    expect(wrapper.vm.isHelpExpanded).toBe(false);

    // Act
    const helpToggle = wrapper.find('.help-toggle');
    await helpToggle.trigger('click');

    // Assert
    expect(wrapper.vm.isHelpExpanded).toBe(true);
    expect(wrapper.find('.help-content').exists()).toBe(true);

    // Act again to toggle off
    await helpToggle.trigger('click');

    // Assert
    expect(wrapper.vm.isHelpExpanded).toBe(false);
    expect(wrapper.find('.help-content').exists()).toBe(false);
  });

  it('updates step data when step component emits update:step-data', async () => {
    // Arrange
    const mockStepComponent = wrapper.findComponent(MockStepComponent);
    const newData = { testField: 'test value' };

    // Act
    mockStepComponent.vm.$emit('update:step-data', newData);
    await wrapper.vm.$nextTick();

    // Assert
    expect(wrapper.vm.stepsData[0]).toEqual(newData);
  });

  it('validates current step when step component emits validate', async () => {
    // Arrange
    const mockStepComponent = wrapper.findComponent(MockStepComponent);

    // Act
    mockStepComponent.vm.$emit('validate', true);
    await wrapper.vm.$nextTick();

    // Assert
    expect(wrapper.vm.stepsValidity[0]).toBe(true);
    expect(wrapper.vm.canProceedToNextStep).toBe(true);
  });

  it('calculates progress percentage correctly', async () => {
    // Arrange
    wrapper.vm.stepsValidity = {
      0: true,
      1: true,
      2: false,
    };
    await wrapper.vm.$nextTick();

    // Assert
    expect(wrapper.vm.progressPercentage).toBe(67); // 2/3 * 100 = 66.67, rounded to 67
  });

  it('emits analytics events when steps are viewed', async () => {
    // Act
    wrapper.vm.trackStepView(1);

    // Assert
    expect(wrapper.emitted('analytics')).toBeTruthy();
    expect(wrapper.emitted('analytics')[0][0].type).toBe('step_view');
    expect(wrapper.emitted('analytics')[0][0].data.stepIndex).toBe(1);
    expect(wrapper.emitted('analytics')[0][0].data.stepTitle).toBe('Step 2');
  });

  it('clears saved progress when wizard is completed', async () => {
    // Arrange
    // Save some progress first
    wrapper.vm.saveProgress();
    expect(localStorage.getItem('test-wizard')).toBeTruthy();

    // Go to the last step and make it valid
    wrapper.vm.goToStep(2);
    wrapper.vm.validateCurrentStep(true);
    await wrapper.vm.$nextTick();

    // Act
    wrapper.vm.completeWizard();

    // Assert
    expect(localStorage.getItem('test-wizard')).toBeNull();
  });

  it('handles keyboard navigation with arrow keys', async () => {
    // Arrange
    // Make the current step valid
    wrapper.vm.validateCurrentStep(true);
    await wrapper.vm.$nextTick();

    // Act - simulate right arrow key press
    const event = new KeyboardEvent('keydown', { key: 'ArrowRight' });
    window.dispatchEvent(event);
    await wrapper.vm.$nextTick();

    // Assert
    expect(wrapper.vm.currentStepIndex).toBe(1);

    // Act - simulate left arrow key press
    const backEvent = new KeyboardEvent('keydown', { key: 'ArrowLeft' });
    window.dispatchEvent(backEvent);
    await wrapper.vm.$nextTick();

    // Assert
    expect(wrapper.vm.currentStepIndex).toBe(0);
  });

  it('handles keyboard navigation with number keys', async () => {
    // Arrange
    // Make all steps valid and accessible
    wrapper.vm.stepsValidity = {
      0: true,
      1: true,
      2: true,
    };
    await wrapper.vm.$nextTick();

    // Act - simulate pressing "3" key to go to step 3
    const event = new KeyboardEvent('keydown', { key: '3' });
    window.dispatchEvent(event);
    await wrapper.vm.$nextTick();

    // Assert
    expect(wrapper.vm.currentStepIndex).toBe(2);
  });

  it('ignores keyboard navigation when typing in input fields', async () => {
    // Arrange
    // Create a mock input field and focus it
    const input = document.createElement('input');
    document.body.appendChild(input);
    input.focus();

    // Make the current step valid
    wrapper.vm.validateCurrentStep(true);
    await wrapper.vm.$nextTick();

    // Act - simulate right arrow key press while input is focused
    const event = new KeyboardEvent('keydown', { key: 'ArrowRight' });
    input.dispatchEvent(event);
    await wrapper.vm.$nextTick();

    // Assert - should not navigate
    expect(wrapper.vm.currentStepIndex).toBe(0);

    // Clean up
    document.body.removeChild(input);
  });

  it('handles step dependencies validation', async () => {
    // Create a new wrapper with dependencies
    const mockDependencies = {
      1: {
        requiredFields: [{ stepIndex: 0, field: 'name', message: 'Name is required from Step 1' }],
      },
    };

    const dependencyWrapper = mount(WizardContainer, {
      propsData: {
        title: 'Test Wizard',
        description: 'Test Description',
        steps: mockSteps,
        initialStepIndex: 1, // Start at step 2 which has dependencies
        allowStepNavigation: true,
        showBackButton: true,
        showSaveButton: true,
        showHelpSection: true,
        nextButtonText: 'Next',
        finishButtonText: 'Finish',
        storageKey: 'test-wizard',
        stepDependencies: mockDependencies,
      },
      stubs: {
        transition: false,
      },
    });

    // Initially, dependencies are not met
    expect(dependencyWrapper.vm.currentStepDependenciesValid).toBe(false);

    // Update step data to meet dependencies
    dependencyWrapper.vm.stepsData = {
      0: { name: 'Test Name' },
    };
    await dependencyWrapper.vm.$nextTick();

    // Now dependencies should be met
    expect(dependencyWrapper.vm.currentStepDependenciesValid).toBe(true);

    // Clean up
    dependencyWrapper.destroy();
  });

  it('displays dependency errors when dependencies are not met', async () => {
    // Create a new wrapper with dependencies
    const mockDependencies = {
      1: {
        requiredFields: [{ stepIndex: 0, field: 'name', message: 'Name is required from Step 1' }],
      },
    };

    const dependencyWrapper = mount(WizardContainer, {
      propsData: {
        title: 'Test Wizard',
        description: 'Test Description',
        steps: mockSteps,
        initialStepIndex: 1, // Start at step 2 which has dependencies
        allowStepNavigation: true,
        showBackButton: true,
        showSaveButton: true,
        showHelpSection: true,
        nextButtonText: 'Next',
        finishButtonText: 'Finish',
        storageKey: 'test-wizard',
        stepDependencies: mockDependencies,
      },
      stubs: {
        transition: false,
      },
    });

    // Check if dependency errors are displayed
    expect(dependencyWrapper.find('.dependency-errors').exists()).toBe(true);
    expect(dependencyWrapper.find('.dependency-error-item').text()).toContain(
      'Name is required from Step 1',
    );

    // Clean up
    dependencyWrapper.destroy();
  });
});
