#### **`interactive_asset_spec.md`**

# Interactive Asset Spec

## Asset Types
- **Regular**: static meshes only.  
- **Interactive**: tagged sub-groups and behaviors.

## Tag Library
- `drawerTrackSlideFull`  
- `drawerTrackRollHalf`  
- `doorHingeSwingLeft`  
- `switchSelfContainedToggle`

## JSON Example
```json
{
  "assetId": "drawer_001",
  "type": "interactive",
  "category": "Drawer",
  "interaction": {
    "mechanism": "track",
    "movement": "full",
    "tagMap": {
      "track": "drawerTrackSlideFull",
      "handle": "drawerHandleStd"
    }
  }
}
````

## Workflow

1. Vendor selects **Interactive**.
2. Choose category/mechanism/movement.
3. Assign tags visually or via JSON.
4. Preview interaction in sandbox.
