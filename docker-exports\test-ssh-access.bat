@echo off
echo ========================================
echo Testing SSH Access to DigitalOcean Server
echo ========================================
echo.

set SERVER_IP=**************
set SSH_KEY=C:\Users\<USER>\.ssh\mvs-vr

echo Testing different SSH access methods...
echo.

echo 1. Testing with root user...
ssh -i %SSH_KEY% -o ConnectTimeout=10 -o StrictHostKeyChecking=no root@%SERVER_IP% "echo 'SSH as root: SUCCESS'"
if %ERRORLEVEL% EQU 0 (
    echo ✅ SSH as root: WORKING
    set WORKING_USER=root
    goto :deploy
) else (
    echo ❌ SSH as root: FAILED
)

echo.
echo 2. Testing with vectorax user...
ssh -i %SSH_KEY% -o ConnectTimeout=10 -o StrictHostKeyChecking=no vectorax@%SERVER_IP% "echo 'SSH as vectorax: SUCCESS'"
if %ERRORLEVEL% EQU 0 (
    echo ✅ SSH as vectorax: WORKING
    set WORKING_USER=vectorax
    goto :deploy
) else (
    echo ❌ SSH as vectorax: FAILED
)

echo.
echo 3. Testing with ubuntu user...
ssh -i %SSH_KEY% -o ConnectTimeout=10 -o StrictHostKeyChecking=no ubuntu@%SERVER_IP% "echo 'SSH as ubuntu: SUCCESS'"
if %ERRORLEVEL% EQU 0 (
    echo ✅ SSH as ubuntu: WORKING
    set WORKING_USER=ubuntu
    goto :deploy
) else (
    echo ❌ SSH as ubuntu: FAILED
)

echo.
echo ❌ All SSH attempts failed!
echo.
echo SOLUTIONS:
echo 1. Use DigitalOcean Console:
echo    - Log into DigitalOcean dashboard
echo    - Go to your droplet
echo    - Click "Console" for direct access
echo.
echo 2. Reset SSH access:
echo    - In DigitalOcean, go to droplet settings
echo    - Reset root password
echo    - Use console to add your SSH key
echo.
echo 3. Check SSH key:
echo    - Verify key exists: %SSH_KEY%
echo    - Check key format (should start with ssh-rsa or ssh-ed25519)
echo.
goto :end

:deploy
echo.
echo ✅ SSH access working with user: %WORKING_USER%
echo.
set /p deploy="Do you want to deploy MVS-VR now? (Y/N): "
if /i "%deploy%" EQU "Y" (
    echo Running deployment script...
    powershell.exe -ExecutionPolicy Bypass -File "Upload-And-Deploy.ps1" -Username %WORKING_USER%
) else (
    echo.
    echo To deploy manually, run:
    echo powershell.exe -ExecutionPolicy Bypass -File "Upload-And-Deploy.ps1" -Username %WORKING_USER%
)

:end
echo.
pause
