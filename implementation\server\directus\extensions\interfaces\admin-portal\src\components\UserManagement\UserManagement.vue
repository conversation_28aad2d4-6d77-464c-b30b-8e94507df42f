<template>
  <div class="user-management">
    <div class="d-flex justify-space-between align-center mb-4">
      <h2>User Management</h2>
      <div>
        <v-btn color="primary" @click="openCreateUserDialog">
          <v-icon left>mdi-account-plus</v-icon>
          Add User
        </v-btn>
      </div>
    </div>

    <!-- Filters -->
    <v-card class="mb-4">
      <v-card-title>Filters</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="4">
            <v-text-field
              v-model="filters.search"
              label="Search"
              prepend-icon="mdi-magnify"
              clearable
              @input="applyFilters"
            ></v-text-field>
          </v-col>

          <v-col cols="12" md="3">
            <v-select
              v-model="filters.role"
              :items="roleOptions"
              label="Role"
              prepend-icon="mdi-shield-account"
              clearable
              @change="applyFilters"
            ></v-select>
          </v-col>

          <v-col cols="12" md="3">
            <v-select
              v-model="filters.status"
              :items="statusOptions"
              label="Status"
              prepend-icon="mdi-account-check"
              clearable
              @change="applyFilters"
            ></v-select>
          </v-col>

          <v-col cols="12" md="2" class="d-flex align-center">
            <v-btn text color="primary" @click="resetFilters">
              Reset Filters
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- User List -->
    <v-card>
      <v-data-table
        :headers="headers"
        :items="filteredUsers"
        :loading="loading"
        :server-items-length="totalUsers"
        :options.sync="options"
        :footer-props="{
          'items-per-page-options': [10, 25, 50, 100],
        }"
        @update:options="fetchUsers"
        class="user-table"
      >
        <!-- Avatar Column -->
        <template v-slot:item.avatar="{ item }">
          <v-avatar size="36" color="primary" class="white--text">
            <span v-if="!item.avatar">{{ getInitials(item.first_name, item.last_name) }}</span>
            <img v-else :src="item.avatar" alt="Avatar">
          </v-avatar>
        </template>

        <!-- Name Column -->
        <template v-slot:item.name="{ item }">
          <div class="font-weight-medium">{{ item.first_name }} {{ item.last_name }}</div>
          <div class="text-caption">{{ item.email }}</div>
        </template>

        <!-- Role Column -->
        <template v-slot:item.role="{ item }">
          <v-chip
            :color="getRoleColor(item.role)"
            text-color="white"
            small
          >
            {{ item.role }}
          </v-chip>
        </template>

        <!-- Status Column -->
        <template v-slot:item.status="{ item }">
          <v-chip
            :color="getStatusColor(item.status)"
            text-color="white"
            small
          >
            {{ item.status }}
          </v-chip>
        </template>

        <!-- Last Login Column -->
        <template v-slot:item.last_login="{ item }">
          <span>{{ formatDate(item.last_login) }}</span>
        </template>

        <!-- Actions Column -->
        <template v-slot:item.actions="{ item }">
          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                icon
                small
                v-bind="attrs"
                v-on="on"
                @click="viewUser(item)"
              >
                <v-icon small>mdi-eye</v-icon>
              </v-btn>
            </template>
            <span>View Details</span>
          </v-tooltip>

          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                icon
                small
                v-bind="attrs"
                v-on="on"
                @click="editUser(item)"
              >
                <v-icon small>mdi-pencil</v-icon>
              </v-btn>
            </template>
            <span>Edit User</span>
          </v-tooltip>

          <v-tooltip bottom>
            <template v-slot:activator="{ on, attrs }">
              <v-btn
                icon
                small
                color="error"
                v-bind="attrs"
                v-on="on"
                @click="confirmDeleteUser(item)"
              >
                <v-icon small>mdi-delete</v-icon>
              </v-btn>
            </template>
            <span>Delete User</span>
          </v-tooltip>
        </template>

        <!-- No Data -->
        <template v-slot:no-data>
          <div class="text-center py-4">
            <v-icon size="48" color="grey lighten-1">mdi-account-off</v-icon>
            <p class="mt-2 grey--text">No users found</p>
          </div>
        </template>

        <!-- Loading -->
        <template v-slot:progress>
          <v-progress-linear
            indeterminate
            color="primary"
          ></v-progress-linear>
        </template>
      </v-data-table>
    </v-card>

    <!-- User Detail Dialog -->
    <user-detail-dialog
      v-if="selectedUser"
      :user="selectedUser"
      :visible="userDetailDialogVisible"
      @close="closeUserDetailDialog"
    />

    <!-- User Form Dialog -->
    <user-form-dialog
      :user="selectedUser"
      :visible="userFormDialogVisible"
      :is-new="isNewUser"
      @close="closeUserFormDialog"
      @save="saveUser"
    />

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="deleteDialogVisible" max-width="400px">
      <v-card>
        <v-card-title class="headline">Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete this user? This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialogVisible = false">Cancel</v-btn>
          <v-btn color="error" @click="deleteUser">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import UserDetailDialog from './UserDetailDialog.vue';
import UserFormDialog from './UserFormDialog.vue';

export default {
  name: 'UserManagement',

  components: {
    UserDetailDialog,
    UserFormDialog
  },

  data() {
    return {
      // Table data
      users: [],
      filteredUsers: [],
      totalUsers: 0,
      loading: false,

      // Table options
      options: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['last_login'],
        sortDesc: [true]
      },

      // Table headers
      headers: [
        { text: '', value: 'avatar', sortable: false, width: '60px' },
        { text: 'Name', value: 'name', sortable: true },
        { text: 'Role', value: 'role', sortable: true },
        { text: 'Status', value: 'status', sortable: true },
        { text: 'Last Login', value: 'last_login', sortable: true },
        { text: 'Actions', value: 'actions', sortable: false, align: 'center', width: '120px' }
      ],

      // Filters
      filters: {
        search: '',
        role: null,
        status: null
      },

      // Filter options
      roleOptions: [
        { text: 'Admin', value: 'admin' },
        { text: 'Manager', value: 'manager' },
        { text: 'Editor', value: 'editor' },
        { text: 'User', value: 'user' }
      ],

      statusOptions: [
        { text: 'Active', value: 'active' },
        { text: 'Inactive', value: 'inactive' },
        { text: 'Pending', value: 'pending' },
        { text: 'Suspended', value: 'suspended' }
      ],

      // Dialog visibility
      userDetailDialogVisible: false,
      userFormDialogVisible: false,
      deleteDialogVisible: false,

      // Selected user
      selectedUser: null,
      isNewUser: false
    };
  },

  mounted() {
    this.fetchUsers();
  },

  methods: {
    // Fetch users from API
    async fetchUsers() {
      this.loading = true;

      try {
        // In a real implementation, this would call an API endpoint
        // For now, we'll use mock data
        await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay

        const mockUsers = this.getMockUsers();
        this.users = mockUsers;
        this.filteredUsers = [...mockUsers];
        this.totalUsers = mockUsers.length;

        // Apply sorting
        this.applySorting();

        // Apply filters
        this.applyFilters();
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        this.loading = false;
      }
    },

    // Apply filters to users
    applyFilters() {
      let filtered = [...this.users];

      // Apply search filter
      if (this.filters.search) {
        const searchTerm = this.filters.search.toLowerCase();
        filtered = filtered.filter(user =>
          user.first_name.toLowerCase().includes(searchTerm) ||
          user.last_name.toLowerCase().includes(searchTerm) ||
          user.email.toLowerCase().includes(searchTerm)
        );
      }

      // Apply role filter
      if (this.filters.role) {
        filtered = filtered.filter(user => user.role === this.filters.role);
      }

      // Apply status filter
      if (this.filters.status) {
        filtered = filtered.filter(user => user.status === this.filters.status);
      }

      this.filteredUsers = filtered;
      this.totalUsers = filtered.length;
    },

    // Reset filters
    resetFilters() {
      this.filters = {
        search: '',
        role: null,
        status: null
      };

      this.filteredUsers = [...this.users];
      this.totalUsers = this.users.length;
    },

    // Apply sorting to users
    applySorting() {
      const sortBy = this.options.sortBy[0];
      const sortDesc = this.options.sortDesc[0];

      if (!sortBy) return;

      this.filteredUsers.sort((a, b) => {
        let aValue, bValue;

        // Handle special case for name column
        if (sortBy === 'name') {
          aValue = `${a.first_name} ${a.last_name}`.toLowerCase();
          bValue = `${b.first_name} ${b.last_name}`.toLowerCase();
        } else {
          aValue = a[sortBy];
          bValue = b[sortBy];
        }

        // Handle date comparison
        if (sortBy === 'last_login') {
          aValue = aValue ? new Date(aValue).getTime() : 0;
          bValue = bValue ? new Date(bValue).getTime() : 0;
        }

        if (sortDesc) {
          return bValue > aValue ? 1 : -1;
        } else {
          return aValue > bValue ? 1 : -1;
        }
      });
    },

    // Get user initials for avatar
    getInitials(firstName, lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    },

    // Get role color
    getRoleColor(role) {
      switch (role) {
        case 'admin':
          return 'red';
        case 'manager':
          return 'orange';
        case 'editor':
          return 'blue';
        case 'user':
          return 'green';
        default:
          return 'grey';
      }
    },

    // Get status color
    getStatusColor(status) {
      switch (status) {
        case 'active':
          return 'success';
        case 'inactive':
          return 'grey';
        case 'pending':
          return 'warning';
        case 'suspended':
          return 'error';
        default:
          return 'grey';
      }
    },

    // Format date
    formatDate(dateString) {
      if (!dateString) return 'Never';

      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    },

    // View user details
    viewUser(user) {
      this.selectedUser = user;
      this.userDetailDialogVisible = true;
    },

    // Close user detail dialog
    closeUserDetailDialog() {
      this.userDetailDialogVisible = false;
      this.selectedUser = null;
    },

    // Open create user dialog
    openCreateUserDialog() {
      this.selectedUser = null;
      this.isNewUser = true;
      this.userFormDialogVisible = true;
    },

    // Edit user
    editUser(user) {
      this.selectedUser = { ...user };
      this.isNewUser = false;
      this.userFormDialogVisible = true;
    },

    // Close user form dialog
    closeUserFormDialog() {
      this.userFormDialogVisible = false;
      this.selectedUser = null;
      this.isNewUser = false;
    },

    // Save user (create or update)
    saveUser(userData) {
      if (this.isNewUser) {
        // Create new user
        // In a real implementation, this would call an API endpoint
        console.log('Creating new user:', userData);

        // Add user to list
        const newUser = {
          id: Date.now().toString(),
          ...userData,
          created_at: new Date().toISOString(),
          last_login: null
        };

        this.users.push(newUser);
      } else {
        // Update existing user
        // In a real implementation, this would call an API endpoint
        console.log('Updating user:', userData);

        // Update user in list
        const index = this.users.findIndex(u => u.id === userData.id);
        if (index !== -1) {
          this.users[index] = { ...this.users[index], ...userData };
        }
      }

      // Apply filters and sorting
      this.applyFilters();
      this.applySorting();

      // Close dialog
      this.closeUserFormDialog();
    },

    // Confirm delete user
    confirmDeleteUser(user) {
      this.selectedUser = user;
      this.deleteDialogVisible = true;
    },

    // Delete user
    deleteUser() {
      if (!this.selectedUser) return;

      // In a real implementation, this would call an API endpoint
      console.log('Deleting user:', this.selectedUser);

      // Remove user from list
      const index = this.users.findIndex(u => u.id === this.selectedUser.id);
      if (index !== -1) {
        this.users.splice(index, 1);
      }

      // Apply filters and sorting
      this.applyFilters();
      this.applySorting();

      // Close dialog
      this.deleteDialogVisible = false;
      this.selectedUser = null;
    },

    // Get mock users for development
    getMockUsers() {
      return [
        {
          id: '1',
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          avatar: null,
          created_at: '2023-01-15T08:30:00Z',
          last_login: '2023-06-01T14:25:00Z'
        },
        {
          id: '2',
          first_name: 'Jane',
          last_name: 'Smith',
          email: '<EMAIL>',
          role: 'manager',
          status: 'active',
          avatar: null,
          created_at: '2023-02-20T10:15:00Z',
          last_login: '2023-05-28T09:10:00Z'
        },
        {
          id: '3',
          first_name: 'Robert',
          last_name: 'Johnson',
          email: '<EMAIL>',
          role: 'editor',
          status: 'inactive',
          avatar: null,
          created_at: '2023-03-10T15:45:00Z',
          last_login: '2023-04-15T11:30:00Z'
        },
        {
          id: '4',
          first_name: 'Emily',
          last_name: 'Williams',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          avatar: null,
          created_at: '2023-03-25T09:20:00Z',
          last_login: '2023-05-30T16:45:00Z'
        },
        {
          id: '5',
          first_name: 'Michael',
          last_name: 'Brown',
          email: '<EMAIL>',
          role: 'user',
          status: 'pending',
          avatar: null,
          created_at: '2023-04-05T14:10:00Z',
          last_login: null
        },
        {
          id: '6',
          first_name: 'Sarah',
          last_name: 'Davis',
          email: '<EMAIL>',
          role: 'manager',
          status: 'active',
          avatar: null,
          created_at: '2023-04-18T11:25:00Z',
          last_login: '2023-05-29T10:15:00Z'
        },
        {
          id: '7',
          first_name: 'David',
          last_name: 'Miller',
          email: '<EMAIL>',
          role: 'editor',
          status: 'suspended',
          avatar: null,
          created_at: '2023-05-02T08:50:00Z',
          last_login: '2023-05-10T09:30:00Z'
        },
        {
          id: '8',
          first_name: 'Jennifer',
          last_name: 'Wilson',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          avatar: null,
          created_at: '2023-05-15T13:40:00Z',
          last_login: '2023-05-31T15:20:00Z'
        },
        {
          id: '9',
          first_name: 'James',
          last_name: 'Taylor',
          email: '<EMAIL>',
          role: 'user',
          status: 'inactive',
          avatar: null,
          created_at: '2023-05-20T10:30:00Z',
          last_login: '2023-05-25T14:10:00Z'
        },
        {
          id: '10',
          first_name: 'Lisa',
          last_name: 'Anderson',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          avatar: null,
          created_at: '2023-05-25T09:15:00Z',
          last_login: '2023-06-01T08:45:00Z'
        }
      ];
    }
  }
};
</script>

<style scoped>
.user-management {
  padding: 20px;
}

.user-table {
  width: 100%;
}
</style>
