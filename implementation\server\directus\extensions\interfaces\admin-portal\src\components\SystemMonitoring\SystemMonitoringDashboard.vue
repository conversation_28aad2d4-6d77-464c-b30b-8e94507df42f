<template>
  <div class="system-monitoring-dashboard">
    <div class="dashboard-header">
      <h2>System Monitoring</h2>
      <div class="refresh-controls">
        <v-select
          v-model="refreshInterval"
          :items="refreshIntervals"
          item-text="label"
          item-value="value"
          label="Auto-refresh"
          dense
          outlined
          hide-details
          class="refresh-select"
        />
        <v-btn
          color="primary"
          @click="refreshAllData"
          :loading="isRefreshing"
          :disabled="isRefreshing"
          class="refresh-button"
        >
          <v-icon left>mdi-refresh</v-icon>
          Refresh
        </v-btn>
      </div>
    </div>

    <v-row>
      <v-col cols="12">
        <system-status-overview
          :system-health="systemHealth"
          :loading="loadingSystemHealth"
          @refresh="fetchSystemHealth"
        />
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="6">
        <system-metrics
          :metrics="systemHealth?.system"
          :loading="loadingSystemHealth"
          @refresh="fetchSystemHealth"
        />
      </v-col>
      <v-col cols="12" md="6">
        <service-status
          :services="systemHealth?.services"
          :loading="loadingSystemHealth"
          @refresh="fetchSystemHealth"
        />
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12">
        <alert-summary
          :alerts="systemHealth?.alerts"
          :loading="loadingSystemHealth"
          @refresh="fetchSystemHealth"
          @view-all="navigateToAlerts"
        />
      </v-col>
    </v-row>

    <v-tabs v-model="activeTab" background-color="transparent" grow>
      <v-tab>API Metrics</v-tab>
      <v-tab>Database Metrics</v-tab>
      <v-tab>Logs</v-tab>
      <v-tab>User Activity</v-tab>
    </v-tabs>

    <v-tabs-items v-model="activeTab">
      <v-tab-item>
        <api-metrics
          :api-usage="apiMetrics?.usage"
          :api-performance="apiMetrics?.performance"
          :rate-limiting="apiMetrics?.rateLimiting"
          :loading="loadingApiMetrics"
          @refresh="fetchApiMetrics"
        />
      </v-tab-item>
      <v-tab-item>
        <database-metrics
          :db-performance="databaseMetrics?.performance"
          :db-health="databaseMetrics?.health"
          :db-tables="databaseMetrics?.tables"
          :loading="loadingDatabaseMetrics"
          @refresh="fetchDatabaseMetrics"
        />
      </v-tab-item>
      <v-tab-item>
        <log-viewer
          :logs="logs"
          :log-stats="logStats"
          :loading="loadingLogs"
          @refresh="fetchLogs"
          @search="searchLogs"
        />
      </v-tab-item>
      <v-tab-item>
        <user-activity
          :active-sessions="userActivity?.sessions"
          :auth-events="userActivity?.authEvents"
          :activity-stats="userActivity?.stats"
          :loading="loadingUserActivity"
          @refresh="fetchUserActivity"
        />
      </v-tab-item>
    </v-tabs-items>
  </div>
</template>

<script>
import SystemStatusOverview from './SystemStatusOverview.vue';
import SystemMetrics from './SystemMetrics.vue';
import ServiceStatus from './ServiceStatus.vue';
import AlertSummary from './AlertSummary.vue';
import ApiMetrics from './ApiMetrics.vue';
import DatabaseMetrics from './DatabaseMetrics.vue';
import LogViewer from './LogViewer.vue';
import UserActivity from './UserActivity.vue';

export default {
  name: 'SystemMonitoringDashboard',
  
  components: {
    SystemStatusOverview,
    SystemMetrics,
    ServiceStatus,
    AlertSummary,
    ApiMetrics,
    DatabaseMetrics,
    LogViewer,
    UserActivity
  },
  
  data() {
    return {
      // Tab control
      activeTab: 0,
      
      // Refresh control
      isRefreshing: false,
      refreshInterval: 60000, // 1 minute default
      refreshIntervals: [
        { label: 'Off', value: 0 },
        { label: '30 seconds', value: 30000 },
        { label: '1 minute', value: 60000 },
        { label: '5 minutes', value: 300000 },
        { label: '15 minutes', value: 900000 }
      ],
      refreshTimerId: null,
      
      // System health data
      systemHealth: null,
      loadingSystemHealth: false,
      
      // API metrics data
      apiMetrics: {
        usage: null,
        performance: null,
        rateLimiting: null
      },
      loadingApiMetrics: false,
      
      // Database metrics data
      databaseMetrics: {
        performance: null,
        health: null,
        tables: null
      },
      loadingDatabaseMetrics: false,
      
      // Logs data
      logs: [],
      logStats: null,
      loadingLogs: false,
      
      // User activity data
      userActivity: {
        sessions: [],
        authEvents: [],
        stats: null
      },
      loadingUserActivity: false
    };
  },
  
  mounted() {
    this.fetchAllData();
    this.setupAutoRefresh();
  },
  
  beforeDestroy() {
    this.clearAutoRefresh();
  },
  
  watch: {
    refreshInterval(newValue) {
      this.clearAutoRefresh();
      if (newValue > 0) {
        this.setupAutoRefresh();
      }
    }
  },
  
  methods: {
    // Refresh all data
    refreshAllData() {
      this.isRefreshing = true;
      
      Promise.all([
        this.fetchSystemHealth(),
        this.fetchApiMetrics(),
        this.fetchDatabaseMetrics(),
        this.fetchLogs(),
        this.fetchUserActivity()
      ]).finally(() => {
        this.isRefreshing = false;
      });
    },
    
    // Fetch all data on initial load
    fetchAllData() {
      this.fetchSystemHealth();
      
      // Only fetch data for the active tab initially
      switch (this.activeTab) {
        case 0:
          this.fetchApiMetrics();
          break;
        case 1:
          this.fetchDatabaseMetrics();
          break;
        case 2:
          this.fetchLogs();
          break;
        case 3:
          this.fetchUserActivity();
          break;
      }
    },
    
    // Set up auto-refresh timer
    setupAutoRefresh() {
      if (this.refreshInterval > 0) {
        this.refreshTimerId = setInterval(() => {
          this.fetchSystemHealth();
          
          // Only refresh data for the active tab
          switch (this.activeTab) {
            case 0:
              this.fetchApiMetrics();
              break;
            case 1:
              this.fetchDatabaseMetrics();
              break;
            case 2:
              this.fetchLogs();
              break;
            case 3:
              this.fetchUserActivity();
              break;
          }
        }, this.refreshInterval);
      }
    },
    
    // Clear auto-refresh timer
    clearAutoRefresh() {
      if (this.refreshTimerId) {
        clearInterval(this.refreshTimerId);
        this.refreshTimerId = null;
      }
    },
    
    // Fetch system health data
    async fetchSystemHealth() {
      this.loadingSystemHealth = true;
      
      try {
        const response = await fetch('/api/monitoring/system-health');
        const data = await response.json();
        
        if (data.success) {
          this.systemHealth = data.data;
        } else {
          console.error('Error fetching system health:', data.error);
        }
      } catch (error) {
        console.error('Error fetching system health:', error);
      } finally {
        this.loadingSystemHealth = false;
      }
    },
    
    // Fetch API metrics data
    async fetchApiMetrics() {
      this.loadingApiMetrics = true;
      
      try {
        const [usageResponse, performanceResponse, rateLimitingResponse] = await Promise.all([
          fetch('/api/monitoring/api-metrics/usage'),
          fetch('/api/monitoring/api-metrics/performance'),
          fetch('/api/monitoring/api-metrics/rate-limiting')
        ]);
        
        const usageData = await usageResponse.json();
        const performanceData = await performanceResponse.json();
        const rateLimitingData = await rateLimitingResponse.json();
        
        if (usageData.success && performanceData.success && rateLimitingData.success) {
          this.apiMetrics = {
            usage: usageData.data,
            performance: performanceData.data,
            rateLimiting: rateLimitingData.data
          };
        } else {
          console.error('Error fetching API metrics');
        }
      } catch (error) {
        console.error('Error fetching API metrics:', error);
      } finally {
        this.loadingApiMetrics = false;
      }
    },
    
    // Fetch database metrics data
    async fetchDatabaseMetrics() {
      this.loadingDatabaseMetrics = true;
      
      try {
        const [performanceResponse, healthResponse, tablesResponse] = await Promise.all([
          fetch('/api/monitoring/database-metrics/performance'),
          fetch('/api/monitoring/database-metrics/health'),
          fetch('/api/monitoring/database-metrics/tables')
        ]);
        
        const performanceData = await performanceResponse.json();
        const healthData = await healthResponse.json();
        const tablesData = await tablesResponse.json();
        
        if (performanceData.success && healthData.success && tablesData.success) {
          this.databaseMetrics = {
            performance: performanceData.data,
            health: healthData.data,
            tables: tablesData.data
          };
        } else {
          console.error('Error fetching database metrics');
        }
      } catch (error) {
        console.error('Error fetching database metrics:', error);
      } finally {
        this.loadingDatabaseMetrics = false;
      }
    },
    
    // Fetch logs data
    async fetchLogs() {
      this.loadingLogs = true;
      
      try {
        const [logsResponse, statsResponse] = await Promise.all([
          fetch('/api/monitoring/logs'),
          fetch('/api/monitoring/logs/statistics')
        ]);
        
        const logsData = await logsResponse.json();
        const statsData = await statsResponse.json();
        
        if (logsData.success && statsData.success) {
          this.logs = logsData.data.logs;
          this.logStats = statsData.data;
        } else {
          console.error('Error fetching logs');
        }
      } catch (error) {
        console.error('Error fetching logs:', error);
      } finally {
        this.loadingLogs = false;
      }
    },
    
    // Search logs with filters
    async searchLogs(filters) {
      this.loadingLogs = true;
      
      try {
        const queryParams = new URLSearchParams();
        
        if (filters.level && filters.level !== 'all') {
          queryParams.append('level', filters.level);
        }
        
        if (filters.service && filters.service !== 'all') {
          queryParams.append('service', filters.service);
        }
        
        if (filters.startTime) {
          queryParams.append('start_time', filters.startTime);
        }
        
        if (filters.endTime) {
          queryParams.append('end_time', filters.endTime);
        }
        
        if (filters.search) {
          queryParams.append('search', filters.search);
        }
        
        if (filters.limit) {
          queryParams.append('limit', filters.limit);
        }
        
        if (filters.offset) {
          queryParams.append('offset', filters.offset);
        }
        
        const response = await fetch(`/api/monitoring/logs?${queryParams.toString()}`);
        const data = await response.json();
        
        if (data.success) {
          this.logs = data.data.logs;
        } else {
          console.error('Error searching logs:', data.error);
        }
      } catch (error) {
        console.error('Error searching logs:', error);
      } finally {
        this.loadingLogs = false;
      }
    },
    
    // Fetch user activity data
    async fetchUserActivity() {
      this.loadingUserActivity = true;
      
      try {
        const [sessionsResponse, authEventsResponse, statsResponse] = await Promise.all([
          fetch('/api/monitoring/user-activity/sessions'),
          fetch('/api/monitoring/user-activity/auth-events'),
          fetch('/api/monitoring/user-activity/statistics')
        ]);
        
        const sessionsData = await sessionsResponse.json();
        const authEventsData = await authEventsResponse.json();
        const statsData = await statsResponse.json();
        
        if (sessionsData.success && authEventsData.success && statsData.success) {
          this.userActivity = {
            sessions: sessionsData.data.sessions,
            authEvents: authEventsData.data.events,
            stats: statsData.data
          };
        } else {
          console.error('Error fetching user activity');
        }
      } catch (error) {
        console.error('Error fetching user activity:', error);
      } finally {
        this.loadingUserActivity = false;
      }
    },
    
    // Navigate to alerts page
    navigateToAlerts() {
      // In a real implementation, this would navigate to the alerts page
      console.log('Navigate to alerts page');
    }
  }
};
</script>

<style scoped>
.system-monitoring-dashboard {
  padding: 20px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.refresh-select {
  width: 150px;
}

.v-tabs {
  margin-top: 20px;
  margin-bottom: 20px;
}
</style>
