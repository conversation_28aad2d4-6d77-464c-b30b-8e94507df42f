import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHand<PERSON>, createError, ErrorCategory } from '../../shared/utils/error-handler';
import { LLMService } from '../../services/llm-service';

// Define the request schema
const QueryRequestSchema = z.object({
  vendor_id: z.string().uuid(),
  message: z.string().min(1).max(1000),
  context: z
    .array(
      z.object({
        role: z.enum(['system', 'user', 'assistant']),
        content: z.string(),
      }),
    )
    .optional(),
  options: z
    .object({
      model: z.string().optional(),
      temperature: z.number().min(0).max(2).optional(),
      max_tokens: z.number().int().positive().optional(),
      stream: z.boolean().optional(),
    })
    .optional(),
});

/**
 * LLM Query API endpoint
 *
 * This endpoint processes LLM queries and returns responses.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate request body
    const bodyResult = QueryRequestSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid request body', details: bodyResult.error.format() });
    }

    // Extract request data
    const { vendor_id, message, context = [], options = {} } = bodyResult.data;

    // Log the request
    logger.info('LLM query request', {
      vendor_id,
      message_length: message.length,
      context_length: context.length,
      options,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get vendor
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('id, name')
      .eq('id', vendor_id)
      .single();

    if (vendorError) {
      logger.error('Error fetching vendor', { error: vendorError, vendor_id });
      return res.status(500).json({ error: 'Error fetching vendor' });
    }

    if (!vendor) {
      return res.status(404).json({ error: 'Vendor not found' });
    }

    // Get LLM usage metrics
    const { data: usageMetrics, error: usageError } = await supabase
      .from('llm_usage_metrics')
      .select('id, llm_monthly_token_limit, llm_tokens_used, llm_reset_date')
      .eq('vendor_id', vendor_id)
      .single();

    if (usageError) {
      logger.error('Error fetching LLM usage metrics', { error: usageError, vendor_id });
      return res.status(500).json({ error: 'Error fetching LLM usage metrics' });
    }

    // Create usage metrics if they don't exist
    let metrics = usageMetrics;
    if (!metrics) {
      const { data: newMetrics, error: createError } = await supabase
        .from('llm_usage_metrics')
        .insert({
          vendor_id,
          llm_monthly_token_limit: 100000,
          llm_tokens_used: 0,
          llm_reset_date: new Date().toISOString().split('T')[0],
        })
        .select()
        .single();

      if (createError) {
        logger.error('Error creating LLM usage metrics', { error: createError, vendor_id });
        return res.status(500).json({ error: 'Error creating LLM usage metrics' });
      }

      metrics = newMetrics;
    }

    // Check if the vendor has exceeded their token limit
    const currentDate = new Date().toISOString().split('T')[0];
    const resetDate = new Date(metrics.llm_reset_date).toISOString().split('T')[0];

    // Reset usage if we're in a new month
    if (currentDate > resetDate) {
      const { error: resetError } = await supabase
        .from('llm_usage_metrics')
        .update({
          llm_tokens_used: 0,
          llm_reset_date: currentDate,
        })
        .eq('id', metrics.id);

      if (resetError) {
        logger.error('Error resetting LLM usage metrics', {
          error: resetError,
          metrics_id: metrics.id,
        });
        return res.status(500).json({ error: 'Error resetting LLM usage metrics' });
      }

      metrics.llm_tokens_used = 0;
      metrics.llm_reset_date = currentDate;
    }

    // Check if the vendor has exceeded their token limit
    if (metrics.llm_tokens_used >= metrics.llm_monthly_token_limit) {
      return res.status(429).json({
        error: 'Token limit exceeded',
        details: {
          limit: metrics.llm_monthly_token_limit,
          used: metrics.llm_tokens_used,
          reset_date: metrics.llm_reset_date,
        },
      });
    }

    // Initialize LLM service
    const llmService = new LLMService();

    // Process the query
    const { response, tokens } = await llmService.processQuery(message, context, options);

    // Update token usage
    const { error: updateError } = await supabase
      .from('llm_usage_metrics')
      .update({
        llm_tokens_used: metrics.llm_tokens_used + tokens,
      })
      .eq('id', metrics.id);

    if (updateError) {
      logger.error('Error updating LLM usage metrics', {
        error: updateError,
        metrics_id: metrics.id,
      });
      // Continue anyway, we don't want to fail the request just because we couldn't update the metrics
    }

    // Log the response
    logger.info('LLM query response', {
      vendor_id,
      tokens_used: tokens,
      total_tokens_used: metrics.llm_tokens_used + tokens,
    });

    // Return response
    return res.status(200).json({
      status: 'success',
      data: {
        response,
        tokens_used: tokens,
        tokens_remaining: metrics.llm_monthly_token_limit - (metrics.llm_tokens_used + tokens),
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      },
    });
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
