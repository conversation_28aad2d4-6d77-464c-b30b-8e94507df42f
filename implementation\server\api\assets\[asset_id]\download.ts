import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../shared/utils/logger';
import { errorHandler } from '../../../shared/utils/error-handler';
import { AssetService } from '../../../services/asset-service';
import axios from 'axios';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  asset_id: z.string().uuid(),
  version: z.string().optional(),
  client_id: z.string().optional(),
});

/**
 * Asset Download API endpoint
 *
 * This endpoint streams an asset file to the client.
 * It can optionally specify a version.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse({
      asset_id: req.query.asset_id,
      version: req.query.version,
      client_id: req.query.client_id,
    });

    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { asset_id, version, client_id } = queryResult.data;

    // Log the request
    logger.info('Asset download request', {
      asset_id,
      version,
      client_id,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create asset service
    const assetService = new AssetService(supabase);

    // Get asset
    const asset = await assetService.getAsset(asset_id);
    if (!asset) {
      return res.status(404).json({ error: 'Asset not found' });
    }

    // Get asset version
    let assetUrl: string;
    let assetHash: string;
    let assetSize: number;

    if (version) {
      // Get specific version
      const { data: versionData, error: versionError } = await supabase
        .from('asset_versions')
        .select('*')
        .eq('asset_id', asset_id)
        .eq('version', version)
        .single();

      if (versionError) {
        logger.error('Error fetching asset version', { error: versionError, asset_id, version });
        return res.status(404).json({ error: 'Asset version not found' });
      }

      assetUrl = versionData.url;
      assetHash = versionData.hash;
      assetSize = versionData.size;
    } else {
      // Use latest version
      assetUrl = asset.url;
      assetHash = asset.hash;
      assetSize = asset.size;
    }

    // Record client download if client_id is provided
    if (client_id) {
      // In a real implementation, we would record this in a database
      logger.info('Recording client download', { client_id, asset_id, version });
    }

    // Stream the asset
    try {
      // Get the asset from its URL
      const response = await axios({
        method: 'get',
        url: assetUrl,
        responseType: 'stream',
      });

      // Set headers
      res.setHeader('Content-Type', response.headers['content-type'] || 'application/octet-stream');
      res.setHeader('Content-Length', assetSize);
      res.setHeader('Content-Disposition', `attachment; filename=${asset.name}`);
      res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1 year
      res.setHeader('ETag', `"${assetHash}"`);

      // Stream the response
      response.data.pipe(res);
    } catch (error) {
      logger.error('Error streaming asset', { error, asset_id, assetUrl });
      return res.status(500).json({ error: 'Error streaming asset' });
    }
  } catch (error) {
    logger.error('Unexpected error in asset download endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
