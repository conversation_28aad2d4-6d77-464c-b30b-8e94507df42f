import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { PerformanceOptimizer } from '../src/utils/PerformanceOptimizer';

describe('PerformanceOptimizer', () => {
  let cache;

  beforeEach(() => {
    // Setup fake timers
    vi.useFakeTimers();

    // Create a new cache instance for each test
    cache = new PerformanceOptimizer(10, 1000, {
      maxMemorySize: 10000,
      evictionThreshold: 0.8,
    });
  });

  afterEach(() => {
    // Clean up
    if (cache) {
      cache.dispose();
    }

    // Restore timers
    vi.restoreAllMocks();
  });

  it('should initialize with correct properties', () => {
    expect(cache.maxSize).toBe(10);
    expect(cache.ttl).toBe(1000);
    expect(cache.maxMemorySize).toBe(10000);
    expect(cache.evictionThreshold).toBe(0.8);
    expect(cache.hits).toBe(0);
    expect(cache.misses).toBe(0);
    expect(cache.evictions).toBe(0);
    expect(cache.expirations).toBe(0);
    expect(cache.totalMemoryUsed).toBe(0);
  });

  it('should store and retrieve items', () => {
    cache.set('key1', 'value1');
    expect(cache.get('key1')).toBe('value1');
    expect(cache.hits).toBe(1);
    expect(cache.misses).toBe(0);
  });

  it('should return null for non-existent items', () => {
    expect(cache.get('nonexistent')).toBeNull();
    expect(cache.hits).toBe(0);
    expect(cache.misses).toBe(1);
  });

  it('should expire items after TTL', () => {
    cache.set('key1', 'value1');

    // Advance time past TTL
    vi.advanceTimersByTime(1500);

    expect(cache.get('key1')).toBeNull();
    expect(cache.hits).toBe(0);
    expect(cache.misses).toBe(1);
    expect(cache.expirations).toBe(1);
  });

  it('should evict items when memory limit is reached', () => {
    // Create items that will exceed the memory limit
    for (let i = 0; i < 5; i++) {
      const key = `key${i}`;
      const value = 'x'.repeat(2000); // Each item is about 2000 bytes
      cache.set(key, value);
    }

    // Check that some items were evicted
    expect(cache.evictions).toBeGreaterThan(0);
    expect(cache.totalMemoryUsed).toBeLessThan(10000);
  });

  it('should evict items when cache is full', () => {
    // Fill the cache
    for (let i = 0; i < 10; i++) {
      cache.set(`key${i}`, `value${i}`);
    }

    // Access some items to update their access time
    cache.get('key0');
    cache.get('key2');
    cache.get('key4');

    // Add a new item to trigger eviction
    cache.set('key10', 'value10');

    // The new item should be in the cache
    expect(cache.get('key10')).not.toBeNull(); // Just added

    // At least one item should have been evicted
    expect(cache.evictions).toBeGreaterThan(0);

    // Cache size should still be at most maxSize
    expect(cache.cache.size).toBeLessThanOrEqual(cache.maxSize);
  });

  it('should track memory usage correctly', () => {
    const value1 = 'x'.repeat(1000); // ~2000 bytes
    const value2 = 'y'.repeat(1500); // ~3000 bytes

    cache.set('key1', value1);
    const memoryAfterFirst = cache.totalMemoryUsed;

    cache.set('key2', value2);
    const memoryAfterSecond = cache.totalMemoryUsed;

    expect(memoryAfterSecond).toBeGreaterThan(memoryAfterFirst);

    // Remove an item and check memory decreases
    cache.cache.delete('key1');
    cache.totalMemoryUsed -= 2000;

    expect(cache.totalMemoryUsed).toBeLessThan(memoryAfterSecond);
  });

  it('should provide accurate statistics', () => {
    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    cache.get('key1');
    cache.get('nonexistent');

    const stats = cache.getStats();

    expect(stats.size).toBe(2);
    expect(stats.hits).toBe(1);
    expect(stats.misses).toBe(1);
    expect(stats.hitRatio).toBe(0.5);
    expect(stats.memoryUsed).toBeGreaterThan(0);
  });

  it('should clean up resources when disposed', () => {
    const clearIntervalSpy = vi.spyOn(global, 'clearInterval');

    cache.set('key1', 'value1');
    cache.dispose();

    expect(clearIntervalSpy).toHaveBeenCalled();
    expect(cache.cache.size).toBe(0);
    expect(cache.totalMemoryUsed).toBe(0);
  });

  it('should automatically clean up expired items', () => {
    cache.set('key1', 'value1', { ttl: 500 });
    cache.set('key2', 'value2', { ttl: 1500 });

    // Advance time to trigger cleanup
    vi.advanceTimersByTime(1000);

    // Manually trigger cleanup (normally done by interval)
    cache.cleanupExpiredItems();

    expect(cache.get('key1')).toBeNull(); // Expired
    expect(cache.get('key2')).not.toBeNull(); // Not expired yet
    expect(cache.expirations).toBe(1);
  });
});
