<template>
  <div class="product-manager">
    <div class="header">
      <h2>Product Management</h2>
      <div class="actions">
        <button class="btn btn-primary" @click="showAddProductModal = true">
          <i class="material-icons">add</i> Add Product
        </button>
        <div class="search">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search products..."
            @input="filterProducts"
          />
          <i class="material-icons">search</i>
        </div>
      </div>
    </div>

    <div class="filters">
      <div class="filter-row">
        <div class="filter">
          <label>Status:</label>
          <select v-model="statusFilter" @change="filterProducts">
            <option value="all">All</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="archived">Archived</option>
          </select>
        </div>
        <div class="filter">
          <label>Category:</label>
          <select v-model="categoryFilter" @change="filterProducts">
            <option value="all">All Categories</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>
        <div class="filter">
          <label>Date Range:</label>
          <div class="date-range-selector">
            <select v-model="dateRangeFilter" @change="handleDateRangeChange">
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="last7days">Last 7 Days</option>
              <option value="last30days">Last 30 Days</option>
              <option value="thisMonth">This Month</option>
              <option value="lastMonth">Last Month</option>
              <option value="custom">Custom Range</option>
            </select>
            <div v-if="dateRangeFilter === 'custom'" class="custom-date-range">
              <input
                type="date"
                v-model="customDateRange.start"
                @change="filterProducts"
                :max="customDateRange.end || today"
              />
              <span>to</span>
              <input
                type="date"
                v-model="customDateRange.end"
                @change="filterProducts"
                :min="customDateRange.start"
                :max="today"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="filter-row">
        <div class="filter">
          <label>Sort By:</label>
          <select v-model="sortBy" @change="filterProducts">
            <option value="name">Name</option>
            <option value="date_created">Date Created</option>
            <option value="date_updated">Date Updated</option>
            <option value="category">Category</option>
            <option value="views">Views</option>
            <option value="interactions">Interactions</option>
          </select>
        </div>
        <div class="filter">
          <label>Order:</label>
          <select v-model="sortOrder" @change="filterProducts">
            <option value="asc">Ascending</option>
            <option value="desc">Descending</option>
          </select>
        </div>
        <div class="filter">
          <label>View:</label>
          <div class="view-toggle">
            <button
              class="view-toggle-btn"
              :class="{ active: viewMode === 'grid' }"
              @click="viewMode = 'grid'"
            >
              <i class="material-icons">grid_view</i>
            </button>
            <button
              class="view-toggle-btn"
              :class="{ active: viewMode === 'list' }"
              @click="viewMode = 'list'"
            >
              <i class="material-icons">view_list</i>
            </button>
          </div>
        </div>
        <div class="filter filter-tags">
          <label>Tags:</label>
          <div class="tag-selector">
            <div
              v-for="tag in availableTags"
              :key="tag.id"
              class="tag"
              :class="{ active: selectedTags.includes(tag.id) }"
              @click="toggleTag(tag.id)"
            >
              {{ tag.name }}
            </div>
          </div>
        </div>
      </div>

      <div class="active-filters" v-if="hasActiveFilters">
        <div class="active-filters-label">Active Filters:</div>
        <div class="active-filter-tags">
          <div v-if="statusFilter !== 'all'" class="active-filter">
            Status: {{ statusFilter }}
            <button @click="clearFilter('status')" class="clear-filter">×</button>
          </div>
          <div v-if="categoryFilter !== 'all'" class="active-filter">
            Category: {{ getCategoryName(categoryFilter) }}
            <button @click="clearFilter('category')" class="clear-filter">×</button>
          </div>
          <div v-if="dateRangeFilter !== 'all'" class="active-filter">
            Date: {{ getDateRangeLabel() }}
            <button @click="clearFilter('dateRange')" class="clear-filter">×</button>
          </div>
          <div v-for="tagId in selectedTags" :key="tagId" class="active-filter">
            Tag: {{ getTagName(tagId) }}
            <button @click="removeTag(tagId)" class="clear-filter">×</button>
          </div>
        </div>
        <button @click="clearAllFilters" class="clear-all-filters">
          Clear All
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading products...</span>
    </div>

    <div v-else-if="filteredProducts.length === 0" class="empty-state">
      <i class="material-icons">inventory_2</i>
      <h3>No products found</h3>
      <p v-if="searchQuery">No products match your search criteria. Try adjusting your filters.</p>
      <p v-else>You haven't added any products yet. Click "Add Product" to get started.</p>
    </div>

    <div v-else>
      <!-- Grid View -->
      <div v-if="viewMode === 'grid'" class="product-grid">
        <div
          v-for="product in filteredProducts"
          :key="product.id"
          class="product-card"
          :class="{
            'product-card--draft': product.status === 'draft',
            'product-card--archived': product.status === 'archived'
          }"
        >
          <div class="product-image">
            <img v-if="product.thumbnail" :src="product.thumbnail" :alt="product.name">
            <div v-else class="placeholder-image">
              <i class="material-icons">image</i>
            </div>
            <div class="product-status" :class="product.status">
              {{ product.status }}
            </div>
          </div>
          <div class="product-info">
            <h3>{{ product.name }}</h3>
            <p class="product-description">{{ product.description || 'No description' }}</p>
            <div class="product-meta">
              <span class="product-id">ID: {{ product.id.substring(0, 8) }}...</span>
              <span class="product-date">Updated: {{ formatDate(product.date_updated) }}</span>
            </div>
            <div class="product-tags" v-if="product.tags && product.tags.length > 0">
              <div v-for="tagId in product.tags" :key="tagId" class="product-tag">
                {{ getTagName(tagId) }}
              </div>
            </div>
            <div class="product-category" v-if="product.category">
              <span class="category-label">Category:</span>
              <span class="category-name">{{ getCategoryName(product.category) }}</span>
            </div>
            <div class="product-variants" v-if="product.hasVariants">
              <div class="variant-badge">
                <i class="material-icons">style</i>
                <span>{{ product.variants ? product.variants.length : 0 }} variants</span>
              </div>
            </div>
          </div>
          <div class="product-actions">
            <button class="btn btn-icon" @click="editProduct(product)" title="Edit Product">
              <i class="material-icons">edit</i>
            </button>
            <button class="btn btn-icon" @click="viewDisplays(product)" title="View in Showroom">
              <i class="material-icons">view_in_ar</i>
            </button>
            <button class="btn btn-icon" @click="duplicateProduct(product)" title="Duplicate Product">
              <i class="material-icons">content_copy</i>
            </button>
            <button class="btn btn-icon" @click="confirmDeleteProduct(product)" title="Delete Product">
              <i class="material-icons">delete</i>
            </button>
          </div>
        </div>
      </div>

      <!-- List View -->
      <div v-else-if="viewMode === 'list'" class="product-list">
        <table class="product-table">
          <thead>
            <tr>
              <th class="thumbnail-col">Image</th>
              <th class="name-col">Name</th>
              <th class="category-col">Category</th>
              <th class="variants-col">Variants</th>
              <th class="status-col">Status</th>
              <th class="date-col">Last Updated</th>
              <th class="metrics-col">Metrics</th>
              <th class="actions-col">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="product in filteredProducts"
              :key="product.id"
              :class="{
                'row-draft': product.status === 'draft',
                'row-archived': product.status === 'archived'
              }"
            >
              <td class="thumbnail-col">
                <div class="list-thumbnail">
                  <img v-if="product.thumbnail" :src="product.thumbnail" :alt="product.name">
                  <div v-else class="placeholder-thumbnail">
                    <i class="material-icons">image</i>
                  </div>
                </div>
              </td>
              <td class="name-col">
                <div class="product-name-container">
                  <span class="product-name">{{ product.name }}</span>
                  <div class="product-tags" v-if="product.tags && product.tags.length > 0">
                    <div v-for="tagId in product.tags.slice(0, 2)" :key="tagId" class="product-tag small">
                      {{ getTagName(tagId) }}
                    </div>
                    <div v-if="product.tags.length > 2" class="product-tag small more">
                      +{{ product.tags.length - 2 }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="category-col">
                {{ product.category ? getCategoryName(product.category) : 'Uncategorized' }}
              </td>
              <td class="variants-col">
                <div v-if="product.hasVariants" class="variants-info">
                  <div class="variant-count">{{ product.variants ? product.variants.length : 0 }}</div>
                  <div class="variant-preview" v-if="product.variants && product.variants.length > 0">
                    <div v-for="(variant, index) in product.variants.slice(0, 2)" :key="index" class="variant-preview-item">
                      {{ variant.name }}
                    </div>
                    <div v-if="product.variants.length > 2" class="variant-preview-more">
                      +{{ product.variants.length - 2 }} more
                    </div>
                  </div>
                </div>
                <div v-else class="no-variants">—</div>
              </td>
              <td class="status-col">
                <div class="status-badge" :class="product.status">
                  {{ product.status }}
                </div>
              </td>
              <td class="date-col">
                {{ formatDate(product.date_updated) }}
              </td>
              <td class="metrics-col">
                <div class="metrics-container">
                  <div class="metric">
                    <i class="material-icons">visibility</i>
                    <span>{{ product.views || 0 }}</span>
                  </div>
                  <div class="metric">
                    <i class="material-icons">touch_app</i>
                    <span>{{ product.interactions || 0 }}</span>
                  </div>
                </div>
              </td>
              <td class="actions-col">
                <div class="list-actions">
                  <button class="btn btn-icon" @click="editProduct(product)" title="Edit Product">
                    <i class="material-icons">edit</i>
                  </button>
                  <button class="btn btn-icon" @click="viewDisplays(product)" title="View in Showroom">
                    <i class="material-icons">view_in_ar</i>
                  </button>
                  <button class="btn btn-icon" @click="duplicateProduct(product)" title="Duplicate Product">
                    <i class="material-icons">content_copy</i>
                  </button>
                  <button class="btn btn-icon" @click="confirmDeleteProduct(product)" title="Delete Product">
                    <i class="material-icons">delete</i>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add/Edit Product Modal -->
    <div v-if="showAddProductModal || showEditProductModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ showEditProductModal ? 'Edit Product' : 'Add New Product' }}</h3>
          <button class="btn btn-icon" @click="closeModals">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="product-name">Product Name</label>
            <input
              id="product-name"
              type="text"
              v-model="productForm.name"
              placeholder="Enter product name"
              required
            />
          </div>
          <div class="form-group">
            <label for="product-description">Description</label>
            <textarea
              id="product-description"
              v-model="productForm.description"
              placeholder="Enter product description"
              rows="4"
            ></textarea>
          </div>
          <div class="form-row">
            <div class="form-group half">
              <label for="product-status">Status</label>
              <select id="product-status" v-model="productForm.status">
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
              </select>
            </div>
            <div class="form-group half">
              <label for="product-category">Category</label>
              <select id="product-category" v-model="productForm.category">
                <option value="">-- Select Category --</option>
                <option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label>Tags</label>
            <div class="tag-selector modal-tags">
              <div
                v-for="tag in availableTags"
                :key="tag.id"
                class="tag"
                :class="{ active: productForm.tags.includes(tag.id) }"
                @click="toggleProductTag(tag.id)"
              >
                {{ tag.name }}
              </div>
            </div>
          </div>

          <div class="form-group">
            <label for="product-thumbnail">Thumbnail</label>
            <div class="file-upload">
              <input
                id="product-thumbnail"
                type="file"
                @change="handleFileUpload"
                accept="image/*"
              />
              <div class="file-preview">
                <img v-if="thumbnailPreview" :src="thumbnailPreview" alt="Thumbnail preview">
                <div v-else class="placeholder-image">
                  <i class="material-icons">image</i>
                  <span>No image selected</span>
                </div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label>Product Assets</label>
            <div class="asset-manager">
              <div class="asset-list">
                <div v-if="productForm.assets && productForm.assets.length > 0">
                  <div v-for="(asset, index) in productForm.assets" :key="index" class="asset-item">
                    <div class="asset-info">
                      <i class="material-icons">{{ getAssetIcon(asset.type) }}</i>
                      <span>{{ asset.name }}</span>
                    </div>
                    <button class="btn btn-icon" @click="removeAsset(index)" title="Remove Asset">
                      <i class="material-icons">delete</i>
                    </button>
                  </div>
                </div>
                <div v-else class="empty-assets">
                  No assets added yet
                </div>
              </div>
              <button class="btn btn-secondary" @click="showAssetUploadModal = true">
                <i class="material-icons">add</i> Add Asset
              </button>
            </div>
          </div>

          <div class="form-section">
            <div class="section-header">
              <h3>Pricing & Inventory</h3>
            </div>

            <div class="form-row">
              <div class="form-group half">
                <label for="product-sku">SKU</label>
                <input
                  id="product-sku"
                  type="text"
                  v-model="productForm.sku"
                  placeholder="Enter SKU"
                  :disabled="productForm.hasVariants"
                />
              </div>
              <div class="form-group half">
                <label for="product-price">Base Price</label>
                <div class="input-with-prefix">
                  <span class="prefix">$</span>
                  <input
                    id="product-price"
                    type="number"
                    v-model="productForm.basePrice"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    :disabled="productForm.hasVariants"
                  />
                </div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group half">
                <label for="product-stock">Stock</label>
                <input
                  id="product-stock"
                  type="number"
                  v-model="productForm.stock"
                  placeholder="Enter stock quantity"
                  min="0"
                  step="1"
                  :disabled="productForm.hasVariants"
                />
              </div>
              <div class="form-group half variant-toggle">
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    v-model="productForm.hasVariants"
                  />
                  <span>This product has multiple variants</span>
                </label>
              </div>
            </div>
          </div>

          <div v-if="productForm.hasVariants" class="form-section">
            <ProductVariantManager
              v-model="productForm.variants"
            />
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeModals">Cancel</button>
          <button
            class="btn btn-primary"
            @click="saveProduct"
            :disabled="!productForm.name"
          >
            {{ showEditProductModal ? 'Update Product' : 'Add Product' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Confirm Delete</h3>
          <button class="btn btn-icon" @click="showDeleteModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to delete the product "{{ productToDelete?.name }}"?</p>
          <p class="warning">This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showDeleteModal = false">Cancel</button>
          <button class="btn btn-danger" @click="deleteProduct">Delete Product</button>
        </div>
      </div>
    </div>

    <!-- Asset Upload Modal -->
    <div v-if="showAssetUploadModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Add Product Asset</h3>
          <button class="btn btn-icon" @click="showAssetUploadModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="asset-name">Asset Name</label>
            <input
              id="asset-name"
              type="text"
              v-model="assetForm.name"
              placeholder="Enter asset name"
              required
            />
          </div>

          <div class="form-group">
            <label for="asset-type">Asset Type</label>
            <select id="asset-type" v-model="assetForm.type">
              <option value="image">Image</option>
              <option value="model">3D Model</option>
              <option value="video">Video</option>
              <option value="document">Document</option>
            </select>
          </div>

          <div class="form-group">
            <label for="asset-file">File</label>
            <div class="file-upload">
              <input
                id="asset-file"
                type="file"
                @change="handleAssetFileUpload"
                :accept="getAssetTypeAccept(assetForm.type)"
              />
              <div class="file-preview asset-preview">
                <div v-if="assetForm.file" class="asset-file-info">
                  <i class="material-icons">{{ getAssetIcon(assetForm.type) }}</i>
                  <span>{{ assetForm.file.name }}</span>
                  <span class="file-size">{{ formatFileSize(assetForm.file.size) }}</span>
                </div>
                <div v-else class="placeholder-image">
                  <i class="material-icons">upload_file</i>
                  <span>No file selected</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showAssetUploadModal = false">Cancel</button>
          <button
            class="btn btn-primary"
            @click="addAsset"
            :disabled="!assetForm.name || !assetForm.file"
          >
            Add Asset
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ProductVariantManager from './ProductVariantManager.vue';

export default {
  name: 'ProductManager',

  components: {
    ProductVariantManager
  },

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      products: [],
      filteredProducts: [],
      loading: true,
      searchQuery: '',
      statusFilter: 'all',
      categoryFilter: 'all',
      dateRangeFilter: 'all',
      customDateRange: {
        start: null,
        end: null
      },
      today: new Date().toISOString().split('T')[0],
      sortBy: 'date_updated',
      sortOrder: 'desc',
      viewMode: 'grid',
      selectedTags: [],
      showAddProductModal: false,
      showEditProductModal: false,
      showDeleteModal: false,
      productForm: {
        name: '',
        description: '',
        status: 'draft',
        category: null,
        tags: [],
        thumbnail: null,
        variants: [],
        hasVariants: false,
        basePrice: '',
        stock: '',
        sku: ''
      },
      thumbnailPreview: null,
      productToDelete: null,
      showAssetUploadModal: false,
      assetForm: {
        name: '',
        type: 'image',
        file: null
      },
      categories: [],
      availableTags: [
        { id: 'modern', name: 'Modern' },
        { id: 'classic', name: 'Classic' },
        { id: 'minimalist', name: 'Minimalist' },
        { id: 'industrial', name: 'Industrial' },
        { id: 'luxury', name: 'Luxury' },
        { id: 'eco-friendly', name: 'Eco-Friendly' },
        { id: 'bestseller', name: 'Best Seller' },
        { id: 'new', name: 'New Arrival' },
        { id: 'sale', name: 'On Sale' }
      ]
    };
  },

  computed: {
    hasActiveFilters() {
      return (
        this.statusFilter !== 'all' ||
        this.categoryFilter !== 'all' ||
        this.dateRangeFilter !== 'all' ||
        this.selectedTags.length > 0
      );
    }
  },

  mounted() {
    this.loadCategories();
    this.loadProducts();
  },

  methods: {
    // Load categories from API
    async loadCategories() {
      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/categories?vendor_id=${this.vendorId}`);
        // this.categories = response.data;

        // Mock data for demonstration
        setTimeout(() => {
          this.categories = [
            { id: 'furniture', name: 'Furniture' },
            { id: 'lighting', name: 'Lighting' },
            { id: 'decor', name: 'Decor' },
            { id: 'kitchen', name: 'Kitchen' },
            { id: 'bathroom', name: 'Bathroom' },
            { id: 'outdoor', name: 'Outdoor' }
          ];
        }, 500);
      } catch (error) {
        console.error('Error loading categories:', error);
      }
    },

    // Load products from API
    async loadProducts() {
      this.loading = true;

      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/products?vendor_id=${this.vendorId}`);
        // this.products = response.data;

        // Mock data for demonstration
        setTimeout(() => {
          this.products = [
            {
              id: '1234abcd-5678-efgh-ijkl-mnopqrstuvwx',
              name: 'Modern Sofa',
              description: 'A comfortable modern sofa with clean lines and durable fabric.',
              status: 'published',
              category: 'furniture',
              tags: ['modern', 'bestseller'],
              thumbnail: null,
              date_created: '2023-05-15T10:30:00Z',
              date_updated: '2023-05-20T14:45:00Z',
              views: 245,
              interactions: 78,
              hasVariants: true,
              variants: [
                {
                  name: 'Gray Fabric',
                  attributes: [
                    { type: 'color', value: 'Gray' },
                    { type: 'material', value: 'Fabric' }
                  ],
                  price: '899.99',
                  stock: '12',
                  sku: 'SOFA-GRY-FAB'
                },
                {
                  name: 'Blue Velvet',
                  attributes: [
                    { type: 'color', value: 'Blue' },
                    { type: 'material', value: 'Velvet' }
                  ],
                  price: '999.99',
                  stock: '8',
                  sku: 'SOFA-BLU-VEL'
                }
              ]
            },
            {
              id: '2345bcde-6789-fghi-jklm-nopqrstuvwxy',
              name: 'Dining Table',
              description: 'Solid wood dining table that seats 6 people comfortably.',
              status: 'published',
              category: 'furniture',
              tags: ['classic', 'luxury'],
              thumbnail: null,
              date_created: '2023-04-10T09:15:00Z',
              date_updated: '2023-05-18T11:20:00Z',
              views: 189,
              interactions: 42
            },
            {
              id: '3456cdef-7890-ghij-klmn-opqrstuvwxyz',
              name: 'Lounge Chair',
              description: 'Ergonomic lounge chair with ottoman for maximum comfort.',
              status: 'draft',
              category: 'furniture',
              tags: ['modern', 'minimalist'],
              thumbnail: null,
              date_created: '2023-05-05T16:45:00Z',
              date_updated: '2023-05-05T16:45:00Z',
              views: 56,
              interactions: 12
            },
            {
              id: '4567defg-8901-hijk-lmno-pqrstuvwxyza',
              name: 'Pendant Light',
              description: 'Modern pendant light with adjustable height and warm lighting.',
              status: 'published',
              category: 'lighting',
              tags: ['modern', 'new'],
              thumbnail: null,
              date_created: '2023-05-12T13:20:00Z',
              date_updated: '2023-05-19T09:30:00Z',
              views: 178,
              interactions: 35
            },
            {
              id: '5678efgh-9012-ijkl-mnop-qrstuvwxyzab',
              name: 'Wall Mirror',
              description: 'Decorative wall mirror with gold frame, perfect for entryways.',
              status: 'published',
              category: 'decor',
              tags: ['luxury', 'bestseller'],
              thumbnail: null,
              date_created: '2023-04-25T11:10:00Z',
              date_updated: '2023-05-17T15:45:00Z',
              views: 312,
              interactions: 94,
              hasVariants: true,
              variants: [
                {
                  name: 'Round Gold',
                  attributes: [
                    { type: 'style', value: 'Round' },
                    { type: 'finish', value: 'Gold' }
                  ],
                  price: '249.99',
                  stock: '15',
                  sku: 'MIR-RND-GLD'
                },
                {
                  name: 'Square Silver',
                  attributes: [
                    { type: 'style', value: 'Square' },
                    { type: 'finish', value: 'Silver' }
                  ],
                  price: '229.99',
                  stock: '10',
                  sku: 'MIR-SQR-SLV'
                },
                {
                  name: 'Oval Bronze',
                  attributes: [
                    { type: 'style', value: 'Oval' },
                    { type: 'finish', value: 'Bronze' }
                  ],
                  price: '279.99',
                  stock: '7',
                  sku: 'MIR-OVL-BRZ'
                }
              ]
            },
            {
              id: '6789fghi-0123-jklm-nopq-rstuvwxyzabc',
              name: 'Kitchen Island',
              description: 'Spacious kitchen island with marble top and storage drawers.',
              status: 'draft',
              category: 'kitchen',
              tags: ['luxury', 'new'],
              thumbnail: null,
              date_created: '2023-05-08T10:15:00Z',
              date_updated: '2023-05-08T10:15:00Z',
              views: 87,
              interactions: 23
            }
          ];

          this.filterProducts();
          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading products:', error);
        this.loading = false;
      }
    },

    // Filter products based on search query and filters
    filterProducts() {
      let filtered = [...this.products];

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(product =>
          product.name.toLowerCase().includes(query) ||
          (product.description && product.description.toLowerCase().includes(query))
        );
      }

      // Apply status filter
      if (this.statusFilter !== 'all') {
        filtered = filtered.filter(product => product.status === this.statusFilter);
      }

      // Apply category filter
      if (this.categoryFilter !== 'all') {
        filtered = filtered.filter(product => product.category === this.categoryFilter);
      }

      // Apply date range filter
      if (this.dateRangeFilter !== 'all') {
        const dateFilters = this.getDateRangeFilterDates();
        if (dateFilters.start && dateFilters.end) {
          filtered = filtered.filter(product => {
            const productDate = new Date(product.date_updated);
            return productDate >= dateFilters.start && productDate <= dateFilters.end;
          });
        }
      }

      // Apply tag filters
      if (this.selectedTags.length > 0) {
        filtered = filtered.filter(product =>
          product.tags && this.selectedTags.every(tagId => product.tags.includes(tagId))
        );
      }

      // Apply sorting
      filtered.sort((a, b) => {
        let valueA, valueB;

        // Special handling for category sorting
        if (this.sortBy === 'category') {
          valueA = this.getCategoryName(a.category || '');
          valueB = this.getCategoryName(b.category || '');
        } else {
          valueA = a[this.sortBy] || 0;
          valueB = b[this.sortBy] || 0;
        }

        // Handle dates
        if (this.sortBy.includes('date')) {
          valueA = new Date(valueA);
          valueB = new Date(valueB);
        }

        if (this.sortOrder === 'asc') {
          return valueA > valueB ? 1 : -1;
        } else {
          return valueA < valueB ? 1 : -1;
        }
      });

      this.filteredProducts = filtered;
    },

    // Get category name by ID
    getCategoryName(categoryId) {
      const category = this.categories.find(c => c.id === categoryId);
      return category ? category.name : 'Uncategorized';
    },

    // Get tag name by ID
    getTagName(tagId) {
      const tag = this.availableTags.find(t => t.id === tagId);
      return tag ? tag.name : '';
    },

    // Toggle tag selection
    toggleTag(tagId) {
      const index = this.selectedTags.indexOf(tagId);
      if (index === -1) {
        this.selectedTags.push(tagId);
      } else {
        this.selectedTags.splice(index, 1);
      }
      this.filterProducts();
    },

    // Remove a specific tag
    removeTag(tagId) {
      const index = this.selectedTags.indexOf(tagId);
      if (index !== -1) {
        this.selectedTags.splice(index, 1);
        this.filterProducts();
      }
    },

    // Handle date range change
    handleDateRangeChange() {
      if (this.dateRangeFilter === 'custom') {
        // Initialize custom date range if not set
        if (!this.customDateRange.start) {
          const today = new Date();
          const lastMonth = new Date();
          lastMonth.setMonth(lastMonth.getMonth() - 1);

          this.customDateRange.start = lastMonth.toISOString().split('T')[0];
          this.customDateRange.end = today.toISOString().split('T')[0];
        }
      }

      this.filterProducts();
    },

    // Get date range filter dates
    getDateRangeFilterDates() {
      const result = {
        start: null,
        end: null
      };

      const today = new Date();
      today.setHours(23, 59, 59, 999);

      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0);

      const yesterdayEnd = new Date();
      yesterdayEnd.setDate(yesterdayEnd.getDate() - 1);
      yesterdayEnd.setHours(23, 59, 59, 999);

      switch (this.dateRangeFilter) {
        case 'today':
          result.start = new Date();
          result.start.setHours(0, 0, 0, 0);
          result.end = today;
          break;

        case 'yesterday':
          result.start = yesterday;
          result.end = yesterdayEnd;
          break;

        case 'last7days':
          result.start = new Date();
          result.start.setDate(result.start.getDate() - 7);
          result.start.setHours(0, 0, 0, 0);
          result.end = today;
          break;

        case 'last30days':
          result.start = new Date();
          result.start.setDate(result.start.getDate() - 30);
          result.start.setHours(0, 0, 0, 0);
          result.end = today;
          break;

        case 'thisMonth':
          result.start = new Date(today.getFullYear(), today.getMonth(), 1);
          result.start.setHours(0, 0, 0, 0);
          result.end = today;
          break;

        case 'lastMonth':
          result.start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
          result.start.setHours(0, 0, 0, 0);
          result.end = new Date(today.getFullYear(), today.getMonth(), 0);
          result.end.setHours(23, 59, 59, 999);
          break;

        case 'custom':
          if (this.customDateRange.start) {
            result.start = new Date(this.customDateRange.start);
            result.start.setHours(0, 0, 0, 0);
          }
          if (this.customDateRange.end) {
            result.end = new Date(this.customDateRange.end);
            result.end.setHours(23, 59, 59, 999);
          }
          break;
      }

      return result;
    },

    // Get date range label for display
    getDateRangeLabel() {
      switch (this.dateRangeFilter) {
        case 'today':
          return 'Today';
        case 'yesterday':
          return 'Yesterday';
        case 'last7days':
          return 'Last 7 Days';
        case 'last30days':
          return 'Last 30 Days';
        case 'thisMonth':
          return 'This Month';
        case 'lastMonth':
          return 'Last Month';
        case 'custom':
          if (this.customDateRange.start && this.customDateRange.end) {
            return `${this.formatDate(this.customDateRange.start)} to ${this.formatDate(this.customDateRange.end)}`;
          }
          return 'Custom Range';
        default:
          return 'All Time';
      }
    },

    // Clear a specific filter
    clearFilter(filterType) {
      switch (filterType) {
        case 'status':
          this.statusFilter = 'all';
          break;
        case 'category':
          this.categoryFilter = 'all';
          break;
        case 'dateRange':
          this.dateRangeFilter = 'all';
          this.customDateRange = {
            start: null,
            end: null
          };
          break;
      }

      this.filterProducts();
    },

    // Clear all filters
    clearAllFilters() {
      this.statusFilter = 'all';
      this.categoryFilter = 'all';
      this.dateRangeFilter = 'all';
      this.customDateRange = {
        start: null,
        end: null
      };
      this.selectedTags = [];
      this.searchQuery = '';

      this.filterProducts();
    },

    // Duplicate a product
    duplicateProduct(product) {
      const newProduct = {
        ...JSON.parse(JSON.stringify(product)),
        id: `new-${Date.now()}`,
        name: `${product.name} (Copy)`,
        status: 'draft',
        date_created: new Date().toISOString(),
        date_updated: new Date().toISOString(),
        views: 0,
        interactions: 0
      };

      this.products.push(newProduct);
      this.filterProducts();
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    // Handle file upload for thumbnail
    handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Preview the image
      this.thumbnailPreview = URL.createObjectURL(file);
      this.productForm.thumbnail = file;
    },

    // Edit product
    editProduct(product) {
      this.productForm = {
        id: product.id,
        name: product.name,
        description: product.description || '',
        status: product.status,
        category: product.category || '',
        tags: product.tags || [],
        thumbnail: null,
        assets: product.assets || [],
        variants: product.variants || [],
        hasVariants: product.hasVariants || false,
        basePrice: product.basePrice || '',
        stock: product.stock || '',
        sku: product.sku || ''
      };

      this.thumbnailPreview = product.thumbnail;
      this.showEditProductModal = true;
    },

    // Open add product modal
    openAddProductModal() {
      this.productForm = {
        name: '',
        description: '',
        status: 'draft',
        category: '',
        tags: [],
        thumbnail: null,
        assets: [],
        variants: [],
        hasVariants: false,
        basePrice: '',
        stock: '',
        sku: ''
      };
      this.thumbnailPreview = null;
      this.showAddProductModal = true;
    },

    // Toggle product tag
    toggleProductTag(tagId) {
      const index = this.productForm.tags.indexOf(tagId);
      if (index === -1) {
        this.productForm.tags.push(tagId);
      } else {
        this.productForm.tags.splice(index, 1);
      }
    },

    // Handle asset file upload
    handleAssetFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      this.assetForm.file = file;
    },

    // Add asset to product
    addAsset() {
      if (!this.assetForm.name || !this.assetForm.file) return;

      // In a real implementation, you would upload the file to a server
      // and get back a URL or file ID
      const newAsset = {
        id: `asset-${Date.now()}`,
        name: this.assetForm.name,
        type: this.assetForm.type,
        file: this.assetForm.file,
        url: URL.createObjectURL(this.assetForm.file),
        size: this.assetForm.file.size
      };

      if (!this.productForm.assets) {
        this.productForm.assets = [];
      }

      this.productForm.assets.push(newAsset);

      // Reset form and close modal
      this.assetForm = {
        name: '',
        type: 'image',
        file: null
      };
      this.showAssetUploadModal = false;
    },

    // Remove asset from product
    removeAsset(index) {
      this.productForm.assets.splice(index, 1);
    },

    // Get asset icon based on type
    getAssetIcon(type) {
      switch (type) {
        case 'image':
          return 'image';
        case 'model':
          return 'view_in_ar';
        case 'video':
          return 'videocam';
        case 'document':
          return 'description';
        default:
          return 'insert_drive_file';
      }
    },

    // Get accept attribute for file input based on asset type
    getAssetTypeAccept(type) {
      switch (type) {
        case 'image':
          return 'image/*';
        case 'model':
          return '.glb,.gltf,.obj,.fbx,.stl';
        case 'video':
          return 'video/*';
        case 'document':
          return '.pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx';
        default:
          return '*/*';
      }
    },

    // Format file size for display
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';

      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));

      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // View product displays
    viewDisplays(product) {
      // This would navigate to product displays view
      console.log('View displays for product:', product.id);
    },

    // Confirm delete product
    confirmDeleteProduct(product) {
      this.productToDelete = product;
      this.showDeleteModal = true;
    },

    // Delete product
    async deleteProduct() {
      if (!this.productToDelete) return;

      try {
        // This would be replaced with actual API call
        // await axios.delete(`/api/products/${this.productToDelete.id}`);

        // Mock deletion
        this.products = this.products.filter(p => p.id !== this.productToDelete.id);
        this.filterProducts();

        this.showDeleteModal = false;
        this.productToDelete = null;
      } catch (error) {
        console.error('Error deleting product:', error);
      }
    },

    // Save product (create or update)
    async saveProduct() {
      try {
        if (this.showEditProductModal) {
          // Update existing product
          // This would be replaced with actual API call
          // await axios.patch(`/api/products/${this.productForm.id}`, this.productForm);

          // Mock update
          const index = this.products.findIndex(p => p.id === this.productForm.id);
          if (index !== -1) {
            this.products[index] = {
              ...this.products[index],
              ...this.productForm,
              date_updated: new Date().toISOString()
            };
          }
        } else {
          // Create new product
          // This would be replaced with actual API call
          // const response = await axios.post('/api/products', {
          //   ...this.productForm,
          //   vendor_id: this.vendorId
          // });

          // Mock creation
          const newProduct = {
            id: `new-${Date.now()}`,
            ...this.productForm,
            vendor_id: this.vendorId,
            date_created: new Date().toISOString(),
            date_updated: new Date().toISOString()
          };

          this.products.push(newProduct);
        }

        this.filterProducts();
        this.closeModals();
      } catch (error) {
        console.error('Error saving product:', error);
      }
    },

    // Close all modals
    closeModals() {
      this.showAddProductModal = false;
      this.showEditProductModal = false;
      this.showDeleteModal = false;
      this.showAssetUploadModal = false;
      this.productForm = {
        name: '',
        description: '',
        status: 'draft',
        category: '',
        tags: [],
        thumbnail: null,
        assets: [],
        variants: [],
        hasVariants: false,
        basePrice: '',
        stock: '',
        sku: ''
      };
      this.thumbnailPreview = null;
      this.productToDelete = null;
      this.assetForm = {
        name: '',
        type: 'image',
        file: null
      };
    }
  }
};
</script>

<style scoped>
.product-manager {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.search {
  position: relative;
}

.search input {
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  width: 200px;
}

.search i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
  font-size: 18px;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.filter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.filter select {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 16px;
}

.empty-state h3 {
  margin-bottom: 8px;
  color: var(--theme--foreground);
}

.empty-state p {
  color: var(--theme--foreground-subdued);
  max-width: 400px;
  margin: 0 auto;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.product-card {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background);
  transition: all 0.2s ease;
}

.product-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.product-card--draft {
  border-left: 4px solid var(--theme--warning);
}

.product-card--archived {
  border-left: 4px solid var(--theme--danger);
  opacity: 0.7;
}

.product-image {
  height: 180px;
  background-color: var(--theme--background-subdued);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.placeholder-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.placeholder-image i {
  font-size: 48px;
  margin-bottom: 8px;
}

.product-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.product-status.published {
  background-color: var(--theme--primary);
  color: white;
}

.product-status.draft {
  background-color: var(--theme--warning);
  color: white;
}

.product-status.archived {
  background-color: var(--theme--danger);
  color: white;
}

.product-info {
  padding: 16px;
}

.product-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.product-description {
  color: var(--theme--foreground-subdued);
  font-size: 14px;
  margin: 0 0 12px 0;
  line-height: 1.4;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.product-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
  gap: 8px;
}

.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--border-color);
}

.btn-danger {
  background-color: var(--theme--danger);
  color: white;
}

.btn-danger:hover {
  background-color: var(--theme--danger-accent);
}

.btn-icon {
  padding: 6px;
  border-radius: var(--theme--border-radius);
  background-color: transparent;
}

.btn-icon i {
  margin-right: 0;
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--theme--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.file-upload {
  border: 2px dashed var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  text-align: center;
}

.file-preview {
  margin-top: 16px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.file-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.warning {
  color: var(--theme--danger);
  font-weight: 500;
}

/* New styles for advanced filtering */
.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 15px;
}

.date-range-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.custom-date-range {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.custom-date-range input {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.view-toggle {
  display: flex;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.view-toggle-btn {
  padding: 6px 10px;
  background: var(--theme--background-subdued);
  border: none;
  cursor: pointer;
}

.view-toggle-btn.active {
  background: var(--theme--primary);
  color: white;
}

.filter-tags {
  flex-grow: 1;
}

.tag-selector {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px;
}

.tag {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag:hover {
  background-color: var(--theme--border-color);
}

.tag.active {
  background-color: var(--theme--primary);
  color: white;
}

.modal-tags {
  margin-top: 0;
}

.active-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  margin-bottom: 20px;
}

.active-filters-label {
  font-weight: 500;
  margin-right: 5px;
}

.active-filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  flex-grow: 1;
}

.active-filter {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
  font-size: 12px;
}

.clear-filter {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  line-height: 1;
  padding: 0;
  color: var(--theme--primary);
}

.clear-all-filters {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--danger);
  font-size: 12px;
  padding: 4px 8px;
}

/* Product list view styles */
.product-list {
  width: 100%;
  overflow-x: auto;
}

.product-table {
  width: 100%;
  border-collapse: collapse;
}

.product-table th {
  text-align: left;
  padding: 12px 15px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
  font-weight: 600;
  color: var(--theme--foreground-subdued);
}

.product-table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--theme--border-color);
  vertical-align: middle;
}

.thumbnail-col {
  width: 80px;
}

.name-col {
  min-width: 200px;
}

.category-col {
  width: 120px;
}

.status-col {
  width: 100px;
}

.date-col {
  width: 120px;
}

.metrics-col {
  width: 120px;
}

.actions-col {
  width: 120px;
}

.list-thumbnail {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.list-thumbnail img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.placeholder-thumbnail {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.placeholder-thumbnail i {
  font-size: 24px;
}

.product-name-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.product-name {
  font-weight: 500;
}

.product-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
}

.product-tag {
  padding: 4px 8px;
  border-radius: 4px;
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
  font-size: 12px;
}

.product-tag.small {
  padding: 2px 6px;
  font-size: 10px;
}

.product-tag.more {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
}

.product-category {
  margin-top: 8px;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.category-label {
  margin-right: 5px;
}

.category-name {
  font-weight: 500;
  color: var(--theme--foreground);
}

.product-variants {
  margin-top: 8px;
}

.variant-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  color: var(--theme--primary);
  font-size: 12px;
}

.variant-badge i {
  font-size: 14px;
}

.variants-col {
  width: 150px;
}

.variants-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.variant-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--theme--primary);
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.variant-preview {
  display: flex;
  flex-direction: column;
  gap: 3px;
  font-size: 12px;
}

.variant-preview-item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.variant-preview-more {
  font-size: 11px;
  color: var(--theme--foreground-subdued);
}

.no-variants {
  color: var(--theme--foreground-subdued);
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
  text-align: center;
}

.status-badge.published {
  background-color: var(--theme--primary);
  color: white;
}

.status-badge.draft {
  background-color: var(--theme--warning);
  color: white;
}

.status-badge.archived {
  background-color: var(--theme--danger);
  color: white;
}

.metrics-container {
  display: flex;
  gap: 10px;
}

.metric {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.metric i {
  font-size: 16px;
  color: var(--theme--foreground-subdued);
}

.list-actions {
  display: flex;
  gap: 5px;
}

.row-draft {
  background-color: rgba(var(--theme--warning-rgb), 0.05);
}

.row-archived {
  background-color: rgba(var(--theme--danger-rgb), 0.05);
  opacity: 0.7;
}

/* Asset management styles */
.asset-manager {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  background-color: var(--theme--background-subdued);
}

.asset-list {
  margin-bottom: 15px;
  max-height: 200px;
  overflow-y: auto;
}

.asset-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  margin-bottom: 8px;
}

.asset-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.asset-info i {
  color: var(--theme--primary);
}

.empty-assets {
  text-align: center;
  padding: 20px;
  color: var(--theme--foreground-subdued);
}

.asset-preview {
  height: 100px;
}

.asset-file-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.asset-file-info i {
  font-size: 32px;
  color: var(--theme--primary);
}

.file-size {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

/* Form layout styles */
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.form-group.half {
  flex: 1;
}

.form-section {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  margin-bottom: 20px;
  background-color: var(--theme--background-subdued);
}

.section-header {
  margin-bottom: 15px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.input-with-prefix {
  position: relative;
}

.input-with-prefix .prefix {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
}

.input-with-prefix input {
  padding-left: 25px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin-top: 30px;
}

.checkbox-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.variant-toggle {
  display: flex;
  align-items: flex-end;
}
</style>
