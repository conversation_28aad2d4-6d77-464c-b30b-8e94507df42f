# Fix 521 Error - Server Connection Issues
# This script diagnoses and fixes the 521 error (server down/unresponsive)

param(
    [string]$ServerIP = "**************",
    [string]$Username = "root",  # Try root first
    [string]$SSHKeyPath = "C:\Users\<USER>\.ssh\id_rsa",
    [string]$Domain = "mvs.kanousai.com"
)

Write-Host "🔧 Fixing 521 Error - Server Connection Issues" -ForegroundColor Red
Write-Host "=============================================" -ForegroundColor Red
Write-Host ""

# Test 1: Check if server is responding to ping
Write-Host "1. Testing server connectivity..." -ForegroundColor Yellow
try {
    $pingResult = Test-Connection -ComputerName $ServerIP -Count 4 -ErrorAction Stop
    Write-Host "   ✅ Server is responding to ping" -ForegroundColor Green
    Write-Host "   Average response time: $($pingResult | Measure-Object ResponseTime -Average | Select-Object -ExpandProperty Average)ms" -ForegroundColor Gray
} catch {
    Write-Host "   ❌ Server is not responding to ping - Server may be down!" -ForegroundColor Red
    Write-Host "   Contact DigitalOcean support or check server status" -ForegroundColor Yellow
    exit 1
}

# Test 2: Check if port 80 is open
Write-Host ""
Write-Host "2. Testing port 80 connectivity..." -ForegroundColor Yellow
try {
    $tcpTest = Test-NetConnection -ComputerName $ServerIP -Port 80 -ErrorAction Stop
    if ($tcpTest.TcpTestSucceeded) {
        Write-Host "   ✅ Port 80 is open and responding" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Port 80 is not responding - This is the main issue!" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ Cannot test port 80 - Connection blocked" -ForegroundColor Red
}

# Test 3: Check if port 443 is open
Write-Host ""
Write-Host "3. Testing port 443 connectivity..." -ForegroundColor Yellow
try {
    $tcpTest443 = Test-NetConnection -ComputerName $ServerIP -Port 443 -ErrorAction Stop
    if ($tcpTest443.TcpTestSucceeded) {
        Write-Host "   ✅ Port 443 is open and responding" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  Port 443 is not responding (SSL not configured yet)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ⚠️  Cannot test port 443 - SSL not configured" -ForegroundColor Yellow
}

# Test 4: Try SSH connection
Write-Host ""
Write-Host "4. Testing SSH connectivity..." -ForegroundColor Yellow

$sshWorking = $false
$workingUser = ""

# Try different users
$users = @("root", "vectorax", "ubuntu")
foreach ($user in $users) {
    try {
        Write-Host "   Trying SSH with user: $user" -ForegroundColor Gray
        $sshTest = ssh -i $SSHKeyPath -o ConnectTimeout=10 -o StrictHostKeyChecking=no $user@$ServerIP "echo 'SSH_OK'" 2>&1
        if ($sshTest -match "SSH_OK") {
            Write-Host "   ✅ SSH working with user: $user" -ForegroundColor Green
            $sshWorking = $true
            $workingUser = $user
            break
        }
    } catch {
        Write-Host "   ❌ SSH failed with user: $user" -ForegroundColor Red
    }
}

if (-not $sshWorking) {
    Write-Host "   ❌ SSH access failed with all users" -ForegroundColor Red
    Write-Host ""
    Write-Host "🚨 CRITICAL: Cannot access server via SSH" -ForegroundColor Red
    Write-Host "Solutions:" -ForegroundColor Yellow
    Write-Host "1. Use DigitalOcean Console (Web interface)" -ForegroundColor White
    Write-Host "2. Reset root password in DigitalOcean dashboard" -ForegroundColor White
    Write-Host "3. Check if server is powered on" -ForegroundColor White
    exit 1
}

# Test 5: Check Docker status on server
Write-Host ""
Write-Host "5. Checking Docker status on server..." -ForegroundColor Yellow
$dockerStatus = ssh -i $SSHKeyPath $workingUser@$ServerIP "systemctl is-active docker" 2>&1
if ($dockerStatus -match "active") {
    Write-Host "   ✅ Docker service is running" -ForegroundColor Green
} else {
    Write-Host "   ❌ Docker service is not running - Starting Docker..." -ForegroundColor Red
    ssh -i $SSHKeyPath $workingUser@$ServerIP "systemctl start docker && systemctl enable docker"
    Start-Sleep 5
}

# Test 6: Check if containers are running
Write-Host ""
Write-Host "6. Checking Docker containers..." -ForegroundColor Yellow
$containers = ssh -i $SSHKeyPath $workingUser@$ServerIP "docker ps --format 'table {{.Names}}\t{{.Status}}'" 2>&1
if ($containers -match "nginx") {
    Write-Host "   ✅ Nginx container is running" -ForegroundColor Green
    Write-Host "   Container Status:" -ForegroundColor Gray
    $containers -split "`n" | ForEach-Object { Write-Host "     $_" -ForegroundColor Gray }
} else {
    Write-Host "   ❌ Nginx container is not running - This is the problem!" -ForegroundColor Red
    Write-Host "   Starting containers..." -ForegroundColor Yellow
    
    # Navigate to project directory and start containers
    $startResult = ssh -i $SSHKeyPath $workingUser@$ServerIP @"
cd /home/<USER>/mvs-vr-deployment 2>/dev/null || cd /root/mvs-vr-deployment 2>/dev/null || cd /home/<USER>/mvs-vr-deployment 2>/dev/null
if [ -f docker-compose.exported.yml ]; then
    echo "Found docker-compose file, starting containers..."
    docker-compose -f docker-compose.exported.yml down
    docker-compose -f docker-compose.exported.yml up -d
    echo "Containers started"
else
    echo "ERROR: docker-compose.exported.yml not found"
    echo "Current directory: \$(pwd)"
    echo "Files available:"
    ls -la
fi
"@
    
    Write-Host "   Start result:" -ForegroundColor Gray
    $startResult -split "`n" | ForEach-Object { Write-Host "     $_" -ForegroundColor Gray }
}

# Test 7: Check firewall status
Write-Host ""
Write-Host "7. Checking firewall configuration..." -ForegroundColor Yellow
$firewallStatus = ssh -i $SSHKeyPath $workingUser@$ServerIP "ufw status | grep -E '80|443'" 2>&1
if ($firewallStatus -match "80") {
    Write-Host "   ✅ Firewall allows port 80" -ForegroundColor Green
} else {
    Write-Host "   ❌ Firewall may be blocking port 80 - Fixing..." -ForegroundColor Red
    ssh -i $SSHKeyPath $workingUser@$ServerIP @"
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp
ufw --force enable
"@
    Write-Host "   ✅ Firewall configured to allow HTTP/HTTPS" -ForegroundColor Green
}

# Test 8: Test local connectivity on server
Write-Host ""
Write-Host "8. Testing local HTTP connectivity on server..." -ForegroundColor Yellow
$localTest = ssh -i $SSHKeyPath $workingUser@$ServerIP "curl -s -o /dev/null -w '%{http_code}' http://localhost/ 2>/dev/null || echo 'FAILED'" 2>&1
if ($localTest -eq "200") {
    Write-Host "   ✅ Server responds locally on port 80" -ForegroundColor Green
} else {
    Write-Host "   ❌ Server not responding locally (HTTP code: $localTest)" -ForegroundColor Red
    Write-Host "   This indicates nginx is not running or configured correctly" -ForegroundColor Yellow
}

# Test 9: Check nginx logs
Write-Host ""
Write-Host "9. Checking nginx logs for errors..." -ForegroundColor Yellow
$nginxLogs = ssh -i $SSHKeyPath $workingUser@$ServerIP "docker logs \$(docker ps -q --filter name=nginx) 2>&1 | tail -10" 2>&1
if ($nginxLogs) {
    Write-Host "   Nginx logs (last 10 lines):" -ForegroundColor Gray
    $nginxLogs -split "`n" | ForEach-Object { Write-Host "     $_" -ForegroundColor Gray }
} else {
    Write-Host "   ❌ Cannot retrieve nginx logs - container may not be running" -ForegroundColor Red
}

# Test 10: Final external connectivity test
Write-Host ""
Write-Host "10. Final external connectivity test..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://$ServerIP/" -TimeoutSec 10 -ErrorAction Stop
    Write-Host "   ✅ SUCCESS: Server is now responding externally!" -ForegroundColor Green
    Write-Host "   HTTP Status: $($response.StatusCode)" -ForegroundColor Green
} catch {
    Write-Host "   ❌ Server still not responding externally" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Gray
}

# Summary and recommendations
Write-Host ""
Write-Host "🔍 DIAGNOSIS SUMMARY:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

if ($sshWorking) {
    Write-Host "✅ SSH Access: Working with user $workingUser" -ForegroundColor Green
} else {
    Write-Host "❌ SSH Access: Failed - Use DigitalOcean Console" -ForegroundColor Red
}

if ($localTest -eq "200") {
    Write-Host "✅ Local HTTP: Server responding on port 80" -ForegroundColor Green
} else {
    Write-Host "❌ Local HTTP: Server not responding - Container issue" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 RECOMMENDED ACTIONS:" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

if ($localTest -ne "200") {
    Write-Host "1. URGENT: Fix container deployment" -ForegroundColor Red
    Write-Host "   - Upload docker-compose.exported.yml to server" -ForegroundColor White
    Write-Host "   - Run: docker-compose -f docker-compose.exported.yml up -d" -ForegroundColor White
    Write-Host ""
}

Write-Host "2. Verify Cloudflare settings:" -ForegroundColor Yellow
Write-Host "   - Ensure DNS points to $ServerIP" -ForegroundColor White
Write-Host "   - Check Cloudflare proxy status" -ForegroundColor White
Write-Host "   - Verify SSL/TLS settings" -ForegroundColor White
Write-Host ""

Write-Host "3. Monitor server status:" -ForegroundColor Yellow
Write-Host "   - Check DigitalOcean dashboard" -ForegroundColor White
Write-Host "   - Monitor container logs" -ForegroundColor White
Write-Host "   - Test regularly with: curl http://$ServerIP/" -ForegroundColor White

Write-Host ""
Write-Host "✅ Diagnosis completed!" -ForegroundColor Green
