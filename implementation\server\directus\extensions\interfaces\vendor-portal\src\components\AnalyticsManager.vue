<template>
  <div class="analytics-manager">
    <div class="header">
      <h2>Analytics Dashboard</h2>
      <div class="time-period-selector">
        <label for="time-period">Time Period:</label>
        <select id="time-period" v-model="timePeriod" @change="loadAnalyticsData">
          <option value="day">Today</option>
          <option value="week">Last 7 Days</option>
          <option value="month">Last 30 Days</option>
          <option value="quarter">Last 90 Days</option>
          <option value="year">Last 12 Months</option>
          <option value="custom">Custom Range</option>
        </select>

        <div v-if="timePeriod === 'custom'" class="custom-date-range">
          <div class="date-input">
            <label for="start-date">From:</label>
            <input
              id="start-date"
              type="date"
              v-model="startDate"
              :max="endDate || today"
            />
          </div>
          <div class="date-input">
            <label for="end-date">To:</label>
            <input
              id="end-date"
              type="date"
              v-model="endDate"
              :min="startDate"
              :max="today"
            />
          </div>
          <button class="btn btn-primary btn-sm" @click="loadAnalyticsData">
            Apply
          </button>
        </div>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading analytics data...</span>
    </div>

    <div v-else class="analytics-content">
      <!-- Overview Section -->
      <div class="overview-section">
        <div class="metrics-grid">
          <div class="metric-card" v-for="(metric, key) in overviewMetrics" :key="key">
            <div class="metric-icon" :class="metric.trend">
              <i class="material-icons">{{ metric.icon }}</i>
            </div>
            <div class="metric-content">
              <div class="metric-title">{{ metric.title }}</div>
              <div class="metric-value">{{ metric.value }}</div>
              <div class="metric-trend" :class="metric.trend">
                <i class="material-icons">{{ getTrendIcon(metric.trend) }}</i>
                <span>{{ metric.change }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Tabs -->
      <div class="analytics-tabs">
        <div class="tabs-header">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            class="tab-button"
            :class="{ active: activeTab === tab.id }"
            @click="activeTab = tab.id"
          >
            {{ tab.name }}
          </button>
        </div>

        <div class="tab-content">
          <!-- Showroom Analytics Tab -->
          <div v-if="activeTab === 'showroom'" class="tab-pane">
            <h3>Showroom Analytics</h3>
            <p>Detailed analytics about your virtual showrooms.</p>

            <!-- Showroom Performance Chart -->
            <div class="chart-container">
              <h4>Showroom Performance</h4>
              <div class="chart-wrapper">
                <canvas ref="showroomChart"></canvas>
              </div>
            </div>

            <!-- Showroom Comparison Table -->
            <div class="table-container">
              <h4>Showroom Comparison</h4>
              <table class="analytics-table">
                <thead>
                  <tr>
                    <th>Showroom</th>
                    <th>Visits</th>
                    <th>Avg. Time</th>
                    <th>Interactions</th>
                    <th>Conversion Rate</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(showroom, index) in showroomAnalytics" :key="index">
                    <td>{{ showroom.name }}</td>
                    <td>{{ showroom.visits }}</td>
                    <td>{{ showroom.avgTime }}</td>
                    <td>{{ showroom.interactions }}</td>
                    <td>{{ showroom.conversionRate }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Product Analytics Tab -->
          <div v-if="activeTab === 'product'" class="tab-pane">
            <h3>Product Analytics</h3>
            <p>Insights about your product performance and engagement.</p>

            <!-- Top Products Chart -->
            <div class="chart-container">
              <h4>Top Products by Views</h4>
              <div class="chart-wrapper">
                <canvas ref="productViewsChart"></canvas>
              </div>
            </div>

            <!-- Product Engagement Chart -->
            <div class="chart-container">
              <h4>Product Engagement</h4>
              <div class="chart-wrapper">
                <canvas ref="productEngagementChart"></canvas>
              </div>
            </div>
          </div>

          <!-- Client Analytics Tab -->
          <div v-if="activeTab === 'client'" class="tab-pane">
            <h3>Client Analytics</h3>
            <p>Data about client behavior and engagement.</p>

            <!-- Client Acquisition Chart -->
            <div class="chart-container">
              <h4>Client Acquisition</h4>
              <div class="chart-wrapper">
                <canvas ref="clientAcquisitionChart"></canvas>
              </div>
            </div>

            <!-- Client Engagement Table -->
            <div class="table-container">
              <h4>Top Engaged Clients</h4>
              <table class="analytics-table">
                <thead>
                  <tr>
                    <th>Client</th>
                    <th>Company</th>
                    <th>Visits</th>
                    <th>Last Visit</th>
                    <th>Engagement Score</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(client, index) in topClients" :key="index">
                    <td>{{ client.name }}</td>
                    <td>{{ client.company }}</td>
                    <td>{{ client.visits }}</td>
                    <td>{{ formatDate(client.lastVisit) }}</td>
                    <td>
                      <div class="engagement-score" :class="getEngagementClass(client.engagementScore)">
                        {{ client.engagementScore }}
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- Engagement Analytics Tab -->
          <div v-if="activeTab === 'engagement'" class="tab-pane">
            <h3>Engagement Analytics</h3>
            <p>Detailed metrics about user interactions and engagement.</p>

            <!-- Interaction Types Chart -->
            <div class="chart-container">
              <h4>Interaction Types</h4>
              <div class="chart-wrapper">
                <canvas ref="interactionTypesChart"></canvas>
              </div>
            </div>

            <!-- Engagement Over Time Chart -->
            <div class="chart-container">
              <h4>Engagement Over Time</h4>
              <div class="chart-wrapper">
                <canvas ref="engagementTimeChart"></canvas>
              </div>
            </div>
          </div>

          <!-- Real-time Data Tab -->
          <div v-if="activeTab === 'realtime'" class="tab-pane">
            <RealtimeVisualization :vendorId="vendorId" />
          </div>

          <!-- Interaction Heatmap Tab -->
          <div v-if="activeTab === 'heatmap'" class="tab-pane">
            <InteractionHeatmap :vendorId="vendorId" />
          </div>

          <!-- Export Data Tab -->
          <div v-if="activeTab === 'export'" class="tab-pane">
            <ExportManager :vendorId="vendorId" />
          </div>
        </div>
      </div>

      <!-- Export Section (Quick Access) -->
      <div class="export-section" v-if="activeTab !== 'export'">
        <h3>Export Data</h3>
        <div class="export-options">
          <button class="btn btn-secondary" @click="activeTab = 'export'">
            <i class="material-icons">file_download</i> Go to Export Manager
          </button>
          <button class="btn btn-secondary" @click="exportData('csv')">
            <i class="material-icons">file_download</i> Quick Export as CSV
          </button>
          <button class="btn btn-secondary" @click="exportData('pdf')">
            <i class="material-icons">picture_as_pdf</i> Quick Export as PDF
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { Chart, registerables } from 'chart.js';
import RealtimeVisualization from './RealtimeVisualization.vue';
import ExportManager from './ExportManager.vue';
import InteractionHeatmap from './InteractionHeatmap.vue';

// Register Chart.js components
Chart.register(...registerables);

export default {
  name: 'AnalyticsManager',

  components: {
    RealtimeVisualization,
    ExportManager,
    InteractionHeatmap
  },

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      timePeriod: 'month',
      startDate: null,
      endDate: null,
      today: new Date().toISOString().split('T')[0],
      activeTab: 'showroom',
      tabs: [
        { id: 'showroom', name: 'Showroom Analytics' },
        { id: 'product', name: 'Product Analytics' },
        { id: 'client', name: 'Client Analytics' },
        { id: 'engagement', name: 'Engagement Analytics' },
        { id: 'realtime', name: 'Real-time Data' },
        { id: 'heatmap', name: 'Interaction Heatmap' },
        { id: 'export', name: 'Export Data' }
      ],
      overviewMetrics: {},
      showroomAnalytics: [],
      productAnalytics: [],
      clientAnalytics: {},
      engagementAnalytics: {},
      topClients: [],
      charts: {
        showroom: null,
        productViews: null,
        productEngagement: null,
        clientAcquisition: null,
        interactionTypes: null,
        engagementTime: null
      }
    };
  },

  mounted() {
    this.loadAnalyticsData();
  },

  methods: {
    // Load analytics data based on selected time period
    async loadAnalyticsData() {
      this.loading = true;

      try {
        // This would be replaced with actual API calls
        // const params = this.getTimeParams();
        // const overviewResponse = await axios.get(`/api/analytics/overview?${params}&vendor_id=${this.vendorId}`);
        // const showroomResponse = await axios.get(`/api/analytics/showrooms?${params}&vendor_id=${this.vendorId}`);
        // const productResponse = await axios.get(`/api/analytics/products?${params}&vendor_id=${this.vendorId}`);
        // const clientResponse = await axios.get(`/api/analytics/clients?${params}&vendor_id=${this.vendorId}`);
        // const engagementResponse = await axios.get(`/api/analytics/engagement?${params}&vendor_id=${this.vendorId}`);

        // Mock data for demonstration
        setTimeout(() => {
          this.loadMockData();
          this.loading = false;

          // Initialize charts after data is loaded
          this.$nextTick(() => {
            this.initCharts();
          });
        }, 1000);
      } catch (error) {
        console.error('Error loading analytics data:', error);
        this.loading = false;
      }
    },

    // Get time parameters for API requests
    getTimeParams() {
      if (this.timePeriod === 'custom' && this.startDate && this.endDate) {
        return `start_date=${this.startDate}&end_date=${this.endDate}`;
      }

      return `period=${this.timePeriod}`;
    },

    // Load mock data for demonstration
    loadMockData() {
      // Mock overview metrics
      this.overviewMetrics = {
        visits: {
          title: 'Total Visits',
          value: '1,248',
          change: '+12.5%',
          trend: 'up',
          icon: 'visibility'
        },
        interactions: {
          title: 'Interactions',
          value: '3,842',
          change: '+8.3%',
          trend: 'up',
          icon: 'touch_app'
        },
        avgTime: {
          title: 'Avg. Session Time',
          value: '5:32',
          change: '+2.1%',
          trend: 'up',
          icon: 'schedule'
        },
        conversion: {
          title: 'Conversion Rate',
          value: '3.2%',
          change: '-0.5%',
          trend: 'down',
          icon: 'trending_up'
        }
      };

      // Mock showroom analytics
      this.showroomAnalytics = [
        {
          name: 'Main Showroom',
          visits: 856,
          avgTime: '5:32',
          interactions: 2450,
          conversionRate: '4.2%'
        },
        {
          name: 'New Collection',
          visits: 392,
          avgTime: '4:15',
          interactions: 1392,
          conversionRate: '3.8%'
        },
        {
          name: 'Office Furniture',
          visits: 245,
          avgTime: '3:45',
          interactions: 980,
          conversionRate: '2.5%'
        }
      ];

      // Mock top clients
      this.topClients = [
        {
          name: 'John Smith',
          company: 'Acme Corporation',
          visits: 24,
          lastVisit: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          engagementScore: 92
        },
        {
          name: 'Sarah Johnson',
          company: 'Johnson Interiors',
          visits: 18,
          lastVisit: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
          engagementScore: 87
        },
        {
          name: 'Michael Brown',
          company: 'Brown & Associates',
          visits: 15,
          lastVisit: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          engagementScore: 76
        },
        {
          name: 'Emily Davis',
          company: 'Davis Design',
          visits: 12,
          lastVisit: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          engagementScore: 68
        },
        {
          name: 'Robert Wilson',
          company: 'Wilson Architects',
          visits: 10,
          lastVisit: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          engagementScore: 62
        }
      ];
    },

    // Initialize charts
    initCharts() {
      this.initShowroomChart();
      this.initProductCharts();
      this.initClientCharts();
      this.initEngagementCharts();
    },

    // Initialize showroom performance chart
    initShowroomChart() {
      if (this.$refs.showroomChart && this.activeTab === 'showroom') {
        const ctx = this.$refs.showroomChart.getContext('2d');

        if (this.charts.showroom) {
          this.charts.showroom.destroy();
        }

        this.charts.showroom = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: this.showroomAnalytics.map(s => s.name),
            datasets: [
              {
                label: 'Visits',
                data: this.showroomAnalytics.map(s => s.visits),
                backgroundColor: 'rgba(52, 152, 219, 0.7)',
                borderColor: 'rgba(52, 152, 219, 1)',
                borderWidth: 1
              },
              {
                label: 'Interactions',
                data: this.showroomAnalytics.map(s => s.interactions),
                backgroundColor: 'rgba(46, 204, 113, 0.7)',
                borderColor: 'rgba(46, 204, 113, 1)',
                borderWidth: 1
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          }
        });
      }
    },

    // Initialize product charts
    initProductCharts() {
      // Product charts initialization would go here
    },

    // Initialize client charts
    initClientCharts() {
      // Client charts initialization would go here
    },

    // Initialize engagement charts
    initEngagementCharts() {
      // Engagement charts initialization would go here
    },

    // Get trend icon based on trend direction
    getTrendIcon(trend) {
      switch (trend) {
        case 'up':
          return 'trending_up';
        case 'down':
          return 'trending_down';
        default:
          return 'trending_flat';
      }
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    // Get CSS class for engagement score
    getEngagementClass(score) {
      if (score >= 80) return 'high';
      if (score >= 50) return 'medium';
      return 'low';
    },

    // Export data in different formats
    exportData(format) {
      console.log(`Exporting data in ${format} format...`);
      // This would be replaced with actual export functionality
      alert(`Data exported in ${format.toUpperCase()} format.`);
    }
  },

  watch: {
    activeTab() {
      // Initialize charts when tab changes
      this.$nextTick(() => {
        this.initCharts();
      });
    }
  }
};
</script>

<style scoped>
.analytics-manager {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.time-period-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.time-period-selector label {
  font-weight: 500;
}

.time-period-selector select {
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.custom-date-range {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 10px;
}

.date-input {
  display: flex;
  align-items: center;
  gap: 5px;
}

.date-input input {
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Overview Section Styles */
.overview-section {
  margin-bottom: 20px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
}

.metric-card {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--theme--border-color);
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--primary-background);
}

.metric-icon i {
  font-size: 24px;
  color: var(--theme--primary);
}

.metric-icon.up {
  background-color: rgba(46, 204, 113, 0.1);
}

.metric-icon.up i {
  color: #2ecc71;
}

.metric-icon.down {
  background-color: rgba(231, 76, 60, 0.1);
}

.metric-icon.down i {
  color: #e74c3c;
}

.metric-content {
  flex-grow: 1;
}

.metric-title {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 5px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.metric-trend.up {
  color: #2ecc71;
}

.metric-trend.down {
  color: #e74c3c;
}

.metric-trend.neutral {
  color: var(--theme--foreground-subdued);
}

/* Tabs Styles */
.analytics-tabs {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  overflow: hidden;
}

.tabs-header {
  display: flex;
  border-bottom: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
  overflow-x: auto;
}

.tab-button {
  padding: 15px 20px;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: var(--theme--foreground-subdued);
  border-bottom: 2px solid transparent;
  white-space: nowrap;
}

.tab-button.active {
  color: var(--theme--primary);
  border-bottom-color: var(--theme--primary);
  background-color: var(--theme--background);
}

.tab-content {
  padding: 20px;
}

.tab-pane {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tab-pane h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
}

.tab-pane p {
  margin: 0 0 20px 0;
  color: var(--theme--foreground-subdued);
}

/* Chart Styles */
.chart-container {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  padding: 20px;
  margin-bottom: 20px;
}

.chart-container h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: var(--theme--foreground);
}

.chart-wrapper {
  height: 300px;
  position: relative;
}

/* Table Styles */
.table-container {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  padding: 20px;
  margin-bottom: 20px;
  overflow-x: auto;
}

.table-container h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: var(--theme--foreground);
}

.analytics-table {
  width: 100%;
  border-collapse: collapse;
}

.analytics-table th {
  text-align: left;
  padding: 12px 15px;
  border-bottom: 1px solid var(--theme--border-color);
  font-weight: 600;
  color: var(--theme--foreground-subdued);
}

.analytics-table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--theme--border-color);
}

.analytics-table tr:last-child td {
  border-bottom: none;
}

.engagement-score {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 600;
  text-align: center;
  min-width: 40px;
}

.engagement-score.high {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.engagement-score.medium {
  background-color: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.engagement-score.low {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

/* Export Section Styles */
.export-section {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  padding: 20px;
}

.export-section h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
}

.export-options {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--border-color);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 14px;
}

.btn-sm i {
  font-size: 16px;
}
</style>
