import { logger } from '../shared/utils/logger';

/**
 * Performance monitoring utility for tracking and reporting performance metrics
 */
class PerformanceMonitor {
  /**
   * Create a new performance monitor
   * @param {Object} options - Configuration options
   * @param {String} options.componentName - Name of the component being monitored
   * @param {Boolean} options.enableTelemetry - Whether to send telemetry data to the server
   * @param {String} options.telemetryEndpoint - Endpoint for sending telemetry data
   * @param {Number} options.sampleRate - Rate at which to sample metrics (0-1)
   * @param {Boolean} options.logToConsole - Whether to log metrics to the console
   */
  constructor(options = {}) {
    this.componentName = options.componentName || 'Unknown';
    this.enableTelemetry = options.enableTelemetry !== undefined ? options.enableTelemetry : true;
    this.telemetryEndpoint = options.telemetryEndpoint || '/api/telemetry';
    this.sampleRate = options.sampleRate || 0.1; // Sample 10% of events by default
    this.logToConsole = options.logToConsole !== undefined ? options.logToConsole : false;
    
    // Initialize metrics storage
    this.metrics = {
      renderTimes: [],
      loadTimes: {
        api: [],
        cache: []
      },
      cacheStats: {
        hits: 0,
        misses: 0,
        evictions: 0,
        expirations: 0
      },
      memoryUsage: [],
      interactions: {},
      errors: []
    };
    
    // Track session information
    this.sessionId = this.generateSessionId();
    this.sessionStartTime = Date.now();
    
    // Bind methods
    this.trackRenderTime = this.trackRenderTime.bind(this);
    this.trackLoadTime = this.trackLoadTime.bind(this);
    this.trackCacheEvent = this.trackCacheEvent.bind(this);
    this.trackMemoryUsage = this.trackMemoryUsage.bind(this);
    this.trackInteraction = this.trackInteraction.bind(this);
    this.trackError = this.trackError.bind(this);
    this.getMetrics = this.getMetrics.bind(this);
    this.sendTelemetry = this.sendTelemetry.bind(this);
    this.resetMetrics = this.resetMetrics.bind(this);
    
    // Set up automatic telemetry sending
    if (this.enableTelemetry) {
      // Send telemetry every 5 minutes
      this.telemetryInterval = setInterval(() => {
        this.sendTelemetry();
      }, 5 * 60 * 1000);
      
      // Send telemetry when the page is unloaded
      if (typeof window !== 'undefined') {
        window.addEventListener('beforeunload', () => {
          this.sendTelemetry();
        });
      }
    }
  }
  
  /**
   * Generate a unique session ID
   * @returns {String} - Unique session ID
   * @private
   */
  generateSessionId() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * Track render time
   * @param {Number} renderTime - Time in milliseconds
   */
  trackRenderTime(renderTime) {
    if (Math.random() > this.sampleRate) return;
    
    this.metrics.renderTimes.push({
      time: renderTime,
      timestamp: Date.now()
    });
    
    // Keep only the last 100 render times
    if (this.metrics.renderTimes.length > 100) {
      this.metrics.renderTimes.shift();
    }
    
    if (this.logToConsole) {
      logger.info(`[PerformanceMonitor] Render time: ${renderTime}ms`);
    }
  }
  
  /**
   * Track load time
   * @param {String} source - Source of the load (api or cache)
   * @param {Number} loadTime - Time in milliseconds
   */
  trackLoadTime(source, loadTime) {
    if (Math.random() > this.sampleRate) return;
    
    if (source === 'api' || source === 'cache') {
      this.metrics.loadTimes[source].push({
        time: loadTime,
        timestamp: Date.now()
      });
      
      // Keep only the last 100 load times
      if (this.metrics.loadTimes[source].length > 100) {
        this.metrics.loadTimes[source].shift();
      }
      
      if (this.logToConsole) {
        logger.info(`[PerformanceMonitor] ${source} load time: ${loadTime}ms`);
      }
    }
  }
  
  /**
   * Track cache event
   * @param {String} event - Event type (hit, miss, eviction, expiration)
   */
  trackCacheEvent(event) {
    if (Math.random() > this.sampleRate) return;
    
    if (this.metrics.cacheStats[event] !== undefined) {
      this.metrics.cacheStats[event]++;
      
      if (this.logToConsole) {
        logger.info(`[PerformanceMonitor] Cache ${event}`);
      }
    }
  }
  
  /**
   * Track memory usage
   * @param {Number} memoryUsed - Memory used in bytes
   * @param {Number} memoryLimit - Memory limit in bytes
   */
  trackMemoryUsage(memoryUsed, memoryLimit) {
    if (Math.random() > this.sampleRate) return;
    
    this.metrics.memoryUsage.push({
      used: memoryUsed,
      limit: memoryLimit,
      timestamp: Date.now()
    });
    
    // Keep only the last 100 memory usage samples
    if (this.metrics.memoryUsage.length > 100) {
      this.metrics.memoryUsage.shift();
    }
    
    if (this.logToConsole) {
      logger.info(`[PerformanceMonitor] Memory usage: ${Math.round(memoryUsed / 1024);}KB / ${Math.round(memoryLimit / 1024)}KB (${Math.round(memoryUsed / memoryLimit * 100)}%)`);
    }
  }
  
  /**
   * Track user interaction
   * @param {String} action - Action performed
   * @param {Object} details - Additional details
   */
  trackInteraction(action, details = {}) {
    if (Math.random() > this.sampleRate) return;
    
    if (!this.metrics.interactions[action]) {
      this.metrics.interactions[action] = [];
    }
    
    this.metrics.interactions[action].push({
      details,
      timestamp: Date.now()
    });
    
    // Keep only the last 100 interactions per action
    if (this.metrics.interactions[action].length > 100) {
      this.metrics.interactions[action].shift();
    }
    
    if (this.logToConsole) {
      logger.info(`[PerformanceMonitor] Interaction: ${action}`, details);
    }
  }
  
  /**
   * Track error
   * @param {String} message - Error message
   * @param {Object} details - Additional details
   */
  trackError(message, details = {}) {
    this.metrics.errors.push({
      message,
      details,
      timestamp: Date.now()
    });
    
    // Keep only the last 100 errors
    if (this.metrics.errors.length > 100) {
      this.metrics.errors.shift();
    }
    
    if (this.logToConsole) {
      console.error(`[PerformanceMonitor] Error: ${message}`, details);
    }
  }
  
  /**
   * Get current metrics
   * @returns {Object} - Current metrics
   */
  getMetrics() {
    // Calculate derived metrics
    const averageRenderTime = this.metrics.renderTimes.length > 0
      ? this.metrics.renderTimes.reduce((sum, item) => sum + item.time, 0) / this.metrics.renderTimes.length
      : 0;
      
    const averageApiLoadTime = this.metrics.loadTimes.api.length > 0
      ? this.metrics.loadTimes.api.reduce((sum, item) => sum + item.time, 0) / this.metrics.loadTimes.api.length
      : 0;
      
    const averageCacheLoadTime = this.metrics.loadTimes.cache.length > 0
      ? this.metrics.loadTimes.cache.reduce((sum, item) => sum + item.time, 0) / this.metrics.loadTimes.cache.length
      : 0;
      
    const cacheHitRatio = (this.metrics.cacheStats.hits + this.metrics.cacheStats.misses) > 0
      ? this.metrics.cacheStats.hits / (this.metrics.cacheStats.hits + this.metrics.cacheStats.misses)
      : 0;
      
    const averageMemoryUsage = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage.reduce((sum, item) => sum + item.used, 0) / this.metrics.memoryUsage.length
      : 0;
      
    const averageMemoryUsageRatio = this.metrics.memoryUsage.length > 0
      ? this.metrics.memoryUsage.reduce((sum, item) => sum + (item.used / item.limit), 0) / this.metrics.memoryUsage.length
      : 0;
    
    return {
      componentName: this.componentName,
      sessionId: this.sessionId,
      sessionDuration: Date.now() - this.sessionStartTime,
      timestamp: Date.now(),
      metrics: {
        renderTimes: this.metrics.renderTimes,
        loadTimes: this.metrics.loadTimes,
        cacheStats: this.metrics.cacheStats,
        memoryUsage: this.metrics.memoryUsage,
        interactions: this.metrics.interactions,
        errors: this.metrics.errors
      },
      summary: {
        averageRenderTime,
        averageApiLoadTime,
        averageCacheLoadTime,
        cacheHitRatio,
        averageMemoryUsage,
        averageMemoryUsageRatio,
        totalErrors: this.metrics.errors.length,
        performanceImprovement: averageApiLoadTime > 0 && averageCacheLoadTime > 0
          ? averageApiLoadTime / averageCacheLoadTime
          : 0
      }
    };
  }
  
  /**
   * Send telemetry data to the server
   * @returns {Promise} - Promise that resolves when telemetry is sent
   */
  async sendTelemetry() {
    if (!this.enableTelemetry) return;
    
    try {
      const metrics = this.getMetrics();
      
      // Send telemetry data to the server
      const response = await fetch(this.telemetryEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metrics)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to send telemetry: ${response.status} ${response.statusText}`);
      }
      
      if (this.logToConsole) {
        logger.info('[PerformanceMonitor] Telemetry sent successfully');
      }
      
      // Reset metrics after sending
      this.resetMetrics();
      
      return response;
    } catch (error) {
      console.error('[PerformanceMonitor] Failed to send telemetry:', error);
    }
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      renderTimes: [],
      loadTimes: {
        api: [],
        cache: []
      },
      cacheStats: {
        hits: 0,
        misses: 0,
        evictions: 0,
        expirations: 0
      },
      memoryUsage: [],
      interactions: {},
      errors: []
    };
  }
  
  /**
   * Dispose of resources
   */
  dispose() {
    if (this.telemetryInterval) {
      clearInterval(this.telemetryInterval);
      this.telemetryInterval = null;
    }
    
    // Send final telemetry
    if (this.enableTelemetry) {
      this.sendTelemetry();
    }
  }
}

export default PerformanceMonitor;
