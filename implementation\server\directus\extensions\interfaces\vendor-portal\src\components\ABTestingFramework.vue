<template>
  <div class="ab-testing-framework">
    <div class="ab-testing-header">
      <h2 class="ab-testing-title">A/B Testing Framework</h2>
      <button class="create-test-button" @click="showCreateTestModal = true">
        <i class="material-icons">add</i>
        Create New Test
      </button>
    </div>

    <div class="ab-testing-content">
      <!-- Active Tests -->
      <div class="test-section">
        <h3 class="section-title">Active Tests</h3>
        <div v-if="activeTests.length === 0" class="empty-state">
          <i class="material-icons">science</i>
          <p>No active tests. Create a new test to get started.</p>
        </div>
        <div v-else class="test-list">
          <div
            v-for="test in activeTests"
            :key="test.id"
            class="test-card"
            @click="selectTest(test)"
          >
            <div class="test-card-header">
              <h4 class="test-name">{{ test.name }}</h4>
              <span class="test-status active">Active</span>
            </div>
            <div class="test-card-content">
              <div class="test-info">
                <div class="info-item">
                  <span class="info-label">Start Date:</span>
                  <span class="info-value">{{ formatDate(test.startDate) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">End Date:</span>
                  <span class="info-value">{{ formatDate(test.endDate) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Variants:</span>
                  <span class="info-value">{{ test.variants.length }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Traffic:</span>
                  <span class="info-value">{{ test.trafficPercentage }}%</span>
                </div>
              </div>
              <div class="test-metrics">
                <div class="metric-item">
                  <span class="metric-value">{{ test.totalVisitors }}</span>
                  <span class="metric-label">Visitors</span>
                </div>
                <div class="metric-item">
                  <span class="metric-value">{{ test.conversionRate.toFixed(2) }}%</span>
                  <span class="metric-label">Conversion</span>
                </div>
              </div>
            </div>
            <div class="test-card-footer">
              <button class="view-results-button" @click.stop="viewTestResults(test)">
                View Results
              </button>
              <button class="stop-test-button" @click.stop="stopTest(test)">
                Stop Test
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Completed Tests -->
      <div class="test-section">
        <h3 class="section-title">Completed Tests</h3>
        <div v-if="completedTests.length === 0" class="empty-state">
          <i class="material-icons">check_circle</i>
          <p>No completed tests yet.</p>
        </div>
        <div v-else class="test-list">
          <div
            v-for="test in completedTests"
            :key="test.id"
            class="test-card"
            @click="selectTest(test)"
          >
            <div class="test-card-header">
              <h4 class="test-name">{{ test.name }}</h4>
              <span class="test-status completed">Completed</span>
            </div>
            <div class="test-card-content">
              <div class="test-info">
                <div class="info-item">
                  <span class="info-label">Duration:</span>
                  <span class="info-value">{{ calculateDuration(test.startDate, test.endDate) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Variants:</span>
                  <span class="info-value">{{ test.variants.length }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Winner:</span>
                  <span class="info-value">{{ getWinnerName(test) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">Improvement:</span>
                  <span class="info-value">{{ calculateImprovement(test) }}%</span>
                </div>
              </div>
              <div class="test-metrics">
                <div class="metric-item">
                  <span class="metric-value">{{ test.totalVisitors }}</span>
                  <span class="metric-label">Visitors</span>
                </div>
                <div class="metric-item">
                  <span class="metric-value">{{ test.conversionRate.toFixed(2) }}%</span>
                  <span class="metric-label">Conversion</span>
                </div>
              </div>
            </div>
            <div class="test-card-footer">
              <button class="view-results-button" @click.stop="viewTestResults(test)">
                View Results
              </button>
              <button class="duplicate-test-button" @click.stop="duplicateTest(test)">
                Duplicate
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Test Modal -->
    <div v-if="showCreateTestModal" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Create New A/B Test</h3>
          <button class="close-button" @click="showCreateTestModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="modal-content">
          <div class="form-group">
            <label for="test-name">Test Name</label>
            <input
              id="test-name"
              v-model="newTest.name"
              type="text"
              placeholder="Enter a descriptive name for your test"
            />
          </div>

          <div class="form-group">
            <label for="test-description">Description</label>
            <textarea
              id="test-description"
              v-model="newTest.description"
              placeholder="Describe the purpose of this test"
              rows="3"
            ></textarea>
          </div>

          <div class="form-row">
            <div class="form-group half">
              <label for="test-start-date">Start Date</label>
              <input
                id="test-start-date"
                v-model="newTest.startDate"
                type="date"
              />
            </div>

            <div class="form-group half">
              <label for="test-end-date">End Date</label>
              <input
                id="test-end-date"
                v-model="newTest.endDate"
                type="date"
              />
            </div>
          </div>

          <div class="form-group">
            <label for="test-traffic">Traffic Allocation (%)</label>
            <input
              id="test-traffic"
              v-model.number="newTest.trafficPercentage"
              type="range"
              min="1"
              max="100"
              step="1"
            />
            <div class="range-value">{{ newTest.trafficPercentage }}%</div>
          </div>

          <div class="form-group">
            <label>Variants</label>
            <div class="variants-container">
              <div
                v-for="(variant, index) in newTest.variants"
                :key="index"
                class="variant-item"
              >
                <div class="variant-header">
                  <h4>{{ variant.name }}</h4>
                  <button
                    v-if="index > 0"
                    class="remove-variant-button"
                    @click="removeVariant(index)"
                  >
                    <i class="material-icons">delete</i>
                  </button>
                </div>

                <div class="form-group">
                  <label :for="`variant-name-${index}`">Variant Name</label>
                  <input
                    :id="`variant-name-${index}`"
                    v-model="variant.name"
                    type="text"
                    placeholder="Variant name"
                  />
                </div>

                <div class="form-group">
                  <label :for="`variant-description-${index}`">Description</label>
                  <textarea
                    :id="`variant-description-${index}`"
                    v-model="variant.description"
                    placeholder="Describe this variant"
                    rows="2"
                  ></textarea>
                </div>

                <div class="form-group">
                  <label :for="`variant-traffic-${index}`">Traffic Weight</label>
                  <input
                    :id="`variant-traffic-${index}`"
                    v-model.number="variant.trafficWeight"
                    type="range"
                    min="1"
                    max="100"
                    step="1"
                  />
                  <div class="range-value">{{ variant.trafficWeight }}%</div>
                </div>
              </div>

              <button class="add-variant-button" @click="addVariant">
                <i class="material-icons">add</i>
                Add Variant
              </button>
            </div>
          </div>

          <div class="form-group">
            <label>Metrics to Track</label>
            <div class="metrics-container">
              <div
                v-for="(metric, index) in availableMetrics"
                :key="metric.id"
                class="metric-checkbox"
              >
                <input
                  :id="`metric-${metric.id}`"
                  type="checkbox"
                  v-model="newTest.selectedMetrics"
                  :value="metric.id"
                />
                <label :for="`metric-${metric.id}`">{{ metric.name }}</label>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="cancel-button" @click="showCreateTestModal = false">Cancel</button>
          <button class="create-button" @click="createTest" :disabled="!isFormValid">Create Test</button>
        </div>
      </div>
    </div>

    <!-- Test Results Modal -->
    <div v-if="showResultsModal" class="modal-overlay">
      <div class="modal-container results-modal">
        <div class="modal-header">
          <h3>Test Results: {{ selectedTest.name }}</h3>
          <button class="close-button" @click="showResultsModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="modal-content">
          <!-- Results content will be implemented in a separate component -->
          <p>Results visualization will be implemented here.</p>
        </div>

        <div class="modal-footer">
          <button class="export-button" @click="exportResults">
            <i class="material-icons">download</i>
            Export Results
          </button>
          <button class="close-button" @click="showResultsModal = false">Close</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue';

export default {
  name: 'ABTestingFramework',

  setup() {
    // Mock data for demonstration
    const activeTests = ref([
      {
        id: 1,
        name: 'Homepage Hero Section Test',
        description: 'Testing different hero section layouts',
        startDate: '2023-06-01',
        endDate: '2023-06-30',
        trafficPercentage: 50,
        variants: [
          { id: 1, name: 'Control', description: 'Current design', trafficWeight: 50 },
          { id: 2, name: 'Variant A', description: 'New design with video', trafficWeight: 50 }
        ],
        totalVisitors: 1245,
        conversionRate: 3.75,
        selectedMetrics: [1, 2, 3]
      }
    ]);

    const completedTests = ref([
      {
        id: 2,
        name: 'Product Page Layout Test',
        description: 'Testing different product page layouts',
        startDate: '2023-05-01',
        endDate: '2023-05-31',
        trafficPercentage: 100,
        variants: [
          { id: 3, name: 'Control', description: 'Current layout', trafficWeight: 33, visitors: 523, conversions: 26 },
          { id: 4, name: 'Variant A', description: 'Simplified layout', trafficWeight: 33, visitors: 498, conversions: 35 },
          { id: 5, name: 'Variant B', description: 'Video-first layout', trafficWeight: 34, visitors: 511, conversions: 31 }
        ],
        totalVisitors: 1532,
        conversionRate: 6.01,
        selectedMetrics: [1, 3, 4],
        winner: 4
      }
    ]);

    // Available metrics
    const availableMetrics = [
      { id: 1, name: 'Page Views' },
      { id: 2, name: 'Time on Page' },
      { id: 3, name: 'Click-through Rate' },
      { id: 4, name: 'Conversion Rate' },
      { id: 5, name: 'Bounce Rate' }
    ];

    // Modal states
    const showCreateTestModal = ref(false);
    const showResultsModal = ref(false);

    // Selected test for viewing results
    const selectedTest = ref(null);

    // New test form
    const newTest = reactive({
      name: '',
      description: '',
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      trafficPercentage: 50,
      variants: [
        { name: 'Control', description: 'Current version', trafficWeight: 50 }
      ],
      selectedMetrics: [1, 4]
    });

    // Form validation
    const isFormValid = computed(() => {
      return (
        newTest.name.trim() !== '' &&
        newTest.startDate &&
        newTest.endDate &&
        new Date(newTest.startDate) < new Date(newTest.endDate) &&
        newTest.variants.length >= 2 &&
        newTest.variants.every(v => v.name.trim() !== '') &&
        newTest.selectedMetrics.length > 0
      );
    });

    // Methods
    const formatDate = (dateString) => {
      const date = new Date(dateString);
      return date.toLocaleDateString();
    };

    const calculateDuration = (startDate, endDate) => {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return `${diffDays} days`;
    };

    const getWinnerName = (test) => {
      if (!test.winner) return 'No clear winner';
      const winner = test.variants.find(v => v.id === test.winner);
      return winner ? winner.name : 'Unknown';
    };

    const calculateImprovement = (test) => {
      if (!test.winner) return 0;

      const winner = test.variants.find(v => v.id === test.winner);
      const control = test.variants.find(v => v.name === 'Control');

      if (!winner || !control) return 0;

      const winnerRate = winner.conversions / winner.visitors * 100;
      const controlRate = control.conversions / control.visitors * 100;

      return ((winnerRate - controlRate) / controlRate * 100).toFixed(2);
    };

    const selectTest = (test) => {
      selectedTest.value = test;
    };

    const viewTestResults = (test) => {
      selectedTest.value = test;
      showResultsModal.value = true;
    };

    const stopTest = (test) => {
      // In a real implementation, this would call an API to stop the test
      const index = activeTests.value.findIndex(t => t.id === test.id);
      if (index !== -1) {
        const stoppedTest = { ...activeTests.value[index], endDate: new Date().toISOString().split('T')[0] };
        activeTests.value.splice(index, 1);
        completedTests.value.unshift(stoppedTest);
      }
    };

    const duplicateTest = (test) => {
      // Create a duplicate of the test with a new ID
      const duplicate = {
        ...JSON.parse(JSON.stringify(test)),
        id: Date.now(),
        name: `${test.name} (Copy)`,
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      };

      // Reset metrics
      duplicate.totalVisitors = 0;
      duplicate.conversionRate = 0;

      // Add to active tests
      activeTests.value.unshift(duplicate);
    };

    const addVariant = () => {
      newTest.variants.push({
        name: `Variant ${String.fromCharCode(65 + newTest.variants.length - 1)}`,
        description: '',
        trafficWeight: Math.floor(100 / (newTest.variants.length + 1))
      });

      // Redistribute traffic weights
      const weight = Math.floor(100 / newTest.variants.length);
      newTest.variants.forEach(variant => {
        variant.trafficWeight = weight;
      });
    };

    const removeVariant = (index) => {
      newTest.variants.splice(index, 1);

      // Redistribute traffic weights
      const weight = Math.floor(100 / newTest.variants.length);
      newTest.variants.forEach(variant => {
        variant.trafficWeight = weight;
      });
    };

    const createTest = () => {
      // In a real implementation, this would call an API to create the test
      const newTestObj = {
        id: Date.now(),
        ...JSON.parse(JSON.stringify(newTest)),
        totalVisitors: 0,
        conversionRate: 0
      };

      activeTests.value.unshift(newTestObj);
      showCreateTestModal.value = false;

      // Reset form
      newTest.name = '';
      newTest.description = '';
      newTest.startDate = new Date().toISOString().split('T')[0];
      newTest.endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      newTest.trafficPercentage = 50;
      newTest.variants = [
        { name: 'Control', description: 'Current version', trafficWeight: 50 }
      ];
      newTest.selectedMetrics = [1, 4];
    };

    const exportResults = () => {
      // In a real implementation, this would generate and download a report
      console.log('Exporting results for test:', selectedTest.value);
    };

    return {
      activeTests,
      completedTests,
      availableMetrics,
      showCreateTestModal,
      showResultsModal,
      selectedTest,
      newTest,
      isFormValid,
      formatDate,
      calculateDuration,
      getWinnerName,
      calculateImprovement,
      selectTest,
      viewTestResults,
      stopTest,
      duplicateTest,
      addVariant,
      removeVariant,
      createTest,
      exportResults
    };
  }
};
</script>

<style scoped>
.ab-testing-framework {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
}

.ab-testing-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.ab-testing-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.create-test-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border: none;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.create-test-button:hover {
  background-color: var(--theme--primary-dark);
}

.ab-testing-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (min-width: 1200px) {
  .ab-testing-content {
    grid-template-columns: 1fr 1fr;
  }
}

.test-section {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 0 0 1px var(--theme--border-color);
}

.section-title {
  margin: 0;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid var(--theme--border-color);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

.test-list {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.test-card {
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  transition: box-shadow 0.2s, transform 0.2s;
  cursor: pointer;
}

.test-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.test-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.test-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.test-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.test-status.active {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
}

.test-status.completed {
  background-color: var(--theme--success-background);
  color: var(--theme--success);
}

.test-card-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.info-value {
  font-weight: 500;
}

.test-metrics {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.metric-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.metric-value {
  font-size: 20px;
  font-weight: 600;
  color: var(--theme--primary);
}

.metric-label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.test-card-footer {
  display: flex;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
}

.view-results-button,
.stop-test-button,
.duplicate-test-button {
  flex: 1;
  padding: 6px 12px;
  border-radius: var(--theme--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  border: 1px solid var(--theme--border-color);
}

.view-results-button {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
  border-color: var(--theme--primary);
}

.view-results-button:hover {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
}

.stop-test-button {
  background-color: var(--theme--danger-background);
  color: var(--theme--danger);
  border-color: var(--theme--danger);
}

.stop-test-button:hover {
  background-color: var(--theme--danger);
  color: white;
}

.duplicate-test-button {
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.duplicate-test-button:hover {
  background-color: var(--theme--background-subdued);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.results-modal {
  width: 800px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-button,
.create-button,
.export-button {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  border: 1px solid var(--theme--border-color);
}

.cancel-button:hover {
  background-color: var(--theme--background-subdued);
}

.create-button {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border: none;
}

.create-button:hover {
  background-color: var(--theme--primary-dark);
}

.create-button:disabled {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
  cursor: not-allowed;
}

.export-button {
  display: flex;
  align-items: center;
  gap: 4px;
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
  border: 1px solid var(--theme--primary);
}

.export-button:hover {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
}

/* Form */
.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group.half {
  flex: 1;
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 4px;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
}

.form-group input[type="range"] {
  width: 100%;
}

.range-value {
  text-align: right;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin-top: 4px;
}

.variants-container {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 16px;
  margin-bottom: 16px;
}

.variant-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.variant-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.variant-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.variant-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.remove-variant-button {
  background: none;
  border: none;
  color: var(--theme--danger);
  cursor: pointer;
}

.add-variant-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 8px;
  background-color: var(--theme--background-subdued);
  border: 1px dashed var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.add-variant-button:hover {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
  border-color: var(--theme--primary);
}

.metrics-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
}

.metric-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-checkbox input[type="checkbox"] {
  margin: 0;
}
</style>
