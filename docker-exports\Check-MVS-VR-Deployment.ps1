# MVS-VR DigitalOcean Deployment Checker and Auto-Fixer
# This script connects to your DigitalOcean server, checks deployment status, and fixes issues

param(
    [string]$ServerIP = "**************",
    [string]$Username = "vectorax", 
    [string]$SSHKeyPath = "C:\Users\<USER>\.ssh\mvs-vr",
    [string]$Domain = "mvs.kanousai.com",
    [string]$ProjectPath = "/home/<USER>/mvs-vr-deployment",
    [switch]$AutoFix = $true,
    [string]$OutputFile = "deployment-status-$(Get-Date -Format 'yyyy-MM-dd-HHmm').txt"
)

# Colors for output
$Red = "Red"
$Green = "Green" 
$Yellow = "Yellow"
$Blue = "Cyan"

# Test results storage
$TestResults = @()
$FixActions = @()

function Write-Status {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
    $script:TestResults += "$(Get-Date -Format 'HH:mm:ss') - $Message"
}

function Write-Success {
    param([string]$Message)
    Write-Status "✅ SUCCESS: $Message" $Green
}

function Write-Error {
    param([string]$Message)
    Write-Status "❌ ERROR: $Message" $Red
}

function Write-Warning {
    param([string]$Message)
    Write-Status "⚠️  WARNING: $Message" $Yellow
}

function Write-Info {
    param([string]$Message)
    Write-Status "ℹ️  INFO: $Message" $Blue
}

function Invoke-SSHCommand {
    param([string]$Command, [switch]$IgnoreErrors)
    
    try {
        $result = ssh -i $SSHKeyPath -o StrictHostKeyChecking=no $Username@$ServerIP $Command 2>&1
        if ($LASTEXITCODE -eq 0 -or $IgnoreErrors) {
            return $result
        } else {
            throw "SSH command failed: $result"
        }
    } catch {
        Write-Error "SSH Command failed: $Command - Error: $_"
        return $null
    }
}

function Test-SSHConnection {
    Write-Info "Testing SSH connection to $ServerIP..."
    
    $result = Invoke-SSHCommand "echo 'SSH Connection Test'" -IgnoreErrors
    if ($result -match "SSH Connection Test") {
        Write-Success "SSH connection established"
        return $true
    } else {
        Write-Error "Cannot establish SSH connection"
        return $false
    }
}

function Test-DockerInstallation {
    Write-Info "Checking Docker installation..."
    
    $dockerVersion = Invoke-SSHCommand "docker --version" -IgnoreErrors
    if ($dockerVersion -match "Docker version") {
        Write-Success "Docker is installed: $dockerVersion"
        
        $composeVersion = Invoke-SSHCommand "docker-compose --version" -IgnoreErrors
        if ($composeVersion -match "docker-compose version") {
            Write-Success "Docker Compose is installed: $composeVersion"
            return $true
        } else {
            Write-Warning "Docker Compose not found, attempting to install..."
            if ($AutoFix) {
                return Install-DockerCompose
            }
            return $false
        }
    } else {
        Write-Error "Docker not found"
        if ($AutoFix) {
            return Install-Docker
        }
        return $false
    }
}

function Install-Docker {
    Write-Info "Installing Docker..."
    $script:FixActions += "Installing Docker"
    
    $commands = @(
        "curl -fsSL https://get.docker.com -o get-docker.sh",
        "sh get-docker.sh",
        "systemctl start docker",
        "systemctl enable docker",
        "usermod -aG docker $Username"
    )
    
    foreach ($cmd in $commands) {
        $result = Invoke-SSHCommand $cmd -IgnoreErrors
        Write-Info "Executed: $cmd"
    }
    
    # Verify installation
    Start-Sleep 5
    $dockerVersion = Invoke-SSHCommand "docker --version" -IgnoreErrors
    if ($dockerVersion -match "Docker version") {
        Write-Success "Docker installed successfully"
        return $true
    } else {
        Write-Error "Docker installation failed"
        return $false
    }
}

function Install-DockerCompose {
    Write-Info "Installing Docker Compose..."
    $script:FixActions += "Installing Docker Compose"
    
    $commands = @(
        "curl -L `"https://github.com/docker/compose/releases/latest/download/docker-compose-`$(uname -s)-`$(uname -m)`" -o /usr/local/bin/docker-compose",
        "chmod +x /usr/local/bin/docker-compose",
        "ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose"
    )
    
    foreach ($cmd in $commands) {
        Invoke-SSHCommand $cmd -IgnoreErrors
    }
    
    $composeVersion = Invoke-SSHCommand "docker-compose --version" -IgnoreErrors
    if ($composeVersion -match "docker-compose version") {
        Write-Success "Docker Compose installed successfully"
        return $true
    } else {
        Write-Error "Docker Compose installation failed"
        return $false
    }
}

function Test-ProjectDeployment {
    Write-Info "Checking project deployment..."
    
    $projectExists = Invoke-SSHCommand "test -d $ProjectPath && echo 'exists'" -IgnoreErrors
    if ($projectExists -match "exists") {
        Write-Success "Project directory exists at $ProjectPath"
        
        $composeFile = Invoke-SSHCommand "test -f $ProjectPath/docker-compose.exported.yml && echo 'exists'" -IgnoreErrors
        if ($composeFile -match "exists") {
            Write-Success "Docker compose file found"
            return $true
        } else {
            Write-Warning "Docker compose file missing"
            if ($AutoFix) {
                return Deploy-Project
            }
            return $false
        }
    } else {
        Write-Warning "Project directory not found"
        if ($AutoFix) {
            return Deploy-Project
        }
        return $false
    }
}

function Deploy-Project {
    Write-Info "Deploying MVS-VR project..."
    $script:FixActions += "Deploying project files"
    
    # Create project directory
    Invoke-SSHCommand "mkdir -p $ProjectPath" -IgnoreErrors
    
    # Copy files using SCP
    Write-Info "Copying project files..."
    try {
        $scpResult = scp -i $SSHKeyPath -r ".\*" "$Username@$ServerIP`:$ProjectPath/" 2>&1
        Write-Success "Project files copied successfully"
        return $true
    } catch {
        Write-Error "Failed to copy project files: $_"
        return $false
    }
}

function Test-DockerContainers {
    Write-Info "Checking Docker containers..."
    
    $containers = Invoke-SSHCommand "cd $ProjectPath && docker-compose -f docker-compose.exported.yml ps --format json" -IgnoreErrors
    
    if ($containers) {
        $containerData = $containers | ConvertFrom-Json -ErrorAction SilentlyContinue
        if ($containerData) {
            $runningCount = ($containerData | Where-Object { $_.State -eq "running" }).Count
            $totalCount = $containerData.Count
            
            Write-Info "Containers status: $runningCount/$totalCount running"
            
            foreach ($container in $containerData) {
                if ($container.State -eq "running") {
                    Write-Success "Container $($container.Service) is running"
                } else {
                    Write-Warning "Container $($container.Service) is $($container.State)"
                }
            }
            
            if ($runningCount -lt $totalCount -and $AutoFix) {
                return Start-Containers
            }
            return $runningCount -gt 0
        }
    }
    
    Write-Warning "No containers found or not running"
    if ($AutoFix) {
        return Start-Containers
    }
    return $false
}

function Start-Containers {
    Write-Info "Starting Docker containers..."
    $script:FixActions += "Starting Docker containers"
    
    # Stop any existing containers
    Invoke-SSHCommand "cd $ProjectPath && docker-compose -f docker-compose.exported.yml down" -IgnoreErrors
    
    # Start containers
    $result = Invoke-SSHCommand "cd $ProjectPath && docker-compose -f docker-compose.exported.yml up -d" -IgnoreErrors
    
    # Wait for containers to start
    Start-Sleep 10
    
    # Check if containers are running
    $containers = Invoke-SSHCommand "cd $ProjectPath && docker-compose -f docker-compose.exported.yml ps --format json" -IgnoreErrors
    if ($containers) {
        Write-Success "Containers started successfully"
        return $true
    } else {
        Write-Error "Failed to start containers"
        return $false
    }
}

function Test-Port80Access {
    Write-Info "Testing port 80 accessibility..."
    
    # Test from server locally
    $localTest = Invoke-SSHCommand "curl -s -o /dev/null -w '%{http_code}' http://localhost/" -IgnoreErrors
    if ($localTest -eq "200") {
        Write-Success "Port 80 accessible locally on server"
    } else {
        Write-Warning "Port 80 not accessible locally (HTTP code: $localTest)"
    }
    
    # Test from external
    try {
        $response = Invoke-WebRequest -Uri "http://$ServerIP/" -TimeoutSec 10 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Success "Port 80 accessible externally from $ServerIP"
            return $true
        }
    } catch {
        Write-Warning "Port 80 not accessible externally: $_"
    }
    
    if ($AutoFix) {
        return Fix-Port80Access
    }
    return $false
}

function Fix-Port80Access {
    Write-Info "Fixing port 80 access..."
    $script:FixActions += "Configuring firewall for port 80"
    
    $commands = @(
        "ufw allow 80/tcp",
        "ufw allow 443/tcp", 
        "systemctl restart docker",
        "cd $ProjectPath && docker-compose -f docker-compose.exported.yml restart nginx"
    )
    
    foreach ($cmd in $commands) {
        Invoke-SSHCommand $cmd -IgnoreErrors
        Write-Info "Executed: $cmd"
    }
    
    Start-Sleep 5
    return Test-Port80Access
}

function Test-DomainResolution {
    Write-Info "Testing domain resolution for $Domain..."
    
    try {
        $dnsResult = Resolve-DnsName -Name $Domain -ErrorAction Stop
        $resolvedIP = $dnsResult | Where-Object { $_.Type -eq "A" } | Select-Object -First 1 -ExpandProperty IPAddress
        
        if ($resolvedIP -eq $ServerIP) {
            Write-Success "Domain $Domain correctly resolves to $ServerIP"
            return $true
        } else {
            Write-Warning "Domain $Domain resolves to $resolvedIP, expected $ServerIP"
            return $false
        }
    } catch {
        Write-Warning "Domain $Domain does not resolve: $_"
        return $false
    }
}

function Test-SupabaseConnectivity {
    Write-Info "Testing Supabase connectivity..."
    
    $supabaseTest = Invoke-SSHCommand "curl -s -o /dev/null -w '%{http_code}' https://hiyqiqbgiueyyvqoqhht.supabase.co/rest/v1/" -IgnoreErrors
    if ($supabaseTest -eq "401") {
        Write-Success "Supabase connectivity working (expected 401 without API key)"
        return $true
    } else {
        Write-Warning "Supabase connectivity issue (HTTP code: $supabaseTest)"
        return $false
    }
}

function Test-HealthEndpoints {
    Write-Info "Testing health endpoints..."

    $endpoints = @(
        @{ Name = "Main Health"; URL = "http://localhost/health" },
        @{ Name = "Main Page"; URL = "http://localhost/" },
        @{ Name = "External Health"; URL = "http://$ServerIP/health" },
        @{ Name = "External Main"; URL = "http://$ServerIP/" }
    )

    $allPassed = $true
    foreach ($endpoint in $endpoints) {
        if ($endpoint.URL -match "localhost") {
            # Test from server
            $result = Invoke-SSHCommand "curl -s -o /dev/null -w '%{http_code}' $($endpoint.URL)" -IgnoreErrors
        } else {
            # Test from local machine
            try {
                $response = Invoke-WebRequest -Uri $endpoint.URL -TimeoutSec 10 -ErrorAction Stop
                $result = $response.StatusCode
            } catch {
                $result = "Failed"
            }
        }

        if ($result -eq "200") {
            Write-Success "$($endpoint.Name) endpoint working"
        } else {
            Write-Warning "$($endpoint.Name) endpoint failed (HTTP code: $result)"
            $allPassed = $false
        }
    }

    return $allPassed
}

function Test-ServiceLogs {
    Write-Info "Checking service logs for errors..."

    $services = @("nginx", "redis", "auth-service", "api-gateway", "asset-service", "analytics-service")
    $hasErrors = $false

    foreach ($service in $services) {
        $logs = Invoke-SSHCommand "cd $ProjectPath && docker-compose -f docker-compose.exported.yml logs --tail=50 $service 2>/dev/null" -IgnoreErrors
        if ($logs) {
            $errorCount = ($logs | Select-String -Pattern "error|Error|ERROR|failed|Failed|FAILED").Count
            if ($errorCount -gt 0) {
                Write-Warning "Service $service has $errorCount error(s) in logs"
                $hasErrors = $true
            } else {
                Write-Success "Service $service logs look clean"
            }
        } else {
            Write-Warning "Could not retrieve logs for service $service"
        }
    }

    return -not $hasErrors
}

function Test-SystemResources {
    Write-Info "Checking system resources..."

    # Check disk space
    $diskSpace = Invoke-SSHCommand "df -h / | tail -1 | awk '{print `$5}' | sed 's/%//'" -IgnoreErrors
    if ($diskSpace -and [int]$diskSpace -lt 80) {
        Write-Success "Disk usage: $diskSpace% (healthy)"
    } else {
        Write-Warning "Disk usage: $diskSpace% (high)"
    }

    # Check memory
    $memInfo = Invoke-SSHCommand "free -m | grep Mem | awk '{printf `"%.1f`", `$3/`$2 * 100.0}'" -IgnoreErrors
    if ($memInfo -and [float]$memInfo -lt 80) {
        Write-Success "Memory usage: $memInfo% (healthy)"
    } else {
        Write-Warning "Memory usage: $memInfo% (high)"
    }

    # Check Docker stats
    $dockerStats = Invoke-SSHCommand "docker stats --no-stream --format 'table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}'" -IgnoreErrors
    if ($dockerStats) {
        Write-Success "Docker containers resource usage retrieved"
        $script:TestResults += "Docker Stats:`n$dockerStats"
    }

    return $true
}

function Generate-Report {
    Write-Info "Generating deployment report..."
    
    $report = @"
MVS-VR DigitalOcean Deployment Status Report
Generated: $(Get-Date)
Server: $ServerIP
Domain: $Domain
Project Path: $ProjectPath

=== TEST RESULTS ===
$($TestResults -join "`n")

=== FIXES APPLIED ===
$($FixActions -join "`n")

=== SUMMARY ===
Total Tests: $($TestResults.Count)
Fixes Applied: $($FixActions.Count)

=== NEXT STEPS ===
1. Verify all services are running properly
2. Configure DNS if domain resolution failed
3. Monitor logs for any issues
4. Test from external networks

Report saved to: $OutputFile
"@

    $report | Out-File -FilePath $OutputFile -Encoding UTF8
    Write-Success "Report saved to $OutputFile"
}

# Main execution
Write-Info "Starting MVS-VR DigitalOcean Deployment Check..."
Write-Info "Server: $ServerIP | Domain: $Domain | Auto-fix: $AutoFix"

$allTestsPassed = $true

# Run all tests
$tests = @(
    { Test-SSHConnection },
    { Test-DockerInstallation },
    { Test-ProjectDeployment },
    { Test-DockerContainers },
    { Test-Port80Access },
    { Test-DomainResolution },
    { Test-SupabaseConnectivity },
    { Test-HealthEndpoints },
    { Test-ServiceLogs },
    { Test-SystemResources }
)

foreach ($test in $tests) {
    if (-not (& $test)) {
        $allTestsPassed = $false
    }
    Write-Host ""
}

# Generate final report
Generate-Report

if ($allTestsPassed) {
    Write-Success "🎉 All tests passed! MVS-VR deployment is working correctly."
} else {
    Write-Warning "⚠️ Some tests failed. Check the report for details."
}

Write-Info "Deployment check completed. Report saved to: $OutputFile"
