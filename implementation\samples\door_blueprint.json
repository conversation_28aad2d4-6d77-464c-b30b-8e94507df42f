{"blueprint_id": "door_interactive", "version": "1.0.0", "name": "Interactive Door", "description": "Blueprint for interactive doors with hinge behavior", "tags": ["door", "interactive", "hinge"], "script": {"type": "behavior", "components": [{"type": "hinge", "config": {"auto_detect": true, "hinge_side": "auto", "max_angle": 90, "open_speed": 1.0, "close_speed": 1.0, "damping": 0.5, "auto_close": true, "auto_close_delay": 5.0, "lock_mechanism": false}}, {"type": "audio", "config": {"open_sound": "door_open", "close_sound": "door_close", "locked_sound": "door_locked", "volume": 1.0, "spatial": true}}, {"type": "interaction", "config": {"highlight_on_hover": true, "highlight_color": [0.2, 0.6, 1.0, 0.5], "interaction_distance": 150, "interaction_prompt": "Open/Close Door", "interaction_type": "hand_proximity"}}, {"type": "physics", "config": {"mass": 10.0, "friction": 0.5, "restitution": 0.1, "linear_damping": 0.5, "angular_damping": 0.5, "enable_gravity": true, "collision_profile": "interactive"}}], "triggers": [{"event": "interaction", "actions": [{"type": "toggle_state", "params": {"states": ["open", "closed"], "property": "hinge.state"}}, {"type": "play_sound", "params": {"sound": "hinge.state == 'open' ? 'open_sound' : 'close_sound'"}}]}, {"event": "state_changed", "condition": "hinge.state == 'open'", "actions": [{"type": "rotate_to", "params": {"angle": "hinge.max_angle", "axis": "hinge.axis", "duration": "1.0 / hinge.open_speed", "ease": "ease_out_cubic"}}]}, {"event": "state_changed", "condition": "hinge.state == 'closed'", "actions": [{"type": "rotate_to", "params": {"angle": 0, "axis": "hinge.axis", "duration": "1.0 / hinge.close_speed", "ease": "ease_out_cubic"}}]}, {"event": "timer", "condition": "hinge.state == 'open' && hinge.auto_close", "delay": "hinge.auto_close_delay", "actions": [{"type": "set_state", "params": {"property": "hinge.state", "value": "closed"}}]}], "initialization": [{"type": "detect_door_frame", "params": {"search_radius": 100, "required": true, "error_message": "No door frame detected nearby"}}, {"type": "detect_hinge_position", "params": {"side_preference": "hinge.hinge_side", "required": true, "error_message": "Could not determine hinge position"}}, {"type": "create_physics_constraint", "params": {"constraint_type": "hinge", "attach_parent": "door_frame", "attach_child": "self", "pivot": "hinge.position", "axis": "hinge.axis", "limit_angle": "hinge.max_angle"}}, {"type": "set_state", "params": {"property": "hinge.state", "value": "closed"}}], "properties": {"door_frame": null, "hinge": {"position": [0, 0, 0], "axis": [0, 1, 0], "state": "closed"}}, "debug": {"visualize_hinge": true, "visualize_constraint": true, "log_state_changes": true}}}