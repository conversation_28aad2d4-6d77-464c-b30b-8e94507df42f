<template>
  <div class="api-metrics">
    <v-skeleton-loader
      v-if="loading"
      type="card"
      class="mx-auto"
    ></v-skeleton-loader>
    
    <div v-else class="metrics-content">
      <!-- API Usage Overview -->
      <v-card class="mb-4">
        <v-card-title>API Usage Overview</v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-counter</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ apiUsage?.total_requests || 0 }}</div>
                  <div class="metric-label">Total Requests</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="success" size="36">mdi-check-circle</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ apiUsage?.success_rate || '0' }}%</div>
                  <div class="metric-label">Success Rate</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="error" size="36">mdi-alert-circle</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ apiUsage?.error_rate || '0' }}%</div>
                  <div class="metric-label">Error Rate</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="info" size="36">mdi-clock-outline</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ apiUsage?.avg_requests_per_minute || '0' }}</div>
                  <div class="metric-label">Avg Requests/Min</div>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <div class="period-selector">
                <v-btn-toggle
                  v-model="selectedPeriod"
                  mandatory
                  @change="periodChanged"
                >
                  <v-btn value="1h">1h</v-btn>
                  <v-btn value="6h">6h</v-btn>
                  <v-btn value="24h">24h</v-btn>
                  <v-btn value="7d">7d</v-btn>
                  <v-btn value="30d">30d</v-btn>
                </v-btn-toggle>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <div class="chart-container">
                <h3 class="chart-title">Requests by Endpoint</h3>
                <div class="chart-wrapper">
                  <canvas ref="requestsChart"></canvas>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      
      <!-- API Performance -->
      <v-card class="mb-4">
        <v-card-title>API Performance</v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-timer</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ apiPerformance?.avg_response_time || '0' }} ms</div>
                  <div class="metric-label">Avg Response Time</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="warning" size="36">mdi-timer-sand</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ apiPerformance?.p95_response_time || '0' }} ms</div>
                  <div class="metric-label">P95 Response Time</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="error" size="36">mdi-timer-alert</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ apiPerformance?.p99_response_time || '0' }} ms</div>
                  <div class="metric-label">P99 Response Time</div>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <div class="chart-container">
                <h3 class="chart-title">Response Time Trend</h3>
                <div class="chart-wrapper">
                  <canvas ref="responseTimeChart"></canvas>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <h3 class="section-title">Endpoint Performance</h3>
              <v-simple-table>
                <template v-slot:default>
                  <thead>
                    <tr>
                      <th>Endpoint</th>
                      <th>Avg Response Time</th>
                      <th>P95 Response Time</th>
                      <th>P99 Response Time</th>
                      <th>Error Rate</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(endpoint, index) in apiPerformance?.endpoint_performance" :key="index">
                      <td>{{ endpoint.name }}</td>
                      <td>{{ endpoint.avg_response_time }} ms</td>
                      <td>{{ endpoint.p95_response_time }} ms</td>
                      <td>{{ endpoint.p99_response_time }} ms</td>
                      <td>{{ endpoint.error_rate }}%</td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      
      <!-- Rate Limiting -->
      <v-card>
        <v-card-title>Rate Limiting</v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="error" size="36">mdi-shield-alert</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ rateLimiting?.rate_limit_breaches || 0 }}</div>
                  <div class="metric-label">Rate Limit Breaches</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="6">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="warning" size="36">mdi-shield</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ rateLimiting?.rate_limit_warnings || 0 }}</div>
                  <div class="metric-label">Rate Limit Warnings</div>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="6">
              <h3 class="section-title">Most Limited Endpoints</h3>
              <v-simple-table>
                <template v-slot:default>
                  <thead>
                    <tr>
                      <th>Endpoint</th>
                      <th>Breaches</th>
                      <th>Warnings</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(endpoint, index) in rateLimiting?.most_limited_endpoints" :key="index">
                      <td>{{ endpoint.endpoint }}</td>
                      <td>{{ endpoint.breaches }}</td>
                      <td>{{ endpoint.warnings }}</td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-col>
            
            <v-col cols="12" md="6">
              <h3 class="section-title">Most Limited Users</h3>
              <v-simple-table>
                <template v-slot:default>
                  <thead>
                    <tr>
                      <th>User</th>
                      <th>Breaches</th>
                      <th>Warnings</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(user, index) in rateLimiting?.most_limited_users" :key="index">
                      <td>{{ user.email }}</td>
                      <td>{{ user.breaches }}</td>
                      <td>{{ user.warnings }}</td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'ApiMetrics',
  
  props: {
    apiUsage: {
      type: Object,
      default: null
    },
    apiPerformance: {
      type: Object,
      default: null
    },
    rateLimiting: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      selectedPeriod: '24h',
      requestsChart: null,
      responseTimeChart: null
    };
  },
  
  watch: {
    apiUsage() {
      this.$nextTick(() => {
        this.initRequestsChart();
      });
    },
    apiPerformance() {
      this.$nextTick(() => {
        this.initResponseTimeChart();
      });
    }
  },
  
  mounted() {
    this.$nextTick(() => {
      if (this.apiUsage) {
        this.initRequestsChart();
      }
      
      if (this.apiPerformance) {
        this.initResponseTimeChart();
      }
    });
  },
  
  beforeDestroy() {
    if (this.requestsChart) {
      this.requestsChart.destroy();
    }
    
    if (this.responseTimeChart) {
      this.responseTimeChart.destroy();
    }
  },
  
  methods: {
    // Initialize requests chart
    initRequestsChart() {
      if (!this.apiUsage || !this.apiUsage.requests_by_endpoint) return;
      
      const ctx = this.$refs.requestsChart?.getContext('2d');
      if (!ctx) return;
      
      // Destroy existing chart if it exists
      if (this.requestsChart) {
        this.requestsChart.destroy();
      }
      
      // Prepare data for chart
      const datasets = this.apiUsage.requests_by_endpoint.map(endpoint => {
        return {
          label: endpoint.name,
          data: endpoint.data.map(point => point.value),
          borderColor: endpoint.color,
          backgroundColor: endpoint.color + '20',
          borderWidth: 2,
          tension: 0.4,
          fill: true
        };
      });
      
      // Create chart
      this.requestsChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.apiUsage.requests_by_endpoint[0]?.data.map(point => {
            const date = new Date(point.time);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          }),
          datasets: datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Requests'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Time'
              }
            }
          }
        }
      });
    },
    
    // Initialize response time chart
    initResponseTimeChart() {
      if (!this.apiPerformance || !this.apiPerformance.response_time_trend) return;
      
      const ctx = this.$refs.responseTimeChart?.getContext('2d');
      if (!ctx) return;
      
      // Destroy existing chart if it exists
      if (this.responseTimeChart) {
        this.responseTimeChart.destroy();
      }
      
      // Prepare data for chart
      const dataset = {
        label: this.apiPerformance.response_time_trend.name,
        data: this.apiPerformance.response_time_trend.data.map(point => point.value),
        borderColor: this.apiPerformance.response_time_trend.color,
        backgroundColor: this.apiPerformance.response_time_trend.color + '20',
        borderWidth: 2,
        tension: 0.4,
        fill: true
      };
      
      // Create chart
      this.responseTimeChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.apiPerformance.response_time_trend.data.map(point => {
            const date = new Date(point.time);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          }),
          datasets: [dataset]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Response Time (ms)'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Time'
              }
            }
          }
        }
      });
    },
    
    // Handle period change
    periodChanged() {
      this.$emit('refresh', { period: this.selectedPeriod });
    }
  }
};
</script>

<style scoped>
.api-metrics {
  padding: 16px;
}

.metrics-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  height: 100%;
}

.metric-icon {
  margin-right: 16px;
}

.metric-details {
  flex: 1;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 4px;
}

.metric-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.period-selector {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.chart-container {
  margin-top: 16px;
}

.chart-title {
  margin-bottom: 16px;
  font-size: 1.1rem;
  font-weight: 500;
}

.chart-wrapper {
  height: 300px;
}

.section-title {
  margin-bottom: 16px;
  font-size: 1.1rem;
  font-weight: 500;
}
</style>
