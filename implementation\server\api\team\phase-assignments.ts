/**
 * Team Phase Assignment Endpoints
 *
 * This file defines the API endpoints for team phase assignments.
 */

import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { TeamPhaseManagerService } from '../../services/team/team-phase-manager';
import { PhaseType } from '../../config/phase-config';

// Initialize team phase manager service
const teamPhaseManager = new TeamPhaseManagerService(supabase);

/**
 * Get phase assignments for a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const getPhaseAssignments = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get phase assignments
    const assignments = await teamPhaseManager.getPhaseAssignments(scene_id);

    res.status(200).json({
      success: true,
      data: assignments,
    });
  } catch (error) {
    logger.error('Error in getPhaseAssignments', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Assign phase to team member
 *
 * @param req - Request
 * @param res - Response
 */
export const assignPhaseToTeamMember = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { phase, assigned_to, due_date, notes } = req.body;
    const assigned_by = req.user.id;

    // Validate parameters
    if (!scene_id || !phase || !assigned_to) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Scene ID, phase, and assigned_to are required',
        },
      });
      return;
    }

    // Validate phase
    if (!Object.values(PhaseType).includes(phase)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PHASE',
          message: 'Invalid phase',
        },
      });
      return;
    }

    // Assign phase
    const result = await teamPhaseManager.assignPhaseToTeamMember(
      scene_id,
      phase,
      assigned_to,
      assigned_by,
      due_date,
      notes,
    );

    if (!result.success) {
      res.status(400).json({
        success: false,
        error: {
          code: 'ASSIGNMENT_FAILED',
          message: result.error || 'Failed to assign phase',
        },
      });
      return;
    }

    res.status(201).json({
      success: true,
      data: result.assignment,
    });
  } catch (error) {
    logger.error('Error in assignPhaseToTeamMember', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Update phase assignment status
 *
 * @param req - Request
 * @param res - Response
 */
export const updatePhaseAssignmentStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { assignment_id } = req.params;
    const { status, notes } = req.body;

    // Validate parameters
    if (!assignment_id || !status) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Assignment ID and status are required',
        },
      });
      return;
    }

    // Validate status
    if (!['pending', 'in_progress', 'completed', 'overdue'].includes(status)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_STATUS',
          message: 'Invalid status',
        },
      });
      return;
    }

    // Update status
    const result = await teamPhaseManager.updatePhaseAssignmentStatus(assignment_id, status, notes);

    if (!result.success) {
      res.status(400).json({
        success: false,
        error: {
          code: 'UPDATE_FAILED',
          message: result.error || 'Failed to update assignment status',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: result.assignment,
    });
  } catch (error) {
    logger.error('Error in updatePhaseAssignmentStatus', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get team members for a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const getSceneTeamMembers = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get team members
    const teamMembers = await teamPhaseManager.getSceneTeamMembers(scene_id);

    res.status(200).json({
      success: true,
      data: teamMembers,
    });
  } catch (error) {
    logger.error('Error in getSceneTeamMembers', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on method and path
    if (req.method === 'GET' && req.path.endsWith('/team-members')) {
      await getSceneTeamMembers(req, res);
    } else if (req.method === 'GET') {
      await getPhaseAssignments(req, res);
    } else if (req.method === 'POST') {
      await assignPhaseToTeamMember(req, res);
    } else if (req.method === 'PATCH') {
      await updatePhaseAssignmentStatus(req, res);
    } else {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
    }
  } catch (error) {
    logger.error('Error in phase assignment handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
}
