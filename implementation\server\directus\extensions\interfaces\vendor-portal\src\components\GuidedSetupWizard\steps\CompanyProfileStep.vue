<template>
  <wizard-step
    title="Company Profile"
    description="Set up your company's basic information that will be displayed to clients."
    :step-data="stepData"
    :validation-schema="validationSchema"
    :help-tips="helpTips"
    @update:step-data="updateStepData"
    @validate="validateStep"
  >
    <div class="company-profile-form">
      <div class="form-section">
        <h3 class="section-title">Basic Information</h3>
        
        <div class="form-group">
          <label for="company-name">Company Name *</label>
          <input
            id="company-name"
            type="text"
            v-model="localStepData.companyName"
            @input="updateField('companyName', $event.target.value)"
            placeholder="Enter your company name"
          />
        </div>
        
        <div class="form-group">
          <label for="industry">Industry *</label>
          <select
            id="industry"
            v-model="localStepData.industry"
            @change="updateField('industry', $event.target.value)"
          >
            <option value="">Select an industry</option>
            <option value="furniture">Furniture</option>
            <option value="interior_design">Interior Design</option>
            <option value="architecture">Architecture</option>
            <option value="real_estate">Real Estate</option>
            <option value="retail">Retail</option>
            <option value="hospitality">Hospitality</option>
            <option value="other">Other</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="description">Company Description</label>
          <textarea
            id="description"
            v-model="localStepData.description"
            @input="updateField('description', $event.target.value)"
            placeholder="Describe your company and what you offer"
            rows="4"
          ></textarea>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Contact Information</h3>
        
        <div class="form-group">
          <label for="contact-email">Contact Email *</label>
          <input
            id="contact-email"
            type="email"
            v-model="localStepData.contactEmail"
            @input="updateField('contactEmail', $event.target.value)"
            placeholder="Enter your contact email"
          />
        </div>
        
        <div class="form-group">
          <label for="contact-phone">Contact Phone</label>
          <input
            id="contact-phone"
            type="tel"
            v-model="localStepData.contactPhone"
            @input="updateField('contactPhone', $event.target.value)"
            placeholder="Enter your contact phone number"
          />
        </div>
        
        <div class="form-group">
          <label for="website">Website</label>
          <input
            id="website"
            type="url"
            v-model="localStepData.website"
            @input="updateField('website', $event.target.value)"
            placeholder="Enter your website URL"
          />
        </div>
        
        <div class="form-group">
          <label for="address">Address</label>
          <textarea
            id="address"
            v-model="localStepData.address"
            @input="updateField('address', $event.target.value)"
            placeholder="Enter your company address"
            rows="3"
          ></textarea>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Company Logo</h3>
        
        <div class="logo-upload">
          <div 
            class="logo-preview" 
            :class="{ 'has-logo': localStepData.logo && localStepData.logo.preview }"
          >
            <img 
              v-if="localStepData.logo && localStepData.logo.preview" 
              :src="localStepData.logo.preview" 
              alt="Company Logo Preview" 
            />
            <div v-else class="logo-placeholder">
              <i class="material-icons">business</i>
              <span>No logo uploaded</span>
            </div>
          </div>
          
          <div class="logo-actions">
            <label class="upload-button">
              <input 
                type="file" 
                accept="image/*" 
                @change="handleLogoUpload" 
                hidden
              />
              <i class="material-icons">upload</i>
              <span>Upload Logo</span>
            </label>
            
            <button 
              v-if="localStepData.logo && localStepData.logo.preview" 
              class="remove-button"
              @click="removeLogo"
            >
              <i class="material-icons">delete</i>
              <span>Remove</span>
            </button>
          </div>
          
          <p class="logo-help">
            Upload a high-resolution company logo (recommended size: 512x512px)
          </p>
        </div>
      </div>
    </div>
  </wizard-step>
</template>

<script>
import WizardStep from '../WizardStep.vue';

export default {
  name: 'CompanyProfileStep',
  
  components: {
    WizardStep
  },
  
  props: {
    stepData: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      localStepData: {
        companyName: '',
        industry: '',
        description: '',
        contactEmail: '',
        contactPhone: '',
        website: '',
        address: '',
        logo: null,
        ...this.stepData
      },
      validationSchema: {
        companyName: {
          required: true,
          minLength: 2,
          maxLength: 100,
          label: 'Company Name'
        },
        industry: {
          required: true,
          label: 'Industry'
        },
        description: {
          maxLength: 500,
          label: 'Company Description'
        },
        contactEmail: {
          required: true,
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
          patternMessage: 'Please enter a valid email address',
          label: 'Contact Email'
        },
        contactPhone: {
          pattern: '^[0-9+\\-\\s()]*$',
          patternMessage: 'Please enter a valid phone number',
          label: 'Contact Phone'
        },
        website: {
          pattern: '^(https?:\\/\\/)?([\\da-z.-]+)\\.([a-z.]{2,6})([\\/\\w .-]*)*\\/?$',
          patternMessage: 'Please enter a valid website URL',
          label: 'Website'
        }
      },
      helpTips: [
        {
          title: 'Why is this important?',
          text: 'Your company profile information will be displayed to clients in the virtual showroom and helps establish your brand identity.'
        },
        {
          title: 'Logo Requirements',
          text: 'For best results, upload a square logo with dimensions of at least 512x512 pixels in PNG or JPG format.'
        }
      ]
    };
  },
  
  methods: {
    updateStepData(data) {
      this.localStepData = { ...data };
      this.$emit('update:step-data', this.localStepData);
    },
    
    updateField(field, value) {
      this.localStepData[field] = value;
      this.$emit('update:step-data', this.localStepData);
    },
    
    validateStep(isValid) {
      this.$emit('validate', isValid);
    },
    
    handleLogoUpload(event) {
      const file = event.target.files[0];
      
      if (!file) return;
      
      // Check file type
      if (!file.type.match('image.*')) {
        alert('Please upload an image file');
        return;
      }
      
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size should not exceed 5MB');
        return;
      }
      
      // Create file preview
      const reader = new FileReader();
      
      reader.onload = (e) => {
        this.localStepData.logo = {
          file: file,
          preview: e.target.result,
          name: file.name,
          size: file.size,
          type: file.type
        };
        
        this.$emit('update:step-data', this.localStepData);
      };
      
      reader.readAsDataURL(file);
    },
    
    removeLogo() {
      this.localStepData.logo = null;
      this.$emit('update:step-data', this.localStepData);
    }
  }
};
</script>

<style scoped>
.company-profile-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--theme--foreground);
}

.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--theme--foreground);
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--theme--primary);
  outline: none;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.logo-upload {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.logo-preview {
  width: 128px;
  height: 128px;
  border: 2px dashed var(--theme--border-color);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logo-preview.has-logo {
  border: none;
}

.logo-preview img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.logo-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.logo-placeholder i {
  font-size: 32px;
  margin-bottom: 8px;
}

.logo-placeholder span {
  font-size: 12px;
}

.logo-actions {
  display: flex;
  gap: 8px;
}

.upload-button,
.remove-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-button {
  background-color: var(--theme--primary);
  border: none;
  color: var(--theme--primary-background);
}

.remove-button {
  background-color: transparent;
  border: 1px solid var(--theme--danger);
  color: var(--theme--danger);
}

.upload-button:hover {
  background-color: var(--theme--primary-accent);
}

.remove-button:hover {
  background-color: rgba(var(--theme--danger-rgb), 0.1);
}

.logo-help {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin: 0;
}
</style>
