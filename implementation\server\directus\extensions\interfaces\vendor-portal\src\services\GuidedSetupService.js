/**
 * GuidedSetupService.js
 * Service for handling API interactions for the Guided Setup Wizard
 */

import axios from 'axios';
import { getDirectusUrl, getDirectusToken } from '../utils/directus.js';

class GuidedSetupService {
  constructor() {
    this.baseUrl = getDirectusUrl();
    this.token = getDirectusToken();
    this.axios = axios.create({
      baseURL: this.baseUrl,
      headers: {
        Authorization: `Bearer ${this.token}`,
        'Content-Type': 'application/json',
      },
    });
  }

  /**
   * Get the onboarding status for a vendor
   * @param {string} vendorId - The vendor ID
   * @returns {Promise<Object>} - The onboarding status
   */
  async getOnboardingStatus(vendorId) {
    try {
      const response = await this.axios.get(
        `/items/vendor_onboarding?filter[vendor_id][_eq]=${vendorId}`,
      );
      return response.data.data.length > 0 ? response.data.data[0] : null;
    } catch (error) {
      console.error('Error getting onboarding status:', error);
      throw error;
    }
  }

  /**
   * Save the onboarding status for a vendor
   * @param {string} vendorId - The vendor ID
   * @param {boolean} isCompleted - Whether the onboarding is completed
   * @param {Object} progressData - The progress data
   * @returns {Promise<Object>} - The saved onboarding status
   */
  async saveOnboardingStatus(vendorId, isCompleted, progressData) {
    try {
      const existingStatus = await this.getOnboardingStatus(vendorId);

      if (existingStatus) {
        // Update existing status
        const response = await this.axios.patch(`/items/vendor_onboarding/${existingStatus.id}`, {
          is_completed: isCompleted,
          progress_data: JSON.stringify(progressData),
          updated_at: new Date().toISOString(),
        });
        return response.data.data;
      } else {
        // Create new status
        const response = await this.axios.post('/items/vendor_onboarding', {
          vendor_id: vendorId,
          is_completed: isCompleted,
          progress_data: JSON.stringify(progressData),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
        return response.data.data;
      }
    } catch (error) {
      console.error('Error saving onboarding status:', error);
      throw error;
    }
  }

  /**
   * Save company profile data
   * @param {string} vendorId - The vendor ID
   * @param {Object} profileData - The company profile data
   * @returns {Promise<Object>} - The saved company profile
   */
  async saveCompanyProfile(vendorId, profileData) {
    try {
      const response = await this.axios.patch(`/items/vendors/${vendorId}`, {
        company_name: profileData.companyName,
        industry: profileData.industry,
        description: profileData.description,
        contact_email: profileData.contactEmail,
        contact_phone: profileData.contactPhone,
        website: profileData.website,
        address: profileData.address,
      });

      // If logo was uploaded, handle it separately
      if (profileData.logo && profileData.logo.file) {
        await this.uploadCompanyLogo(vendorId, profileData.logo.file);
      }

      return response.data.data;
    } catch (error) {
      console.error('Error saving company profile:', error);
      throw error;
    }
  }

  /**
   * Upload company logo
   * @param {string} vendorId - The vendor ID
   * @param {File} logoFile - The logo file
   * @returns {Promise<Object>} - The uploaded file data
   */
  async uploadCompanyLogo(vendorId, logoFile) {
    try {
      const formData = new FormData();
      formData.append('file', logoFile);

      // Upload the file
      const fileResponse = await this.axios.post('/files', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const fileId = fileResponse.data.data.id;

      // Update the vendor with the new logo
      await this.axios.patch(`/items/vendors/${vendorId}`, {
        logo: fileId,
      });

      return fileResponse.data.data;
    } catch (error) {
      console.error('Error uploading company logo:', error);
      throw error;
    }
  }

  /**
   * Save user account data
   * @param {string} vendorId - The vendor ID
   * @param {Object} accountData - The user account data
   * @returns {Promise<Object>} - The saved user account data
   */
  async saveUserAccounts(vendorId, accountData) {
    try {
      // First, update the admin user
      const adminResponse = await this.axios.patch(`/users/${accountData.adminUser.id}`, {
        first_name: accountData.adminUser.firstName,
        last_name: accountData.adminUser.lastName,
        email: accountData.adminUser.email,
        role: accountData.adminUser.role,
      });

      // Then, create or update team members
      const teamMemberPromises = accountData.teamMembers.map(async member => {
        if (member.id) {
          // Update existing team member
          return this.axios.patch(`/users/${member.id}`, {
            first_name: member.firstName,
            last_name: member.lastName,
            email: member.email,
            role: member.role,
          });
        } else {
          // Create new team member
          return this.axios.post('/users', {
            first_name: member.firstName,
            last_name: member.lastName,
            email: member.email,
            role: member.role,
            status: 'invited',
            provider: 'default',
          });
        }
      });

      const teamMemberResponses = await Promise.all(teamMemberPromises);

      return {
        adminUser: adminResponse.data.data,
        teamMembers: teamMemberResponses.map(response => response.data.data),
      };
    } catch (error) {
      console.error('Error saving user accounts:', error);
      throw error;
    }
  }

  /**
   * Save branding data
   * @param {string} vendorId - The vendor ID
   * @param {Object} brandingData - The branding data
   * @returns {Promise<Object>} - The saved branding data
   */
  async saveBranding(vendorId, brandingData) {
    try {
      const response = await this.axios.patch(
        `/items/vendor_branding?filter[vendor_id][_eq]=${vendorId}`,
        {
          primary_color: brandingData.primaryColor,
          secondary_color: brandingData.secondaryColor,
          accent_color: brandingData.accentColor,
          font_primary: brandingData.fontPrimary,
          font_secondary: brandingData.fontSecondary,
          logo_placement: brandingData.logoPlacement,
          theme_mode: brandingData.themeMode,
        },
      );

      return response.data.data;
    } catch (error) {
      console.error('Error saving branding data:', error);
      throw error;
    }
  }

  /**
   * Track wizard analytics
   * @param {string} vendorId - The vendor ID
   * @param {Object} analyticsData - The analytics data
   * @returns {Promise<Object>} - The saved analytics data
   */
  async trackWizardAnalytics(vendorId, analyticsData) {
    try {
      const response = await this.axios.post('/items/wizard_analytics', {
        vendor_id: vendorId,
        event_type: analyticsData.eventType,
        event_data: JSON.stringify(analyticsData.eventData),
        timestamp: new Date().toISOString(),
      });

      return response.data.data;
    } catch (error) {
      console.error('Error tracking wizard analytics:', error);
      // Don't throw error for analytics to prevent blocking the main flow
      return null;
    }
  }
}

export default GuidedSetupService;
