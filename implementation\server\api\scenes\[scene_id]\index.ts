import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../shared/utils/logger';
import { errorHandler } from '../../../shared/utils/error-handler';

// Define the response schema
const SceneResponseSchema = z.object({
  scene_id: z.string().uuid(),
  vendor_id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable(),
  version: z.string(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
});

// Define the query parameters schema
const QueryParamsSchema = z.object({
  scene_id: z.string().uuid(),
});

/**
 * Scene API endpoint
 *
 * This endpoint returns information about a scene.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse(req.query);
    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { scene_id } = queryResult.data;

    // Log the request
    logger.info('Scene request', {
      scene_id,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get scene
    const { data: scene, error: sceneError } = await supabase
      .from('scenes')
      .select('id, vendor_id, name, description, version, created_at, updated_at')
      .eq('id', scene_id)
      .single();

    if (sceneError) {
      logger.error('Error fetching scene', { error: sceneError, scene_id });
      return res.status(500).json({ error: 'Error fetching scene' });
    }

    if (!scene) {
      return res.status(404).json({ error: 'Scene not found' });
    }

    // Prepare response
    const response = {
      scene_id: scene.id,
      vendor_id: scene.vendor_id,
      name: scene.name,
      description: scene.description,
      version: scene.version,
      created_at: scene.created_at,
      updated_at: scene.updated_at,
    };

    // Validate response
    const responseResult = SceneResponseSchema.safeParse(response);
    if (!responseResult.success) {
      logger.error('Invalid response format', { error: responseResult.error.format(), response });
      return res.status(500).json({ error: 'Invalid response format' });
    }

    // Log success
    logger.info('Scene response', {
      scene_id,
    });

    // Return response
    return res.status(200).json(response);
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
