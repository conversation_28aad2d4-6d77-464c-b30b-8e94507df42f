/**
 * Role Sync Hook for Directus
 *
 * This hook synchronizes user roles between Supabase and Directus.
 * It ensures that when a user's role changes in Supabase, it is reflected in Directus,
 * and vice versa.
 */

const { createClient } = require('@supabase/supabase-js');

// Role mapping between Supabase and Directus
const ROLE_MAPPING = {
  // Supabase role: Directus role ID
  super_admin: '1', // Directus Administrator role
  admin: '2', // Custom Admin role
  vendor_admin: '3', // Vendor Admin role
  vendor_user: '4', // Vendor User role
  client_admin: '5', // Client Admin role
  client_user: '6', // Client User role
};

// Cache for role information to reduce database queries
const roleCache = new Map();
const userCache = new Map();

// Cache expiration time (in milliseconds)
const CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutes

module.exports = function registerHook({ env, logger, database, services }) {
  // Initialize Supabase client
  const supabaseUrl = env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseKey) {
    logger.warn('Supabase credentials not set. Role sync hook will not function.');
    return;
  }

  const supabase = createClient(supabaseUrl, supabaseKey);

  // Helper function to get Directus role ID from Supabase role
  function getDirectusRoleId(supabaseRole) {
    return ROLE_MAPPING[supabaseRole] || ROLE_MAPPING['client_user']; // Default to client_user
  }

  // Helper function to get Supabase role from Directus role ID
  function getSupabaseRole(directusRoleId) {
    for (const [supabaseRole, dirRoleId] of Object.entries(ROLE_MAPPING)) {
      if (dirRoleId === directusRoleId) {
        return supabaseRole;
      }
    }
    return 'client_user'; // Default to client_user
  }

  // Helper function to log and handle errors
  function handleError(operation, userId, error) {
    logger.error(`Role sync error during ${operation} for user ${userId}: ${error.message}`);
    logger.debug(error.stack);
  }

  // Helper function to get user from Supabase by email
  async function getUserFromSupabase(email) {
    // Check cache first
    if (userCache.has(email)) {
      const { user, timestamp } = userCache.get(email);
      if (Date.now() - timestamp < CACHE_EXPIRATION) {
        return user;
      }
      userCache.delete(email); // Cache expired
    }

    try {
      const { data, error } = await supabase
        .from('users')
        .select('id, email, role')
        .eq('email', email)
        .single();

      if (error) throw error;

      // Cache the result
      userCache.set(email, { user: data, timestamp: Date.now() });
      return data;
    } catch (error) {
      logger.error(`Error fetching user from Supabase: ${error.message}`);
      return null;
    }
  }

  // Register hooks for role synchronization
  return {
    // When a user is created or updated in Directus
    'users.update': async (payload, meta) => {
      try {
        // Get the user's email and role from Directus
        const directusUser = await database
          .select('email', 'role')
          .from('directus_users')
          .where({ id: payload.id })
          .first();

        if (!directusUser) return;

        // Get the corresponding Supabase user
        const supabaseUser = await getUserFromSupabase(directusUser.email);
        if (!supabaseUser) return;

        // Get the Supabase role that corresponds to the Directus role
        const supabaseRole = getSupabaseRole(directusUser.role);

        // Update the user's role in Supabase if it's different
        if (supabaseUser.role !== supabaseRole) {
          const { error } = await supabase
            .from('users')
            .update({ role: supabaseRole })
            .eq('id', supabaseUser.id);

          if (error) throw error;
          logger.info(`Updated role in Supabase for user ${directusUser.email} to ${supabaseRole}`);
        }
      } catch (error) {
        handleError('update', payload.id, error);
      }
    },

    // Custom endpoint to sync roles from Supabase to Directus
    'server.start': async () => {
      logger.info('Role sync hook initialized');

      // Create a custom endpoint for manual role synchronization
      services.register('custom-endpoints', {
        id: 'sync-roles',
        handler: async (req, res) => {
          try {
            // Get all users from Supabase
            const { data: supabaseUsers, error } = await supabase
              .from('users')
              .select('id, email, role');

            if (error) throw error;

            // For each Supabase user, update the corresponding Directus user
            for (const user of supabaseUsers) {
              // Find the Directus user by email
              const directusUser = await database
                .select('id', 'role')
                .from('directus_users')
                .where({ email: user.email })
                .first();

              if (!directusUser) continue;

              // Get the Directus role ID that corresponds to the Supabase role
              const directusRoleId = getDirectusRoleId(user.role);

              // Update the user's role in Directus if it's different
              if (directusUser.role !== directusRoleId) {
                await database('directus_users')
                  .update({ role: directusRoleId })
                  .where({ id: directusUser.id });

                logger.info(`Updated role in Directus for user ${user.email} to ${directusRoleId}`);
              }
            }

            res.json({ success: true, message: 'Roles synchronized successfully' });
          } catch (error) {
            logger.error(`Error synchronizing roles: ${error.message}`);
            res.status(500).json({ success: false, message: error.message });
          }
        },
      });
    },
  };
};
