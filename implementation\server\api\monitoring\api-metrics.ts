/**
 * API Metrics Monitoring
 *
 * This module provides endpoints for monitoring API usage and performance metrics.
 */

import { Router, Request, Response } from 'express';
import { rateLimiter } from '../../middleware/rate-limiter';
import { logger } from '../../lib/logger';
import { supabase } from '../../lib/supabase';

const router = Router();

/**
 * Get API usage metrics
 *
 * @param req - Request
 * @param res - Response
 */
export const getApiUsageMetrics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { period = '24h' } = req.query;

    // Get API usage metrics
    const apiUsageMetrics = await getApiUsage(period as string);

    res.status(200).json({
      success: true,
      data: apiUsageMetrics,
    });
  } catch (error) {
    logger.error('Error getting API usage metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting API usage metrics',
      },
    });
  }
};

/**
 * Get API performance metrics
 *
 * @param req - Request
 * @param res - Response
 */
export const getApiPerformanceMetrics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { period = '24h' } = req.query;

    // Get API performance metrics
    const apiPerformanceMetrics = await getApiPerformance(period as string);

    res.status(200).json({
      success: true,
      data: apiPerformanceMetrics,
    });
  } catch (error) {
    logger.error('Error getting API performance metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting API performance metrics',
      },
    });
  }
};

/**
 * Get rate limiting metrics
 *
 * @param req - Request
 * @param res - Response
 */
export const getRateLimitingMetrics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { period = '24h' } = req.query;

    // Get rate limiting metrics
    const rateLimitingMetrics = await getRateLimiting(period as string);

    res.status(200).json({
      success: true,
      data: rateLimitingMetrics,
    });
  } catch (error) {
    logger.error('Error getting rate limiting metrics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting rate limiting metrics',
      },
    });
  }
};

/**
 * Get API usage metrics
 *
 * @param period - Time period
 * @returns API usage metrics
 */
async function getApiUsage(period: string): Promise<any> {
  // In a real implementation, this would query an API metrics database
  // For now, we'll return mock data

  // Calculate start time based on period
  const now = new Date();
  let startTime: Date;

  switch (period) {
    case '1h':
      startTime = new Date(now.getTime() - 60 * 60 * 1000);
      break;
    case '6h':
      startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
      break;
    case '24h':
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  // Generate time points for the chart
  const timePoints = generateTimePoints(startTime, now, period);

  // Generate mock data for each endpoint
  const endpoints = [
    { name: '/api/products', color: '#4CAF50' },
    { name: '/api/users', color: '#2196F3' },
    { name: '/api/orders', color: '#FFC107' },
    { name: '/api/analytics', color: '#9C27B0' },
    { name: '/api/auth', color: '#F44336' },
  ];

  const requestsData = endpoints.map(endpoint => {
    return {
      name: endpoint.name,
      color: endpoint.color,
      data: timePoints.map(point => ({
        time: point,
        value: Math.floor(Math.random() * 100) + 50,
      })),
    };
  });

  // Calculate total requests
  const totalRequests = requestsData.reduce((total, endpoint) => {
    return total + endpoint.data.reduce((sum, point) => sum + point.value, 0);
  }, 0);

  // Calculate success rate (95-100%)
  const successRate = 95 + Math.random() * 5;

  // Calculate error rate (100 - success rate)
  const errorRate = 100 - successRate;

  // Calculate average requests per minute
  const minutesDiff = (now.getTime() - startTime.getTime()) / (60 * 1000);
  const avgRequestsPerMinute = totalRequests / minutesDiff;

  return {
    total_requests: totalRequests,
    success_rate: successRate.toFixed(2),
    error_rate: errorRate.toFixed(2),
    avg_requests_per_minute: avgRequestsPerMinute.toFixed(2),
    requests_by_endpoint: requestsData,
    period: period,
    start_time: startTime.toISOString(),
    end_time: now.toISOString(),
  };
}

/**
 * Get API performance metrics
 *
 * @param period - Time period
 * @returns API performance metrics
 */
async function getApiPerformance(period: string): Promise<any> {
  // In a real implementation, this would query an API performance database
  // For now, we'll return mock data

  // Calculate start time based on period
  const now = new Date();
  let startTime: Date;

  switch (period) {
    case '1h':
      startTime = new Date(now.getTime() - 60 * 60 * 1000);
      break;
    case '6h':
      startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
      break;
    case '24h':
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  // Generate time points for the chart
  const timePoints = generateTimePoints(startTime, now, period);

  // Generate mock data for response time
  const responseTimeData = {
    name: 'Average Response Time',
    color: '#2196F3',
    data: timePoints.map(point => ({
      time: point,
      value: Math.floor(Math.random() * 100) + 50, // 50-150ms
    })),
  };

  // Calculate average response time
  const avgResponseTime =
    responseTimeData.data.reduce((sum, point) => sum + point.value, 0) /
    responseTimeData.data.length;

  // Generate mock data for each endpoint's response time
  const endpoints = [
    { name: '/api/products', color: '#4CAF50' },
    { name: '/api/users', color: '#2196F3' },
    { name: '/api/orders', color: '#FFC107' },
    { name: '/api/analytics', color: '#9C27B0' },
    { name: '/api/auth', color: '#F44336' },
  ];

  const endpointPerformance = endpoints.map(endpoint => {
    const baseResponseTime = avgResponseTime * (0.8 + Math.random() * 0.4); // 80-120% of average

    return {
      name: endpoint.name,
      avg_response_time: baseResponseTime.toFixed(2),
      p95_response_time: (baseResponseTime * 1.5).toFixed(2),
      p99_response_time: (baseResponseTime * 2).toFixed(2),
      error_rate: (Math.random() * 2).toFixed(2), // 0-2%
    };
  });

  return {
    avg_response_time: avgResponseTime.toFixed(2),
    p95_response_time: (avgResponseTime * 1.5).toFixed(2),
    p99_response_time: (avgResponseTime * 2).toFixed(2),
    response_time_trend: responseTimeData,
    endpoint_performance: endpointPerformance,
    period: period,
    start_time: startTime.toISOString(),
    end_time: now.toISOString(),
  };
}

/**
 * Get rate limiting metrics
 *
 * @param period - Time period
 * @returns Rate limiting metrics
 */
async function getRateLimiting(period: string): Promise<any> {
  // In a real implementation, this would query a rate limiting database
  // For now, we'll return mock data

  // Calculate start time based on period
  const now = new Date();
  let startTime: Date;

  switch (period) {
    case '1h':
      startTime = new Date(now.getTime() - 60 * 60 * 1000);
      break;
    case '6h':
      startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
      break;
    case '24h':
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  return {
    rate_limit_breaches: 12,
    rate_limit_warnings: 48,
    most_limited_endpoints: [
      { endpoint: '/api/products', breaches: 5, warnings: 18 },
      { endpoint: '/api/analytics', breaches: 4, warnings: 15 },
      { endpoint: '/api/users', breaches: 2, warnings: 10 },
      { endpoint: '/api/orders', breaches: 1, warnings: 5 },
    ],
    most_limited_users: [
      { user_id: 'user-123', email: '<EMAIL>', breaches: 3, warnings: 12 },
      { user_id: 'user-456', email: '<EMAIL>', breaches: 2, warnings: 8 },
      { user_id: 'user-789', email: '<EMAIL>', breaches: 1, warnings: 6 },
    ],
    period: period,
    start_time: startTime.toISOString(),
    end_time: now.toISOString(),
  };
}

/**
 * Generate time points for charts
 *
 * @param startTime - Start time
 * @param endTime - End time
 * @param period - Time period
 * @returns Array of time points
 */
function generateTimePoints(startTime: Date, endTime: Date, period: string): string[] {
  const timePoints: string[] = [];
  let interval: number;
  let format: string;

  // Determine interval and format based on period
  switch (period) {
    case '1h':
      interval = 5 * 60 * 1000; // 5 minutes
      format = 'HH:mm';
      break;
    case '6h':
      interval = 15 * 60 * 1000; // 15 minutes
      format = 'HH:mm';
      break;
    case '24h':
      interval = 60 * 60 * 1000; // 1 hour
      format = 'HH:mm';
      break;
    case '7d':
      interval = 6 * 60 * 60 * 1000; // 6 hours
      format = 'MM-DD HH:mm';
      break;
    case '30d':
      interval = 24 * 60 * 60 * 1000; // 1 day
      format = 'MM-DD';
      break;
    default:
      interval = 60 * 60 * 1000; // 1 hour
      format = 'HH:mm';
  }

  // Generate time points
  for (let time = startTime.getTime(); time <= endTime.getTime(); time += interval) {
    timePoints.push(new Date(time).toISOString());
  }

  return timePoints;
}

// Register routes
router.get('/usage', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getApiUsageMetrics);
router.get('/performance', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getApiPerformanceMetrics);
router.get('/rate-limiting', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getRateLimitingMetrics);

export default router;
