<template>
  <div class="chart-widget">
    <div v-if="!chartData" class="no-data">
      <v-icon size="48" color="grey lighten-1">mdi-chart-line</v-icon>
      <p class="mt-2 grey--text">No chart data available</p>
    </div>
    
    <div v-else class="chart-container">
      <!-- Line Chart -->
      <canvas v-if="settings.chartType === 'line'" ref="lineChart"></canvas>
      
      <!-- Bar Chart -->
      <canvas v-else-if="settings.chartType === 'bar'" ref="barChart"></canvas>
      
      <!-- Pie Chart -->
      <canvas v-else-if="settings.chartType === 'pie'" ref="pieChart"></canvas>
      
      <!-- Doughnut Chart -->
      <canvas v-else-if="settings.chartType === 'doughnut'" ref="doughnutChart"></canvas>
      
      <!-- Area Chart (Line chart with fill) -->
      <canvas v-else-if="settings.chartType === 'area'" ref="areaChart"></canvas>
      
      <!-- Default to Line Chart -->
      <canvas v-else ref="defaultChart"></canvas>
    </div>
    
    <div v-if="settings.showLegend && chartData && chartData.labels" class="chart-legend">
      <div v-for="(label, index) in chartData.labels" :key="index" class="legend-item">
        <div class="legend-color" :style="{ backgroundColor: getLegendColor(index) }"></div>
        <div class="legend-label">{{ label }}</div>
        <div v-if="chartData.datasets && chartData.datasets[0].data" class="legend-value">
          {{ chartData.datasets[0].data[index] }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'ChartWidget',
  
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    settings: {
      type: Object,
      default: () => ({
        chartType: 'line',
        showLegend: true,
        timeRange: 'month'
      })
    }
  },
  
  data() {
    return {
      chart: null,
      chartColors: [
        '#4caf50', '#2196f3', '#ff9800', '#f44336', 
        '#9c27b0', '#3f51b5', '#009688', '#ffeb3b'
      ]
    };
  },
  
  computed: {
    chartData() {
      if (!this.data.chartData) {
        return this.getDefaultChartData();
      }
      
      return this.data.chartData;
    }
  },
  
  watch: {
    chartData: {
      handler() {
        this.renderChart();
      },
      deep: true
    },
    'settings.chartType': function() {
      this.destroyChart();
      this.$nextTick(() => {
        this.renderChart();
      });
    }
  },
  
  mounted() {
    this.renderChart();
  },
  
  beforeDestroy() {
    this.destroyChart();
  },
  
  methods: {
    renderChart() {
      if (!this.chartData) return;
      
      this.$nextTick(() => {
        this.destroyChart();
        
        const chartType = this.settings.chartType || 'line';
        const refName = `${chartType}Chart`;
        const canvas = this.$refs[refName] || this.$refs.defaultChart;
        
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        
        // Configure chart options based on chart type
        const options = this.getChartOptions(chartType);
        
        // Create chart
        this.chart = new Chart(ctx, {
          type: chartType === 'area' ? 'line' : chartType,
          data: this.getChartData(chartType),
          options: options
        });
      });
    },
    
    destroyChart() {
      if (this.chart) {
        this.chart.destroy();
        this.chart = null;
      }
    },
    
    getChartData(chartType) {
      const data = { ...this.chartData };
      
      // For area charts, add fill option
      if (chartType === 'area' && data.datasets) {
        data.datasets = data.datasets.map(dataset => ({
          ...dataset,
          fill: true
        }));
      }
      
      return data;
    },
    
    getChartOptions(chartType) {
      const baseOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        }
      };
      
      // Add specific options based on chart type
      switch (chartType) {
        case 'line':
        case 'area':
          return {
            ...baseOptions,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          };
        case 'bar':
          return {
            ...baseOptions,
            scales: {
              y: {
                beginAtZero: true
              }
            }
          };
        case 'pie':
        case 'doughnut':
          return {
            ...baseOptions,
            cutout: chartType === 'doughnut' ? '70%' : 0
          };
        default:
          return baseOptions;
      }
    },
    
    getLegendColor(index) {
      if (this.chartData.datasets && this.chartData.datasets[0].backgroundColor) {
        const bgColor = this.chartData.datasets[0].backgroundColor;
        
        if (Array.isArray(bgColor)) {
          return bgColor[index % bgColor.length];
        }
        
        return bgColor;
      }
      
      return this.chartColors[index % this.chartColors.length];
    },
    
    getDefaultChartData() {
      // Default line chart data
      if (this.settings.chartType === 'line' || this.settings.chartType === 'area') {
        return {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
          datasets: [{
            label: 'Users',
            data: [65, 59, 80, 81, 56, 55, 40],
            borderColor: '#2196f3',
            backgroundColor: 'rgba(33, 150, 243, 0.1)',
            tension: 0.4
          }]
        };
      }
      
      // Default bar chart data
      if (this.settings.chartType === 'bar') {
        return {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
          datasets: [{
            label: 'Users',
            data: [65, 59, 80, 81, 56, 55, 40],
            backgroundColor: [
              'rgba(33, 150, 243, 0.7)',
              'rgba(33, 150, 243, 0.7)',
              'rgba(33, 150, 243, 0.7)',
              'rgba(33, 150, 243, 0.7)',
              'rgba(33, 150, 243, 0.7)',
              'rgba(33, 150, 243, 0.7)',
              'rgba(33, 150, 243, 0.7)'
            ]
          }]
        };
      }
      
      // Default pie/doughnut chart data
      if (this.settings.chartType === 'pie' || this.settings.chartType === 'doughnut') {
        return {
          labels: ['Admin', 'Vendor', 'Client', 'Guest'],
          datasets: [{
            data: [15, 35, 40, 10],
            backgroundColor: [
              '#f44336',
              '#4caf50',
              '#2196f3',
              '#ff9800'
            ]
          }]
        };
      }
      
      // Default fallback
      return {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
        datasets: [{
          label: 'Data',
          data: [65, 59, 80, 81, 56, 55, 40],
          borderColor: '#2196f3',
          backgroundColor: 'rgba(33, 150, 243, 0.1)'
        }]
      };
    }
  }
};
</script>

<style scoped>
.chart-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.chart-container {
  flex: 1;
  min-height: 200px;
  position: relative;
}

.chart-legend {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 4px;
}

.legend-label {
  font-size: 0.75rem;
  margin-right: 4px;
}

.legend-value {
  font-size: 0.75rem;
  font-weight: bold;
}
</style>
