<template>
  <wizard-step
    title="Showroom Configuration"
    description="Configure how your virtual showroom looks and functions for clients."
    :step-data="stepData"
    :validation-schema="validationSchema"
    :help-tips="helpTips"
    @update:step-data="updateStepData"
    @validate="validateStep"
  >
    <div class="showroom-config-form">
      <div class="form-section">
        <h3 class="section-title">Showroom Layout</h3>
        <p class="section-description">
          Choose a layout template for your virtual showroom.
        </p>
        
        <div class="layout-templates">
          <div 
            v-for="(template, index) in layoutTemplates" 
            :key="index"
            class="layout-template"
            :class="{ active: localStepData.layoutTemplateId === template.id }"
            @click="updateField('layoutTemplateId', template.id)"
          >
            <div class="template-preview">
              <img :src="template.preview" :alt="template.name" />
            </div>
            <div class="template-info">
              <div class="template-name">{{ template.name }}</div>
              <div class="template-description">{{ template.description }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Environment Settings</h3>
        
        <div class="form-group">
          <label for="environment-type">Environment Type *</label>
          <select
            id="environment-type"
            v-model="localStepData.environmentType"
            @change="updateField('environmentType', $event.target.value)"
          >
            <option value="">Select an environment</option>
            <option value="indoor">Indoor Showroom</option>
            <option value="outdoor">Outdoor Setting</option>
            <option value="studio">Studio Environment</option>
            <option value="abstract">Abstract Space</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="lighting-preset">Lighting Preset *</label>
          <select
            id="lighting-preset"
            v-model="localStepData.lightingPreset"
            @change="updateField('lightingPreset', $event.target.value)"
          >
            <option value="">Select a lighting preset</option>
            <option value="daylight">Daylight</option>
            <option value="evening">Evening</option>
            <option value="studio">Studio Lighting</option>
            <option value="dramatic">Dramatic</option>
            <option value="warm">Warm</option>
            <option value="cool">Cool</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="ambient-sound">Ambient Sound</label>
          <select
            id="ambient-sound"
            v-model="localStepData.ambientSound"
            @change="updateField('ambientSound', $event.target.value)"
          >
            <option value="">None</option>
            <option value="ambient_music">Ambient Music</option>
            <option value="nature">Nature Sounds</option>
            <option value="city">City Ambience</option>
            <option value="office">Office Environment</option>
          </select>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Interaction Settings</h3>
        
        <div class="form-group">
          <label for="navigation-type">Navigation Type *</label>
          <select
            id="navigation-type"
            v-model="localStepData.navigationType"
            @change="updateField('navigationType', $event.target.value)"
          >
            <option value="">Select navigation type</option>
            <option value="free">Free Movement</option>
            <option value="guided">Guided Tour</option>
            <option value="teleport">Teleport Points</option>
            <option value="hybrid">Hybrid Navigation</option>
          </select>
        </div>
        
        <div class="form-group">
          <label for="interaction-mode">Interaction Mode *</label>
          <select
            id="interaction-mode"
            v-model="localStepData.interactionMode"
            @change="updateField('interactionMode', $event.target.value)"
          >
            <option value="">Select interaction mode</option>
            <option value="click">Click to Interact</option>
            <option value="hover">Hover to Interact</option>
            <option value="proximity">Proximity Based</option>
          </select>
        </div>
        
        <div class="form-group">
          <label>Interactive Features</label>
          <div class="checkbox-group">
            <div class="checkbox-item">
              <input
                id="feature-product-info"
                type="checkbox"
                v-model="localStepData.features.productInfo"
                @change="updateFeature('productInfo', $event.target.checked)"
              />
              <label for="feature-product-info">Product Information</label>
            </div>
            
            <div class="checkbox-item">
              <input
                id="feature-product-customization"
                type="checkbox"
                v-model="localStepData.features.productCustomization"
                @change="updateFeature('productCustomization', $event.target.checked)"
              />
              <label for="feature-product-customization">Product Customization</label>
            </div>
            
            <div class="checkbox-item">
              <input
                id="feature-add-to-cart"
                type="checkbox"
                v-model="localStepData.features.addToCart"
                @change="updateFeature('addToCart', $event.target.checked)"
              />
              <label for="feature-add-to-cart">Add to Cart</label>
            </div>
            
            <div class="checkbox-item">
              <input
                id="feature-save-favorites"
                type="checkbox"
                v-model="localStepData.features.saveFavorites"
                @change="updateFeature('saveFavorites', $event.target.checked)"
              />
              <label for="feature-save-favorites">Save Favorites</label>
            </div>
            
            <div class="checkbox-item">
              <input
                id="feature-share"
                type="checkbox"
                v-model="localStepData.features.share"
                @change="updateFeature('share', $event.target.checked)"
              />
              <label for="feature-share">Share Showroom</label>
            </div>
            
            <div class="checkbox-item">
              <input
                id="feature-virtual-assistant"
                type="checkbox"
                v-model="localStepData.features.virtualAssistant"
                @change="updateFeature('virtualAssistant', $event.target.checked)"
              />
              <label for="feature-virtual-assistant">Virtual Assistant</label>
            </div>
          </div>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">Performance Settings</h3>
        
        <div class="form-group">
          <label for="quality-preset">Quality Preset *</label>
          <select
            id="quality-preset"
            v-model="localStepData.qualityPreset"
            @change="updateField('qualityPreset', $event.target.value)"
          >
            <option value="">Select quality preset</option>
            <option value="low">Low (Best Performance)</option>
            <option value="medium">Medium (Balanced)</option>
            <option value="high">High (Best Quality)</option>
            <option value="ultra">Ultra (Highest Quality)</option>
            <option value="auto">Auto (Device Based)</option>
          </select>
        </div>
        
        <div class="form-group">
          <label>Advanced Settings</label>
          <div class="checkbox-group">
            <div class="checkbox-item">
              <input
                id="setting-texture-compression"
                type="checkbox"
                v-model="localStepData.advancedSettings.textureCompression"
                @change="updateAdvancedSetting('textureCompression', $event.target.checked)"
              />
              <label for="setting-texture-compression">Texture Compression</label>
            </div>
            
            <div class="checkbox-item">
              <input
                id="setting-progressive-loading"
                type="checkbox"
                v-model="localStepData.advancedSettings.progressiveLoading"
                @change="updateAdvancedSetting('progressiveLoading', $event.target.checked)"
              />
              <label for="setting-progressive-loading">Progressive Loading</label>
            </div>
            
            <div class="checkbox-item">
              <input
                id="setting-occlusion-culling"
                type="checkbox"
                v-model="localStepData.advancedSettings.occlusionCulling"
                @change="updateAdvancedSetting('occlusionCulling', $event.target.checked)"
              />
              <label for="setting-occlusion-culling">Occlusion Culling</label>
            </div>
            
            <div class="checkbox-item">
              <input
                id="setting-lod"
                type="checkbox"
                v-model="localStepData.advancedSettings.lod"
                @change="updateAdvancedSetting('lod', $event.target.checked)"
              />
              <label for="setting-lod">Level of Detail (LOD)</label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </wizard-step>
</template>

<script>
import WizardStep from '../WizardStep.vue';

export default {
  name: 'ShowroomConfigStep',
  
  components: {
    WizardStep
  },
  
  props: {
    stepData: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      localStepData: {
        layoutTemplateId: '',
        environmentType: '',
        lightingPreset: '',
        ambientSound: '',
        navigationType: '',
        interactionMode: '',
        features: {
          productInfo: true,
          productCustomization: false,
          addToCart: true,
          saveFavorites: true,
          share: true,
          virtualAssistant: false
        },
        qualityPreset: '',
        advancedSettings: {
          textureCompression: true,
          progressiveLoading: true,
          occlusionCulling: true,
          lod: true
        },
        ...this.stepData
      },
      validationSchema: {
        layoutTemplateId: {
          required: true,
          label: 'Layout Template'
        },
        environmentType: {
          required: true,
          label: 'Environment Type'
        },
        lightingPreset: {
          required: true,
          label: 'Lighting Preset'
        },
        navigationType: {
          required: true,
          label: 'Navigation Type'
        },
        interactionMode: {
          required: true,
          label: 'Interaction Mode'
        },
        qualityPreset: {
          required: true,
          label: 'Quality Preset'
        }
      },
      helpTips: [
        {
          title: 'Layout Templates',
          text: 'Choose a layout that best showcases your products. You can customize the layout further after setup.'
        },
        {
          title: 'Performance Settings',
          text: 'Higher quality settings provide better visuals but may reduce performance on lower-end devices. The "Auto" setting will adjust based on the client\'s device capabilities.'
        }
      ],
      layoutTemplates: [
        {
          id: 'template_1',
          name: 'Modern Showroom',
          description: 'A clean, modern showroom with ample space for product display.',
          preview: '/assets/templates/modern-showroom.jpg'
        },
        {
          id: 'template_2',
          name: 'Retail Store',
          description: 'Simulates a retail store environment with shelves and display areas.',
          preview: '/assets/templates/retail-store.jpg'
        },
        {
          id: 'template_3',
          name: 'Gallery Space',
          description: 'An art gallery inspired space with focused lighting on products.',
          preview: '/assets/templates/gallery-space.jpg'
        },
        {
          id: 'template_4',
          name: 'Home Environment',
          description: 'Products displayed in a home-like environment for context.',
          preview: '/assets/templates/home-environment.jpg'
        }
      ]
    };
  },
  
  methods: {
    updateStepData(data) {
      this.localStepData = { ...data };
      this.$emit('update:step-data', this.localStepData);
    },
    
    updateField(field, value) {
      this.localStepData[field] = value;
      this.$emit('update:step-data', this.localStepData);
    },
    
    updateFeature(feature, value) {
      this.localStepData.features[feature] = value;
      this.$emit('update:step-data', this.localStepData);
    },
    
    updateAdvancedSetting(setting, value) {
      this.localStepData.advancedSettings[setting] = value;
      this.$emit('update:step-data', this.localStepData);
    },
    
    validateStep(isValid) {
      this.$emit('validate', isValid);
    }
  }
};
</script>

<style scoped>
.showroom-config-form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--theme--foreground);
}

.section-description {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin: 0 0 16px 0;
}

.layout-templates {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.layout-template {
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--theme--background);
  cursor: pointer;
  transition: all 0.2s ease;
}

.layout-template:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.layout-template.active {
  border-color: var(--theme--primary);
}

.template-preview {
  height: 120px;
  overflow: hidden;
}

.template-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.template-info {
  padding: 12px;
}

.template-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.template-description {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.form-group {
  margin-bottom: 16px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--theme--foreground);
}

.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group select:focus {
  border-color: var(--theme--primary);
  outline: none;
}

.checkbox-group {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.checkbox-item label {
  margin: 0;
  font-size: 14px;
  font-weight: normal;
  cursor: pointer;
}
</style>
