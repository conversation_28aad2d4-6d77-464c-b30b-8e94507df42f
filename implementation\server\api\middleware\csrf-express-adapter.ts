/**
 * CSRF Express Adapter
 *
 * This adapter allows the iron-session based CSRF protection to be used
 * with Express applications. It provides a drop-in replacement for the
 * deprecated csurf middleware.
 */

import { Request, Response, NextFunction } from 'express';
import { getIronSession, IronSessionOptions } from 'iron-session';
import crypto from 'crypto';
import { logger } from '../../lib/logger';

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32;

// Iron session configuration
const ironOptions: IronSessionOptions = {
  cookieName: 'mvs_session',
  password: process.env.SESSION_SECRET || 'complex_password_at_least_32_characters_long',
  cookieOptions: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    sameSite: 'strict',
    path: '/',
  },
};

// Interface for session with CSRF token
interface SessionData {
  csrfToken?: string;
  userId?: string;
}

/**
 * Generate a CSRF token
 * @returns {string} CSRF token
 */
export function generateCsrfToken(): string {
  return crypto.randomBytes(CSRF_TOKEN_LENGTH).toString('hex');
}

/**
 * Get or create a CSRF token in the session
 * @param req - Express request
 * @param res - Express response
 * @returns {Promise<string>} CSRF token
 */
export async function getOrCreateCsrfToken(req: Request, res: Response): Promise<string> {
  const session = await getIronSession<SessionData>(req, res, ironOptions);

  // Create a new token if one doesn't exist
  if (!session.csrfToken) {
    session.csrfToken = generateCsrfToken();
    await session.save();
  }

  return session.csrfToken;
}

/**
 * Validate a CSRF token
 * @param req - Express request
 * @param res - Express response
 * @param token - Token to validate
 * @returns {Promise<boolean>} Whether the token is valid
 */
export async function validateCsrfToken(
  req: Request,
  res: Response,
  token: string,
): Promise<boolean> {
  const session = await getIronSession<SessionData>(req, res, ironOptions);
  return session.csrfToken === token;
}

/**
 * CSRF protection middleware for Express
 * @param options - Options
 * @returns {Function} Express middleware
 */
export function csrfProtection(
  options: {
    headerName?: string;
    cookieName?: string;
    methods?: string[];
    ignorePaths?: string[];
  } = {},
) {
  const {
    headerName = 'X-CSRF-Token',
    methods = ['POST', 'PUT', 'DELETE', 'PATCH'],
    ignorePaths = [],
  } = options;

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Skip CSRF check for non-mutating methods
      if (!methods.includes(req.method)) {
        return next();
      }

      // Skip CSRF check for ignored paths
      if (ignorePaths.some(path => req.path.startsWith(path))) {
        return next();
      }

      // Get CSRF token from request
      const token = (req.headers[headerName.toLowerCase()] as string) || req.body?._csrf;

      // If no token, reject the request
      if (!token) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'CSRF_TOKEN_MISSING',
            message: 'CSRF token is missing',
          },
        });
      }

      // Check if token is valid
      const isValid = await validateCsrfToken(req, res, token);

      if (!isValid) {
        return res.status(403).json({
          success: false,
          error: {
            code: 'CSRF_TOKEN_INVALID',
            message: 'CSRF token is invalid or expired',
          },
        });
      }

      // Token is valid, continue
      next();
    } catch (error) {
      logger.error('Error in CSRF protection middleware:', error);
      next(error);
    }
  };
}

/**
 * CSRF token generator middleware for Express
 * @param options - Options
 * @returns {Function} Express middleware
 */
export function csrfTokenGenerator(
  options: {
    cookieName?: string;
  } = {},
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // Generate a new CSRF token
      const token = await getOrCreateCsrfToken(req, res);

      // Add token to response locals for templates
      res.locals.csrfToken = token;

      next();
    } catch (error) {
      logger.error('Error in CSRF token generator middleware:', error);
      next(error);
    }
  };
}

export default {
  csrfProtection,
  csrfTokenGenerator,
  generateCsrfToken,
  getOrCreateCsrfToken,
  validateCsrfToken,
};
