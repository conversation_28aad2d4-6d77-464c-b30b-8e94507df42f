<template>
  <div class="preview-controls">
    <div class="preview-mode-selector">
      <button
        v-for="mode in previewModes"
        :key="mode.id"
        class="mode-button"
        :class="{ active: previewMode === mode.id }"
        @click="setPreviewMode(mode.id)"
        :title="mode.name"
      >
        <i class="material-icons">{{ mode.icon }}</i>
        <span>{{ mode.name }}</span>
      </button>
    </div>
    
    <div class="preview-actions">
      <button
        class="action-button"
        @click="refreshPreview"
        :disabled="previewLoading"
        title="Refresh preview"
      >
        <i class="material-icons">refresh</i>
        <span>Refresh</span>
      </button>
      
      <button
        class="action-button"
        @click="resetPreview"
        title="Reset preview"
      >
        <i class="material-icons">restart_alt</i>
        <span>Reset</span>
      </button>
      
      <button
        class="action-button"
        @click="toggleFullscreen"
        title="Toggle fullscreen"
      >
        <i class="material-icons">{{ isFullscreen ? 'fullscreen_exit' : 'fullscreen' }}</i>
        <span>{{ isFullscreen ? 'Exit Fullscreen' : 'Fullscreen' }}</span>
      </button>
    </div>
    
    <div class="preview-keyboard-shortcuts">
      <button
        class="shortcuts-button"
        @click="toggleShortcutsPanel"
        title="Keyboard shortcuts"
      >
        <i class="material-icons">keyboard</i>
        <span>Shortcuts</span>
      </button>
    </div>
    
    <!-- Keyboard Shortcuts Panel -->
    <div v-if="showShortcutsPanel" class="shortcuts-panel">
      <div class="shortcuts-panel-header">
        <h3>Keyboard Shortcuts</h3>
        <button @click="toggleShortcutsPanel" class="close-button">
          <i class="material-icons">close</i>
        </button>
      </div>
      
      <div class="shortcuts-panel-content">
        <div v-for="(shortcut, index) in keyboardShortcuts" :key="index" class="shortcut-item">
          <div class="shortcut-keys">
            <span v-for="(key, keyIndex) in shortcut.keys" :key="keyIndex" class="key">
              {{ key }}
            </span>
          </div>
          <div class="shortcut-description">{{ shortcut.description }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { usePreviewContext } from '../contexts/PreviewContext';

export default {
  name: 'PreviewControls',
  
  setup() {
    // Get preview context
    const {
      previewMode,
      previewLoading,
      setPreviewMode,
      refreshPreview,
      resetPreview
    } = usePreviewContext();
    
    // Fullscreen state
    const isFullscreen = ref(false);
    
    // Shortcuts panel state
    const showShortcutsPanel = ref(false);
    
    // Preview modes
    const previewModes = [
      { id: 'edit', name: 'Edit', icon: 'edit' },
      { id: 'preview', name: 'Preview', icon: 'visibility' },
      { id: 'split', name: 'Split', icon: 'vertical_split' }
    ];
    
    // Keyboard shortcuts
    const keyboardShortcuts = [
      { keys: ['Ctrl', 'E'], description: 'Toggle between Edit and Preview modes' },
      { keys: ['Ctrl', 'S'], description: 'Toggle Split mode' },
      { keys: ['Ctrl', 'R'], description: 'Refresh preview' },
      { keys: ['Ctrl', 'Z'], description: 'Reset preview' },
      { keys: ['F11'], description: 'Toggle fullscreen' },
      { keys: ['Esc'], description: 'Exit fullscreen or close panels' }
    ];
    
    // Toggle fullscreen
    const toggleFullscreen = () => {
      if (!isFullscreen.value) {
        // Enter fullscreen
        const element = document.documentElement;
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen();
        }
      } else {
        // Exit fullscreen
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      }
    };
    
    // Toggle shortcuts panel
    const toggleShortcutsPanel = () => {
      showShortcutsPanel.value = !showShortcutsPanel.value;
    };
    
    // Handle keyboard shortcuts
    const handleKeyDown = (event) => {
      // Ctrl+E: Toggle between Edit and Preview modes
      if (event.ctrlKey && event.key === 'e') {
        event.preventDefault();
        setPreviewMode(previewMode.value === 'edit' ? 'preview' : 'edit');
      }
      
      // Ctrl+S: Toggle Split mode
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault();
        setPreviewMode(previewMode.value === 'split' ? 'edit' : 'split');
      }
      
      // Ctrl+R: Refresh preview
      if (event.ctrlKey && event.key === 'r') {
        event.preventDefault();
        refreshPreview();
      }
      
      // Ctrl+Z: Reset preview
      if (event.ctrlKey && event.key === 'z') {
        event.preventDefault();
        resetPreview();
      }
      
      // F11: Toggle fullscreen
      if (event.key === 'F11') {
        event.preventDefault();
        toggleFullscreen();
      }
      
      // Esc: Exit fullscreen or close panels
      if (event.key === 'Escape') {
        if (showShortcutsPanel.value) {
          showShortcutsPanel.value = false;
        } else if (isFullscreen.value) {
          toggleFullscreen();
        }
      }
    };
    
    // Handle fullscreen change
    const handleFullscreenChange = () => {
      isFullscreen.value = !!document.fullscreenElement || 
                           !!document.mozFullScreenElement || 
                           !!document.webkitFullscreenElement || 
                           !!document.msFullscreenElement;
    };
    
    // Lifecycle hooks
    onMounted(() => {
      // Add event listeners
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      document.addEventListener('mozfullscreenchange', handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.addEventListener('msfullscreenchange', handleFullscreenChange);
    });
    
    onBeforeUnmount(() => {
      // Remove event listeners
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    });
    
    return {
      previewMode,
      previewLoading,
      previewModes,
      isFullscreen,
      showShortcutsPanel,
      keyboardShortcuts,
      setPreviewMode,
      refreshPreview,
      resetPreview,
      toggleFullscreen,
      toggleShortcutsPanel
    };
  }
};
</script>

<style scoped>
.preview-controls {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  position: relative;
}

.preview-mode-selector {
  display: flex;
  gap: 4px;
}

.preview-actions {
  display: flex;
  gap: 4px;
  margin-left: auto;
}

.preview-keyboard-shortcuts {
  margin-left: 8px;
}

.mode-button,
.action-button,
.shortcuts-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.mode-button:hover,
.action-button:hover,
.shortcuts-button:hover {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
}

.mode-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border-color: var(--theme--primary);
}

.mode-button i,
.action-button i,
.shortcuts-button i {
  font-size: 18px;
}

/* Shortcuts Panel */
.shortcuts-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 400px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.shortcuts-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.shortcuts-panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
}

.shortcuts-panel-content {
  padding: 12px 16px;
  max-height: 300px;
  overflow-y: auto;
}

.shortcut-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--theme--border-color-subdued);
}

.shortcut-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.shortcut-keys {
  display: flex;
  gap: 4px;
  margin-right: 16px;
  min-width: 100px;
}

.key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 6px;
  background-color: var(--theme--background-subdued);
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.shortcut-description {
  flex: 1;
  font-size: 14px;
}
</style>
