-- Migration: Enhance Blueprint Management
-- Description: This migration adds tables and functions for enhanced blueprint management

-- Create blueprints table if it doesn't exist
CREATE TABLE IF NOT EXISTS blueprints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL,
  project_id UUID,
  name TEXT NOT NULL,
  description TEXT,
  version TEXT NOT NULL,
  is_published BOOLEAN NOT NULL DEFAULT FALSE,
  is_template BOOLEAN NOT NULL DEFAULT FALSE,
  thumbnail_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_blueprints_vendor
    FOREIGN KEY (vendor_id)
    REFERENCES vendors(id)
    ON DELETE CASCADE
);

-- Create blueprint_contents table
CREATE TABLE IF NOT EXISTS blueprint_contents (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blueprint_id UUID NOT NULL,
  url TEXT,
  data JSONB,
  hash TEXT NOT NULL,
  size INTEGER NOT NULL DEFAULT 0,
  content_type TEXT NOT NULL DEFAULT 'application/json',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_blueprint_contents_blueprint
    FOREIGN KEY (blueprint_id)
    REFERENCES blueprints(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_blueprint_content
    UNIQUE (blueprint_id)
);

-- Create blueprint_dependencies table
CREATE TABLE IF NOT EXISTS blueprint_dependencies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blueprint_id UUID NOT NULL,
  dependency_id UUID NOT NULL,
  dependency_type TEXT NOT NULL,
  is_required BOOLEAN NOT NULL DEFAULT TRUE,
  metadata JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_blueprint_dependencies_blueprint
    FOREIGN KEY (blueprint_id)
    REFERENCES blueprints(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_blueprint_dependency
    UNIQUE (blueprint_id, dependency_id)
);

-- Create blueprint_versions table to track version history
CREATE TABLE IF NOT EXISTS blueprint_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blueprint_id UUID NOT NULL,
  version TEXT NOT NULL,
  data JSONB NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_blueprint_versions_blueprint
    FOREIGN KEY (blueprint_id)
    REFERENCES blueprints(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_blueprint_version
    UNIQUE (blueprint_id, version)
);

-- Create blueprint_version_dependencies table
CREATE TABLE IF NOT EXISTS blueprint_version_dependencies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blueprint_id UUID NOT NULL,
  version TEXT NOT NULL,
  dependency_id UUID NOT NULL,
  dependency_type TEXT NOT NULL,
  is_required BOOLEAN NOT NULL DEFAULT TRUE,
  metadata JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_blueprint_version_dependencies_blueprint
    FOREIGN KEY (blueprint_id)
    REFERENCES blueprints(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_blueprint_version_dependency
    UNIQUE (blueprint_id, version, dependency_id)
);

-- Create blueprint_validations table
CREATE TABLE IF NOT EXISTS blueprint_validations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blueprint_id UUID NOT NULL,
  version TEXT NOT NULL,
  is_valid BOOLEAN NOT NULL,
  errors JSONB NOT NULL DEFAULT '[]',
  warnings JSONB NOT NULL DEFAULT '[]',
  missing_dependencies JSONB,
  performance_impact JSONB,
  validated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_blueprint_validations_blueprint
    FOREIGN KEY (blueprint_id)
    REFERENCES blueprints(id)
    ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_blueprints_vendor_id ON blueprints(vendor_id);
CREATE INDEX idx_blueprints_project_id ON blueprints(project_id);
CREATE INDEX idx_blueprints_is_published ON blueprints(is_published);
CREATE INDEX idx_blueprints_is_template ON blueprints(is_template);
CREATE INDEX idx_blueprint_contents_blueprint_id ON blueprint_contents(blueprint_id);
CREATE INDEX idx_blueprint_dependencies_blueprint_id ON blueprint_dependencies(blueprint_id);
CREATE INDEX idx_blueprint_dependencies_dependency_id ON blueprint_dependencies(dependency_id);
CREATE INDEX idx_blueprint_versions_blueprint_id ON blueprint_versions(blueprint_id);
CREATE INDEX idx_blueprint_version_dependencies_blueprint_id ON blueprint_version_dependencies(blueprint_id);
CREATE INDEX idx_blueprint_version_dependencies_version ON blueprint_version_dependencies(blueprint_id, version);
CREATE INDEX idx_blueprint_validations_blueprint_id ON blueprint_validations(blueprint_id);

-- Create function to store blueprint version history
CREATE OR REPLACE FUNCTION store_blueprint_version_history()
RETURNS TRIGGER AS $$
BEGIN
  -- Only store history if version changed
  IF OLD.version IS DISTINCT FROM NEW.version THEN
    -- Get blueprint content
    DECLARE
      v_content JSONB;
      v_hash TEXT;
      v_size INTEGER;
      v_content_type TEXT;
    BEGIN
      SELECT data, hash, size, content_type INTO v_content, v_hash, v_size, v_content_type
      FROM blueprint_contents
      WHERE blueprint_id = OLD.id;
      
      -- Store the old version in history
      INSERT INTO blueprint_versions (
        blueprint_id,
        version,
        data,
        created_at
      ) VALUES (
        OLD.id,
        OLD.version,
        jsonb_build_object(
          'id', OLD.id,
          'vendor_id', OLD.vendor_id,
          'project_id', OLD.project_id,
          'name', OLD.name,
          'description', OLD.description,
          'is_published', OLD.is_published,
          'is_template', OLD.is_template,
          'thumbnail_url', OLD.thumbnail_url,
          'metadata', OLD.metadata,
          'content', v_content,
          'content_hash', v_hash,
          'content_size', v_size,
          'content_type', v_content_type,
          'created_at', OLD.created_at,
          'updated_at', OLD.updated_at
        ),
        OLD.updated_at
      );
      
      -- Store dependencies for this version
      INSERT INTO blueprint_version_dependencies (
        blueprint_id,
        version,
        dependency_id,
        dependency_type,
        is_required,
        metadata,
        created_at
      )
      SELECT
        blueprint_id,
        OLD.version,
        dependency_id,
        dependency_type,
        is_required,
        metadata,
        created_at
      FROM blueprint_dependencies
      WHERE blueprint_id = OLD.id;
    END;
  END IF;
  
  -- Update the updated_at timestamp
  NEW.updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to store version history
CREATE TRIGGER store_blueprint_version_history_trigger
BEFORE UPDATE OF version ON blueprints
FOR EACH ROW
EXECUTE FUNCTION store_blueprint_version_history();

-- Create function to get blueprint with content and dependencies
CREATE OR REPLACE FUNCTION get_blueprint_with_details(p_blueprint_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_blueprint JSONB;
  v_content JSONB;
  v_dependencies JSONB;
BEGIN
  -- Get blueprint
  SELECT jsonb_build_object(
    'id', b.id,
    'vendor_id', b.vendor_id,
    'project_id', b.project_id,
    'name', b.name,
    'description', b.description,
    'version', b.version,
    'is_published', b.is_published,
    'is_template', b.is_template,
    'thumbnail_url', b.thumbnail_url,
    'metadata', b.metadata,
    'created_at', b.created_at,
    'updated_at', b.updated_at
  ) INTO v_blueprint
  FROM blueprints b
  WHERE b.id = p_blueprint_id;
  
  -- Get content
  SELECT jsonb_build_object(
    'url', c.url,
    'data', c.data,
    'hash', c.hash,
    'size', c.size,
    'content_type', c.content_type
  ) INTO v_content
  FROM blueprint_contents c
  WHERE c.blueprint_id = p_blueprint_id;
  
  -- Get dependencies
  SELECT jsonb_agg(
    jsonb_build_object(
      'id', d.dependency_id,
      'type', d.dependency_type,
      'is_required', d.is_required,
      'metadata', d.metadata
    )
  ) INTO v_dependencies
  FROM blueprint_dependencies d
  WHERE d.blueprint_id = p_blueprint_id;
  
  -- Combine blueprint, content, and dependencies
  v_blueprint = jsonb_set(v_blueprint, '{content}', COALESCE(v_content, '{}'::jsonb));
  v_blueprint = jsonb_set(v_blueprint, '{dependencies}', COALESCE(v_dependencies, '[]'::jsonb));
  
  RETURN v_blueprint;
END;
$$;

-- Add RLS policies for blueprints
ALTER TABLE blueprints ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see blueprints they have access to
CREATE POLICY blueprints_select_policy
  ON blueprints
  FOR SELECT
  USING (
    vendor_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM vendors v
      WHERE v.id = vendor_id AND v.user_id = auth.uid()
    ) OR
    auth.jwt() ->> 'role' = 'admin'
  );

-- Add RLS policies for blueprint_contents
ALTER TABLE blueprint_contents ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see blueprint contents they have access to
CREATE POLICY blueprint_contents_select_policy
  ON blueprint_contents
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM blueprints b
      WHERE b.id = blueprint_id AND (
        b.vendor_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM vendors v
          WHERE v.id = b.vendor_id AND v.user_id = auth.uid()
        ) OR
        auth.jwt() ->> 'role' = 'admin'
      )
    )
  );
