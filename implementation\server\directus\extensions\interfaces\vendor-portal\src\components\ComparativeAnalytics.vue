<template>
  <div class="comparative-analytics">
    <div class="analytics-header">
      <h3>Comparative Analytics</h3>
      <div class="analytics-controls">
        <div class="comparison-type-selector">
          <label>Comparison Type:</label>
          <select v-model="comparisonType" @change="resetSelection">
            <option value="showrooms">Compare Showrooms</option>
            <option value="products">Compare Products</option>
            <option value="time-periods">Compare Time Periods</option>
          </select>
        </div>
        <button class="btn btn-secondary" @click="exportComparison">
          <i class="material-icons">download</i> Export
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
      <span>Loading comparison data...</span>
    </div>

    <div v-else class="analytics-content">
      <!-- Showroom Comparison -->
      <div v-if="comparisonType === 'showrooms'" class="comparison-section">
        <div class="selection-panel">
          <h4>Select Showrooms to Compare</h4>
          <div class="showroom-selector">
            <div v-for="showroom in availableShowrooms" :key="showroom.id" class="selector-item">
              <input
                type="checkbox"
                :id="'showroom-' + showroom.id"
                :value="showroom.id"
                v-model="selectedShowrooms"
                @change="updateComparison"
              >
              <label :for="'showroom-' + showroom.id">{{ showroom.name }}</label>
            </div>
          </div>
          <div class="metrics-selector">
            <h5>Select Metrics</h5>
            <div v-for="metric in showroomMetrics" :key="metric.id" class="selector-item">
              <input
                type="checkbox"
                :id="'metric-' + metric.id"
                :value="metric.id"
                v-model="selectedMetrics"
                @change="updateComparison"
              >
              <label :for="'metric-' + metric.id">{{ metric.name }}</label>
            </div>
          </div>
        </div>

        <div class="comparison-results">
          <div v-if="!comparisonData" class="no-data-message">
            Select at least two showrooms and one metric to compare
          </div>
          <div v-else class="comparison-charts">
            <div v-for="metric in selectedMetrics" :key="metric" class="comparison-chart">
              <h5>{{ getMetricName(metric) }}</h5>
              <canvas :ref="'chart-' + metric"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Comparison -->
      <div v-if="comparisonType === 'products'" class="comparison-section">
        <div class="selection-panel">
          <h4>Select Products to Compare</h4>
          <div class="product-selector">
            <div v-for="product in availableProducts" :key="product.id" class="selector-item">
              <input
                type="checkbox"
                :id="'product-' + product.id"
                :value="product.id"
                v-model="selectedProducts"
                @change="updateComparison"
              >
              <label :for="'product-' + product.id">{{ product.name }}</label>
            </div>
          </div>
          <div class="metrics-selector">
            <h5>Select Metrics</h5>
            <div v-for="metric in productMetrics" :key="metric.id" class="selector-item">
              <input
                type="checkbox"
                :id="'metric-' + metric.id"
                :value="metric.id"
                v-model="selectedMetrics"
                @change="updateComparison"
              >
              <label :for="'metric-' + metric.id">{{ metric.name }}</label>
            </div>
          </div>
        </div>

        <div class="comparison-results">
          <div v-if="!comparisonData" class="no-data-message">
            Select at least two products and one metric to compare
          </div>
          <div v-else class="comparison-charts">
            <div v-for="metric in selectedMetrics" :key="metric" class="comparison-chart">
              <h5>{{ getMetricName(metric) }}</h5>
              <canvas :ref="'chart-' + metric"></canvas>
            </div>
          </div>
        </div>
      </div>

      <!-- Time Period Comparison -->
      <div v-if="comparisonType === 'time-periods'" class="comparison-section">
        <div class="selection-panel">
          <h4>Select Time Periods to Compare</h4>
          
          <div class="period-selector">
            <h5>Period 1</h5>
            <div class="date-input">
              <label>From:</label>
              <input type="date" v-model="period1Start" @change="updateComparison">
            </div>
            <div class="date-input">
              <label>To:</label>
              <input type="date" v-model="period1End" @change="updateComparison">
            </div>
          </div>
          
          <div class="period-selector">
            <h5>Period 2</h5>
            <div class="date-input">
              <label>From:</label>
              <input type="date" v-model="period2Start" @change="updateComparison">
            </div>
            <div class="date-input">
              <label>To:</label>
              <input type="date" v-model="period2End" @change="updateComparison">
            </div>
          </div>
          
          <div class="showroom-selector">
            <h5>Select Showroom (Optional)</h5>
            <select v-model="selectedShowroomForTimePeriod" @change="updateComparison">
              <option value="">All Showrooms</option>
              <option v-for="showroom in availableShowrooms" :key="showroom.id" :value="showroom.id">
                {{ showroom.name }}
              </option>
            </select>
          </div>
          
          <div class="metrics-selector">
            <h5>Select Metrics</h5>
            <div v-for="metric in showroomMetrics" :key="metric.id" class="selector-item">
              <input
                type="checkbox"
                :id="'time-metric-' + metric.id"
                :value="metric.id"
                v-model="selectedMetrics"
                @change="updateComparison"
              >
              <label :for="'time-metric-' + metric.id">{{ metric.name }}</label>
            </div>
          </div>
        </div>

        <div class="comparison-results">
          <div v-if="!comparisonData" class="no-data-message">
            Select valid date ranges and at least one metric to compare
          </div>
          <div v-else class="comparison-charts">
            <div v-for="metric in selectedMetrics" :key="metric" class="comparison-chart">
              <h5>{{ getMetricName(metric) }}</h5>
              <div class="period-comparison">
                <div class="period-bar">
                  <div class="period-label">Period 1</div>
                  <div class="period-value">{{ formatMetricValue(comparisonData.comparison[metric].period1Value, metric) }}</div>
                </div>
                <div class="period-bar">
                  <div class="period-label">Period 2</div>
                  <div class="period-value">{{ formatMetricValue(comparisonData.comparison[metric].period2Value, metric) }}</div>
                </div>
                <div class="period-change" :class="getChangeClass(comparisonData.comparison[metric].percentChange)">
                  <i class="material-icons">{{ getChangeIcon(comparisonData.comparison[metric].percentChange) }}</i>
                  <span>{{ formatChange(comparisonData.comparison[metric].percentChange) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComparativeAnalytics',

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      comparisonType: 'showrooms',
      comparisonData: null,
      
      // Showroom comparison
      availableShowrooms: [],
      selectedShowrooms: [],
      
      // Product comparison
      availableProducts: [],
      selectedProducts: [],
      
      // Time period comparison
      period1Start: '',
      period1End: '',
      period2Start: '',
      period2End: '',
      selectedShowroomForTimePeriod: '',
      
      // Metrics
      selectedMetrics: [],
      showroomMetrics: [
        { id: 'visits', name: 'Total Visits' },
        { id: 'unique_visitors', name: 'Unique Visitors' },
        { id: 'avg_duration', name: 'Average Duration' },
        { id: 'bounce_rate', name: 'Bounce Rate' },
        { id: 'interactions', name: 'Interactions' }
      ],
      productMetrics: [
        { id: 'views', name: 'Views' },
        { id: 'interactions', name: 'Interactions' },
        { id: 'avg_duration', name: 'Average Duration' },
        { id: 'conversion_rate', name: 'Conversion Rate' }
      ]
    };
  },

  mounted() {
    // Set default date ranges for time period comparison
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(today.getDate() - 60);
    
    this.period1Start = sixtyDaysAgo.toISOString().split('T')[0];
    this.period1End = thirtyDaysAgo.toISOString().split('T')[0];
    this.period2Start = thirtyDaysAgo.toISOString().split('T')[0];
    this.period2End = today.toISOString().split('T')[0];
    
    // Load initial data
    this.loadInitialData();
  },

  methods: {
    // Load initial data
    async loadInitialData() {
      this.loading = true;
      
      try {
        // Load showrooms
        const showroomsResponse = await fetch(`/api/showrooms?vendor_id=${this.vendorId}`);
        const showroomsData = await showroomsResponse.json();
        
        if (showroomsData.success) {
          this.availableShowrooms = showroomsData.data;
        }
        
        // Load products
        const productsResponse = await fetch(`/api/products?vendor_id=${this.vendorId}`);
        const productsData = await productsResponse.json();
        
        if (productsData.success) {
          this.availableProducts = productsData.data;
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        this.loading = false;
      }
    },
    
    // Reset selection when comparison type changes
    resetSelection() {
      this.selectedShowrooms = [];
      this.selectedProducts = [];
      this.selectedMetrics = [];
      this.comparisonData = null;
    },
    
    // Update comparison data
    async updateComparison() {
      if (this.comparisonType === 'showrooms' && (this.selectedShowrooms.length < 2 || this.selectedMetrics.length === 0)) {
        this.comparisonData = null;
        return;
      }
      
      if (this.comparisonType === 'products' && (this.selectedProducts.length < 2 || this.selectedMetrics.length === 0)) {
        this.comparisonData = null;
        return;
      }
      
      if (this.comparisonType === 'time-periods' && (!this.period1Start || !this.period1End || !this.period2Start || !this.period2End || this.selectedMetrics.length === 0)) {
        this.comparisonData = null;
        return;
      }
      
      this.loading = true;
      
      try {
        let url = '';
        
        if (this.comparisonType === 'showrooms') {
          url = `/api/analytics/comparative/showrooms?vendor_id=${this.vendorId}&showroom_ids=${this.selectedShowrooms.join(',')}&metrics=${this.selectedMetrics.join(',')}`;
        } else if (this.comparisonType === 'products') {
          url = `/api/analytics/comparative/products?vendor_id=${this.vendorId}&product_ids=${this.selectedProducts.join(',')}&metrics=${this.selectedMetrics.join(',')}`;
        } else if (this.comparisonType === 'time-periods') {
          url = `/api/analytics/comparative/time-periods?vendor_id=${this.vendorId}&metrics=${this.selectedMetrics.join(',')}&period_1_start=${this.period1Start}&period_1_end=${this.period1End}&period_2_start=${this.period2Start}&period_2_end=${this.period2End}`;
          
          if (this.selectedShowroomForTimePeriod) {
            url += `&showroom_id=${this.selectedShowroomForTimePeriod}`;
          }
        }
        
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.success) {
          this.comparisonData = data.data;
          this.$nextTick(() => {
            this.renderCharts();
          });
        } else {
          console.error('Error loading comparison data:', data.error);
          this.comparisonData = null;
        }
      } catch (error) {
        console.error('Error updating comparison:', error);
        this.comparisonData = null;
      } finally {
        this.loading = false;
      }
    },
    
    // Render comparison charts
    renderCharts() {
      if (!this.comparisonData) return;
      
      if (this.comparisonType === 'showrooms' || this.comparisonType === 'products') {
        this.selectedMetrics.forEach(metric => {
          const chartRef = this.$refs[`chart-${metric}`];
          if (!chartRef || !chartRef[0]) return;
          
          const ctx = chartRef[0].getContext('2d');
          
          // Create chart data
          const labels = this.comparisonType === 'showrooms' 
            ? this.selectedShowrooms.map(id => this.getShowroomName(id))
            : this.selectedProducts.map(id => this.getProductName(id));
          
          const data = this.comparisonType === 'showrooms'
            ? this.selectedShowrooms.map(id => this.comparisonData[id]?.[metric] || 0)
            : this.selectedProducts.map(id => this.comparisonData[id]?.[metric] || 0);
          
          // Create chart
          new Chart(ctx, {
            type: 'bar',
            data: {
              labels,
              datasets: [{
                label: this.getMetricName(metric),
                data,
                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
              }]
            },
            options: {
              responsive: true,
              scales: {
                y: {
                  beginAtZero: true
                }
              }
            }
          });
        });
      }
    },
    
    // Get showroom name by ID
    getShowroomName(id) {
      const showroom = this.availableShowrooms.find(s => s.id === id);
      return showroom ? showroom.name : id;
    },
    
    // Get product name by ID
    getProductName(id) {
      const product = this.availableProducts.find(p => p.id === id);
      return product ? product.name : id;
    },
    
    // Get metric name by ID
    getMetricName(metricId) {
      const allMetrics = [...this.showroomMetrics, ...this.productMetrics];
      const metric = allMetrics.find(m => m.id === metricId);
      return metric ? metric.name : metricId;
    },
    
    // Format metric value based on metric type
    formatMetricValue(value, metricId) {
      if (!value) return '0';
      
      if (metricId === 'avg_duration') {
        // Format as time (mm:ss)
        const minutes = Math.floor(value / 60);
        const seconds = Math.floor(value % 60);
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      } else if (metricId === 'bounce_rate' || metricId === 'conversion_rate') {
        // Format as percentage
        return `${value.toFixed(1)}%`;
      } else {
        // Format as number
        return value.toLocaleString();
      }
    },
    
    // Get CSS class for change value
    getChangeClass(change) {
      if (change > 0) return 'positive';
      if (change < 0) return 'negative';
      return 'neutral';
    },
    
    // Get icon for change value
    getChangeIcon(change) {
      if (change > 0) return 'arrow_upward';
      if (change < 0) return 'arrow_downward';
      return 'remove';
    },
    
    // Format change value
    formatChange(change) {
      if (!change) return '0%';
      return `${change > 0 ? '+' : ''}${change.toFixed(1)}%`;
    },
    
    // Export comparison data
    exportComparison() {
      if (!this.comparisonData) {
        this.$buefy.toast.open({
          message: 'No comparison data to export',
          type: 'is-warning'
        });
        return;
      }
      
      // Show export options dialog
      this.$buefy.dialog.prompt({
        title: 'Export Comparison Data',
        message: 'Select export format:',
        inputAttrs: {
          type: 'select',
          options: ['CSV', 'Excel', 'PDF']
        },
        confirmText: 'Export',
        cancelText: 'Cancel',
        onConfirm: (format) => {
          this.downloadComparison(format.toLowerCase());
        }
      });
    },
    
    // Download comparison in selected format
    downloadComparison(format) {
      let url = '';
      
      if (this.comparisonType === 'showrooms') {
        url = `/api/analytics/export/${format}?vendor_id=${this.vendorId}&report_type=showroom_comparison&showroom_ids=${this.selectedShowrooms.join(',')}&metrics=${this.selectedMetrics.join(',')}`;
      } else if (this.comparisonType === 'products') {
        url = `/api/analytics/export/${format}?vendor_id=${this.vendorId}&report_type=product_comparison&product_ids=${this.selectedProducts.join(',')}&metrics=${this.selectedMetrics.join(',')}`;
      } else if (this.comparisonType === 'time-periods') {
        url = `/api/analytics/export/${format}?vendor_id=${this.vendorId}&report_type=time_period_comparison&metrics=${this.selectedMetrics.join(',')}&period_1_start=${this.period1Start}&period_1_end=${this.period1End}&period_2_start=${this.period2Start}&period_2_end=${this.period2End}`;
        
        if (this.selectedShowroomForTimePeriod) {
          url += `&showroom_id=${this.selectedShowroomForTimePeriod}`;
        }
      }
      
      // Open download in new window
      window.open(url, '_blank');
      
      // Show success message
      this.$buefy.toast.open({
        message: `Exporting comparison in ${format.toUpperCase()} format...`,
        type: 'is-success'
      });
    }
  }
};
</script>

<style scoped>
.comparative-analytics {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.comparison-section {
  display: flex;
  gap: 20px;
}

.selection-panel {
  width: 300px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.comparison-results {
  flex: 1;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 15px;
  min-height: 400px;
}

.selector-item {
  margin-bottom: 8px;
}

.comparison-chart {
  margin-bottom: 30px;
}

.period-comparison {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.period-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.period-change {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 5px;
  font-weight: bold;
}

.positive {
  color: var(--theme--primary);
}

.negative {
  color: var(--theme--danger);
}

.neutral {
  color: var(--theme--foreground-subdued);
}

.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--theme--foreground-subdued);
  font-style: italic;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--background);
  border-top: 4px solid var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
