/**
 * UE Scene Compatibility
 *
 * This module provides endpoints for validating and managing scenes
 * for Unreal Engine 5.4+ compatibility.
 */

import { Router } from 'express';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { z } from 'zod';
import { SceneService } from '../../services/scene-service';

// Create router
const router = Router();

// Define UE version schema
const UEVersionSchema = z.object({
  major: z.number(),
  minor: z.number(),
  patch: z.number(),
});

// Define scene validation schema
const SceneValidationSchema = z.object({
  scene_id: z.string().uuid(),
  ue_version: UEVersionSchema,
  target_environment: z.string().optional(),
});

// Define scene template schema
const SceneTemplateSchema = z.object({
  template_id: z.string().uuid(),
  ue_version: UEVersionSchema,
});

/**
 * Validate scene for UE compatibility
 *
 * @param req Request object
 * @param res Response object
 */
async function validateScene(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = SceneValidationSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract scene ID, UE version, and target environment
    const { scene_id, ue_version, target_environment } = bodyResult.data;

    // Log the request
    logger.info('Scene validation request', {
      scene_id,
      ue_version,
      target_environment,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get scene
    const { data: scene, error: sceneError } = await supabase
      .from('scenes')
      .select('id, vendor_id, name, description, configuration, version, created_at, updated_at')
      .eq('id', scene_id)
      .single();

    if (sceneError) {
      logger.error('Error fetching scene', { error: sceneError, scene_id });
      return res.status(500).json({ error: 'Error fetching scene' });
    }

    if (!scene) {
      return res.status(404).json({ error: 'Scene not found' });
    }

    // Validate scene for UE compatibility
    const validationResult = validateSceneForUE(scene, ue_version, target_environment);

    // Return validation result
    return res.status(200).json({
      valid: validationResult.valid,
      issues: validationResult.issues,
      warnings: validationResult.warnings,
      scene_id,
      ue_version,
      target_environment,
    });
  } catch (error) {
    return errorHandler(error, req, res);
  }
}

/**
 * Get scene template for UE compatibility
 *
 * @param req Request object
 * @param res Response object
 */
async function getSceneTemplate(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate request body
    const bodyResult = SceneTemplateSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res.status(400).json({
        error: 'Invalid request body',
        details: bodyResult.error.format(),
      });
    }

    // Extract template ID and UE version
    const { template_id, ue_version } = bodyResult.data;

    // Log the request
    logger.info('Scene template request', {
      template_id,
      ue_version,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if scene templates are supported for this UE version
    if (!areSceneTemplatesSupportedForUE(ue_version)) {
      return res.status(400).json({
        error: 'Scene templates not supported',
        message: `Scene templates are not supported for UE ${ue_version.major}.${ue_version.minor}.${ue_version.patch}`,
      });
    }

    // Get scene template
    const { data: template, error: templateError } = await supabase
      .from('scene_templates')
      .select('id, name, description, configuration, version, created_at, updated_at')
      .eq('id', template_id)
      .single();

    if (templateError) {
      logger.error('Error fetching scene template', { error: templateError, template_id });
      return res.status(500).json({ error: 'Error fetching scene template' });
    }

    if (!template) {
      return res.status(404).json({ error: 'Scene template not found' });
    }

    // Return template
    return res.status(200).json({
      template_id: template.id,
      name: template.name,
      description: template.description,
      configuration: template.configuration,
      version: template.version,
      created_at: template.created_at,
      updated_at: template.updated_at,
      ue_version,
    });
  } catch (error) {
    return errorHandler(error, req, res);
  }
}

/**
 * Validate scene for UE compatibility
 *
 * @param scene Scene object
 * @param version UE version
 * @param targetEnvironment Target environment
 * @returns Validation result
 */
function validateSceneForUE(
  scene: Record<string, any>,
  version: z.infer<typeof UEVersionSchema>,
  targetEnvironment?: string,
): {
  valid: boolean;
  issues: Array<{ code: string; message: string; path: string }>;
  warnings: Array<{ code: string; message: string; path: string }>;
} {
  const issues: Array<{ code: string; message: string; path: string }> = [];
  const warnings: Array<{ code: string; message: string; path: string }> = [];

  // Check for required fields
  if (!scene.configuration) {
    issues.push({
      code: 'MISSING_CONFIGURATION',
      message: 'Scene must have a configuration',
      path: 'configuration',
    });
    return {
      valid: false,
      issues,
      warnings,
    };
  }

  // Parse configuration
  let config: any;
  try {
    config =
      typeof scene.configuration === 'string'
        ? JSON.parse(scene.configuration)
        : scene.configuration;
  } catch (error) {
    issues.push({
      code: 'INVALID_CONFIGURATION',
      message: 'Scene configuration is not valid JSON',
      path: 'configuration',
    });
    return {
      valid: false,
      issues,
      warnings,
    };
  }

  // Check for required configuration fields
  if (!config.objects || !Array.isArray(config.objects)) {
    issues.push({
      code: 'MISSING_OBJECTS',
      message: 'Scene configuration must have an objects array',
      path: 'configuration.objects',
    });
  }

  if (!config.settings) {
    warnings.push({
      code: 'MISSING_SETTINGS',
      message: 'Scene configuration should have settings',
      path: 'configuration.settings',
    });
  }

  // Check for UE 5.4+ specific features
  if (version.major === 5 && version.minor >= 4) {
    // Check for blueprint references
    if (config.objects) {
      config.objects.forEach((object: any, index: number) => {
        if (object.blueprint_id && !object.blueprint_version) {
          warnings.push({
            code: 'MISSING_BLUEPRINT_VERSION',
            message: `Object at index ${index} has a blueprint_id but no blueprint_version`,
            path: `configuration.objects[${index}].blueprint_version`,
          });
        }
      });
    }
  }

  // Check for target environment compatibility
  if (targetEnvironment) {
    if (targetEnvironment === 'quest2' || targetEnvironment === 'quest3') {
      // Check for Quest-specific limitations
      if (config.objects && config.objects.length > 100) {
        warnings.push({
          code: 'TOO_MANY_OBJECTS',
          message: `Scene has ${config.objects.length} objects, which may cause performance issues on ${targetEnvironment}`,
          path: 'configuration.objects',
        });
      }
    }
  }

  return {
    valid: issues.length === 0,
    issues,
    warnings,
  };
}

/**
 * Check if scene templates are supported for UE version
 *
 * @param version UE version
 * @returns True if supported, false otherwise
 */
function areSceneTemplatesSupportedForUE(version: z.infer<typeof UEVersionSchema>): boolean {
  // Scene templates are supported in UE 5.4+
  if (version.major === 5 && version.minor >= 4) {
    return true;
  }

  // Not supported in other versions
  return false;
}

// Register routes
router.post('/validate', validateScene);
router.post('/template', getSceneTemplate);

// Export router
export default router;
