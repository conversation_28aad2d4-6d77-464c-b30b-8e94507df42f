import React, { useState } from 'react'
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  IconButton,
  Tooltip,
  Collapse,
  Alert,
  CircularProgress,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material'
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Speed as SpeedIcon,
  Devices as DevicesIcon,
  Code as CodeIcon,
} from '@mui/icons-material'

interface ValidationReportProps {
  sceneId: string
  validationResult?: any
  performanceAnalysis?: any
  compatibilityResult?: any
  loading?: boolean
  error?: string
  onValidate?: () => void
  onAnalyzePerformance?: () => void
  onCheckCompatibility?: (environment: string) => void
}

/**
 * Enhanced Validation Report Component
 * 
 * Displays validation results in a structured format with detailed information
 */
const EnhancedValidationReport: React.FC<ValidationReportProps> = ({
  sceneId,
  validationResult,
  performanceAnalysis,
  compatibilityResult,
  loading = false,
  error,
  onValidate,
  onAnalyzePerformance,
  onCheckCompatibility,
}) => {
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({})
  const [selectedEnvironment, setSelectedEnvironment] = useState<string>('quest2')

  // Toggle item expansion
  const toggleExpand = (itemId: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId],
    }))
  }

  // Handle environment change
  const handleEnvironmentChange = (event: SelectChangeEvent) => {
    const environment = event.target.value
    setSelectedEnvironment(environment)
    if (onCheckCompatibility) {
      onCheckCompatibility(environment)
    }
  }

  // Render validation errors and warnings
  const renderValidationIssues = () => {
    if (!validationResult) return null

    const { errors, warnings } = validationResult

    return (
      <Box>
        {errors && errors.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="h6" color="error" gutterBottom>
              Errors ({errors.length})
            </Typography>
            <List>
              {errors.map((error: any, index: number) => (
                <ListItem key={`error-${index}`} divider>
                  <ListItemIcon>
                    <ErrorIcon color="error" />
                  </ListItemIcon>
                  <ListItemText
                    primary={error.message}
                    secondary={`Code: ${error.code}${error.path ? ` | Path: ${error.path}` : ''}`}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {warnings && warnings.length > 0 && (
          <Box>
            <Typography variant="h6" color="warning.main" gutterBottom>
              Warnings ({warnings.length})
            </Typography>
            <List>
              {warnings.map((warning: any, index: number) => (
                <ListItem key={`warning-${index}`} divider>
                  <ListItemIcon>
                    <WarningIcon color="warning" />
                  </ListItemIcon>
                  <ListItemText
                    primary={warning.message}
                    secondary={`Code: ${warning.code}${warning.path ? ` | Path: ${warning.path}` : ''}`}
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {errors && errors.length === 0 && warnings && warnings.length === 0 && (
          <Alert severity="success">No validation issues found!</Alert>
        )}
      </Box>
    )
  }

  // Render performance analysis
  const renderPerformanceAnalysis = () => {
    if (!performanceAnalysis) return null

    const { impact, metrics, recommendations, optimizationPotential } = performanceAnalysis

    return (
      <Box>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardHeader 
                title="Performance Impact" 
                subheader={`Overall impact: ${impact.toUpperCase()}`}
                avatar={
                  <SpeedIcon 
                    color={impact === 'low' ? 'success' : (impact === 'medium' ? 'warning' : 'error')} 
                  />
                }
              />
              <CardContent>
                <Typography variant="body2" gutterBottom>
                  This scene has a <strong>{impact}</strong> performance impact on target devices.
                </Typography>
                
                {optimizationPotential && (
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" gutterBottom>Optimization Potential:</Typography>
                    <Grid container spacing={1}>
                      <Grid item xs={6}>
                        <Typography variant="body2">
                          Size Reduction: {(optimizationPotential.sizeReduction / 1024 / 1024).toFixed(2)} MB
                        </Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2">
                          Performance Improvement: {Math.round(optimizationPotential.performanceImprovement * 100)}%
                        </Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2">
                          Quality Impact: {optimizationPotential.qualityImpact.toUpperCase()}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Card variant="outlined">
              <CardHeader title="Performance Metrics" />
              <CardContent>
                <TableContainer>
                  <Table size="small">
                    <TableBody>
                      <TableRow>
                        <TableCell>Asset Count</TableCell>
                        <TableCell align="right">{metrics.assetCount}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Total Asset Size</TableCell>
                        <TableCell align="right">{(metrics.totalAssetSize / 1024 / 1024).toFixed(2)} MB</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Complexity Score</TableCell>
                        <TableCell align="right">{metrics.complexityScore}</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Estimated Load Time</TableCell>
                        <TableCell align="right">{metrics.estimatedLoadTime.toFixed(2)} seconds</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Estimated Memory Usage</TableCell>
                        <TableCell align="right">{(metrics.estimatedMemoryUsage / 1024 / 1024).toFixed(2)} MB</TableCell>
                      </TableRow>
                      {metrics.drawCalls && (
                        <TableRow>
                          <TableCell>Draw Calls</TableCell>
                          <TableCell align="right">{metrics.drawCalls}</TableCell>
                        </TableRow>
                      )}
                      {metrics.textureMemory && (
                        <TableRow>
                          <TableCell>Texture Memory</TableCell>
                          <TableCell align="right">{(metrics.textureMemory / 1024 / 1024).toFixed(2)} MB</TableCell>
                        </TableRow>
                      )}
                      {metrics.meshMemory && (
                        <TableRow>
                          <TableCell>Mesh Memory</TableCell>
                          <TableCell align="right">{(metrics.meshMemory / 1024 / 1024).toFixed(2)} MB</TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardHeader title="Optimization Recommendations" />
              <CardContent>
                {recommendations && recommendations.length > 0 ? (
                  <List>
                    {recommendations.map((rec: any, index: number) => {
                      const itemId = `rec-${index}`
                      const isExpanded = expandedItems[itemId] || false
                      
                      return (
                        <React.Fragment key={itemId}>
                          <ListItem 
                            button 
                            onClick={() => toggleExpand(itemId)}
                          >
                            <ListItemIcon>
                              {rec.priority === 'high' ? (
                                <ErrorIcon color="error" />
                              ) : rec.priority === 'medium' ? (
                                <WarningIcon color="warning" />
                              ) : (
                                <InfoIcon color="info" />
                              )}
                            </ListItemIcon>
                            <ListItemText
                              primary={rec.message}
                              secondary={`Priority: ${rec.priority.toUpperCase()} | Code: ${rec.code}`}
                            />
                            {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                          </ListItem>
                          
                          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                            <Box sx={{ pl: 4, pr: 2, py: 1, bgcolor: 'background.default' }}>
                              {rec.details && (
                                <Grid container spacing={1}>
                                  {rec.details.currentSize !== undefined && (
                                    <Grid item xs={12} sm={6}>
                                      <Typography variant="body2">
                                        <strong>Current:</strong> {typeof rec.details.currentSize === 'number' && rec.details.currentSize > 1024 * 1024 
                                          ? `${(rec.details.currentSize / 1024 / 1024).toFixed(2)} MB` 
                                          : rec.details.currentSize}
                                      </Typography>
                                    </Grid>
                                  )}
                                  
                                  {rec.details.recommendedSize !== undefined && (
                                    <Grid item xs={12} sm={6}>
                                      <Typography variant="body2">
                                        <strong>Recommended:</strong> {typeof rec.details.recommendedSize === 'number' && rec.details.recommendedSize > 1024 * 1024 
                                          ? `${(rec.details.recommendedSize / 1024 / 1024).toFixed(2)} MB` 
                                          : rec.details.recommendedSize}
                                      </Typography>
                                    </Grid>
                                  )}
                                  
                                  {rec.details.estimatedSavings !== undefined && (
                                    <Grid item xs={12} sm={6}>
                                      <Typography variant="body2">
                                        <strong>Estimated Savings:</strong> {typeof rec.details.estimatedSavings === 'number' && rec.details.estimatedSavings > 1024 * 1024 
                                          ? `${(rec.details.estimatedSavings / 1024 / 1024).toFixed(2)} MB` 
                                          : rec.details.estimatedSavings}
                                      </Typography>
                                    </Grid>
                                  )}
                                  
                                  {rec.details.optimizationTechnique && (
                                    <Grid item xs={12}>
                                      <Typography variant="body2">
                                        <strong>Technique:</strong> {rec.details.optimizationTechnique}
                                      </Typography>
                                    </Grid>
                                  )}
                                  
                                  {rec.details.implementationDifficulty && (
                                    <Grid item xs={12}>
                                      <Typography variant="body2">
                                        <strong>Difficulty:</strong> {rec.details.implementationDifficulty.toUpperCase()}
                                      </Typography>
                                    </Grid>
                                  )}
                                </Grid>
                              )}
                            </Box>
                          </Collapse>
                          <Divider />
                        </React.Fragment>
                      )
                    })}
                  </List>
                ) : (
                  <Alert severity="info">No optimization recommendations available.</Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    )
  }

  // Determine which content to render
  const renderContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      )
    }

    if (error) {
      return <Alert severity="error">{error}</Alert>
    }

    if (validationResult) {
      return renderValidationIssues()
    }

    if (performanceAnalysis) {
      return renderPerformanceAnalysis()
    }

    return (
      <Box sx={{ textAlign: 'center', p: 3 }}>
        <Typography variant="body1" gutterBottom>No validation data available.</Typography>
        <Button 
          variant="contained" 
          color="primary" 
          onClick={onValidate || onAnalyzePerformance || (() => onCheckCompatibility && onCheckCompatibility(selectedEnvironment))}
          startIcon={<RefreshIcon />}
          disabled={loading || (!onValidate && !onAnalyzePerformance && !onCheckCompatibility)}
          sx={{ mt: 2 }}
        >
          Run Validation
        </Button>
      </Box>
    )
  }

  return (
    <Box>
      {renderContent()}
    </Box>
  )
}

export default EnhancedValidationReport
