import { mount } from '@vue/test-utils';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import GuidedSetupWizard from '../GuidedSetupWizard.vue';
import WizardContainer from '../WizardContainer.vue';
import CompanyProfileStep from '../steps/CompanyProfileStep.vue';
import UserAccountStep from '../steps/UserAccountStep.vue';
import BrandingSetupStep from '../steps/BrandingSetupStep.vue';
import ProductUploadStep from '../steps/ProductUploadStep.vue';
import ShowroomConfigStep from '../steps/ShowroomConfigStep.vue';
import CompletionStep from '../steps/CompletionStep.vue';

// Mock the components to avoid rendering issues in tests
vi.mock('../WizardContainer.vue', () => ({
  name: 'WizardContainer',
  render: h => h('div'),
  props: ['title', 'description', 'steps', 'initialStepIndex', 'allowStepNavigation', 
          'showBackButton', 'showSaveButton', 'showHelpSection', 'nextButtonText', 
          'finishButtonText', 'storageKey']
}));

vi.mock('../steps/CompanyProfileStep.vue', () => ({
  name: 'CompanyProfileStep',
  render: h => h('div'),
  props: ['stepData']
}));

vi.mock('../steps/UserAccountStep.vue', () => ({
  name: 'UserAccountStep',
  render: h => h('div'),
  props: ['stepData']
}));

vi.mock('../steps/BrandingSetupStep.vue', () => ({
  name: 'BrandingSetupStep',
  render: h => h('div'),
  props: ['stepData']
}));

vi.mock('../steps/ProductUploadStep.vue', () => ({
  name: 'ProductUploadStep',
  render: h => h('div'),
  props: ['stepData']
}));

vi.mock('../steps/ShowroomConfigStep.vue', () => ({
  name: 'ShowroomConfigStep',
  render: h => h('div'),
  props: ['stepData']
}));

vi.mock('../steps/CompletionStep.vue', () => ({
  name: 'CompletionStep',
  render: h => h('div'),
  props: ['stepData', 'allStepsData']
}));

describe('GuidedSetupWizard', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(GuidedSetupWizard, {
      propsData: {
        vendorId: 'test-vendor-id'
      }
    });
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('renders the WizardContainer component', () => {
    expect(wrapper.findComponent(WizardContainer).exists()).toBe(true);
  });

  it('passes the correct props to WizardContainer', () => {
    const wizardContainer = wrapper.findComponent(WizardContainer);
    
    expect(wizardContainer.props('title')).toBe('Vendor Onboarding Wizard');
    expect(wizardContainer.props('steps')).toHaveLength(6);
    expect(wizardContainer.props('storageKey')).toBe('vendor-onboarding-wizard');
  });

  it('has the correct number of steps', () => {
    expect(wrapper.vm.wizardSteps).toHaveLength(6);
  });

  it('includes all required steps', () => {
    const stepComponents = wrapper.vm.wizardSteps.map(step => step.component);
    
    expect(stepComponents).toContain(CompanyProfileStep);
    expect(stepComponents).toContain(UserAccountStep);
    expect(stepComponents).toContain(BrandingSetupStep);
    expect(stepComponents).toContain(ProductUploadStep);
    expect(stepComponents).toContain(ShowroomConfigStep);
    expect(stepComponents).toContain(CompletionStep);
  });

  it('passes the vendorId prop correctly', () => {
    expect(wrapper.props('vendorId')).toBe('test-vendor-id');
  });

  it('emits the correct events when wizard is completed', async () => {
    // Simulate wizard completion
    await wrapper.vm.handleWizardComplete({ testData: 'test' });
    
    // Check that the complete event was emitted
    expect(wrapper.emitted().complete).toBeTruthy();
    expect(wrapper.emitted().complete[0][0]).toHaveProperty('vendorId', 'test-vendor-id');
    expect(wrapper.emitted().complete[0][0]).toHaveProperty('setupData');
    expect(wrapper.emitted().complete[0][0]).toHaveProperty('completedAt');
  });

  it('emits the correct events when progress is saved', async () => {
    // Simulate progress save
    const progressData = { currentStep: 1, data: { test: 'data' } };
    await wrapper.vm.handleSaveProgress(progressData);
    
    // Check that the save-progress event was emitted
    expect(wrapper.emitted()['save-progress']).toBeTruthy();
    expect(wrapper.emitted()['save-progress'][0][0]).toHaveProperty('vendorId', 'test-vendor-id');
    expect(wrapper.emitted()['save-progress'][0][0]).toHaveProperty('progressData', progressData);
    expect(wrapper.emitted()['save-progress'][0][0]).toHaveProperty('savedAt');
  });

  it('emits the correct events when progress is loaded', async () => {
    // Simulate progress load
    const progressData = { currentStep: 1, data: { test: 'data' } };
    await wrapper.vm.handleLoadProgress(progressData);
    
    // Check that the load-progress event was emitted
    expect(wrapper.emitted()['load-progress']).toBeTruthy();
    expect(wrapper.emitted()['load-progress'][0][0]).toHaveProperty('vendorId', 'test-vendor-id');
    expect(wrapper.emitted()['load-progress'][0][0]).toHaveProperty('progressData', progressData);
    expect(wrapper.emitted()['load-progress'][0][0]).toHaveProperty('loadedAt');
  });

  it('emits the correct events when analytics events occur', async () => {
    // Simulate analytics event
    const eventData = { event: 'step_view', stepIndex: 1, stepTitle: 'Test Step' };
    await wrapper.vm.handleAnalyticsEvent(eventData);
    
    // Check that the analytics event was emitted
    expect(wrapper.emitted().analytics).toBeTruthy();
    expect(wrapper.emitted().analytics[0][0]).toHaveProperty('vendorId', 'test-vendor-id');
    expect(wrapper.emitted().analytics[0][0]).toHaveProperty('event', 'step_view');
    expect(wrapper.emitted().analytics[0][0]).toHaveProperty('stepIndex', 1);
    expect(wrapper.emitted().analytics[0][0]).toHaveProperty('stepTitle', 'Test Step');
    expect(wrapper.emitted().analytics[0][0]).toHaveProperty('timestamp');
  });

  it('emits the exit event when handleExit is called', async () => {
    // Mock localStorage to avoid errors
    const localStorageMock = {
      getItem: vi.fn().mockReturnValue(null),
      setItem: vi.fn(),
      removeItem: vi.fn()
    };
    Object.defineProperty(window, 'localStorage', { value: localStorageMock });
    
    // Simulate exit
    await wrapper.vm.handleExit();
    
    // Check that the exit event was emitted
    expect(wrapper.emitted().exit).toBeTruthy();
  });
});
