import React, { useEffect, useRef, useState } from 'react'
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  Grid,
  IconButton,
  Tooltip,
} from '@mui/material'
import {
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon,
  AccountTree as AccountTreeIcon,
  Loop as LoopIcon,
  CallSplit as CallSplitIcon,
  CallMerge as CallMergeIcon,
  Block as BlockIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  Fullscreen as FullscreenIcon,
  FullscreenExit as FullscreenExitIcon,
} from '@mui/icons-material'
import { apiClient } from '../../services/apiClient'

// Import visualization libraries
// Note: You would need to install these dependencies
// npm install react-flow-renderer d3
import React<PERSON>low, {
  Background,
  Controls,
  MiniMap,
  Node,
  Edge,
  NodeTypes,
  EdgeTypes,
  ConnectionLineType,
} from 'react-flow-renderer'

interface FlowVisualizationProps {
  sceneId: string
  flowData?: any
  simulationResult?: any
  loading?: boolean
  error?: string
  onSimulateFlow?: () => void
}

/**
 * Flow Visualization Component
 * 
 * Visualizes scene flow and simulation results
 */
const FlowVisualization: React.FC<FlowVisualizationProps> = ({
  sceneId,
  flowData,
  simulationResult,
  loading = false,
  error,
  onSimulateFlow,
}) => {
  const [activeTab, setActiveTab] = useState<number>(0)
  const [nodes, setNodes] = useState<Node[]>([])
  const [edges, setEdges] = useState<Edge[]>([])
  const [zoom, setZoom] = useState<number>(1)
  const [fullscreen, setFullscreen] = useState<boolean>(false)
  const flowContainerRef = useRef<HTMLDivElement>(null)

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  // Convert flow data to nodes and edges
  useEffect(() => {
    if (flowData) {
      const flowNodes: Node[] = []
      const flowEdges: Edge[] = []
      
      // Create nodes
      Object.entries(flowData).forEach(([nodeId, nodeData]: [string, any], index) => {
        const position = {
          x: 250 * (index % 4),
          y: 150 * Math.floor(index / 4),
        }
        
        flowNodes.push({
          id: nodeId,
          type: 'flowNode',
          data: { 
            label: nodeId, 
            space: nodeData.space,
            properties: nodeData.properties,
          },
          position,
        })
        
        // Create edges for next node
        if (nodeData.next) {
          flowEdges.push({
            id: `${nodeId}-${nodeData.next}`,
            source: nodeId,
            target: nodeData.next,
            type: 'smoothstep',
            animated: true,
          })
        }
        
        // Create edges for options
        if (nodeData.options) {
          Object.entries(nodeData.options).forEach(([optionKey, targetNodeId]: [string, any]) => {
            flowEdges.push({
              id: `${nodeId}-${targetNodeId}-${optionKey}`,
              source: nodeId,
              target: targetNodeId,
              label: optionKey,
              type: 'smoothstep',
              style: { strokeDasharray: '5,5' },
            })
          })
        }
      })
      
      setNodes(flowNodes)
      setEdges(flowEdges)
    }
  }, [flowData])

  // Highlight paths, bottlenecks, etc. when simulation result changes
  useEffect(() => {
    if (simulationResult && nodes.length > 0) {
      const updatedNodes = [...nodes]
      const updatedEdges = [...edges]
      
      // Reset styles
      updatedNodes.forEach(node => {
        node.style = { ...node.style, border: '1px solid #ddd', background: '#fff' }
      })
      
      updatedEdges.forEach(edge => {
        edge.style = { ...edge.style, stroke: '#b1b1b7' }
      })
      
      // Highlight bottlenecks
      simulationResult.bottlenecks.forEach((bottleneck: any) => {
        const nodeIndex = updatedNodes.findIndex(n => n.id === bottleneck.nodeId)
        if (nodeIndex >= 0) {
          const color = bottleneck.severity === 'high' ? '#f44336' : 
                        bottleneck.severity === 'medium' ? '#ff9800' : '#2196f3'
          
          updatedNodes[nodeIndex] = {
            ...updatedNodes[nodeIndex],
            style: {
              ...updatedNodes[nodeIndex].style,
              border: `2px solid ${color}`,
              boxShadow: `0 0 10px ${color}`,
            },
            data: {
              ...updatedNodes[nodeIndex].data,
              bottleneck: bottleneck,
            },
          }
        }
      })
      
      // Highlight dead ends
      simulationResult.deadEnds.forEach((nodeId: string) => {
        const nodeIndex = updatedNodes.findIndex(n => n.id === nodeId)
        if (nodeIndex >= 0) {
          updatedNodes[nodeIndex] = {
            ...updatedNodes[nodeIndex],
            style: {
              ...updatedNodes[nodeIndex].style,
              background: '#ffebee',
              border: '2px solid #f44336',
            },
            data: {
              ...updatedNodes[nodeIndex].data,
              deadEnd: true,
            },
          }
        }
      })
      
      // Highlight unreachable nodes
      simulationResult.unreachableNodes.forEach((nodeId: string) => {
        const nodeIndex = updatedNodes.findIndex(n => n.id === nodeId)
        if (nodeIndex >= 0) {
          updatedNodes[nodeIndex] = {
            ...updatedNodes[nodeIndex],
            style: {
              ...updatedNodes[nodeIndex].style,
              opacity: 0.5,
              border: '2px dashed #9e9e9e',
            },
            data: {
              ...updatedNodes[nodeIndex].data,
              unreachable: true,
            },
          }
        }
      })
      
      // Highlight cycles
      simulationResult.cycles.forEach((cycle: any) => {
        // Highlight nodes in cycle
        cycle.nodes.forEach((nodeId: string) => {
          const nodeIndex = updatedNodes.findIndex(n => n.id === nodeId)
          if (nodeIndex >= 0) {
            updatedNodes[nodeIndex] = {
              ...updatedNodes[nodeIndex],
              style: {
                ...updatedNodes[nodeIndex].style,
                background: '#e3f2fd',
                border: '2px solid #2196f3',
              },
              data: {
                ...updatedNodes[nodeIndex].data,
                cycle: true,
              },
            }
          }
        })
        
        // Highlight edges in cycle
        for (let i = 0; i < cycle.nodes.length; i++) {
          const sourceId = cycle.nodes[i]
          const targetId = cycle.nodes[(i + 1) % cycle.nodes.length]
          
          const edgeIndex = updatedEdges.findIndex(e => 
            e.source === sourceId && e.target === targetId
          )
          
          if (edgeIndex >= 0) {
            updatedEdges[edgeIndex] = {
              ...updatedEdges[edgeIndex],
              animated: true,
              style: {
                ...updatedEdges[edgeIndex].style,
                stroke: '#2196f3',
                strokeWidth: 2,
              },
            }
          }
        }
      })
      
      setNodes(updatedNodes)
      setEdges(updatedEdges)
    }
  }, [simulationResult, nodes, edges])

  // Zoom in
  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.2, 2))
  }

  // Zoom out
  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.2, 0.5))
  }

  // Toggle fullscreen
  const toggleFullscreen = () => {
    setFullscreen(prev => !prev)
  }

  // Simulate flow
  const handleSimulateFlow = async () => {
    if (onSimulateFlow) {
      onSimulateFlow()
    } else if (flowData && sceneId) {
      try {
        const response = await apiClient.post('/api/scenes/validate/flow-simulation', {
          flow: flowData,
        })
        
        if (response.data.success) {
          // Handle simulation result
          console.log('Simulation result:', response.data.data)
        }
      } catch (error) {
        console.error('Error simulating flow:', error)
      }
    }
  }

  // Render flow visualization
  const renderFlowVisualization = () => {
    if (!flowData) {
      return (
        <Box sx={{ textAlign: 'center', p: 3 }}>
          <Typography variant="body1" gutterBottom>No flow data available.</Typography>
        </Box>
      )
    }

    return (
      <Box 
        ref={flowContainerRef}
        sx={{ 
          height: fullscreen ? '80vh' : '500px', 
          border: '1px solid #ddd',
          borderRadius: '4px',
          position: 'relative',
        }}
      >
        <Box sx={{ position: 'absolute', top: 10, right: 10, zIndex: 10 }}>
          <Tooltip title="Zoom In">
            <IconButton onClick={handleZoomIn} size="small">
              <ZoomInIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom Out">
            <IconButton onClick={handleZoomOut} size="small">
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title={fullscreen ? "Exit Fullscreen" : "Fullscreen"}>
            <IconButton onClick={toggleFullscreen} size="small">
              {fullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
            </IconButton>
          </Tooltip>
        </Box>
        
        <ReactFlow
          nodes={nodes}
          edges={edges}
          defaultZoom={zoom}
          minZoom={0.2}
          maxZoom={2}
          fitView
          attributionPosition="bottom-left"
          connectionLineType={ConnectionLineType.SmoothStep}
        >
          <Controls />
          <MiniMap />
          <Background color="#f8f8f8" gap={16} />
        </ReactFlow>
      </Box>
    )
  }

  // Render simulation metrics
  const renderSimulationMetrics = () => {
    if (!simulationResult) {
      return (
        <Box sx={{ textAlign: 'center', p: 3 }}>
          <Typography variant="body1" gutterBottom>No simulation data available.</Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={handleSimulateFlow}
            disabled={loading || !flowData}
            startIcon={<TimelineIcon />}
            sx={{ mt: 2 }}
          >
            {loading ? <CircularProgress size={24} /> : 'Simulate Flow'}
          </Button>
        </Box>
      )
    }

    const { metrics, bottlenecks, deadEnds, unreachableNodes, cycles } = simulationResult

    return (
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardHeader title="Flow Metrics" />
            <CardContent>
              <Typography variant="body2" gutterBottom>
                <strong>Average Path Length:</strong> {metrics.averagePathLength.toFixed(2)} nodes
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Longest Path:</strong> {metrics.longestPath} nodes
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Shortest Path:</strong> {metrics.shortestPath} nodes
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Total Paths:</strong> {metrics.totalPaths}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Average Time Estimate:</strong> {metrics.averageTimeEstimate.toFixed(2)} seconds
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Max Time Estimate:</strong> {metrics.maxTimeEstimate.toFixed(2)} seconds
              </Typography>
              <Typography variant="body2">
                <strong>Min Time Estimate:</strong> {metrics.minTimeEstimate.toFixed(2)} seconds
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardHeader title="Flow Issues" />
            <CardContent>
              <Typography variant="body2" gutterBottom>
                <strong>Bottlenecks:</strong> {bottlenecks.length}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Dead Ends:</strong> {deadEnds.length}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Unreachable Nodes:</strong> {unreachableNodes.length}
              </Typography>
              <Typography variant="body2">
                <strong>Cycles:</strong> {cycles.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        {bottlenecks.length > 0 && (
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardHeader 
                title="Bottlenecks" 
                avatar={<CallMergeIcon color="warning" />}
              />
              <CardContent>
                <List dense>
                  {bottlenecks.map((bottleneck: any, index: number) => (
                    <ListItem key={`bottleneck-${index}`} divider>
                      <ListItemIcon>
                        {bottleneck.type === 'convergence' ? (
                          <CallMergeIcon color="warning" />
                        ) : bottleneck.type === 'divergence' ? (
                          <CallSplitIcon color="warning" />
                        ) : (
                          <BlockIcon color="error" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={bottleneck.description}
                        secondary={`Node: ${bottleneck.nodeId} | Type: ${bottleneck.type} | Severity: ${bottleneck.severity}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}
        
        {cycles.length > 0 && (
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardHeader 
                title="Cycles" 
                avatar={<LoopIcon color="info" />}
              />
              <CardContent>
                <List dense>
                  {cycles.map((cycle: any, index: number) => (
                    <ListItem key={`cycle-${index}`} divider>
                      <ListItemIcon>
                        <LoopIcon color="info" />
                      </ListItemIcon>
                      <ListItemText
                        primary={cycle.description}
                        secondary={`Entry: ${cycle.entryPoint} | Nodes: ${cycle.nodes.join(' → ')}`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    )
  }

  return (
    <Box>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label="Flow Visualization" icon={<AccountTreeIcon />} iconPosition="start" />
          <Tab label="Simulation Metrics" icon={<TimelineIcon />} iconPosition="start" />
        </Tabs>
        
        <Box sx={{ p: 3 }}>
          {activeTab === 0 && renderFlowVisualization()}
          {activeTab === 1 && renderSimulationMetrics()}
        </Box>
      </Paper>
    </Box>
  )
}

export default FlowVisualization
