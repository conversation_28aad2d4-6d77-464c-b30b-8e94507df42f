<template>
  <div class="preview-testing-tools">
    <div class="tools-header">
      <h2 class="tools-title">Preview & Testing Tools</h2>
      
      <div class="tools-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          class="tab-button"
          :class="{ active: activeTab === tab.id }"
          @click="activeTab = tab.id"
        >
          <i class="material-icons">{{ tab.icon }}</i>
          <span>{{ tab.name }}</span>
        </button>
      </div>
    </div>
    
    <div class="tools-content">
      <!-- Live Preview Tab -->
      <div v-if="activeTab === 'live-preview'" class="tab-content">
        <LivePreview 
          :initial-mode="'edit'"
          :initial-url="previewUrl"
          :initial-data="previewData"
          @change="handlePreviewChange"
        />
      </div>
      
      <!-- Device Preview Tab -->
      <div v-else-if="activeTab === 'device-preview'" class="tab-content">
        <DevicePreview :src="previewUrl" />
      </div>
      
      <!-- A/B Testing Tab -->
      <div v-else-if="activeTab === 'ab-testing'" class="tab-content">
        <ABTestingFramework />
      </div>
      
      <!-- Performance Testing Tab -->
      <div v-else-if="activeTab === 'performance-testing'" class="tab-content">
        <PerformanceTestingTools />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue';
import LivePreview from './LivePreview.vue';
import DevicePreview from './DevicePreview.vue';
import ABTestingFramework from './ABTestingFramework.vue';
import PerformanceTestingTools from './PerformanceTestingTools.vue';

export default {
  name: 'PreviewTestingTools',
  
  components: {
    LivePreview,
    DevicePreview,
    ABTestingFramework,
    PerformanceTestingTools
  },
  
  props: {
    initialUrl: {
      type: String,
      default: ''
    },
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  
  setup(props, { emit }) {
    // Available tabs
    const tabs = [
      { id: 'live-preview', name: 'Live Preview', icon: 'visibility' },
      { id: 'device-preview', name: 'Device Preview', icon: 'devices' },
      { id: 'ab-testing', name: 'A/B Testing', icon: 'science' },
      { id: 'performance-testing', name: 'Performance', icon: 'speed' }
    ];
    
    // Active tab
    const activeTab = ref('live-preview');
    
    // Preview URL and data
    const previewUrl = ref(props.initialUrl || 'https://example.com');
    const previewData = reactive(props.initialData || {});
    
    // Handle preview change
    const handlePreviewChange = (data) => {
      emit('preview-change', data);
    };
    
    return {
      tabs,
      activeTab,
      previewUrl,
      previewData,
      handlePreviewChange
    };
  }
};
</script>

<style scoped>
.preview-testing-tools {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
}

.tools-header {
  display: flex;
  flex-direction: column;
  padding: 16px 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.tools-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
}

.tools-tabs {
  display: flex;
  gap: 8px;
  border-bottom: 1px solid var(--theme--border-color);
  padding-bottom: 8px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  color: var(--theme--foreground);
}

.tab-button.active {
  color: var(--theme--primary);
  border-bottom-color: var(--theme--primary);
}

.tools-content {
  flex: 1;
  overflow: hidden;
}

.tab-content {
  height: 100%;
}
</style>
