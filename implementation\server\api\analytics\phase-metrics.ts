/**
 * Phase Analytics API
 *
 * This file defines the API endpoints for phase analytics.
 */

import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { PhaseAnalyticsService } from '../../services/analytics/phase-analytics';

// Initialize phase analytics service
const phaseAnalytics = new PhaseAnalyticsService(supabase);

/**
 * Get phase analytics
 *
 * @param req - Request
 * @param res - Response
 */
export const getPhaseAnalytics = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, start_date, end_date } = req.query;

    // Get phase analytics
    const analytics = await phaseAnalytics.getPhaseAnalytics(
      vendor_id as string,
      start_date as string,
      end_date as string,
    );

    res.status(200).json({
      success: true,
      data: analytics,
    });
  } catch (error) {
    logger.error('Error in getPhaseAnalytics', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get phase durations
 *
 * @param req - Request
 * @param res - Response
 */
export const getPhaseDurations = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, start_date, end_date } = req.query;

    // Build query
    let query = supabase.rpc('get_phase_durations');

    // Add filters
    if (vendor_id) {
      query = query.eq('vendor_id', vendor_id);
    }

    if (start_date) {
      query = query.gte('completed_at', start_date);
    }

    if (end_date) {
      query = query.lte('completed_at', end_date);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error getting phase durations', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting phase durations',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getPhaseDurations', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get phase success rates
 *
 * @param req - Request
 * @param res - Response
 */
export const getPhaseSuccessRates = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, start_date, end_date } = req.query;

    // Build query
    let query = supabase.rpc('get_phase_success_rates');

    // Add filters
    if (vendor_id) {
      query = query.eq('vendor_id', vendor_id);
    }

    if (start_date) {
      query = query.gte('last_updated', start_date);
    }

    if (end_date) {
      query = query.lte('last_updated', end_date);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error getting phase success rates', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting phase success rates',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getPhaseSuccessRates', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get validation success rates
 *
 * @param req - Request
 * @param res - Response
 */
export const getValidationSuccessRates = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, start_date, end_date } = req.query;

    // Build query
    let query = supabase.rpc('get_validation_success_rates');

    // Add filters
    if (vendor_id) {
      query = query.eq('vendor_id', vendor_id);
    }

    if (start_date) {
      query = query.gte('timestamp', start_date);
    }

    if (end_date) {
      query = query.lte('timestamp', end_date);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error getting validation success rates', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting validation success rates',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getValidationSuccessRates', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get team performance
 *
 * @param req - Request
 * @param res - Response
 */
export const getTeamPerformance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, start_date, end_date } = req.query;

    // Validate vendor ID
    if (!vendor_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_VENDOR_ID',
          message: 'Vendor ID is required',
        },
      });
      return;
    }

    // Build query
    let query = supabase.rpc('get_team_performance').eq('vendor_id', vendor_id);

    // Add filters
    if (start_date) {
      query = query.gte('completed_at', start_date);
    }

    if (end_date) {
      query = query.lte('completed_at', end_date);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error getting team performance', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting team performance',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getTeamPerformance', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on method and path
    if (req.method === 'GET' && req.path.endsWith('/durations')) {
      await getPhaseDurations(req, res);
    } else if (req.method === 'GET' && req.path.endsWith('/success-rates')) {
      await getPhaseSuccessRates(req, res);
    } else if (req.method === 'GET' && req.path.endsWith('/validation-rates')) {
      await getValidationSuccessRates(req, res);
    } else if (req.method === 'GET' && req.path.endsWith('/team-performance')) {
      await getTeamPerformance(req, res);
    } else if (req.method === 'GET') {
      await getPhaseAnalytics(req, res);
    } else {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
    }
  } catch (error) {
    logger.error('Error in phase analytics handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
}
