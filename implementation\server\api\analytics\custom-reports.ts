/**
 * Custom Reports API
 *
 * This file contains API endpoints for custom report generation.
 */

import express from 'express';
import { supabase } from '../../lib/supabase';
import { logger } from '../../lib/logger';
import { expressAuthenticate } from '../../middleware/auth';
import { expressRateLimiter } from '../../middleware/rate-limiter';
import { validateRequest } from '../../middleware/validate-request';
import * as Papa from 'papaparse';

// Create router
const router: express.Router = express.Router();

/**
 * Generate custom report
 *
 * @param req - Request
 * @param res - Response
 */
export const generateCustomReport = async (
  req: express.Request,
  res: express.Response,
): Promise<void> => {
  try {
    const {
      vendor_id,
      report_name,
      data_sources,
      metrics,
      filters,
      grouping,
      start_date,
      end_date,
    } = req.body;

    // Validate required parameters
    if (!vendor_id || !data_sources || !metrics) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID, data sources, and metrics are required',
        },
      });
      return;
    }

    // Parse data sources
    const dataSources = Array.isArray(data_sources) ? data_sources : [data_sources];

    // Parse metrics
    const metricsList = Array.isArray(metrics) ? metrics : [metrics];

    // Generate report data
    const reportData = await generateReportData(
      vendor_id,
      dataSources,
      metricsList,
      filters || {},
      grouping,
      start_date,
      end_date,
    );

    // Save report if name is provided
    if (report_name) {
      await saveCustomReport(vendor_id, report_name, {
        data_sources: dataSources,
        metrics: metricsList,
        filters: filters || {},
        grouping,
        start_date,
        end_date,
      });
    }

    // Return data
    res.status(200).json({
      success: true,
      data: reportData,
    });
  } catch (error) {
    logger.error('Error in generateCustomReport', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get saved custom reports
 *
 * @param req - Request
 * @param res - Response
 */
export const getSavedReports = async (
  req: express.Request,
  res: express.Response,
): Promise<void> => {
  try {
    const { vendor_id } = req.query;

    // Validate vendor ID
    if (!vendor_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_VENDOR_ID',
          message: 'Vendor ID is required',
        },
      });
      return;
    }

    // Get saved reports
    const { data, error } = await supabase
      .from('custom_reports')
      .select('id, name, config, created_at, updated_at')
      .eq('vendor_id', vendor_id)
      .order('updated_at', { ascending: false });

    if (error) {
      logger.error('Error getting saved reports', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting saved reports',
        },
      });
      return;
    }

    // Return data
    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getSavedReports', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Run saved custom report
 *
 * @param req - Request
 * @param res - Response
 */
export const runSavedReport = async (
  req: express.Request,
  res: express.Response,
): Promise<void> => {
  try {
    const { report_id } = req.params;
    const { start_date, end_date } = req.query;

    // Validate report ID
    if (!report_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REPORT_ID',
          message: 'Report ID is required',
        },
      });
      return;
    }

    // Get report config
    const { data: reportData, error: reportError } = await supabase
      .from('custom_reports')
      .select('vendor_id, config')
      .eq('id', report_id)
      .single();

    if (reportError || !reportData) {
      logger.error('Error getting report config', { error: reportError });
      res.status(404).json({
        success: false,
        error: {
          code: 'REPORT_NOT_FOUND',
          message: 'Report not found',
        },
      });
      return;
    }

    // Extract config
    const { vendor_id, config } = reportData;

    // Override date range if provided
    const reportConfig = {
      ...config,
      start_date: start_date || config.start_date,
      end_date: end_date || config.end_date,
    };

    // Generate report data
    const data = await generateReportData(
      vendor_id,
      reportConfig.data_sources,
      reportConfig.metrics,
      reportConfig.filters,
      reportConfig.grouping,
      reportConfig.start_date,
      reportConfig.end_date,
    );

    // Return data
    res.status(200).json({
      success: true,
      data,
      config: reportConfig,
    });
  } catch (error) {
    logger.error('Error in runSavedReport', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Delete saved custom report
 *
 * @param req - Request
 * @param res - Response
 */
export const deleteSavedReport = async (
  req: express.Request,
  res: express.Response,
): Promise<void> => {
  try {
    const { report_id } = req.params;

    // Validate report ID
    if (!report_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REPORT_ID',
          message: 'Report ID is required',
        },
      });
      return;
    }

    // Delete report
    const { error } = await supabase.from('custom_reports').delete().eq('id', report_id);

    if (error) {
      logger.error('Error deleting report', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error deleting report',
        },
      });
      return;
    }

    // Return success
    res.status(200).json({
      success: true,
      message: 'Report deleted successfully',
    });
  } catch (error) {
    logger.error('Error in deleteSavedReport', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Generate report data
 *
 * @param vendorId - Vendor ID
 * @param dataSources - Data sources
 * @param metrics - Metrics
 * @param filters - Filters
 * @param grouping - Grouping
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Report data
 */
async function generateReportData(
  vendorId: string,
  dataSources: string[],
  metrics: string[],
  filters: Record<string, any>,
  grouping?: string,
  startDate?: string,
  endDate?: string,
) {
  // Initialize result
  const result: Record<string, any> = {};

  // Process each data source
  for (const source of dataSources) {
    // Get data for source
    const sourceData = await getDataForSource(
      vendorId,
      source,
      metrics,
      filters,
      startDate,
      endDate,
    );

    // Apply grouping if specified
    const processedData = grouping ? applyGrouping(sourceData, grouping) : sourceData;

    // Add to result
    result[source] = processedData;
  }

  return result;
}

/**
 * Get data for a specific source
 *
 * @param vendorId - Vendor ID
 * @param source - Data source
 * @param metrics - Metrics
 * @param filters - Filters
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Source data
 */
async function getDataForSource(
  vendorId: string,
  source: string,
  _metrics: string[], // Prefix with underscore to indicate it's intentionally unused
  filters: Record<string, any>,
  startDate?: string,
  endDate?: string,
) {
  // Map source to table
  const tableMap: Record<string, string> = {
    visitors: 'visitor_sessions',
    products: 'product_interactions',
    showrooms: 'showroom_analytics',
    conversions: 'conversion_events',
    clients: 'client_interactions',
  };

  const table = tableMap[source] || source;

  // Build query
  let query = supabase.from(table).select('*').eq('vendor_id', vendorId);

  // Add date filters if provided
  if (startDate) {
    query = query.gte('created_at', startDate);
  }

  if (endDate) {
    query = query.lte('created_at', endDate);
  }

  // Add custom filters
  for (const [key, value] of Object.entries(filters)) {
    if (key.startsWith(source + '_')) {
      const field = key.replace(source + '_', '');
      query = query.eq(field, value);
    }
  }

  // Execute query
  const { data, error } = await query;

  if (error) {
    logger.error('Error getting data for source', { error, source });
    throw error;
  }

  return data || [];
}

/**
 * Apply grouping to data
 *
 * @param data - Raw data
 * @param grouping - Grouping field
 * @returns Grouped data
 */
function applyGrouping(data: any[], grouping: string) {
  // Group data by the specified field
  const groupedData: Record<string, any[]> = {};

  data.forEach(item => {
    const groupValue = item[grouping];

    if (!groupValue) {
      return;
    }

    if (!groupedData[groupValue]) {
      groupedData[groupValue] = [];
    }

    groupedData[groupValue].push(item);
  });

  return groupedData;
}

/**
 * Save custom report
 *
 * @param vendorId - Vendor ID
 * @param name - Report name
 * @param config - Report configuration
 * @returns Saved report ID
 */
async function saveCustomReport(vendorId: string, name: string, config: Record<string, any>) {
  // Save report
  const { data, error } = await supabase
    .from('custom_reports')
    .insert({
      vendor_id: vendorId,
      name,
      config,
    })
    .select('id')
    .single();

  if (error) {
    logger.error('Error saving custom report', { error });
    throw error;
  }

  return data?.id;
}

/**
 * Export custom report as CSV
 *
 * @param req - Request
 * @param res - Response
 */
export const exportCustomReportCSV = async (
  req: express.Request,
  res: express.Response,
): Promise<void> => {
  try {
    const { report_id } = req.params;
    const { start_date, end_date } = req.query;

    // Get report config
    const { data: reportData, error: reportError } = await supabase
      .from('custom_reports')
      .select('*')
      .eq('id', report_id)
      .single();

    if (reportError || !reportData) {
      logger.error('Error getting report config', { error: reportError });
      res.status(404).json({
        success: false,
        error: {
          code: 'REPORT_NOT_FOUND',
          message: 'Report not found',
        },
      });
      return;
    }

    // Extract config
    const { vendor_id, config, name } = reportData;

    // Override date range if provided
    const reportConfig = {
      ...config,
      start_date: start_date || config.start_date,
      end_date: end_date || config.end_date,
    };

    // Generate report data
    const data = await generateReportData(
      vendor_id,
      reportConfig.data_sources,
      reportConfig.metrics,
      reportConfig.filters,
      reportConfig.grouping,
      reportConfig.start_date,
      reportConfig.end_date,
    );

    // Convert data to CSV
    // Papa.unparse expects an array of objects, so we need to convert our data
    const dataArray = Object.entries(data).flatMap(([source, items]) => {
      // If items is an array, add source field to each item
      if (Array.isArray(items)) {
        return items.map(item => ({ source, ...item }));
      }
      // If items is an object (grouped data), flatten it
      return Object.entries(items).flatMap(([group, groupItems]) => {
        if (Array.isArray(groupItems)) {
          return groupItems.map(item => ({ source, group, ...item }));
        }
        return [];
      });
    });

    const csv = Papa.unparse(dataArray);

    // Set response headers
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${name.replace(/\s+/g, '_')}-${new Date().toISOString().split('T')[0]}.csv`,
    );

    // Send CSV data
    res.status(200).send(csv);
  } catch (error) {
    logger.error('Error in exportCustomReportCSV', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

// Register routes
router.post(
  '/custom-reports',
  expressAuthenticate,
  expressRateLimiter,
  validateRequest,
  generateCustomReport,
);
router.get('/custom-reports', expressAuthenticate, expressRateLimiter, getSavedReports);
router.get('/custom-reports/:report_id', expressAuthenticate, expressRateLimiter, runSavedReport);
router.get(
  '/custom-reports/:report_id/export/csv',
  expressAuthenticate,
  expressRateLimiter,
  exportCustomReportCSV,
);
router.delete(
  '/custom-reports/:report_id',
  expressAuthenticate,
  expressRateLimiter,
  deleteSavedReport,
);

export default router;
