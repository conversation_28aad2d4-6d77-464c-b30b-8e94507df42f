<template>
  <div class="interaction-heatmap">
    <div class="heatmap-header">
      <h3>Interactive Heatmap Visualization</h3>
      <div class="controls">
        <div class="select-wrapper">
          <label for="showroom-select">Showroom:</label>
          <select id="showroom-select" v-model="selectedShowroom" @change="loadHeatmapData">
            <option v-for="showroom in showrooms" :key="showroom.id" :value="showroom.id">
              {{ showroom.name }}
            </option>
          </select>
        </div>
        <div class="select-wrapper">
          <label for="date-range">Date Range:</label>
          <select id="date-range" v-model="dateRange" @change="loadHeatmapData">
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="custom">Custom Range</option>
          </select>
        </div>
        <div v-if="dateRange === 'custom'" class="date-inputs">
          <input type="date" v-model="startDate" @change="loadHeatmapData" :max="today" />
          <span>to</span>
          <input type="date" v-model="endDate" @change="loadHeatmapData" :max="today" :min="startDate" />
        </div>
        <div class="select-wrapper">
          <label for="interaction-type">Interaction Type:</label>
          <select id="interaction-type" v-model="interactionType" @change="loadHeatmapData">
            <option value="all">All Interactions</option>
            <option value="view">Views</option>
            <option value="click">Clicks</option>
            <option value="hover">Hovers</option>
            <option value="dwell">Dwell Time</option>
          </select>
        </div>
        <button class="refresh-button" @click="loadHeatmapData" :disabled="loading">
          <i class="material-icons">refresh</i>
          Refresh
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-indicator">
      <div class="spinner"></div>
      <p>Loading heatmap data...</p>
    </div>

    <div v-else-if="!selectedShowroom" class="no-selection">
      <p>Please select a showroom to view the interaction heatmap.</p>
    </div>

    <div v-else class="heatmap-content">
      <div class="heatmap-container">
        <div class="heatmap-wrapper">
          <!-- Showroom layout image -->
          <img 
            :src="showroomLayout.imageUrl" 
            alt="Showroom Layout" 
            class="showroom-layout"
            ref="layoutImage"
            @load="initHeatmap"
          />
          
          <!-- Heatmap canvas overlay -->
          <canvas ref="heatmapCanvas" class="heatmap-canvas"></canvas>
        </div>
      </div>

      <div class="heatmap-sidebar">
        <div class="heatmap-stats">
          <h4>Interaction Statistics</h4>
          
          <div class="stat-item">
            <div class="stat-label">Total Interactions</div>
            <div class="stat-value">{{ stats.totalInteractions }}</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">Unique Visitors</div>
            <div class="stat-value">{{ stats.uniqueVisitors }}</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">Average Dwell Time</div>
            <div class="stat-value">{{ formatTime(stats.avgDwellTime) }}</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">Interaction Density</div>
            <div class="stat-value">{{ stats.interactionDensity.toFixed(2) }}</div>
          </div>
        </div>

        <div class="hotspot-list">
          <h4>Top Interaction Hotspots</h4>
          <div v-if="hotspots.length === 0" class="no-data">
            No hotspot data available
          </div>
          <ul v-else>
            <li v-for="(hotspot, index) in hotspots" :key="index" class="hotspot-item">
              <div class="hotspot-name">{{ hotspot.name }}</div>
              <div class="hotspot-count">{{ hotspot.count }} interactions</div>
              <div class="hotspot-bar">
                <div class="hotspot-bar-fill" :style="{ width: `${hotspot.percentage}%` }"></div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import h337 from 'heatmap.js';

export default {
  name: 'InteractionHeatmap',

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      showrooms: [],
      selectedShowroom: '',
      dateRange: '30',
      startDate: this.getDefaultStartDate(),
      endDate: new Date().toISOString().split('T')[0],
      interactionType: 'all',
      heatmapInstance: null,
      showroomLayout: {
        imageUrl: '',
        width: 0,
        height: 0
      },
      interactionData: [],
      stats: {
        totalInteractions: 0,
        uniqueVisitors: 0,
        avgDwellTime: 0,
        interactionDensity: 0
      },
      hotspots: [],
      today: new Date().toISOString().split('T')[0]
    };
  },

  mounted() {
    this.loadShowrooms();
  },

  methods: {
    // Get default start date (30 days ago)
    getDefaultStartDate() {
      const date = new Date();
      date.setDate(date.getDate() - 30);
      return date.toISOString().split('T')[0];
    },

    // Load showrooms
    async loadShowrooms() {
      try {
        const response = await fetch(`/api/showrooms?vendor_id=${this.vendorId}`);
        const data = await response.json();
        
        if (data.success) {
          this.showrooms = data.data;
          
          // Select first showroom by default if available
          if (this.showrooms.length > 0 && !this.selectedShowroom) {
            this.selectedShowroom = this.showrooms[0].id;
            this.loadHeatmapData();
          }
        } else {
          console.error('Error loading showrooms:', data.error);
          this.showrooms = [];
        }
      } catch (error) {
        console.error('Error loading showrooms:', error);
        this.showrooms = [];
      } finally {
        this.loading = false;
      }
    },

    // Load heatmap data
    async loadHeatmapData() {
      if (!this.selectedShowroom) return;
      
      this.loading = true;
      
      try {
        // Build query parameters
        let params = new URLSearchParams({
          vendor_id: this.vendorId,
          showroom_id: this.selectedShowroom,
          interaction_type: this.interactionType
        });
        
        // Add date parameters
        if (this.dateRange === 'custom') {
          params.append('start_date', this.startDate);
          params.append('end_date', this.endDate);
        } else {
          params.append('days', this.dateRange);
        }
        
        // Fetch heatmap data
        const response = await fetch(`/api/analytics/realtime/heatmap?${params.toString()}`);
        const data = await response.json();
        
        if (data.success) {
          // Get showroom layout
          this.showroomLayout = data.data.layout;
          
          // Get interaction data
          this.interactionData = data.data.interactions;
          
          // Get statistics
          this.stats = data.data.stats;
          
          // Get hotspots
          this.hotspots = data.data.hotspots;
          
          // Initialize heatmap after data is loaded
          this.$nextTick(() => {
            this.initHeatmap();
          });
        } else {
          console.error('Error loading heatmap data:', data.error);
        }
      } catch (error) {
        console.error('Error loading heatmap data:', error);
      } finally {
        this.loading = false;
      }
    },

    // Initialize heatmap
    initHeatmap() {
      if (!this.$refs.heatmapCanvas || !this.$refs.layoutImage || this.interactionData.length === 0) return;
      
      // Get layout image dimensions
      const layoutImage = this.$refs.layoutImage;
      const canvas = this.$refs.heatmapCanvas;
      
      // Set canvas dimensions to match image
      canvas.width = layoutImage.width;
      canvas.height = layoutImage.height;
      
      // Destroy existing heatmap instance if it exists
      if (this.heatmapInstance) {
        this.heatmapInstance.setData({ data: [] });
      }
      
      // Create new heatmap instance
      this.heatmapInstance = h337.create({
        container: canvas.parentElement,
        canvas: canvas,
        radius: 30,
        maxOpacity: 0.6,
        minOpacity: 0,
        blur: 0.85
      });
      
      // Prepare data for heatmap
      const points = this.interactionData.map(interaction => ({
        x: Math.floor(interaction.x * canvas.width),
        y: Math.floor(interaction.y * canvas.height),
        value: interaction.value
      }));
      
      // Set data
      this.heatmapInstance.setData({
        max: Math.max(...this.interactionData.map(i => i.value)),
        data: points
      });
    },

    // Format time in seconds to minutes and seconds
    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      
      if (minutes > 0) {
        return `${minutes}m ${remainingSeconds}s`;
      }
      
      return `${remainingSeconds}s`;
    }
  }
};
</script>

<style scoped>
.interaction-heatmap {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.heatmap-header {
  margin-bottom: 20px;
}

.heatmap-header h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.select-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.select-wrapper label {
  font-weight: 500;
}

select, input[type="date"] {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.date-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.refresh-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.loading-indicator, .no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.heatmap-content {
  display: flex;
  gap: 20px;
}

.heatmap-container {
  flex: 3;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  overflow: hidden;
}

.heatmap-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.showroom-layout {
  display: block;
  max-width: 100%;
  height: auto;
}

.heatmap-canvas {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
}

.heatmap-sidebar {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.heatmap-stats, .hotspot-list {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.heatmap-stats h4, .hotspot-list h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.stat-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: 500;
}

.hotspot-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.hotspot-item {
  margin-bottom: 15px;
}

.hotspot-name {
  font-weight: 500;
  margin-bottom: 5px;
}

.hotspot-count {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.hotspot-bar {
  height: 8px;
  background-color: #eee;
  border-radius: 4px;
  overflow: hidden;
}

.hotspot-bar-fill {
  height: 100%;
  background-color: #4CAF50;
  border-radius: 4px;
}

.no-data {
  color: #666;
  font-style: italic;
}

@media (max-width: 1200px) {
  .heatmap-content {
    flex-direction: column;
  }
  
  .heatmap-sidebar {
    flex-direction: row;
  }
  
  .heatmap-stats, .hotspot-list {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .heatmap-sidebar {
    flex-direction: column;
  }
}
</style>
