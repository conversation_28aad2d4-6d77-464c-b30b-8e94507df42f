import React, { useEffect, useState } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import {
  <PERSON><PERSON>,
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Snackbar,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import { Add, Preview, Save } from '@mui/icons-material';
import { Blueprint } from '../../shared/models/blueprint';
import MonacoJsonEditor from './MonacoJsonEditor';

interface BlueprintEditorProps {
  vendorId: string;
  blueprintId?: string;
  onSave?: (blueprint: Blueprint) => void;
  onCancel?: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`blueprint-tabpanel-${index}`}
      aria-labelledby={`blueprint-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const defaultScript = {
  author: '',
  initialization_actions: [],
  triggers: [],
  properties: {},
};

export const BlueprintEditor: React.FC<BlueprintEditorProps> = ({
  vendorId,
  blueprintId,
  onSave,
  onCancel,
}: BlueprintEditorProps) => {
  const supabase = useSupabaseClient();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState<number>(0);

  const [blueprint, setBlueprint] = useState<Blueprint>({
    id: blueprintId || '',
    vendor_id: vendorId,
    name: '',
    description: null,
    tags: [],
    script: defaultScript,
    version: '1.0.0',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  });

  const [newTag, setNewTag] = useState<string>('');
  const [previewDialogOpen, setPreviewDialogOpen] = useState<boolean>(false);

  useEffect(() => {
    if (blueprintId) {
      loadBlueprint();
    }
  }, [blueprintId]);

  const loadBlueprint = async () => {
    if (!blueprintId) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('blueprints')
        .select('*')
        .eq('id', blueprintId)
        .single();

      if (error) {
        throw error;
      }

      if (data) {
        setBlueprint(data as Blueprint);
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to load blueprint: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setLoading(true);
    setError(null);

    try {
      const updatedBlueprint = {
        ...blueprint,
        updated_at: new Date().toISOString(),
      };

      let result;

      if (blueprintId) {
        // Update existing blueprint
        const { data, error } = await supabase
          .from('blueprints')
          .update(updatedBlueprint)
          .eq('id', blueprintId)
          .select()
          .single();

        if (error) {
          throw error;
        }

        result = data;
        setSuccess('Blueprint updated successfully');
      } else {
        // Create new blueprint
        const { data, error } = await supabase
          .from('blueprints')
          .insert(updatedBlueprint)
          .select()
          .single();

        if (error) {
          throw error;
        }

        result = data;
        setSuccess('Blueprint created successfully');
      }

      if (onSave && result) {
        onSave(result as Blueprint);
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(`Failed to save blueprint: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !blueprint.tags.includes(newTag.trim())) {
      setBlueprint({
        ...blueprint,
        tags: [...blueprint.tags, newTag.trim()],
      });
      setNewTag('');
    }
  };

  const handleRemoveTag = (tag: string) => {
    setBlueprint({
      ...blueprint,
      tags: blueprint.tags.filter((t: string) => t !== tag),
    });
  };

  const handleScriptChange = (newScript: Record<string, unknown>) => {
    setBlueprint({
      ...blueprint,
      script: newScript,
    });
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box>
      <Card>
        <CardHeader
          title={blueprintId ? 'Edit Blueprint' : 'Create Blueprint'}
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<Preview />}
                onClick={() => setPreviewDialogOpen(true)}
              >
                Preview
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={<Save />}
                onClick={handleSave}
                disabled={loading}
              >
                Save
              </Button>
              {onCancel && (
                <Button variant="outlined" onClick={onCancel} disabled={loading}>
                  Cancel
                </Button>
              )}
            </Box>
          }
        />
        <Divider />
        <CardContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="blueprint editor tabs">
              <Tab label="Basic Info" />
              <Tab label="Script Editor" />
              <Tab label="Test" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <TextField
                  fullWidth
                  label="Name"
                  value={blueprint.name}
                  onChange={(e: React.ChangeEvent<any>) =>
                    setBlueprint({ ...blueprint, name: e.target.value })
                  }
                  required
                  margin="normal"
                  sx={{ flex: '1 1 45%' }}
                />
                <TextField
                  fullWidth
                  label="Version"
                  value={blueprint.version}
                  onChange={(e: React.ChangeEvent<any>) =>
                    setBlueprint({ ...blueprint, version: e.target.value })
                  }
                  required
                  margin="normal"
                  sx={{ flex: '1 1 45%' }}
                />
              </Box>
              <TextField
                fullWidth
                label="Description"
                value={blueprint.description || ''}
                onChange={(e: React.ChangeEvent<any>) =>
                  setBlueprint({ ...blueprint, description: e.target.value })
                }
                multiline
                rows={3}
                margin="normal"
              />
              <Box>
                <Typography variant="subtitle1" gutterBottom>
                  Tags
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                  {blueprint.tags.map((tag: string) => (
                    <Chip key={tag} label={tag} onDelete={() => handleRemoveTag(tag)} />
                  ))}
                </Box>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <TextField
                    label="Add Tag"
                    value={newTag}
                    onChange={(e: React.ChangeEvent<any>) => setNewTag(e.target.value)}
                    onKeyPress={(e: React.KeyboardEvent<any>) =>
                      e.key === 'Enter' && handleAddTag()
                    }
                  />
                  <Button variant="outlined" startIcon={<Add />} onClick={handleAddTag}>
                    Add
                  </Button>
                </Box>
              </Box>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Box sx={{ height: '500px' }}>
              <MonacoJsonEditor
                value={blueprint.script}
                onChange={handleScriptChange}
                height="500px"
                width="100%"
              />
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6" gutterBottom>
              Test Blueprint
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              This feature will be available in a future update.
            </Typography>
          </TabPanel>
        </CardContent>
      </Card>

      <Dialog
        open={previewDialogOpen}
        onClose={() => setPreviewDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Blueprint Preview</DialogTitle>
        <DialogContent>
          <Box sx={{ height: '500px', width: '100%' }}>
            <MonacoJsonEditor
              value={blueprint}
              onChange={() => {}}
              readOnly
              height="500px"
              width="100%"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar open={!!success} autoHideDuration={6000} onClose={() => setSuccess(null)}>
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};
