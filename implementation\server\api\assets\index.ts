/**
 * Asset Management API
 *
 * This module provides API endpoints for asset management.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { validateRequest } from '../middleware/validation';
import { authenticateRequest } from '../middleware/auth';
import { errorMiddleware } from '../middleware/error';
import { Logger } from '../../services/integration/logger';
import { assetService, AssetType } from '../../services/asset/asset-service';
import { supabase } from '../../shared/utils/supabase-client';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  vendor_id: z.string().uuid().optional(),
  type: z.string().optional(),
  limit: z.string().transform(Number).optional(),
  offset: z.string().transform(Number).optional(),
});

/**
 * Assets API endpoint
 *
 * This endpoint returns a list of assets.
 * It can be filtered by vendor_id and type.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse(req.query);
    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { vendor_id, type, limit = 100, offset = 0 } = queryResult.data;

    // Log the request
    logger.info('Assets request', {
      vendor_id,
      type,
      limit,
      offset,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create asset service
    const assetService = new AssetService(supabase);

    // Build query
    let query = supabase.from('assets').select('*');

    if (vendor_id) {
      query = query.eq('vendor_id', vendor_id);
    }

    if (type) {
      query = query.eq('type', type);
    }

    // Add pagination
    query = query.range(offset, offset + limit - 1);

    // Execute query
    const { data: assets, error, count } = await query;

    if (error) {
      logger.error('Error fetching assets', { error });
      return errorHandler(error, res);
    }

    // Get total count
    const { count: totalCount, error: countError } = await supabase
      .from('assets')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      logger.error('Error getting total count', { error: countError });
      // Continue anyway, just don't return total count
    }

    // Return assets
    return res.status(200).json({
      assets,
      pagination: {
        limit,
        offset,
        total: totalCount,
      },
    });
  } catch (error) {
    logger.error('Unexpected error in assets endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
