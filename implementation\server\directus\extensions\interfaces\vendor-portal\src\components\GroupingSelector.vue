<template>
  <div class="grouping-selector">
    <div class="grouping-header">
      <h5>Group Data By</h5>
      <div class="grouping-actions">
        <button 
          class="btn btn-secondary" 
          @click="clearGrouping" 
          :disabled="!selectedDimension"
          title="Clear Grouping"
        >
          <i class="material-icons">clear</i> Clear
        </button>
      </div>
    </div>

    <div class="grouping-content">
      <div class="dimension-selector">
        <label>Select Dimension:</label>
        <select v-model="selectedDimension" @change="onDimensionChange">
          <option value="">No Grouping</option>
          <option v-for="dimension in availableDimensions" :key="dimension.id" :value="dimension.id">
            {{ dimension.name }}
          </option>
        </select>
      </div>

      <!-- Time Dimension Options -->
      <div v-if="selectedDimension === 'time'" class="dimension-options">
        <div class="time-options">
          <div class="option-group">
            <label>Time Unit:</label>
            <select v-model="timeUnit" @change="updateGrouping">
              <option value="day">Day</option>
              <option value="week">Week</option>
              <option value="month">Month</option>
              <option value="quarter">Quarter</option>
              <option value="year">Year</option>
            </select>
          </div>
          
          <div class="option-group">
            <label>Date Range:</label>
            <div class="date-range">
              <div class="date-input">
                <label>From:</label>
                <input type="date" v-model="startDate" @change="updateGrouping">
              </div>
              <div class="date-input">
                <label>To:</label>
                <input type="date" v-model="endDate" @change="updateGrouping">
              </div>
            </div>
          </div>
          
          <div class="option-group">
            <label>Compare With:</label>
            <select v-model="timeComparison" @change="updateGrouping">
              <option value="">No Comparison</option>
              <option value="previous_period">Previous Period</option>
              <option value="previous_year">Previous Year</option>
              <option value="custom">Custom Period</option>
            </select>
          </div>
          
          <div v-if="timeComparison === 'custom'" class="option-group">
            <label>Custom Comparison Range:</label>
            <div class="date-range">
              <div class="date-input">
                <label>From:</label>
                <input type="date" v-model="comparisonStartDate" @change="updateGrouping">
              </div>
              <div class="date-input">
                <label>To:</label>
                <input type="date" v-model="comparisonEndDate" @change="updateGrouping">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Category Dimension Options -->
      <div v-else-if="selectedDimension === 'category'" class="dimension-options">
        <div class="category-options">
          <div class="option-group">
            <label>Category Field:</label>
            <select v-model="categoryField" @change="updateGrouping">
              <option v-for="field in categoryFields" :key="field.id" :value="field.id">
                {{ field.name }}
              </option>
            </select>
          </div>
          
          <div class="option-group">
            <label>Sort By:</label>
            <select v-model="categorySortBy" @change="updateGrouping">
              <option value="name">Name</option>
              <option value="value">Value</option>
              <option value="count">Count</option>
            </select>
          </div>
          
          <div class="option-group">
            <label>Sort Direction:</label>
            <select v-model="categorySortDirection" @change="updateGrouping">
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
          
          <div class="option-group">
            <label>Limit Categories:</label>
            <input 
              type="number" 
              v-model.number="categoryLimit" 
              min="0" 
              max="100"
              @change="updateGrouping"
            >
            <span class="help-text">0 = No limit</span>
          </div>
          
          <div class="option-group">
            <label>Group Small Values:</label>
            <div class="checkbox-group">
              <input 
                type="checkbox" 
                id="group-small-values" 
                v-model="groupSmallValues"
                @change="updateGrouping"
              >
              <label for="group-small-values">Group values less than:</label>
              <input 
                type="number" 
                v-model.number="smallValueThreshold" 
                min="0" 
                max="100"
                :disabled="!groupSmallValues"
                @change="updateGrouping"
              >
              <span class="help-text">%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Location Dimension Options -->
      <div v-else-if="selectedDimension === 'location'" class="dimension-options">
        <div class="location-options">
          <div class="option-group">
            <label>Location Field:</label>
            <select v-model="locationField" @change="updateGrouping">
              <option v-for="field in locationFields" :key="field.id" :value="field.id">
                {{ field.name }}
              </option>
            </select>
          </div>
          
          <div class="option-group">
            <label>Group By:</label>
            <select v-model="locationGroupBy" @change="updateGrouping">
              <option value="country">Country</option>
              <option value="region">Region/State</option>
              <option value="city">City</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Field Dimension Options -->
      <div v-else-if="selectedDimension === 'field'" class="dimension-options">
        <div class="field-options">
          <div class="option-group">
            <label>Field:</label>
            <select v-model="selectedField" @change="updateGrouping">
              <option v-for="field in availableFields" :key="field.id" :value="field.id">
                {{ field.name }}
              </option>
            </select>
          </div>
          
          <div class="option-group">
            <label>Sort By:</label>
            <select v-model="fieldSortBy" @change="updateGrouping">
              <option value="name">Name</option>
              <option value="value">Value</option>
              <option value="count">Count</option>
            </select>
          </div>
          
          <div class="option-group">
            <label>Sort Direction:</label>
            <select v-model="fieldSortDirection" @change="updateGrouping">
              <option value="asc">Ascending</option>
              <option value="desc">Descending</option>
            </select>
          </div>
        </div>
      </div>

      <div v-if="selectedDimension" class="grouping-preview">
        <h5>Grouping Preview</h5>
        <div class="preview-content">
          <div v-if="isLoading" class="loading-preview">
            <div class="spinner-small"></div>
            <span>Loading preview...</span>
          </div>
          <div v-else-if="previewData.length === 0" class="no-preview-data">
            <i class="material-icons">info</i>
            <p>No preview data available</p>
          </div>
          <div v-else class="preview-table-container">
            <table class="preview-table">
              <thead>
                <tr>
                  <th>{{ getGroupColumnName() }}</th>
                  <th>Count</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(group, index) in previewData" :key="index">
                  <td>{{ group.name }}</td>
                  <td>{{ group.count }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GroupingSelector',

  props: {
    value: {
      type: Object,
      default: () => null
    },
    fields: {
      type: Array,
      required: true
    },
    dataSource: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      selectedDimension: this.value ? this.value.dimension : '',
      
      // Time dimension options
      timeUnit: this.value && this.value.dimension === 'time' ? this.value.unit : 'month',
      startDate: this.value && this.value.dimension === 'time' ? this.value.startDate : this.getDefaultStartDate(),
      endDate: this.value && this.value.dimension === 'time' ? this.value.endDate : this.getDefaultEndDate(),
      timeComparison: this.value && this.value.dimension === 'time' ? this.value.comparison : '',
      comparisonStartDate: this.value && this.value.dimension === 'time' ? this.value.comparisonStartDate : '',
      comparisonEndDate: this.value && this.value.dimension === 'time' ? this.value.comparisonEndDate : '',
      
      // Category dimension options
      categoryField: this.value && this.value.dimension === 'category' ? this.value.field : '',
      categorySortBy: this.value && this.value.dimension === 'category' ? this.value.sortBy : 'name',
      categorySortDirection: this.value && this.value.dimension === 'category' ? this.value.sortDirection : 'asc',
      categoryLimit: this.value && this.value.dimension === 'category' ? this.value.limit : 10,
      groupSmallValues: this.value && this.value.dimension === 'category' ? this.value.groupSmallValues : false,
      smallValueThreshold: this.value && this.value.dimension === 'category' ? this.value.smallValueThreshold : 5,
      
      // Location dimension options
      locationField: this.value && this.value.dimension === 'location' ? this.value.field : '',
      locationGroupBy: this.value && this.value.dimension === 'location' ? this.value.groupBy : 'country',
      
      // Field dimension options
      selectedField: this.value && this.value.dimension === 'field' ? this.value.field : '',
      fieldSortBy: this.value && this.value.dimension === 'field' ? this.value.sortBy : 'name',
      fieldSortDirection: this.value && this.value.dimension === 'field' ? this.value.sortDirection : 'asc',
      
      // Preview data
      isLoading: false,
      previewData: []
    };
  },

  computed: {
    availableDimensions() {
      return [
        { id: 'time', name: 'Time' },
        { id: 'category', name: 'Category' },
        { id: 'location', name: 'Location' },
        { id: 'field', name: 'Custom Field' }
      ];
    },
    
    categoryFields() {
      return this.fields.filter(field => 
        field.type === 'string' || 
        field.type === 'enum' || 
        field.id === 'category'
      );
    },
    
    locationFields() {
      return this.fields.filter(field => 
        field.id === 'location' || 
        field.id.includes('location') || 
        field.id.includes('country') || 
        field.id.includes('city') || 
        field.id.includes('region')
      );
    },
    
    availableFields() {
      return this.fields.filter(field => 
        field.type !== 'datetime' && 
        field.type !== 'date'
      );
    }
  },

  watch: {
    value(newValue) {
      if (!newValue) {
        this.selectedDimension = '';
        return;
      }
      
      this.selectedDimension = newValue.dimension;
      
      if (newValue.dimension === 'time') {
        this.timeUnit = newValue.unit;
        this.startDate = newValue.startDate;
        this.endDate = newValue.endDate;
        this.timeComparison = newValue.comparison;
        this.comparisonStartDate = newValue.comparisonStartDate;
        this.comparisonEndDate = newValue.comparisonEndDate;
      } else if (newValue.dimension === 'category') {
        this.categoryField = newValue.field;
        this.categorySortBy = newValue.sortBy;
        this.categorySortDirection = newValue.sortDirection;
        this.categoryLimit = newValue.limit;
        this.groupSmallValues = newValue.groupSmallValues;
        this.smallValueThreshold = newValue.smallValueThreshold;
      } else if (newValue.dimension === 'location') {
        this.locationField = newValue.field;
        this.locationGroupBy = newValue.groupBy;
      } else if (newValue.dimension === 'field') {
        this.selectedField = newValue.field;
        this.fieldSortBy = newValue.sortBy;
        this.fieldSortDirection = newValue.sortDirection;
      }
    },
    
    selectedDimension() {
      this.loadPreviewData();
    }
  },

  mounted() {
    // Initialize default values based on available fields
    if (this.categoryFields.length > 0 && !this.categoryField) {
      this.categoryField = this.categoryFields[0].id;
    }
    
    if (this.locationFields.length > 0 && !this.locationField) {
      this.locationField = this.locationFields[0].id;
    }
    
    if (this.availableFields.length > 0 && !this.selectedField) {
      this.selectedField = this.availableFields[0].id;
    }
    
    // Load preview data if dimension is selected
    if (this.selectedDimension) {
      this.loadPreviewData();
    }
  },

  methods: {
    getDefaultStartDate() {
      const date = new Date();
      date.setMonth(date.getMonth() - 3);
      return date.toISOString().split('T')[0];
    },
    
    getDefaultEndDate() {
      return new Date().toISOString().split('T')[0];
    },
    
    onDimensionChange() {
      // Reset dimension-specific options when dimension changes
      if (this.selectedDimension === 'time') {
        this.timeUnit = 'month';
        this.startDate = this.getDefaultStartDate();
        this.endDate = this.getDefaultEndDate();
        this.timeComparison = '';
      } else if (this.selectedDimension === 'category') {
        if (this.categoryFields.length > 0) {
          this.categoryField = this.categoryFields[0].id;
        }
        this.categorySortBy = 'name';
        this.categorySortDirection = 'asc';
        this.categoryLimit = 10;
        this.groupSmallValues = false;
        this.smallValueThreshold = 5;
      } else if (this.selectedDimension === 'location') {
        if (this.locationFields.length > 0) {
          this.locationField = this.locationFields[0].id;
        }
        this.locationGroupBy = 'country';
      } else if (this.selectedDimension === 'field') {
        if (this.availableFields.length > 0) {
          this.selectedField = this.availableFields[0].id;
        }
        this.fieldSortBy = 'name';
        this.fieldSortDirection = 'asc';
      }
      
      this.updateGrouping();
    },
    
    updateGrouping() {
      let groupingConfig = null;
      
      if (this.selectedDimension === 'time') {
        groupingConfig = {
          dimension: 'time',
          unit: this.timeUnit,
          startDate: this.startDate,
          endDate: this.endDate,
          comparison: this.timeComparison,
          comparisonStartDate: this.comparisonStartDate,
          comparisonEndDate: this.comparisonEndDate
        };
      } else if (this.selectedDimension === 'category') {
        groupingConfig = {
          dimension: 'category',
          field: this.categoryField,
          sortBy: this.categorySortBy,
          sortDirection: this.categorySortDirection,
          limit: this.categoryLimit,
          groupSmallValues: this.groupSmallValues,
          smallValueThreshold: this.smallValueThreshold
        };
      } else if (this.selectedDimension === 'location') {
        groupingConfig = {
          dimension: 'location',
          field: this.locationField,
          groupBy: this.locationGroupBy
        };
      } else if (this.selectedDimension === 'field') {
        groupingConfig = {
          dimension: 'field',
          field: this.selectedField,
          sortBy: this.fieldSortBy,
          sortDirection: this.fieldSortDirection
        };
      }
      
      this.$emit('input', groupingConfig);
      this.$emit('change', groupingConfig);
      
      // Load preview data
      this.loadPreviewData();
    },
    
    clearGrouping() {
      this.selectedDimension = '';
      this.$emit('input', null);
      this.$emit('change', null);
      this.previewData = [];
    },
    
    async loadPreviewData() {
      if (!this.selectedDimension) {
        this.previewData = [];
        return;
      }
      
      this.isLoading = true;
      
      try {
        // This will be replaced with actual API call
        // const response = await fetch(`/api/analytics/grouping-preview?dataSource=${this.dataSource}&dimension=${this.selectedDimension}&config=${JSON.stringify(this.value)}`);
        // const data = await response.json();
        
        // Mock data for now
        setTimeout(() => {
          this.previewData = this.generateMockPreviewData();
          this.isLoading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading preview data:', error);
        this.previewData = [];
        this.isLoading = false;
      }
    },
    
    generateMockPreviewData() {
      const mockData = [];
      const count = 5;
      
      if (this.selectedDimension === 'time') {
        const timeFormats = {
          day: { format: 'MMM D, YYYY', increment: 1, unit: 'day' },
          week: { format: 'Week of MMM D, YYYY', increment: 7, unit: 'day' },
          month: { format: 'MMM YYYY', increment: 1, unit: 'month' },
          quarter: { format: 'Q[Q] YYYY', increment: 3, unit: 'month' },
          year: { format: 'YYYY', increment: 1, unit: 'year' }
        };
        
        const format = timeFormats[this.timeUnit].format;
        const increment = timeFormats[this.timeUnit].increment;
        const unit = timeFormats[this.timeUnit].unit;
        
        const date = new Date(this.startDate);
        
        for (let i = 0; i < count; i++) {
          let name = '';
          
          if (this.timeUnit === 'day') {
            name = `${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
          } else if (this.timeUnit === 'week') {
            name = `Week of ${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
          } else if (this.timeUnit === 'month') {
            name = `${date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}`;
          } else if (this.timeUnit === 'quarter') {
            const quarter = Math.floor(date.getMonth() / 3) + 1;
            name = `Q${quarter} ${date.getFullYear()}`;
          } else if (this.timeUnit === 'year') {
            name = `${date.getFullYear()}`;
          }
          
          mockData.push({
            name,
            count: Math.floor(Math.random() * 1000) + 100
          });
          
          if (unit === 'day') {
            date.setDate(date.getDate() + increment);
          } else if (unit === 'month') {
            date.setMonth(date.getMonth() + increment);
          } else if (unit === 'year') {
            date.setFullYear(date.getFullYear() + increment);
          }
        }
      } else if (this.selectedDimension === 'category') {
        const categories = ['Furniture', 'Lighting', 'Decor', 'Outdoor', 'Office', 'Kitchen', 'Bathroom', 'Bedroom'];
        
        for (let i = 0; i < Math.min(count, categories.length); i++) {
          mockData.push({
            name: categories[i],
            count: Math.floor(Math.random() * 1000) + 100
          });
        }
      } else if (this.selectedDimension === 'location') {
        const locations = {
          country: ['United States', 'Canada', 'United Kingdom', 'Australia', 'Germany'],
          region: ['California', 'New York', 'Texas', 'Florida', 'Illinois'],
          city: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix']
        };
        
        const locationList = locations[this.locationGroupBy] || locations.country;
        
        for (let i = 0; i < Math.min(count, locationList.length); i++) {
          mockData.push({
            name: locationList[i],
            count: Math.floor(Math.random() * 1000) + 100
          });
        }
      } else if (this.selectedDimension === 'field') {
        const field = this.fields.find(f => f.id === this.selectedField);
        
        if (field && field.type === 'enum' && field.options) {
          for (let i = 0; i < Math.min(count, field.options.length); i++) {
            mockData.push({
              name: field.options[i].label,
              count: Math.floor(Math.random() * 1000) + 100
            });
          }
        } else {
          for (let i = 0; i < count; i++) {
            mockData.push({
              name: `Value ${i + 1}`,
              count: Math.floor(Math.random() * 1000) + 100
            });
          }
        }
      }
      
      return mockData;
    },
    
    getGroupColumnName() {
      if (this.selectedDimension === 'time') {
        return this.timeUnit.charAt(0).toUpperCase() + this.timeUnit.slice(1);
      } else if (this.selectedDimension === 'category') {
        const field = this.fields.find(f => f.id === this.categoryField);
        return field ? field.name : 'Category';
      } else if (this.selectedDimension === 'location') {
        return this.locationGroupBy.charAt(0).toUpperCase() + this.locationGroupBy.slice(1);
      } else if (this.selectedDimension === 'field') {
        const field = this.fields.find(f => f.id === this.selectedField);
        return field ? field.name : 'Field';
      }
      
      return 'Group';
    }
  }
};
</script>

<style scoped>
.grouping-selector {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.grouping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.grouping-header h5 {
  margin: 0;
}

.grouping-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dimension-selector {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.dimension-options {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.time-options,
.category-options,
.location-options,
.field-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.date-range {
  display: flex;
  gap: 10px;
}

.date-input {
  display: flex;
  flex-direction: column;
  gap: 5px;
  flex: 1;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.checkbox-group input[type="number"] {
  width: 60px;
}

.help-text {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-left: 5px;
}

select, input {
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

input[type="checkbox"] {
  width: auto;
}

.grouping-preview {
  margin-top: 10px;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  overflow: hidden;
}

.grouping-preview h5 {
  margin: 0;
  padding: 10px 15px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
}

.preview-content {
  padding: 15px;
}

.loading-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--theme--foreground-subdued);
}

.spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid var(--theme--background);
  border-top: 2px solid var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-preview-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.no-preview-data i {
  font-size: 48px;
  margin-bottom: 10px;
}

.preview-table-container {
  overflow-x: auto;
}

.preview-table {
  width: 100%;
  border-collapse: collapse;
}

.preview-table th {
  text-align: left;
  padding: 8px;
  border-bottom: 1px solid var(--theme--border-color);
  font-weight: bold;
  color: var(--theme--foreground-subdued);
}

.preview-table td {
  padding: 8px;
  border-bottom: 1px solid var(--theme--border-color-subdued);
}
</style>
