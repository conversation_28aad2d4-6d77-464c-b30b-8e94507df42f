<template>
  <div class="realtime-visualization">
    <div class="visualization-header">
      <h3>Real-time Data Visualization</h3>
      <div class="controls">
        <div class="select-wrapper">
          <label for="showroom-select">Showroom:</label>
          <select id="showroom-select" v-model="selectedShowroom" @change="loadRealtimeData">
            <option value="all">All Showrooms</option>
            <option v-for="showroom in showrooms" :key="showroom.id" :value="showroom.id">
              {{ showroom.name }}
            </option>
          </select>
        </div>
        <div class="select-wrapper">
          <label for="interval-select">Interval:</label>
          <select id="interval-select" v-model="selectedInterval" @change="loadRealtimeData">
            <option value="5m">Last 5 minutes</option>
            <option value="15m">Last 15 minutes</option>
            <option value="30m">Last 30 minutes</option>
            <option value="1h">Last hour</option>
          </select>
        </div>
        <button class="refresh-button" @click="loadRealtimeData" :disabled="loading">
          <i class="material-icons">refresh</i>
          Refresh
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-indicator">
      <div class="spinner"></div>
      <p>Loading real-time data...</p>
    </div>

    <div v-else class="visualization-content">
      <!-- Active Users Card -->
      <div class="metric-card active-users">
        <div class="metric-icon">
          <i class="material-icons">people</i>
        </div>
        <div class="metric-content">
          <div class="metric-title">Active Users</div>
          <div class="metric-value">{{ realtimeData.activeUsers || 0 }}</div>
          <div class="metric-subtitle">Currently in showroom</div>
        </div>
      </div>

      <!-- Sessions Card -->
      <div class="metric-card total-sessions">
        <div class="metric-icon">
          <i class="material-icons">timeline</i>
        </div>
        <div class="metric-content">
          <div class="metric-title">Total Sessions</div>
          <div class="metric-value">{{ realtimeData.totalSessions || 0 }}</div>
          <div class="metric-subtitle">In selected time period</div>
        </div>
      </div>

      <!-- Time Series Chart -->
      <div class="chart-container time-series">
        <h4>Session Activity</h4>
        <canvas ref="timeSeriesChart"></canvas>
      </div>

      <!-- Device Distribution Chart -->
      <div class="chart-container device-distribution">
        <h4>Device Distribution</h4>
        <canvas ref="deviceChart"></canvas>
      </div>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'RealtimeVisualization',

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      selectedShowroom: 'all',
      selectedInterval: '5m',
      showrooms: [],
      realtimeData: {
        activeUsers: 0,
        totalSessions: 0,
        timeSeriesData: [],
        deviceData: []
      },
      charts: {
        timeSeries: null,
        device: null
      },
      refreshInterval: null
    };
  },

  mounted() {
    this.loadShowrooms();
    this.loadRealtimeData();
    
    // Set up auto-refresh every 30 seconds
    this.refreshInterval = setInterval(() => {
      this.loadRealtimeData();
    }, 30000);
  },

  beforeDestroy() {
    // Clear the refresh interval when component is destroyed
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
    
    // Destroy charts to prevent memory leaks
    if (this.charts.timeSeries) {
      this.charts.timeSeries.destroy();
    }
    if (this.charts.device) {
      this.charts.device.destroy();
    }
  },

  methods: {
    // Load showroom data
    async loadShowrooms() {
      try {
        const response = await fetch(`/api/showrooms?vendor_id=${this.vendorId}`);
        const data = await response.json();
        
        if (data.success) {
          this.showrooms = data.data;
        } else {
          console.error('Error loading showrooms:', data.error);
          this.showrooms = [];
        }
      } catch (error) {
        console.error('Error loading showrooms:', error);
        this.showrooms = [];
      }
    },
    
    // Load real-time data
    async loadRealtimeData() {
      this.loading = true;
      
      try {
        const response = await fetch(
          `/api/analytics/realtime-visualization?vendor_id=${this.vendorId}&showroom_id=${this.selectedShowroom}&interval=${this.selectedInterval}`
        );
        const data = await response.json();
        
        if (data.success) {
          this.realtimeData = data.data;
          this.initCharts();
        } else {
          console.error('Error loading real-time data:', data.error);
          // Fall back to empty data
          this.realtimeData = {
            activeUsers: 0,
            totalSessions: 0,
            timeSeriesData: [],
            deviceData: []
          };
        }
      } catch (error) {
        console.error('Error loading real-time data:', error);
        // Fall back to empty data
        this.realtimeData = {
          activeUsers: 0,
          totalSessions: 0,
          timeSeriesData: [],
          deviceData: []
        };
      } finally {
        this.loading = false;
      }
    },
    
    // Initialize charts
    initCharts() {
      this.initTimeSeriesChart();
      this.initDeviceChart();
    },
    
    // Initialize time series chart
    initTimeSeriesChart() {
      if (this.$refs.timeSeriesChart) {
        const ctx = this.$refs.timeSeriesChart.getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts.timeSeries) {
          this.charts.timeSeries.destroy();
        }
        
        // Format labels for better display
        const labels = this.realtimeData.timeSeriesData.map(item => {
          const date = new Date(item.time);
          return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        });
        
        // Create new chart
        this.charts.timeSeries = new Chart(ctx, {
          type: 'line',
          data: {
            labels: labels,
            datasets: [{
              label: 'Sessions',
              data: this.realtimeData.timeSeriesData.map(item => item.count),
              borderColor: 'rgba(75, 192, 192, 1)',
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              borderWidth: 2,
              tension: 0.4,
              fill: true
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  precision: 0
                }
              }
            },
            plugins: {
              tooltip: {
                mode: 'index',
                intersect: false
              },
              legend: {
                display: false
              }
            }
          }
        });
      }
    },
    
    // Initialize device chart
    initDeviceChart() {
      if (this.$refs.deviceChart) {
        const ctx = this.$refs.deviceChart.getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts.device) {
          this.charts.device.destroy();
        }
        
        // Define colors for different device types
        const backgroundColors = [
          'rgba(54, 162, 235, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(255, 206, 86, 0.7)',
          'rgba(75, 192, 192, 0.7)',
          'rgba(153, 102, 255, 0.7)'
        ];
        
        // Create new chart
        this.charts.device = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: this.realtimeData.deviceData.map(item => item.device),
            datasets: [{
              data: this.realtimeData.deviceData.map(item => item.count),
              backgroundColor: backgroundColors.slice(0, this.realtimeData.deviceData.length),
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'right'
              }
            }
          }
        });
      }
    }
  }
};
</script>

<style scoped>
.realtime-visualization {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.controls {
  display: flex;
  gap: 15px;
}

.select-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

select {
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.refresh-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.visualization-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto 1fr;
  gap: 20px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  color: white;
}

.active-users .metric-icon {
  background-color: #3498db;
}

.total-sessions .metric-icon {
  background-color: #2ecc71;
}

.metric-title {
  font-size: 14px;
  color: #666;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin: 5px 0;
}

.metric-subtitle {
  font-size: 12px;
  color: #999;
}

.chart-container {
  grid-column: span 2;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  height: 300px;
}

.chart-container h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.time-series {
  grid-column: span 2;
}

.device-distribution {
  grid-column: span 2;
}

@media (min-width: 1200px) {
  .visualization-content {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .time-series {
    grid-column: span 3;
  }
  
  .device-distribution {
    grid-column: span 1;
  }
}
</style>
