import React from 'react';
import { Box, TextField, Typography } from '@mui/material';

interface VectorInputProps {
  label: string;
  value: number[];
  onChange: (value: number[]) => void;
  min?: number;
  max?: number;
  step?: number;
}

const VectorInput: React.FC<VectorInputProps> = ({ 
  label, 
  value, 
  onChange, 
  min = -1000, 
  max = 1000, 
  step = 0.1 
}) => {
  const handleChange = (index: number, newValue: string) => {
    const numValue = parseFloat(newValue);
    if (isNaN(numValue)) return;
    
    const newVector = [...value];
    newVector[index] = numValue;
    onChange(newVector);
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" gutterBottom>{label}</Typography>
      <Box sx={{ display: 'flex', gap: 1 }}>
        <TextField
          label="X"
          type="number"
          value={value[0]}
          onChange={(e) => handleChange(0, e.target.value)}
          inputProps={{ min, max, step }}
          size="small"
          sx={{ flex: 1 }}
        />
        <TextField
          label="Y"
          type="number"
          value={value[1]}
          onChange={(e) => handleChange(1, e.target.value)}
          inputProps={{ min, max, step }}
          size="small"
          sx={{ flex: 1 }}
        />
        <TextField
          label="Z"
          type="number"
          value={value[2]}
          onChange={(e) => handleChange(2, e.target.value)}
          inputProps={{ min, max, step }}
          size="small"
          sx={{ flex: 1 }}
        />
      </Box>
    </Box>
  );
};

export default VectorInput;
