# 🚀 MVS-VR DigitalOcean Deployment Checker Guide

## 📋 Overview

The `Check-MVS-VR-Deployment.ps1` script is a comprehensive PowerShell tool that:
- Connects to your DigitalOcean server via SSH
- Checks all aspects of your MVS-VR deployment
- Automatically fixes common issues
- Tests all endpoints and connectivity
- Generates detailed status reports

## 🔧 Prerequisites

### 1. SSH Key Setup
- SSH key must be located at: `C:\Users\<USER>\.ssh\mvs-vr`
- Key should have proper permissions for SSH access
- Server user: `<EMAIL>`
- Server IP: `**************`

### 2. PowerShell Requirements
- Windows PowerShell 5.1 or PowerShell Core 7+
- SSH client installed (available in Windows 10/11 by default)
- SCP command available for file transfers

### 3. Network Access
- Outbound SSH access (port 22) to your DigitalOcean server
- Outbound HTTP/HTTPS access for testing endpoints

## 🚀 Quick Start

### Method 1: Using Batch File (Easiest)
```cmd
# Navigate to docker-exports directory
cd docker-exports

# Run the batch file
run-deployment-check.bat
```

### Method 2: Direct PowerShell
```powershell
# Navigate to docker-exports directory
cd docker-exports

# Run with default settings
.\Check-MVS-VR-Deployment.ps1

# Run with custom parameters
.\Check-MVS-VR-Deployment.ps1 -ServerIP "**************" -AutoFix $true
```

## 📊 What the Script Tests

### 🔐 1. SSH Connection
- Tests SSH connectivity to your server
- Verifies authentication with SSH key
- **Auto-fix**: None (manual SSH setup required)

### 🐳 2. Docker Installation
- Checks if Docker is installed and running
- Verifies Docker Compose availability
- **Auto-fix**: Installs Docker and Docker Compose if missing

### 📁 3. Project Deployment
- Verifies project files exist on server
- Checks for docker-compose.exported.yml
- **Auto-fix**: Copies project files via SCP if missing

### 🏃 4. Docker Containers
- Lists all containers and their status
- Checks if services are running
- **Auto-fix**: Starts stopped containers

### 🌐 5. Port 80 Access
- Tests local port 80 accessibility
- Tests external port 80 accessibility
- **Auto-fix**: Configures firewall and restarts services

### 🔗 6. Domain Resolution
- Tests if mvs.kanousai.com resolves to correct IP
- Verifies DNS configuration
- **Auto-fix**: None (DNS must be configured externally)

### 🗄️ 7. Supabase Connectivity
- Tests connection to remote Supabase instance
- Verifies API endpoints are reachable
- **Auto-fix**: None (checks connectivity only)

### 🏥 8. Health Endpoints
- Tests /health endpoint locally and externally
- Tests main page accessibility
- Verifies response codes
- **Auto-fix**: Restarts services if endpoints fail

### 📝 9. Service Logs
- Checks logs for errors in all services
- Identifies problematic services
- **Auto-fix**: None (reports issues for manual review)

### 💻 10. System Resources
- Monitors disk space usage
- Checks memory consumption
- Reviews Docker container resource usage
- **Auto-fix**: None (monitoring only)

## 📋 Script Parameters

```powershell
.\Check-MVS-VR-Deployment.ps1 [parameters]
```

### Available Parameters:
- **`-ServerIP`**: DigitalOcean server IP (default: "**************")
- **`-Username`**: SSH username (default: "vectorax")
- **`-SSHKeyPath`**: Path to SSH private key (default: "C:\Users\<USER>\.ssh\mvs-vr")
- **`-Domain`**: Domain name to test (default: "mvs.kanousai.com")
- **`-ProjectPath`**: Server project path (default: "/home/<USER>/mvs-vr-deployment")
- **`-AutoFix`**: Enable automatic fixes (default: $true)
- **`-OutputFile`**: Report filename (default: auto-generated with timestamp)

### Example Usage:
```powershell
# Basic run with defaults
.\Check-MVS-VR-Deployment.ps1

# Custom server and disable auto-fix
.\Check-MVS-VR-Deployment.ps1 -ServerIP "*************" -AutoFix $false

# Custom output file
.\Check-MVS-VR-Deployment.ps1 -OutputFile "my-deployment-report.txt"
```

## 📄 Output Report

The script generates a comprehensive report including:

### Report Sections:
1. **Test Results**: Detailed log of all tests performed
2. **Fixes Applied**: List of automatic fixes that were applied
3. **Summary**: Overview of test results and fix count
4. **Next Steps**: Recommended actions for any remaining issues

### Sample Report Structure:
```
MVS-VR DigitalOcean Deployment Status Report
Generated: 2025-06-01 10:30:00
Server: **************
Domain: mvs.kanousai.com

=== TEST RESULTS ===
10:30:01 - ✅ SUCCESS: SSH connection established
10:30:02 - ✅ SUCCESS: Docker is installed
10:30:03 - ⚠️ WARNING: Some containers not running
10:30:04 - ✅ SUCCESS: Port 80 accessible externally
...

=== FIXES APPLIED ===
Starting Docker containers
Configuring firewall for port 80
...

=== SUMMARY ===
Total Tests: 10
Fixes Applied: 2
```

## 🔧 Troubleshooting

### Common Issues:

#### 1. SSH Connection Failed
```
❌ ERROR: Cannot establish SSH connection
```
**Solutions:**
- Verify SSH key exists at `C:\Users\<USER>\.ssh\mvs-vr`
- Check SSH key permissions
- Ensure server IP is correct
- Verify server is running

#### 2. Permission Denied
```
❌ ERROR: SSH Command failed: Permission denied
```
**Solutions:**
- Check SSH key has correct permissions
- Verify username is correct (vectorax)
- Ensure SSH key is added to server's authorized_keys

#### 3. Docker Not Found
```
❌ ERROR: Docker not found
```
**Solutions:**
- Script will auto-install Docker if AutoFix is enabled
- Manually install Docker on server if auto-fix fails

#### 4. Port 80 Not Accessible
```
⚠️ WARNING: Port 80 not accessible externally
```
**Solutions:**
- Script will configure firewall automatically
- Check DigitalOcean firewall settings
- Verify nginx container is running

#### 5. Domain Resolution Failed
```
⚠️ WARNING: Domain mvs.kanousai.com does not resolve
```
**Solutions:**
- Configure DNS A record: mvs.kanousai.com → **************
- Wait for DNS propagation (up to 24 hours)
- Use DNS checker tools to verify

## 🔄 Regular Monitoring

### Recommended Schedule:
- **Daily**: Run basic health checks
- **Weekly**: Full deployment verification
- **After Changes**: Always run after code deployments

### Automated Monitoring:
You can schedule the script to run automatically:

```powershell
# Create scheduled task (run as administrator)
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File C:\path\to\Check-MVS-VR-Deployment.ps1"
$trigger = New-ScheduledTaskTrigger -Daily -At "09:00"
Register-ScheduledTask -TaskName "MVS-VR-Health-Check" -Action $action -Trigger $trigger
```

## 📞 Support

If you encounter issues:
1. Check the generated report for detailed error messages
2. Review the troubleshooting section above
3. Verify all prerequisites are met
4. Check server logs manually if needed

## 🔐 Security Notes

- SSH keys should be kept secure and not shared
- The script uses read-only operations where possible
- Auto-fixes only apply standard configurations
- No sensitive data is logged in reports
