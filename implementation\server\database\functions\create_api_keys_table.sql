-- Function to create the api_keys table
CREATE OR REPLACE FUNCTION create_api_keys_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create the api_keys table if it doesn't exist
  CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    key_hash VARCHAR(64) NOT NULL UNIQUE,
    permissions TEXT[] DEFAULT '{}',
    scopes TEXT[] DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_used TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    enabled BOOLEAN DEFAULT TRUE
  );

  -- Add comment to the table
  COMMENT ON TABLE api_keys IS 'API keys for authenticating API requests';

  -- Add comments to columns
  COMMENT ON COLUMN api_keys.id IS 'Unique identifier for the API key';
  COMMENT ON COLUMN api_keys.user_id IS 'User ID that owns the API key';
  COMMENT ON COLUMN api_keys.name IS 'Human-readable name for the API key';
  COMMENT ON COLUMN api_keys.key_hash IS 'Hashed value of the API key for secure storage';
  COMMENT ON COLUMN api_keys.permissions IS 'Array of permissions granted to the API key';
  COMMENT ON COLUMN api_keys.scopes IS 'Array of scopes granted to the API key';
  COMMENT ON COLUMN api_keys.created_at IS 'Timestamp when the API key was created';
  COMMENT ON COLUMN api_keys.last_used IS 'Timestamp when the API key was last used';
  COMMENT ON COLUMN api_keys.expires_at IS 'Timestamp when the API key expires (null for no expiration)';
  COMMENT ON COLUMN api_keys.enabled IS 'Whether the API key is enabled';
END;
$$;
