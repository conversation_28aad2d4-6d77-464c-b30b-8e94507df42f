/**
 * Telemetry endpoint for collecting performance metrics
 */
module.exports = {
  id: 'telemetry',
  handler: (router, { services, exceptions }) => {
    const { ItemsService } = services;
    const { ForbiddenException } = exceptions;

    /**
     * POST /telemetry
     * Receive and store telemetry data
     */
    router.post('/', async (req, res, next) => {
      try {
        // Check authentication
        if (!req.accountability?.user) {
          throw new ForbiddenException();
        }

        const telemetryData = req.body;

        // Add user information
        telemetryData.user_id = req.accountability.user;
        telemetryData.ip_address = req.ip;
        telemetryData.user_agent = req.get('User-Agent');

        // Store telemetry data
        const telemetryService = new ItemsService('telemetry', {
          schema: req.schema,
          accountability: req.accountability,
        });

        // Store summary metrics
        const summaryData = {
          component_name: telemetryData.componentName,
          session_id: telemetryData.sessionId,
          session_duration: telemetryData.sessionDuration,
          timestamp: telemetryData.timestamp,
          user_id: telemetryData.user_id,
          ip_address: telemetryData.ip_address,
          user_agent: telemetryData.user_agent,
          average_render_time: telemetryData.summary.averageRenderTime,
          average_api_load_time: telemetryData.summary.averageApiLoadTime,
          average_cache_load_time: telemetryData.summary.averageCacheLoadTime,
          cache_hit_ratio: telemetryData.summary.cacheHitRatio,
          average_memory_usage: telemetryData.summary.averageMemoryUsage,
          average_memory_usage_ratio: telemetryData.summary.averageMemoryUsageRatio,
          total_errors: telemetryData.summary.totalErrors,
          performance_improvement: telemetryData.summary.performanceImprovement,
        };

        await telemetryService.createOne(summaryData);

        // Store detailed metrics if needed
        if (telemetryData.metrics.errors.length > 0) {
          const errorService = new ItemsService('telemetry_errors', {
            schema: req.schema,
            accountability: req.accountability,
          });

          for (const error of telemetryData.metrics.errors) {
            await errorService.createOne({
              session_id: telemetryData.sessionId,
              component_name: telemetryData.componentName,
              user_id: telemetryData.user_id,
              message: error.message,
              details: JSON.stringify(error.details),
              timestamp: error.timestamp,
            });
          }
        }

        res.status(200).json({ success: true });
      } catch (error) {
        next(error);
      }
    });

    /**
     * GET /telemetry/stats
     * Get aggregated telemetry statistics
     */
    router.get('/stats', async (req, res, next) => {
      try {
        // Check authentication and permissions
        if (!req.accountability?.admin) {
          throw new ForbiddenException();
        }

        const knex = req.knex;

        // Get aggregated statistics
        const stats = await knex('telemetry')
          .select(
            'component_name',
            knex.raw('AVG(average_render_time) as avg_render_time'),
            knex.raw('AVG(average_api_load_time) as avg_api_load_time'),
            knex.raw('AVG(average_cache_load_time) as avg_cache_load_time'),
            knex.raw('AVG(cache_hit_ratio) as avg_cache_hit_ratio'),
            knex.raw('AVG(performance_improvement) as avg_performance_improvement'),
            knex.raw('COUNT(DISTINCT session_id) as session_count'),
            knex.raw('COUNT(DISTINCT user_id) as user_count'),
            knex.raw('SUM(total_errors) as total_errors'),
          )
          .groupBy('component_name');

        res.status(200).json(stats);
      } catch (error) {
        next(error);
      }
    });

    /**
     * GET /telemetry/trends
     * Get telemetry trends over time
     */
    router.get('/trends', async (req, res, next) => {
      try {
        // Check authentication and permissions
        if (!req.accountability?.admin) {
          throw new ForbiddenException();
        }

        const knex = req.knex;
        const { component, period = 'day', days = 30 } = req.query;

        // Define time period format
        let timeFormat;
        switch (period) {
          case 'hour':
            timeFormat = 'YYYY-MM-DD HH:00:00';
            break;
          case 'day':
            timeFormat = 'YYYY-MM-DD';
            break;
          case 'week':
            timeFormat = 'YYYY-WW';
            break;
          case 'month':
            timeFormat = 'YYYY-MM';
            break;
          default:
            timeFormat = 'YYYY-MM-DD';
        }

        // Build query
        let query = knex('telemetry')
          .select(
            knex.raw(`DATE_FORMAT(timestamp, '${timeFormat}') as time_period`),
            knex.raw('AVG(average_render_time) as avg_render_time'),
            knex.raw('AVG(average_api_load_time) as avg_api_load_time'),
            knex.raw('AVG(average_cache_load_time) as avg_cache_load_time'),
            knex.raw('AVG(cache_hit_ratio) as avg_cache_hit_ratio'),
            knex.raw('AVG(performance_improvement) as avg_performance_improvement'),
          )
          .where(knex.raw(`timestamp >= DATE_SUB(NOW(), INTERVAL ${days} DAY)`));

        // Filter by component if specified
        if (component) {
          query = query.where('component_name', component);
        }

        // Group by time period
        query = query.groupBy('time_period').orderBy('time_period');

        const trends = await query;

        res.status(200).json(trends);
      } catch (error) {
        next(error);
      }
    });

    return router;
  },
};
