/**
 * LLM Conversation Detail API
 *
 * This module provides API endpoints for LLM conversation detail operations.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { llmService, LLMMessageRole } from '../../services/llm/llm-service';

// Create logger instance
const apiLogger = new Logger();

/**
 * Get conversation handler
 *
 * @param req Request
 * @param res Response
 */
async function getConversation(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { id } = req.query;

    // Get conversation
    try {
      const conversation = await llmService.getConversation(id as string);

      // Check if user is authorized to access this conversation
      if (conversation.user_id !== session.user.id && session.user.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'You are not authorized to access this conversation',
          },
        });
        return;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: conversation,
      });
    } catch (error) {
      logger.error('Error getting conversation', { error });
      res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: 'Conversation not found',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in getConversation', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Update conversation handler
 *
 * @param req Request
 * @param res Response
 */
async function updateConversation(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { id } = req.query;
    const { title, metadata } = req.body;

    // Get conversation to check authorization
    try {
      const conversation = await llmService.getConversation(id as string);

      // Check if user is authorized to update this conversation
      if (conversation.user_id !== session.user.id && session.user.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'You are not authorized to update this conversation',
          },
        });
        return;
      }

      // Update conversation
      const { data: updatedConversation, error } = await supabase
        .from('llm_conversations')
        .update({
          ...(title && { title }),
          ...(metadata && { metadata: { ...conversation.metadata, ...metadata } }),
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        logger.error('Error updating conversation', { error });
        res.status(500).json({
          success: false,
          error: {
            code: 'UPDATE_ERROR',
            message: 'Failed to update conversation',
          },
        });
        return;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: updatedConversation,
      });
    } catch (error) {
      logger.error('Error updating conversation', { error });
      res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: 'Conversation not found',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in updateConversation', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Delete conversation handler
 *
 * @param req Request
 * @param res Response
 */
async function deleteConversation(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { id } = req.query;

    // Get conversation to check authorization
    try {
      const conversation = await llmService.getConversation(id as string);

      // Check if user is authorized to delete this conversation
      if (conversation.user_id !== session.user.id && session.user.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'You are not authorized to delete this conversation',
          },
        });
        return;
      }

      // Delete conversation
      const { error } = await supabase.from('llm_conversations').delete().eq('id', id);

      if (error) {
        logger.error('Error deleting conversation', { error });
        res.status(500).json({
          success: false,
          error: {
            code: 'DELETE_ERROR',
            message: 'Failed to delete conversation',
          },
        });
        return;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: {
          id,
          deleted: true,
        },
      });
    } catch (error) {
      logger.error('Error deleting conversation', { error });
      res.status(404).json({
        success: false,
        error: {
          code: 'CONVERSATION_NOT_FOUND',
          message: 'Conversation not found',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in deleteConversation', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Handler for LLM conversation detail API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'GET':
      await getConversation(req, res);
      break;
    case 'PUT':
      await updateConversation(req, res);
      break;
    case 'DELETE':
      await deleteConversation(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
