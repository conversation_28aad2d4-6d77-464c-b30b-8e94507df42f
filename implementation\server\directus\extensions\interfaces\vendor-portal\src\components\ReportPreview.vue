<template>
  <div class="report-preview">
    <div class="preview-header">
      <div class="view-selector">
        <button
          class="view-button"
          :class="{ active: viewMode === 'table' }"
          @click="setViewMode('table')"
          title="Table View"
        >
          <i class="material-icons">table_chart</i>
          <span>Table</span>
        </button>
        <button
          class="view-button"
          :class="{ active: viewMode === 'chart' }"
          @click="setViewMode('chart')"
          title="Chart View"
        >
          <i class="material-icons">bar_chart</i>
          <span>Chart</span>
        </button>
      </div>

      <div class="preview-actions">
        <button class="btn btn-secondary" @click="refreshData" title="Refresh Data">
          <i class="material-icons">refresh</i> Refresh
        </button>
        <button class="btn btn-primary" @click="exportData" title="Export Data">
          <i class="material-icons">download</i> Export
        </button>
      </div>
    </div>

    <div class="preview-content">
      <!-- Table View -->
      <ReportTableView
        v-if="viewMode === 'table'"
        :data="reportData"
        :columns="reportColumns"
        :loading="loading"
        @refresh="refreshData"
        @export="showExportOptions"
        @row-select="onRowSelect"
      />

      <!-- Chart View -->
      <ReportChartView
        v-else-if="viewMode === 'chart'"
        ref="chartView"
        :data="reportData"
        :columns="reportColumns"
        :grouping="reportConfig.grouping"
        :loading="loading"
        @refresh="refreshData"
        @export="showExportOptions"
      />

      <!-- No Data View -->
      <div v-else class="no-preview">
        <i class="material-icons">info</i>
        <p>Select a view mode to preview your report</p>
      </div>
    </div>

    <div v-if="showExportPanel" class="export-panel">
      <div class="export-header">
        <h4>Export Options</h4>
        <button class="btn btn-icon" @click="toggleExportPanel">
          <i class="material-icons">close</i>
        </button>
      </div>

      <div class="export-content">
        <div class="export-option">
          <label>Export Format:</label>
          <select v-model="exportFormat">
            <option value="csv">CSV</option>
            <option value="excel">Excel</option>
            <option value="pdf">PDF</option>
            <option value="image" v-if="viewMode === 'chart'">Image (PNG)</option>
          </select>
        </div>

        <div class="export-option">
          <label>Include Headers:</label>
          <input type="checkbox" v-model="exportOptions.includeHeaders">
        </div>

        <div class="export-option">
          <label>Export Scope:</label>
          <select v-model="exportOptions.scope">
            <option value="all">All Data</option>
            <option value="visible">Visible Data Only</option>
            <option value="selected" :disabled="!hasSelectedRows">Selected Rows Only</option>
          </select>
        </div>

        <div class="export-option" v-if="exportFormat === 'pdf'">
          <label>Page Size:</label>
          <select v-model="exportOptions.pageSize">
            <option value="a4">A4</option>
            <option value="letter">Letter</option>
            <option value="legal">Legal</option>
          </select>
        </div>

        <div class="export-option" v-if="exportFormat === 'pdf'">
          <label>Orientation:</label>
          <select v-model="exportOptions.orientation">
            <option value="portrait">Portrait</option>
            <option value="landscape">Landscape</option>
          </select>
        </div>
      </div>

      <div class="export-footer">
        <button class="btn btn-secondary" @click="toggleExportPanel">Cancel</button>
        <button class="btn btn-secondary" @click="scheduleExport">Schedule</button>
        <button class="btn btn-primary" @click="executeExport">Export</button>
      </div>
    </div>
  </div>
</template>

<script>
import ReportTableView from './ReportTableView.vue';
import ReportChartView from './ReportChartView.vue';

export default {
  name: 'ReportPreview',

  components: {
    ReportTableView,
    ReportChartView
  },

  props: {
    reportConfig: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      viewMode: 'table',
      loading: false,
      reportData: [],
      reportColumns: [],
      selectedRows: [],
      showExportPanel: false,
      exportFormat: 'csv',
      exportOptions: {
        includeHeaders: true,
        scope: 'all',
        pageSize: 'a4',
        orientation: 'portrait'
      },
      error: null
    };
  },

  computed: {
    hasSelectedRows() {
      return this.selectedRows.length > 0;
    }
  },

  watch: {
    reportConfig: {
      handler() {
        this.loadReportData();
      },
      deep: true
    }
  },

  mounted() {
    this.loadReportData();
  },

  methods: {
    setViewMode(mode) {
      this.viewMode = mode;
    },

    async loadReportData() {
      if (!this.reportConfig.dataSource || this.reportConfig.metrics.length === 0) {
        this.reportData = [];
        this.reportColumns = [];
        return;
      }

      this.loading = true;
      this.error = null;

      try {
        // This will be replaced with actual API call
        // const response = await fetch('/api/analytics/report-data', {
        //   method: 'POST',
        //   headers: {
        //     'Content-Type': 'application/json'
        //   },
        //   body: JSON.stringify(this.reportConfig)
        // });
        // const data = await response.json();

        // Mock data for now
        setTimeout(() => {
          this.generateMockData();
          this.loading = false;
        }, 1500);
      } catch (error) {
        console.error('Error loading report data:', error);
        this.error = 'Failed to load report data. Please try again.';
        this.loading = false;
      }
    },

    generateMockData() {
      // Generate columns based on data source and metrics
      this.reportColumns = this.generateMockColumns();

      // Generate rows
      const rowCount = 50;
      const rows = [];

      for (let i = 0; i < rowCount; i++) {
        const row = {};

        // Add ID
        row.id = `row-${i + 1}`;

        // Add dimension field based on grouping
        if (this.reportConfig.grouping) {
          if (this.reportConfig.grouping.dimension === 'time') {
            const date = new Date();
            date.setDate(date.getDate() - i);

            if (this.reportConfig.grouping.unit === 'day') {
              row.time_period = date.toLocaleDateString();
            } else if (this.reportConfig.grouping.unit === 'week') {
              row.time_period = `Week ${Math.ceil((i + 1) / 7)}`;
            } else if (this.reportConfig.grouping.unit === 'month') {
              row.time_period = date.toLocaleDateString(undefined, { month: 'long', year: 'numeric' });
            } else if (this.reportConfig.grouping.unit === 'quarter') {
              const quarter = Math.floor(date.getMonth() / 3) + 1;
              row.time_period = `Q${quarter} ${date.getFullYear()}`;
            } else if (this.reportConfig.grouping.unit === 'year') {
              row.time_period = date.getFullYear().toString();
            }
          } else if (this.reportConfig.grouping.dimension === 'category') {
            const categories = ['Furniture', 'Lighting', 'Decor', 'Outdoor', 'Office'];
            row.category = categories[i % categories.length];
          } else if (this.reportConfig.grouping.dimension === 'location') {
            const locations = ['United States', 'Canada', 'United Kingdom', 'Australia', 'Germany'];
            row.location = locations[i % locations.length];
          }
        }

        // Add data for each column
        this.reportColumns.forEach(column => {
          if (column.id === 'id' || column.id === 'time_period' || column.id === 'category' || column.id === 'location') {
            // Skip ID and dimension columns (already added)
            return;
          }

          if (column.type === 'number') {
            // Generate random number
            row[column.id] = Math.floor(Math.random() * 1000) + 100;
          } else if (column.type === 'date') {
            // Generate random date
            const date = new Date();
            date.setDate(date.getDate() - Math.floor(Math.random() * 30));
            row[column.id] = date.toISOString().split('T')[0];
          } else if (column.type === 'boolean') {
            // Generate random boolean
            row[column.id] = Math.random() > 0.5;
          } else {
            // Generate random string
            row[column.id] = `Value ${i + 1}`;
          }
        });

        rows.push(row);
      }

      this.reportData = rows;
    },

    generateMockColumns() {
      const columns = [];

      // Add ID column
      columns.push({
        id: 'id',
        name: 'ID',
        type: 'string',
        visible: false
      });

      // Add dimension column based on grouping
      if (this.reportConfig.grouping) {
        if (this.reportConfig.grouping.dimension === 'time') {
          columns.push({
            id: 'time_period',
            name: this.reportConfig.grouping.unit.charAt(0).toUpperCase() + this.reportConfig.grouping.unit.slice(1),
            type: 'string',
            visible: true,
            sortable: true
          });
        } else if (this.reportConfig.grouping.dimension === 'category') {
          columns.push({
            id: 'category',
            name: 'Category',
            type: 'string',
            visible: true,
            sortable: true
          });
        } else if (this.reportConfig.grouping.dimension === 'location') {
          columns.push({
            id: 'location',
            name: this.reportConfig.grouping.groupBy.charAt(0).toUpperCase() + this.reportConfig.grouping.groupBy.slice(1),
            type: 'string',
            visible: true,
            sortable: true
          });
        }
      }

      // Add metric columns
      this.reportConfig.metrics.forEach(metricId => {
        // Find metric in available metrics
        const metric = {
          id: metricId,
          name: this.getMetricName(metricId),
          type: 'number',
          visible: true,
          sortable: true,
          decimals: 0
        };

        // Set decimals based on metric type
        if (metricId.includes('rate') || metricId.includes('percentage')) {
          metric.decimals = 2;
        }

        columns.push(metric);
      });

      return columns;
    },

    getMetricName(metricId) {
      // Map metric IDs to readable names
      const metricNames = {
        visits: 'Visits',
        unique_visitors: 'Unique Visitors',
        avg_duration: 'Avg. Duration',
        bounce_rate: 'Bounce Rate',
        conversion_rate: 'Conversion Rate',
        interactions: 'Interactions',
        views: 'Views',
        add_to_cart: 'Add to Cart',
        purchases: 'Purchases'
      };

      return metricNames[metricId] || metricId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    },

    refreshData() {
      this.loadReportData();
    },

    onRowSelect(rows) {
      this.selectedRows = rows;
    },

    showExportOptions() {
      this.showExportPanel = true;
    },

    toggleExportPanel() {
      this.showExportPanel = !this.showExportPanel;
    },

    exportData() {
      this.showExportOptions();
    },

    async executeExport() {
      this.loading = true;

      try {
        if (this.exportFormat === 'image' && this.viewMode === 'chart') {
          // For chart images, use the chart's built-in export functionality
          const chartComponent = this.$refs.chartView;
          if (chartComponent) {
            chartComponent.exportChart();
          }
        } else {
          // For other formats, use the API
          const exportParams = new URLSearchParams({
            format: this.exportFormat,
            include_headers: this.exportOptions.includeHeaders,
            scope: this.exportOptions.scope,
            page_size: this.exportOptions.pageSize,
            orientation: this.exportOptions.orientation
          });

          // Add report config
          const reportConfigParam = encodeURIComponent(JSON.stringify(this.reportConfig));
          exportParams.append('config', reportConfigParam);

          // Create export URL
          const exportUrl = `/api/analytics/export?${exportParams.toString()}`;

          // Open in new window/tab
          window.open(exportUrl, '_blank');

          // Show success message
          this.$buefy.toast.open({
            message: `Exporting report in ${this.exportFormat.toUpperCase()} format...`,
            type: 'is-success'
          });
        }
      } catch (error) {
        console.error('Error exporting report:', error);

        this.$buefy.toast.open({
          message: 'Error exporting report. Please try again.',
          type: 'is-danger'
        });
      } finally {
        this.loading = false;

        // Close export panel
        this.toggleExportPanel();
      }
    },

    // Add export scheduling
    scheduleExport() {
      // Show scheduling dialog
      this.$buefy.dialog.prompt({
        title: 'Schedule Export',
        message: 'How often would you like to receive this report?',
        inputAttrs: {
          type: 'select',
          options: [
            { text: 'Daily', value: 'daily' },
            { text: 'Weekly', value: 'weekly' },
            { text: 'Monthly', value: 'monthly' }
          ]
        },
        confirmText: 'Next',
        cancelText: 'Cancel',
        onConfirm: (frequency) => {
          this.configureSchedule(frequency);
        }
      });
    },

    configureSchedule(frequency) {
      // Configure schedule details based on frequency
      let message = '';
      let options = [];

      if (frequency === 'daily') {
        message = 'Select time of day:';
        options = [
          { text: '6:00 AM', value: '06:00' },
          { text: '12:00 PM', value: '12:00' },
          { text: '6:00 PM', value: '18:00' }
        ];
      } else if (frequency === 'weekly') {
        message = 'Select day of week:';
        options = [
          { text: 'Monday', value: 'monday' },
          { text: 'Wednesday', value: 'wednesday' },
          { text: 'Friday', value: 'friday' }
        ];
      } else if (frequency === 'monthly') {
        message = 'Select day of month:';
        options = [
          { text: '1st', value: '1' },
          { text: '15th', value: '15' },
          { text: 'Last day', value: 'last' }
        ];
      }

      this.$buefy.dialog.prompt({
        title: 'Schedule Export',
        message,
        inputAttrs: {
          type: 'select',
          options
        },
        confirmText: 'Next',
        cancelText: 'Cancel',
        onConfirm: (timeValue) => {
          this.configureDelivery(frequency, timeValue);
        }
      });
    },

    configureDelivery(frequency, timeValue) {
      // Configure delivery method
      this.$buefy.dialog.prompt({
        title: 'Schedule Export',
        message: 'How would you like to receive this report?',
        inputAttrs: {
          type: 'select',
          options: [
            { text: 'Email', value: 'email' },
            { text: 'Download Link', value: 'download' },
            { text: 'Cloud Storage', value: 'storage' }
          ]
        },
        confirmText: 'Next',
        cancelText: 'Cancel',
        onConfirm: (deliveryMethod) => {
          this.finalizeSchedule(frequency, timeValue, deliveryMethod);
        }
      });
    },

    finalizeSchedule(frequency, timeValue, deliveryMethod) {
      // For email delivery, ask for email address
      if (deliveryMethod === 'email') {
        this.$buefy.dialog.prompt({
          title: 'Schedule Export',
          message: 'Enter email address:',
          inputAttrs: {
            type: 'email',
            placeholder: '<EMAIL>',
            required: true
          },
          confirmText: 'Schedule',
          cancelText: 'Cancel',
          onConfirm: (email) => {
            this.saveSchedule(frequency, timeValue, deliveryMethod, email);
          }
        });
      } else {
        this.saveSchedule(frequency, timeValue, deliveryMethod);
      }
    },

    saveSchedule(frequency, timeValue, deliveryMethod, email = null) {
      // Create schedule object
      const schedule = {
        report_config: this.reportConfig,
        format: this.exportFormat,
        frequency,
        time_value: timeValue,
        delivery_method: deliveryMethod,
        email,
        created_at: new Date().toISOString()
      };

      // This would be replaced with actual API call
      // const response = await fetch('/api/analytics/schedules', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json'
      //   },
      //   body: JSON.stringify(schedule)
      // });

      // Mock save for now
      console.log('Saving schedule:', schedule);

      // Show success message
      this.$buefy.toast.open({
        message: `Report scheduled successfully. It will be delivered ${frequency}.`,
        type: 'is-success',
        duration: 5000
      });
    }
  }
};
</script>

<style scoped>
.report-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.view-selector {
  display: flex;
  gap: 10px;
}

.view-button {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 8px 12px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  cursor: pointer;
  transition: all 0.2s;
}

.view-button:hover {
  background-color: var(--theme--background-subdued);
}

.view-button.active {
  background-color: var(--theme--primary);
  color: white;
  border-color: var(--theme--primary);
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.preview-content {
  flex: 1;
  position: relative;
  min-height: 400px;
}

.no-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.no-preview i {
  font-size: 48px;
  margin-bottom: 10px;
}

.export-panel {
  position: absolute;
  top: 50px;
  right: 0;
  width: 300px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  flex-direction: column;
}

.export-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border-bottom: 1px solid var(--theme--border-color);
}

.export-header h4 {
  margin: 0;
}

.export-content {
  padding: 15px;
}

.export-option {
  margin-bottom: 15px;
}

.export-option label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.export-option select,
.export-option input[type="text"] {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.export-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 10px 15px;
  border-top: 1px solid var(--theme--border-color);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  cursor: pointer;
  transition: all 0.2s;
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
}
</style>
