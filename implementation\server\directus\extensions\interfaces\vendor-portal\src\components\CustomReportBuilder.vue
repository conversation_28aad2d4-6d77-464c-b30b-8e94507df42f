<template>
  <div class="custom-report-builder">
    <div class="report-builder-header">
      <div class="header-left">
        <h3>{{ reportName || 'New Custom Report' }}</h3>
        <div v-if="isDirty" class="unsaved-indicator">*</div>
      </div>
      <div class="header-actions">
        <button class="btn btn-secondary" @click="saveReport" :disabled="!isDirty || !isValid">
          <i class="material-icons">save</i> Save
        </button>
        <button class="btn btn-secondary" @click="loadReport">
          <i class="material-icons">folder_open</i> Load
        </button>
        <button class="btn btn-primary" @click="runReport" :disabled="!isValid">
          <i class="material-icons">play_arrow</i> Run Report
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
      <span>{{ loadingMessage }}</span>
    </div>

    <div class="report-builder-content">
      <div class="report-builder-sidebar">
        <div class="steps-container">
          <div
            v-for="(step, index) in steps"
            :key="step.id"
            class="step-item"
            :class="{
              'active': currentStep === index,
              'completed': isStepCompleted(index),
              'disabled': !isStepEnabled(index)
            }"
            @click="goToStep(index)"
          >
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-info">
              <div class="step-name">{{ step.name }}</div>
              <div class="step-description">{{ step.description }}</div>
            </div>
            <div class="step-status">
              <i v-if="isStepCompleted(index)" class="material-icons">check_circle</i>
              <i v-else-if="currentStep === index" class="material-icons">edit</i>
              <i v-else class="material-icons">radio_button_unchecked</i>
            </div>
          </div>
        </div>
      </div>

      <div class="report-builder-main">
        <div class="step-content">
          <!-- Data Source Selection -->
          <div v-if="currentStep === 0" class="step-panel">
            <h4>Select Data Source</h4>
            <p>Choose the primary data source for your report.</p>
            <DataSourceSelector
              :dataSources="dataSources"
              v-model="reportConfig.dataSource"
              :vendorId="vendorId"
              @change="onDataSourceChange"
            />
          </div>

          <!-- Metrics Selection -->
          <div v-if="currentStep === 1" class="step-panel">
            <h4>Select Metrics</h4>
            <p>Choose the metrics you want to include in your report.</p>
            <MetricsSelector
              :metrics="availableMetrics"
              v-model="reportConfig.metrics"
              @change="onMetricsChange"
            />
          </div>

          <!-- Filter Configuration -->
          <div v-if="currentStep === 2" class="step-panel">
            <h4>Configure Filters</h4>
            <p>Add filters to narrow down your data.</p>
            <FilterBuilder
              v-model="reportConfig.filters"
              :fields="availableFields"
              :dataSource="reportConfig.dataSource"
              @change="onFiltersChange"
            />
          </div>

          <!-- Grouping Configuration -->
          <div v-if="currentStep === 3" class="step-panel">
            <h4>Configure Grouping</h4>
            <p>Choose how to group and organize your data.</p>
            <GroupingSelector
              v-model="reportConfig.grouping"
              :fields="availableFields"
              :dataSource="reportConfig.dataSource"
              @change="onGroupingChange"
            />
          </div>

          <!-- Preview and Export -->
          <div v-if="currentStep === 4" class="step-panel">
            <h4>Preview and Export</h4>
            <p>Preview your report and export it in different formats.</p>
            <ReportPreview
              :reportConfig="reportConfig"
            />
          </div>
        </div>

        <div class="step-navigation">
          <button
            class="btn btn-secondary"
            @click="previousStep"
            :disabled="currentStep === 0"
          >
            <i class="material-icons">arrow_back</i> Previous
          </button>
          <button
            class="btn btn-primary"
            @click="nextStep"
            :disabled="currentStep === steps.length - 1 || !canProceedToNextStep"
          >
            Next <i class="material-icons">arrow_forward</i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DataSourceSelector from './DataSourceSelector.vue';
import MetricsSelector from './MetricsSelector.vue';
import FilterBuilder from './FilterBuilder.vue';
import GroupingSelector from './GroupingSelector.vue';
import ReportPreview from './ReportPreview.vue';

export default {
  name: 'CustomReportBuilder',

  components: {
    DataSourceSelector,
    MetricsSelector,
    FilterBuilder,
    GroupingSelector,
    ReportPreview
  },

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: false,
      loadingMessage: 'Loading...',
      isDirty: false,
      reportName: '',
      currentStep: 0,

      // Report configuration
      reportConfig: {
        dataSource: null,
        metrics: [],
        filters: [],
        filterOperator: 'and',
        grouping: null,
        visualization: 'table'
      },

      // Step definitions
      steps: [
        {
          id: 'data-source',
          name: 'Data Source',
          description: 'Select the primary data source',
          isCompleted: false,
          isEnabled: true
        },
        {
          id: 'metrics',
          name: 'Metrics',
          description: 'Choose metrics to include',
          isCompleted: false,
          isEnabled: false
        },
        {
          id: 'filters',
          name: 'Filters',
          description: 'Add filters to narrow data',
          isCompleted: false,
          isEnabled: false
        },
        {
          id: 'grouping',
          name: 'Grouping',
          description: 'Group and organize data',
          isCompleted: false,
          isEnabled: false
        },
        {
          id: 'preview',
          name: 'Preview',
          description: 'Preview and export report',
          isCompleted: false,
          isEnabled: false
        }
      ],

      // Available data sources
      dataSources: [],

      // Available metrics (depends on selected data source)
      availableMetrics: [],

      // Preview data
      previewData: null
    };
  },

  computed: {
    isValid() {
      // Basic validation: must have data source and at least one metric
      return this.reportConfig.dataSource && this.reportConfig.metrics.length > 0;
    },

    canProceedToNextStep() {
      // Check if current step is completed
      return this.isStepCompleted(this.currentStep);
    },

    availableFields() {
      // Generate fields based on data source and selected metrics
      const baseFields = [
        { id: 'id', name: 'ID', type: 'string' },
        { id: 'created_at', name: 'Created Date', type: 'datetime' },
        { id: 'updated_at', name: 'Updated Date', type: 'datetime' }
      ];

      // Add data source specific fields
      let sourceFields = [];

      if (this.reportConfig.dataSource === 'showrooms') {
        sourceFields = [
          { id: 'name', name: 'Showroom Name', type: 'string' },
          { id: 'description', name: 'Description', type: 'string' },
          { id: 'status', name: 'Status', type: 'enum', options: [
            { value: 'published', label: 'Published' },
            { value: 'draft', label: 'Draft' },
            { value: 'archived', label: 'Archived' }
          ]},
          { id: 'category', name: 'Category', type: 'string' },
          { id: 'location', name: 'Location', type: 'string' }
        ];
      } else if (this.reportConfig.dataSource === 'products') {
        sourceFields = [
          { id: 'name', name: 'Product Name', type: 'string' },
          { id: 'description', name: 'Description', type: 'string' },
          { id: 'category', name: 'Category', type: 'string' },
          { id: 'price', name: 'Price', type: 'number' },
          { id: 'status', name: 'Status', type: 'enum', options: [
            { value: 'active', label: 'Active' },
            { value: 'inactive', label: 'Inactive' }
          ]}
        ];
      } else if (this.reportConfig.dataSource === 'visitors') {
        sourceFields = [
          { id: 'user_id', name: 'User ID', type: 'string' },
          { id: 'device_type', name: 'Device Type', type: 'enum', options: [
            { value: 'desktop', label: 'Desktop' },
            { value: 'mobile', label: 'Mobile' },
            { value: 'tablet', label: 'Tablet' },
            { value: 'vr', label: 'VR Headset' }
          ]},
          { id: 'browser', name: 'Browser', type: 'string' },
          { id: 'location', name: 'Location', type: 'string' },
          { id: 'referrer', name: 'Referrer', type: 'string' }
        ];
      } else if (this.reportConfig.dataSource === 'conversions') {
        sourceFields = [
          { id: 'user_id', name: 'User ID', type: 'string' },
          { id: 'product_id', name: 'Product ID', type: 'string' },
          { id: 'conversion_type', name: 'Conversion Type', type: 'enum', options: [
            { value: 'view', label: 'View' },
            { value: 'click', label: 'Click' },
            { value: 'add_to_cart', label: 'Add to Cart' },
            { value: 'purchase', label: 'Purchase' }
          ]},
          { id: 'value', name: 'Value', type: 'number' }
        ];
      }

      // Add metric fields
      const metricFields = this.availableMetrics.map(metric => ({
        id: metric.id,
        name: metric.name,
        type: metric.id.includes('rate') ? 'number' :
              metric.id.includes('duration') ? 'number' :
              'number'
      }));

      return [...baseFields, ...sourceFields, ...metricFields];
    }
  },

  mounted() {
    // Load available data sources
    this.loadDataSources();
  },

  methods: {
    // Navigation methods
    nextStep() {
      if (this.currentStep < this.steps.length - 1 && this.canProceedToNextStep) {
        this.currentStep++;
      }
    },

    previousStep() {
      if (this.currentStep > 0) {
        this.currentStep--;
      }
    },

    goToStep(stepIndex) {
      // Only allow navigation to enabled steps
      if (this.isStepEnabled(stepIndex)) {
        this.currentStep = stepIndex;
      }
    },

    // Step status methods
    isStepCompleted(stepIndex) {
      return this.steps[stepIndex].isCompleted;
    },

    isStepEnabled(stepIndex) {
      return this.steps[stepIndex].isEnabled;
    },

    markStepCompleted(stepIndex, isCompleted = true) {
      this.steps[stepIndex].isCompleted = isCompleted;

      // Enable next step if this step is completed
      if (isCompleted && stepIndex < this.steps.length - 1) {
        this.steps[stepIndex + 1].isEnabled = true;
      }

      // Mark report as dirty
      this.isDirty = true;
    },

    // Data loading methods
    async loadDataSources() {
      this.loading = true;
      this.loadingMessage = 'Loading data sources...';

      try {
        // This will be replaced with actual API call
        // const response = await fetch(`/api/analytics/data-sources?vendor_id=${this.vendorId}`);
        // const data = await response.json();

        // Mock data for now
        setTimeout(() => {
          this.dataSources = [
            { id: 'showrooms', name: 'Showrooms', description: 'Showroom analytics data' },
            { id: 'products', name: 'Products', description: 'Product performance data' },
            { id: 'visitors', name: 'Visitors', description: 'Visitor behavior data' },
            { id: 'conversions', name: 'Conversions', description: 'Conversion funnel data' }
          ];
          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading data sources:', error);
        this.loading = false;
      }
    },

    // Data source handling
    onDataSourceChange(sourceId) {
      console.log('Data source changed:', sourceId);

      // Mark data source step as completed
      this.markStepCompleted(0, !!sourceId);

      // Reset metrics when data source changes
      this.reportConfig.metrics = [];
      this.markStepCompleted(1, false);

      // Load available metrics for this data source
      if (sourceId) {
        this.loadMetricsForDataSource(sourceId);
      }
    },

    // Metrics handling
    onMetricsChange(metrics) {
      console.log('Metrics changed:', metrics);

      // Mark metrics step as completed if at least one metric is selected
      this.markStepCompleted(1, this.reportConfig.metrics.length > 0);

      // Update isDirty flag
      this.isDirty = true;
    },

    // Filters handling
    onFiltersChange(filterData) {
      console.log('Filters changed:', filterData);

      // Update filter operator if provided
      if (filterData.operator) {
        this.reportConfig.filterOperator = filterData.operator;
      }

      // Mark filters step as completed
      // We consider filters optional, so the step is always completed
      this.markStepCompleted(2, true);

      // Update isDirty flag
      this.isDirty = true;
    },

    // Grouping handling
    onGroupingChange(groupingConfig) {
      console.log('Grouping changed:', groupingConfig);

      // Mark grouping step as completed
      // We consider grouping optional, so the step is always completed
      this.markStepCompleted(3, true);

      // Update isDirty flag
      this.isDirty = true;
    },

    async loadMetricsForDataSource(sourceId) {
      this.loading = true;
      this.loadingMessage = 'Loading metrics...';

      try {
        // This will be replaced with actual API call
        // const response = await fetch(`/api/analytics/data-sources/${sourceId}/metrics?vendor_id=${this.vendorId}`);
        // const data = await response.json();

        // Mock data for now
        setTimeout(() => {
          // Generate metrics based on data source
          let metrics = [];

          if (sourceId === 'showrooms') {
            metrics = [
              { id: 'visits', name: 'Total Visits', category: 'Traffic' },
              { id: 'unique_visitors', name: 'Unique Visitors', category: 'Traffic' },
              { id: 'avg_duration', name: 'Average Duration', category: 'Engagement' },
              { id: 'bounce_rate', name: 'Bounce Rate', category: 'Engagement' },
              { id: 'interactions', name: 'Interactions', category: 'Engagement' },
              { id: 'conversion_rate', name: 'Conversion Rate', category: 'Conversion' }
            ];
          } else if (sourceId === 'products') {
            metrics = [
              { id: 'views', name: 'Views', category: 'Traffic' },
              { id: 'interactions', name: 'Interactions', category: 'Engagement' },
              { id: 'avg_time', name: 'Average Time Spent', category: 'Engagement' },
              { id: 'add_to_cart', name: 'Add to Cart', category: 'Conversion' },
              { id: 'purchases', name: 'Purchases', category: 'Conversion' },
              { id: 'conversion_rate', name: 'Conversion Rate', category: 'Conversion' }
            ];
          } else if (sourceId === 'visitors') {
            metrics = [
              { id: 'sessions', name: 'Sessions', category: 'Traffic' },
              { id: 'avg_session_duration', name: 'Average Session Duration', category: 'Engagement' },
              { id: 'pages_per_session', name: 'Pages per Session', category: 'Engagement' },
              { id: 'bounce_rate', name: 'Bounce Rate', category: 'Engagement' },
              { id: 'new_visitors', name: 'New Visitors', category: 'Traffic' },
              { id: 'returning_visitors', name: 'Returning Visitors', category: 'Traffic' }
            ];
          } else if (sourceId === 'conversions') {
            metrics = [
              { id: 'conversion_count', name: 'Conversion Count', category: 'Conversion' },
              { id: 'conversion_rate', name: 'Conversion Rate', category: 'Conversion' },
              { id: 'avg_order_value', name: 'Average Order Value', category: 'Revenue' },
              { id: 'revenue', name: 'Revenue', category: 'Revenue' },
              { id: 'cart_abandonment', name: 'Cart Abandonment Rate', category: 'Conversion' }
            ];
          }

          this.availableMetrics = metrics;
          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading metrics:', error);
        this.loading = false;
      }
    },

    // Report actions
    saveReport() {
      if (!this.isValid) {
        alert('Please complete the required steps before saving the report.');
        return;
      }

      // Show save dialog
      this.$buefy.dialog.prompt({
        title: 'Save Report',
        message: 'Enter a name for your report:',
        inputAttrs: {
          placeholder: this.reportName || 'New Custom Report',
          value: this.reportName,
          maxlength: 100,
          required: true
        },
        confirmText: 'Save',
        cancelText: 'Cancel',
        onConfirm: (reportName) => {
          if (!reportName.trim()) {
            this.$buefy.toast.open({
              message: 'Report name cannot be empty',
              type: 'is-danger'
            });
            return;
          }

          this.saveReportWithName(reportName);
        }
      });
    },

    async saveReportWithName(reportName) {
      this.loading = true;
      this.loadingMessage = 'Saving report...';

      try {
        // Prepare report data
        const reportData = {
          name: reportName,
          description: '',
          config: { ...this.reportConfig },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by_user: { first_name: 'Current', last_name: 'User' }
        };

        // This will be replaced with actual API call
        // const response = await fetch(`/api/analytics/custom-reports?vendor_id=${this.vendorId}`, {
        //   method: 'POST',
        //   headers: {
        //     'Content-Type': 'application/json'
        //   },
        //   body: JSON.stringify(reportData)
        // });
        // const data = await response.json();

        // Mock save for now
        setTimeout(() => {
          // Update report name
          this.reportName = reportName;

          // Reset dirty flag
          this.isDirty = false;

          // Show success message
          this.$buefy.toast.open({
            message: `Report "${reportName}" saved successfully`,
            type: 'is-success'
          });

          this.loading = false;

          // Emit save event
          this.$emit('save', reportData);
        }, 1000);
      } catch (error) {
        console.error('Error saving report:', error);

        this.$buefy.toast.open({
          message: 'Error saving report. Please try again.',
          type: 'is-danger'
        });

        this.loading = false;
      }
    },

    loadReport() {
      this.loading = true;
      this.loadingMessage = 'Loading saved reports...';

      try {
        // This will be replaced with actual API call
        // const response = await fetch(`/api/analytics/custom-reports?vendor_id=${this.vendorId}`);
        // const data = await response.json();

        // Mock data for now
        setTimeout(() => {
          const savedReports = [
            {
              id: '1',
              name: 'Monthly Showroom Performance',
              description: 'Tracks showroom visits, engagement, and conversions on a monthly basis',
              created_at: '2023-06-15T10:30:00Z',
              updated_at: '2023-07-20T14:45:00Z',
              created_by_user: { first_name: 'John', last_name: 'Doe' },
              config: {
                dataSource: 'showrooms',
                metrics: ['visits', 'unique_visitors', 'avg_duration', 'conversion_rate'],
                filters: [],
                filterOperator: 'and',
                grouping: {
                  dimension: 'time',
                  unit: 'month',
                  startDate: '2023-01-01',
                  endDate: '2023-12-31'
                },
                visualization: 'table'
              }
            },
            {
              id: '2',
              name: 'Product Engagement Analysis',
              description: 'Analyzes product views, interactions, and conversion rates',
              created_at: '2023-05-10T09:15:00Z',
              updated_at: '2023-07-18T11:20:00Z',
              created_by_user: { first_name: 'Jane', last_name: 'Smith' },
              config: {
                dataSource: 'products',
                metrics: ['views', 'interactions', 'avg_time', 'conversion_rate'],
                filters: [],
                filterOperator: 'and',
                grouping: {
                  dimension: 'category',
                  field: 'category',
                  sortBy: 'value',
                  sortDirection: 'desc'
                },
                visualization: 'chart'
              }
            },
            {
              id: '3',
              name: 'Visitor Behavior Trends',
              description: 'Tracks visitor behavior patterns and engagement metrics',
              created_at: '2023-07-05T16:45:00Z',
              updated_at: '2023-07-15T13:30:00Z',
              created_by_user: { first_name: 'John', last_name: 'Doe' },
              config: {
                dataSource: 'visitors',
                metrics: ['sessions', 'avg_session_duration', 'bounce_rate', 'returning_visitors'],
                filters: [],
                filterOperator: 'and',
                grouping: {
                  dimension: 'time',
                  unit: 'week',
                  startDate: '2023-06-01',
                  endDate: '2023-07-31'
                },
                visualization: 'chart'
              }
            }
          ];

          this.loading = false;

          // Show report selection dialog
          this.showReportSelectionDialog(savedReports);
        }, 1000);
      } catch (error) {
        console.error('Error loading saved reports:', error);

        this.$buefy.toast.open({
          message: 'Error loading saved reports. Please try again.',
          type: 'is-danger'
        });

        this.loading = false;
      }
    },

    showReportSelectionDialog(reports) {
      if (reports.length === 0) {
        this.$buefy.toast.open({
          message: 'No saved reports found',
          type: 'is-info'
        });
        return;
      }

      // Create report options
      const options = reports.map(report => ({
        value: report.id,
        text: report.name
      }));

      // Show selection dialog
      this.$buefy.dialog.prompt({
        title: 'Load Report',
        message: 'Select a report to load:',
        inputAttrs: {
          type: 'select',
          options
        },
        confirmText: 'Load',
        cancelText: 'Cancel',
        onConfirm: (reportId) => {
          const selectedReport = reports.find(r => r.id === reportId);
          if (selectedReport) {
            this.loadReportConfig(selectedReport);
          }
        }
      });
    },

    loadReportConfig(report) {
      // Check if current report has unsaved changes
      if (this.isDirty) {
        this.$buefy.dialog.confirm({
          title: 'Unsaved Changes',
          message: 'You have unsaved changes. Loading a new report will discard these changes. Do you want to continue?',
          confirmText: 'Continue',
          cancelText: 'Cancel',
          type: 'is-warning',
          onConfirm: () => {
            this.applyReportConfig(report);
          }
        });
      } else {
        this.applyReportConfig(report);
      }
    },

    applyReportConfig(report) {
      this.loading = true;
      this.loadingMessage = `Loading report "${report.name}"...`;

      // Apply report configuration
      this.reportName = report.name;
      this.reportConfig = { ...report.config };

      // Reset current step
      this.currentStep = 0;

      // Mark steps as completed based on config
      this.markStepCompleted(0, !!this.reportConfig.dataSource);
      this.markStepCompleted(1, this.reportConfig.metrics && this.reportConfig.metrics.length > 0);
      this.markStepCompleted(2, true); // Filters are optional
      this.markStepCompleted(3, true); // Grouping is optional
      this.markStepCompleted(4, true); // Preview is always available if previous steps are completed

      // Reset dirty flag
      this.isDirty = false;

      setTimeout(() => {
        this.loading = false;

        // Show success message
        this.$buefy.toast.open({
          message: `Report "${report.name}" loaded successfully`,
          type: 'is-success'
        });

        // Go to preview step
        this.currentStep = 4;
      }, 1000);
    },

    runReport() {
      if (!this.isValid) {
        alert('Please complete the required steps before running the report.');
        return;
      }

      // Go to preview step
      this.currentStep = 4;

      // Show success message
      this.$buefy.toast.open({
        message: 'Report is ready to view',
        type: 'is-success'
      });
    }
  }
};
</script>

<style scoped>
.custom-report-builder {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.report-builder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--theme--border-color);
}

.header-left {
  display: flex;
  align-items: center;
}

.unsaved-indicator {
  color: var(--theme--primary);
  font-size: 24px;
  margin-left: 5px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.report-builder-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.report-builder-sidebar {
  width: 250px;
  border-right: 1px solid var(--theme--border-color);
  overflow-y: auto;
  padding: 15px;
}

.report-builder-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.step-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.step-navigation {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  border-top: 1px solid var(--theme--border-color);
}

.steps-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.step-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: var(--theme--border-radius);
  cursor: pointer;
  transition: background-color 0.2s;
}

.step-item:hover:not(.disabled) {
  background-color: var(--theme--background-subdued);
}

.step-item.active {
  background-color: var(--theme--primary-background);
  border-left: 3px solid var(--theme--primary);
}

.step-item.completed {
  color: var(--theme--primary);
}

.step-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.step-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-weight: bold;
}

.step-item.active .step-number {
  background-color: var(--theme--primary);
  color: white;
}

.step-info {
  flex: 1;
}

.step-name {
  font-weight: bold;
}

.step-description {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.step-panel {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 20px;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--background);
  border-top: 4px solid var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Placeholder styles for development */
.data-source-placeholder,
.metrics-placeholder,
.filters-placeholder,
.grouping-placeholder,
.preview-placeholder {
  background-color: var(--theme--background-subdued);
  border: 1px dashed var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 30px;
  text-align: center;
  color: var(--theme--foreground-subdued);
  margin-top: 20px;
}
</style>
