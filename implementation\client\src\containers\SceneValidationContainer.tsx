import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { Box, Typography, Container, Paper, CircularProgress } from '@mui/material'
import ValidationReport from '../components/validation/ValidationReport'
import { apiClient } from '../services/apiClient'

/**
 * Scene Validation Container
 * 
 * Container component for scene validation
 */
const SceneValidationContainer: React.FC = () => {
  const { sceneId } = useParams<{ sceneId: string }>()
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [validationResult, setValidationResult] = useState<any>(null)
  const [performanceAnalysis, setPerformanceAnalysis] = useState<any>(null)
  const [compatibilityResult, setCompatibilityResult] = useState<any>(null)

  // Validate scene
  const validateScene = async () => {
    if (!sceneId) return

    setLoading(true)
    setError(null)

    try {
      const response = await apiClient.get(`/api/scenes/validate/${sceneId}`)
      
      if (response.data.success) {
        setValidationResult(response.data.data)
      } else {
        setError(response.data.error?.message || 'Failed to validate scene')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while validating the scene')
    } finally {
      setLoading(false)
    }
  }

  // Analyze scene performance
  const analyzePerformance = async () => {
    if (!sceneId) return

    setLoading(true)
    setError(null)

    try {
      const response = await apiClient.get(`/api/scenes/validate/${sceneId}/performance`)
      
      if (response.data.success) {
        setPerformanceAnalysis(response.data.data)
      } else {
        setError(response.data.error?.message || 'Failed to analyze scene performance')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while analyzing scene performance')
    } finally {
      setLoading(false)
    }
  }

  // Check scene compatibility
  const checkCompatibility = async (environment: string) => {
    if (!sceneId) return

    setLoading(true)
    setError(null)

    try {
      const response = await apiClient.get(`/api/scenes/validate/${sceneId}/compatibility`, {
        params: { target_environment: environment },
      })
      
      if (response.data.success) {
        setCompatibilityResult(response.data.data)
      } else {
        setError(response.data.error?.message || 'Failed to check scene compatibility')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while checking scene compatibility')
    } finally {
      setLoading(false)
    }
  }

  // Validate scene data
  const validateSceneData = async (data: any) => {
    setLoading(true)
    setError(null)

    try {
      const response = await apiClient.post('/api/scenes/validate/data', { data })
      
      if (response.data.success) {
        return response.data.data
      } else {
        setError(response.data.error?.message || 'Failed to validate scene data')
        return null
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while validating scene data')
      return null
    } finally {
      setLoading(false)
    }
  }

  // Validate scene flow
  const validateSceneFlow = async (flow: any) => {
    setLoading(true)
    setError(null)

    try {
      const response = await apiClient.post('/api/scenes/validate/flow', { flow })
      
      if (response.data.success) {
        return response.data.data
      } else {
        setError(response.data.error?.message || 'Failed to validate scene flow')
        return null
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while validating scene flow')
      return null
    } finally {
      setLoading(false)
    }
  }

  // Load scene validation on mount
  useEffect(() => {
    if (sceneId) {
      validateScene()
    }
  }, [sceneId])

  if (!sceneId) {
    return (
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom>
            Scene Validation
          </Typography>
          <Typography variant="body1">
            No scene selected. Please select a scene to validate.
          </Typography>
        </Paper>
      </Container>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ mt: 4 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          Scene Validation: {sceneId}
        </Typography>
        <Typography variant="body1" gutterBottom>
          Validate your scene to ensure it meets all requirements and is optimized for performance.
        </Typography>
      </Paper>

      <ValidationReport
        sceneId={sceneId}
        validationResult={validationResult}
        performanceAnalysis={performanceAnalysis}
        compatibilityResult={compatibilityResult}
        loading={loading}
        error={error || undefined}
        onValidate={validateScene}
        onAnalyzePerformance={analyzePerformance}
        onCheckCompatibility={checkCompatibility}
      />
    </Container>
  )
}

export default SceneValidationContainer
