# MVS-VR Advanced Developer Guide

This guide provides in-depth information for developers working with the MVS-VR platform. It covers advanced topics such as asset optimization, performance optimization, security best practices, offline mode implementation, and custom extension development.

## Table of Contents

1. [Asset Optimization](#asset-optimization)
2. [Performance Optimization](#performance-optimization)
3. [Security Best Practices](#security-best-practices)
4. [Offline Mode Implementation](#offline-mode-implementation)
5. [Custom Extension Development](#custom-extension-development)
6. [Advanced API Usage](#advanced-api-usage)
7. [Testing and Debugging](#testing-and-debugging)
8. [Deployment Strategies](#deployment-strategies)

## Asset Optimization

### 3D Model Optimization

Optimizing 3D models is crucial for performance, especially in VR applications. Follow these guidelines:

1. **Polygon Reduction**
   - Use LOD (Level of Detail) for models
   - Aim for 50,000 polygons or less for complex models
   - Use decimation tools to reduce polygon count

```javascript
// Example of using the MVS-VR optimization service
const optimizedAsset = await client.assets.optimize(assetId, {
  target: 'polygon',
  maxPolygons: 50000,
  preserveUVs: true,
  preserveNormals: true,
});
```

2. **Texture Optimization**
   - Use texture atlasing to reduce draw calls
   - Compress textures using appropriate formats (BC7, ASTC, etc.)
   - Use mipmaps for better performance at different distances

```javascript
// Example of texture optimization
const optimizedTexture = await client.assets.optimizeTexture(textureId, {
  format: 'BC7',
  generateMipmaps: true,
  maxSize: 2048,
});
```

3. **Material Optimization**
   - Use PBR (Physically Based Rendering) materials
   - Combine materials where possible
   - Use material instances for variations

### Asset Bundling

Bundling assets can significantly improve loading times:

```javascript
// Create an asset bundle
const bundle = await client.assets.createBundle({
  name: 'Furniture Pack',
  assets: ['asset-id-1', 'asset-id-2', 'asset-id-3'],
  compressionLevel: 'high',
  includeMetadata: true,
});

// Load an asset bundle
const loadedBundle = await client.assets.loadBundle(bundle.id);
```

### Progressive Loading

Implement progressive loading for better user experience:

```javascript
// Configure progressive loading
client.assets.setProgressiveLoadingOptions({
  prioritizeVisible: true,
  loadLODsInOrder: true,
  preloadDistance: 10,
});

// Load an asset progressively
const asset = await client.assets.loadProgressively(assetId, {
  onProgress: (progress) => {
    console.log(`Loading progress: ${progress.percent}%`);
    updateLoadingUI(progress.percent);
  },
  priority: 'high',
});
```

## Performance Optimization

### Client-Side Optimization

1. **Rendering Optimization**
   - Use frustum culling
   - Implement occlusion culling
   - Use instancing for repeated objects

```javascript
// Configure rendering options
client.rendering.setOptions({
  enableFrustumCulling: true,
  enableOcclusionCulling: true,
  enableInstancing: true,
  shadowQuality: 'medium',
  antiAliasing: 'TAA',
});
```

2. **Memory Management**
   - Implement asset unloading for unused assets
   - Use texture streaming
   - Monitor memory usage

```javascript
// Configure memory management
client.memory.setOptions({
  maxMemoryUsage: 2048, // MB
  unloadUnusedAssets: true,
  unloadTimeout: 60, // seconds
  enableTextureStreaming: true,
});

// Monitor memory usage
client.memory.onWarning((usage) => {
  console.warn(`High memory usage: ${usage.current}MB / ${usage.max}MB`);
  // Take action to reduce memory usage
});
```

3. **Network Optimization**
   - Use CDN for asset delivery
   - Implement request batching
   - Use compression for API requests

```javascript
// Configure network options
client.network.setOptions({
  useCDN: true,
  enableCompression: true,
  batchRequests: true,
  maxConcurrentRequests: 8,
});
```

### Server-Side Optimization

1. **API Optimization**
   - Use pagination for large result sets
   - Implement caching with ETags
   - Use compression for responses

2. **Database Optimization**
   - Create appropriate indexes
   - Optimize queries
   - Use connection pooling

3. **Asset Processing Optimization**
   - Implement parallel processing
   - Use worker threads for CPU-intensive tasks
   - Implement job queues for background processing

## Security Best Practices

### Authentication and Authorization

1. **Secure Authentication**
   - Use HTTPS for all requests
   - Implement token refresh
   - Use short-lived access tokens
   - Store refresh tokens securely

```javascript
// Secure token refresh
client.auth.onTokenExpired(async () => {
  try {
    await client.auth.refreshToken();
  } catch (error) {
    // Token refresh failed, redirect to login
    window.location.href = '/login';
  }
});
```

2. **Role-Based Access Control**
   - Implement principle of least privilege
   - Use role-based permissions
   - Validate permissions on both client and server

```javascript
// Check permissions
if (await client.auth.hasPermission('assets.create')) {
  // Show asset creation UI
} else {
  // Hide or disable asset creation UI
}
```

### Data Security

1. **Input Validation**
   - Validate all user input
   - Use parameterized queries
   - Implement content security policy

```javascript
// Validate input
const schema = {
  name: { type: 'string', minLength: 1, maxLength: 100 },
  type: { type: 'string', enum: ['model', 'texture', 'material'] },
  tags: { type: 'array', items: { type: 'string' } },
};

const validator = new client.validation.Validator(schema);
const isValid = validator.validate(userInput);

if (!isValid) {
  console.error('Validation errors:', validator.errors);
  return;
}
```

2. **Secure Storage**
   - Encrypt sensitive data
   - Use secure storage for credentials
   - Implement proper access controls

```javascript
// Secure storage
await client.storage.setSecure('api-key', apiKey);
const storedKey = await client.storage.getSecure('api-key');
```

3. **API Security**
   - Implement rate limiting
   - Use CSRF protection
   - Validate API keys and tokens

## Offline Mode Implementation

### Offline Data Synchronization

1. **Data Caching**
   - Implement a caching strategy
   - Use IndexedDB for client-side storage
   - Implement versioned caching

```javascript
// Configure offline mode
client.offline.configure({
  enabled: true,
  syncInterval: 60, // seconds
  maxCacheSize: 500, // MB
  persistentCache: true,
});

// Check if offline
const isOffline = client.offline.isOffline();
console.log(`Application is ${isOffline ? 'offline' : 'online'}`);

// Handle offline/online events
client.offline.onStatusChange((status) => {
  console.log(`Connection status changed to: ${status}`);
  updateUIForConnectionStatus(status);
});
```

2. **Conflict Resolution**
   - Implement conflict detection
   - Use timestamps for conflict resolution
   - Provide UI for manual conflict resolution

```javascript
// Configure conflict resolution
client.offline.setConflictResolution({
  strategy: 'server-wins', // or 'client-wins', 'manual'
  onConflict: (serverData, clientData) => {
    // For manual resolution, show UI and return resolved data
    return showConflictResolutionUI(serverData, clientData);
  },
});
```

3. **Background Synchronization**
   - Use service workers for background sync
   - Implement retry mechanisms
   - Provide sync status indicators

```javascript
// Trigger manual sync
await client.offline.sync();

// Listen for sync events
client.offline.onSyncStart(() => {
  showSyncIndicator();
});

client.offline.onSyncComplete((result) => {
  hideSyncIndicator();
  console.log(`Sync completed. Items synced: ${result.syncedItems}`);
});

client.offline.onSyncError((error) => {
  hideSyncIndicator();
  console.error('Sync error:', error);
  showSyncErrorNotification(error);
});
```

### Offline Asset Management

1. **Asset Preloading**
   - Preload essential assets
   - Implement priority-based loading
   - Use background loading during idle time

```javascript
// Preload assets for offline use
await client.offline.preloadAssets({
  assets: ['asset-id-1', 'asset-id-2'],
  scenes: ['scene-id-1'],
  priority: 'high',
  onProgress: (progress) => {
    updatePreloadProgress(progress);
  },
});
```

2. **Storage Management**
   - Implement storage quotas
   - Provide UI for managing offline assets
   - Implement cleanup strategies

```javascript
// Get offline storage usage
const usage = await client.offline.getStorageUsage();
console.log(`Using ${usage.used}MB of ${usage.quota}MB (${usage.percent}%)`);

// Clear specific assets from offline storage
await client.offline.clearAssets(['asset-id-1', 'asset-id-2']);

// Clear all offline data
await client.offline.clearAll();
```

## Custom Extension Development

### Plugin Development

1. **Plugin Architecture**
   - Understand the plugin system
   - Use the plugin API
   - Follow the plugin lifecycle

```javascript
// Create a custom plugin
class MyCustomPlugin {
  constructor(client) {
    this.client = client;
  }

  // Plugin lifecycle methods
  async initialize() {
    console.log('Plugin initialized');
    // Register event handlers, etc.
  }

  async destroy() {
    console.log('Plugin destroyed');
    // Clean up resources
  }

  // Custom plugin methods
  async doSomething() {
    // Implement custom functionality
  }
}

// Register the plugin
client.plugins.register('my-custom-plugin', MyCustomPlugin);

// Use the plugin
const myPlugin = client.plugins.get('my-custom-plugin');
await myPlugin.doSomething();
```

2. **Custom UI Components**
   - Create custom UI components
   - Integrate with the MVS-VR UI system
   - Use the theming system

```javascript
// React component example
import { useClient, useTheme } from '@mvs-vr/react-hooks';

function MyCustomComponent({ assetId }) {
  const client = useClient();
  const theme = useTheme();
  
  // Use client and theme
  
  return (
    <div style={{ color: theme.colors.primary }}>
      {/* Component content */}
    </div>
  );
}
```

### API Extensions

1. **Custom Endpoints**
   - Create custom API endpoints
   - Implement authentication and authorization
   - Handle errors properly

```javascript
// Server-side example (Node.js/Express)
const express = require('express');
const { authenticate, authorize } = require('@mvs-vr/server-sdk');

const router = express.Router();

// Custom endpoint with authentication and authorization
router.post('/custom-endpoint',
  authenticate(),
  authorize('custom.action'),
  async (req, res) => {
    try {
      // Implement custom logic
      const result = await doSomething(req.body);
      res.json(result);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }
);

module.exports = router;
```

2. **Custom Data Models**
   - Define custom data models
   - Implement validation
   - Create database migrations

```javascript
// Custom data model
const { DataModel } = require('@mvs-vr/server-sdk');

class CustomModel extends DataModel {
  static get tableName() {
    return 'custom_models';
  }

  static get schema() {
    return {
      id: { type: 'string', primary: true },
      name: { type: 'string', required: true },
      data: { type: 'object' },
      createdAt: { type: 'datetime', defaultNow: true },
      updatedAt: { type: 'datetime', defaultNow: true, onUpdate: true },
    };
  }

  static get relations() {
    return {
      assets: {
        relation: 'hasMany',
        model: 'Asset',
        foreignKey: 'customModelId',
      },
    };
  }
}

module.exports = CustomModel;
```

## Advanced API Usage

### Batch Operations

```javascript
// Batch create assets
const results = await client.batch.create('assets', [
  { name: 'Asset 1', type: 'model' },
  { name: 'Asset 2', type: 'texture' },
  { name: 'Asset 3', type: 'material' },
]);

// Batch update assets
await client.batch.update('assets', [
  { id: 'asset-id-1', tags: ['updated'] },
  { id: 'asset-id-2', tags: ['updated'] },
]);

// Batch delete assets
await client.batch.delete('assets', ['asset-id-1', 'asset-id-2']);
```

### Transactions

```javascript
// Perform operations in a transaction
await client.transaction(async (tx) => {
  const asset = await tx.assets.create({ name: 'New Asset', type: 'model' });
  const scene = await tx.scenes.create({ name: 'New Scene', assets: [asset.id] });
  await tx.publish(scene.id);
});
```

### Real-time Updates

```javascript
// Subscribe to asset changes
const unsubscribe = client.subscribe('assets', (event) => {
  console.log(`Asset ${event.id} was ${event.type}`);
  
  if (event.type === 'created') {
    // Handle created event
  } else if (event.type === 'updated') {
    // Handle updated event
  } else if (event.type === 'deleted') {
    // Handle deleted event
  }
});

// Unsubscribe when done
unsubscribe();
```

## Testing and Debugging

### Unit Testing

```javascript
// Jest example
import { MVSClient } from '@mvs-vr/client-sdk';

describe('Asset Management', () => {
  let client;
  
  beforeEach(() => {
    client = new MVSClient({
      apiUrl: 'https://api.mvs-vr.com',
      authToken: 'test-token',
    });
  });
  
  test('should create an asset', async () => {
    const asset = await client.assets.create({
      name: 'Test Asset',
      type: 'model',
    });
    
    expect(asset).toBeDefined();
    expect(asset.id).toBeDefined();
    expect(asset.name).toBe('Test Asset');
    expect(asset.type).toBe('model');
  });
});
```

### Integration Testing

```javascript
// Cypress example
describe('Asset Management UI', () => {
  beforeEach(() => {
    cy.login('test-user', 'test-password');
    cy.visit('/assets');
  });
  
  it('should create a new asset', () => {
    cy.get('[data-cy=create-asset-button]').click();
    cy.get('[data-cy=asset-name-input]').type('Test Asset');
    cy.get('[data-cy=asset-type-select]').select('model');
    cy.get('[data-cy=save-asset-button]').click();
    
    cy.get('[data-cy=asset-list]').should('contain', 'Test Asset');
  });
});
```

### Debugging

1. **Client-Side Debugging**
   - Use the browser developer tools
   - Enable debug logging
   - Use the MVS-VR debug tools

```javascript
// Enable debug logging
client.debug.setLevel('verbose');

// Log specific events
client.debug.on('api:request', (request) => {
  console.log('API Request:', request);
});

client.debug.on('api:response', (response) => {
  console.log('API Response:', response);
});

// Open debug tools
client.debug.openTools();
```

2. **Server-Side Debugging**
   - Use logging
   - Use a debugger
   - Monitor performance

## Deployment Strategies

### Continuous Integration and Deployment

1. **CI/CD Pipeline**
   - Set up automated testing
   - Implement deployment automation
   - Use environment-specific configurations

2. **Environment Management**
   - Use environment variables
   - Implement feature flags
   - Use staging environments

3. **Monitoring and Alerting**
   - Implement logging
   - Set up monitoring
   - Configure alerts

For more information on deployment, see the [Deployment Guide](./DEPLOYMENT_GUIDE.md).
