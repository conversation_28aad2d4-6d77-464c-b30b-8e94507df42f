# [ADR-0005] API Key Rotation

## Status

Accepted

## Context

API keys are used for authentication in the MVS-VR platform. Long-lived API keys pose a security risk if they are compromised. We need a strategy for regularly rotating API keys to minimize the impact of potential key leaks while ensuring a smooth experience for users.

## Decision

We will implement automatic API key rotation with the following characteristics:

1. API keys will be rotated every 30 days
2. Old API keys will remain valid for a 7-day grace period after rotation
3. Users will be notified by email when their API keys are rotated
4. Both the current and previous API keys will be stored in the database

## Consequences

### Positive

- Reduced risk from compromised API keys
- Limited time window for potential attackers
- Better compliance with security best practices
- Improved audit trail with key rotation history

### Negative

- Additional complexity in authentication logic
- Potential for service disruption if clients don't update keys
- Increased database storage requirements
- Need for user education about key rotation

### Neutral

- Regular communication with users about security practices
- Different handling for different types of API clients

## Alternatives Considered

### Alternative 1: Manual API Key Rotation

Require users to manually rotate their API keys.

#### Pros

- User control over when rotation happens
- No risk of unexpected service disruption
- Simpler implementation

#### Cons

- Lower security as users may not rotate keys regularly
- No enforcement of security best practices
- Increased support burden for key rotation assistance

### Alternative 2: Short-Lived JWT Tokens

Replace API keys with short-lived JWT tokens that require regular refresh.

#### Pros

- More secure with shorter validity periods
- Built-in expiration mechanism
- Standard approach with good library support

#### Cons

- More complex client implementation
- Need for refresh token management
- Different authentication flow for API clients

## Related Decisions

- [ADR-0004] Security Headers Implementation

## Notes

We will implement API key rotation with the following components:

1. Database schema changes:
   - Add `api_key_created_at` timestamp to track key age
   - Add `previous_api_key` field to store the old key
   - Add `previous_api_key_expires_at` timestamp for grace period

2. API key rotation script:
   - Scheduled to run daily
   - Identifies keys older than 30 days
   - Generates new secure keys
   - Updates database with new and old keys
   - Sends email notifications

3. Authentication middleware changes:
   - Check current API key first
   - Fall back to previous API key if current fails
   - Validate previous key hasn't expired
   - Include key age in response headers

4. Monitoring and alerts:
   - Track key rotation events
   - Alert on high failure rates after rotation
   - Monitor key usage patterns

Example API key rotation implementation:

```javascript
async function rotateApiKeys() {
  const users = await db.users.findAll({ 
    where: { 
      apiKeyCreatedAt: { [Op.lt]: subDays(new Date(), 30) } 
    } 
  });
  
  for (const user of users) {
    // Generate new API key
    const newApiKey = generateSecureApiKey();
    
    // Store new API key with creation date
    await db.users.update({
      apiKey: newApiKey,
      apiKeyCreatedAt: new Date(),
      previousApiKey: user.apiKey,
      previousApiKeyExpiresAt: addDays(new Date(), 7), // 7-day grace period
    }, { where: { id: user.id } });
    
    // Notify user
    await sendApiKeyRotationEmail(user.email, newApiKey);
  }
}
```
