import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, TextField, Grid, Paper, IconButton, Accordion, AccordionSummary, AccordionDetails, List, ListItem, ListItemText, ListItemSecondaryAction } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, ExpandMore as ExpandMoreIcon, Edit as EditIcon } from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import { Location, Exhibition, Space } from '../../types/scene';
import VectorInput from './VectorInput';
import ExhibitionEditor from './ExhibitionEditor';

interface LocationEditorProps {
  sceneId: string;
  locations: Location[];
  onSave: (locations: Location[]) => void;
}

const LocationEditor: React.FC<LocationEditorProps> = ({ sceneId, locations, onSave }) => {
  const [editLocations, setEditLocations] = useState<Location[]>([]);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [showLocationForm, setShowLocationForm] = useState(false);
  const [currentExhibition, setCurrentExhibition] = useState<Exhibition | null>(null);
  const [showExhibitionForm, setShowExhibitionForm] = useState(false);

  useEffect(() => {
    setEditLocations(locations || []);
  }, [locations]);

  const handleAddLocation = () => {
    const newLocation: Location = {
      id: uuidv4(),
      name: 'New Location',
      description: '',
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scale: [1, 1, 1],
      exhibitions: [],
      properties: {}
    };
    setCurrentLocation(newLocation);
    setShowLocationForm(true);
  };

  const handleEditLocation = (location: Location) => {
    setCurrentLocation({ ...location });
    setShowLocationForm(true);
  };

  const handleDeleteLocation = (locationId: string) => {
    setEditLocations(editLocations.filter(loc => loc.id !== locationId));
    onSave(editLocations.filter(loc => loc.id !== locationId));
  };

  const handleSaveLocation = () => {
    if (!currentLocation) return;

    const updatedLocations = [...editLocations];
    const index = updatedLocations.findIndex(loc => loc.id === currentLocation.id);

    if (index >= 0) {
      updatedLocations[index] = currentLocation;
    } else {
      updatedLocations.push(currentLocation);
    }

    setEditLocations(updatedLocations);
    onSave(updatedLocations);
    setShowLocationForm(false);
    setCurrentLocation(null);
  };

  const handleAddExhibition = (locationId: string) => {
    const newExhibition: Exhibition = {
      id: uuidv4(),
      name: 'New Exhibition',
      description: '',
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scale: [1, 1, 1],
      spaces: [],
      properties: {}
    };
    
    const location = editLocations.find(loc => loc.id === locationId);
    if (location) {
      setCurrentExhibition(newExhibition);
      setCurrentLocation(location);
      setShowExhibitionForm(true);
    }
  };

  const handleSaveExhibition = (exhibition: Exhibition) => {
    if (!currentLocation) return;

    const updatedLocation = { ...currentLocation };
    const index = updatedLocation.exhibitions.findIndex(ex => ex.id === exhibition.id);

    if (index >= 0) {
      updatedLocation.exhibitions[index] = exhibition;
    } else {
      updatedLocation.exhibitions.push(exhibition);
    }

    const updatedLocations = editLocations.map(loc => 
      loc.id === updatedLocation.id ? updatedLocation : loc
    );

    setEditLocations(updatedLocations);
    onSave(updatedLocations);
    setShowExhibitionForm(false);
    setCurrentExhibition(null);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Locations</Typography>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<AddIcon />}
          onClick={handleAddLocation}
        >
          Add Location
        </Button>
      </Box>

      {editLocations.length === 0 ? (
        <Paper sx={{ p: 2, textAlign: 'center' }}>
          <Typography variant="body1" color="textSecondary">
            No locations added yet. Click "Add Location" to create your first location.
          </Typography>
        </Paper>
      ) : (
        <List>
          {editLocations.map((location) => (
            <Accordion key={location.id}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>{location.name}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle1">Description: {location.description}</Typography>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography variant="subtitle2">Position: [{location.position.join(', ')}]</Typography>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography variant="subtitle2">Rotation: [{location.rotation.join(', ')}]</Typography>
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Typography variant="subtitle2">Scale: [{location.scale.join(', ')}]</Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                      <Typography variant="subtitle1">Exhibitions ({location.exhibitions.length})</Typography>
                      <Button 
                        size="small" 
                        startIcon={<AddIcon />}
                        onClick={() => handleAddExhibition(location.id)}
                      >
                        Add Exhibition
                      </Button>
                    </Box>
                    {location.exhibitions.length > 0 ? (
                      <List dense>
                        {location.exhibitions.map((exhibition) => (
                          <ListItem key={exhibition.id}>
                            <ListItemText 
                              primary={exhibition.name} 
                              secondary={`Spaces: ${exhibition.spaces.length}`} 
                            />
                            <ListItemSecondaryAction>
                              <IconButton edge="end" onClick={() => {
                                setCurrentExhibition(exhibition);
                                setCurrentLocation(location);
                                setShowExhibitionForm(true);
                              }}>
                                <EditIcon />
                              </IconButton>
                            </ListItemSecondaryAction>
                          </ListItem>
                        ))}
                      </List>
                    ) : (
                      <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                        No exhibitions added yet
                      </Typography>
                    )}
                  </Grid>
                </Grid>
                <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                  <Button 
                    color="error" 
                    startIcon={<DeleteIcon />}
                    onClick={() => handleDeleteLocation(location.id)}
                    sx={{ mr: 1 }}
                  >
                    Delete
                  </Button>
                  <Button 
                    variant="outlined" 
                    startIcon={<EditIcon />}
                    onClick={() => handleEditLocation(location)}
                  >
                    Edit
                  </Button>
                </Box>
              </AccordionDetails>
            </Accordion>
          ))}
        </List>
      )}

      {showLocationForm && currentLocation && (
        <Paper sx={{ p: 2, mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            {currentLocation.id ? 'Edit Location' : 'Add Location'}
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Name"
                fullWidth
                value={currentLocation.name}
                onChange={(e) => setCurrentLocation({...currentLocation, name: e.target.value})}
                margin="normal"
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Description"
                fullWidth
                value={currentLocation.description}
                onChange={(e) => setCurrentLocation({...currentLocation, description: e.target.value})}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <VectorInput
                label="Position"
                value={currentLocation.position}
                onChange={(value) => setCurrentLocation({...currentLocation, position: value})}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <VectorInput
                label="Rotation"
                value={currentLocation.rotation}
                onChange={(value) => setCurrentLocation({...currentLocation, rotation: value})}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <VectorInput
                label="Scale"
                value={currentLocation.scale}
                onChange={(value) => setCurrentLocation({...currentLocation, scale: value})}
              />
            </Grid>
          </Grid>
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button 
              onClick={() => {
                setShowLocationForm(false);
                setCurrentLocation(null);
              }} 
              sx={{ mr: 1 }}
            >
              Cancel
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSaveLocation}
            >
              Save Location
            </Button>
          </Box>
        </Paper>
      )}

      {showExhibitionForm && currentExhibition && currentLocation && (
        <ExhibitionEditor
          exhibition={currentExhibition}
          onSave={handleSaveExhibition}
          onCancel={() => {
            setShowExhibitionForm(false);
            setCurrentExhibition(null);
          }}
        />
      )}
    </Box>
  );
};

export default LocationEditor;
