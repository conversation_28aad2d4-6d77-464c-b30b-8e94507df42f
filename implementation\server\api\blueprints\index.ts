import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { BlueprintService } from '../../services/blueprint-service';

// Define the query parameters schema for GET
const GetQueryParamsSchema = z.object({
  vendor_id: z.string().uuid().optional(),
  tags: z.string().optional(),
  limit: z.string().transform(Number).optional(),
  offset: z.string().transform(Number).optional(),
});

// Define the request body schema for POST
const PostBodySchema = z.object({
  vendor_id: z.string().uuid(),
  name: z.string(),
  description: z.string().optional().nullable(),
  tags: z.array(z.string()).optional().default([]),
  script: z.record(z.any()),
  version: z.string().optional().default('1.0.0'),
});

/**
 * Blueprints API endpoint
 *
 * This endpoint handles listing and creating blueprints.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create blueprint service
    const blueprintService = new BlueprintService(supabase);

    // Handle GET request (list blueprints)
    if (req.method === 'GET') {
      // Validate query parameters
      const queryResult = GetQueryParamsSchema.safeParse(req.query);
      if (!queryResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
      }

      // Extract parameters
      const { vendor_id, tags, limit = 100, offset = 0 } = queryResult.data;

      // Log the request
      logger.info('Blueprints request', {
        vendor_id,
        tags,
        limit,
        offset,
      });

      // Build query
      let query = supabase.from('blueprints').select('*');

      if (vendor_id) {
        query = query.eq('vendor_id', vendor_id);
      }

      if (tags) {
        const tagArray = tags.split(',');
        query = query.contains('tags', tagArray);
      }

      // Add pagination
      query = query.range(offset, offset + limit - 1);

      // Execute query
      const { data: blueprints, error } = await query;

      if (error) {
        logger.error('Error fetching blueprints', { error });
        return errorHandler(error, res);
      }

      // Get total count
      const { count: totalCount, error: countError } = await supabase
        .from('blueprints')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        logger.error('Error getting total count', { error: countError });
        // Continue anyway, just don't return total count
      }

      // Return blueprints
      return res.status(200).json({
        status: 'success',
        data: {
          blueprints,
          pagination: {
            limit,
            offset,
            total: totalCount,
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        },
      });
    }

    // Handle POST request (create blueprint)
    if (req.method === 'POST') {
      // Validate request body
      const bodyResult = PostBodySchema.safeParse(req.body);
      if (!bodyResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid request body', details: bodyResult.error.format() });
      }

      // Extract request data
      const blueprintData = bodyResult.data;

      // Log the request
      logger.info('Create blueprint request', {
        vendor_id: blueprintData.vendor_id,
        name: blueprintData.name,
      });

      // Create blueprint
      const blueprint = await blueprintService.createBlueprint(
        blueprintData.vendor_id,
        blueprintData.name,
        blueprintData.description || null,
        blueprintData.tags,
        blueprintData.script,
        blueprintData.version,
      );

      if (!blueprint) {
        return res.status(500).json({ error: 'Failed to create blueprint' });
      }

      // Return created blueprint
      return res.status(201).json({
        status: 'success',
        data: blueprint,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        },
      });
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Unexpected error in blueprints endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
