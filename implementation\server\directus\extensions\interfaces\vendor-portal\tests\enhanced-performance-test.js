/**
 * Test script for enhanced PerformanceOptimizer and VirtualListRenderer
 */
const { PerformanceOptimizer, VirtualListRenderer } = require('../src/utils/PerformanceOptimizer');

// Test 1: Enhanced LRU Cache with Memory Management
console.log('\n=== Test 1: Enhanced LRU Cache with Memory Management ===');

// Create a cache with memory management
const cache = new PerformanceOptimizer(100, 60 * 1000, {
  maxMemorySize: 1024 * 1024, // 1MB
  evictionThreshold: 0.8
});

// Add some items to the cache
for (let i = 0; i < 50; i++) {
  const key = `key-${i}`;
  const value = { 
    id: i,
    name: `Item ${i}`,
    data: Array(i * 100).fill('x').join('')  // Create increasingly larger items
  };
  
  cache.set(key, value);
}

// Get some items to update access patterns
for (let i = 0; i < 30; i++) {
  const key = `key-${Math.floor(Math.random() * 50)}`;
  cache.get(key);
}

// Print cache statistics
console.log('Cache statistics:', cache.getStats());

// Test 2: VirtualListRenderer with Prefetching and Web Workers
console.log('\n=== Test 2: VirtualListRenderer with Prefetching and Web Workers ===');

// Create mock items
const mockItems = Array.from({ length: 50 }, (_, i) => ({
  id: `item-${i}`,
  name: `Item ${i}`,
  value: i
}));

// Create mock load more function
const mockLoadMoreItems = async (page, pageSize) => {
  console.log(`Loading page ${page} with pageSize ${pageSize}`);
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return Array.from({ length: pageSize }, (_, i) => ({
    id: `item-${(page - 1) * pageSize + i + mockItems.length}`,
    name: `Item ${(page - 1) * pageSize + i + mockItems.length}`,
    value: (page - 1) * pageSize + i + mockItems.length
  }));
};

// Create renderer with prefetching
const renderer = new VirtualListRenderer(
  mockItems,
  40, // Item height
  400, // Container height
  5, // Buffer
  {
    lazyLoad: true,
    loadMoreItems: mockLoadMoreItems,
    loadThreshold: 0.8,
    pageSize: 20,
    totalItems: 200,
    prefetch: true,
    prefetchThreshold: 0.5
  }
);

// Test prefetching
console.log('\nTesting prefetching:');
console.log('Initial state:', {
  itemCount: renderer.items.length,
  isPrefetching: renderer.isPrefetching,
  prefetchedData: !!renderer.prefetchedData
});

// Trigger prefetching
renderer.prefetchNextPage();

// Wait for prefetching to complete
setTimeout(() => {
  console.log('After prefetching:', {
    itemCount: renderer.items.length,
    isPrefetching: renderer.isPrefetching,
    prefetchedData: !!renderer.prefetchedData
  });
  
  // Test scrolling and lazy loading
  console.log('\nTesting scrolling and lazy loading:');
  
  // Scroll to trigger lazy loading
  const totalHeight = renderer.items.length * renderer.itemHeight;
  const scrollPosition = (totalHeight - renderer.containerHeight) * renderer.loadThreshold + 1;
  console.log(`Scrolling to position: ${scrollPosition}`);
  
  renderer.updateScroll(scrollPosition);
  
  // Wait for lazy loading to complete
  setTimeout(() => {
    console.log('After lazy loading:', {
      itemCount: renderer.items.length,
      isPrefetching: renderer.isPrefetching,
      prefetchedData: !!renderer.prefetchedData,
      metrics: renderer.getMetrics()
    });
    
    // Clean up
    renderer.dispose();
    cache.dispose();
    
    console.log('\nTests completed successfully!');
  }, 500);
}, 500);
