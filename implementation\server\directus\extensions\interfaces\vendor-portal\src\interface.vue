<template>
  <div class="vendor-portal">
    <div class="header">
      <h1>Vendor Portal</h1>
      <div v-if="loading" class="loading">Loading...</div>
    </div>

    <div class="tabs">
      <button
        v-for="tab in availableTabs"
        :key="tab.id"
        :class="{ active: activeTab === tab.id }"
        @click="activeTab = tab.id"
      >
        {{ tab.name }}
      </button>
    </div>

    <div class="content">
      <!-- Dashboard Tab -->
      <div v-if="activeTab === 'dashboard'" class="tab-content">
        <DashboardOverview
          :vendor-id="vendorId"
          @view-products="activeTab = 'products'"
          @view-clients="activeTab = 'clients'"
          @view-showrooms="activeTab = 'showrooms'"
          @view-subscription="activeTab = 'subscription'"
          @view-analytics="activeTab = 'analytics'"
        />
      </div>

      <!-- Products Tab -->
      <div v-if="activeTab === 'products'" class="tab-content">
        <ProductManager :vendor-id="vendorId" />
      </div>

      <!-- Categories Tab -->
      <div v-if="activeTab === 'categories'" class="tab-content">
        <CategoryManager :vendor-id="vendorId" />
      </div>

      <!-- Clients Tab -->
      <div v-if="activeTab === 'clients'" class="tab-content">
        <ClientManager :vendor-id="vendorId" />
      </div>

      <!-- Showrooms Tab -->
      <div v-if="activeTab === 'showrooms'" class="tab-content">
        <ShowroomManager :vendor-id="vendorId" />
      </div>

      <!-- Subscription Tab -->
      <div v-if="activeTab === 'subscription'" class="tab-content">
        <SubscriptionManager :vendor-id="vendorId" />
      </div>

      <!-- Branding Tab -->
      <div v-if="activeTab === 'branding'" class="tab-content">
        <BrandingManager :vendor-id="vendorId" />
      </div>

      <!-- Analytics Tab -->
      <div v-if="activeTab === 'analytics'" class="tab-content">
        <AnalyticsManager :vendor-id="vendorId" />
      </div>

      <!-- Preview & Testing Tab -->
      <div v-if="activeTab === 'preview-testing'" class="tab-content">
        <PreviewTestingTools />
      </div>

      <!-- Visual Editors Tab -->
      <div v-if="activeTab === 'visual-editors'" class="tab-content">
        <VisualEditors :vendor-id="vendorId" />
      </div>

      <!-- Collaboration Tab -->
      <div v-if="activeTab === 'collaboration'" class="tab-content">
        <CollaborationFeatures />
      </div>

      <!-- Setup Wizard Tab -->
      <div v-if="activeTab === 'setup-wizard'" class="tab-content">
        <GuidedSetupWizard :vendor-id="vendorId" />
      </div>
    </div>
  </div>
</template>

<script>
import ProductManager from './components/ProductManager.vue';
import AnalyticsChart from './components/AnalyticsChart.vue';
import ClientManager from './components/ClientManager.vue';
import SubscriptionManager from './components/SubscriptionManager.vue';
import ShowroomManager from './components/ShowroomManager.vue';
import DashboardOverview from './components/DashboardOverview.vue';
import BrandingManager from './components/BrandingManager.vue';
import AnalyticsManager from './components/AnalyticsManager.vue';
import CategoryManager from './components/CategoryManager.vue';
import PreviewTestingTools from './components/PreviewTestingTools.vue';
import CollaborationFeatures from './components/CollaborationFeatures.vue';
import GuidedSetupWizard from './components/GuidedSetupWizard/GuidedSetupWizard.vue';
import VisualEditors from './components/VisualEditors/VisualEditors.vue';

export default {
  name: 'InterfaceVendorPortal',

  components: {
    ProductManager,
    AnalyticsChart,
    ClientManager,
    SubscriptionManager,
    ShowroomManager,
    DashboardOverview,
    BrandingManager,
    AnalyticsManager,
    CategoryManager,
    PreviewTestingTools,
    CollaborationFeatures,
    GuidedSetupWizard,
    VisualEditors
  },

  props: {
    value: {
      type: [Object, String],
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      loading: true,
      analyticsLoading: false,
      activeTab: 'dashboard',
      products: [],
      clients: [],
      showrooms: [],
      subscription: null,
      branding: null,
      analytics: {}
    };
  },

  computed: {
    availableTabs() {
      const tabs = [];

      if (this.options.showDashboard !== false) {
        tabs.push({ id: 'dashboard', name: 'Dashboard' });
      }

      if (this.options.showProducts !== false) {
        tabs.push({ id: 'products', name: 'Products' });
      }

      if (this.options.showCategories !== false) {
        tabs.push({ id: 'categories', name: 'Categories' });
      }

      if (this.options.showClients !== false) {
        tabs.push({ id: 'clients', name: 'Clients' });
      }

      if (this.options.showShowrooms !== false) {
        tabs.push({ id: 'showrooms', name: 'Showrooms' });
      }

      if (this.options.showSubscription !== false) {
        tabs.push({ id: 'subscription', name: 'Subscription' });
      }

      if (this.options.showBranding !== false) {
        tabs.push({ id: 'branding', name: 'Branding' });
      }

      if (this.options.showAnalytics !== false) {
        tabs.push({ id: 'analytics', name: 'Analytics' });
      }

      if (this.options.showPreviewTesting !== false) {
        tabs.push({ id: 'preview-testing', name: 'Preview & Testing' });
      }

      if (this.options.showVisualEditors !== false) {
        tabs.push({ id: 'visual-editors', name: 'Visual Editors' });
      }

      if (this.options.showCollaboration !== false) {
        tabs.push({ id: 'collaboration', name: 'Collaboration' });
      }

      // Always show the Setup Wizard tab
      tabs.push({ id: 'setup-wizard', name: 'Setup Wizard' });

      return tabs;
    },

    vendorId() {
      return this.options.vendorId || this.getCurrentVendorId();
    }
  },

  mounted() {
    this.loadData();
  },

  methods: {
    async loadData() {
      this.loading = true;

      try {
        // Load data based on active tab
        if (this.activeTab === 'dashboard') {
          // For dashboard, we need to load multiple data sets
          await Promise.all([
            this.loadProducts(),
            this.loadClients(),
            this.loadShowrooms(),
            this.loadSubscription(),
            this.loadAnalytics()
          ]);
        } else if (this.activeTab === 'products') {
          await this.loadProducts();
        } else if (this.activeTab === 'categories') {
          await this.loadCategories();
        } else if (this.activeTab === 'clients') {
          await this.loadClients();
        } else if (this.activeTab === 'showrooms') {
          await this.loadShowrooms();
        } else if (this.activeTab === 'subscription') {
          await this.loadSubscription();
        } else if (this.activeTab === 'branding') {
          await this.loadBranding();
        } else if (this.activeTab === 'analytics') {
          await this.loadAnalytics();
        } else if (this.activeTab === 'preview-testing' || this.activeTab === 'collaboration' || this.activeTab === 'setup-wizard' || this.activeTab === 'visual-editors') {
          // These components manage their own data loading
          console.log(`Loading ${this.activeTab} tab`);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        this.loading = false;
      }
    },

    async loadProducts() {
      // This would be replaced with actual API call
      this.products = [
        { id: '1', name: 'Sample Product 1', description: 'This is a sample product', status: 'published' },
        { id: '2', name: 'Sample Product 2', description: 'Another sample product', status: 'draft' }
      ];
    },

    async loadCategories() {
      // This would be replaced with actual API call
      // Categories are loaded directly in the CategoryManager component
      console.log('Loading categories for vendor:', this.vendorId);
    },

    async loadClients() {
      // This would be replaced with actual API call
      // const response = await axios.get(`/api/clients?vendor_id=${this.vendorId}`);
      // this.clients = response.data;

      // Mock data for demonstration
      this.clients = [
        {
          id: '1',
          name: 'John Smith',
          company: 'Acme Corporation',
          email: '<EMAIL>',
          phone: '(*************',
          status: 'active',
          last_activity: '2023-05-20T14:45:00Z'
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          company: 'Johnson Interiors',
          email: '<EMAIL>',
          phone: '(*************',
          status: 'active',
          last_activity: '2023-05-18T11:20:00Z'
        },
        {
          id: '3',
          name: 'Michael Brown',
          company: 'Brown & Associates',
          email: '<EMAIL>',
          phone: '(*************',
          status: 'inactive',
          last_activity: '2023-04-10T09:15:00Z'
        }
      ];
    },

    async loadShowrooms() {
      // This would be replaced with actual API call
      this.showrooms = [
        { id: '1', name: 'Main Showroom', description: 'Our main virtual showroom', status: 'published' },
        { id: '2', name: 'New Collection', description: 'Featuring our newest products', status: 'draft' }
      ];
    },

    async loadSubscription() {
      // This would be replaced with actual API call
      // const response = await axios.get(`/api/subscription?vendor_id=${this.vendorId}`);
      // this.subscription = response.data;

      // Mock data for demonstration
      this.subscription = {
        id: 'sub_12345',
        plan: 'Professional',
        status: 'Active',
        price: 49.99,
        startDate: '2023-01-15T00:00:00Z',
        nextBillingDate: '2023-06-15T00:00:00Z'
      };
    },

    async loadBranding() {
      // This would be replaced with actual API call
      this.branding = {
        primary_color: '#3498db',
        secondary_color: '#2c3e50',
        accent_color: '#e74c3c',
        font_family: 'Arial, sans-serif'
      };
    },

    async loadAnalytics() {
      // This would be replaced with actual API call
      this.analytics = {
        visits: 1250,
        productViews: 3720,
        engagementRate: '42%'
      };
    },

    async updateAnalytics(period) {
      this.analyticsLoading = true;

      try {
        // This would be replaced with actual API call to get analytics for the specified period
        console.log(`Loading analytics for period: ${period}`);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Update analytics data
        const multiplier = period === 'day' ? 1 :
                          period === 'week' ? 7 :
                          period === 'month' ? 30 : 365;

        this.analytics = {
          visits: Math.floor(1250 * (multiplier / 7)),
          productViews: Math.floor(3720 * (multiplier / 7)),
          engagementRate: `${Math.floor(42 * (0.8 + (Math.random() * 0.4)))}%`
        };
      } catch (error) {
        console.error('Error updating analytics:', error);
      } finally {
        this.analyticsLoading = false;
      }
    },

    getCurrentVendorId() {
      // This would be replaced with actual logic to get current vendor ID
      return 'current-vendor-id';
    }
  },

  watch: {
    activeTab() {
      this.loadData();
    }
  }
};
</script>

<style scoped>
.vendor-portal {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  max-width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--theme--border-color);
  margin-bottom: 20px;
}

.tabs button {
  background: none;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tabs button.active {
  color: var(--theme--primary);
  border-bottom-color: var(--theme--primary);
}

.tab-content {
  padding: 10px 0;
}

.empty-state {
  padding: 40px;
  text-align: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  color: var(--theme--foreground-subdued);
}

.product-grid, .showroom-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.product-card, .showroom-card {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  transition: all 0.2s ease;
}

.product-card:hover, .showroom-card:hover {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.product-image {
  height: 150px;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-info, .showroom-info {
  padding: 15px;
}

.product-status, .showroom-status {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  margin-top: 10px;
}

.product-status.published, .showroom-status.published {
  background-color: var(--theme--primary);
  color: white;
}

.product-status.draft, .showroom-status.draft {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
}

.branding-preview {
  margin-top: 20px;
}

.color-palette {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.color-sample {
  width: 150px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--theme--border-radius);
  color: white;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
}

.font-preview {
  padding: 20px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
}

.analytics-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.analytics-card {
  padding: 20px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  text-align: center;
}

.metric {
  font-size: 32px;
  font-weight: bold;
  color: var(--theme--primary);
  margin-top: 10px;
}

.analytics-charts {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-top: 30px;
}
</style>
