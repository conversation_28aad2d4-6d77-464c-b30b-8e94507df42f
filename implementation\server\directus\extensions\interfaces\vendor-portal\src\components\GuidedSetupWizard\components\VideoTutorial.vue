<template>
  <div class="video-tutorial" :class="{ 'is-expanded': isExpanded }">
    <div class="video-header">
      <h3 class="video-title">{{ title }}</h3>
      <div class="video-controls">
        <button 
          v-if="!isExpanded"
          @click="expandVideo"
          class="expand-button"
          aria-label="Expand video"
        >
          <i class="material-icons">fullscreen</i>
        </button>
        <button 
          v-else
          @click="collapseVideo"
          class="collapse-button"
          aria-label="Collapse video"
        >
          <i class="material-icons">fullscreen_exit</i>
        </button>
      </div>
    </div>
    
    <div class="video-container" :class="{ 'is-expanded': isExpanded }">
      <div v-if="isLoading" class="video-loading">
        <div class="loading-spinner"></div>
        <p>Loading video...</p>
      </div>
      
      <div v-else-if="error" class="video-error">
        <p>{{ error }}</p>
        <button @click="loadVideo" class="retry-button">Retry</button>
      </div>
      
      <div v-else class="video-player-container">
        <video
          ref="videoPlayer"
          class="video-player"
          :src="videoSrc"
          :poster="posterSrc"
          :controls="showControls"
          :autoplay="autoplay"
          :loop="loop"
          :muted="muted"
          @play="handlePlay"
          @pause="handlePause"
          @ended="handleEnded"
          @timeupdate="handleTimeUpdate"
          @loadedmetadata="handleMetadataLoaded"
          @error="handleVideoError"
        ></video>
        
        <div v-if="!showControls" class="custom-controls">
          <div class="progress-container" @click="seekVideo">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: `${progressPercentage}%` }"></div>
            </div>
            <div class="progress-time">{{ formattedCurrentTime }} / {{ formattedDuration }}</div>
          </div>
          
          <div class="control-buttons">
            <button 
              @click="togglePlay"
              class="control-button"
              :aria-label="isPlaying ? 'Pause' : 'Play'"
            >
              <i class="material-icons">{{ isPlaying ? 'pause' : 'play_arrow' }}</i>
            </button>
            
            <button 
              @click="toggleMute"
              class="control-button"
              :aria-label="isMuted ? 'Unmute' : 'Mute'"
            >
              <i class="material-icons">{{ isMuted ? 'volume_off' : 'volume_up' }}</i>
            </button>
            
            <div class="volume-slider-container">
              <input 
                type="range" 
                min="0" 
                max="1" 
                step="0.1" 
                v-model="volume" 
                class="volume-slider"
                @input="updateVolume"
                aria-label="Volume"
              />
            </div>
            
            <button 
              @click="toggleFullscreen"
              class="control-button"
              aria-label="Toggle fullscreen"
            >
              <i class="material-icons">fullscreen</i>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <div v-if="transcript" class="video-transcript">
      <div class="transcript-header">
        <h4 class="transcript-title">Transcript</h4>
        <button 
          @click="toggleTranscript"
          class="transcript-toggle"
          :aria-expanded="showTranscript.toString()"
          aria-controls="video-transcript-content"
        >
          {{ showTranscript ? 'Hide' : 'Show' }}
        </button>
      </div>
      
      <transition name="slide">
        <div 
          v-if="showTranscript"
          id="video-transcript-content"
          class="transcript-content"
        >
          <p v-for="(segment, index) in transcriptSegments" 
             :key="index" 
             class="transcript-segment"
             :class="{ 'active': isCurrentSegment(segment) }"
             @click="seekToSegment(segment)"
          >
            <span class="segment-time">{{ formatTime(segment.startTime) }}</span>
            <span class="segment-text">{{ segment.text }}</span>
          </p>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VideoTutorial',
  
  props: {
    title: {
      type: String,
      default: 'Video Tutorial'
    },
    videoSrc: {
      type: String,
      required: true
    },
    posterSrc: {
      type: String,
      default: ''
    },
    transcript: {
      type: Array,
      default: () => []
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    loop: {
      type: Boolean,
      default: false
    },
    muted: {
      type: Boolean,
      default: false
    },
    showControls: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      isLoading: true,
      error: null,
      isPlaying: false,
      isMuted: this.muted,
      volume: 1,
      currentTime: 0,
      duration: 0,
      isExpanded: false,
      showTranscript: false,
      transcriptSegments: []
    };
  },
  
  computed: {
    progressPercentage() {
      return this.duration ? (this.currentTime / this.duration) * 100 : 0;
    },
    
    formattedCurrentTime() {
      return this.formatTime(this.currentTime);
    },
    
    formattedDuration() {
      return this.formatTime(this.duration);
    }
  },
  
  mounted() {
    this.loadVideo();
    this.processTranscript();
  },
  
  beforeDestroy() {
    // Clean up any resources
    if (this.$refs.videoPlayer) {
      this.$refs.videoPlayer.pause();
    }
  },
  
  methods: {
    loadVideo() {
      this.isLoading = true;
      this.error = null;
      
      // Simulate loading delay
      setTimeout(() => {
        this.isLoading = false;
      }, 1000);
    },
    
    processTranscript() {
      if (this.transcript && this.transcript.length > 0) {
        this.transcriptSegments = this.transcript.map(segment => ({
          startTime: segment.startTime || 0,
          endTime: segment.endTime || 0,
          text: segment.text || ''
        }));
      } else {
        // Generate mock transcript if none provided
        this.transcriptSegments = [
          { startTime: 0, endTime: 15, text: 'Welcome to this tutorial on using the Guided Setup Wizard.' },
          { startTime: 15, endTime: 30, text: 'In this video, we\'ll walk through the process step by step.' },
          { startTime: 30, endTime: 45, text: 'First, you\'ll need to enter your company information and upload your logo.' },
          { startTime: 45, endTime: 60, text: 'Next, you\'ll set up user accounts for your team members.' },
          { startTime: 60, endTime: 75, text: 'Then, you\'ll configure your brand settings including colors and fonts.' },
          { startTime: 75, endTime: 90, text: 'After that, you\'ll upload your products and organize them into categories.' },
          { startTime: 90, endTime: 105, text: 'Finally, you\'ll configure your virtual showroom layout and settings.' },
          { startTime: 105, endTime: 120, text: 'Once completed, your virtual showroom will be ready for your clients to explore.' }
        ];
      }
    },
    
    handlePlay() {
      this.isPlaying = true;
      this.$emit('play');
    },
    
    handlePause() {
      this.isPlaying = false;
      this.$emit('pause');
    },
    
    handleEnded() {
      this.isPlaying = false;
      this.$emit('ended');
    },
    
    handleTimeUpdate() {
      if (this.$refs.videoPlayer) {
        this.currentTime = this.$refs.videoPlayer.currentTime;
        this.$emit('timeupdate', this.currentTime);
      }
    },
    
    handleMetadataLoaded() {
      if (this.$refs.videoPlayer) {
        this.duration = this.$refs.videoPlayer.duration;
        this.$emit('loaded', {
          duration: this.duration,
          videoWidth: this.$refs.videoPlayer.videoWidth,
          videoHeight: this.$refs.videoPlayer.videoHeight
        });
      }
    },
    
    handleVideoError() {
      this.isLoading = false;
      this.error = 'Failed to load video. Please check your connection and try again.';
      this.$emit('error', this.error);
    },
    
    togglePlay() {
      if (this.$refs.videoPlayer) {
        if (this.isPlaying) {
          this.$refs.videoPlayer.pause();
        } else {
          this.$refs.videoPlayer.play();
        }
      }
    },
    
    toggleMute() {
      if (this.$refs.videoPlayer) {
        this.isMuted = !this.isMuted;
        this.$refs.videoPlayer.muted = this.isMuted;
      }
    },
    
    updateVolume() {
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.volume = this.volume;
        this.isMuted = this.volume === 0;
      }
    },
    
    seekVideo(event) {
      if (this.$refs.videoPlayer && this.duration) {
        const progressBar = event.currentTarget;
        const rect = progressBar.getBoundingClientRect();
        const offsetX = event.clientX - rect.left;
        const percentage = offsetX / rect.width;
        
        this.$refs.videoPlayer.currentTime = percentage * this.duration;
      }
    },
    
    toggleFullscreen() {
      if (this.$refs.videoPlayer) {
        if (document.fullscreenElement) {
          document.exitFullscreen();
        } else {
          this.$refs.videoPlayer.requestFullscreen();
        }
      }
    },
    
    expandVideo() {
      this.isExpanded = true;
      this.$emit('expand');
    },
    
    collapseVideo() {
      this.isExpanded = false;
      this.$emit('collapse');
    },
    
    toggleTranscript() {
      this.showTranscript = !this.showTranscript;
    },
    
    formatTime(seconds) {
      if (!seconds) return '0:00';
      
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
    },
    
    isCurrentSegment(segment) {
      return this.currentTime >= segment.startTime && this.currentTime < segment.endTime;
    },
    
    seekToSegment(segment) {
      if (this.$refs.videoPlayer) {
        this.$refs.videoPlayer.currentTime = segment.startTime;
        if (!this.isPlaying) {
          this.$refs.videoPlayer.play();
        }
      }
    }
  }
};
</script>

<style scoped>
.video-tutorial {
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  overflow: hidden;
  background-color: var(--theme--background);
  margin-bottom: 24px;
}

.video-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
}

.video-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.video-controls {
  display: flex;
  gap: 8px;
}

.expand-button,
.collapse-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.expand-button:hover,
.collapse-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.video-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 aspect ratio */
  background-color: #000;
}

.video-container.is-expanded {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  padding-top: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.9);
}

.video-loading,
.video-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.retry-button {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  margin-top: 16px;
}

.video-player-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-player-container:hover .custom-controls {
  opacity: 1;
}

.progress-container {
  margin-bottom: 8px;
  cursor: pointer;
}

.progress-bar {
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background-color: var(--theme--primary);
  transition: width 0.1s linear;
}

.progress-time {
  font-size: 12px;
  color: #fff;
  text-align: right;
}

.control-buttons {
  display: flex;
  align-items: center;
}

.control-button {
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.volume-slider-container {
  width: 80px;
  margin-right: 16px;
}

.volume-slider {
  width: 100%;
  height: 4px;
  -webkit-appearance: none;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #fff;
  cursor: pointer;
}

.video-transcript {
  border-top: 1px solid var(--theme--border-color);
}

.transcript-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--theme--background-subdued);
}

.transcript-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.transcript-toggle {
  background: none;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  color: var(--theme--foreground);
  transition: all 0.2s ease;
}

.transcript-toggle:hover {
  background-color: var(--theme--background-accent);
}

.transcript-content {
  max-height: 200px;
  overflow-y: auto;
  padding: 16px;
}

.transcript-segment {
  margin: 0 0 12px 0;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.transcript-segment:hover {
  background-color: var(--theme--background-subdued);
}

.transcript-segment.active {
  background-color: rgba(var(--theme--primary-rgb), 0.1);
  border-left: 3px solid var(--theme--primary);
  padding-left: 12px;
}

.segment-time {
  display: inline-block;
  font-weight: 600;
  margin-right: 8px;
  color: var(--theme--foreground-subdued);
}

.segment-text {
  color: var(--theme--foreground);
}

/* Transitions */
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
  max-height: 200px;
}

.slide-enter,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}
</style>
