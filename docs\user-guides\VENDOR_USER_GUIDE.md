# MVS-VR Vendor User Guide

This guide provides comprehensive instructions for vendors using the MVS-VR platform. It covers the vendor portal overview, asset management, showroom management, analytics, and subscription management.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Vendor Portal Overview](#vendor-portal-overview)
3. [Asset Management](#asset-management)
4. [Showroom Management](#showroom-management)
5. [Analytics](#analytics)
6. [Subscription Management](#subscription-management)
7. [Team Management](#team-management)
8. [Branding](#branding)
9. [Troubleshooting](#troubleshooting)

## Getting Started

### Accessing the Vendor Portal

1. Navigate to `https://your-domain.com/vendor` in your web browser
2. Enter your vendor credentials (email and password)
3. If enabled, complete the two-factor authentication process
4. You will be redirected to the vendor dashboard

### First-Time Setup

If you're accessing the vendor portal for the first time, you'll need to complete the following steps:

1. Change your temporary password
2. Set up two-factor authentication (recommended)
3. Complete your vendor profile
4. Set up your branding
5. Invite team members (if needed)

### Navigation

The vendor portal has a sidebar navigation menu with the following sections:

- **Dashboard**: Overview of your vendor account and key metrics
- **Assets**: Asset management
- **Showrooms**: Showroom management
- **Clients**: Client management
- **Analytics**: Analytics and reporting
- **Team**: Team management
- **Branding**: Branding management
- **Subscription**: Subscription management
- **Settings**: Vendor portal settings

## Vendor Portal Overview

The vendor dashboard provides a comprehensive overview of your vendor account and key metrics.

### Dashboard Widgets

The dashboard consists of customizable widgets that display various metrics and information:

- **Asset Overview**: Shows the number of assets by type and status
- **Showroom Overview**: Displays the number of showrooms and their status
- **Client Overview**: Shows the number of clients and their status
- **Recent Activity**: Displays recent activity in your vendor account
- **Storage Usage**: Shows your storage usage
- **Popular Assets**: Displays your most popular assets
- **Recent Feedback**: Shows recent client feedback

### Customizing the Dashboard

You can customize the dashboard to show the information most relevant to you:

1. Click the **Customize** button in the top-right corner of the dashboard
2. Drag and drop widgets to rearrange them
3. Click the **Add Widget** button to add new widgets
4. Click the gear icon on a widget to configure it or remove it
5. Click **Save** to save your dashboard layout

## Asset Management

The asset management section allows you to manage all your assets on the MVS-VR platform.

### Asset List

The asset list displays all your assets with the following information:

- **Name**: Asset name
- **Type**: Asset type (Model, Texture, Material, etc.)
- **Status**: Asset status (Draft, Processing, Published, Error)
- **Size**: Asset size
- **Created**: Date and time the asset was created
- **Updated**: Date and time the asset was last updated
- **Actions**: Actions you can perform on the asset

### Creating a New Asset

To create a new asset:

1. Click the **Create Asset** button in the top-right corner of the asset list
2. Select the asset type (Model, Texture, Material, etc.)
3. Fill in the asset details:
   - **Name**: Asset name
   - **Description**: Asset description
   - **Tags**: Tags for categorizing the asset
   - **Category**: Asset category
   - **Visibility**: Asset visibility (Public, Private)
4. Upload the asset file(s)
5. Click **Create** to create the asset

### Uploading a 3D Model

To upload a 3D model:

1. Follow the steps to create a new asset and select "Model" as the asset type
2. In the file upload section, click **Choose File** or drag and drop your model file
3. Supported formats include:
   - GLB/GLTF (.glb, .gltf)
   - FBX (.fbx)
   - OBJ (.obj)
   - USDZ (.usdz)
4. If your model has textures, you can either:
   - Include them in the model file (recommended for GLB/GLTF)
   - Upload them separately (required for OBJ)
5. Set the model options:
   - **Scale**: Model scale factor
   - **Up Axis**: Model up axis (Y or Z)
   - **Generate LODs**: Whether to generate Levels of Detail
   - **Optimize**: Whether to optimize the model
6. Click **Upload** to upload the model

### Uploading a Texture

To upload a texture:

1. Follow the steps to create a new asset and select "Texture" as the asset type
2. In the file upload section, click **Choose File** or drag and drop your texture file
3. Supported formats include:
   - PNG (.png)
   - JPEG (.jpg, .jpeg)
   - TIFF (.tiff, .tif)
   - EXR (.exr)
   - HDR (.hdr)
4. Set the texture options:
   - **Type**: Texture type (Albedo, Normal, Roughness, etc.)
   - **Generate Mipmaps**: Whether to generate mipmaps
   - **sRGB**: Whether the texture is in sRGB color space
   - **Compress**: Whether to compress the texture
5. Click **Upload** to upload the texture

### Editing an Asset

To edit an asset:

1. Click the edit icon in the Actions column for the asset you want to edit
2. Update the asset details
3. Click **Save** to save your changes

### Publishing an Asset

To publish an asset:

1. Click the publish icon in the Actions column for the asset you want to publish
2. Confirm that you want to publish the asset
3. The asset's status will change to Published, and it will be available for use in showrooms

### Unpublishing an Asset

To unpublish an asset:

1. Click the unpublish icon in the Actions column for the asset you want to unpublish
2. Confirm that you want to unpublish the asset
3. The asset's status will change to Draft, and it will no longer be available for use in showrooms

### Deleting an Asset

To delete an asset:

1. Click the delete icon in the Actions column for the asset you want to delete
2. Confirm that you want to delete the asset
3. The asset will be permanently deleted from the system

### Asset Details

To view detailed information about an asset:

1. Click the asset's name in the asset list
2. The asset details page will display the following information:
   - **Overview**: General information about the asset
   - **Preview**: Preview of the asset
   - **Metadata**: Metadata for the asset
   - **Versions**: Version history for the asset
   - **Usage**: Where the asset is being used
   - **Analytics**: Analytics and metrics for the asset

### Asset Categories

Asset categories help you organize your assets. To manage asset categories:

1. Click the **Categories** button in the top-right corner of the asset list
2. From here, you can:
   - View all categories
   - Create new categories
   - Edit existing categories
   - Delete categories

### Asset Tags

Asset tags help you categorize and search for assets. To add tags to an asset:

1. Edit the asset
2. In the Tags field, enter the tags you want to add
3. Press Enter after each tag
4. Click **Save** to save your changes

## Showroom Management

The showroom management section allows you to create and manage virtual showrooms for your products.

### Showroom List

The showroom list displays all your showrooms with the following information:

- **Name**: Showroom name
- **Status**: Showroom status (Draft, Published)
- **Clients**: Number of clients with access to the showroom
- **Views**: Number of times the showroom has been viewed
- **Created**: Date and time the showroom was created
- **Updated**: Date and time the showroom was last updated
- **Actions**: Actions you can perform on the showroom

### Creating a New Showroom

To create a new showroom:

1. Click the **Create Showroom** button in the top-right corner of the showroom list
2. Fill in the showroom details:
   - **Name**: Showroom name
   - **Description**: Showroom description
   - **Tags**: Tags for categorizing the showroom
   - **Visibility**: Showroom visibility (Public, Private, Client-specific)
3. Click **Create** to create the showroom

### Designing a Showroom

After creating a showroom, you can design it:

1. Click the showroom's name in the showroom list
2. Click the **Design** tab
3. Use the showroom editor to:
   - Add and position assets
   - Create rooms and areas
   - Set lighting and environment
   - Add interactive elements
   - Configure navigation
4. Click **Save** to save your changes

### Publishing a Showroom

To publish a showroom:

1. Click the publish icon in the Actions column for the showroom you want to publish
2. Confirm that you want to publish the showroom
3. The showroom's status will change to Published, and it will be available for clients to view

### Unpublishing a Showroom

To unpublish a showroom:

1. Click the unpublish icon in the Actions column for the showroom you want to unpublish
2. Confirm that you want to unpublish the showroom
3. The showroom's status will change to Draft, and it will no longer be available for clients to view

### Deleting a Showroom

To delete a showroom:

1. Click the delete icon in the Actions column for the showroom you want to delete
2. Confirm that you want to delete the showroom
3. The showroom will be permanently deleted from the system

### Showroom Details

To view detailed information about a showroom:

1. Click the showroom's name in the showroom list
2. The showroom details page will display the following information:
   - **Overview**: General information about the showroom
   - **Preview**: Preview of the showroom
   - **Design**: Showroom editor
   - **Assets**: Assets used in the showroom
   - **Clients**: Clients with access to the showroom
   - **Analytics**: Analytics and metrics for the showroom

### Sharing a Showroom

To share a showroom with clients:

1. Click the showroom's name in the showroom list
2. Click the **Clients** tab
3. Click the **Add Client** button
4. Select the clients you want to share the showroom with
5. Click **Add** to give the selected clients access to the showroom

## Analytics

The analytics section provides insights into how clients are interacting with your assets and showrooms.

### Analytics Dashboard

The analytics dashboard provides an overview of key metrics:

- **Showroom Views**: Number of showroom views over time
- **Asset Interactions**: Number of asset interactions over time
- **Client Engagement**: Client engagement metrics
- **Popular Assets**: Most popular assets
- **Popular Showrooms**: Most popular showrooms
- **Client Demographics**: Client demographic information

### Showroom Analytics

To view analytics for a specific showroom:

1. Click the showroom's name in the showroom list
2. Click the **Analytics** tab
3. View the following metrics:
   - **Views**: Number of views over time
   - **Unique Visitors**: Number of unique visitors
   - **Average Session Duration**: Average time spent in the showroom
   - **Heatmap**: Heatmap showing where clients spend the most time
   - **Asset Interactions**: Interactions with assets in the showroom
   - **Client Feedback**: Feedback from clients

### Asset Analytics

To view analytics for a specific asset:

1. Click the asset's name in the asset list
2. Click the **Analytics** tab
3. View the following metrics:
   - **Views**: Number of views over time
   - **Interactions**: Number of interactions over time
   - **Showroom Usage**: Showrooms where the asset is used
   - **Client Feedback**: Feedback from clients

### Custom Reports

The custom reports section allows you to create and save custom reports:

1. Click **Create Custom Report**
2. Select the data source (Showrooms, Assets, Clients)
3. Select the metrics to include
4. Set filters and grouping
5. Set the visualization type (Table, Chart, etc.)
6. Save the report for future use

### Exporting Data

To export analytics data:

1. View the analytics dashboard, showroom analytics, or asset analytics
2. Click the **Export** button
3. Select the export format (CSV, PDF, Excel)
4. Click **Export** to download the data

## Subscription Management

The subscription management section allows you to manage your MVS-VR subscription.

### Subscription Overview

The subscription overview displays information about your current subscription:

- **Plan**: Your current subscription plan
- **Status**: Subscription status (Active, Expired, etc.)
- **Billing Cycle**: Billing cycle (Monthly, Annual)
- **Next Billing Date**: Date of the next billing
- **Payment Method**: Current payment method

### Changing Your Plan

To change your subscription plan:

1. Click the **Change Plan** button
2. Select the new plan
3. Review the plan details and price
4. Click **Confirm** to change your plan

### Updating Payment Information

To update your payment information:

1. Click the **Payment Methods** tab
2. Click **Add Payment Method** to add a new payment method
3. Enter your payment details
4. Click **Save** to save the payment method
5. To set a payment method as default, click the **Set as Default** button

### Viewing Billing History

To view your billing history:

1. Click the **Billing History** tab
2. View a list of all billing transactions
3. Click on a transaction to view details
4. To download an invoice, click the **Download Invoice** button

## Team Management

The team management section allows you to manage your team members on the MVS-VR platform.

### Team Member List

The team member list displays all your team members with the following information:

- **Name**: Team member's name
- **Email**: Team member's email address
- **Role**: Team member's role
- **Status**: Team member's status (Active, Pending, Inactive)
- **Last Login**: Date and time of the team member's last login
- **Actions**: Actions you can perform on the team member

### Inviting a Team Member

To invite a new team member:

1. Click the **Invite Team Member** button in the top-right corner of the team member list
2. Fill in the following information:
   - **Email**: Team member's email address
   - **Role**: Team member's role (Admin, Editor, Viewer)
   - **Message**: Optional message to include in the invitation
3. Click **Send Invitation** to send the invitation

### Managing Team Member Roles

The MVS-VR platform has the following team member roles:

- **Admin**: Full access to the vendor portal and all features
- **Editor**: Can create and edit assets and showrooms, but cannot manage team members or subscription
- **Viewer**: Can view assets and showrooms, but cannot create or edit them

To change a team member's role:

1. Click the edit icon in the Actions column for the team member
2. Select the new role
3. Click **Save** to save your changes

### Deactivating a Team Member

To deactivate a team member:

1. Click the deactivate icon in the Actions column for the team member
2. Confirm that you want to deactivate the team member
3. The team member's status will change to Inactive, and they will no longer be able to log in

### Reactivating a Team Member

To reactivate a team member:

1. Click the activate icon in the Actions column for the team member
2. The team member's status will change to Active, and they will be able to log in again

### Removing a Team Member

To remove a team member:

1. Click the remove icon in the Actions column for the team member
2. Confirm that you want to remove the team member
3. The team member will be removed from your team

## Branding

The branding section allows you to customize the appearance of your vendor portal and showrooms.

### Branding Overview

The branding overview displays your current branding settings:

- **Logo**: Your company logo
- **Colors**: Your brand colors
- **Typography**: Your brand typography
- **Email Templates**: Your email templates
- **Showroom Branding**: Your showroom branding

### Updating Your Logo

To update your logo:

1. Click the **Logo** tab
2. Click **Upload Logo** to upload a new logo
3. Adjust the logo size and position
4. Click **Save** to save your changes

### Customizing Colors

To customize your brand colors:

1. Click the **Colors** tab
2. Set the following colors:
   - **Primary Color**: Main brand color
   - **Secondary Color**: Secondary brand color
   - **Accent Color**: Accent color for highlights
   - **Background Color**: Background color
   - **Text Color**: Text color
3. Click **Save** to save your changes

### Customizing Typography

To customize your typography:

1. Click the **Typography** tab
2. Set the following typography options:
   - **Heading Font**: Font for headings
   - **Body Font**: Font for body text
   - **Font Sizes**: Font sizes for different elements
3. Click **Save** to save your changes

### Customizing Email Templates

To customize your email templates:

1. Click the **Email Templates** tab
2. Select the email template you want to customize
3. Edit the template content
4. Click **Save** to save your changes

### Customizing Showroom Branding

To customize your showroom branding:

1. Click the **Showroom Branding** tab
2. Set the following options:
   - **Loading Screen**: Customize the loading screen
   - **Navigation**: Customize the navigation
   - **UI Elements**: Customize UI elements
   - **Environment**: Customize the environment
3. Click **Save** to save your changes

## Troubleshooting

### Common Issues

1. **Asset Upload Fails**
   - Check that the file format is supported
   - Verify that the file size is within limits
   - Check your internet connection
   - Try uploading a smaller file or a different format

2. **Showroom Editor Issues**
   - Clear your browser cache
   - Try using a different browser
   - Check that your graphics card and drivers are up to date
   - Reduce the number of assets in the showroom

3. **Client Access Issues**
   - Verify that the showroom is published
   - Check that the client has been given access to the showroom
   - Verify that the client's account is active
   - Check if the client is using a supported browser and device

### Getting Help

If you encounter issues not covered in this guide, you can:

- Check the [Vendor FAQ](./VENDOR_FAQ.md)
- Visit the [MVS-VR Support Portal](https://support.mvs-vr.com)
- Contact <NAME_EMAIL>
