/**
 * Business Continuity Controller
 *
 * This controller provides API endpoints for business continuity features.
 */

const express = require('express');
const {
  getBusinessContinuityService,
  SERVICE_STATUS,
} = require('../services/continuity/business-continuity');
const { Logger } = require('../services/integration/logger');

// Create logger
const logger = new Logger();

// Create router
const router = express.Router();

// Get business continuity service
const businessContinuityService = getBusinessContinuityService();

/**
 * Get service status
 *
 * @route GET /api/business-continuity/status
 * @group Business Continuity - Business continuity operations
 * @returns {Object} 200 - Service status
 * @returns {Error} 500 - Unexpected error
 */
router.get('/status', (req, res) => {
  try {
    const serviceStatus = businessContinuityService.getAllServiceStatuses();

    // Count services by status
    const statusCounts = Object.values(serviceStatus).reduce((counts, status) => {
      counts[status] = (counts[status] || 0) + 1;
      return counts;
    }, {});

    // Calculate overall status
    let overallStatus = SERVICE_STATUS.OPERATIONAL;

    if (statusCounts[SERVICE_STATUS.OUTAGE] > 0) {
      overallStatus = SERVICE_STATUS.OUTAGE;
    } else if (statusCounts[SERVICE_STATUS.DEGRADED] > 0) {
      overallStatus = SERVICE_STATUS.DEGRADED;
    } else if (statusCounts[SERVICE_STATUS.MAINTENANCE] > 0) {
      overallStatus = SERVICE_STATUS.MAINTENANCE;
    }

    res.json({
      status: overallStatus,
      services: serviceStatus,
      statusCounts,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error(`Error getting service status: ${error.message}`, { error });
    res.status(500).json({ error: 'Error getting service status' });
  }
});

/**
 * Get service details
 *
 * @route GET /api/business-continuity/services/:serviceId
 * @group Business Continuity - Business continuity operations
 * @param {string} serviceId.path.required - Service ID
 * @returns {Object} 200 - Service details
 * @returns {Error} 404 - Service not found
 * @returns {Error} 500 - Unexpected error
 */
router.get('/services/:serviceId', (req, res) => {
  try {
    const { serviceId } = req.params;

    // Find service
    const service = businessContinuityService.services.find(s => s.id === serviceId);

    if (!service) {
      return res.status(404).json({ error: `Service not found: ${serviceId}` });
    }

    // Get service status
    const status = businessContinuityService.getServiceStatus(serviceId);

    // Get dependencies
    const dependencies = businessContinuityService.dependencies.get(serviceId) || [];

    // Get reverse dependencies
    const reverseDependencies =
      businessContinuityService.dependencies.get(`reverse:${serviceId}`) || [];

    // Calculate business impact
    const impact = businessContinuityService.calculateBusinessImpact(service, status);

    res.json({
      ...service,
      status,
      impact,
      dependencies,
      dependedOnBy: reverseDependencies,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error(`Error getting service details: ${error.message}`, { error });
    res.status(500).json({ error: 'Error getting service details' });
  }
});

/**
 * Get business metrics
 *
 * @route GET /api/business-continuity/metrics
 * @group Business Continuity - Business continuity operations
 * @returns {Object} 200 - Business metrics
 * @returns {Error} 500 - Unexpected error
 */
router.get('/metrics', (req, res) => {
  try {
    const metrics = businessContinuityService.getBusinessMetrics();

    res.json({
      metrics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error(`Error getting business metrics: ${error.message}`, { error });
    res.status(500).json({ error: 'Error getting business metrics' });
  }
});

/**
 * Update business metric
 *
 * @route POST /api/business-continuity/metrics/:metricName
 * @group Business Continuity - Business continuity operations
 * @param {string} metricName.path.required - Metric name
 * @param {Object} request.body.required - Metric data
 * @param {number} request.body.value.required - Metric value
 * @param {Object} request.body.labels - Metric labels
 * @returns {Object} 200 - Updated metric
 * @returns {Error} 400 - Invalid request
 * @returns {Error} 500 - Unexpected error
 */
router.post('/metrics/:metricName', (req, res) => {
  try {
    const { metricName } = req.params;
    const { value, labels = {} } = req.body;

    // Validate request
    if (value === undefined) {
      return res.status(400).json({ error: 'Missing required field: value' });
    }

    // Update metric
    businessContinuityService.updateBusinessMetric(metricName, value, labels);

    // Get updated metric
    const metrics = businessContinuityService.getBusinessMetrics();
    const key = `${metricName}:${JSON.stringify(labels)}`;
    const updatedMetric = metrics[key];

    res.json({
      metric: updatedMetric,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error(`Error updating business metric: ${error.message}`, { error });
    res.status(500).json({ error: 'Error updating business metric' });
  }
});

/**
 * Get service dependency graph
 *
 * @route GET /api/business-continuity/dependencies
 * @group Business Continuity - Business continuity operations
 * @returns {Object} 200 - Service dependency graph
 * @returns {Error} 500 - Unexpected error
 */
router.get('/dependencies', (req, res) => {
  try {
    const nodes = [];
    const edges = [];

    // Create nodes for each service
    for (const service of businessContinuityService.services) {
      const status = businessContinuityService.getServiceStatus(service.id);
      const impact = businessContinuityService.calculateBusinessImpact(service, status);

      nodes.push({
        id: service.id,
        label: service.name,
        status,
        priority: service.priority,
        impact: impact.score,
      });
    }

    // Create edges for dependencies
    for (const service of businessContinuityService.services) {
      const dependencies = businessContinuityService.dependencies.get(service.id) || [];

      for (const dependency of dependencies) {
        edges.push({
          source: dependency,
          target: service.id,
        });
      }
    }

    res.json({
      nodes,
      edges,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error(`Error getting service dependencies: ${error.message}`, { error });
    res.status(500).json({ error: 'Error getting service dependencies' });
  }
});

module.exports = router;
