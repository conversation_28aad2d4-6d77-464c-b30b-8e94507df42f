import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import VisualEditors from '../src/components/VisualEditors/VisualEditors.vue';

// Mock the API
const mockApi = {
  get: vi.fn(),
};

describe('VisualEditors Simple Test', () => {
  let wrapper;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Mock API responses
    mockApi.get.mockResolvedValue({
      data: {
        data: []
      }
    });

    // Mount component
    wrapper = mount(VisualEditors, {
      propsData: {
        vendorId: 'vendor1',
      },
      mocks: {
        $api: mockApi,
      },
      stubs: {
        ShowroomLayoutEditor: true,
        ProductConfigurator: true,
        MaterialTextureEditor: true,
        LightingEditor: true,
        AnimationEditor: true,
      },
    });
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.visual-editors').exists()).toBe(true);
  });

  it('renders the tabs correctly', () => {
    const tabs = wrapper.findAll('.tab-button');
    expect(tabs.length).toBe(5);

    // Check tab names
    expect(tabs[0].text()).toContain('Showroom Layout');
    expect(tabs[1].text()).toContain('Product Config');
    expect(tabs[2].text()).toContain('Materials');
    expect(tabs[3].text()).toContain('Lighting');
    expect(tabs[4].text()).toContain('Animation');
  });

  it('has the correct initial active tab', () => {
    expect(wrapper.vm.activeTab).toBe('layout');
  });
});
