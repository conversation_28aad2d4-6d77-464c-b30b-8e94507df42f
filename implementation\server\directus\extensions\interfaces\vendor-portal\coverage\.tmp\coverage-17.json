{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/VisualEditorsPerformance.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36496, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 374, "endOffset": 545, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 442, "endOffset": 497, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 615, "endOffset": 782, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 682, "endOffset": 735, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 854, "endOffset": 1027, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 923, "endOffset": 979, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1092, "endOffset": 1250, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1154, "endOffset": 1202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1316, "endOffset": 1477, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 1379, "endOffset": 1428, "count": 1}], "isBlockCoverage": true}, {"functionName": "generateLargeDataset", "ranges": [{"startOffset": 1830, "endOffset": 2503, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1916, "endOffset": 2499, "count": 490}, {"startOffset": 2207, "endOffset": 2220, "count": 245}, {"startOffset": 2221, "endOffset": 2230, "count": 245}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2543, "endOffset": 7682, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2597, "endOffset": 2854, "count": 4}, {"startOffset": 2723, "endOffset": 2794, "count": 0}], "isBlockCoverage": true}, {"functionName": "now", "ranges": [{"startOffset": 2759, "endOffset": 2785, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2916, "endOffset": 4401, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3010, "endOffset": 3630, "count": 4}, {"startOffset": 3063, "endOffset": 3162, "count": 1}, {"startOffset": 3162, "endOffset": 3570, "count": 3}, {"startOffset": 3198, "endOffset": 3296, "count": 1}, {"startOffset": 3296, "endOffset": 3570, "count": 2}, {"startOffset": 3333, "endOffset": 3570, "count": 1}, {"startOffset": 3570, "endOffset": 3629, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3929, "endOffset": 3962, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4463, "endOffset": 5971, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4557, "endOffset": 5181, "count": 4}, {"startOffset": 4610, "endOffset": 4710, "count": 1}, {"startOffset": 4710, "endOffset": 5121, "count": 3}, {"startOffset": 4746, "endOffset": 4845, "count": 1}, {"startOffset": 4845, "endOffset": 5121, "count": 2}, {"startOffset": 4882, "endOffset": 5121, "count": 1}, {"startOffset": 5121, "endOffset": 5180, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5480, "endOffset": 5513, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6017, "endOffset": 6838, "count": 1}, {"startOffset": 6566, "endOffset": 6676, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6878, "endOffset": 7678, "count": 1}, {"startOffset": 7420, "endOffset": 7528, "count": 10}, {"startOffset": 7463, "endOffset": 7473, "count": 5}, {"startOffset": 7474, "endOffset": 7485, "count": 5}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/VisualEditors.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 36385, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 36385, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 2891, "endOffset": 3612, "count": 4}], "isBlockCoverage": true}, {"functionName": "formatLastSaved", "ranges": [{"startOffset": 3633, "endOffset": 4361, "count": 0}], "isBlockCoverage": false}, {"functionName": "mounted", "ranges": [{"startOffset": 4370, "endOffset": 4413, "count": 4}], "isBlockCoverage": true}, {"functionName": "loadInitialData", "ranges": [{"startOffset": 4433, "endOffset": 5424, "count": 4}, {"startOffset": 4815, "endOffset": 4884, "count": 3}, {"startOffset": 4924, "endOffset": 4991, "count": 3}, {"startOffset": 5032, "endOffset": 5101, "count": 3}, {"startOffset": 5143, "endOffset": 5214, "count": 3}, {"startOffset": 5223, "endOffset": 5370, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadShowrooms", "ranges": [{"startOffset": 5431, "endOffset": 5804, "count": 5}, {"startOffset": 5696, "endOffset": 5798, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadProducts", "ranges": [{"startOffset": 5811, "endOffset": 6173, "count": 4}, {"startOffset": 6066, "endOffset": 6167, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadMaterials", "ranges": [{"startOffset": 6180, "endOffset": 6546, "count": 4}, {"startOffset": 6438, "endOffset": 6540, "count": 0}], "isBlockCoverage": true}, {"functionName": "loadAnimations", "ranges": [{"startOffset": 6553, "endOffset": 6923, "count": 4}, {"startOffset": 6814, "endOffset": 6917, "count": 0}], "isBlockCoverage": true}, {"functionName": "setActiveTab", "ranges": [{"startOffset": 6930, "endOffset": 7597, "count": 5}, {"startOffset": 7062, "endOffset": 7184, "count": 1}, {"startOffset": 7193, "endOffset": 7314, "count": 1}, {"startOffset": 7251, "endOffset": 7297, "count": 0}, {"startOffset": 7323, "endOffset": 7447, "count": 1}, {"startOffset": 7383, "endOffset": 7430, "count": 0}, {"startOffset": 7456, "endOffset": 7583, "count": 1}, {"startOffset": 7518, "endOffset": 7566, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleLayoutUpdate", "ranges": [{"startOffset": 7604, "endOffset": 8041, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleProductUpdate", "ranges": [{"startOffset": 8048, "endOffset": 8483, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleMaterialUpdate", "ranges": [{"startOffset": 8490, "endOffset": 8931, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleLightingUpdate", "ranges": [{"startOffset": 8938, "endOffset": 9058, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleAnimationUpdate", "ranges": [{"startOffset": 9065, "endOffset": 9512, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateLastSaved", "ranges": [{"startOffset": 9519, "endOffset": 9579, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleAutoSave", "ranges": [{"startOffset": 9586, "endOffset": 9662, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 9943, "endOffset": 10049, "count": 4}, {"startOffset": 10012, "endOffset": 10047, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10491, "endOffset": 10580, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10698, "endOffset": 10781, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10830, "endOffset": 10874, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/VisualEditors.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 62421, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 62421, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 9807, "count": 26}, {"startOffset": 278, "endOffset": 282, "count": 0}, {"startOffset": 968, "endOffset": 1008, "count": 0}, {"startOffset": 1714, "endOffset": 1825, "count": 0}, {"startOffset": 2411, "endOffset": 2438, "count": 17}, {"startOffset": 2439, "endOffset": 3706, "count": 5}, {"startOffset": 3707, "endOffset": 3717, "count": 21}, {"startOffset": 3747, "endOffset": 3773, "count": 6}, {"startOffset": 3774, "endOffset": 5034, "count": 1}, {"startOffset": 5035, "endOffset": 5045, "count": 25}, {"startOffset": 5076, "endOffset": 5103, "count": 1}, {"startOffset": 5104, "endOffset": 6371, "count": 1}, {"startOffset": 6372, "endOffset": 6382, "count": 25}, {"startOffset": 6414, "endOffset": 6442, "count": 1}, {"startOffset": 6443, "endOffset": 7723, "count": 1}, {"startOffset": 7724, "endOffset": 7734, "count": 25}, {"startOffset": 7752, "endOffset": 7948, "count": 8}, {"startOffset": 7949, "endOffset": 9802, "count": 18}, {"startOffset": 7961, "endOffset": 8304, "count": 0}, {"startOffset": 8394, "endOffset": 8640, "count": 10}, {"startOffset": 8641, "endOffset": 8651, "count": 8}, {"startOffset": 8681, "endOffset": 8925, "count": 5}, {"startOffset": 8926, "endOffset": 8936, "count": 13}, {"startOffset": 8967, "endOffset": 9216, "count": 1}, {"startOffset": 9217, "endOffset": 9227, "count": 17}, {"startOffset": 9258, "endOffset": 9500, "count": 1}, {"startOffset": 9501, "endOffset": 9511, "count": 17}, {"startOffset": 9543, "endOffset": 9789, "count": 1}, {"startOffset": 9790, "endOffset": 9800, "count": 17}], "isBlockCoverage": true}, {"functionName": "_c.on.change", "ranges": [{"startOffset": 1065, "endOffset": 1589, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1915, "endOffset": 2321, "count": 130}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 2104, "endOffset": 2176, "count": 5}], "isBlockCoverage": true}, {"functionName": "change", "ranges": [{"startOffset": 2870, "endOffset": 3232, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3266, "endOffset": 3449, "count": 270}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 3532, "endOffset": 3598, "count": 0}], "isBlockCoverage": false}, {"functionName": "change", "ranges": [{"startOffset": 4200, "endOffset": 4561, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4594, "endOffset": 4773, "count": 50}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 4856, "endOffset": 4921, "count": 0}], "isBlockCoverage": false}, {"functionName": "change", "ranges": [{"startOffset": 5535, "endOffset": 5897, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5931, "endOffset": 6114, "count": 50}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 6197, "endOffset": 6263, "count": 0}], "isBlockCoverage": false}, {"functionName": "change", "ranges": [{"startOffset": 6879, "endOffset": 7242, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7277, "endOffset": 7464, "count": 50}], "isBlockCoverage": true}, {"functionName": "click", "ranges": [{"startOffset": 7547, "endOffset": 7614, "count": 0}], "isBlockCoverage": false}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 9832, "endOffset": 10048, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10174, "endOffset": 10196, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 10303, "endOffset": 10334, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/VisualEditors.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 558, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 558, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 1}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 1}, {"startOffset": 543, "endOffset": 553, "count": 0}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 1}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 1}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 4}, {"startOffset": 2202, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 0}], "isBlockCoverage": false}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 2}, {"startOffset": 2728, "endOffset": 2796, "count": 1}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}