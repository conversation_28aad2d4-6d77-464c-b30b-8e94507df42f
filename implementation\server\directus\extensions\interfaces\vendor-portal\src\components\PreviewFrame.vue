<template>
  <div 
    class="preview-frame" 
    :class="[
      `device-${previewDevice}`, 
      `orientation-${previewOrientation}`,
      { 'is-loading': previewLoading }
    ]"
    :style="frameStyle"
  >
    <div class="preview-frame-header">
      <div class="preview-frame-controls">
        <button 
          v-for="device in availableDevices" 
          :key="device.id"
          class="device-button"
          :class="{ active: previewDevice === device.id }"
          @click="setPreviewDevice(device.id)"
          :title="device.name"
        >
          <i class="material-icons">{{ device.icon }}</i>
        </button>
        
        <button 
          v-if="previewDevice !== 'desktop' && previewDevice !== 'tv'"
          class="orientation-button"
          @click="toggleOrientation"
          :title="previewOrientation === 'portrait' ? 'Switch to landscape' : 'Switch to portrait'"
        >
          <i class="material-icons">{{ previewOrientation === 'portrait' ? 'stay_current_landscape' : 'stay_current_portrait' }}</i>
        </button>
      </div>
      
      <div class="preview-frame-info">
        <span class="device-name">{{ currentDeviceName }}</span>
        <span class="dimensions">{{ deviceDimensions.width }} × {{ deviceDimensions.height }}</span>
      </div>
      
      <div class="preview-frame-actions">
        <button 
          class="scale-button"
          @click="decreaseScale"
          :disabled="previewScale <= 0.25"
          title="Zoom out"
        >
          <i class="material-icons">remove</i>
        </button>
        
        <span class="scale-value">{{ Math.round(previewScale * 100) }}%</span>
        
        <button 
          class="scale-button"
          @click="increaseScale"
          :disabled="previewScale >= 2"
          title="Zoom in"
        >
          <i class="material-icons">add</i>
        </button>
        
        <button 
          class="refresh-button"
          @click="refreshPreview"
          :disabled="previewLoading"
          title="Refresh preview"
        >
          <i class="material-icons">refresh</i>
        </button>
      </div>
    </div>
    
    <div class="preview-frame-device-container">
      <div class="preview-frame-device" :style="deviceStyle">
        <div v-if="previewLoading" class="preview-loading">
          <div class="spinner"></div>
          <span>Loading preview...</span>
        </div>
        
        <div v-else-if="previewError" class="preview-error">
          <i class="material-icons">error_outline</i>
          <span>{{ previewError }}</span>
          <button @click="refreshPreview" class="retry-button">Retry</button>
        </div>
        
        <iframe 
          v-else
          ref="previewIframe"
          class="preview-iframe"
          :src="previewUrl"
          frameborder="0"
          @load="handleIframeLoad"
        ></iframe>
      </div>
    </div>
    
    <div class="preview-frame-footer">
      <div class="network-selector">
        <label for="network-condition">Network:</label>
        <select 
          id="network-condition"
          v-model="previewNetworkCondition"
          @change="setNetworkCondition(previewNetworkCondition)"
        >
          <option value="online">Online (No throttling)</option>
          <option value="4g">Fast 4G</option>
          <option value="3g">Slow 3G</option>
          <option value="slow-2g">Offline (Slow 2G)</option>
        </select>
      </div>
      
      <div class="last-updated">
        Last updated: {{ formattedLastUpdated }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { usePreviewContext } from '../contexts/PreviewContext';

export default {
  name: 'PreviewFrame',
  
  props: {
    src: {
      type: String,
      default: ''
    }
  },
  
  setup(props) {
    const previewIframe = ref(null);
    const iframeLoaded = ref(false);
    
    // Get preview context
    const {
      previewMode,
      previewDevice,
      previewOrientation,
      previewScale,
      previewLoading,
      previewError,
      previewUrl,
      previewNetworkCondition,
      lastUpdated,
      deviceDimensions,
      setPreviewDevice,
      setPreviewOrientation,
      setPreviewScale,
      setPreviewUrl,
      setNetworkCondition,
      refreshPreview
    } = usePreviewContext();
    
    // Set initial URL from props
    if (props.src && !previewUrl.value) {
      setPreviewUrl(props.src);
    }
    
    // Available devices
    const availableDevices = [
      { id: 'mobile', name: 'Mobile', icon: 'smartphone' },
      { id: 'tablet', name: 'Tablet', icon: 'tablet' },
      { id: 'desktop', name: 'Desktop', icon: 'desktop_windows' },
      { id: 'tv', name: 'TV', icon: 'tv' }
    ];
    
    // Get current device name
    const currentDeviceName = computed(() => {
      const device = availableDevices.find(d => d.id === previewDevice.value);
      return device ? device.name : 'Unknown';
    });
    
    // Format last updated time
    const formattedLastUpdated = computed(() => {
      return lastUpdated.value.toLocaleTimeString();
    });
    
    // Frame style
    const frameStyle = computed(() => {
      return {
        '--preview-scale': previewScale.value
      };
    });
    
    // Device style
    const deviceStyle = computed(() => {
      return {
        width: `${deviceDimensions.value.width}px`,
        height: `${deviceDimensions.value.height}px`
      };
    });
    
    // Toggle orientation
    const toggleOrientation = () => {
      setPreviewOrientation(previewOrientation.value === 'portrait' ? 'landscape' : 'portrait');
    };
    
    // Scale controls
    const increaseScale = () => {
      setPreviewScale(Math.min(previewScale.value + 0.25, 2));
    };
    
    const decreaseScale = () => {
      setPreviewScale(Math.max(previewScale.value - 0.25, 0.25));
    };
    
    // Handle iframe load
    const handleIframeLoad = () => {
      iframeLoaded.value = true;
    };
    
    // Lifecycle hooks
    onMounted(() => {
      // Initial refresh
      refreshPreview();
    });
    
    onBeforeUnmount(() => {
      // Cleanup
    });
    
    return {
      previewIframe,
      iframeLoaded,
      previewMode,
      previewDevice,
      previewOrientation,
      previewScale,
      previewLoading,
      previewError,
      previewUrl,
      previewNetworkCondition,
      deviceDimensions,
      availableDevices,
      currentDeviceName,
      formattedLastUpdated,
      frameStyle,
      deviceStyle,
      setPreviewDevice,
      setPreviewOrientation,
      setNetworkCondition,
      toggleOrientation,
      increaseScale,
      decreaseScale,
      refreshPreview,
      handleIframeLoad
    };
  }
};
</script>

<style scoped>
.preview-frame {
  display: flex;
  flex-direction: column;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  overflow: hidden;
  height: 100%;
}

.preview-frame-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
}

.preview-frame-controls {
  display: flex;
  gap: 4px;
}

.preview-frame-info {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.preview-frame-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.preview-frame-device-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow: auto;
  background-color: var(--theme--background-subdued);
}

.preview-frame-device {
  position: relative;
  transform: scale(var(--preview-scale));
  transform-origin: center;
  transition: width 0.3s, height 0.3s;
  background-color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

.preview-loading,
.preview-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
}

.preview-frame-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-top: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

/* Buttons */
.device-button,
.orientation-button,
.scale-button,
.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.device-button:hover,
.orientation-button:hover,
.scale-button:hover,
.refresh-button:hover {
  background-color: var(--theme--primary-background);
  color: var(--theme--primary);
}

.device-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border-color: var(--theme--primary);
}

/* Spinner */
.spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--theme--primary);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
</style>
