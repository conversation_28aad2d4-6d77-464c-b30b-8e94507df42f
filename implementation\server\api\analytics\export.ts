/**
 * Analytics Export API
 *
 * This file contains API endpoints for exporting analytics data in different formats.
 */

import { Request, Response } from 'express';
import { supabase } from '../../lib/supabase';
import { logger } from '../../lib/logger';
import { authenticate } from '../../middleware/auth';
import { rateLimiter } from '../../middleware/rate-limiter';
// Unused import, commented out

import { Router } from 'express';
import * as ExcelJS from 'exceljs';
import * as Papa from 'papaparse';
import PDFDocument from 'pdfkit';

// Create router
const router: Router = Router();

/**
 * Export analytics data in CSV format
 *
 * @param req - Request
 * @param res - Response
 */
export const exportAnalyticsCSV = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, report_type, start_date, end_date, showroom_id } = req.query;

    // Validate required parameters
    if (!vendor_id || !report_type) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID and report type are required',
        },
      });
      return;
    }

    // Get data based on report type
    const data = await getReportData(vendor_id as string, report_type as string, {
      start_date: start_date as string,
      end_date: end_date as string,
      showroom_id: showroom_id as string,
    });

    // Convert data to CSV
    const csv = Papa.unparse(data);

    // Set response headers
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${report_type}-${vendor_id}-${new Date().toISOString().split('T')[0]}.csv`,
    );

    // Send CSV data
    res.status(200).send(csv);
  } catch (error) {
    logger.error('Error in exportAnalyticsCSV', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Export analytics data in Excel format
 *
 * @param req - Request
 * @param res - Response
 */
export const exportAnalyticsExcel = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, report_type, start_date, end_date, showroom_id } = req.query;

    // Validate required parameters
    if (!vendor_id || !report_type) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID and report type are required',
        },
      });
      return;
    }

    // Get data based on report type
    const data = await getReportData(vendor_id as string, report_type as string, {
      start_date: start_date as string,
      end_date: end_date as string,
      showroom_id: showroom_id as string,
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(report_type as string);

    // Add headers
    const headers = Object.keys(data[0] || {});
    worksheet.addRow(headers);

    // Add data rows
    data.forEach((item: Record<string, string | number | boolean | null>) => {
      worksheet.addRow(Object.values(item));
    });

    // Style headers
    worksheet.getRow(1).font = { bold: true };
    worksheet.columns.forEach(column => {
      if (column) {
        column.width = 15;
      }
    });

    // Set response headers
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${report_type}-${vendor_id}-${new Date().toISOString().split('T')[0]}.xlsx`,
    );

    // Write to response
    await workbook.xlsx.write(res);
    res.end();
  } catch (error) {
    logger.error('Error in exportAnalyticsExcel', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Export analytics data in PDF format
 *
 * @param req - Request
 * @param res - Response
 */
export const exportAnalyticsPDF = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, report_type, start_date, end_date, showroom_id } = req.query;

    // Validate required parameters
    if (!vendor_id || !report_type) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID and report type are required',
        },
      });
      return;
    }

    // Get data based on report type
    const data = await getReportData(vendor_id as string, report_type as string, {
      start_date: start_date as string,
      end_date: end_date as string,
      showroom_id: showroom_id as string,
    });

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename=${report_type}-${vendor_id}-${new Date().toISOString().split('T')[0]}.pdf`,
    );

    // Create PDF document
    const doc = new PDFDocument();
    doc.pipe(res);

    // Add title
    const reportTypeStr =
      typeof report_type === 'string'
        ? report_type
        : Array.isArray(report_type)
          ? report_type[0]
          : String(report_type);
    doc
      .fontSize(16)
      .text(`${reportTypeStr.charAt(0).toUpperCase() + reportTypeStr.slice(1)} Report`, {
        align: 'center',
      });
    doc.moveDown();

    // Add date range
    const startDateStr = start_date ? String(start_date) : 'All time';
    const endDateStr = end_date ? String(end_date) : 'Present';
    doc.fontSize(12).text(`Date Range: ${startDateStr} to ${endDateStr}`, {
      align: 'center',
    });
    doc.moveDown();

    // Add table headers
    const headers = Object.keys(data[0] || {});
    const tableTop = 150;
    let tableRow = tableTop;
    const rowHeight = 20;
    const colWidth = 100;

    // Draw headers
    headers.forEach((header, i) => {
      doc.font('Helvetica-Bold').text(header, 50 + i * colWidth, tableRow, {
        width: colWidth,
        align: 'left',
      });
    });

    // Draw data rows
    data.forEach((row: Record<string, string | number | boolean | null>, rowIndex: number) => {
      tableRow = tableTop + (rowIndex + 1) * rowHeight;

      // Check if we need a new page
      if (tableRow > 700) {
        doc.addPage();
        tableRow = 50;
      }

      // Draw row data
      Object.values(row).forEach((value, colIndex) => {
        doc.font('Helvetica').text(String(value), 50 + colIndex * colWidth, tableRow, {
          width: colWidth,
          align: 'left',
        });
      });
    });

    // Finalize PDF
    doc.end();
  } catch (error) {
    logger.error('Error in exportAnalyticsPDF', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get report data based on report type
 *
 * @param vendorId - Vendor ID
 * @param reportType - Report type
 * @param options - Additional options
 * @returns Report data
 */
// Define types for options and query
interface ReportOptions {
  start_date?: string;
  end_date?: string;
  showroom_id?: string;
}

// Define a type for the query
type SupabaseQuery = ReturnType<typeof supabase.from>;

async function getReportData(vendorId: string, reportType: string, options: ReportOptions) {
  const { start_date, end_date, showroom_id } = options;

  // Build base query
  let query: SupabaseQuery;

  switch (reportType) {
    case 'visitors':
      query = supabase
        .from('visitor_sessions')
        .select('id, user_id, showroom_id, started_at, ended_at, duration, device_type, location')
        .eq('vendor_id', vendorId);
      break;
    case 'products':
      query = supabase
        .from('product_interactions')
        .select('id, product_id, user_id, event_type, duration, created_at')
        .eq('vendor_id', vendorId);
      break;
    case 'showrooms':
      query = supabase
        .from('showroom_analytics')
        .select('id, showroom_id, visits, unique_visitors, avg_duration, bounce_rate, created_at')
        .eq('vendor_id', vendorId);
      break;
    case 'conversions':
      query = supabase
        .from('conversion_events')
        .select('id, user_id, product_id, event_type, created_at')
        .eq('vendor_id', vendorId);
      break;
    default:
      throw new Error(`Unknown report type: ${reportType}`);
  }

  // Add filters
  if (showroom_id) {
    query = query.eq('showroom_id', showroom_id);
  }

  if (start_date) {
    query = query.gte('created_at', start_date);
  }

  if (end_date) {
    query = query.lte('created_at', end_date);
  }

  // Execute query
  const { data, error } = await query;

  if (error) {
    logger.error('Error getting report data', { error });
    throw error;
  }

  return data || [];
}

// Register routes
router.get(
  '/export/csv',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 10 }),
  exportAnalyticsCSV,
);
router.get(
  '/export/excel',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 10 }),
  exportAnalyticsExcel,
);
router.get(
  '/export/pdf',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 10 }),
  exportAnalyticsPDF,
);

export default router;
