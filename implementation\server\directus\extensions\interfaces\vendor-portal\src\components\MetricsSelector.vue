<template>
  <div class="metrics-selector">
    <div class="metrics-header">
      <div class="search-container">
        <div class="search-input">
          <i class="material-icons">search</i>
          <input 
            type="text" 
            v-model="searchQuery" 
            placeholder="Search metrics..." 
            @input="filterMetrics"
          >
        </div>
      </div>
      <div class="category-filter">
        <span class="filter-label">Filter by category:</span>
        <div class="category-buttons">
          <button 
            class="category-button"
            :class="{ active: selectedCategory === 'all' }"
            @click="selectCategory('all')"
          >
            All
          </button>
          <button 
            v-for="category in uniqueCategories" 
            :key="category"
            class="category-button"
            :class="{ active: selectedCategory === category }"
            @click="selectCategory(category)"
          >
            {{ category }}
          </button>
        </div>
      </div>
    </div>

    <div class="metrics-content">
      <div class="available-metrics">
        <h5>Available Metrics</h5>
        <div class="metrics-list">
          <div 
            v-for="metric in filteredMetrics" 
            :key="metric.id"
            class="metric-item"
            :class="{ 'selected': isMetricSelected(metric.id) }"
            @click="toggleMetric(metric)"
          >
            <div class="metric-checkbox">
              <i v-if="isMetricSelected(metric.id)" class="material-icons">check_box</i>
              <i v-else class="material-icons">check_box_outline_blank</i>
            </div>
            <div class="metric-info">
              <div class="metric-name">{{ metric.name }}</div>
              <div class="metric-category">{{ metric.category }}</div>
            </div>
            <div class="metric-action">
              <i class="material-icons">add</i>
            </div>
          </div>
        </div>
        <div v-if="filteredMetrics.length === 0" class="no-metrics">
          <i class="material-icons">search_off</i>
          <p>No metrics found matching "{{ searchQuery }}"</p>
        </div>
      </div>

      <div class="selected-metrics">
        <h5>Selected Metrics</h5>
        <div v-if="selectedMetrics.length === 0" class="no-selected-metrics">
          <i class="material-icons">info</i>
          <p>No metrics selected. Select metrics from the list on the left.</p>
        </div>
        <draggable 
          v-else
          v-model="selectedMetrics" 
          class="selected-metrics-list"
          handle=".drag-handle"
          @change="updateSelectedMetrics"
        >
          <div 
            v-for="(metric, index) in selectedMetrics" 
            :key="metric.id"
            class="selected-metric-item"
          >
            <div class="drag-handle">
              <i class="material-icons">drag_indicator</i>
            </div>
            <div class="metric-info">
              <div class="metric-name">{{ metric.name }}</div>
              <div class="metric-category">{{ metric.category }}</div>
            </div>
            <div class="metric-config">
              <button class="config-button" @click="configureMetric(metric)">
                <i class="material-icons">settings</i>
              </button>
            </div>
            <div class="metric-remove">
              <button class="remove-button" @click="removeMetric(index)">
                <i class="material-icons">close</i>
              </button>
            </div>
          </div>
        </draggable>
      </div>
    </div>

    <div v-if="configuringMetric" class="metric-configuration-overlay">
      <div class="metric-configuration-panel">
        <div class="configuration-header">
          <h4>Configure {{ configuringMetric.name }}</h4>
          <button class="close-button" @click="closeMetricConfiguration">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="configuration-content">
          <div class="configuration-section">
            <h5>Display Options</h5>
            <div class="configuration-field">
              <label>Display Name</label>
              <input 
                type="text" 
                v-model="metricConfig.displayName" 
                placeholder="Custom display name"
              >
            </div>
            <div class="configuration-field">
              <label>Format</label>
              <select v-model="metricConfig.format">
                <option value="number">Number</option>
                <option value="currency">Currency</option>
                <option value="percent">Percentage</option>
                <option value="duration">Duration</option>
              </select>
            </div>
            <div class="configuration-field">
              <label>Decimal Places</label>
              <input 
                type="number" 
                v-model.number="metricConfig.decimals" 
                min="0" 
                max="5"
              >
            </div>
          </div>
          <div class="configuration-section">
            <h5>Aggregation</h5>
            <div class="configuration-field">
              <label>Aggregation Method</label>
              <select v-model="metricConfig.aggregation">
                <option value="sum">Sum</option>
                <option value="average">Average</option>
                <option value="count">Count</option>
                <option value="min">Minimum</option>
                <option value="max">Maximum</option>
              </select>
            </div>
          </div>
          <div class="configuration-section">
            <h5>Conditional Formatting</h5>
            <div class="configuration-field">
              <label>Enable Conditional Formatting</label>
              <input 
                type="checkbox" 
                v-model="metricConfig.conditionalFormatting.enabled"
              >
            </div>
            <div v-if="metricConfig.conditionalFormatting.enabled" class="conditional-formatting-rules">
              <div class="rule-item">
                <div class="rule-condition">
                  <select v-model="metricConfig.conditionalFormatting.condition">
                    <option value="greater">Greater than</option>
                    <option value="less">Less than</option>
                    <option value="equal">Equal to</option>
                    <option value="between">Between</option>
                  </select>
                  <input 
                    type="number" 
                    v-model.number="metricConfig.conditionalFormatting.value1" 
                    placeholder="Value"
                  >
                  <input 
                    v-if="metricConfig.conditionalFormatting.condition === 'between'" 
                    type="number" 
                    v-model.number="metricConfig.conditionalFormatting.value2" 
                    placeholder="Second value"
                  >
                </div>
                <div class="rule-format">
                  <label>Color</label>
                  <select v-model="metricConfig.conditionalFormatting.color">
                    <option value="green">Green</option>
                    <option value="red">Red</option>
                    <option value="yellow">Yellow</option>
                    <option value="blue">Blue</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="configuration-actions">
          <button class="btn btn-secondary" @click="closeMetricConfiguration">Cancel</button>
          <button class="btn btn-primary" @click="saveMetricConfiguration">Save Configuration</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MetricsSelector',

  props: {
    metrics: {
      type: Array,
      required: true
    },
    value: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      searchQuery: '',
      selectedCategory: 'all',
      filteredMetrics: [...this.metrics],
      selectedMetrics: this.value.map(metricId => {
        const metric = this.metrics.find(m => m.id === metricId);
        return metric ? { ...metric, config: {} } : null;
      }).filter(Boolean),
      configuringMetric: null,
      metricConfig: {
        displayName: '',
        format: 'number',
        decimals: 2,
        aggregation: 'sum',
        conditionalFormatting: {
          enabled: false,
          condition: 'greater',
          value1: 0,
          value2: 0,
          color: 'green'
        }
      }
    };
  },

  computed: {
    uniqueCategories() {
      const categories = this.metrics.map(metric => metric.category);
      return [...new Set(categories)];
    }
  },

  watch: {
    metrics: {
      handler(newMetrics) {
        this.filteredMetrics = [...newMetrics];
        this.filterMetrics();
      },
      deep: true
    },
    value: {
      handler(newValue) {
        this.selectedMetrics = newValue.map(metricId => {
          const metric = this.metrics.find(m => m.id === metricId);
          return metric ? { ...metric, config: {} } : null;
        }).filter(Boolean);
      },
      deep: true
    }
  },

  mounted() {
    this.filterMetrics();
  },

  methods: {
    filterMetrics() {
      let filtered = [...this.metrics];
      
      // Apply category filter
      if (this.selectedCategory !== 'all') {
        filtered = filtered.filter(metric => metric.category === this.selectedCategory);
      }
      
      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(metric => 
          metric.name.toLowerCase().includes(query) || 
          metric.category.toLowerCase().includes(query)
        );
      }
      
      this.filteredMetrics = filtered;
    },
    
    selectCategory(category) {
      this.selectedCategory = category;
      this.filterMetrics();
    },
    
    isMetricSelected(metricId) {
      return this.selectedMetrics.some(m => m.id === metricId);
    },
    
    toggleMetric(metric) {
      if (this.isMetricSelected(metric.id)) {
        this.selectedMetrics = this.selectedMetrics.filter(m => m.id !== metric.id);
      } else {
        this.selectedMetrics.push({ ...metric, config: {} });
      }
      
      this.updateSelectedMetrics();
    },
    
    removeMetric(index) {
      this.selectedMetrics.splice(index, 1);
      this.updateSelectedMetrics();
    },
    
    updateSelectedMetrics() {
      this.$emit('input', this.selectedMetrics.map(m => m.id));
      this.$emit('change', this.selectedMetrics);
    },
    
    configureMetric(metric) {
      this.configuringMetric = metric;
      
      // Initialize configuration with existing values or defaults
      const existingConfig = metric.config || {};
      this.metricConfig = {
        displayName: existingConfig.displayName || metric.name,
        format: existingConfig.format || 'number',
        decimals: existingConfig.decimals !== undefined ? existingConfig.decimals : 2,
        aggregation: existingConfig.aggregation || 'sum',
        conditionalFormatting: existingConfig.conditionalFormatting || {
          enabled: false,
          condition: 'greater',
          value1: 0,
          value2: 0,
          color: 'green'
        }
      };
    },
    
    closeMetricConfiguration() {
      this.configuringMetric = null;
    },
    
    saveMetricConfiguration() {
      // Find the metric in the selected metrics array
      const index = this.selectedMetrics.findIndex(m => m.id === this.configuringMetric.id);
      
      if (index !== -1) {
        // Update the metric configuration
        this.selectedMetrics[index].config = { ...this.metricConfig };
        
        // Emit the change event
        this.updateSelectedMetrics();
      }
      
      // Close the configuration panel
      this.closeMetricConfiguration();
    }
  }
};
</script>

<style scoped>
.metrics-selector {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.metrics-header {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 8px 12px;
}

.search-input i {
  color: var(--theme--foreground-subdued);
  margin-right: 8px;
}

.search-input input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--theme--foreground);
  font-size: 14px;
  outline: none;
}

.category-filter {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.category-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.category-button {
  background-color: var(--theme--background-subdued);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  padding: 5px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.category-button:hover {
  border-color: var(--theme--primary);
}

.category-button.active {
  background-color: var(--theme--primary);
  color: white;
  border-color: var(--theme--primary);
}

.metrics-content {
  display: flex;
  gap: 20px;
  height: 400px;
}

.available-metrics,
.selected-metrics {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.available-metrics h5,
.selected-metrics h5 {
  margin: 0;
  padding: 10px 15px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
}

.metrics-list,
.selected-metrics-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.metric-item,
.selected-metric-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: var(--theme--border-radius);
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.2s;
}

.metric-item:hover {
  background-color: var(--theme--background-subdued);
}

.metric-item.selected {
  background-color: var(--theme--primary-background);
}

.selected-metric-item {
  background-color: var(--theme--background-subdued);
  cursor: default;
}

.metric-checkbox,
.drag-handle {
  margin-right: 10px;
  color: var(--theme--primary);
}

.drag-handle {
  cursor: grab;
}

.metric-info {
  flex: 1;
}

.metric-name {
  font-weight: bold;
  margin-bottom: 2px;
}

.metric-category {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.metric-action,
.metric-config,
.metric-remove {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
}

.config-button,
.remove-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  transition: color 0.2s;
}

.config-button:hover {
  color: var(--theme--primary);
}

.remove-button:hover {
  color: var(--theme--danger);
}

.no-metrics,
.no-selected-metrics {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: var(--theme--foreground-subdued);
  text-align: center;
  height: 100%;
}

.no-metrics i,
.no-selected-metrics i {
  font-size: 48px;
  margin-bottom: 10px;
}

.metric-configuration-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.metric-configuration-panel {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  width: 500px;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.configuration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--theme--border-color);
}

.configuration-header h4 {
  margin: 0;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  transition: color 0.2s;
}

.close-button:hover {
  color: var(--theme--danger);
}

.configuration-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.configuration-section {
  margin-bottom: 20px;
}

.configuration-section h5 {
  margin-top: 0;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--theme--border-color-subdued);
}

.configuration-field {
  margin-bottom: 10px;
}

.configuration-field label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.configuration-field input[type="text"],
.configuration-field input[type="number"],
.configuration-field select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.conditional-formatting-rules {
  margin-top: 10px;
}

.rule-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 10px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.rule-condition {
  display: flex;
  gap: 10px;
}

.rule-condition select,
.rule-condition input {
  flex: 1;
}

.rule-format {
  display: flex;
  align-items: center;
  gap: 10px;
}

.configuration-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 15px;
  border-top: 1px solid var(--theme--border-color);
}
</style>
