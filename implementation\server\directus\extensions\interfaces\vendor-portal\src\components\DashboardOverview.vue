<template>
  <div class="dashboard-overview">
    <div class="header">
      <h2>Dashboard</h2>
      <div class="actions">
        <div class="date-range">
          <select v-model="selectedPeriod" @change="loadDashboardData">
            <option value="today">Today</option>
            <option value="week">Last 7 Days</option>
            <option value="month">Last 30 Days</option>
            <option value="quarter">Last 90 Days</option>
            <option value="year">Last 12 Months</option>
          </select>
        </div>
        <button class="btn btn-secondary" @click="toggleCustomization">
          <i class="material-icons">{{ isCustomizing ? 'done' : 'dashboard_customize' }}</i>
          {{ isCustomizing ? 'Save Layout' : 'Customize' }}
        </button>
        <button class="btn btn-primary" @click="refreshDashboard">
          <i class="material-icons">refresh</i> Refresh
        </button>
      </div>
    </div>

    <!-- Dashboard Customization Panel -->
    <div v-if="isCustomizing" class="customization-panel">
      <h3>Dashboard Customization</h3>
      <p>Select which widgets to display on your dashboard:</p>
      <div class="widget-toggles">
        <div
          v-for="widget in availableWidgets"
          :key="widget.id"
          class="widget-toggle"
          :class="{ 'enabled': widget.enabled }"
          @click="toggleWidget(widget.id)"
        >
          <div class="widget-toggle-checkbox">
            <i class="material-icons">{{ widget.enabled ? 'check_box' : 'check_box_outline_blank' }}</i>
          </div>
          <div class="widget-toggle-name">{{ widget.name }}</div>
        </div>
      </div>
      <div class="customization-help">
        <i class="material-icons">info</i>
        <span>Drag and drop widgets to reorder them (coming soon)</span>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading dashboard data...</span>
    </div>

    <div v-else class="dashboard-content">
      <!-- Key Metrics Section -->
      <div v-if="isWidgetEnabled('key-metrics')" class="metrics-section">
        <div class="metric-card" v-for="(metric, key) in keyMetrics" :key="key">
          <div class="metric-icon" :class="metric.trend">
            <i class="material-icons">{{ metric.icon }}</i>
          </div>
          <div class="metric-content">
            <div class="metric-title">{{ metric.title }}</div>
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-trend" :class="metric.trend">
              <i class="material-icons">{{ getTrendIcon(metric.trend) }}</i>
              <span>{{ metric.change }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Overview Section -->
      <div v-if="isWidgetEnabled('activity')" class="dashboard-section">
        <div class="section-header">
          <h3>Activity Overview</h3>
          <div class="section-actions">
            <button class="btn btn-text" @click="viewAllActivity">
              View All <i class="material-icons">arrow_forward</i>
            </button>
          </div>
        </div>
        <div class="activity-timeline">
          <div v-if="recentActivity.length === 0" class="empty-state">
            No recent activity
          </div>
          <div v-else class="timeline">
            <div v-for="(activity, index) in recentActivity" :key="index" class="timeline-item">
              <div class="timeline-icon" :class="activity.type">
                <i class="material-icons">{{ getActivityIcon(activity.type) }}</i>
              </div>
              <div class="timeline-content">
                <div class="timeline-header">
                  <span class="timeline-title">{{ activity.title }}</span>
                  <span class="timeline-time">{{ formatTimeAgo(activity.timestamp) }}</span>
                </div>
                <div class="timeline-body">
                  {{ activity.description }}
                </div>
                <div v-if="activity.actionText" class="timeline-action">
                  <button class="btn btn-sm btn-text" @click="handleActivityAction(activity)">
                    {{ activity.actionText }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Stats Section -->
      <div class="dashboard-row">
        <div v-if="isWidgetEnabled('product-performance')" class="dashboard-section half">
          <div class="section-header">
            <h3>Product Performance</h3>
          </div>
          <div class="product-performance">
            <div class="chart-container">
              <canvas ref="productChart"></canvas>
            </div>
            <div class="top-products">
              <h4>Top Products</h4>
              <div class="product-list">
                <div v-for="(product, index) in topProducts" :key="index" class="product-item">
                  <div class="product-rank">{{ index + 1 }}</div>
                  <div class="product-info">
                    <div class="product-name">{{ product.name }}</div>
                    <div class="product-views">{{ product.views }} views</div>
                  </div>
                  <div class="product-trend" :class="product.trend">
                    <i class="material-icons">{{ getTrendIcon(product.trend) }}</i>
                    <span>{{ product.change }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="isWidgetEnabled('client-engagement')" class="dashboard-section half">
          <div class="section-header">
            <h3>Client Engagement</h3>
          </div>
          <div class="client-engagement">
            <div class="chart-container">
              <canvas ref="clientChart"></canvas>
            </div>
            <div class="active-clients">
              <h4>Recently Active Clients</h4>
              <div class="client-list">
                <div v-for="(client, index) in activeClients" :key="index" class="client-item">
                  <div class="client-avatar">
                    <img v-if="client.avatar" :src="client.avatar" :alt="client.name">
                    <div v-else class="placeholder-avatar">
                      {{ getInitials(client.name) }}
                    </div>
                  </div>
                  <div class="client-info">
                    <div class="client-name">{{ client.name }}</div>
                    <div class="client-company">{{ client.company }}</div>
                  </div>
                  <div class="client-activity">
                    <span>{{ formatTimeAgo(client.lastActivity) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Showroom Stats Section -->
      <div v-if="isWidgetEnabled('showroom-performance')" class="dashboard-section">
        <div class="section-header">
          <h3>Showroom Performance</h3>
          <div class="section-actions">
            <button class="btn btn-text" @click="viewAllShowrooms">
              Manage Showrooms <i class="material-icons">arrow_forward</i>
            </button>
          </div>
        </div>
        <div class="showroom-stats">
          <div class="showroom-grid">
            <div v-for="(showroom, index) in showrooms" :key="index" class="showroom-card">
              <div class="showroom-header">
                <h4>{{ showroom.name }}</h4>
                <div class="showroom-status" :class="showroom.status.toLowerCase()">
                  {{ showroom.status }}
                </div>
              </div>
              <div class="showroom-metrics">
                <div class="showroom-metric">
                  <div class="metric-label">Visits</div>
                  <div class="metric-value">{{ showroom.visits }}</div>
                  <div class="metric-trend" :class="showroom.visitsTrend">
                    <i class="material-icons">{{ getTrendIcon(showroom.visitsTrend) }}</i>
                    <span>{{ showroom.visitsChange }}</span>
                  </div>
                </div>
                <div class="showroom-metric">
                  <div class="metric-label">Avg. Time</div>
                  <div class="metric-value">{{ showroom.avgTime }}</div>
                  <div class="metric-trend" :class="showroom.timeTrend">
                    <i class="material-icons">{{ getTrendIcon(showroom.timeTrend) }}</i>
                    <span>{{ showroom.timeChange }}</span>
                  </div>
                </div>
                <div class="showroom-metric">
                  <div class="metric-label">Interactions</div>
                  <div class="metric-value">{{ showroom.interactions }}</div>
                  <div class="metric-trend" :class="showroom.interactionsTrend">
                    <i class="material-icons">{{ getTrendIcon(showroom.interactionsTrend) }}</i>
                    <span>{{ showroom.interactionsChange }}</span>
                  </div>
                </div>
              </div>
              <div class="showroom-actions">
                <button class="btn btn-sm btn-secondary" @click="viewShowroom(showroom)">
                  View Details
                </button>
                <button class="btn btn-sm btn-primary" @click="editShowroom(showroom)">
                  Edit
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Subscription Overview Section -->
      <div v-if="isWidgetEnabled('subscription')" class="dashboard-section">
        <div class="section-header">
          <h3>Subscription Overview</h3>
          <div class="section-actions">
            <button class="btn btn-text" @click="manageSubscription">
              Manage Subscription <i class="material-icons">arrow_forward</i>
            </button>
          </div>
        </div>
        <div class="subscription-overview">
          <div class="subscription-info">
            <div class="subscription-plan">
              <div class="plan-name">{{ subscription.plan }}</div>
              <div class="plan-status" :class="subscription.status.toLowerCase()">
                {{ subscription.status }}
              </div>
            </div>
            <div class="subscription-details">
              <div class="subscription-price">
                <span class="currency">$</span>
                <span class="amount">{{ subscription.price }}</span>
                <span class="period">/month</span>
              </div>
              <div class="subscription-renewal">
                Next billing: {{ formatDate(subscription.nextBillingDate) }}
              </div>
            </div>
          </div>
          <div class="usage-overview">
            <div v-for="(usage, key) in usageMetrics" :key="key" class="usage-item">
              <div class="usage-label">{{ usage.label }}</div>
              <div class="usage-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :style="{ width: calculatePercentage(usage.current, usage.limit) + '%' }"
                    :class="{ 'warning': calculatePercentage(usage.current, usage.limit) > 80 }"
                  ></div>
                </div>
                <div class="progress-text">
                  {{ usage.current }} / {{ usage.limit }} {{ usage.unit }} ({{ calculatePercentage(usage.current, usage.limit) }}%)
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Conversion Rates Widget (New) -->
      <div v-if="isWidgetEnabled('conversion-rates')" class="dashboard-section">
        <div class="section-header">
          <h3>Conversion Rates</h3>
        </div>
        <div class="conversion-rates">
          <div class="empty-state">
            <i class="material-icons">construction</i>
            <p>Conversion rates widget is under development</p>
          </div>
        </div>
      </div>

      <!-- Engagement Metrics Widget (New) -->
      <div v-if="isWidgetEnabled('engagement-metrics')" class="dashboard-section">
        <div class="section-header">
          <h3>Engagement Metrics</h3>
        </div>
        <div class="engagement-metrics">
          <div class="empty-state">
            <i class="material-icons">construction</i>
            <p>Engagement metrics widget is under development</p>
          </div>
        </div>
      </div>

      <!-- No Widgets Message -->
      <div v-if="!hasEnabledWidgets" class="empty-dashboard">
        <i class="material-icons">dashboard</i>
        <h3>No widgets enabled</h3>
        <p>Click the "Customize" button above to add widgets to your dashboard.</p>
      </div>
    </div>
  </div>
</template>

<script>
import { Chart, registerables } from 'chart.js';

// Register Chart.js components
Chart.register(...registerables);

export default {
  name: 'DashboardOverview',

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  computed: {
    hasEnabledWidgets() {
      return this.availableWidgets.some(widget => widget.enabled);
    }
  },

  data() {
    return {
      loading: true,
      selectedPeriod: 'month',
      keyMetrics: {},
      recentActivity: [],
      topProducts: [],
      activeClients: [],
      showrooms: [],
      subscription: {},
      usageMetrics: {},
      productChart: null,
      clientChart: null,
      lastFetchTime: null,
      cacheExpiry: 5 * 60 * 1000, // 5 minutes in milliseconds
      autoRefreshInterval: null,
      isCustomizing: false,
      dashboardLayout: null,
      availableWidgets: [
        { id: 'key-metrics', name: 'Key Metrics', enabled: true },
        { id: 'activity', name: 'Activity Overview', enabled: true },
        { id: 'product-performance', name: 'Product Performance', enabled: true },
        { id: 'client-engagement', name: 'Client Engagement', enabled: true },
        { id: 'showroom-performance', name: 'Showroom Performance', enabled: true },
        { id: 'subscription', name: 'Subscription Overview', enabled: true },
        { id: 'conversion-rates', name: 'Conversion Rates', enabled: false },
        { id: 'engagement-metrics', name: 'Engagement Metrics', enabled: false }
      ]
    };
  },

  mounted() {
    this.loadDashboardData();
    this.loadUserPreferences();
    // Set up auto-refresh interval (every 5 minutes)
    this.autoRefreshInterval = setInterval(() => {
      if (!this.isCustomizing) {
        this.refreshDashboard(true);
      }
    }, 5 * 60 * 1000);
  },

  beforeDestroy() {
    // Clear the auto-refresh interval when component is destroyed
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval);
    }

    // Destroy charts to prevent memory leaks
    if (this.productChart) {
      this.productChart.destroy();
    }
    if (this.clientChart) {
      this.clientChart.destroy();
    }
  },

  methods: {
    // Load dashboard data from API
    async loadDashboardData(forceRefresh = false) {
      // Check if we have cached data and it's still valid
      const now = Date.now();
      const isCacheValid = this.lastFetchTime && (now - this.lastFetchTime < this.cacheExpiry);

      if (!forceRefresh && isCacheValid) {
        console.log('Using cached dashboard data');
        return;
      }

      this.loading = true;

      try {
        // This would be replaced with actual API calls
        // const metricsResponse = await axios.get(`/api/dashboard/metrics?vendor_id=${this.vendorId}&period=${this.selectedPeriod}`);
        // const activityResponse = await axios.get(`/api/dashboard/activity?vendor_id=${this.vendorId}&limit=5`);
        // const productsResponse = await axios.get(`/api/dashboard/products?vendor_id=${this.vendorId}&period=${this.selectedPeriod}`);
        // const clientsResponse = await axios.get(`/api/dashboard/clients?vendor_id=${this.vendorId}&limit=5`);
        // const showroomsResponse = await axios.get(`/api/dashboard/showrooms?vendor_id=${this.vendorId}&period=${this.selectedPeriod}`);
        // const subscriptionResponse = await axios.get(`/api/dashboard/subscription?vendor_id=${this.vendorId}`);

        // Mock data for demonstration
        setTimeout(() => {
          // Mock key metrics
          this.keyMetrics = {
            visits: {
              title: 'Total Visits',
              value: '1,248',
              change: '+12.5%',
              trend: 'up',
              icon: 'visibility'
            },
            interactions: {
              title: 'Interactions',
              value: '3,842',
              change: '+8.3%',
              trend: 'up',
              icon: 'touch_app'
            },
            clients: {
              title: 'Active Clients',
              value: '24',
              change: '+2',
              trend: 'up',
              icon: 'people'
            },
            revenue: {
              title: 'Est. Revenue',
              value: '$4,250',
              change: '-3.2%',
              trend: 'down',
              icon: 'attach_money'
            }
          };

          // Mock recent activity
          this.recentActivity = [
            {
              type: 'client',
              title: 'New Client Registration',
              description: 'Sarah Johnson from Johnson Interiors registered as a new client.',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
              actionText: 'View Client'
            },
            {
              type: 'product',
              title: 'Product Viewed',
              description: 'Modern Sofa was viewed 12 times in the last hour.',
              timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString(),
              actionText: 'View Product'
            },
            {
              type: 'showroom',
              title: 'Showroom Updated',
              description: 'Main Showroom was updated with 3 new products.',
              timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
              actionText: 'View Showroom'
            },
            {
              type: 'order',
              title: 'New Order Placed',
              description: 'John Smith placed an order for 5 Modern Sofas.',
              timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
              actionText: 'View Order'
            },
            {
              type: 'system',
              title: 'System Update',
              description: 'The platform was updated to version 2.3.0 with new features.',
              timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
            }
          ];

          // Mock top products
          this.topProducts = [
            {
              name: 'Modern Sofa',
              views: 245,
              change: '+15%',
              trend: 'up'
            },
            {
              name: 'Dining Table',
              views: 187,
              change: '+8%',
              trend: 'up'
            },
            {
              name: 'Lounge Chair',
              views: 156,
              change: '-3%',
              trend: 'down'
            },
            {
              name: 'Coffee Table',
              views: 132,
              change: '+5%',
              trend: 'up'
            },
            {
              name: 'Bookshelf',
              views: 98,
              change: '+12%',
              trend: 'up'
            }
          ];

          // Mock active clients
          this.activeClients = [
            {
              name: 'John Smith',
              company: 'Acme Corporation',
              avatar: null,
              lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
            },
            {
              name: 'Sarah Johnson',
              company: 'Johnson Interiors',
              avatar: null,
              lastActivity: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
            },
            {
              name: 'Michael Brown',
              company: 'Brown & Associates',
              avatar: null,
              lastActivity: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
            },
            {
              name: 'Emily Davis',
              company: 'Davis Design',
              avatar: null,
              lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
            }
          ];

          // Mock showrooms
          this.showrooms = [
            {
              id: '1',
              name: 'Main Showroom',
              status: 'Published',
              visits: 856,
              visitsTrend: 'up',
              visitsChange: '+12%',
              avgTime: '5:32',
              timeTrend: 'up',
              timeChange: '+8%',
              interactions: 2450,
              interactionsTrend: 'up',
              interactionsChange: '+15%'
            },
            {
              id: '2',
              name: 'New Collection',
              status: 'Draft',
              visits: 0,
              visitsTrend: 'neutral',
              visitsChange: '0%',
              avgTime: '0:00',
              timeTrend: 'neutral',
              timeChange: '0%',
              interactions: 0,
              interactionsTrend: 'neutral',
              interactionsChange: '0%'
            },
            {
              id: '3',
              name: 'Office Furniture',
              status: 'Published',
              visits: 392,
              visitsTrend: 'down',
              visitsChange: '-5%',
              avgTime: '4:15',
              timeTrend: 'up',
              timeChange: '+3%',
              interactions: 1392,
              interactionsTrend: 'down',
              interactionsChange: '-2%'
            }
          ];

          // Mock subscription
          this.subscription = {
            plan: 'Professional',
            status: 'Active',
            price: 49.99,
            nextBillingDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString()
          };

          // Mock usage metrics
          this.usageMetrics = {
            storage: {
              label: 'Storage',
              current: 25.7,
              limit: 50,
              unit: 'GB'
            },
            api_calls: {
              label: 'API Calls',
              current: 8750,
              limit: 10000,
              unit: 'calls'
            },
            team_members: {
              label: 'Team Members',
              current: 6,
              limit: 10,
              unit: 'members'
            }
          };

          // Update last fetch time for cache management
          this.lastFetchTime = Date.now();
          this.loading = false;

          // Initialize charts after data is loaded
          this.$nextTick(() => {
            this.initCharts();
          });
        }, 1000);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        this.loading = false;
      }
    },

    // Initialize charts
    initCharts() {
      // Product performance chart
      if (this.$refs.productChart) {
        const productCtx = this.$refs.productChart.getContext('2d');

        if (this.productChart) {
          this.productChart.destroy();
        }

        this.productChart = new Chart(productCtx, {
          type: 'line',
          data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
              {
                label: 'Product Views',
                data: [450, 520, 500, 480, 520, 570],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                tension: 0.4,
                fill: true
              },
              {
                label: 'Interactions',
                data: [320, 380, 420, 450, 420, 480],
                borderColor: '#2ecc71',
                backgroundColor: 'rgba(46, 204, 113, 0.1)',
                tension: 0.4,
                fill: true
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  font: {
                    family: 'var(--theme--font-family-sans-serif)',
                    size: 12
                  },
                  color: 'var(--theme--foreground)'
                }
              }
            },
            scales: {
              x: {
                grid: {
                  display: false
                },
                ticks: {
                  color: 'var(--theme--foreground-subdued)'
                }
              },
              y: {
                grid: {
                  color: 'var(--theme--border-color)',
                  drawBorder: false
                },
                ticks: {
                  color: 'var(--theme--foreground-subdued)'
                }
              }
            }
          }
        });
      }

      // Client engagement chart
      if (this.$refs.clientChart) {
        const clientCtx = this.$refs.clientChart.getContext('2d');

        if (this.clientChart) {
          this.clientChart.destroy();
        }

        this.clientChart = new Chart(clientCtx, {
          type: 'bar',
          data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
              {
                label: 'Active Clients',
                data: [18, 20, 22, 21, 23, 24],
                backgroundColor: 'rgba(155, 89, 182, 0.7)'
              },
              {
                label: 'New Clients',
                data: [5, 3, 4, 2, 4, 3],
                backgroundColor: 'rgba(52, 152, 219, 0.7)'
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  font: {
                    family: 'var(--theme--font-family-sans-serif)',
                    size: 12
                  },
                  color: 'var(--theme--foreground)'
                }
              }
            },
            scales: {
              x: {
                grid: {
                  display: false
                },
                ticks: {
                  color: 'var(--theme--foreground-subdued)'
                }
              },
              y: {
                grid: {
                  color: 'var(--theme--border-color)',
                  drawBorder: false
                },
                ticks: {
                  color: 'var(--theme--foreground-subdued)'
                }
              }
            }
          }
        });
      }
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    // Format time ago for display
    formatTimeAgo(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now - date;
      const diffSec = Math.round(diffMs / 1000);
      const diffMin = Math.round(diffSec / 60);
      const diffHour = Math.round(diffMin / 60);
      const diffDay = Math.round(diffHour / 24);

      if (diffSec < 60) {
        return 'just now';
      } else if (diffMin < 60) {
        return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
      } else if (diffHour < 24) {
        return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
      } else if (diffDay < 30) {
        return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
      } else {
        return this.formatDate(dateString);
      }
    },

    // Get trend icon based on trend direction
    getTrendIcon(trend) {
      switch (trend) {
        case 'up':
          return 'trending_up';
        case 'down':
          return 'trending_down';
        default:
          return 'trending_flat';
      }
    },

    // Get activity icon based on activity type
    getActivityIcon(type) {
      switch (type) {
        case 'client':
          return 'person';
        case 'product':
          return 'inventory_2';
        case 'showroom':
          return 'view_in_ar';
        case 'order':
          return 'shopping_cart';
        case 'system':
          return 'settings';
        default:
          return 'event';
      }
    },

    // Get initials from name
    getInitials(name) {
      if (!name) return '';

      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    },

    // Calculate percentage for progress bars
    calculatePercentage(current, limit) {
      if (!limit) return 0;
      const percentage = Math.round((current / limit) * 100);
      return Math.min(percentage, 100);
    },

    // Refresh dashboard data
    refreshDashboard(silent = false) {
      if (!silent) {
        this.loading = true;
      }
      this.loadDashboardData(true);
    },

    // Load user dashboard preferences
    async loadUserPreferences() {
      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/dashboard/preferences?vendor_id=${this.vendorId}`);
        // const preferences = response.data;

        // Mock data for demonstration
        const preferences = {
          layout: [
            'key-metrics',
            'activity',
            'product-performance',
            'client-engagement',
            'showroom-performance',
            'subscription'
          ],
          widgets: {
            'key-metrics': { enabled: true, order: 0 },
            'activity': { enabled: true, order: 1 },
            'product-performance': { enabled: true, order: 2 },
            'client-engagement': { enabled: true, order: 3 },
            'showroom-performance': { enabled: true, order: 4 },
            'subscription': { enabled: true, order: 5 },
            'conversion-rates': { enabled: false, order: 6 },
            'engagement-metrics': { enabled: false, order: 7 }
          }
        };

        this.dashboardLayout = preferences.layout;

        // Update available widgets with user preferences
        this.availableWidgets.forEach(widget => {
          if (preferences.widgets[widget.id]) {
            widget.enabled = preferences.widgets[widget.id].enabled;
          }
        });
      } catch (error) {
        console.error('Error loading user preferences:', error);
      }
    },

    // Save user dashboard preferences
    async saveUserPreferences() {
      try {
        const preferences = {
          layout: this.dashboardLayout,
          widgets: {}
        };

        this.availableWidgets.forEach(widget => {
          preferences.widgets[widget.id] = {
            enabled: widget.enabled,
            order: this.dashboardLayout.indexOf(widget.id)
          };
        });

        // This would be replaced with actual API call
        // await axios.post(`/api/dashboard/preferences?vendor_id=${this.vendorId}`, preferences);

        console.log('Saved user preferences:', preferences);
        this.isCustomizing = false;
      } catch (error) {
        console.error('Error saving user preferences:', error);
      }
    },

    // Toggle dashboard customization mode
    toggleCustomization() {
      this.isCustomizing = !this.isCustomizing;
      if (!this.isCustomizing) {
        this.saveUserPreferences();
      }
    },

    // Toggle widget visibility
    toggleWidget(widgetId) {
      const widget = this.availableWidgets.find(w => w.id === widgetId);
      if (widget) {
        widget.enabled = !widget.enabled;

        if (widget.enabled && !this.dashboardLayout.includes(widgetId)) {
          this.dashboardLayout.push(widgetId);
        } else if (!widget.enabled && this.dashboardLayout.includes(widgetId)) {
          this.dashboardLayout = this.dashboardLayout.filter(id => id !== widgetId);
        }
      }
    },

    // Check if a widget is enabled
    isWidgetEnabled(widgetId) {
      const widget = this.availableWidgets.find(w => w.id === widgetId);
      return widget && widget.enabled;
    },

    // View all activity
    viewAllActivity() {
      // This would navigate to activity log or emit an event
      console.log('View all activity');
      this.$emit('view-activity');
    },

    // Handle activity action
    handleActivityAction(activity) {
      // This would navigate to the relevant page or emit an event
      console.log('Handle activity action:', activity.type, activity.actionText);

      switch (activity.type) {
        case 'client':
          this.$emit('view-client');
          break;
        case 'product':
          this.$emit('view-product');
          break;
        case 'showroom':
          this.$emit('view-showroom');
          break;
        case 'order':
          this.$emit('view-order');
          break;
        default:
          break;
      }
    },

    // View showroom details
    viewShowroom(showroom) {
      // This would navigate to showroom details or emit an event
      console.log('View showroom:', showroom.id);
      this.$emit('view-showroom', showroom.id);
    },

    // Edit showroom
    editShowroom(showroom) {
      // This would navigate to showroom editor or emit an event
      console.log('Edit showroom:', showroom.id);
      this.$emit('edit-showroom', showroom.id);
    },

    // View all showrooms
    viewAllShowrooms() {
      // This would navigate to showrooms page or emit an event
      console.log('View all showrooms');
      this.$emit('view-showrooms');
    },

    // Manage subscription
    manageSubscription() {
      // This would navigate to subscription page or emit an event
      console.log('Manage subscription');
      this.$emit('manage-subscription');
    }
  }
};
</script>

<style scoped>
.dashboard-overview {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.date-range select {
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* Key Metrics Section */
.metrics-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.metric-card {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--theme--border-color);
}

.metric-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--primary-background);
}

.metric-icon i {
  font-size: 24px;
  color: var(--theme--primary);
}

.metric-icon.up {
  background-color: rgba(46, 204, 113, 0.1);
}

.metric-icon.up i {
  color: #2ecc71;
}

.metric-icon.down {
  background-color: rgba(231, 76, 60, 0.1);
}

.metric-icon.down i {
  color: #e74c3c;
}

.metric-content {
  flex-grow: 1;
}

.metric-title {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 5px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.metric-trend.up {
  color: #2ecc71;
}

.metric-trend.down {
  color: #e74c3c;
}

.metric-trend.neutral {
  color: var(--theme--foreground-subdued);
}

/* Dashboard Sections */
.dashboard-section {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 20px;
  border: 1px solid var(--theme--border-color);
}

.dashboard-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: 20px;
}

.dashboard-section.half {
  min-height: 400px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.section-actions {
  display: flex;
  gap: 10px;
}

/* Activity Timeline */
.activity-timeline {
  max-height: 400px;
  overflow-y: auto;
}

.timeline {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.timeline-item {
  display: flex;
  gap: 15px;
}

.timeline-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--primary-background);
  flex-shrink: 0;
}

.timeline-icon i {
  font-size: 20px;
  color: var(--theme--primary);
}

.timeline-icon.client {
  background-color: rgba(52, 152, 219, 0.1);
}

.timeline-icon.client i {
  color: #3498db;
}

.timeline-icon.product {
  background-color: rgba(155, 89, 182, 0.1);
}

.timeline-icon.product i {
  color: #9b59b6;
}

.timeline-icon.showroom {
  background-color: rgba(46, 204, 113, 0.1);
}

.timeline-icon.showroom i {
  color: #2ecc71;
}

.timeline-icon.order {
  background-color: rgba(230, 126, 34, 0.1);
}

.timeline-icon.order i {
  color: #e67e22;
}

.timeline-icon.system {
  background-color: rgba(52, 73, 94, 0.1);
}

.timeline-icon.system i {
  color: #34495e;
}

.timeline-content {
  flex-grow: 1;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.timeline-title {
  font-weight: 600;
}

.timeline-time {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.timeline-body {
  margin-bottom: 10px;
  color: var(--theme--foreground);
}

.timeline-action {
  display: flex;
  justify-content: flex-end;
}

/* Product Performance */
.product-performance {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-container {
  height: 200px;
  position: relative;
}

.top-products h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: var(--theme--foreground-subdued);
}

.product-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.product-rank {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--theme--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
  margin-right: 10px;
}

.product-info {
  flex-grow: 1;
}

.product-name {
  font-weight: 600;
  margin-bottom: 3px;
}

.product-views {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.product-trend {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
}

.product-trend.up {
  color: #2ecc71;
}

.product-trend.down {
  color: #e74c3c;
}

/* Client Engagement */
.client-engagement {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.active-clients h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: var(--theme--foreground-subdued);
}

.client-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.client-item {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.client-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--primary);
  color: white;
  font-weight: 600;
}

.client-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.client-info {
  flex-grow: 1;
}

.client-name {
  font-weight: 600;
  margin-bottom: 3px;
}

.client-company {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.client-activity {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

/* Showroom Stats */
.showroom-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.showroom-card {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.showroom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.showroom-header h4 {
  margin: 0;
  font-size: 16px;
}

.showroom-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.showroom-status.published {
  background-color: var(--theme--primary);
  color: white;
}

.showroom-status.draft {
  background-color: var(--theme--warning);
  color: white;
}

.showroom-metrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-bottom: 15px;
}

.showroom-metric {
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 5px;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.showroom-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Subscription Overview */
.subscription-overview {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.subscription-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.subscription-plan {
  display: flex;
  align-items: center;
  gap: 10px;
}

.plan-name {
  font-size: 18px;
  font-weight: 600;
}

.plan-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.plan-status.active {
  background-color: var(--theme--primary);
  color: white;
}

.plan-status.cancelling {
  background-color: var(--theme--warning);
  color: white;
}

.plan-status.cancelled {
  background-color: var(--theme--danger);
  color: white;
}

.subscription-details {
  text-align: right;
}

.subscription-price {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 5px;
}

.subscription-price .currency {
  font-size: 14px;
  vertical-align: top;
}

.subscription-price .period {
  font-size: 14px;
  font-weight: normal;
  color: var(--theme--foreground-subdued);
}

.subscription-renewal {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.usage-overview {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.usage-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.usage-label {
  font-size: 14px;
  font-weight: 500;
}

.usage-progress {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.progress-bar {
  height: 8px;
  background-color: var(--theme--background);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--theme--primary);
  border-radius: 4px;
}

.progress-fill.warning {
  background-color: var(--theme--warning);
}

.progress-text {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  text-align: right;
}

.empty-state {
  text-align: center;
  padding: 20px;
  color: var(--theme--foreground-subdued);
  font-style: italic;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--border-color);
}

.btn-text {
  background-color: transparent;
  color: var(--theme--primary);
  padding: 4px 8px;
}

.btn-text i {
  margin-left: 4px;
  margin-right: 0;
}

.btn-text:hover {
  background-color: var(--theme--primary-background);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-sm i {
  font-size: 14px;
}

/* Dashboard Customization Styles */
.customization-panel {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  padding: 20px;
  margin-bottom: 20px;
}

.customization-panel h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: 600;
}

.customization-panel p {
  margin-bottom: 15px;
  color: var(--theme--foreground-subdued);
}

.widget-toggles {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  margin-bottom: 15px;
}

.widget-toggle {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  cursor: pointer;
  transition: all 0.2s ease;
}

.widget-toggle:hover {
  background-color: var(--theme--primary-background);
}

.widget-toggle.enabled {
  background-color: var(--theme--primary-background);
}

.widget-toggle-checkbox {
  margin-right: 10px;
  color: var(--theme--primary);
}

.widget-toggle-name {
  font-weight: 500;
}

.customization-help {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  color: var(--theme--foreground-subdued);
  font-size: 14px;
}

.customization-help i {
  margin-right: 8px;
  font-size: 18px;
}

.empty-dashboard {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.empty-dashboard i {
  font-size: 48px;
  color: var(--theme--primary);
  margin-bottom: 20px;
}

.empty-dashboard h3 {
  margin: 0 0 10px 0;
  font-size: 20px;
}

.empty-dashboard p {
  color: var(--theme--foreground-subdued);
  max-width: 400px;
}
</style>
