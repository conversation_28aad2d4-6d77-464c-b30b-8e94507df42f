import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { AssetBundleService } from '../../../services/asset/asset-bundle-service';
import { AssetBundleCreationRequest } from '../../../shared/models/asset-management';
import { validateRequest } from '../../middleware/validation';

// Initialize asset bundle service
const assetBundleService = new AssetBundleService(supabase);

/**
 * Get asset bundles by vendor
 *
 * @param req - Request
 * @param res - Response
 */
export const getAssetBundles = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id } = req.query;

    // Validate vendor ID
    if (!vendor_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_VENDOR_ID',
          message: 'Vendor ID is required',
        },
      });
      return;
    }

    // Get bundles
    const bundles = await assetBundleService.getAssetBundlesByVendor(vendor_id as string);

    res.status(200).json({
      success: true,
      data: bundles,
    });
  } catch (error) {
    logger.error('Error getting asset bundles', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Create a new asset bundle
 *
 * @param req - Request
 * @param res - Response
 */
export const createAssetBundle = async (req: Request, res: Response): Promise<void> => {
  try {
    const request: AssetBundleCreationRequest = req.body;

    // Create bundle
    const bundle = await assetBundleService.createAssetBundle(request);

    if (!bundle) {
      res.status(500).json({
        success: false,
        error: {
          code: 'BUNDLE_CREATION_FAILED',
          message: 'Failed to create asset bundle',
        },
      });
      return;
    }

    res.status(201).json({
      success: true,
      data: bundle,
    });
  } catch (error) {
    logger.error('Error creating asset bundle', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Optimize an asset bundle
 *
 * @param req - Request
 * @param res - Response
 */
export const optimizeAssetBundle = async (req: Request, res: Response): Promise<void> => {
  try {
    const { bundle_id } = req.params;

    // Validate bundle ID
    if (!bundle_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_BUNDLE_ID',
          message: 'Bundle ID is required',
        },
      });
      return;
    }

    // Optimize bundle
    const optimizedBundle = await assetBundleService.optimizeAssetBundle(bundle_id);

    if (!optimizedBundle) {
      res.status(404).json({
        success: false,
        error: {
          code: 'BUNDLE_NOT_FOUND',
          message: 'Asset bundle not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: optimizedBundle,
    });
  } catch (error) {
    logger.error('Error optimizing asset bundle', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
