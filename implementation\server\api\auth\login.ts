import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';

// Define the request schema
const LoginRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

// Define the response schema
const LoginResponseSchema = z.object({
  user: z.object({
    id: z.string().uuid(),
    email: z.string().email(),
    role: z.string(),
  }),
  session: z.object({
    access_token: z.string(),
    refresh_token: z.string(),
    expires_at: z.number(),
  }),
});

/**
 * Login API endpoint
 *
 * This endpoint authenticates a user and returns a session.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate request body
    const bodyResult = LoginRequestSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid request body', details: bodyResult.error.format() });
    }

    // Extract credentials
    const { email, password } = bodyResult.data;

    // Log the request
    logger.info('Login request', {
      email,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate user
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      logger.error('Login error', { error, email });
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Get user role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', data.user.id)
      .single();

    if (profileError) {
      logger.error('Error fetching user profile', { error: profileError, user_id: data.user.id });
      return res.status(500).json({ error: 'Error fetching user profile' });
    }

    // Prepare response
    const response = {
      user: {
        id: data.user.id,
        email: data.user.email,
        role: profile?.role || 'user',
      },
      session: {
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at,
      },
    };

    // Validate response
    const responseResult = LoginResponseSchema.safeParse(response);
    if (!responseResult.success) {
      logger.error('Invalid response format', { error: responseResult.error.format(), response });
      return res.status(500).json({ error: 'Invalid response format' });
    }

    // Log success
    logger.info('Login successful', {
      user_id: data.user.id,
      email: data.user.email,
      role: profile?.role || 'user',
    });

    // Return response
    return res.status(200).json(response);
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
