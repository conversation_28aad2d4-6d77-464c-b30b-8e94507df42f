import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { AssetPreloadService } from '../../services/asset/asset-preload-service';
import { AssetPreloadRequest } from '../../shared/models/asset-management';
import { validateRequest } from '../middleware/validation';

// Initialize asset preload service
const assetPreloadService = new AssetPreloadService(supabase);

/**
 * Preload assets for a client
 *
 * @param req - Request
 * @param res - Response
 */
export const preloadAssets = async (req: Request, res: Response): Promise<void> => {
  try {
    const request: AssetPreloadRequest = req.body;

    // Validate request
    if (!request.asset_ids || !Array.isArray(request.asset_ids) || request.asset_ids.length === 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ASSET_IDS',
          message: 'Asset IDs must be a non-empty array',
        },
      });
      return;
    }

    if (!request.client_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CLIENT_ID',
          message: 'Client ID is required',
        },
      });
      return;
    }

    // Preload assets
    const jobId = await assetPreloadService.preloadAssets(request);

    if (!jobId) {
      res.status(500).json({
        success: false,
        error: {
          code: 'PRELOAD_FAILED',
          message: 'Failed to preload assets',
        },
      });
      return;
    }

    res.status(202).json({
      success: true,
      data: {
        job_id: jobId,
        message: 'Asset preloading started',
      },
    });
  } catch (error) {
    logger.error('Error preloading assets', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get preload job status
 *
 * @param req - Request
 * @param res - Response
 */
export const getPreloadJobStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { job_id } = req.params;

    // Validate job ID
    if (!job_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_JOB_ID',
          message: 'Job ID is required',
        },
      });
      return;
    }

    // Get job status
    const status = assetPreloadService.getPreloadJobStatus(job_id);

    if (!status) {
      res.status(404).json({
        success: false,
        error: {
          code: 'JOB_NOT_FOUND',
          message: 'Preload job not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: status,
    });
  } catch (error) {
    logger.error('Error getting preload job status', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get client cache info
 *
 * @param req - Request
 * @param res - Response
 */
export const getClientCacheInfo = async (req: Request, res: Response): Promise<void> => {
  try {
    const { client_id } = req.params;

    // Validate client ID
    if (!client_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CLIENT_ID',
          message: 'Client ID is required',
        },
      });
      return;
    }

    // Get cache info
    const cacheInfo = await assetPreloadService.getClientCacheInfo(client_id);

    res.status(200).json({
      success: true,
      data: cacheInfo,
    });
  } catch (error) {
    logger.error('Error getting client cache info', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Invalidate client cache
 *
 * @param req - Request
 * @param res - Response
 */
export const invalidateClientCache = async (req: Request, res: Response): Promise<void> => {
  try {
    const { client_id } = req.params;
    const { asset_ids } = req.body;

    // Validate client ID
    if (!client_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CLIENT_ID',
          message: 'Client ID is required',
        },
      });
      return;
    }

    // Invalidate cache
    const success = await assetPreloadService.invalidateClientCache(client_id, asset_ids);

    if (!success) {
      res.status(500).json({
        success: false,
        error: {
          code: 'INVALIDATION_FAILED',
          message: 'Failed to invalidate client cache',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        message: 'Client cache invalidated successfully',
      },
    });
  } catch (error) {
    logger.error('Error invalidating client cache', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Check if an asset is cached for a client
 *
 * @param req - Request
 * @param res - Response
 */
export const isAssetCached = async (req: Request, res: Response): Promise<void> => {
  try {
    const { client_id, asset_id } = req.params;

    // Validate parameters
    if (!client_id || !asset_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Client ID and Asset ID are required',
        },
      });
      return;
    }

    // Check if asset is cached
    const isCached = await assetPreloadService.isAssetCached(client_id, asset_id);

    res.status(200).json({
      success: true,
      data: {
        is_cached: isCached,
      },
    });
  } catch (error) {
    logger.error('Error checking if asset is cached', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
