{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/tests/ShowroomLayoutEditorSimple.spec.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13694, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13694, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 862, "endOffset": 3000, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 921, "endOffset": 1898, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1056, "endOffset": 1681, "count": 8}, {"startOffset": 1101, "endOffset": 1621, "count": 4}, {"startOffset": 1621, "endOffset": 1680, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1954, "endOffset": 2123, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2183, "endOffset": 2297, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2367, "endOffset": 2611, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2665, "endOffset": 2996, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38148, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38148, "count": 1}], "isBlockCoverage": true}, {"functionName": "data", "ranges": [{"startOffset": 1939, "endOffset": 2622, "count": 4}], "isBlockCoverage": true}, {"functionName": "has<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 2643, "endOffset": 2768, "count": 4}, {"startOffset": 2691, "endOffset": 2761, "count": 0}], "isBlockCoverage": true}, {"functionName": "gridStyle", "ranges": [{"startOffset": 2779, "endOffset": 2898, "count": 4}], "isBlockCoverage": true}, {"functionName": "created", "ranges": [{"startOffset": 2907, "endOffset": 2943, "count": 4}], "isBlockCoverage": true}, {"functionName": "loadData", "ranges": [{"startOffset": 2963, "endOffset": 3874, "count": 4}, {"startOffset": 3183, "endOffset": 3188, "count": 0}, {"startOffset": 3402, "endOffset": 3407, "count": 0}, {"startOffset": 3491, "endOffset": 3783, "count": 0}, {"startOffset": 3792, "endOffset": 3868, "count": 0}], "isBlockCoverage": true}, {"functionName": "filterProducts", "ranges": [{"startOffset": 3881, "endOffset": 4269, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCategoryName", "ranges": [{"startOffset": 4276, "endOffset": 4439, "count": 16}, {"startOffset": 4399, "endOffset": 4414, "count": 8}, {"startOffset": 4415, "endOffset": 4432, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4350, "endOffset": 4374, "count": 12}], "isBlockCoverage": true}, {"functionName": "to<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4446, "endOffset": 4504, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleSnap", "ranges": [{"startOffset": 4511, "endOffset": 4573, "count": 0}], "isBlockCoverage": false}, {"functionName": "zoomIn", "ranges": [{"startOffset": 4580, "endOffset": 4667, "count": 0}], "isBlockCoverage": false}, {"functionName": "zoomOut", "ranges": [{"startOffset": 4674, "endOffset": 4764, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetZoom", "ranges": [{"startOffset": 4771, "endOffset": 4816, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleViewMode", "ranges": [{"startOffset": 4823, "endOffset": 4907, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDragStart", "ranges": [{"startOffset": 4914, "endOffset": 5068, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDragOver", "ranges": [{"startOffset": 5075, "endOffset": 5130, "count": 0}], "isBlockCoverage": false}, {"functionName": "onDrop", "ranges": [{"startOffset": 5137, "endOffset": 6006, "count": 0}], "isBlockCoverage": false}, {"functionName": "startDrag", "ranges": [{"startOffset": 6013, "endOffset": 7166, "count": 0}], "isBlockCoverage": false}, {"functionName": "getItemStyle", "ranges": [{"startOffset": 7173, "endOffset": 7428, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 7435, "endOffset": 7609, "count": 0}], "isBlockCoverage": false}, {"functionName": "rotateItem", "ranges": [{"startOffset": 7616, "endOffset": 7690, "count": 0}], "isBlockCoverage": false}, {"functionName": "saveLayout", "ranges": [{"startOffset": 7697, "endOffset": 8416, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetLayout", "ranges": [{"startOffset": 8423, "endOffset": 8556, "count": 0}], "isBlockCoverage": false}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 8837, "endOffset": 8943, "count": 4}, {"startOffset": 8906, "endOffset": 8941, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9391, "endOffset": 9480, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9605, "endOffset": 9688, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9737, "endOffset": 9781, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 59065, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 59065, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 8645, "count": 12}, {"startOffset": 278, "endOffset": 282, "count": 0}, {"startOffset": 6929, "endOffset": 6943, "count": 0}, {"startOffset": 7320, "endOffset": 7330, "count": 0}, {"startOffset": 8567, "endOffset": 8634, "count": 0}], "isBlockCoverage": true}, {"functionName": "input", "ranges": [{"startOffset": 1702, "endOffset": 1833, "count": 0}], "isBlockCoverage": false}, {"functionName": "change", "ranges": [{"startOffset": 2227, "endOffset": 2598, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2632, "endOffset": 2815, "count": 36}], "isBlockCoverage": true}, {"functionName": "change", "ranges": [{"startOffset": 3183, "endOffset": 3550, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 4304, "endOffset": 4423, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4607, "endOffset": 5482, "count": 16}, {"startOffset": 4968, "endOffset": 5073, "count": 0}], "isBlockCoverage": true}, {"functionName": "dragstart", "ranges": [{"startOffset": 4794, "endOffset": 4874, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7357, "endOffset": 8560, "count": 0}], "isBlockCoverage": false}, {"functionName": "_vm", "ranges": [{"startOffset": 8670, "endOffset": 9014, "count": 4}, {"startOffset": 8750, "endOffset": 8754, "count": 0}], "isBlockCoverage": true}, {"functionName": "staticRenderFns", "ranges": [{"startOffset": 9016, "endOffset": 9360, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9486, "endOffset": 9508, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 9615, "endOffset": 9646, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/VisualEditors/ShowroomLayoutEditor.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 566, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 566, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 1}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 1}, {"startOffset": 543, "endOffset": 553, "count": 0}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 1}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 1}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 4}, {"startOffset": 2202, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 0}], "isBlockCoverage": false}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 2}, {"startOffset": 2728, "endOffset": 2796, "count": 1}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}