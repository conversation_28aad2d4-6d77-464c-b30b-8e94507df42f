import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON>per,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  Step<PERSON>ontent,
  Typo<PERSON>,
  Button,
  Paper,
  CircularProgress,
  Alert,
  Chip,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material'
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  <PERSON>p<PERSON>ext as SkipNextIcon,
  ArrowForward as ArrowForwardIcon,
  Refresh as RefreshIcon,
  Help as HelpIcon,
} from '@mui/icons-material'
import { apiClient } from '../../services/apiClient'

// Define phase types
export enum PhaseType {
  PLANNING = 'planning',
  ASSET_CREATION = 'asset_creation',
  SCENE_CONSTRUCTION = 'scene_construction',
  INTERACTION_DEVELOPMENT = 'interaction_development',
  OPTIMIZATION = 'optimization',
  TESTING = 'testing',
  DEPLOYMENT = 'deployment',
}

// Define phase status
export enum PhaseStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped',
}

// Define phase data
interface PhaseData {
  type: PhaseType;
  status: PhaseStatus;
  started_at?: string;
  completed_at?: string;
  validation_results?: Record<string, {
    valid: boolean;
    timestamp: string;
    details?: any;
  }>;
  notes?: string;
}

// Define scene phase state
interface ScenePhaseState {
  scene_id: string;
  current_phase: PhaseType;
  phases: Record<PhaseType, PhaseData>;
  completed_phases: PhaseType[];
  validation_level: string;
  last_updated: string;
}

// Define phase info
interface PhaseInfo {
  type: PhaseType;
  name: string;
  description: string;
  order: number;
}

// Define phase progress indicator props
interface PhaseProgressIndicatorProps {
  sceneId: string;
  onPhaseChange?: (phase: PhaseType) => void;
  onValidationComplete?: (validationType: string, result: { valid: boolean; details?: any }) => void;
}

/**
 * Phase Progress Indicator Component
 * 
 * Displays the current phase and progress of scene development
 */
const PhaseProgressIndicator: React.FC<PhaseProgressIndicatorProps> = ({
  sceneId,
  onPhaseChange,
  onValidationComplete,
}) => {
  const [phaseState, setPhaseState] = useState<ScenePhaseState | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [transitioning, setTransitioning] = useState<boolean>(false)
  const [validating, setValidating] = useState<boolean>(false)
  const [helpDialogOpen, setHelpDialogOpen] = useState<boolean>(false)
  const [selectedPhase, setSelectedPhase] = useState<PhaseType | null>(null)

  // Phase info
  const phaseInfo: Record<PhaseType, PhaseInfo> = {
    [PhaseType.PLANNING]: {
      type: PhaseType.PLANNING,
      name: 'Planning',
      description: 'Define scene requirements, structure, and flow',
      order: 1,
    },
    [PhaseType.ASSET_CREATION]: {
      type: PhaseType.ASSET_CREATION,
      name: 'Asset Creation',
      description: 'Create and import assets for the scene',
      order: 2,
    },
    [PhaseType.SCENE_CONSTRUCTION]: {
      type: PhaseType.SCENE_CONSTRUCTION,
      name: 'Scene Construction',
      description: 'Build the scene using assets and blueprints',
      order: 3,
    },
    [PhaseType.INTERACTION_DEVELOPMENT]: {
      type: PhaseType.INTERACTION_DEVELOPMENT,
      name: 'Interaction Development',
      description: 'Implement interactions and behaviors',
      order: 4,
    },
    [PhaseType.OPTIMIZATION]: {
      type: PhaseType.OPTIMIZATION,
      name: 'Optimization',
      description: 'Optimize scene for performance and compatibility',
      order: 5,
    },
    [PhaseType.TESTING]: {
      type: PhaseType.TESTING,
      name: 'Testing',
      description: 'Test scene functionality and performance',
      order: 6,
    },
    [PhaseType.DEPLOYMENT]: {
      type: PhaseType.DEPLOYMENT,
      name: 'Deployment',
      description: 'Prepare scene for deployment',
      order: 7,
    },
  }

  // Load phase state
  useEffect(() => {
    if (sceneId) {
      loadPhaseState()
    }
  }, [sceneId])

  // Load phase state
  const loadPhaseState = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await apiClient.get(`/api/scenes/${sceneId}/phases`)

      if (response.data.success) {
        setPhaseState(response.data.data)
      } else {
        setError(response.data.error?.message || 'Failed to load phase state')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while loading phase state')
    } finally {
      setLoading(false)
    }
  }

  // Initialize phase state
  const initializePhaseState = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await apiClient.post(`/api/scenes/${sceneId}/phases/initialize`)

      if (response.data.success) {
        setPhaseState(response.data.data)
      } else {
        setError(response.data.error?.message || 'Failed to initialize phase state')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while initializing phase state')
    } finally {
      setLoading(false)
    }
  }

  // Transition to next phase
  const transitionToNextPhase = async () => {
    try {
      setTransitioning(true)
      setError(null)

      const response = await apiClient.post(`/api/scenes/${sceneId}/phases/next`)

      if (response.data.success) {
        setPhaseState(prevState => {
          if (!prevState) return null

          const newState = { ...prevState }
          newState.current_phase = response.data.data.new_phase
          newState.phases[response.data.data.previous_phase].status = PhaseStatus.COMPLETED
          newState.phases[response.data.data.new_phase].status = PhaseStatus.IN_PROGRESS
          newState.completed_phases.push(response.data.data.previous_phase)
          return newState
        })

        if (onPhaseChange) {
          onPhaseChange(response.data.data.new_phase)
        }
      } else {
        setError(response.data.error?.message || 'Failed to transition to next phase')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while transitioning to next phase')
    } finally {
      setTransitioning(false)
    }
  }

  // Transition to specific phase
  const transitionToPhase = async (phase: PhaseType) => {
    try {
      setTransitioning(true)
      setError(null)

      const response = await apiClient.post(`/api/scenes/${sceneId}/phases/transition`, {
        target_phase: phase,
      })

      if (response.data.success) {
        setPhaseState(prevState => {
          if (!prevState) return null

          const newState = { ...prevState }
          newState.current_phase = response.data.data.new_phase
          newState.phases[response.data.data.previous_phase].status = PhaseStatus.COMPLETED
          newState.phases[response.data.data.new_phase].status = PhaseStatus.IN_PROGRESS
          
          if (!newState.completed_phases.includes(response.data.data.previous_phase)) {
            newState.completed_phases.push(response.data.data.previous_phase)
          }
          
          return newState
        })

        if (onPhaseChange) {
          onPhaseChange(response.data.data.new_phase)
        }
      } else {
        setError(response.data.error?.message || 'Failed to transition to phase')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while transitioning to phase')
    } finally {
      setTransitioning(false)
    }
  }

  // Skip phase
  const skipPhase = async (phase: PhaseType) => {
    try {
      setTransitioning(true)
      setError(null)

      const response = await apiClient.post(`/api/scenes/${sceneId}/phases/skip`, {
        phase,
      })

      if (response.data.success) {
        setPhaseState(prevState => {
          if (!prevState) return null

          const newState = { ...prevState }
          newState.current_phase = response.data.data.new_phase
          newState.phases[response.data.data.previous_phase].status = PhaseStatus.SKIPPED
          newState.phases[response.data.data.new_phase].status = PhaseStatus.IN_PROGRESS
          return newState
        })

        if (onPhaseChange) {
          onPhaseChange(response.data.data.new_phase)
        }
      } else {
        setError(response.data.error?.message || 'Failed to skip phase')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while skipping phase')
    } finally {
      setTransitioning(false)
    }
  }

  // Validate current phase
  const validateCurrentPhase = async () => {
    if (!phaseState) return

    try {
      setValidating(true)
      setError(null)

      const response = await apiClient.get(`/api/scenes/${sceneId}/phases/validate`)

      if (response.data.success) {
        // Update validation results
        if (onValidationComplete) {
          response.data.data.required_validations.forEach((validation: any) => {
            onValidationComplete(validation.type, {
              valid: validation.valid,
              details: validation.details,
            })
          })
        }
      } else {
        setError(response.data.error?.message || 'Failed to validate phase')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while validating phase')
    } finally {
      setValidating(false)
    }
  }

  // Get phase status color
  const getPhaseStatusColor = (status: PhaseStatus) => {
    switch (status) {
      case PhaseStatus.COMPLETED:
        return 'success.main'
      case PhaseStatus.IN_PROGRESS:
        return 'primary.main'
      case PhaseStatus.FAILED:
        return 'error.main'
      case PhaseStatus.SKIPPED:
        return 'warning.main'
      default:
        return 'text.disabled'
    }
  }

  // Get phase status icon
  const getPhaseStatusIcon = (status: PhaseStatus) => {
    switch (status) {
      case PhaseStatus.COMPLETED:
        return <CheckCircleIcon color="success" />
      case PhaseStatus.IN_PROGRESS:
        return <CircularProgress size={20} />
      case PhaseStatus.FAILED:
        return <ErrorIcon color="error" />
      case PhaseStatus.SKIPPED:
        return <SkipNextIcon color="warning" />
      default:
        return null
    }
  }

  // Get phase status label
  const getPhaseStatusLabel = (status: PhaseStatus) => {
    switch (status) {
      case PhaseStatus.COMPLETED:
        return 'Completed'
      case PhaseStatus.IN_PROGRESS:
        return 'In Progress'
      case PhaseStatus.FAILED:
        return 'Failed'
      case PhaseStatus.SKIPPED:
        return 'Skipped'
      default:
        return 'Not Started'
    }
  }

  // Open help dialog
  const openHelpDialog = (phase: PhaseType) => {
    setSelectedPhase(phase)
    setHelpDialogOpen(true)
  }

  // Close help dialog
  const closeHelpDialog = () => {
    setHelpDialogOpen(false)
    setSelectedPhase(null)
  }

  // Render help dialog
  const renderHelpDialog = () => {
    if (!selectedPhase) return null

    const phase = phaseInfo[selectedPhase]

    return (
      <Dialog open={helpDialogOpen} onClose={closeHelpDialog} maxWidth="md" fullWidth>
        <DialogTitle>{phase.name} Phase</DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            {phase.description}
          </Typography>
          
          <Typography variant="h6" gutterBottom>
            Required Validations
          </Typography>
          
          <List>
            {/* This would be populated with actual validation requirements */}
            <ListItem>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText 
                primary="Scene Structure Validation" 
                secondary="Validates the basic structure of the scene" 
              />
            </ListItem>
            <Divider />
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="info" />
              </ListItemIcon>
              <ListItemText 
                primary="Flow Validation" 
                secondary="Validates the scene flow configuration" 
              />
            </ListItem>
          </List>
          
          <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>
            Next Steps
          </Typography>
          
          <Typography variant="body1">
            Complete all required validations to proceed to the next phase.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeHelpDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    )
  }

  // If loading, show loading indicator
  if (loading && !phaseState) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    )
  }

  // If error and no phase state, show error
  if (error && !phaseState) {
    return (
      <Box sx={{ mb: 3 }}>
        <Alert 
          severity="error" 
          action={
            <Button color="inherit" size="small" onClick={initializePhaseState}>
              Initialize
            </Button>
          }
        >
          {error}
        </Alert>
      </Box>
    )
  }

  // If no phase state, show initialize button
  if (!phaseState) {
    return (
      <Box sx={{ mb: 3 }}>
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No Phase Information
          </Typography>
          <Button 
            variant="contained" 
            onClick={initializePhaseState}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Initialize Phase Tracking'}
          </Button>
        </Paper>
      </Box>
    )
  }

  // Get active step
  const activeStep = Object.values(phaseInfo)
    .sort((a, b) => a.order - b.order)
    .findIndex(phase => phase.type === phaseState.current_phase)

  return (
    <Box sx={{ mb: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">Development Phase</Typography>
          <Box>
            <Tooltip title="Refresh">
              <IconButton onClick={loadPhaseState} disabled={loading}>
                <RefreshIcon />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
        
        <Stepper activeStep={activeStep} orientation="vertical">
          {Object.values(phaseInfo)
            .sort((a, b) => a.order - b.order)
            .map((phase) => {
              const phaseData = phaseState.phases[phase.type]
              const isActive = phaseState.current_phase === phase.type
              const isCompleted = phaseState.completed_phases.includes(phase.type)
              const canTransition = isCompleted || (
                phaseState.current_phase === phase.type && 
                phaseData.status === PhaseStatus.IN_PROGRESS
              )
              
              return (
                <Step key={phase.type}>
                  <StepLabel
                    optional={
                      <Chip 
                        label={getPhaseStatusLabel(phaseData.status)} 
                        size="small" 
                        color={
                          phaseData.status === PhaseStatus.COMPLETED ? 'success' :
                          phaseData.status === PhaseStatus.IN_PROGRESS ? 'primary' :
                          phaseData.status === PhaseStatus.FAILED ? 'error' :
                          phaseData.status === PhaseStatus.SKIPPED ? 'warning' :
                          'default'
                        }
                      />
                    }
                    icon={getPhaseStatusIcon(phaseData.status)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {phase.name}
                      <Tooltip title="Phase Information">
                        <IconButton size="small" onClick={() => openHelpDialog(phase.type)}>
                          <HelpIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </StepLabel>
                  <StepContent>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {phase.description}
                    </Typography>
                    
                    <Box sx={{ mb: 2 }}>
                      <Button
                        variant="contained"
                        onClick={validateCurrentPhase}
                        disabled={validating || !isActive}
                        sx={{ mr: 1 }}
                      >
                        {validating ? <CircularProgress size={24} /> : 'Validate'}
                      </Button>
                      
                      <Button
                        variant="outlined"
                        onClick={() => transitionToNextPhase()}
                        disabled={transitioning || !isActive}
                        endIcon={<ArrowForwardIcon />}
                        sx={{ mr: 1 }}
                      >
                        {transitioning ? <CircularProgress size={24} /> : 'Next Phase'}
                      </Button>
                      
                      {isActive && phaseData.type === PhaseType.OPTIMIZATION && (
                        <Button
                          variant="text"
                          onClick={() => skipPhase(phase.type)}
                          disabled={transitioning}
                          startIcon={<SkipNextIcon />}
                        >
                          Skip
                        </Button>
                      )}
                    </Box>
                  </StepContent>
                </Step>
              )
            })}
        </Stepper>
      </Paper>
      
      {renderHelpDialog()}
    </Box>
  )
}

export default PhaseProgressIndicator
