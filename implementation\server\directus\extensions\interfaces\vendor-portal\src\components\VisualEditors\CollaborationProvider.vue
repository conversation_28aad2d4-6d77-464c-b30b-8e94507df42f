<template>
  <div class="collaboration-provider">
    <!-- Collaboration UI elements -->
    <div v-if="isCollaborationEnabled" class="collaboration-status">
      <div class="status-indicator" :class="{ 'connected': isConnected, 'disconnected': !isConnected }">
        <i class="material-icons">{{ isConnected ? 'cloud_done' : 'cloud_off' }}</i>
        <span>{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
      </div>
      
      <div class="connected-users">
        <div v-for="user in connectedUsers" :key="user.userId" class="user-indicator" :style="{ backgroundColor: user.userColor }">
          <span class="user-initial">{{ user.userName.charAt(0) }}</span>
          <Tooltip>
            <template #trigger>
              <span class="user-name">{{ user.userName }}</span>
            </template>
            <div>
              <p>{{ user.userName }}</p>
              <p v-if="user.activeAnimation">Editing: {{ getAnimationName(user.activeAnimation) }}</p>
            </div>
          </Tooltip>
        </div>
      </div>
      
      <div class="collaboration-actions">
        <button class="action-button" @click="toggleChat">
          <i class="material-icons">chat</i>
          <span class="chat-badge" v-if="unreadMessages > 0">{{ unreadMessages }}</span>
        </button>
        <button class="action-button" @click="toggleCollaboration">
          <i class="material-icons">{{ isCollaborationEnabled ? 'people' : 'person' }}</i>
        </button>
      </div>
    </div>
    
    <!-- Chat panel -->
    <div v-if="showChat" class="chat-panel">
      <div class="chat-header">
        <h4>Chat</h4>
        <button class="close-button" @click="toggleChat">
          <i class="material-icons">close</i>
        </button>
      </div>
      
      <div class="chat-messages" ref="chatMessages">
        <div v-for="(message, index) in chatMessages" :key="index" class="chat-message" :class="{ 'own-message': message.userId === userId }">
          <div class="message-header">
            <span class="message-author" :style="{ color: message.userColor }">{{ message.userName }}</span>
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          </div>
          <div class="message-content">{{ message.text }}</div>
        </div>
      </div>
      
      <div class="chat-input">
        <input 
          type="text" 
          v-model="newMessage" 
          placeholder="Type a message..." 
          @keyup.enter="sendChatMessage"
        />
        <button class="send-button" @click="sendChatMessage" :disabled="!newMessage.trim()">
          <i class="material-icons">send</i>
        </button>
      </div>
    </div>
    
    <!-- User cursors (rendered as absolute positioned elements) -->
    <div v-for="user in connectedUsers" :key="`cursor-${user.userId}`" v-if="user.userId !== userId && user.cursor" class="user-cursor" :style="getCursorStyle(user)">
      <div class="cursor-pointer" :style="{ backgroundColor: user.userColor }"></div>
      <div class="cursor-label" :style="{ backgroundColor: user.userColor }">
        {{ user.userName }}
      </div>
    </div>
    
    <!-- Slot for the actual editor content -->
    <slot></slot>
  </div>
</template>

<script>
import { WebSocketService } from '../../services/collaboration/websocket-service';
import { YjsProvider } from '../../services/collaboration/yjs-provider';
import { ref, onMounted, onBeforeUnmount, computed, watch } from 'vue';
import Tooltip from '../common/Tooltip.vue';

export default {
  name: 'CollaborationProvider',
  
  components: {
    Tooltip
  },
  
  props: {
    /**
     * Whether collaboration is enabled
     */
    enabled: {
      type: Boolean,
      default: false
    },
    
    /**
     * Room ID for collaboration
     */
    roomId: {
      type: String,
      required: true
    },
    
    /**
     * Current user ID
     */
    userId: {
      type: String,
      required: true
    },
    
    /**
     * Current user name
     */
    userName: {
      type: String,
      required: true
    },
    
    /**
     * WebSocket URL for collaboration
     */
    wsUrl: {
      type: String,
      default: null
    },
    
    /**
     * Initial animations data
     */
    animations: {
      type: Array,
      default: () => []
    }
  },
  
  setup(props, { emit }) {
    // State
    const isCollaborationEnabled = ref(props.enabled);
    const isConnected = ref(false);
    const connectedUsers = ref([]);
    const showChat = ref(false);
    const chatMessages = ref([]);
    const newMessage = ref('');
    const unreadMessages = ref(0);
    const chatMessages = ref(null);
    
    // Services
    let websocketService = null;
    let yjsProvider = null;
    
    // Computed
    const userColor = computed(() => {
      return generateUserColor(props.userId);
    });
    
    // Methods
    const initCollaboration = async () => {
      if (!isCollaborationEnabled.value) return;
      
      try {
        // Initialize WebSocket service
        websocketService = new WebSocketService({
          roomId: props.roomId,
          userId: props.userId,
          userName: props.userName,
          userColor: userColor.value,
          wsUrl: props.wsUrl
        });
        
        // Initialize Y.js provider
        yjsProvider = new YjsProvider({
          roomId: props.roomId,
          userId: props.userId,
          userName: props.userName,
          userColor: userColor.value,
          wsUrl: props.wsUrl,
          enablePersistence: true,
          enableUndoManager: true
        });
        
        // Set initial animations
        if (props.animations && props.animations.length > 0) {
          yjsProvider.setAnimations(props.animations);
        }
        
        // Set up event listeners
        setupEventListeners();
        
        // Connect to WebSocket
        await websocketService.connect();
        isConnected.value = true;
        
        // Emit connected event
        emit('connected');
      } catch (error) {
        console.error('Failed to initialize collaboration:', error);
        isConnected.value = false;
        emit('error', error);
      }
    };
    
    const setupEventListeners = () => {
      if (!websocketService || !yjsProvider) return;
      
      // WebSocket events
      websocketService.addEventListener('open', () => {
        isConnected.value = true;
        emit('connected');
      });
      
      websocketService.addEventListener('close', () => {
        isConnected.value = false;
        emit('disconnected');
      });
      
      websocketService.addEventListener('error', (error) => {
        emit('error', error);
      });
      
      websocketService.addEventListener('message', (message) => {
        if (message.type === 'chat_message') {
          handleChatMessage(message);
        }
      });
      
      // Y.js events
      yjsProvider.on('awareness', (event) => {
        updateConnectedUsers();
      });
      
      yjsProvider.on('update', ({ update, origin }) => {
        // Handle document updates
        const animations = yjsProvider.getAnimations();
        emit('update', animations);
      });
    };
    
    const updateConnectedUsers = () => {
      if (!yjsProvider) return;
      
      connectedUsers.value = yjsProvider.getConnectedUsers();
    };
    
    const handleChatMessage = (message) => {
      chatMessages.value.push(message);
      
      // Increment unread count if chat is not visible
      if (!showChat.value && message.userId !== props.userId) {
        unreadMessages.value++;
      }
      
      // Scroll to bottom of chat
      scrollChatToBottom();
    };
    
    const scrollChatToBottom = () => {
      if (!chatMessages.value) return;
      
      setTimeout(() => {
        const element = chatMessages.value;
        element.scrollTop = element.scrollHeight;
      }, 50);
    };
    
    const sendChatMessage = () => {
      if (!newMessage.value.trim() || !websocketService) return;
      
      const message = {
        type: 'chat_message',
        userId: props.userId,
        userName: props.userName,
        userColor: userColor.value,
        text: newMessage.value.trim(),
        timestamp: Date.now()
      };
      
      websocketService.sendMessage(message);
      
      // Also add to local chat
      handleChatMessage(message);
      
      // Clear input
      newMessage.value = '';
    };
    
    const toggleChat = () => {
      showChat.value = !showChat.value;
      
      // Reset unread count when opening chat
      if (showChat.value) {
        unreadMessages.value = 0;
        scrollChatToBottom();
      }
    };
    
    const toggleCollaboration = () => {
      isCollaborationEnabled.value = !isCollaborationEnabled.value;
      
      if (isCollaborationEnabled.value) {
        initCollaboration();
      } else {
        destroyCollaboration();
      }
      
      emit('toggle', isCollaborationEnabled.value);
    };
    
    const destroyCollaboration = () => {
      if (yjsProvider) {
        yjsProvider.destroy();
        yjsProvider = null;
      }
      
      if (websocketService) {
        websocketService.disconnect();
        websocketService = null;
      }
      
      isConnected.value = false;
      connectedUsers.value = [];
      showChat.value = false;
      chatMessages.value = [];
      unreadMessages.value = 0;
    };
    
    const updateCursor = (position) => {
      if (!yjsProvider) return;
      
      yjsProvider.updateCursor(position);
    };
    
    const updateSelection = (selection) => {
      if (!yjsProvider) return;
      
      yjsProvider.updateSelection(selection);
    };
    
    const updateActiveElements = (active) => {
      if (!yjsProvider) return;
      
      yjsProvider.updateActiveElements(active);
    };
    
    const getCursorStyle = (user) => {
      if (!user.cursor) return {};
      
      return {
        position: 'absolute',
        left: `${user.cursor.x}px`,
        top: `${user.cursor.y}px`,
        zIndex: 1000
      };
    };
    
    const getAnimationName = (animationId) => {
      if (!props.animations) return 'Unknown';
      
      const animation = props.animations.find(a => a.id === animationId);
      return animation ? animation.name : 'Unknown';
    };
    
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };
    
    const generateUserColor = (userId) => {
      // Simple hash function to generate a number from a string
      let hash = 0;
      for (let i = 0; i < userId.length; i++) {
        hash = userId.charCodeAt(i) + ((hash << 5) - hash);
      }
      
      // Convert to hex color
      let color = '#';
      for (let i = 0; i < 3; i++) {
        const value = (hash >> (i * 8)) & 0xFF;
        color += ('00' + value.toString(16)).substr(-2);
      }
      
      return color;
    };
    
    // Lifecycle hooks
    onMounted(() => {
      if (isCollaborationEnabled.value) {
        initCollaboration();
      }
    });
    
    onBeforeUnmount(() => {
      destroyCollaboration();
    });
    
    // Watch for prop changes
    watch(() => props.enabled, (newValue) => {
      if (newValue !== isCollaborationEnabled.value) {
        isCollaborationEnabled.value = newValue;
        
        if (isCollaborationEnabled.value) {
          initCollaboration();
        } else {
          destroyCollaboration();
        }
      }
    });
    
    watch(() => props.animations, (newValue) => {
      if (yjsProvider && newValue && newValue.length > 0) {
        yjsProvider.setAnimations(newValue);
      }
    });
    
    // Expose methods and state to template
    return {
      isCollaborationEnabled,
      isConnected,
      connectedUsers,
      showChat,
      chatMessages,
      newMessage,
      unreadMessages,
      chatMessages,
      userColor,
      toggleChat,
      toggleCollaboration,
      sendChatMessage,
      updateCursor,
      updateSelection,
      updateActiveElements,
      getCursorStyle,
      getAnimationName,
      formatTime
    };
  }
};
</script>

<style scoped>
.collaboration-provider {
  position: relative;
  width: 100%;
  height: 100%;
}

.collaboration-status {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-right: 12px;
  font-size: 0.8rem;
}

.status-indicator i {
  font-size: 16px;
  margin-right: 4px;
}

.status-indicator.connected {
  color: var(--theme--primary);
}

.status-indicator.disconnected {
  color: var(--theme--danger);
}

.connected-users {
  display: flex;
  flex-grow: 1;
  overflow-x: auto;
  padding: 0 8px;
}

.user-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 4px;
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
}

.collaboration-actions {
  display: flex;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  position: relative;
}

.action-button i {
  font-size: 20px;
  color: var(--theme--foreground);
}

.chat-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--theme--primary);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-panel {
  position: absolute;
  right: 16px;
  top: 40px;
  width: 300px;
  height: 400px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 1000;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--theme--border-color);
}

.chat-header h4 {
  margin: 0;
  font-size: 1rem;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 8px;
}

.chat-message {
  margin-bottom: 8px;
  max-width: 80%;
}

.chat-message.own-message {
  margin-left: auto;
}

.message-header {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  margin-bottom: 2px;
}

.message-content {
  background-color: var(--theme--background-subdued);
  padding: 6px 10px;
  border-radius: 12px;
  word-break: break-word;
}

.own-message .message-content {
  background-color: var(--theme--primary-background);
}

.chat-input {
  display: flex;
  padding: 8px;
  border-top: 1px solid var(--theme--border-color);
}

.chat-input input {
  flex-grow: 1;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  margin-right: 8px;
}

.send-button {
  background-color: var(--theme--primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0 8px;
  cursor: pointer;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-cursor {
  position: absolute;
  pointer-events: none;
  z-index: 1000;
}

.cursor-pointer {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: relative;
}

.cursor-pointer::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: white;
}

.cursor-label {
  position: absolute;
  top: -20px;
  left: 0;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
  font-size: 0.7rem;
  white-space: nowrap;
}
</style>
