import React, { useEffect, useState } from 'react';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import {
  Alert,
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  IconButton,
  InputLabel,
  List,
  ListItem,
  ListItemSecondaryAction,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  Snackbar,
  TextField,
  Typography,
} from '@mui/material';
import { Add, Delete, Edit, FilterList, Visibility } from '@mui/icons-material';
import { Blueprint } from '../../shared/models/blueprint';
import { BlueprintEditor } from './BlueprintEditor';

interface BlueprintListProps {
  vendorId?: string;
  onSelectBlueprint?: (blueprint: Blueprint) => void;
}

export const BlueprintList: React.FC<BlueprintListProps> = ({ vendorId, onSelectBlueprint }) => {
  const supabase = useSupabaseClient();
  const [loading, setLoading] = useState<boolean>(false);
  const [blueprints, setBlueprints] = useState<Blueprint[]>([]);
  const [selectedBlueprint, setSelectedBlueprint] = useState<Blueprint | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState<boolean>(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [blueprintToDelete, setBlueprintToDelete] = useState<Blueprint | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [tagFilter, setTagFilter] = useState<string>('');
  const [availableTags, setAvailableTags] = useState<string[]>([]);

  useEffect(() => {
    loadBlueprints();
  }, [vendorId]);

  const loadBlueprints = async () => {
    setLoading(true);
    setError(null);

    try {
      let query = supabase.from('blueprints').select('*');

      if (vendorId) {
        query = query.eq('vendor_id', vendorId);
      }

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      if (data) {
        setBlueprints(data as Blueprint[]);

        // Extract all unique tags
        const tags = new Set<string>();
        data.forEach((blueprint: Blueprint) => {
          blueprint.tags.forEach(tag => tags.add(tag));
        });

        setAvailableTags(Array.from(tags));
      }
    } catch (err: any) {
      setError(`Failed to load blueprints: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBlueprint = () => {
    setSelectedBlueprint(null);
    setIsEditorOpen(true);
  };

  const handleEditBlueprint = (blueprint: Blueprint) => {
    setSelectedBlueprint(blueprint);
    setIsEditorOpen(true);
  };

  const handleViewBlueprint = (blueprint: Blueprint) => {
    if (onSelectBlueprint) {
      onSelectBlueprint(blueprint);
    }
  };

  const handleDeleteBlueprint = (blueprint: Blueprint) => {
    setBlueprintToDelete(blueprint);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteBlueprint = async () => {
    if (!blueprintToDelete) return;

    setLoading(true);
    setError(null);

    try {
      const { error } = await supabase.from('blueprints').delete().eq('id', blueprintToDelete.id);

      if (error) {
        throw error;
      }

      setBlueprints(blueprints.filter(b => b.id !== blueprintToDelete.id));
      setSuccess(`Blueprint "${blueprintToDelete.name}" deleted successfully`);
    } catch (err: any) {
      setError(`Failed to delete blueprint: ${err.message}`);
    } finally {
      setLoading(false);
      setIsDeleteDialogOpen(false);
      setBlueprintToDelete(null);
    }
  };

  const handleSaveBlueprint = (blueprint: Blueprint) => {
    if (selectedBlueprint) {
      // Update existing blueprint in the list
      setBlueprints(blueprints.map(b => (b.id === blueprint.id ? blueprint : b)));
    } else {
      // Add new blueprint to the list
      setBlueprints([...blueprints, blueprint]);
    }

    setIsEditorOpen(false);
    setSelectedBlueprint(null);

    // Update available tags
    const tags = new Set<string>(availableTags);
    blueprint.tags.forEach(tag => tags.add(tag));
    setAvailableTags(Array.from(tags));
  };

  const handleTagFilterChange = (event: SelectChangeEvent<string>) => {
    setTagFilter(event.target.value);
  };

  const filteredBlueprints = blueprints.filter(blueprint => {
    const matchesSearch =
      searchTerm === '' ||
      blueprint.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (blueprint.description &&
        blueprint.description.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesTag = tagFilter === '' || blueprint.tags.includes(tagFilter);

    return matchesSearch && matchesTag;
  });

  return (
    <Box>
      <Card>
        <CardHeader
          title="Blueprints"
          action={
            <Button
              variant="contained"
              color="primary"
              startIcon={<Add />}
              onClick={handleCreateBlueprint}
            >
              Create Blueprint
            </Button>
          }
        />
        <Divider />
        <CardContent>
          <Box sx={{ mb: 3, display: 'flex', gap: 2 }}>
            <TextField
              label="Search"
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
              sx={{ flexGrow: 1 }}
            />
            <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
              <InputLabel id="tag-filter-label">Filter by Tag</InputLabel>
              <Select
                labelId="tag-filter-label"
                value={tagFilter}
                onChange={handleTagFilterChange}
                label="Filter by Tag"
              >
                <MenuItem value="">
                  <em>All Tags</em>
                </MenuItem>
                {availableTags.map(tag => (
                  <MenuItem key={tag} value={tag}>
                    {tag}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : filteredBlueprints.length === 0 ? (
            <Paper sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body1" color="textSecondary">
                No blueprints found
              </Typography>
            </Paper>
          ) : (
            <List>
              {filteredBlueprints.map(blueprint => (
                <ListItem
                  key={blueprint.id}
                  divider
                  component="div"
                  sx={{ cursor: 'pointer' }}
                  onClick={() => handleViewBlueprint(blueprint)}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="subtitle1">{blueprint.name}</Typography>
                        <Typography variant="caption" color="textSecondary" sx={{ ml: 1 }}>
                          v{blueprint.version}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="textSecondary" noWrap>
                          {blueprint.description || 'No description'}
                        </Typography>
                        <Box
                          sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 0.5,
                            mt: 0.5,
                          }}
                        >
                          {blueprint.tags.map(tag => (
                            <Chip key={tag} label={tag} size="small" />
                          ))}
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={e => {
                        e.stopPropagation();
                        handleEditBlueprint(blueprint);
                      }}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      edge="end"
                      onClick={e => {
                        e.stopPropagation();
                        handleDeleteBlueprint(blueprint);
                      }}
                    >
                      <Delete />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {isEditorOpen && (
        <Dialog open={isEditorOpen} onClose={() => setIsEditorOpen(false)} maxWidth="lg" fullWidth>
          <DialogContent>
            <BlueprintEditor
              vendorId={vendorId || ''}
              blueprintId={selectedBlueprint?.id}
              onSave={handleSaveBlueprint}
              onCancel={() => setIsEditorOpen(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      <Dialog open={isDeleteDialogOpen} onClose={() => setIsDeleteDialogOpen(false)}>
        <DialogTitle>Delete Blueprint</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the blueprint "{blueprintToDelete?.name}"? This action
            cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={confirmDeleteBlueprint} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      <Snackbar open={!!error} autoHideDuration={6000} onClose={() => setError(null)}>
        <Alert onClose={() => setError(null)} severity="error">
          {error}
        </Alert>
      </Snackbar>

      <Snackbar open={!!success} autoHideDuration={6000} onClose={() => setSuccess(null)}>
        <Alert onClose={() => setSuccess(null)} severity="success">
          {success}
        </Alert>
      </Snackbar>
    </Box>
  );
};
