/**
 * User Management API
 *
 * This module provides API endpoints for user management.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase, supabaseAdmin, executeSupabaseQuery } from '../../shared/utils/supabase-client';
import { validateRequest } from '../middleware/validation';
import { authenticateRequest } from '../middleware/auth';
import {
  errorMiddleware,
  notFoundError,
  badRequestError,
  unauthorizedError,
} from '../middleware/error';
import { Logger } from '../../services/integration/logger';

// Create logger
const logger = new Logger();

/**
 * User registration handler
 *
 * @param req Request
 * @param res Response
 */
async function registerUser(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Validate request
    validateRequest('registerUser')(req, res, async () => {
      const { email, password, name, metadata } = req.body;

      // Register user with Supabase Auth
      const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: metadata || {},
      });

      if (authError) {
        logger.error('Error registering user', { error: authError });
        res.status(400).json({
          success: false,
          error: {
            code: 'REGISTRATION_ERROR',
            message: authError.message,
          },
        });
        return;
      }

      // Create user profile in users table
      const { data: userData, error: userError } = await supabaseAdmin
        .from('users')
        .insert({
          id: authData.user.id,
          name,
          role: 'user',
          metadata: metadata || {},
        })
        .select()
        .single();

      if (userError) {
        logger.error('Error creating user profile', { error: userError });

        // Clean up auth user if profile creation fails
        await supabaseAdmin.auth.admin.deleteUser(authData.user.id);

        res.status(500).json({
          success: false,
          error: {
            code: 'PROFILE_CREATION_ERROR',
            message: 'Failed to create user profile',
          },
        });
        return;
      }

      // Return success response
      res.status(201).json({
        success: true,
        data: {
          id: userData.id,
          name: userData.name,
          email: authData.user.email,
          role: userData.role,
          metadata: userData.metadata,
        },
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * User login handler
 *
 * @param req Request
 * @param res Response
 */
async function loginUser(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Validate request
    validateRequest('loginUser')(req, res, async () => {
      const { email, password } = req.body;

      // Authenticate user with Supabase Auth
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        logger.error('Error logging in user', { error });
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_ERROR',
            message: error.message,
          },
        });
        return;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: {
          user: data.user,
          session: data.session,
        },
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Get user profile handler
 *
 * @param req Request
 * @param res Response
 */
async function getUserProfile(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('getUserProfile')(req, res, async () => {
        // Get user ID from params or authenticated user
        const userId = req.query.id || req.user?.id;

        if (!userId) {
          res.status(400).json({
            success: false,
            error: {
              code: 'MISSING_USER_ID',
              message: 'User ID is required',
            },
          });
          return;
        }

        // Get user profile
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single();

        if (userError) {
          logger.error('Error getting user profile', { error: userError });
          res.status(404).json({
            success: false,
            error: {
              code: 'USER_NOT_FOUND',
              message: 'User not found',
            },
          });
          return;
        }

        // Return success response
        res.status(200).json({
          success: true,
          data: userData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Update user profile handler
 *
 * @param req Request
 * @param res Response
 */
async function updateUserProfile(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('updateUserProfile')(req, res, async () => {
        // Get user ID from params or authenticated user
        const userId = req.query.id || req.user?.id;

        if (!userId) {
          res.status(400).json({
            success: false,
            error: {
              code: 'MISSING_USER_ID',
              message: 'User ID is required',
            },
          });
          return;
        }

        // Check if user is authorized to update this profile
        if (req.user?.id !== userId && req.user?.role !== 'admin') {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to update this profile',
            },
          });
          return;
        }

        const { name, metadata } = req.body;

        // Update user profile
        const { data: userData, error: userError } = await supabase
          .from('users')
          .update({
            ...(name && { name }),
            ...(metadata && { metadata }),
          })
          .eq('id', userId)
          .select()
          .single();

        if (userError) {
          logger.error('Error updating user profile', { error: userError });
          res.status(500).json({
            success: false,
            error: {
              code: 'UPDATE_ERROR',
              message: 'Failed to update user profile',
            },
          });
          return;
        }

        // Return success response
        res.status(200).json({
          success: true,
          data: userData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Handler for user API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'POST':
      if (req.query.action === 'login') {
        await loginUser(req, res);
      } else {
        await registerUser(req, res);
      }
      break;
    case 'GET':
      await getUserProfile(req, res);
      break;
    case 'PUT':
      await updateUserProfile(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
