/**
 * Telemetry API
 *
 * This module provides API endpoints for telemetry tracking.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { telemetryService } from '../../services/telemetry/telemetry-service';

// Create logger instance
const apiLogger = new Logger();

/**
 * Track event handler
 *
 * @param req Request
 * @param res Response
 */
async function trackEvent(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Get session (optional)
    const {
      data: { session },
    } = await supabase.auth.getSession();
    const userId = session?.user?.id;

    const { event_name, properties, anonymous } = req.body;

    // Track event
    try {
      // If anonymous is true, don't include user ID
      const eventUserId = anonymous ? undefined : userId;

      const event = await telemetryService.trackEvent(event_name, eventUserId, properties);

      // Return success response
      res.status(201).json({
        success: true,
        data: event,
      });
    } catch (error) {
      logger.error('Error tracking event', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'TRACKING_ERROR',
          message: 'Failed to track event',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in trackEvent', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Get events handler
 *
 * @param req Request
 * @param res Response
 */
async function getEvents(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    // Check if user is admin
    if (session.user.role !== 'admin') {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Only admins can access telemetry data',
        },
      });
      return;
    }

    const { user_id, event_name, limit = 100, offset = 0 } = req.query;

    // Get events
    try {
      let events;

      if (user_id) {
        events = await telemetryService.getEventsByUser(
          user_id as string,
          Number(limit),
          Number(offset),
        );
      } else if (event_name) {
        events = await telemetryService.getEventsByName(
          event_name as string,
          Number(limit),
          Number(offset),
        );
      } else {
        // Get all events (limited)
        const { data, error } = await supabase
          .from('telemetry')
          .select('*')
          .order('timestamp', { ascending: false })
          .range(Number(offset), Number(offset) + Number(limit) - 1);

        if (error) {
          throw error;
        }

        events = data;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: events,
      });
    } catch (error) {
      logger.error('Error getting events', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'QUERY_ERROR',
          message: 'Failed to get events',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in getEvents', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Get event counts handler
 *
 * @param req Request
 * @param res Response
 */
async function getEventCounts(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    // Check if user is admin
    if (session.user.role !== 'admin') {
      res.status(403).json({
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: 'Only admins can access telemetry data',
        },
      });
      return;
    }

    const { start_date, end_date } = req.query;

    // Get event counts
    try {
      const counts = await telemetryService.getEventCountsByName(
        start_date as string,
        end_date as string,
      );

      // Return success response
      res.status(200).json({
        success: true,
        data: counts,
      });
    } catch (error) {
      logger.error('Error getting event counts', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'QUERY_ERROR',
          message: 'Failed to get event counts',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in getEventCounts', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Track cache eviction handler
 *
 * @param req Request
 * @param res Response
 */
async function trackCacheEviction(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { vendor_id, asset_id, reason, metadata } = req.body;

    // Track cache eviction
    try {
      const eviction = await telemetryService.trackCacheEviction(
        vendor_id,
        asset_id,
        reason,
        metadata,
      );

      // Return success response
      res.status(201).json({
        success: true,
        data: eviction,
      });
    } catch (error) {
      logger.error('Error tracking cache eviction', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'TRACKING_ERROR',
          message: 'Failed to track cache eviction',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in trackCacheEviction', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Handler for telemetry API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'POST':
      if (req.query.action === 'cache-eviction') {
        await trackCacheEviction(req, res);
      } else {
        await trackEvent(req, res);
      }
      break;
    case 'GET':
      if (req.query.action === 'counts') {
        await getEventCounts(req, res);
      } else {
        await getEvents(req, res);
      }
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
