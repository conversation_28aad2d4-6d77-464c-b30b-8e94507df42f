/**
 * API Key Authentication Middleware
 *
 * This middleware handles API key authentication for the server-side API.
 * It validates API keys, checks permissions and scopes, and tracks usage.
 *
 * Enhanced with:
 * - Caching to reduce database lookups
 * - Rate limiting specific to API keys
 * - Better security practices
 * - Comprehensive error handling
 * - Detailed logging
 */

const crypto = require('crypto');
const { createClient } = require('@supabase/supabase-js');
const { logger, redis } = require('./auth-middleware');

// Configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const nodeEnv = process.env.NODE_ENV || 'development';

// Cache configuration
const CACHE_TTL = 5 * 60; // 5 minutes in seconds
const CACHE_PREFIX = 'api_key:';

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60; // 1 minute in seconds
const DEFAULT_RATE_LIMIT = 60; // 60 requests per minute

// Initialize Supabase client
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
  },
});

/**
 * Hash an API key for comparison
 * @param {string} apiKey - API key to hash
 * @returns {string} Hashed API key
 */
function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

/**
 * Get API key data from cache or database
 * @param {string} hashedKey - Hashed API key
 * @returns {Promise<Object|null>} API key data or null if not found
 */
async function getApiKeyData(hashedKey) {
  try {
    // Try to get from cache first
    const cacheKey = `${CACHE_PREFIX}${hashedKey}`;
    const cachedData = await redis.get(cacheKey);

    if (cachedData) {
      logger.debug('API key data retrieved from cache');
      return JSON.parse(cachedData);
    }

    // If not in cache, get from database
    const { data: apiKeyData, error } = await supabase
      .from('api_keys')
      .select('id, user_id, permissions, scopes, expires_at, enabled, rate_limit')
      .eq('key_hash', hashedKey)
      .single();

    if (error || !apiKeyData) {
      return null;
    }

    // Store in cache
    await redis.set(cacheKey, JSON.stringify(apiKeyData), 'EX', CACHE_TTL);

    return apiKeyData;
  } catch (error) {
    logger.error('Error getting API key data:', error);
    return null;
  }
}

/**
 * Check if API key is rate limited
 * @param {string} apiKeyId - API key ID
 * @param {number} rateLimit - Rate limit (requests per minute)
 * @returns {Promise<boolean>} True if rate limited, false otherwise
 */
async function isRateLimited(apiKeyId, rateLimit = DEFAULT_RATE_LIMIT) {
  try {
    const rateLimitKey = `${CACHE_PREFIX}${apiKeyId}:rate_limit`;
    const currentCount = await redis.incr(rateLimitKey);

    // Set expiry on first request
    if (currentCount === 1) {
      await redis.expire(rateLimitKey, RATE_LIMIT_WINDOW);
    }

    return currentCount > rateLimit;
  } catch (error) {
    logger.error('Error checking rate limit:', error);
    return false; // Allow request on error
  }
}

/**
 * Track API key usage
 * @param {string} apiKeyId - API key ID
 * @param {string} endpoint - API endpoint
 * @param {string} method - HTTP method
 * @param {string} ip - Client IP address
 */
async function trackApiKeyUsage(apiKeyId, endpoint, method, ip) {
  try {
    // Update last_used timestamp in database (with debounce to reduce DB writes)
    const updateKey = `${CACHE_PREFIX}${apiKeyId}:last_updated`;
    const lastUpdated = await redis.get(updateKey);

    if (!lastUpdated) {
      // Update database and set debounce flag
      await supabase
        .from('api_keys')
        .update({ last_used: new Date().toISOString() })
        .eq('id', apiKeyId);

      await redis.set(updateKey, Date.now().toString(), 'EX', 60); // 1 minute debounce
    }

    // Track usage in Redis for rate limiting and analytics
    const now = Date.now();
    const day = Math.floor(now / 86400000); // Current day (for daily limits)

    // Increment usage counters
    await redis.hincrby(`api_key:${apiKeyId}:usage`, 'total', 1);
    await redis.hincrby(`api_key:${apiKeyId}:usage:${day}`, 'daily', 1);
    await redis.hincrby(`api_key:${apiKeyId}:endpoints`, endpoint, 1);

    // Store recent requests for analytics (with sampling to reduce Redis memory usage)
    if (Math.random() < 0.1) {
      // 10% sampling rate
      const requestData = JSON.stringify({
        timestamp: now,
        endpoint,
        method,
        ip,
      });

      await redis.lpush(`api_key:${apiKeyId}:requests`, requestData);
      await redis.ltrim(`api_key:${apiKeyId}:requests`, 0, 99); // Keep last 100 requests
    }

    logger.debug(`API key usage tracked: ${apiKeyId.substring(0, 8)}...`);
  } catch (error) {
    logger.error('Error tracking API key usage:', error);
  }
}

/**
 * Check if API key has required permissions
 * @param {Array} keyPermissions - API key permissions
 * @param {Array} requiredPermissions - Required permissions
 * @returns {boolean} True if API key has required permissions
 */
function hasRequiredPermissions(keyPermissions, requiredPermissions) {
  // If no permissions are required, allow access
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true;
  }

  // If key has wildcard permission, allow access
  if (keyPermissions.includes('*')) {
    return true;
  }

  // Check if key has all required permissions
  return requiredPermissions.every(permission => {
    // Check for exact match
    if (keyPermissions.includes(permission)) {
      return true;
    }

    // Check for wildcard matches (e.g. "users:*" matches "users:read")
    return keyPermissions.some(keyPermission => {
      if (keyPermission.endsWith(':*')) {
        const prefix = keyPermission.slice(0, -2);
        return permission.startsWith(prefix);
      }
      return false;
    });
  });
}

/**
 * Check if API key has required scopes
 * @param {Array} keyScopes - API key scopes
 * @param {Array} requiredScopes - Required scopes
 * @returns {boolean} True if API key has required scopes
 */
function hasRequiredScopes(keyScopes, requiredScopes) {
  // If no scopes are required, allow access
  if (!requiredScopes || requiredScopes.length === 0) {
    return true;
  }

  // If key has wildcard scope, allow access
  if (keyScopes.includes('*')) {
    return true;
  }

  // Check if key has all required scopes
  return requiredScopes.every(scope => keyScopes.includes(scope));
}

/**
 * API key authentication middleware
 * @param {Object} options - Options
 * @param {boolean} options.required - Whether API key authentication is required
 * @param {Array} options.permissions - Required permissions
 * @param {Array} options.scopes - Required scopes
 * @returns {Function} - Express middleware
 */
const authenticateApiKey = (options = { required: true, permissions: [], scopes: [] }) => {
  return async (req, res, next) => {
    try {
      // Get API key from header or query parameter (header takes precedence)
      const apiKey = req.headers['x-api-key'] || req.query.api_key;

      if (!apiKey) {
        if (options.required) {
          return res.status(401).json({
            success: false,
            error: {
              code: 'API_KEY_REQUIRED',
              message: 'API key is required',
            },
          });
        } else {
          // Continue without API key if not required
          return next();
        }
      }

      // Hash API key for comparison
      const hashedKey = hashApiKey(apiKey);

      // Get API key data from cache or database
      const apiKeyData = await getApiKeyData(hashedKey);

      if (!apiKeyData) {
        logger.warn(`Invalid API key: ${apiKey.substring(0, 8)}...`);
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_API_KEY',
            message: 'Invalid API key',
          },
        });
      }

      // Check if API key is enabled
      if (!apiKeyData.enabled) {
        logger.warn(`Disabled API key used: ${apiKeyData.id}`);
        return res.status(401).json({
          success: false,
          error: {
            code: 'API_KEY_DISABLED',
            message: 'API key is disabled',
          },
        });
      }

      // Check if API key is expired
      if (apiKeyData.expires_at && new Date(apiKeyData.expires_at) < new Date()) {
        logger.warn(`Expired API key used: ${apiKeyData.id}`);
        return res.status(401).json({
          success: false,
          error: {
            code: 'API_KEY_EXPIRED',
            message: 'API key is expired',
          },
        });
      }

      // Check if API key is rate limited
      const rateLimit = apiKeyData.rate_limit || DEFAULT_RATE_LIMIT;
      if (await isRateLimited(apiKeyData.id, rateLimit)) {
        logger.warn(`Rate limit exceeded for API key: ${apiKeyData.id}`);
        return res.status(429).json({
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: 'Rate limit exceeded. Please try again later.',
          },
        });
      }

      // Check permissions
      if (!hasRequiredPermissions(apiKeyData.permissions, options.permissions)) {
        logger.warn(`Insufficient permissions for API key: ${apiKeyData.id}`);
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_PERMISSIONS',
            message: 'API key does not have required permissions',
          },
        });
      }

      // Check scopes
      if (!hasRequiredScopes(apiKeyData.scopes, options.scopes)) {
        logger.warn(`Insufficient scopes for API key: ${apiKeyData.id}`);
        return res.status(403).json({
          success: false,
          error: {
            code: 'INSUFFICIENT_SCOPES',
            message: 'API key does not have required scopes',
          },
        });
      }

      // Get user data with caching
      const userCacheKey = `${CACHE_PREFIX}user:${apiKeyData.user_id}`;
      let userData = null;

      // Try to get user data from cache
      const cachedUserData = await redis.get(userCacheKey);
      if (cachedUserData) {
        userData = JSON.parse(cachedUserData);
      } else {
        // Get user data from database
        const { data, error: userError } = await supabase
          .from('users')
          .select('id, email, role, vendor_id')
          .eq('id', apiKeyData.user_id)
          .single();

        if (userError) {
          logger.error('Error fetching user data for API key:', userError);
          return res.status(500).json({
            success: false,
            error: {
              code: 'INTERNAL_ERROR',
              message: 'Error fetching user data',
            },
          });
        }

        userData = data;

        // Cache user data
        await redis.set(userCacheKey, JSON.stringify(userData), 'EX', CACHE_TTL);
      }

      // Add API key and user data to request
      req.apiKey = {
        id: apiKeyData.id,
        permissions: apiKeyData.permissions,
        scopes: apiKeyData.scopes,
        rateLimit,
      };

      req.user = userData;

      // Track API key usage
      const clientIp = req.ip || req.connection.remoteAddress;
      trackApiKeyUsage(apiKeyData.id, req.originalUrl, req.method, clientIp);

      // Continue to next middleware
      next();
    } catch (error) {
      logger.error('API key authentication error:', error);
      return res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An error occurred while authenticating the API key',
        },
      });
    }
  };
};

module.exports = {
  authenticateApiKey,
  hashApiKey,
  trackApiKeyUsage,
  hasRequiredPermissions,
  hasRequiredScopes,
  getApiKeyData,
  isRateLimited,
};
