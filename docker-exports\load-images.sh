#!/bin/bash
# Load Docker images from exported tar.zip files

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MANIFEST_FILE="$SCRIPT_DIR/export-manifest.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

function log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

function log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

function log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if manifest exists
if [[ ! -f "$MANIFEST_FILE" ]]; then
    log_error "Manifest file not found: $MANIFEST_FILE"
    exit 1
fi

# Check if docker is available
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed or not in PATH"
    exit 1
fi

# Check if unzip is available
if ! command -v unzip &> /dev/null; then
    log_error "unzip is not installed or not in PATH"
    exit 1
fi

log_info "Loading Docker images from exported files..."

# Parse manifest and load images
loaded_count=0
failed_count=0
total_size=0

# Read manifest and process each image
while IFS= read -r line; do
    if [[ $line =~ \"file\":[[:space:]]*\"([^\"]+)\" ]]; then
        filename="${BASH_REMATCH[1]}"
        filepath="$SCRIPT_DIR/$filename"
        
        if [[ -f "$filepath" ]]; then
            log_info "Loading image from: $filename"
            
            # Get file size for progress tracking
            file_size=$(stat -c%s "$filepath" 2>/dev/null || echo "0")
            total_size=$((total_size + file_size))
            
            # Extract and load the image
            if [[ "$filename" == *.tar.zip ]]; then
                # Extract the zip file and load the tar
                temp_dir=$(mktemp -d)
                if unzip -q "$filepath" -d "$temp_dir"; then
                    tar_file=$(find "$temp_dir" -name "*.tar" | head -1)
                    if [[ -f "$tar_file" ]]; then
                        if docker load < "$tar_file"; then
                            log_success "Loaded: $filename"
                            loaded_count=$((loaded_count + 1))
                        else
                            log_error "Failed to load: $filename"
                            failed_count=$((failed_count + 1))
                        fi
                    else
                        log_error "No tar file found in: $filename"
                        failed_count=$((failed_count + 1))
                    fi
                else
                    log_error "Failed to extract: $filename"
                    failed_count=$((failed_count + 1))
                fi
                rm -rf "$temp_dir"
            elif [[ "$filename" == *.tar ]]; then
                # Load tar file directly
                if docker load < "$filepath"; then
                    log_success "Loaded: $filename"
                    loaded_count=$((loaded_count + 1))
                else
                    log_error "Failed to load: $filename"
                    failed_count=$((failed_count + 1))
                fi
            else
                log_warning "Skipping unknown file type: $filename"
            fi
        else
            log_warning "File not found: $filename"
            failed_count=$((failed_count + 1))
        fi
    fi
done < "$MANIFEST_FILE"

# Convert total size to human readable
if [[ $total_size -gt 1073741824 ]]; then
    size_human=$(echo "scale=2; $total_size / 1073741824" | bc -l 2>/dev/null || echo "$(($total_size / 1073741824))")
    size_unit="GB"
elif [[ $total_size -gt 1048576 ]]; then
    size_human=$(echo "scale=2; $total_size / 1048576" | bc -l 2>/dev/null || echo "$(($total_size / 1048576))")
    size_unit="MB"
else
    size_human=$(echo "scale=2; $total_size / 1024" | bc -l 2>/dev/null || echo "$(($total_size / 1024))")
    size_unit="KB"
fi

log_info "=== Load Summary ==="
log_info "Images loaded: $loaded_count"
log_info "Failed: $failed_count"
log_info "Total size processed: ${size_human}${size_unit}"

# Show loaded images
log_info "=== Loaded Images ==="
docker images --filter "reference=mvs-vr-v2-*" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

if [[ $failed_count -eq 0 ]]; then
    log_success "All images loaded successfully!"
    exit 0
else
    log_error "Some images failed to load. Check the logs above."
    exit 1
fi
