<template>
  <div class="activity-tracking">
    <div class="activity-header">
      <h2 class="activity-title">Activity Tracking</h2>
      <div class="activity-actions">
        <button class="refresh-button" @click="refreshActivities" :disabled="isLoading">
          <i class="material-icons">refresh</i>
          <span>Refresh</span>
        </button>
        <button class="filter-button" @click="showFilterPanel = !showFilterPanel">
          <i class="material-icons">filter_list</i>
          <span>Filter</span>
        </button>
      </div>
    </div>

    <div class="activity-content">
      <div class="activity-sidebar" :class="{ 'show-filters': showFilterPanel }">
        <div class="filter-header">
          <h3>Filters</h3>
          <button class="close-filters-button" @click="showFilterPanel = false">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="filter-content">
          <div class="filter-group">
            <h4>Activity Type</h4>
            <div
              v-for="type in activityTypes"
              :key="type.id"
              class="filter-checkbox"
            >
              <input
                :id="`type-${type.id}`"
                type="checkbox"
                v-model="selectedTypes"
                :value="type.id"
              />
              <label :for="`type-${type.id}`">{{ type.name }}</label>
            </div>
          </div>

          <div class="filter-group">
            <h4>Users</h4>
            <div
              v-for="user in users"
              :key="user.id"
              class="filter-checkbox"
            >
              <input
                :id="`user-${user.id}`"
                type="checkbox"
                v-model="selectedUsers"
                :value="user.id"
              />
              <label :for="`user-${user.id}`">{{ user.name }}</label>
            </div>
          </div>

          <div class="filter-group">
            <h4>Date Range</h4>
            <div class="date-range-inputs">
              <div class="date-input">
                <label for="date-from">From</label>
                <input
                  id="date-from"
                  type="date"
                  v-model="dateRange.from"
                />
              </div>
              <div class="date-input">
                <label for="date-to">To</label>
                <input
                  id="date-to"
                  type="date"
                  v-model="dateRange.to"
                />
              </div>
            </div>
          </div>

          <div class="filter-actions">
            <button class="reset-button" @click="resetFilters">Reset Filters</button>
            <button class="apply-button" @click="applyFilters">Apply Filters</button>
          </div>
        </div>
      </div>

      <div class="activity-feed">
        <div class="feed-header">
          <div class="feed-tabs">
            <button
              v-for="tab in feedTabs"
              :key="tab.id"
              class="tab-button"
              :class="{ active: activeTab === tab.id }"
              @click="activeTab = tab.id"
            >
              {{ tab.name }}
            </button>
          </div>

          <div class="feed-search">
            <i class="material-icons search-icon">search</i>
            <input
              type="text"
              v-model="searchQuery"
              placeholder="Search activities..."
              class="search-input"
            />
          </div>
        </div>

        <div v-if="isLoading" class="feed-loading">
          <div class="spinner"></div>
          <span>Loading activities...</span>
        </div>

        <div v-else-if="filteredActivities.length === 0" class="feed-empty">
          <i class="material-icons">history</i>
          <p>No activities found</p>
          <button class="reset-button" @click="resetFilters">Reset Filters</button>
        </div>

        <div v-else class="feed-list">
          <div
            v-for="(group, date) in groupedActivities"
            :key="date"
            class="activity-group"
          >
            <div class="group-header">
              <div class="date-divider"></div>
              <div class="date-label">{{ formatGroupDate(date) }}</div>
              <div class="date-divider"></div>
            </div>

            <div
              v-for="activity in group"
              :key="activity.id"
              class="activity-item"
              :class="{ 'expanded': expandedActivities.includes(activity.id) }"
            >
              <div class="activity-icon" :class="`type-${activity.type}`">
                <i class="material-icons">{{ getActivityIcon(activity.type) }}</i>
              </div>

              <div class="activity-content">
                <div class="activity-header">
                  <div class="activity-user">
                    <div
                      class="user-avatar"
                      :style="{ backgroundColor: getAvatarColor(activity.user) }"
                    >
                      {{ getInitials(activity.user) }}
                    </div>
                    <span class="user-name">{{ activity.user }}</span>
                  </div>

                  <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
                </div>

                <div class="activity-message" v-html="formatActivityMessage(activity)"></div>

                <div v-if="activity.details" class="activity-details">
                  <button
                    class="details-toggle"
                    @click="toggleActivityDetails(activity.id)"
                  >
                    <i class="material-icons">{{ expandedActivities.includes(activity.id) ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</i>
                    <span>{{ expandedActivities.includes(activity.id) ? 'Hide Details' : 'Show Details' }}</span>
                  </button>

                  <div v-if="expandedActivities.includes(activity.id)" class="details-content">
                    <pre>{{ JSON.stringify(activity.details, null, 2) }}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-if="hasMoreActivities" class="load-more">
            <button class="load-more-button" @click="loadMoreActivities">
              Load More
            </button>
          </div>
        </div>
      </div>

      <div class="activity-notifications">
        <div class="notifications-header">
          <h3>Notifications</h3>
          <button class="settings-button" @click="showNotificationSettings = true">
            <i class="material-icons">settings</i>
          </button>
        </div>

        <div class="notifications-list">
          <div v-if="notifications.length === 0" class="notifications-empty">
            <i class="material-icons">notifications_none</i>
            <p>No new notifications</p>
          </div>

          <div
            v-for="notification in notifications"
            :key="notification.id"
            class="notification-item"
            :class="{ 'unread': !notification.read }"
          >
            <div class="notification-icon" :class="`priority-${notification.priority}`">
              <i class="material-icons">{{ getNotificationIcon(notification.type) }}</i>
            </div>

            <div class="notification-content">
              <div class="notification-message">{{ notification.message }}</div>
              <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
            </div>

            <div class="notification-actions">
              <button
                v-if="!notification.read"
                class="mark-read-button"
                @click="markNotificationRead(notification.id)"
                title="Mark as read"
              >
                <i class="material-icons">check</i>
              </button>
              <button
                class="delete-notification-button"
                @click="deleteNotification(notification.id)"
                title="Delete notification"
              >
                <i class="material-icons">delete</i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Notification Settings Modal -->
    <div v-if="showNotificationSettings" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Notification Settings</h3>
          <button class="close-button" @click="showNotificationSettings = false">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="modal-content">
          <div class="settings-section">
            <h4>Email Notifications</h4>
            <div
              v-for="setting in emailSettings"
              :key="setting.id"
              class="setting-item"
            >
              <div class="setting-info">
                <div class="setting-name">{{ setting.name }}</div>
                <div class="setting-description">{{ setting.description }}</div>
              </div>
              <div class="setting-control">
                <label class="toggle">
                  <input
                    type="checkbox"
                    v-model="setting.enabled"
                  />
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>

          <div class="settings-section">
            <h4>In-App Notifications</h4>
            <div
              v-for="setting in inAppSettings"
              :key="setting.id"
              class="setting-item"
            >
              <div class="setting-info">
                <div class="setting-name">{{ setting.name }}</div>
                <div class="setting-description">{{ setting.description }}</div>
              </div>
              <div class="setting-control">
                <label class="toggle">
                  <input
                    type="checkbox"
                    v-model="setting.enabled"
                  />
                  <span class="toggle-slider"></span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="cancel-button" @click="showNotificationSettings = false">Cancel</button>
          <button class="save-button" @click="saveNotificationSettings">Save Settings</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue';

export default {
  name: 'ActivityTracking',

  setup() {
    // Loading state
    const isLoading = ref(false);

    // Filter panel state
    const showFilterPanel = ref(false);

    // Notification settings modal
    const showNotificationSettings = ref(false);

    // Active tab
    const activeTab = ref('all');

    // Feed tabs
    const feedTabs = [
      { id: 'all', name: 'All Activity' },
      { id: 'edits', name: 'Edits' },
      { id: 'comments', name: 'Comments' },
      { id: 'logins', name: 'Logins' }
    ];

    // Search query
    const searchQuery = ref('');

    // Activity types
    const activityTypes = [
      { id: 'edit', name: 'Edits' },
      { id: 'comment', name: 'Comments' },
      { id: 'login', name: 'Logins' },
      { id: 'export', name: 'Exports' },
      { id: 'import', name: 'Imports' }
    ];

    // Users
    const users = [
      { id: 1, name: 'John Doe' },
      { id: 2, name: 'Jane Smith' },
      { id: 3, name: 'Bob Johnson' },
      { id: 4, name: 'Alice Williams' }
    ];

    // Selected filters
    const selectedTypes = ref(['edit', 'comment', 'login', 'export', 'import']);
    const selectedUsers = ref([1, 2, 3, 4]);
    const dateRange = reactive({
      from: '',
      to: ''
    });

    // Expanded activities
    const expandedActivities = ref([]);

    // Mock activities data
    const activities = ref([
      {
        id: 1,
        type: 'edit',
        user: 'John Doe',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        resource: 'Product',
        resourceId: 'PRD-001',
        resourceName: 'Modern Sofa',
        action: 'updated',
        details: {
          changes: {
            price: { from: 499.99, to: 599.99 },
            description: { from: 'Comfortable sofa', to: 'Luxurious comfortable sofa with premium fabric' }
          }
        }
      },
      {
        id: 2,
        type: 'comment',
        user: 'Jane Smith',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        resource: 'Product',
        resourceId: 'PRD-001',
        resourceName: 'Modern Sofa',
        action: 'commented',
        message: 'The new product images look great!'
      },
      {
        id: 3,
        type: 'login',
        user: 'Bob Johnson',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5), // 5 hours ago
        action: 'logged in',
        details: {
          ip: '***********',
          browser: 'Chrome',
          os: 'Windows'
        }
      },
      {
        id: 4,
        type: 'export',
        user: 'Alice Williams',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        resource: 'Products',
        action: 'exported',
        details: {
          format: 'CSV',
          count: 42,
          filters: { category: 'Furniture' }
        }
      },
      {
        id: 5,
        type: 'edit',
        user: 'John Doe',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
        resource: 'Showroom',
        resourceId: 'SHW-002',
        resourceName: 'Modern Living',
        action: 'created',
        details: {
          template: 'Modern',
          products: 12
        }
      }
    ]);

    // Mock notifications
    const notifications = ref([
      {
        id: 1,
        type: 'mention',
        priority: 'high',
        message: 'Jane Smith mentioned you in a comment',
        timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
        read: false
      },
      {
        id: 2,
        type: 'edit',
        priority: 'medium',
        message: 'Bob Johnson edited the "Modern Sofa" product',
        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        read: false
      },
      {
        id: 3,
        type: 'system',
        priority: 'low',
        message: 'System maintenance scheduled for tomorrow',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3), // 3 hours ago
        read: true
      }
    ]);

    // Email notification settings
    const emailSettings = reactive([
      {
        id: 1,
        name: 'Comments and Mentions',
        description: 'Receive emails when someone comments or mentions you',
        enabled: true
      },
      {
        id: 2,
        name: 'Product Updates',
        description: 'Receive emails when products are updated',
        enabled: true
      },
      {
        id: 3,
        name: 'User Activity',
        description: 'Receive emails about user logins and actions',
        enabled: false
      },
      {
        id: 4,
        name: 'System Notifications',
        description: 'Receive emails about system updates and maintenance',
        enabled: true
      }
    ]);

    // In-app notification settings
    const inAppSettings = reactive([
      {
        id: 1,
        name: 'Comments and Mentions',
        description: 'Show notifications when someone comments or mentions you',
        enabled: true
      },
      {
        id: 2,
        name: 'Product Updates',
        description: 'Show notifications when products are updated',
        enabled: true
      },
      {
        id: 3,
        name: 'User Activity',
        description: 'Show notifications about user logins and actions',
        enabled: true
      },
      {
        id: 4,
        name: 'System Notifications',
        description: 'Show notifications about system updates and maintenance',
        enabled: true
      }
    ]);

    // Filtered activities
    const filteredActivities = computed(() => {
      let result = [...activities.value];

      // Filter by tab
      if (activeTab.value !== 'all') {
        const tabTypeMap = {
          edits: 'edit',
          comments: 'comment',
          logins: 'login'
        };

        result = result.filter(activity => activity.type === tabTypeMap[activeTab.value]);
      }

      // Filter by type
      result = result.filter(activity => selectedTypes.value.includes(activity.type));

      // Filter by user
      const userNames = users
        .filter(user => selectedUsers.value.includes(user.id))
        .map(user => user.name);

      result = result.filter(activity => userNames.includes(activity.user));

      // Filter by date range
      if (dateRange.from) {
        const fromDate = new Date(dateRange.from);
        result = result.filter(activity => activity.timestamp >= fromDate);
      }

      if (dateRange.to) {
        const toDate = new Date(dateRange.to);
        toDate.setHours(23, 59, 59, 999); // End of day
        result = result.filter(activity => activity.timestamp <= toDate);
      }

      // Filter by search query
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(activity => {
          return (
            activity.user.toLowerCase().includes(query) ||
            (activity.resource && activity.resource.toLowerCase().includes(query)) ||
            (activity.resourceName && activity.resourceName.toLowerCase().includes(query)) ||
            (activity.message && activity.message.toLowerCase().includes(query))
          );
        });
      }

      // Sort by timestamp (newest first)
      result.sort((a, b) => b.timestamp - a.timestamp);

      return result;
    });

    // Group activities by date
    const groupedActivities = computed(() => {
      const groups = {};

      filteredActivities.value.forEach(activity => {
        const date = activity.timestamp.toDateString();

        if (!groups[date]) {
          groups[date] = [];
        }

        groups[date].push(activity);
      });

      return groups;
    });

    // Has more activities
    const hasMoreActivities = ref(false);

    // Methods
    const refreshActivities = async () => {
      isLoading.value = true;

      try {
        // In a real implementation, this would fetch activities from an API
        await new Promise(resolve => setTimeout(resolve, 1000));

        // For demo purposes, we'll just reset the loading state
        isLoading.value = false;
      } catch (error) {
        console.error('Error refreshing activities:', error);
        isLoading.value = false;
      }
    };

    const resetFilters = () => {
      selectedTypes.value = ['edit', 'comment', 'login', 'export', 'import'];
      selectedUsers.value = users.map(user => user.id);
      dateRange.from = '';
      dateRange.to = '';
      searchQuery.value = '';
      activeTab.value = 'all';
    };

    const applyFilters = () => {
      // In a real implementation, this might trigger a new API request
      // For now, we'll just close the filter panel
      showFilterPanel.value = false;
    };

    const loadMoreActivities = async () => {
      isLoading.value = true;

      try {
        // In a real implementation, this would fetch more activities from an API
        await new Promise(resolve => setTimeout(resolve, 1000));

        // For demo purposes, we'll just reset the loading state
        isLoading.value = false;
        hasMoreActivities.value = false;
      } catch (error) {
        console.error('Error loading more activities:', error);
        isLoading.value = false;
      }
    };

    const toggleActivityDetails = (activityId) => {
      const index = expandedActivities.value.indexOf(activityId);

      if (index === -1) {
        expandedActivities.value.push(activityId);
      } else {
        expandedActivities.value.splice(index, 1);
      }
    };

    const markNotificationRead = (notificationId) => {
      const notification = notifications.value.find(n => n.id === notificationId);

      if (notification) {
        notification.read = true;
      }
    };

    const deleteNotification = (notificationId) => {
      const index = notifications.value.findIndex(n => n.id === notificationId);

      if (index !== -1) {
        notifications.value.splice(index, 1);
      }
    };

    const saveNotificationSettings = () => {
      // In a real implementation, this would save settings to an API
      showNotificationSettings.value = false;
    };

    const getActivityIcon = (type) => {
      switch (type) {
        case 'edit': return 'edit';
        case 'comment': return 'comment';
        case 'login': return 'login';
        case 'export': return 'download';
        case 'import': return 'upload';
        default: return 'history';
      }
    };

    const getNotificationIcon = (type) => {
      switch (type) {
        case 'mention': return 'alternate_email';
        case 'edit': return 'edit';
        case 'system': return 'info';
        default: return 'notifications';
      }
    };

    const formatActivityMessage = (activity) => {
      let message = `<strong>${activity.user}</strong> `;

      if (activity.action) {
        message += `${activity.action} `;
      }

      if (activity.resource) {
        message += `${activity.resource} `;
      }

      if (activity.resourceName) {
        message += `<strong>${activity.resourceName}</strong>`;
      }

      if (activity.message) {
        message += `: "${activity.message}"`;
      }

      return message;
    };

    const formatGroupDate = (dateString) => {
      const date = new Date(dateString);
      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      if (date.toDateString() === today.toDateString()) {
        return 'Today';
      } else if (date.toDateString() === yesterday.toDateString()) {
        return 'Yesterday';
      } else {
        return date.toLocaleDateString(undefined, { weekday: 'long', month: 'long', day: 'numeric' });
      }
    };

    const formatTime = (date) => {
      const now = new Date();
      const diff = now - date;

      // Less than a minute
      if (diff < 60 * 1000) {
        return 'Just now';
      }

      // Less than an hour
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
      }

      // Less than a day
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
      }

      // Less than a week
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days} day${days !== 1 ? 's' : ''} ago`;
      }

      // Format as date and time
      return date.toLocaleString();
    };

    const getInitials = (name) => {
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    };

    const getAvatarColor = (name) => {
      // Generate a deterministic color based on the name
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      const hue = hash % 360;
      return `hsl(${hue}, 70%, 60%)`;
    };

    return {
      isLoading,
      showFilterPanel,
      showNotificationSettings,
      activeTab,
      feedTabs,
      searchQuery,
      activityTypes,
      users,
      selectedTypes,
      selectedUsers,
      dateRange,
      expandedActivities,
      activities,
      notifications,
      emailSettings,
      inAppSettings,
      filteredActivities,
      groupedActivities,
      hasMoreActivities,
      refreshActivities,
      resetFilters,
      applyFilters,
      loadMoreActivities,
      toggleActivityDetails,
      markNotificationRead,
      deleteNotification,
      saveNotificationSettings,
      getActivityIcon,
      getNotificationIcon,
      formatActivityMessage,
      formatGroupDate,
      formatTime,
      getInitials,
      getAvatarColor
    };
  }
};
</script>

<style scoped>
.activity-tracking {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
}

.activity-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.activity-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.activity-actions {
  display: flex;
  gap: 8px;
}

.refresh-button,
.filter-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-button:hover,
.filter-button:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.refresh-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.activity-content {
  flex: 1;
  padding: 24px;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 24px;
  overflow: hidden;
  position: relative;
}

/* Sidebar */
.activity-sidebar {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 300px;
  background-color: var(--theme--background);
  border-right: 1px solid var(--theme--border-color);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  z-index: 10;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.activity-sidebar.show-filters {
  transform: translateX(0);
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.filter-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.close-filters-button {
  background: none;
  border: none;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
}

.filter-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-checkbox input[type="checkbox"] {
  margin: 0;
}

.date-range-inputs {
  display: flex;
  gap: 16px;
}

.date-input {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-input label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.date-input input {
  padding: 8px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.reset-button,
.apply-button {
  flex: 1;
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.reset-button {
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  border: 1px solid var(--theme--border-color);
}

.reset-button:hover {
  background-color: var(--theme--background-subdued);
}

.apply-button {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border: none;
}

.apply-button:hover {
  background-color: var(--theme--primary-dark);
}

/* Feed */
.activity-feed {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.feed-header {
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.feed-tabs {
  display: flex;
  gap: 8px;
}

.tab-button {
  padding: 6px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.tab-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border-color: var(--theme--primary);
}

.feed-search {
  flex: 1;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
}

.feed-loading,
.feed-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--theme--background-subdued);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.feed-empty i {
  font-size: 48px;
  margin-bottom: 16px;
}

.feed-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.activity-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.date-divider {
  flex: 1;
  height: 1px;
  background-color: var(--theme--border-color);
}

.date-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
  white-space: nowrap;
}

.activity-item {
  display: flex;
  gap: 16px;
  padding: 16px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  transition: all 0.2s;
}

.activity-item:hover {
  background-color: var(--theme--background);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.activity-item.expanded {
  background-color: var(--theme--background);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.activity-icon.type-edit {
  background-color: #2196f3;
}

.activity-icon.type-comment {
  background-color: #4caf50;
}

.activity-icon.type-login {
  background-color: #9c27b0;
}

.activity-icon.type-export {
  background-color: #ff9800;
}

.activity-icon.type-import {
  background-color: #795548;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activity-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.activity-user {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: 600;
}

.user-name {
  font-weight: 500;
}

.activity-time {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.activity-message {
  font-size: 14px;
  line-height: 1.5;
}

.activity-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.details-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: var(--theme--primary);
  cursor: pointer;
  padding: 0;
  font-size: 12px;
}

.details-content {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  padding: 12px;
  font-family: monospace;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  border: 1px solid var(--theme--border-color);
}

.load-more {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.load-more-button {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
  transition: all 0.2s;
}

.load-more-button:hover {
  background-color: var(--theme--background-subdued);
}

/* Notifications */
.activity-notifications {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notifications-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.notifications-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.settings-button:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.notifications-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notifications-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.notifications-empty i {
  font-size: 48px;
  margin-bottom: 16px;
}

.notification-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  transition: all 0.2s;
}

.notification-item:hover {
  background-color: var(--theme--background);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  border-left: 3px solid var(--theme--primary);
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.notification-icon.priority-high {
  background-color: #f44336;
}

.notification-icon.priority-medium {
  background-color: #ff9800;
}

.notification-icon.priority-low {
  background-color: #4caf50;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.notification-message {
  font-size: 14px;
  line-height: 1.5;
}

.notification-time {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.notification-actions {
  display: flex;
  gap: 4px;
}

.mark-read-button,
.delete-notification-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: var(--theme--border-radius);
  border: none;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.mark-read-button:hover,
.delete-notification-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--theme--foreground);
}

.delete-notification-button:hover {
  color: var(--theme--danger);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.settings-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.settings-section h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
}

.setting-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 12px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
}

.setting-info {
  flex: 1;
}

.setting-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.setting-description {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.setting-control {
  display: flex;
  align-items: center;
}

.toggle {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 24px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--theme--primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(16px);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
}

.cancel-button,
.save-button {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  border: 1px solid var(--theme--border-color);
}

.cancel-button:hover {
  background-color: var(--theme--background-subdued);
}

.save-button {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border: none;
}

.save-button:hover {
  background-color: var(--theme--primary-dark);
}

/* Responsive */
@media (max-width: 1200px) {
  .activity-content {
    grid-template-columns: 1fr;
  }

  .activity-notifications {
    display: none;
  }
}

@media (max-width: 768px) {
  .feed-header {
    flex-direction: column;
  }

  .feed-tabs {
    overflow-x: auto;
    width: 100%;
  }
}
</style>
