# Kanousei VR Platform – Comprehensive Development Document

## 1. OVERVIEW & OBJECTIVES

**Summary:**
We are building a modular, plugin-driven, web-based VR platform enabling Admins and Vendors to import IFC architectural shells, slot in interactive product assets, and dynamically configure environments—including materials, lighting, AI, and spatial audio—without code. The platform delivers rapid showroom deployment and rich interactivity through server-managed bundles and a lightweight client bootstrap.

**Measurable Objectives:**

* **Plugin Bootstrap:** 100% automated download and local caching of scene bundles, with <150 ms average startup latency.
* **Asset Pipeline:** Support import of glTF-based products and IFC shells; maintain <5 MB avg product file size, <50 MB shell size.
* **Interactivity:** 90% of tagged assets respond to defined actions (e.g., drawer open, light toggle) within 50 ms input-to-action time.
* **UX Metrics:** Vendors complete onboarding wizard in ≤10 min; end-users achieve NPS ≥60 after first session.
* **Performance:** Maintain 90 fps on Oculus Quest 3; client memory usage <1 GB per scene.

**Architectural Context:**
This component sits between our cloud backend (API, asset storage, AI services) and the client VR runtime (Unreal/Unity plugin or WebGL viewer). It coordinates asset conversion services, scene configuration, and user interactions via a unified manifest-driven engine.

---

## 2. TECHNICAL SPECIFICATIONS

**Core Technologies & Frameworks:**

* **Server-Side:** Node.js microservices, Python (IfcOpenShell) for IFC→glTF conversion, AWS S3 for asset CDN, JWT-based Auth (Auth0).
* **Client-Side:** WebGL viewer (Three.js + IFC.js or xeoKit), Unreal/Unity plugin for native VR, WebRTC for live support, REST/GraphQL APIs.
* **AI & Data:** OpenAI LLM for assistant, Redis for session memory cache, PostgreSQL for relational data, ElasticSearch for analytics queries.

**Key Architectural Patterns:**

* **Manifest-Driven Boot:** Client reads `bundle.manifest.json` and `scene.flow.json` to load resources.
* **Proxy Layer:** IFC shells are rendered as glTF; discrete product assets replace proxies at runtime.
* **Event Bus:** In-app Pub/Sub (Socket.IO) for UI toggles, live avatar events, and QA preview commands.
* **Blueprint Injection:** Behavior scripts attached based on asset metadata tags.

**APIs & Dependencies:**

* **Conversion API:** `/convert/ifc-to-gltf` (Python Flask), `/convert/gltf-metadata`.
* **Asset API:** `/api/v1/assets` (upload, review, versioning).
* **Scene API:** `/api/v1/scenes/{id}` for flow and config.
* **Viewer SDK:** IFC.js v0.1+, xeoKit v2.3, GLTFLoader, DRACOLoader.
* **Security:** TLS 1.3, AES-256, IAM roles, OWASP ASVS L2.

**Performance Constraints:**

* Plugin startup <150 ms to load core scene.
* Asset caching incremental diff downloads <50 ms per bundle.
* Render loop ≤11 ms/frame (90 fps target).
* Max 10 concurrent 3D audio sources with spatial panning.

---

## 3. DATA STRUCTURES & MODELS

### 3.1 Scene Flow Schema (`scene.flow.json`)

```json
{
  "startup": { "space":"StartupScreen","next":"locationSelect" },
  "locationSelect": { "space":"LocationMenu","options":{"Dubai":"dubaiHub"} }
}
```

### 3.2 Asset Metadata Schema (`asset.metadata.json`)

```json
{
  "assetId":"drawer_001","type":"interactive",
  "interaction":{ "mechanism":"track","movement":"full"},
  "materials":["Wood_Master|Tint=#C8A373"]
}
```

### 3.3 Proxy Mapping Model

| Field     | Type    | Description                                        |
| --------- | ------- | -------------------------------------------------- |
| elementId | string  | IFC or glTF node ID                                |
| assetId   | string  | Product asset identifier                           |
| transform | Matrix4 | Local transform to align proxy with shell geometry |

### 3.4 Database Entities

* **Users:** `{id, role, vendorId, settings}`
* **Assets:** `{assetId, type, version, status, metadata}`
* **Scenes:** `{sceneId, manifestHash, config}`
* **Proxies:** `{sceneId, elementId, assetId}`
* **Sessions:** `{sessionId, userId, memoryBucket}`
* **AuditLogs:** `{logId, userId, action, target, timestamp}`

**Relationships:**

* One Vendor → Many Assets
* One Scene → Many Proxies
* One Session → Many Memory Entries

**Validation:**

* Asset IDs unique, follow `^[a-z0-9_\-]+$`.
* Metadata JSON must validate against its schema (AJV on server).

---

## 4. FUNCTIONAL REQUIREMENTS

### 4.1 Plugin Initialization

```js
async function initBootstrap(vendorId, sceneId) {
  const manifest = await fetch(`/api/scenes/${sceneId}/manifest`);
  await downloadBundles(manifest.bundles);
  return loadScene(manifest);
}
```

**Inputs:** `vendorId`, `sceneId`
**Outputs:** Promise resolving when scene is ready
**Errors:** Reject if download fails 3x (retry logic)

### 4.2 Scene Loading

* `loadScene(flowConfig)` iterates through flow nodes, instantiates spaces
* `replaceNode(elementId, assetId)` swaps proxy with asset glTF
* **Edge Cases:** missing proxies → log warning, continue

### 4.3 Asset Upload

* Endpoint: `POST /api/assets`
* Validate file size ≤ 10 MB for products, ≤ 100 MB for shells
* Run auto-QA (polycount ≤ 100k, texture ≤ 4 K)
* **Responses:** `201 Created`, `422 Unprocessable Entity` on fail

### 4.4 Interactivity Handling

```js
viewer.onClick((nodeId) => {
  const behavior = metadata.actions[nodeId];
  blueprintEngine.execute(behavior);
});
```

**Complex Ops:** dynamic weight calc: `weight = volume * density` per asset metadata

---

## 5. UI/UX CONSIDERATIONS

**Admin Panel Flows:**

1. **Onboarding Wizard:** stepper UI with summary, asset upload, shell import, proxy mapping.
2. **Proxy Mapping:** 3D canvas + list view; click IFC element → select product asset.
3. **Discipline Toggle & LOD Slider:** grouped checklist + slider controlling Three.js LOD levels.
4. **Preview Mode:** QA badge, no analytics, full interaction.

**Vendor Panel:**

* **Asset Library:** searchable grid, status labels
* **Tagging Modal:** map sub-mesh to interaction tags

**End-User UI:**

* **HUD:** minimal control (help, menu, favorites)
* **Guided Tour:** AI avatar prompts at first use

**Accessibility:**

* High-contrast mode toggle
* Keyboard/mouse + gamepad + VR controller support
* Captions for all audio prompts

---

## 6. TESTING STRATEGY

**Unit Tests:**

* JS/TS: plugin bootstrap, scene loader, proxy replace functions
* Python: IFC conversion scripts
* Validation: JSON schema tests

**Integration Tests:**

* Mock API + GitHub Actions: simulate full bootstrap flow
* End-to-end: Cypress + WebGL test runner to open scene and perform interactions

**Acceptance Criteria:**

* All PRD user stories automated in test suite
* Smoke tests pass on every merge

**Performance Tests:**

* Automated 90 fps benchmark tests using Puppeteer + headless WebGL
* Memory leak detection via Chrome DevTools protocol

**Security Tests:**

* API vulnerability scans (OWASP ZAP)
* JWT expiry and role enforcement tests

---

## 8. POTENTIAL CHALLENGES & MITIGATIONS

| Challenge                                    | Mitigation                                                            |
| -------------------------------------------- | --------------------------------------------------------------------- |
| Large IFC models cause slow conversion       | Implement server-side queuing, chunked geometry export                |
| Complex blueprint logic becomes unmanageable | Enforce modular behavior templates, limit depth of chaining (Phase 2) |
| Maintaining 90 fps on low-end devices        | Use aggressive LOD, cull distant objects, dynamic quality scaling     |
| Security breach risk in upload pipeline      | Harden endpoints with JWT, rate limit, scan uploads for malware       |

**Research Spikes:**

* Evaluate xeoKit vs IFC.js performance on 50 MB IFC files
* Prototype chunked IFC conversion to glTF 

---

## 9. QUALITY ASSURANCE CHECKLIST

*End of Document.*
