import EasingFunctions from './EasingFunctions';

/**
 * Utility functions for animation processing
 */
const AnimationUtils = {
  /**
   * Interpolate between two values using the specified easing function
   * 
   * @param {number} start - Start value
   * @param {number} end - End value
   * @param {number} t - Progress (0-1)
   * @param {string} easing - Easing function name
   * @returns {number} - Interpolated value
   */
  interpolate(start, end, t, easing = 'linear') {
    // Get the easing function or default to linear
    const easingFn = EasingFunctions[easing] || EasingFunctions.linear;
    
    // Apply easing to the progress value
    const easedT = easingFn(t);
    
    // Linear interpolation with eased progress
    return start + (end - start) * easedT;
  },
  
  /**
   * Interpolate between two objects recursively
   * 
   * @param {Object} obj1 - First object
   * @param {Object} obj2 - Second object
   * @param {number} t - Progress (0-1)
   * @param {string} easing - Easing function name
   * @returns {Object} - Interpolated object
   */
  interpolateObjects(obj1, obj2, t, easing = 'linear') {
    // Handle null values
    if (!obj1) return obj2;
    if (!obj2) return obj1;
    
    // Create a new object for the result
    const result = {};
    
    // Get all keys from both objects
    const keys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
    
    // Process each key
    keys.forEach(key => {
      // If the key exists in both objects
      if (obj1[key] !== undefined && obj2[key] !== undefined) {
        // If both values are objects, recursively interpolate
        if (typeof obj1[key] === 'object' && typeof obj2[key] === 'object' && obj1[key] !== null && obj2[key] !== null) {
          result[key] = this.interpolateObjects(obj1[key], obj2[key], t, easing);
        }
        // If both values are numbers, interpolate
        else if (typeof obj1[key] === 'number' && typeof obj2[key] === 'number') {
          result[key] = this.interpolate(obj1[key], obj2[key], t, easing);
        }
        // Otherwise, use the second object's value
        else {
          result[key] = t < 0.5 ? obj1[key] : obj2[key];
        }
      }
      // If the key only exists in one object
      else {
        result[key] = obj1[key] !== undefined ? obj1[key] : obj2[key];
      }
    });
    
    return result;
  },
  
  /**
   * Blend two animations
   * 
   * @param {Object} anim1 - First animation
   * @param {Object} anim2 - Second animation
   * @param {number} factor - Blend factor (0-1)
   * @returns {Object} - Blended animation
   */
  blendAnimations(anim1, anim2, factor) {
    // Create a new animation
    const blendedAnimation = {
      name: `Blend of ${anim1.name} and ${anim2.name}`,
      duration: Math.max(anim1.duration, anim2.duration),
      tracks: []
    };
    
    // Get all unique track types from both animations
    const trackTypes = new Set();
    if (anim1.tracks) anim1.tracks.forEach(track => trackTypes.add(track.type));
    if (anim2.tracks) anim2.tracks.forEach(track => trackTypes.add(track.type));
    
    // Process each track type
    trackTypes.forEach(trackType => {
      // Find tracks of this type in both animations
      const tracks1 = anim1.tracks ? anim1.tracks.filter(t => t.type === trackType) : [];
      const tracks2 = anim2.tracks ? anim2.tracks.filter(t => t.type === trackType) : [];
      
      // If both animations have tracks of this type, blend them
      if (tracks1.length > 0 && tracks2.length > 0) {
        for (let i = 0; i < Math.min(tracks1.length, tracks2.length); i++) {
          const track1 = tracks1[i];
          const track2 = tracks2[i];
          
          // Create a new blended track
          const blendedTrack = {
            id: `track_${Date.now()}_${i}`,
            name: `${track1.name} (Blended)`,
            type: trackType,
            keyframes: this.blendKeyframes(track1.keyframes, track2.keyframes, factor)
          };
          
          blendedAnimation.tracks.push(blendedTrack);
        }
      } else if (tracks1.length > 0) {
        // Only animation 1 has tracks of this type
        blendedAnimation.tracks.push(...this.scaleTracksByFactor(tracks1, 1 - factor));
      } else if (tracks2.length > 0) {
        // Only animation 2 has tracks of this type
        blendedAnimation.tracks.push(...this.scaleTracksByFactor(tracks2, factor));
      }
    });
    
    return blendedAnimation;
  },
  
  /**
   * Blend keyframes from two tracks
   * 
   * @param {Array} keyframes1 - Keyframes from first track
   * @param {Array} keyframes2 - Keyframes from second track
   * @param {number} factor - Blend factor (0-1)
   * @returns {Array} - Blended keyframes
   */
  blendKeyframes(keyframes1, keyframes2, factor) {
    const blendedKeyframes = [];
    const timeMap = new Set();
    
    // Collect all unique keyframe times
    if (keyframes1) keyframes1.forEach(kf => timeMap.add(kf.time));
    if (keyframes2) keyframes2.forEach(kf => timeMap.add(kf.time));
    
    // Sort times
    const times = Array.from(timeMap).sort((a, b) => a - b);
    
    // Create blended keyframes at each time point
    times.forEach(time => {
      // Find or interpolate values from both animations
      const value1 = this.getValueAtTime(keyframes1 || [], time);
      const value2 = this.getValueAtTime(keyframes2 || [], time);
      
      // Blend the values
      const blendedValue = this.blendValues(value1, value2, factor);
      
      // Create a new keyframe
      blendedKeyframes.push({
        id: `keyframe_${Date.now()}_${time}`,
        time,
        easing: 'linear', // Default to linear easing
        value: blendedValue
      });
    });
    
    return blendedKeyframes;
  },
  
  /**
   * Get the value at a specific time from a set of keyframes
   * 
   * @param {Array} keyframes - Keyframes array
   * @param {number} time - Time to get value at
   * @returns {Object} - Value at the specified time
   */
  getValueAtTime(keyframes, time) {
    // If no keyframes, return null
    if (!keyframes || keyframes.length === 0) return null;
    
    // If exact keyframe exists at this time
    const exactKeyframe = keyframes.find(kf => kf.time === time);
    if (exactKeyframe) return exactKeyframe.value;
    
    // Find surrounding keyframes for interpolation
    const prevKeyframe = keyframes.filter(kf => kf.time < time).sort((a, b) => b.time - a.time)[0];
    const nextKeyframe = keyframes.filter(kf => kf.time > time).sort((a, b) => a.time - b.time)[0];
    
    // If no surrounding keyframes, return null
    if (!prevKeyframe && !nextKeyframe) return null;
    
    // If only previous keyframe exists
    if (prevKeyframe && !nextKeyframe) return prevKeyframe.value;
    
    // If only next keyframe exists
    if (!prevKeyframe && nextKeyframe) return nextKeyframe.value;
    
    // Interpolate between keyframes
    const t = (time - prevKeyframe.time) / (nextKeyframe.time - prevKeyframe.time);
    return this.interpolateObjects(prevKeyframe.value, nextKeyframe.value, t, prevKeyframe.easing || 'linear');
  },
  
  /**
   * Blend two values
   * 
   * @param {Object} value1 - First value
   * @param {Object} value2 - Second value
   * @param {number} factor - Blend factor (0-1)
   * @returns {Object} - Blended value
   */
  blendValues(value1, value2, factor) {
    // Handle null values
    if (!value1) return value2;
    if (!value2) return value1;
    
    // Use interpolateObjects with the blend factor
    return this.interpolateObjects(value1, value2, factor);
  },
  
  /**
   * Scale tracks by a factor
   * 
   * @param {Array} tracks - Tracks to scale
   * @param {number} factor - Scale factor
   * @returns {Array} - Scaled tracks
   */
  scaleTracksByFactor(tracks, factor) {
    return tracks.map(track => {
      const newTrack = JSON.parse(JSON.stringify(track));
      newTrack.id = `track_${Date.now()}_${track.id}`;
      
      // Scale numeric values in keyframes
      if (newTrack.keyframes) {
        newTrack.keyframes = newTrack.keyframes.map(keyframe => {
          const newKeyframe = JSON.parse(JSON.stringify(keyframe));
          newKeyframe.id = `keyframe_${Date.now()}_${keyframe.id}`;
          
          // Scale values recursively
          this.scaleObject(newKeyframe.value, factor);
          
          return newKeyframe;
        });
      }
      
      return newTrack;
    });
  },
  
  /**
   * Scale an object's numeric values by a factor
   * 
   * @param {Object} obj - Object to scale
   * @param {number} factor - Scale factor
   */
  scaleObject(obj, factor) {
    if (!obj) return;
    
    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        // Recursively scale nested objects
        this.scaleObject(obj[key], factor);
      } else if (typeof obj[key] === 'number') {
        // Scale numbers
        obj[key] *= factor;
      }
    }
  }
};

export default AnimationUtils;
