import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';

// Define the request schema
const RefreshRequestSchema = z.object({
  refresh_token: z.string(),
});

// Define the response schema
const RefreshResponseSchema = z.object({
  session: z.object({
    access_token: z.string(),
    refresh_token: z.string(),
    expires_at: z.number(),
  }),
});

/**
 * Refresh API endpoint
 *
 * This endpoint refreshes a session using a refresh token.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate request body
    const bodyResult = RefreshRequestSchema.safeParse(req.body);
    if (!bodyResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid request body', details: bodyResult.error.format() });
    }

    // Extract refresh token
    const { refresh_token } = bodyResult.data;

    // Log the request
    logger.info('Refresh request');

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Refresh session
    const { data, error } = await supabase.auth.refreshSession({
      refresh_token,
    });

    if (error) {
      logger.error('Refresh error', { error });
      return res.status(401).json({ error: 'Invalid refresh token' });
    }

    // Prepare response
    const response = {
      session: {
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token,
        expires_at: data.session.expires_at,
      },
    };

    // Validate response
    const responseResult = RefreshResponseSchema.safeParse(response);
    if (!responseResult.success) {
      logger.error('Invalid response format', { error: responseResult.error.format(), response });
      return res.status(500).json({ error: 'Invalid response format' });
    }

    // Log success
    logger.info('Refresh successful', {
      user_id: data.user.id,
    });

    // Return response
    return res.status(200).json(response);
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
