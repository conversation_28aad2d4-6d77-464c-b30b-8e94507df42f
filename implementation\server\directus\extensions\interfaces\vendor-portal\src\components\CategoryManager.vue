<template>
  <div class="category-manager">
    <div class="header">
      <h2>Category Management</h2>
      <div class="actions">
        <button class="btn btn-primary" @click="openAddCategoryModal">
          <i class="material-icons">add</i> Add Category
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading categories...</span>
    </div>

    <div v-else-if="categories.length === 0" class="empty-state">
      <i class="material-icons">category</i>
      <h3>No categories found</h3>
      <p>You haven't added any product categories yet. Click "Add Category" to get started.</p>
    </div>

    <div v-else class="category-content">
      <div class="category-list">
        <div class="category-list-header">
          <div class="col-name">Name</div>
          <div class="col-products">Products</div>
          <div class="col-status">Status</div>
          <div class="col-actions">Actions</div>
        </div>

        <div class="category-items">
          <div
            v-for="category in categories"
            :key="category.id"
            class="category-item"
            :class="{ 'has-children': category.children && category.children.length > 0 }"
          >
            <div class="category-row">
              <div class="col-name">
                <div class="category-name-container">
                  <button
                    v-if="category.children && category.children.length > 0"
                    class="toggle-btn"
                    @click="toggleCategory(category.id)"
                  >
                    <i class="material-icons">{{ expandedCategories.includes(category.id) ? 'expand_more' : 'chevron_right' }}</i>
                  </button>
                  <div class="category-icon" :style="{ backgroundColor: getCategoryColor(category) }">
                    <i class="material-icons">{{ category.icon || 'category' }}</i>
                  </div>
                  <span class="category-name">{{ category.name }}</span>
                </div>
              </div>
              <div class="col-products">
                <span class="product-count">{{ category.productCount || 0 }}</span>
              </div>
              <div class="col-status">
                <div class="status-badge" :class="category.status">
                  {{ category.status }}
                </div>
              </div>
              <div class="col-actions">
                <button class="btn btn-icon" @click="editCategory(category)" title="Edit Category">
                  <i class="material-icons">edit</i>
                </button>
                <button class="btn btn-icon" @click="confirmDeleteCategory(category)" title="Delete Category">
                  <i class="material-icons">delete</i>
                </button>
              </div>
            </div>

            <div
              v-if="category.children && category.children.length > 0 && expandedCategories.includes(category.id)"
              class="subcategories"
            >
              <div
                v-for="subcategory in category.children"
                :key="subcategory.id"
                class="category-item subcategory-item"
              >
                <div class="category-row">
                  <div class="col-name">
                    <div class="category-name-container">
                      <div class="subcategory-indent"></div>
                      <div class="category-icon" :style="{ backgroundColor: getCategoryColor(subcategory) }">
                        <i class="material-icons">{{ subcategory.icon || 'category' }}</i>
                      </div>
                      <span class="category-name">{{ subcategory.name }}</span>
                    </div>
                  </div>
                  <div class="col-products">
                    <span class="product-count">{{ subcategory.productCount || 0 }}</span>
                  </div>
                  <div class="col-status">
                    <div class="status-badge" :class="subcategory.status">
                      {{ subcategory.status }}
                    </div>
                  </div>
                  <div class="col-actions">
                    <button class="btn btn-icon" @click="editCategory(subcategory)" title="Edit Category">
                      <i class="material-icons">edit</i>
                    </button>
                    <button class="btn btn-icon" @click="confirmDeleteCategory(subcategory)" title="Delete Category">
                      <i class="material-icons">delete</i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Add/Edit Category Modal -->
    <div v-if="showCategoryModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>{{ editingCategory ? 'Edit Category' : 'Add New Category' }}</h3>
          <button class="btn btn-icon" @click="closeCategoryModal">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="category-name">Category Name</label>
            <input
              id="category-name"
              type="text"
              v-model="categoryForm.name"
              placeholder="Enter category name"
              required
            />
          </div>

          <div class="form-group">
            <label for="category-description">Description</label>
            <textarea
              id="category-description"
              v-model="categoryForm.description"
              placeholder="Enter category description"
              rows="3"
            ></textarea>
          </div>

          <div class="form-row">
            <div class="form-group half">
              <label for="category-parent">Parent Category</label>
              <select id="category-parent" v-model="categoryForm.parentId">
                <option value="">None (Top Level)</option>
                <option
                  v-for="category in topLevelCategories"
                  :key="category.id"
                  :value="category.id"
                  :disabled="editingCategory && category.id === categoryForm.id"
                >
                  {{ category.name }}
                </option>
              </select>
            </div>

            <div class="form-group half">
              <label for="category-status">Status</label>
              <select id="category-status" v-model="categoryForm.status">
                <option value="active">Active</option>
                <option value="hidden">Hidden</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group half">
              <label for="category-icon">Icon</label>
              <div class="icon-selector">
                <select id="category-icon" v-model="categoryForm.icon">
                  <option value="category">Category</option>
                  <option value="chair">Furniture</option>
                  <option value="lightbulb">Lighting</option>
                  <option value="home">Home</option>
                  <option value="kitchen">Kitchen</option>
                  <option value="bed">Bedroom</option>
                  <option value="weekend">Living Room</option>
                  <option value="deck">Outdoor</option>
                  <option value="bathroom">Bathroom</option>
                  <option value="palette">Decor</option>
                </select>
                <div class="icon-preview">
                  <i class="material-icons">{{ categoryForm.icon || 'category' }}</i>
                </div>
              </div>
            </div>

            <div class="form-group half">
              <label for="category-color">Color</label>
              <input
                id="category-color"
                type="color"
                v-model="categoryForm.color"
              />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeCategoryModal">Cancel</button>
          <button
            class="btn btn-primary"
            @click="saveCategory"
            :disabled="!categoryForm.name"
          >
            {{ editingCategory ? 'Update Category' : 'Add Category' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Confirm Delete</h3>
          <button class="btn btn-icon" @click="showDeleteModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to delete the category "{{ categoryToDelete?.name }}"?</p>
          <p v-if="categoryToDelete?.productCount > 0" class="warning">
            This category contains {{ categoryToDelete.productCount }} products. These products will be uncategorized.
          </p>
          <p v-if="categoryToDelete?.children && categoryToDelete?.children.length > 0" class="warning">
            This category has {{ categoryToDelete.children.length }} subcategories that will also be deleted.
          </p>
          <p class="warning">This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showDeleteModal = false">Cancel</button>
          <button class="btn btn-danger" @click="deleteCategory">Delete Category</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CategoryManager',

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      categories: [],
      loading: true,
      expandedCategories: [],
      showCategoryModal: false,
      showDeleteModal: false,
      editingCategory: null,
      categoryToDelete: null,
      categoryForm: {
        name: '',
        description: '',
        parentId: '',
        status: 'active',
        icon: 'category',
        color: '#3498db'
      }
    };
  },

  computed: {
    topLevelCategories() {
      return this.categories.filter(category => !category.parentId);
    }
  },

  mounted() {
    this.loadCategories();
  },

  methods: {
    // Load categories from API
    async loadCategories() {
      this.loading = true;

      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/categories?vendor_id=${this.vendorId}`);
        // this.categories = response.data;

        // Mock data for demonstration
        setTimeout(() => {
          this.categories = [
            {
              id: 'furniture',
              name: 'Furniture',
              description: 'All furniture products',
              status: 'active',
              icon: 'chair',
              color: '#3498db',
              productCount: 15,
              children: [
                {
                  id: 'seating',
                  name: 'Seating',
                  description: 'Chairs, sofas, and other seating',
                  parentId: 'furniture',
                  status: 'active',
                  icon: 'weekend',
                  color: '#2980b9',
                  productCount: 8
                },
                {
                  id: 'tables',
                  name: 'Tables',
                  description: 'Dining tables, coffee tables, and more',
                  parentId: 'furniture',
                  status: 'active',
                  icon: 'table_restaurant',
                  color: '#2980b9',
                  productCount: 7
                }
              ]
            },
            {
              id: 'lighting',
              name: 'Lighting',
              description: 'All lighting products',
              status: 'active',
              icon: 'lightbulb',
              color: '#f1c40f',
              productCount: 10,
              children: []
            },
            {
              id: 'decor',
              name: 'Decor',
              description: 'Decorative items for your home',
              status: 'active',
              icon: 'palette',
              color: '#e74c3c',
              productCount: 12,
              children: []
            },
            {
              id: 'kitchen',
              name: 'Kitchen',
              description: 'Kitchen furniture and accessories',
              status: 'active',
              icon: 'kitchen',
              color: '#27ae60',
              productCount: 8,
              children: []
            },
            {
              id: 'bathroom',
              name: 'Bathroom',
              description: 'Bathroom furniture and accessories',
              status: 'hidden',
              icon: 'bathroom',
              color: '#9b59b6',
              productCount: 0,
              children: []
            },
            {
              id: 'outdoor',
              name: 'Outdoor',
              description: 'Outdoor furniture and accessories',
              status: 'active',
              icon: 'deck',
              color: '#16a085',
              productCount: 5,
              children: []
            }
          ];

          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading categories:', error);
        this.loading = false;
      }
    },

    // Toggle category expansion
    toggleCategory(categoryId) {
      const index = this.expandedCategories.indexOf(categoryId);
      if (index === -1) {
        this.expandedCategories.push(categoryId);
      } else {
        this.expandedCategories.splice(index, 1);
      }
    },

    // Get category color
    getCategoryColor(category) {
      return category.color || '#3498db';
    },

    // Open add category modal
    openAddCategoryModal() {
      this.editingCategory = null;
      this.categoryForm = {
        name: '',
        description: '',
        parentId: '',
        status: 'active',
        icon: 'category',
        color: '#3498db'
      };
      this.showCategoryModal = true;
    },

    // Open edit category modal
    editCategory(category) {
      this.editingCategory = category;
      this.categoryForm = {
        id: category.id,
        name: category.name,
        description: category.description || '',
        parentId: category.parentId || '',
        status: category.status || 'active',
        icon: category.icon || 'category',
        color: category.color || '#3498db'
      };
      this.showCategoryModal = true;
    },

    // Close category modal
    closeCategoryModal() {
      this.showCategoryModal = false;
      this.editingCategory = null;
    },

    // Save category
    async saveCategory() {
      try {
        if (this.editingCategory) {
          // Update existing category
          // This would be replaced with actual API call
          // await axios.put(`/api/categories/${this.categoryForm.id}`, {
          //   ...this.categoryForm,
          //   vendor_id: this.vendorId
          // });

          // Mock update for demonstration
          const index = this.categories.findIndex(c => c.id === this.categoryForm.id);
          if (index !== -1) {
            // If it's a top-level category
            this.categories[index] = {
              ...this.categories[index],
              name: this.categoryForm.name,
              description: this.categoryForm.description,
              parentId: this.categoryForm.parentId,
              status: this.categoryForm.status,
              icon: this.categoryForm.icon,
              color: this.categoryForm.color
            };
          } else {
            // Check if it's a subcategory
            for (const category of this.categories) {
              if (category.children) {
                const subIndex = category.children.findIndex(s => s.id === this.categoryForm.id);
                if (subIndex !== -1) {
                  category.children[subIndex] = {
                    ...category.children[subIndex],
                    name: this.categoryForm.name,
                    description: this.categoryForm.description,
                    parentId: this.categoryForm.parentId,
                    status: this.categoryForm.status,
                    icon: this.categoryForm.icon,
                    color: this.categoryForm.color
                  };

                  // If parent changed, move to new parent
                  if (this.categoryForm.parentId !== category.id) {
                    const subcategory = { ...category.children[subIndex] };
                    category.children.splice(subIndex, 1);

                    if (!this.categoryForm.parentId) {
                      // Move to top level
                      this.categories.push(subcategory);
                    } else {
                      // Move to new parent
                      const newParent = this.categories.find(c => c.id === this.categoryForm.parentId);
                      if (newParent) {
                        if (!newParent.children) {
                          newParent.children = [];
                        }
                        newParent.children.push(subcategory);
                      }
                    }
                  }
                  break;
                }
              }
            }
          }
        } else {
          // Create new category
          // This would be replaced with actual API call
          // const response = await axios.post('/api/categories', {
          //   ...this.categoryForm,
          //   vendor_id: this.vendorId
          // });
          // const newCategory = response.data;

          // Mock creation for demonstration
          const newCategory = {
            ...this.categoryForm,
            id: `category-${Date.now()}`,
            productCount: 0
          };

          if (this.categoryForm.parentId) {
            // Add as subcategory
            const parentCategory = this.categories.find(c => c.id === this.categoryForm.parentId);
            if (parentCategory) {
              if (!parentCategory.children) {
                parentCategory.children = [];
              }
              parentCategory.children.push(newCategory);
            }
          } else {
            // Add as top-level category
            this.categories.push(newCategory);
          }
        }

        this.closeCategoryModal();
      } catch (error) {
        console.error('Error saving category:', error);
      }
    },

    // Confirm delete category
    confirmDeleteCategory(category) {
      this.categoryToDelete = category;
      this.showDeleteModal = true;
    },

    // Delete category
    async deleteCategory() {
      try {
        // This would be replaced with actual API call
        // await axios.delete(`/api/categories/${this.categoryToDelete.id}`);

        // Mock deletion for demonstration
        if (this.categoryToDelete.parentId) {
          // Delete subcategory
          for (const category of this.categories) {
            if (category.children) {
              const index = category.children.findIndex(s => s.id === this.categoryToDelete.id);
              if (index !== -1) {
                category.children.splice(index, 1);
                break;
              }
            }
          }
        } else {
          // Delete top-level category
          const index = this.categories.findIndex(c => c.id === this.categoryToDelete.id);
          if (index !== -1) {
            this.categories.splice(index, 1);
          }
        }

        this.showDeleteModal = false;
        this.categoryToDelete = null;
      } catch (error) {
        console.error('Error deleting category:', error);
      }
    }
  }
};
</script>

<style scoped>
.category-manager {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.empty-state p {
  margin: 0;
  color: var(--theme--foreground-subdued);
}

.category-content {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  overflow: hidden;
}

.category-list-header {
  display: flex;
  background-color: var(--theme--background-subdued);
  padding: 12px 16px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
  border-bottom: 1px solid var(--theme--border-color);
}

.category-item {
  border-bottom: 1px solid var(--theme--border-color);
}

.category-item:last-child {
  border-bottom: none;
}

.category-row {
  display: flex;
  padding: 12px 16px;
  align-items: center;
}

.col-name {
  flex: 1;
  min-width: 0;
}

.col-products {
  width: 100px;
  text-align: center;
}

.col-status {
  width: 100px;
  text-align: center;
}

.col-actions {
  width: 100px;
  display: flex;
  justify-content: flex-end;
  gap: 5px;
}

.category-name-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.category-icon {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.category-icon i {
  font-size: 18px;
}

.category-name {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: var(--theme--background-subdued);
  padding: 0 8px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.status-badge.active {
  background-color: var(--theme--primary);
  color: white;
}

.status-badge.hidden {
  background-color: var(--theme--warning);
  color: white;
}

.subcategories {
  background-color: var(--theme--background-subdued);
}

.subcategory-item {
  border-bottom: 1px solid rgba(var(--theme--border-color-rgb), 0.5);
}

.subcategory-indent {
  width: 24px;
}

.icon-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.icon-preview {
  width: 40px;
  height: 40px;
  border-radius: 4px;
  background-color: var(--theme--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.icon-preview i {
  font-size: 24px;
}

input[type="color"] {
  width: 100%;
  height: 40px;
  padding: 0;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  cursor: pointer;
}

.warning {
  color: var(--theme--danger);
  font-weight: 500;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--border-color);
}

.btn-danger {
  background-color: var(--theme--danger);
  color: white;
}

.btn-danger:hover {
  background-color: var(--theme--danger-accent);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-icon {
  padding: 6px;
  border-radius: var(--theme--border-radius);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-icon i {
  font-size: 18px;
  margin-right: 0;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 16px 20px;
  border-top: 1px solid var(--theme--border-color);
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--theme--primary);
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.form-group.half {
  flex: 1;
}
</style>
