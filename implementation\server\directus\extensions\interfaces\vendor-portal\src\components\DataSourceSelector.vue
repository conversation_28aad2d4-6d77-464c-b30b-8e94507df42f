<template>
  <div class="data-source-selector">
    <div class="search-container">
      <div class="search-input">
        <i class="material-icons">search</i>
        <input 
          type="text" 
          v-model="searchQuery" 
          placeholder="Search data sources..." 
          @input="filterDataSources"
        >
      </div>
    </div>

    <div class="data-sources-grid">
      <div 
        v-for="source in filteredDataSources" 
        :key="source.id"
        class="data-source-card"
        :class="{ 'selected': selectedSource === source.id }"
        @click="selectDataSource(source.id)"
      >
        <div class="data-source-icon">
          <i class="material-icons">{{ getSourceIcon(source.id) }}</i>
        </div>
        <div class="data-source-info">
          <div class="data-source-name">{{ source.name }}</div>
          <div class="data-source-description">{{ source.description }}</div>
        </div>
        <div class="data-source-selection">
          <i v-if="selectedSource === source.id" class="material-icons">check_circle</i>
          <i v-else class="material-icons">radio_button_unchecked</i>
        </div>
      </div>
    </div>

    <div v-if="filteredDataSources.length === 0" class="no-results">
      <i class="material-icons">search_off</i>
      <p>No data sources found matching "{{ searchQuery }}"</p>
    </div>

    <div v-if="selectedSource" class="data-source-details">
      <h4>{{ getSelectedSourceName() }} Details</h4>
      <div class="details-content">
        <div class="detail-item">
          <div class="detail-label">Available Records</div>
          <div class="detail-value">{{ sourceDetails.recordCount || 'Loading...' }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Last Updated</div>
          <div class="detail-value">{{ sourceDetails.lastUpdated || 'Loading...' }}</div>
        </div>
        <div class="detail-item">
          <div class="detail-label">Available Fields</div>
          <div class="detail-value">{{ sourceDetails.fieldCount || 'Loading...' }}</div>
        </div>
      </div>

      <div class="sample-data">
        <h5>Sample Data</h5>
        <div class="sample-table-container">
          <table class="sample-table" v-if="sampleData.length > 0">
            <thead>
              <tr>
                <th v-for="(column, index) in sampleColumns" :key="index">{{ column }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, rowIndex) in sampleData" :key="rowIndex">
                <td v-for="(column, colIndex) in sampleColumns" :key="colIndex">
                  {{ row[column] }}
                </td>
              </tr>
            </tbody>
          </table>
          <div v-else class="loading-sample">
            <div class="spinner-small"></div>
            <span>Loading sample data...</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataSourceSelector',

  props: {
    dataSources: {
      type: Array,
      required: true
    },
    value: {
      type: String,
      default: null
    },
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      selectedSource: this.value,
      searchQuery: '',
      filteredDataSources: [...this.dataSources],
      sourceDetails: {
        recordCount: null,
        lastUpdated: null,
        fieldCount: null
      },
      sampleData: [],
      sampleColumns: []
    };
  },

  watch: {
    value(newValue) {
      this.selectedSource = newValue;
      if (newValue) {
        this.loadSourceDetails(newValue);
      }
    },
    
    dataSources: {
      handler(newSources) {
        this.filteredDataSources = [...newSources];
        this.filterDataSources();
      },
      deep: true
    }
  },

  mounted() {
    // Load details for initially selected source
    if (this.selectedSource) {
      this.loadSourceDetails(this.selectedSource);
    }
  },

  methods: {
    selectDataSource(sourceId) {
      this.selectedSource = sourceId;
      this.loadSourceDetails(sourceId);
      this.$emit('input', sourceId);
      this.$emit('change', sourceId);
    },
    
    filterDataSources() {
      if (!this.searchQuery) {
        this.filteredDataSources = [...this.dataSources];
        return;
      }
      
      const query = this.searchQuery.toLowerCase();
      this.filteredDataSources = this.dataSources.filter(source => 
        source.name.toLowerCase().includes(query) || 
        source.description.toLowerCase().includes(query)
      );
    },
    
    getSourceIcon(sourceId) {
      const iconMap = {
        showrooms: 'store',
        products: 'inventory_2',
        visitors: 'people',
        conversions: 'shopping_cart',
        clients: 'person',
        orders: 'receipt',
        interactions: 'touch_app'
      };
      
      return iconMap[sourceId] || 'table_chart';
    },
    
    getSelectedSourceName() {
      const source = this.dataSources.find(s => s.id === this.selectedSource);
      return source ? source.name : '';
    },
    
    async loadSourceDetails(sourceId) {
      // Reset details and sample data
      this.sourceDetails = {
        recordCount: null,
        lastUpdated: null,
        fieldCount: null
      };
      this.sampleData = [];
      this.sampleColumns = [];
      
      try {
        // This will be replaced with actual API call
        // const response = await fetch(`/api/analytics/data-sources/${sourceId}/details?vendor_id=${this.vendorId}`);
        // const data = await response.json();
        
        // Mock data for now
        setTimeout(() => {
          // Mock source details
          this.sourceDetails = {
            recordCount: Math.floor(Math.random() * 10000) + 1000,
            lastUpdated: new Date().toLocaleString(),
            fieldCount: Math.floor(Math.random() * 20) + 5
          };
          
          // Mock sample data
          const sampleSize = 5;
          const mockData = [];
          
          // Create columns based on data source
          let columns = [];
          if (sourceId === 'showrooms') {
            columns = ['id', 'name', 'visits', 'unique_visitors', 'avg_duration'];
          } else if (sourceId === 'products') {
            columns = ['id', 'name', 'category', 'views', 'interactions'];
          } else if (sourceId === 'visitors') {
            columns = ['id', 'session_id', 'device', 'location', 'duration'];
          } else if (sourceId === 'conversions') {
            columns = ['id', 'user_id', 'product_id', 'type', 'timestamp'];
          } else {
            columns = ['id', 'name', 'value', 'date', 'status'];
          }
          
          this.sampleColumns = columns;
          
          // Create sample rows
          for (let i = 0; i < sampleSize; i++) {
            const row = {};
            
            columns.forEach(column => {
              if (column === 'id') {
                row[column] = `${i + 1}`;
              } else if (column === 'name') {
                row[column] = `Sample ${sourceId.charAt(0).toUpperCase() + sourceId.slice(1, -1)} ${i + 1}`;
              } else if (column.includes('duration')) {
                row[column] = `${Math.floor(Math.random() * 300) + 30}s`;
              } else if (column.includes('visits') || column.includes('views') || column.includes('interactions')) {
                row[column] = Math.floor(Math.random() * 1000) + 100;
              } else if (column === 'category') {
                const categories = ['Furniture', 'Lighting', 'Decor', 'Outdoor', 'Office'];
                row[column] = categories[Math.floor(Math.random() * categories.length)];
              } else if (column === 'device') {
                const devices = ['Desktop', 'Mobile', 'Tablet', 'VR Headset'];
                row[column] = devices[Math.floor(Math.random() * devices.length)];
              } else if (column === 'location') {
                const locations = ['US', 'UK', 'CA', 'AU', 'DE', 'FR'];
                row[column] = locations[Math.floor(Math.random() * locations.length)];
              } else if (column === 'type') {
                const types = ['View', 'Click', 'Add to Cart', 'Purchase'];
                row[column] = types[Math.floor(Math.random() * types.length)];
              } else if (column === 'timestamp' || column === 'date') {
                const date = new Date();
                date.setDate(date.getDate() - Math.floor(Math.random() * 30));
                row[column] = date.toLocaleDateString();
              } else if (column === 'status') {
                const statuses = ['Active', 'Pending', 'Completed', 'Cancelled'];
                row[column] = statuses[Math.floor(Math.random() * statuses.length)];
              } else {
                row[column] = `Value ${i + 1}`;
              }
            });
            
            mockData.push(row);
          }
          
          this.sampleData = mockData;
        }, 1000);
      } catch (error) {
        console.error('Error loading source details:', error);
      }
    }
  }
};
</script>

<style scoped>
.data-source-selector {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.search-container {
  margin-bottom: 10px;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 8px 12px;
}

.search-input i {
  color: var(--theme--foreground-subdued);
  margin-right: 8px;
}

.search-input input {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--theme--foreground);
  font-size: 14px;
  outline: none;
}

.data-sources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.data-source-card {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  cursor: pointer;
  transition: all 0.2s;
}

.data-source-card:hover {
  border-color: var(--theme--primary);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-source-card.selected {
  background-color: var(--theme--primary-background);
  border-color: var(--theme--primary);
}

.data-source-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.data-source-card.selected .data-source-icon {
  background-color: var(--theme--primary);
  color: white;
}

.data-source-info {
  flex: 1;
}

.data-source-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.data-source-description {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.data-source-selection {
  color: var(--theme--primary);
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.no-results i {
  font-size: 48px;
  margin-bottom: 10px;
}

.data-source-details {
  margin-top: 20px;
  padding: 20px;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
}

.details-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.detail-item {
  background-color: var(--theme--background-subdued);
  padding: 10px;
  border-radius: var(--theme--border-radius);
}

.detail-label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 5px;
}

.detail-value {
  font-weight: bold;
}

.sample-data {
  margin-top: 20px;
}

.sample-table-container {
  margin-top: 10px;
  overflow-x: auto;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 10px;
}

.sample-table {
  width: 100%;
  border-collapse: collapse;
}

.sample-table th {
  text-align: left;
  padding: 8px;
  border-bottom: 1px solid var(--theme--border-color);
  font-weight: bold;
  color: var(--theme--foreground-subdued);
}

.sample-table td {
  padding: 8px;
  border-bottom: 1px solid var(--theme--border-color-subdued);
}

.loading-sample {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: var(--theme--foreground-subdued);
}

.spinner-small {
  width: 20px;
  height: 20px;
  border: 2px solid var(--theme--background);
  border-top: 2px solid var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
