{"name": "directus-extension-auth-integration", "version": "1.0.0", "description": "Authentication integration between Directus and Supabase", "main": "index.js", "keywords": ["directus", "directus-extension", "directus-custom-endpoint", "auth-integration"], "directus:extension": {"type": "endpoint", "path": "index.js", "source": "src/index.js", "host": "^10.0.0"}, "scripts": {"build": "directus-extension build"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "bcryptjs": "^2.4.3", "joi": "^17.9.2", "jsonwebtoken": "^9.0.0", "crypto": "^1.0.1"}}