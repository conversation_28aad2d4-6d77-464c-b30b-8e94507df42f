import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { SceneValidatorService } from '../../../services/scene/scene-validator';
import { validateRequest } from '../../middleware/validation';

// Initialize scene validator service
const sceneValidator = new SceneValidatorService(supabase);

/**
 * Validate a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const validateScene = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Validate scene
    const result = await sceneValidator.validateScene(scene_id);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating scene', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Analyze scene performance
 *
 * @param req - Request
 * @param res - Response
 */
export const analyzeScenePerformance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Analyze scene performance
    const result = await sceneValidator.analyzeScenePerformance(scene_id);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error analyzing scene performance', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Check scene compatibility
 *
 * @param req - Request
 * @param res - Response
 */
export const checkSceneCompatibility = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { target_environment } = req.query;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Check scene compatibility
    const result = await sceneValidator.checkSceneCompatibility(
      scene_id,
      target_environment as string,
    );

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error checking scene compatibility', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Get action from query
    const { action } = req.query;

    // Route to appropriate handler
    switch (action) {
      case 'performance':
        await analyzeScenePerformance(req, res);
        break;
      case 'compatibility':
        await checkSceneCompatibility(req, res);
        break;
      default:
        await validateScene(req, res);
        break;
    }
  } catch (error) {
    logger.error('Error in scene validation handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
}
