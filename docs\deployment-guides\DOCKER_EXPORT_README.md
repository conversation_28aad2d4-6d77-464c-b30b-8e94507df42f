# Docker Image Export and Deployment System

## Overview

The MVS-VR v2 project includes a comprehensive Docker image export and deployment system designed to simplify deployments to DigitalOcean and other cloud providers. This system provides multiple deployment strategies to accommodate different network conditions, security requirements, and operational preferences.

## Key Features

- **Multiple Deployment Methods**: Export files, container registry, or hybrid approach
- **Cross-Platform Support**: Scripts for both Linux/macOS (Bash) and Windows (PowerShell)
- **Automated Workflows**: Complete end-to-end deployment automation
- **Comprehensive Validation**: Health checks, prerequisites verification, and rollback capabilities
- **Flexible Configuration**: Customizable version tags, registry settings, and deployment options

## Quick Start

### 1. Basic Image Export
```bash
# Export all images with compression
./scripts/export-docker-images.sh --compress

# Export with custom version
./scripts/export-docker-images.sh --version v1.0.0 --compress
```

### 2. Upload to DigitalOcean
```bash
# Upload exported images
rsync -avz --progress docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/
```

### 3. Deploy on Server
```bash
# SSH to server and deploy
ssh root@YOUR_SERVER_IP
cd /opt/mvs-vr/docker-exports
./deploy-to-digitalocean.sh
```

### 4. Automated Deployment
```bash
# Complete automated deployment
./scripts/deploy-with-images.sh --server-ip YOUR_SERVER_IP
```

## Available Scripts

### Core Export Scripts

| Script | Platform | Purpose |
|--------|----------|---------|
| `export-docker-images.sh` | Linux/macOS | Build, tag, and export Docker images |
| `export-docker-images.ps1` | Windows | PowerShell version of export script |
| `deploy-with-images.sh` | Linux/macOS | Complete automated deployment workflow |

### Generated Deployment Scripts

| Script | Purpose |
|--------|---------|
| `load-images.sh` | Load exported images on target server |
| `deploy-to-digitalocean.sh` | Deploy services using exported images |
| `load-images.ps1` | PowerShell version of image loader |

## Deployment Methods

### Method 1: Export and Upload (Default)

**Best for**: Direct server deployments, offline environments, bandwidth-limited scenarios

```bash
# Export images locally
./scripts/export-docker-images.sh --compress

# Upload to server
rsync -avz docker-exports/ root@SERVER_IP:/opt/mvs-vr/docker-exports/

# Deploy on server
ssh root@SERVER_IP 'cd /opt/mvs-vr/docker-exports && ./deploy-to-digitalocean.sh'
```

**Advantages**:
- Works without external registry
- Complete control over deployment artifacts
- Suitable for air-gapped environments
- Backup and archival capabilities

### Method 2: Container Registry

**Best for**: Production environments, multiple servers, team collaboration

```bash
# Build and push to DigitalOcean Container Registry
./scripts/export-docker-images.sh --push-registry --skip-export

# Deploy from registry on server
docker-compose -f docker-compose.registry.yml up -d
```

**Advantages**:
- Centralized image management
- Version control and rollback
- Faster deployments to multiple servers
- Integrated with DigitalOcean ecosystem

### Method 3: Hybrid Approach

**Best for**: Maximum flexibility and redundancy

```bash
# Export AND push to registry
./scripts/export-docker-images.sh --push-registry --compress

# Deploy using preferred method
./scripts/deploy-with-images.sh --server-ip SERVER_IP --method hybrid
```

**Advantages**:
- Redundancy (local files + registry)
- Flexibility to choose deployment method
- Backup and disaster recovery options

## Configuration Options

### Export Script Options

```bash
./scripts/export-docker-images.sh [OPTIONS]

# Common options:
--version VERSION      # Custom version tag (default: timestamp)
--compress            # Compress exported files (recommended)
--push-registry       # Push to DigitalOcean Container Registry
--skip-build          # Use existing local images
--skip-export         # Only push to registry, don't export files
--cleanup             # Remove local images after export
--dry-run             # Preview actions without execution
--verbose             # Detailed output for debugging
```

### Deployment Script Options

```bash
./scripts/deploy-with-images.sh [OPTIONS]

# Required:
--server-ip IP        # DigitalOcean server IP address

# Common options:
--method METHOD       # export, registry, or hybrid
--version VERSION     # Specific version to deploy
--user USER           # SSH user (default: root)
--remote-path PATH    # Remote deployment path
--skip-tests          # Skip pre-deployment tests
--no-backup           # Skip backup before deployment
--cleanup             # Clean up local images after deployment
```

## File Structure

After running the export script, you'll find:

```
docker-exports/
├── api-gateway-20240101-120000.tar.gz      # Compressed service images
├── asset-service-20240101-120000.tar.gz
├── scene-service-20240101-120000.tar.gz
├── blueprint-service-20240101-120000.tar.gz
├── llm-service-20240101-120000.tar.gz
├── auth-service-20240101-120000.tar.gz
├── analytics-service-20240101-120000.tar.gz
├── monitoring-service-20240101-120000.tar.gz
├── directus-20240101-120000.tar.gz
├── export-manifest.json                    # Metadata and checksums
├── load-images.sh                          # Image loading script
├── load-images.ps1                         # PowerShell image loader
├── deploy-to-digitalocean.sh               # Deployment script
└── UPLOAD_INSTRUCTIONS.md                  # Detailed upload guide
```

## Environment Variables

### For Registry Deployment

```bash
# DigitalOcean Container Registry
export DOCKER_REGISTRY="registry.digitalocean.com/mvs-vr"
export IMAGE_TAG="v1.0.0"

# Deploy using registry
docker-compose -f docker-compose.registry.yml up -d
```

### For Custom Configuration

```bash
# Custom export settings
export VERSION_TAG="v1.0.0"
export REGISTRY_PREFIX="your-registry.com/mvs-vr"
export COMPRESS_EXPORTS="true"

# Run export with environment variables
./scripts/export-docker-images.sh
```

## Integration with CI/CD

### GitHub Actions Example

```yaml
name: Build and Deploy MVS-VR
on:
  push:
    tags: ['v*']

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Export Docker Images
        run: |
          ./scripts/export-docker-images.sh \
            --version ${{ github.ref_name }} \
            --push-registry \
            --compress
      
      - name: Deploy to DigitalOcean
        run: |
          ./scripts/deploy-with-images.sh \
            --server-ip ${{ secrets.SERVER_IP }} \
            --method registry \
            --version ${{ github.ref_name }}
        env:
          DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DO_TOKEN }}
```

### GitLab CI Example

```yaml
stages:
  - build
  - deploy

build-images:
  stage: build
  script:
    - ./scripts/export-docker-images.sh --version $CI_COMMIT_TAG --push-registry
  only:
    - tags

deploy-production:
  stage: deploy
  script:
    - ./scripts/deploy-with-images.sh --server-ip $PROD_SERVER_IP --method registry
  only:
    - tags
  when: manual
```

## Monitoring and Logging

### Export Process Monitoring

The export script provides detailed logging:

```bash
# Enable verbose output
./scripts/export-docker-images.sh --verbose

# Monitor export progress
tail -f /tmp/docker-export.log
```

### Deployment Monitoring

```bash
# Monitor deployment progress
./scripts/deploy-with-images.sh --server-ip SERVER_IP --verbose

# Check service health after deployment
ssh root@SERVER_IP 'docker-compose ps && docker-compose logs --tail=50'
```

## Troubleshooting

### Common Issues

#### Large File Upload Problems
```bash
# Use rsync with resume capability
rsync -avz --progress --partial docker-exports/ root@SERVER:/opt/mvs-vr/docker-exports/

# Split large files if needed
split -b 1G large-image.tar.gz image-part-
```

#### Registry Authentication Issues
```bash
# Re-authenticate with DigitalOcean
doctl auth init
doctl registry login

# Verify registry access
docker pull registry.digitalocean.com/mvs-vr/api-gateway:latest
```

#### Deployment Failures
```bash
# Check server logs
ssh root@SERVER_IP 'docker-compose logs'

# Verify image loading
ssh root@SERVER_IP 'docker images | grep mvs-vr-v2'

# Manual deployment
ssh root@SERVER_IP 'cd /opt/mvs-vr && docker-compose up -d'
```

### Performance Optimization

#### Reduce Image Sizes
- Use multi-stage Docker builds
- Remove unnecessary packages and files
- Use Alpine-based images where possible
- Implement .dockerignore files

#### Optimize Upload Speed
```bash
# Use compression (enabled by default)
./scripts/export-docker-images.sh --compress

# Parallel uploads for multiple files
parallel -j4 scp {} root@SERVER:/opt/mvs-vr/docker-exports/ ::: *.tar.gz

# Use DigitalOcean's private networking
rsync -avz --progress docker-exports/ root@PRIVATE_IP:/opt/mvs-vr/docker-exports/
```

## Security Considerations

### Image Security
- Scan images for vulnerabilities before export
- Use minimal base images
- Keep images updated with security patches
- Remove sensitive data from images

### Transfer Security
- Use SSH keys instead of passwords
- Encrypt sensitive data in transit
- Verify image checksums after transfer
- Use secure networks for uploads

### Registry Security
- Use private registries for sensitive applications
- Implement image signing and verification
- Regular security audits of stored images
- Access control and authentication

## Best Practices

### Development Workflow
1. Test locally before export
2. Use semantic versioning for releases
3. Document changes in each export
4. Maintain export archives for rollback

### Production Deployment
1. Always backup before deployment
2. Use blue-green deployment strategies
3. Monitor services after deployment
4. Have rollback procedures ready

### Maintenance
1. Regular cleanup of old images
2. Monitor disk usage on servers
3. Update base images regularly
4. Review and update security settings

## Support and Documentation

For additional help and detailed guides:

- [Docker Image Export Guide](./docker-image-export-guide.md)
- [DigitalOcean Step-by-Step Guide](./digitalocean-step-by-step.md)
- [Deployment Checklist](./deployment-checklist.md)
- [Troubleshooting Guide](./troubleshooting.md)

## Contributing

To contribute improvements to the export and deployment system:

1. Test changes thoroughly in development environment
2. Update documentation for any new features
3. Ensure cross-platform compatibility
4. Add appropriate error handling and logging
5. Submit pull requests with detailed descriptions

## License

This deployment system is part of the MVS-VR v2 project and follows the same licensing terms.
