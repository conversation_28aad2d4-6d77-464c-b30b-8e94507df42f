<template>
  <div class="database-metrics">
    <v-skeleton-loader
      v-if="loading"
      type="card"
      class="mx-auto"
    ></v-skeleton-loader>
    
    <div v-else class="metrics-content">
      <!-- Database Performance -->
      <v-card class="mb-4">
        <v-card-title>Database Performance</v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-timer</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ dbPerformance?.avg_query_execution_time || '0' }} ms</div>
                  <div class="metric-label">Avg Query Execution Time</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="success" size="36">mdi-database-sync</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ dbPerformance?.avg_transaction_rate || '0' }}</div>
                  <div class="metric-label">Transactions Per Second</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="info" size="36">mdi-lan-connect</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ dbPerformance?.avg_active_connections || '0' }} / {{ dbPerformance?.max_connections || '0' }}</div>
                  <div class="metric-label">Active Connections</div>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <div class="period-selector">
                <v-btn-toggle
                  v-model="selectedPeriod"
                  mandatory
                  @change="periodChanged"
                >
                  <v-btn value="1h">1h</v-btn>
                  <v-btn value="6h">6h</v-btn>
                  <v-btn value="24h">24h</v-btn>
                  <v-btn value="7d">7d</v-btn>
                  <v-btn value="30d">30d</v-btn>
                </v-btn-toggle>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <v-tabs v-model="activePerformanceTab" background-color="transparent" grow>
                <v-tab>Query Execution Time</v-tab>
                <v-tab>Transaction Rate</v-tab>
                <v-tab>Connection Pool</v-tab>
              </v-tabs>
              
              <v-tabs-items v-model="activePerformanceTab">
                <v-tab-item>
                  <div class="chart-container">
                    <div class="chart-wrapper">
                      <canvas ref="queryExecutionTimeChart"></canvas>
                    </div>
                  </div>
                </v-tab-item>
                
                <v-tab-item>
                  <div class="chart-container">
                    <div class="chart-wrapper">
                      <canvas ref="transactionRateChart"></canvas>
                    </div>
                  </div>
                </v-tab-item>
                
                <v-tab-item>
                  <div class="chart-container">
                    <div class="chart-wrapper">
                      <canvas ref="connectionPoolChart"></canvas>
                    </div>
                  </div>
                </v-tab-item>
              </v-tabs-items>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      
      <!-- Database Health -->
      <v-card class="mb-4">
        <v-card-title class="d-flex justify-space-between">
          <span>Database Health</span>
          <v-chip
            :color="getStatusColor(dbHealth?.status)"
            text-color="white"
            class="status-chip"
          >
            {{ formatStatus(dbHealth?.status) }}
          </v-chip>
        </v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-clock-outline</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ dbHealth?.uptime || 'N/A' }}</div>
                  <div class="metric-label">Uptime</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-database</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ dbHealth?.version || 'N/A' }}</div>
                  <div class="metric-label">Version</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon :color="getStatusColor(dbHealth?.replication?.status)" size="36">mdi-database-sync</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ dbHealth?.replication?.lag || 'N/A' }}</div>
                  <div class="metric-label">Replication Lag</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="3">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon :color="getBackupStatusColor(dbHealth?.backup?.status)" size="36">mdi-backup-restore</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ formatTimestamp(dbHealth?.backup?.last_backup) }}</div>
                  <div class="metric-label">Last Backup</div>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <h3 class="section-title">Optimization Suggestions</h3>
              <v-alert
                v-if="!dbHealth?.optimization_suggestions || dbHealth.optimization_suggestions.length === 0"
                type="success"
                text
              >
                No optimization suggestions at this time.
              </v-alert>
              <v-list v-else>
                <v-list-item
                  v-for="(suggestion, index) in dbHealth.optimization_suggestions"
                  :key="index"
                  class="suggestion-item"
                >
                  <v-list-item-avatar>
                    <v-icon :color="getSuggestionTypeColor(suggestion.type)">
                      {{ getSuggestionTypeIcon(suggestion.type) }}
                    </v-icon>
                  </v-list-item-avatar>
                  
                  <v-list-item-content>
                    <v-list-item-title>
                      {{ getSuggestionTypeLabel(suggestion.type) }}: {{ suggestion.table }}
                      <span v-if="suggestion.index">.{{ suggestion.index }}</span>
                      <span v-if="suggestion.column">.{{ suggestion.column }}</span>
                    </v-list-item-title>
                    <v-list-item-subtitle>
                      {{ suggestion.recommendation }}
                    </v-list-item-subtitle>
                    <v-list-item-subtitle v-if="suggestion.bloat_percentage">
                      Bloat: {{ suggestion.bloat_percentage }}
                    </v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      
      <!-- Database Tables -->
      <v-card>
        <v-card-title>Database Tables</v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-table</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ dbTables?.total_tables || '0' }}</div>
                  <div class="metric-label">Total Tables</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="6">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-database</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ dbTables?.total_size || '0' }}</div>
                  <div class="metric-label">Total Size</div>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <h3 class="section-title">Table Details</h3>
              <v-data-table
                :headers="tableHeaders"
                :items="dbTables?.tables || []"
                :items-per-page="5"
                class="elevation-1"
              >
                <template v-slot:item.size="{ item }">
                  {{ item.size }}
                </template>
                <template v-slot:item.index_size="{ item }">
                  {{ item.index_size }}
                </template>
                <template v-slot:item.growth_rate="{ item }">
                  {{ item.growth_rate }}
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'DatabaseMetrics',
  
  props: {
    dbPerformance: {
      type: Object,
      default: null
    },
    dbHealth: {
      type: Object,
      default: null
    },
    dbTables: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      selectedPeriod: '24h',
      activePerformanceTab: 0,
      queryExecutionTimeChart: null,
      transactionRateChart: null,
      connectionPoolChart: null,
      tableHeaders: [
        { text: 'Table Name', value: 'name' },
        { text: 'Rows', value: 'rows' },
        { text: 'Size', value: 'size' },
        { text: 'Index Size', value: 'index_size' },
        { text: 'Growth Rate', value: 'growth_rate' }
      ]
    };
  },
  
  watch: {
    dbPerformance() {
      this.$nextTick(() => {
        this.initPerformanceCharts();
      });
    },
    activePerformanceTab() {
      this.$nextTick(() => {
        this.initPerformanceCharts();
      });
    }
  },
  
  mounted() {
    this.$nextTick(() => {
      if (this.dbPerformance) {
        this.initPerformanceCharts();
      }
    });
  },
  
  beforeDestroy() {
    this.destroyCharts();
  },
  
  methods: {
    // Initialize performance charts
    initPerformanceCharts() {
      if (!this.dbPerformance) return;
      
      switch (this.activePerformanceTab) {
        case 0:
          this.initQueryExecutionTimeChart();
          break;
        case 1:
          this.initTransactionRateChart();
          break;
        case 2:
          this.initConnectionPoolChart();
          break;
      }
    },
    
    // Initialize query execution time chart
    initQueryExecutionTimeChart() {
      if (!this.dbPerformance || !this.dbPerformance.query_execution_time_trend) return;
      
      const ctx = this.$refs.queryExecutionTimeChart?.getContext('2d');
      if (!ctx) return;
      
      // Destroy existing chart if it exists
      if (this.queryExecutionTimeChart) {
        this.queryExecutionTimeChart.destroy();
      }
      
      // Prepare data for chart
      const dataset = {
        label: this.dbPerformance.query_execution_time_trend.name,
        data: this.dbPerformance.query_execution_time_trend.data.map(point => point.value),
        borderColor: this.dbPerformance.query_execution_time_trend.color,
        backgroundColor: this.dbPerformance.query_execution_time_trend.color + '20',
        borderWidth: 2,
        tension: 0.4,
        fill: true
      };
      
      // Create chart
      this.queryExecutionTimeChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.dbPerformance.query_execution_time_trend.data.map(point => {
            const date = new Date(point.time);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          }),
          datasets: [dataset]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Execution Time (ms)'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Time'
              }
            }
          }
        }
      });
    },
    
    // Initialize transaction rate chart
    initTransactionRateChart() {
      if (!this.dbPerformance || !this.dbPerformance.transaction_rate_trend) return;
      
      const ctx = this.$refs.transactionRateChart?.getContext('2d');
      if (!ctx) return;
      
      // Destroy existing chart if it exists
      if (this.transactionRateChart) {
        this.transactionRateChart.destroy();
      }
      
      // Prepare data for chart
      const dataset = {
        label: this.dbPerformance.transaction_rate_trend.name,
        data: this.dbPerformance.transaction_rate_trend.data.map(point => point.value),
        borderColor: this.dbPerformance.transaction_rate_trend.color,
        backgroundColor: this.dbPerformance.transaction_rate_trend.color + '20',
        borderWidth: 2,
        tension: 0.4,
        fill: true
      };
      
      // Create chart
      this.transactionRateChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.dbPerformance.transaction_rate_trend.data.map(point => {
            const date = new Date(point.time);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          }),
          datasets: [dataset]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Transactions Per Second'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Time'
              }
            }
          }
        }
      });
    },
    
    // Initialize connection pool chart
    initConnectionPoolChart() {
      if (!this.dbPerformance || !this.dbPerformance.connection_pool_trend) return;
      
      const ctx = this.$refs.connectionPoolChart?.getContext('2d');
      if (!ctx) return;
      
      // Destroy existing chart if it exists
      if (this.connectionPoolChart) {
        this.connectionPoolChart.destroy();
      }
      
      // Prepare data for chart
      const dataset = {
        label: this.dbPerformance.connection_pool_trend.name,
        data: this.dbPerformance.connection_pool_trend.data.map(point => point.value),
        borderColor: this.dbPerformance.connection_pool_trend.color,
        backgroundColor: this.dbPerformance.connection_pool_trend.color + '20',
        borderWidth: 2,
        tension: 0.4,
        fill: true
      };
      
      // Create chart
      this.connectionPoolChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.dbPerformance.connection_pool_trend.data.map(point => {
            const date = new Date(point.time);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          }),
          datasets: [dataset]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Active Connections'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Time'
              }
            }
          }
        }
      });
    },
    
    // Destroy all charts
    destroyCharts() {
      if (this.queryExecutionTimeChart) {
        this.queryExecutionTimeChart.destroy();
      }
      
      if (this.transactionRateChart) {
        this.transactionRateChart.destroy();
      }
      
      if (this.connectionPoolChart) {
        this.connectionPoolChart.destroy();
      }
    },
    
    // Handle period change
    periodChanged() {
      this.$emit('refresh', { period: this.selectedPeriod });
    },
    
    // Format status
    formatStatus(status) {
      if (!status) return 'Unknown';
      return status.charAt(0).toUpperCase() + status.slice(1);
    },
    
    // Get status color
    getStatusColor(status) {
      switch (status) {
        case 'healthy':
          return 'success';
        case 'degraded':
          return 'warning';
        case 'unhealthy':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    // Get backup status color
    getBackupStatusColor(status) {
      switch (status) {
        case 'success':
          return 'success';
        case 'warning':
          return 'warning';
        case 'failed':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    // Format timestamp
    formatTimestamp(timestamp) {
      if (!timestamp) return 'N/A';
      
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      
      if (diffMins < 60) {
        return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
      } else if (diffMins < 1440) {
        const hours = Math.floor(diffMins / 60);
        return `${hours} hour${hours === 1 ? '' : 's'} ago`;
      } else {
        return date.toLocaleString();
      }
    },
    
    // Get suggestion type color
    getSuggestionTypeColor(type) {
      switch (type) {
        case 'unused_index':
          return 'warning';
        case 'missing_index':
          return 'info';
        case 'bloated_table':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    // Get suggestion type icon
    getSuggestionTypeIcon(type) {
      switch (type) {
        case 'unused_index':
          return 'mdi-database-remove';
        case 'missing_index':
          return 'mdi-database-plus';
        case 'bloated_table':
          return 'mdi-database-alert';
        default:
          return 'mdi-database';
      }
    },
    
    // Get suggestion type label
    getSuggestionTypeLabel(type) {
      switch (type) {
        case 'unused_index':
          return 'Unused Index';
        case 'missing_index':
          return 'Missing Index';
        case 'bloated_table':
          return 'Bloated Table';
        default:
          return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      }
    }
  }
};
</script>

<style scoped>
.database-metrics {
  padding: 16px;
}

.metrics-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  height: 100%;
}

.metric-icon {
  margin-right: 16px;
}

.metric-details {
  flex: 1;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 4px;
}

.metric-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.period-selector {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.chart-container {
  margin-top: 16px;
}

.chart-wrapper {
  height: 300px;
}

.section-title {
  margin-bottom: 16px;
  font-size: 1.1rem;
  font-weight: 500;
}

.status-chip {
  font-weight: bold;
}

.suggestion-item {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.suggestion-item:last-child {
  border-bottom: none;
}
</style>
