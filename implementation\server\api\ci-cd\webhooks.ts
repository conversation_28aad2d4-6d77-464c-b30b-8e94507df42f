/**
 * CI/CD Webhook Endpoints
 *
 * This file defines the API endpoints for CI/CD webhook integration.
 */

import { Request, Response } from 'express';
import { createHmac } from 'crypto';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { CiCdIntegrationService } from '../../services/ci-cd/ci-cd-integration';
import { ScenePhaseManagerService } from '../../services/scene/scene-phase-manager';

// Initialize services
const ciCdService = new CiCdIntegrationService(supabase);
const phaseManager = new ScenePhaseManagerService(supabase);

/**
 * Validate GitHub webhook signature
 *
 * @param req - Request
 * @param secret - Webhook secret
 * @returns Is valid
 */
function validateGitHubSignature(req: Request, secret: string): boolean {
  const signature = req.headers['x-hub-signature-256'] as string;

  if (!signature) {
    return false;
  }

  const hmac = createHmac('sha256', secret);
  const digest = 'sha256=' + hmac.update(JSON.stringify(req.body)).digest('hex');

  return signature === digest;
}

/**
 * Handle GitHub Actions webhook
 *
 * @param req - Request
 * @param res - Response
 */
export const handleGitHubActionsWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get webhook secret from environment
    const webhookSecret = process.env.GITHUB_WEBHOOK_SECRET;

    // Validate signature if secret is provided
    if (webhookSecret && !validateGitHubSignature(req, webhookSecret)) {
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_SIGNATURE',
          message: 'Invalid webhook signature',
        },
      });
      return;
    }

    // Get event type
    const event = req.headers['x-github-event'] as string;

    // Handle workflow run completed event
    if (event === 'workflow_run' && req.body.action === 'completed') {
      const { workflow_run } = req.body;

      // Extract scene ID from workflow inputs
      const sceneId = workflow_run.inputs?.scene_id;

      if (!sceneId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_SCENE_ID',
            message: 'Scene ID not found in workflow inputs',
          },
        });
        return;
      }

      // Get current phase state
      const phaseState = await phaseManager.getScenePhaseState(sceneId);

      // Record validation result
      await phaseManager.recordValidationResult(
        sceneId,
        phaseState.current_phase,
        'ci_cd_validation',
        {
          valid: workflow_run.conclusion === 'success',
          details: {
            run_id: workflow_run.id,
            run_url: workflow_run.html_url,
            conclusion: workflow_run.conclusion,
            created_at: workflow_run.created_at,
            updated_at: workflow_run.updated_at,
          },
        },
      );

      // Auto-transition to next phase if validation passed and auto-transition is enabled
      if (workflow_run.conclusion === 'success') {
        const phaseConfig = await phaseManager.getPhaseConfig(phaseState.current_phase);

        if (phaseConfig.autoTransition) {
          await phaseManager.transitionToNextPhase(sceneId);
        }
      }

      res.status(200).json({
        success: true,
        message: 'Webhook processed successfully',
      });
    } else {
      // Ignore other events
      res.status(200).json({
        success: true,
        message: 'Event ignored',
      });
    }
  } catch (error) {
    logger.error('Error handling GitHub Actions webhook', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Handle Jenkins webhook
 *
 * @param req - Request
 * @param res - Response
 */
export const handleJenkinsWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    // Implementation for Jenkins webhook
    res.status(200).json({
      success: true,
      message: 'Jenkins webhook not implemented',
    });
  } catch (error) {
    logger.error('Error handling Jenkins webhook', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Handle GitLab CI webhook
 *
 * @param req - Request
 * @param res - Response
 */
export const handleGitLabCIWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    // Implementation for GitLab CI webhook
    res.status(200).json({
      success: true,
      message: 'GitLab CI webhook not implemented',
    });
  } catch (error) {
    logger.error('Error handling GitLab CI webhook', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Handle Azure DevOps webhook
 *
 * @param req - Request
 * @param res - Response
 */
export const handleAzureDevOpsWebhook = async (req: Request, res: Response): Promise<void> => {
  try {
    // Implementation for Azure DevOps webhook
    res.status(200).json({
      success: true,
      message: 'Azure DevOps webhook not implemented',
    });
  } catch (error) {
    logger.error('Error handling Azure DevOps webhook', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on provider
    const { provider } = req.params;

    switch (provider) {
      case 'github':
        await handleGitHubActionsWebhook(req, res);
        break;
      case 'jenkins':
        await handleJenkinsWebhook(req, res);
        break;
      case 'gitlab':
        await handleGitLabCIWebhook(req, res);
        break;
      case 'azure':
        await handleAzureDevOpsWebhook(req, res);
        break;
      default:
        res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_PROVIDER',
            message: 'Invalid CI/CD provider',
          },
        });
    }
  } catch (error) {
    logger.error('Error in webhook handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
}
