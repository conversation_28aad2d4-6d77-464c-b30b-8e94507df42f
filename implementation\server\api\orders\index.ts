/**
 * Order Management API
 *
 * This module provides API endpoints for order management.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase, executeSupabaseQuery } from '../../shared/utils/supabase-client';
import { validateRequest } from '../middleware/validation';
import { authenticateRequest } from '../middleware/auth';
import {
  errorMiddleware,
  notFoundError,
  badRequestError,
  unauthorizedError,
} from '../middleware/error';
import { Logger } from '../../services/integration/logger';

// Create logger
const logger = new Logger();

/**
 * Create order handler
 *
 * @param req Request
 * @param res Response
 */
async function createOrder(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('createOrder')(req, res, async () => {
        const { user_id, vendor_id, product_id, quantity, metadata } = req.body;

        // Check if user is authorized to create order
        if (req.user?.id !== user_id && req.user?.role !== 'admin') {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to create orders for this user',
            },
          });
          return;
        }

        // Check if product exists and is published
        const { data: productData, error: productError } = await supabase
          .from('products')
          .select('*')
          .eq('id', product_id)
          .eq('vendor_id', vendor_id)
          .single();

        if (productError || !productData) {
          logger.error('Error getting product for order', { error: productError });
          res.status(404).json({
            success: false,
            error: {
              code: 'PRODUCT_NOT_FOUND',
              message: 'Product not found or does not belong to the specified vendor',
            },
          });
          return;
        }

        if (productData.status !== 'published') {
          res.status(400).json({
            success: false,
            error: {
              code: 'PRODUCT_NOT_AVAILABLE',
              message: 'Product is not available for purchase',
            },
          });
          return;
        }

        // Create order
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .insert({
            user_id,
            vendor_id,
            product_id,
            quantity,
            metadata: metadata || {},
            status: 'pending',
          })
          .select()
          .single();

        if (orderError) {
          logger.error('Error creating order', { error: orderError });
          res.status(500).json({
            success: false,
            error: {
              code: 'ORDER_CREATION_ERROR',
              message: 'Failed to create order',
            },
          });
          return;
        }

        // Return success response
        res.status(201).json({
          success: true,
          data: orderData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Get orders handler
 *
 * @param req Request
 * @param res Response
 */
async function getOrders(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      const { user_id, vendor_id, product_id, status } = req.query;

      // Build query
      let query = supabase.from('orders').select('*');

      // Filter by user ID if provided
      if (user_id) {
        // Check if user is authorized to view orders for this user
        if (req.user?.id !== user_id && req.user?.role !== 'admin') {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to view orders for this user',
            },
          });
          return;
        }

        query = query.eq('user_id', user_id);
      } else if (vendor_id) {
        // Check if user is authorized to view orders for this vendor
        const { data: vendorData, error: vendorError } = await supabase
          .from('vendors')
          .select('user_id')
          .eq('id', vendor_id)
          .single();

        if (vendorError) {
          logger.error('Error getting vendor for authorization check', { error: vendorError });
          res.status(404).json({
            success: false,
            error: {
              code: 'VENDOR_NOT_FOUND',
              message: 'Vendor not found',
            },
          });
          return;
        }

        if (req.user?.id !== vendorData.user_id && req.user?.role !== 'admin') {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to view orders for this vendor',
            },
          });
          return;
        }

        query = query.eq('vendor_id', vendor_id);
      } else {
        // If no filters provided, only show orders for the authenticated user
        if (req.user?.role !== 'admin') {
          // Check if user is a vendor
          const { data: vendorData, error: vendorError } = await supabase
            .from('vendors')
            .select('id')
            .eq('user_id', req.user?.id)
            .single();

          if (!vendorError && vendorData) {
            // User is a vendor, show orders for their vendor ID
            query = query.eq('vendor_id', vendorData.id);
          } else {
            // User is a regular user, show their orders
            query = query.eq('user_id', req.user?.id);
          }
        }
      }

      // Filter by product ID if provided
      if (product_id) {
        query = query.eq('product_id', product_id);
      }

      // Filter by status if provided
      if (status) {
        query = query.eq('status', status);
      }

      // Execute query
      const { data: ordersData, error: ordersError } = await query;

      if (ordersError) {
        logger.error('Error getting orders', { error: ordersError });
        res.status(500).json({
          success: false,
          error: {
            code: 'QUERY_ERROR',
            message: 'Failed to get orders',
          },
        });
        return;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: ordersData,
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Handler for order API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'POST':
      await createOrder(req, res);
      break;
    case 'GET':
      await getOrders(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
