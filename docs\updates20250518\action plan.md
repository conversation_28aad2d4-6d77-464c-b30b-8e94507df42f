# Kanousei VR Platform – Detailed Action Plan

## Project Overview

A web-based VR platform where Admins and Vendors import IFC/STEP architectural shells and glTF product assets, map proxies, and deploy highly interactive, AI-augmented environments via a plugin-driven runtime.

---

## Objectives

* End-to-end asset pipeline for products and architecture
* Modular interactivity via blueprint tags and triggers
* Unified admin/vendor tooling for configuration and QA
* Seamless VR runtime initialization and updates
* Robust security, performance, and analytics

---

## Detailed Task Breakdown

### Task 1: Plugin Bootstrap System

**Purpose:** Automate download, validation, and instantiation of scene bundles in VR.

* **Area: VR Runtime**

  * **Description:** Bootstrap logic runs at VR startup to fetch and cache manifests and assets.
  * **Subtasks:**

    1. Design `InitBootstrap()` API signature and lifecycle hooks
    2. Implement manifest loader parsing `bundle.manifest.json`
    3. Develop local caching (hash-folder structure) and retry logic
    4. Create error-reporting hooks to Admin backend for failures

### Task 2: Scene Flow Manager

**Purpose:** Manage hierarchical navigation (Location → Exhibition → Space).

* **Area: Administration Backend**

  * **Description:** Define and store scene flow configurations.
  * **Subtasks:**

    1. Design `scene.flow.json` schema (nodes, options, conditions)
    2. Build Admin UI to create and edit flows (drag‑and‑drop nodes)
    3. Integrate flow validation before saving
* **Area: VR Runtime**

  * **Description:** Client reads flow config and routes scenes.
  * **Subtasks:**

    1. Implement scene router to load the correct space prefab
    2. Handle entry/exit triggers and conditional branches
    3. Provide callback hooks for flow events (e.g., `onEnter`, `onExit`)

### Task 3: Fallback Asset Loader

**Purpose:** Ensure scene continuity when assets fail.

* **Area: VR Runtime**

  * **Description:** Detect missing assets at load time and apply defaults.
  * **Subtasks:**

    1. Develop asset health-check on manifest download
    2. Implement fallback logic (e.g., load `DefaultAsset.glb`)
    3. Log incidents to backend with asset ID and scene context
    4. Expose a warning UI overlay in VR for QA mode

### Task 4: Product Asset Import Workflow (glTF)

**Purpose:** Streamline import and QA of small, interactive product assets.

* **Area: Administration Backend**

  * **Description:** API and UI for asset upload, metadata tagging, and QA review.
  * **Subtasks:**

    1. Build `POST /admin/assets` multipart endpoint
    2. Automate QA checks: file size, polycount ≤100k, texture resolution
    3. Store metadata JSON and initiate review queue
* **Area: Vendor System**

  * **Description:** Vendor-facing portal for asset submission and status tracking.
  * **Subtasks:**

    1. Implement vendor upload form with preset filters
    2. Display QA status (pending, approved, revisions needed)
    3. Allow metadata corrections and re-submission

### Task 5: IFC/STEP Shell Import Pipeline

**Purpose:** Convert large BIM models (IFC/STEP) into browser/VR‑ready format.

* **Area: Administration Backend**

  * **Description:** Server-side conversion service using IfcOpenShell, supporting both IFC and STEP.
  * **Subtasks:**

    1. Integrate IfcOpenShell (Python) to parse IFC and STEP inputs
    2. Develop glTF exporter with companion JSON for metadata
    3. Optimize geometry: decimation, texture atlas generation
    4. Store converted files in S3 under versioned paths
* **Area: VR Runtime**

  * **Description:** Load converted glTF shells into the viewer with discipline toggles.
  * **Subtasks:**

    1. Use IFC.js or xeoKit to render shell geometry
    2. Implement UI controls for layer/discipline visibility
    3. Support Level‑of‑Detail selection per discipline group

### Task 6: Dynamic Proxy Mapping System

**Purpose:** Replace architectural placeholders with interactive product assets.

* **Area: Administration Backend**

  * **Description:** UI for mapping IFC element IDs to product assets.
  * **Subtasks:**

    1. Create Proxy Mapping dashboard listing unmatched elements
    2. Search and assign `assetId` to each IFC element
    3. Save mappings to `scene.proxies.json`
* **Area: Vendor System**

  * **Description:** Allow vendors to propose proxy assignments within their portal.
  * **Subtasks:**

    1. Vendor UI to request new product asset for unmapped elements
    2. Queue creation of product asset requests in backend
* **Area: VR Runtime**

  * **Description:** Replace shell nodes with product glTF at runtime.
  * **Subtasks:**

    1. Implement `replaceNode(elementId, assetId)` logic
    2. Apply correct transform, material, and interaction tags
    3. Fallback to proxy if asset not yet available

### Task 7: Interactive Tagging & Blueprint Injection

**Purpose:** Enable asset behaviors based on tagged parts.

* **Area: Vendor System**

  * **Description:** UI to assign interaction tags to mesh subgroups.
  * **Subtasks:**

    1. Develop tagging modal with list of available tags
    2. Visual selector in 3D canvas for sub‑mesh assignment
    3. Save tags to `asset.metadata.json`
* **Area: VR Runtime**

  * **Description:** Instantiate blueprint behaviors per tag.
  * **Subtasks:**

    1. Blueprint engine reads tags at object load
    2. Attach event listeners (e.g., onTap, onHover)
    3. Execute behavior graphs (animations, state changes)

### Task 8: Lighting & Switch System Integration

**Purpose:** Provide dynamic, user‑controlled lighting in VR.

* **Area: Vendor System**

  * **Description:** UI for attaching lights and selecting switch types.
  * **Subtasks:**

    1. Light creation form (type, intensity, color)
    2. Switch selection dropdown (+ modal for new switch definition)
    3. Save linkages to metadata
* **Area: VR Runtime**

  * **Description:** Load light prefabs and bind switch interactions.
  * **Subtasks:**

    1. Instantiate `Light` components per config
    2. Implement switch logic (toggle, dim, trigger)
    3. Enable trigger‑switch events (e.g., fridge door open)

### Task 9: Master Material & Procedural Asset Builder

**Purpose:** Centralize material logic and automate simple asset creation.

* **Area: Administration Backend**

  * **Description:** Master Material library management UI.
  * **Subtasks:**

    1. CRUD interface for shader templates (Master Materials)
    2. Tagging of presets by category (wood, metal, glass)
* **Area: Vendor System**

  * **Description:** Procedural asset request and editing UI.
  * **Subtasks:**

    1. AssetBuilder panel (type, style, dimensions)
    2. Material slot editor with preset selection
    3. Export generated glTF + metadata for review
* **Area: VR Runtime**

  * **Description:** Load procedural assets with correct materials.
  * **Subtasks:**

    1. Apply instance parameters to Master Materials
    2. Sync material overrides at load time

### Task 10: Spatial Audio Logic Implementation

**Purpose:** Immersive audio tied to rooms and interactive objects.

* **Area: Administration Backend**

  * **Description:** Ambient and object audio upload & config UI.
  * **Subtasks:**

    1. Upload audio files (<5 MB, OGG/MP3)
    2. Assign audio to rooms or objects with distance falloff
* **Area: VR Runtime**

  * **Description:** 3D audio source instantiation and management.
  * **Subtasks:**

    1. Position audio sources based on metadata
    2. Implement pooling and culling for performance
    3. Support real-time audio parameter changes

### Task 11: AI Memory & Personalized Feed

**Purpose:** Context-aware user memory and tailored product suggestions.

* **Area: Administration Backend**

  * **Description:** Memory policy and feed config UI.
  * **Subtasks:**

    1. Toggle session vs persistent memory modes
    2. Define feed weighting factors and filters
* **Area: End-User Web Portal**

  * **Description:** Display carousel or grid of recommended products.
  * **Subtasks:**

    1. Fetch `/api/recommendations?userId=`
    2. Render in VR-compatible UI component
    3. Handle opt‑in/out preference changes
* **Area: VR Runtime**

  * **Description:** Inject feed into lobby or vendor spaces.
  * **Subtasks:**

    1. Load recommended product proxies
    2. Animate entry and selection interactions

### Task 12: Live Avatar Assistance System

**Purpose:** Real-time AI or human support via avatar interface.

* **Area: Administration Backend**

  * **Description:** Avatar persona and support channel config.
  * **Subtasks:**

    1. Configure TTS voice, greeting, command triggers
    2. Set routing for live vendor reps (SLA rules)
* **Area: End-User Web Portal**

  * **Description:** Chat overlay or voice UI for assistance.
  * **Subtasks:**

    1. Implement chat widget with socket connection
    2. Handle fallback from AI to live rep
* **Area: VR Runtime**

  * **Description:** Avatar biome and dialog system.
  * **Subtasks:**

    1. Generate avatar prefab with animations
    2. Integrate voice prompts and response timing

### Task 13: Vendor Onboarding & Preview Mode

**Purpose:** Smooth vendor setup and QA preview before public launch.

* **Area: Administration Backend**

  * **Description:** Define onboarding steps and QA flags.
  * **Subtasks:**

    1. Create step definitions for wizard
    2. Store wizard progress per vendor
* **Area: Vendor System**

  * **Description:** Render onboarding wizard and asset preview panel.
  * **Subtasks:**

    1. Multi-step UI with progress indicators
    2. Embed 3D preview canvas toggling QA mode
* **Area: VR Runtime**

  * **Description:** QA badge overlay and non-logged sessions.
  * **Subtasks:**

    1. Suppress analytics during preview
    2. Display "QA MODE" watermark

### Task 14: Zero-Trust Networking & Encryption

**Purpose:** Protect platform integrity and data privacy.

* **Area: All Systems**

  * **Description:** Enforce secure protocols and access controls.
  * **Subtasks:**

    1. Implement OAuth2/JWT auth on APIs
    2. Configure TLS 1.3 for all endpoints
    3. Encrypt S3 buckets with AES-256 and enforce IAM roles
    4. Integrate audit logs into SIEM

### Task 15: Performance Metrics Enforcement

**Purpose:** Ensure consistent high performance across clients.

* **Area: VR Runtime**

  * **Description:** FPS monitoring and dynamic quality scaling.
  * **Subtasks:**

    1. Integrate frameStats collector
    2. Implement auto-LOD and mesh culling
    3. Provide in-VR performance HUD in QA mode
* **Area: End-User Web Portal**

  * **Description:** Display real-time performance charts.
  * **Subtasks:**

    1. Create dashboard widgets for FPS and memory
    2. Alert thresholds for low-end devices
* **Area: Administration Backend**

  * **Description:** Performance budget config UI.
  * **Subtasks:**

    1. Allow admins to set target FPS and budgets
    2. Store and version performance profiles

### Task 16: Real-Time Analytics & Audit Logging

**Purpose:** Collect usage data and maintain compliance.

* **Area: Administration Backend**

  * **Description:** Analytics dashboards and log management.
  * **Subtasks:**

    1. Build dashboard for product views, flow drop-offs
    2. Implement detailed audit log viewer with filters
    3. Configure periodic export of logs for audits
* **Area: VR Runtime**

  * **Description:** Emit telemetry events.
  * **Subtasks:**

    1. Instrument scene transitions and asset interactions
    2. Batch and send events to backend via WebSocket
* **Area: Vendor System**

  * **Description:** Vendor-specific performance insights.
  * **Subtasks:**

    1. Expose product-level metrics to vendors
    2. Enable CSV/PDF export from portal
