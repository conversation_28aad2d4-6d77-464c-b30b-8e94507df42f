# Smart Deployment Scripts for MVS-VR v2

This document describes the enhanced deployment scripts that support incremental updates, missing file detection, and robust error handling for the MVS-VR v2 staging environment.

## Overview

The smart deployment system has been designed to solve the issue where not all Docker images were properly created, compressed, transferred, extracted, and deployed to the DigitalOcean staging server. The enhanced scripts can:

- **Detect missing files** on the remote server
- **Identify file size mismatches** between local and remote files
- **Perform incremental transfers** (only transfer what's needed)
- **Verify file integrity** after transfer
- **Provide detailed logging** and progress reporting
- **Handle errors gracefully** with backup and rollback capabilities

## Available Scripts

### 1. Enhanced `deploy.sh`
**Location**: `./deploy.sh`
**Purpose**: Simple, enhanced version of the original deployment script

**Features**:
- Compares local files with remote files using the manifest
- Only transfers missing or updated files
- Verifies file sizes after transfer
- Colored output for better readability
- Robust error handling

**Usage**:
```bash
./deploy.sh
```

### 2. Smart Staging Deployment (Bash)
**Location**: `./scripts/smart-deploy-staging.sh`
**Purpose**: Comprehensive deployment script with advanced features

**Features**:
- Dry-run mode to preview changes
- Force transfer option
- Checksum verification for critical files
- Backup creation before deployment
- Service health checking
- Detailed progress reporting
- Verbose logging option

**Usage**:
```bash
# Basic deployment
./scripts/smart-deploy-staging.sh

# Dry run to see what would be transferred
./scripts/smart-deploy-staging.sh --dry-run

# Force transfer all files
./scripts/smart-deploy-staging.sh --force

# Verbose output
./scripts/smart-deploy-staging.sh --verbose

# Deploy to different server
./scripts/smart-deploy-staging.sh --server-ip ************* --server-user ubuntu
```

**Options**:
- `--server-ip IP`: Server IP address (default: **************)
- `--server-user USER`: SSH user (default: root)
- `--ssh-key PATH`: SSH key path (default: /root/.ssh/id_rsa)
- `--target-dir DIR`: Target directory on server (default: /opt/mvs-vr-v2)
- `--force`: Force transfer all files regardless of status
- `--dry-run`: Show what would be done without executing
- `--verbose`: Enable verbose output
- `--skip-verification`: Skip file verification after transfer
- `--help`: Show help message

### 3. Smart Staging Deployment (PowerShell)
**Location**: `./scripts/smart-deploy-staging.ps1`
**Purpose**: Windows-compatible version of the smart deployment script

**Features**:
- Same functionality as the Bash version
- Windows PowerShell compatible
- Human-readable file sizes
- Progress tracking

**Usage**:
```powershell
# Basic deployment
.\scripts\smart-deploy-staging.ps1

# Dry run
.\scripts\smart-deploy-staging.ps1 -DryRun

# Force transfer
.\scripts\smart-deploy-staging.ps1 -Force

# Verbose output
.\scripts\smart-deploy-staging.ps1 -Verbose
```

### 4. Enhanced Docker Exports Deployment
**Location**: `./docker-exports/deploy-to-digitalocean.sh`
**Purpose**: Enhanced version of the local deployment script for use on the server

**Features**:
- Validates all exported image files before loading
- Selective Docker image loading (skips already loaded images)
- File integrity checking
- Service health monitoring
- Support for compressed files (.zip, .gz)

**Usage** (on the server):
```bash
cd /opt/mvs-vr-v2
./deploy-to-digitalocean.sh
```

## How It Works

### File Comparison Logic

1. **Manifest Parsing**: Reads `export-manifest.json` to get expected files and sizes
2. **Remote File Check**: Lists files on the remote server with sizes and timestamps
3. **Comparison**: Compares local vs remote files based on:
   - File existence
   - File size
   - Checksums (for critical files)
4. **Transfer Decision**: Only transfers files that are:
   - Missing on remote
   - Have size mismatches
   - Have checksum mismatches (for critical files)
   - Are forced via command line option

### Transfer Process

1. **Prerequisites Check**: Verifies SSH connectivity, required tools, and file availability
2. **Backup Creation**: Creates timestamped backup of existing files
3. **Incremental Transfer**: Transfers only necessary files with progress tracking
4. **Verification**: Confirms transferred files match expected sizes
5. **Deployment**: Loads Docker images and starts services
6. **Health Check**: Verifies services are running correctly

### Error Handling

- **Connection Failures**: Retries with timeout handling
- **Transfer Failures**: Reports specific file transfer errors
- **Verification Failures**: Identifies corrupted or incomplete transfers
- **Service Failures**: Shows container logs for debugging
- **Backup Recovery**: Maintains backups for rollback if needed

## Environment Variables

You can override default settings using environment variables:

```bash
export SERVER_IP="your-server-ip"
export SERVER_USER="your-username"
export SSH_KEY="/path/to/your/ssh/key"
export TARGET_DIR="/your/target/directory"
export FORCE_TRANSFER="true"
export DRY_RUN="true"
export VERBOSE="true"
```

## Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Verify server IP and SSH key path
   - Check network connectivity
   - Ensure SSH key has correct permissions

2. **File Transfer Failed**
   - Check disk space on remote server
   - Verify file permissions
   - Check network stability

3. **File Verification Failed**
   - Re-export the affected Docker images
   - Check for file corruption during transfer
   - Verify manifest file accuracy

4. **Service Deployment Failed**
   - Check Docker and Docker Compose installation
   - Verify environment file configuration
   - Review service logs for specific errors

### Debug Mode

Enable verbose logging to get detailed information:

```bash
./scripts/smart-deploy-staging.sh --verbose --dry-run
```

This will show:
- Detailed file comparison results
- SSH commands being executed
- Transfer progress information
- Service status details

## Best Practices

1. **Always run dry-run first** to preview changes
2. **Use verbose mode** when troubleshooting
3. **Monitor disk space** on both local and remote systems
4. **Keep backups** of working deployments
5. **Verify service health** after deployment
6. **Check logs** if services fail to start

## Security Considerations

- SSH keys should have restrictive permissions (600)
- Use dedicated deployment user accounts
- Regularly rotate SSH keys
- Monitor deployment logs for suspicious activity
- Keep backup files secure and encrypted

## Performance Optimization

- Use compression for large files
- Transfer during off-peak hours
- Monitor network bandwidth usage
- Consider using rsync for very large deployments
- Implement parallel transfers for multiple files (future enhancement)
