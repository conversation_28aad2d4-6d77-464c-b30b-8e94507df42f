# [ADR-0002] GraphQL Persisted Queries

## Status

Accepted

## Context

Our GraphQL API needs to be optimized for performance, especially for mobile clients and clients with limited bandwidth. GraphQL queries can be verbose, leading to larger request payloads. We need a way to reduce the size of these payloads while maintaining the flexibility of GraphQL.

## Decision

We will implement GraphQL persisted queries using Apollo Server's Automatic Persisted Queries (APQ) feature.

## Consequences

### Positive

- Reduced request payload size, especially for complex queries
- Improved network performance for mobile clients and clients with limited bandwidth
- Reduced server load for parsing and validating queries
- Better caching at the CDN level
- Protection against malicious queries

### Negative

- Additional complexity in the client and server implementation
- Need for client libraries that support persisted queries
- Potential for cache invalidation issues if not properly managed

### Neutral

- Need for a shared cache between multiple server instances
- Different client implementation depending on the framework

## Alternatives Considered

### Alternative 1: Manual Persisted Queries

Manually define a set of allowed queries on the server and assign them IDs.

#### Pros

- Complete control over which queries are allowed
- No need for a shared cache
- Simpler implementation

#### Cons

- Less flexible for evolving requirements
- More maintenance overhead
- Requires manual updates when queries change

### Alternative 2: Query Whitelisting

Only allow pre-approved queries to be executed.

#### Pros

- Better security by preventing arbitrary queries
- No need for client changes

#### Cons

- Less flexible than persisted queries
- More maintenance overhead
- Doesn't reduce request payload size

## Related Decisions

- [ADR-0001] Use of GraphQL for API Layer
- [ADR-0003] GraphQL Schema Design

## Notes

We will implement Automatic Persisted Queries (APQ) using Apollo Server and Apollo Client. The implementation will include:

1. Configuring Apollo Server with the APQ plugin
2. Setting up a Redis cache for storing persisted queries
3. Configuring Apollo Client to use persisted queries
4. Monitoring and metrics for persisted query cache hit rates

Example server configuration:

```javascript
const server = new ApolloServer({
  typeDefs,
  resolvers,
  plugins: [
    ApolloServerPluginLandingPageGraphQLPlayground(),
    ApolloServerPluginCacheControl({
      defaultMaxAge: 60,
      calculateHttpHeaders: true,
    }),
    responseCachePlugin({
      sessionId: requestContext => requestContext.request.http.headers.get('authorization') || null,
    }),
  ],
});
```

Example client configuration:

```javascript
const client = new ApolloClient({
  link: createPersistedQueryLink().concat(httpLink),
  cache: new InMemoryCache(),
});
```
