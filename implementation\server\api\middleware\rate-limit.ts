/**
 * Rate Limiting Middleware
 *
 * This middleware provides tiered rate limiting based on user roles.
 * It uses an in-memory store for development and can be configured to use Redis for production.
 */

import { Request, Response, NextFunction } from 'express';
import rateLimit, { Options, RateLimitRequestHandler } from 'express-rate-limit';
import { Logger } from '../../services/integration/logger';

// Create logger
const logger = new Logger();

// Define rate limit tiers
export enum RateLimitTier {
  FREE = 'free',
  BASIC = 'basic',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise',
  ADMIN = 'admin',
}

// Define rate limit configurations for each tier
const rateLimitConfigs: Record<RateLimitTier, Options> = {
  [RateLimitTier.FREE]: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per 15 minutes
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Rate limit exceeded. Please try again later.',
        tier: RateLimitTier.FREE,
      },
    },
  },
  [RateLimitTier.BASIC]: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 500, // 500 requests per 15 minutes
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Rate limit exceeded. Please try again later.',
        tier: RateLimitTier.BASIC,
      },
    },
  },
  [RateLimitTier.PREMIUM]: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 2000, // 2000 requests per 15 minutes
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Rate limit exceeded. Please try again later.',
        tier: RateLimitTier.PREMIUM,
      },
    },
  },
  [RateLimitTier.ENTERPRISE]: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5000, // 5000 requests per 15 minutes
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Rate limit exceeded. Please try again later.',
        tier: RateLimitTier.ENTERPRISE,
      },
    },
  },
  [RateLimitTier.ADMIN]: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10000, // 10000 requests per 15 minutes
    standardHeaders: true,
    legacyHeaders: false,
    message: {
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Rate limit exceeded. Please try again later.',
        tier: RateLimitTier.ADMIN,
      },
    },
  },
};

// Create rate limiters for each tier
const rateLimiters: Record<RateLimitTier, RateLimitRequestHandler> = {
  [RateLimitTier.FREE]: rateLimit(rateLimitConfigs[RateLimitTier.FREE]),
  [RateLimitTier.BASIC]: rateLimit(rateLimitConfigs[RateLimitTier.BASIC]),
  [RateLimitTier.PREMIUM]: rateLimit(rateLimitConfigs[RateLimitTier.PREMIUM]),
  [RateLimitTier.ENTERPRISE]: rateLimit(rateLimitConfigs[RateLimitTier.ENTERPRISE]),
  [RateLimitTier.ADMIN]: rateLimit(rateLimitConfigs[RateLimitTier.ADMIN]),
};

/**
 * Get the rate limit tier for a user
 *
 * @param req Request
 * @returns Rate limit tier
 */
const getRateLimitTier = (req: Request): RateLimitTier => {
  // Check if the user is authenticated
  if (!req.user) {
    return RateLimitTier.FREE;
  }

  // Get the user's role
  const role = req.user.app_metadata?.role || 'user';

  // Map role to tier
  switch (role) {
    case 'admin':
      return RateLimitTier.ADMIN;
    case 'vendor_enterprise':
      return RateLimitTier.ENTERPRISE;
    case 'vendor_premium':
      return RateLimitTier.PREMIUM;
    case 'vendor_basic':
      return RateLimitTier.BASIC;
    default:
      return RateLimitTier.FREE;
  }
};

/**
 * Tiered rate limiting middleware
 *
 * @param req Request
 * @param res Response
 * @param next Next function
 */
export const tieredRateLimit = (req: Request, res: Response, next: NextFunction): void => {
  // Get the rate limit tier for the user
  const tier = getRateLimitTier(req);

  // Apply the appropriate rate limiter
  rateLimiters[tier](req, res, next);
};

/**
 * Admin rate limit bypass middleware
 *
 * @param req Request
 * @param res Response
 * @param next Next function
 */
export const adminRateLimitBypass = (req: Request, res: Response, next: NextFunction): void => {
  // Check if the user is an admin
  if (req.user?.app_metadata?.role === 'admin') {
    // Bypass rate limiting for admins
    next();
  } else {
    // Apply tiered rate limiting for non-admins
    tieredRateLimit(req, res, next);
  }
};

export default {
  tieredRateLimit,
  adminRateLimitBypass,
  RateLimitTier,
  rateLimitConfigs,
  rateLimiters,
};
