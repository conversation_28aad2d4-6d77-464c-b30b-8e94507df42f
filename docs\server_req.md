## IDE Handover Documentation

### 1. Project Overview

* Full rebuild of VR Showroom and Backend infrastructure.
* Retain microservices: Segmentation, Validation.
* Platforms: Varjo XR4, Meta Quest 3.

### 2. VR Development Requirements

* Zones: Startup Area, Environment Changes, Space Room.
* Gestures: Gun-pose teleportation, pinch, palm-down menu.
* Asset Management: Preload, LRU eviction.
* AI Integration: Supabase Function-driven chat.
* Order & Build System: Real-time updates, order handling.
* Performance: Device-specific targets.
* QA Automation: Gesture sim, unit & integration tests.
* Telemetry: Sentry, defined analytics events.
* Onboarding: Interactive tutorial + state tracking.
* Network Status & Offline UI.
* Error & Recovery Flows.
* Security & offline support.
* Configuration: Directus-managed JSON.

### 3. Vendor Portal Enhancements

* AI Assistant Configuration UI.
* Team & User Management.
* Subscription & Usage Dashboard.
* Showroom Layout Editor.

### 4. Client (Web) Portal

* Web-based Build & Order Dashboard.
* Client AI Chat in Browser.
* Reviews & Ratings.

### 5. Backend Infrastructure Requirements

* Data Layer: Supabase (Auth, RLS, Functions, Realtime).
* CMS & APIs: Directus for vendor management, NGINX API Gateway.
* Auth & Authorization: JWT, RLS policies.
* Billing: Stripe subscriptions, metering.
* Microservices: Existing segmentation & validation.
* Realtime: Supabase & Directus updates.
* Performance: Indexing, scaling.
* Security: GDPR, data encryption.
* CI/CD: Schema linting, tests, Docker.
* Docs: OpenAPI, Postman, setup README.

### 6. Final Architectural Summary

* Modular architecture, low-code driven.
* Detailed error reporting & telemetry.

### QC Checklist

* ✔️ All VR, vendor, client features captured.
* ✔️ Updated for tutorial state, offline, error flows.
* ✔️ Vendor portal UI enhancements included.
* ✔️ Client portal flows included.
* ✔️ Non-functional and performance specs updated. 


# VR Showroom Plugin Requirements & Feature Specification

## 1. Overview

Rebuild the VR Showroom plugin from scratch (keeping only the microservices). Target devices: Varjo XR4 & Meta Quest 3. All interactions driven by data-config and Blueprints with minimal C++.

## 2. Functional Requirements

### A. Core Showroom Modes

1. **Zone Management**

   * Three zones: *Startup Area*, *Environment Changes*, *Space Room*
   * Zones defined by `environment_zones` collection (zone\_key, display\_name, asset\_urls)
2. **Showroom Entry / Exit**

   * `EnterZone(zone_key)` loads assets + level + layout JSON
   * `ExitZone()` returns user to launcher or home scene
3. **Hotspot Teleportation**

   * Up to 8 hotspots per zone (actors tagged “teleport”)
   * One “golden” spawn point (tagged “golden”) whose actor name drives menu label
   * Gestures:

     * **Gun-Pose** (index & thumb extended) → show trajectory via `TrajectoryVisualizer`
     * **Close thumb** → teleport along trajectory
4. **Menu Navigation**

   * **Palm-Down** → reveal faded closed menu
   * **Pinch & Drag** → scroll a horizontal carousel of hotspot icons (max 8)
   * **Pinch Release** → select & teleport to highlighted hotspot
   * **Hand Open** → cancel & close menu
   * Active location’s name floats above your pinch midpoint

### B. Asset Management

1. **Preload & Cache**

   * `AssetCacheManager` downloads all product LODs and zone assets on startup/zone change
   * Settings menu: Preload All, Refresh, Clear Cache
   * LRU eviction with per-user quota, eviction logs to backend
2. **Validation Gate**

   * `ValidationChecker` hides any product with `status != published` or flagged `needs_revision`

### C. AI Assistant Integration

1. “Ask AI” button on any product or via voice
2. Calls Supabase Function to proxy to LLM with vendor’s prompts + guardrails
3. Displays chat UI in VR, highlights recommended products

### D. Orders & Builds

1. “Add to Quote” / “Place Order” form → writes to Supabase `orders` table
2. Real-time order-status updates via WebSockets
3. Build Saving: snapshot scene JSON → Supabase `builds` table → reload on demand

### E. Multi-User (Beta Disabled)

* Code paths present but gated by `bEnableMultiUser = false`
* No peer discovery or replication in beta builds

## 3. VR Client Add-Ons

1. **First-Run Tutorial State**

   * Track `hasSeenTutorial` in Supabase `users` table
   * Show tutorial overlay once; allow “Reset Tutorial” option
2. **Network Status & Offline UI**

   * HUD indicator for connectivity; show “Offline Mode” banner when disconnected
   * Disable live features and queue actions locally
3. **Error & Recovery Flows**

   * Centralized error handling via `UIManager.ShowError(message)`
   * Options to “Retry” operations or send bug reports to telemetry

## 4. Non-Functional Requirements

### A. Performance Budgets

| Metric                | Varjo XR4     | Quest 3       |
| --------------------- | ------------- | ------------- |
| Framerate             | 90 FPS RT     | 72 FPS app    |
| Per-eye Resolution    | 2880×2720     | 2064×2208     |
| GPU Budget            | \~6 ms/frame  | \~11 ms/frame |
| CPU Budget            | <10 ms/frame  | <16 ms/frame  |
| Max Texture Memory    | 4 GB          | 2 GB          |
| Triangle Budget/Asset | 50K–100K tris | 30K–60K tris  |
| LODs                  | 3 levels      | 3 levels      |
| Audio Sources         | 16            | 8             |

### B. Onboarding & Accessibility

* First-run tutorial overlay (gestures, teleport, menu)
* Contextual help bubbles linked from Directus prompts
* Haptics: hover, select, error patterns
* Audio cues: teleport whoosh, menu click, error buzz

### C. QA & Automation

* **Gesture Simulation Harness**: replay prerecorded hand data in Editor
* **Blueprint Unit Tests**: FSM, scrolling, teleport
* **Integration Tests**: Play-In-Editor for teleport/menu/caching

### D. Telemetry & Error Reporting

* Analytics events: AppStartup, GestureEvent, TeleportAttempt, MenuInteraction, AssetPreload, Error
* Sentry integration for exceptions/crashes
* Batch upload telemetry to Supabase or BI tool

### E. Configuration Pipeline

* JSON schemas for `gestures.json`, `states.json`, `transitions.json`, `actions.json`, `ui_elements.json`
* CI linting of JSON against schemas
* Hot-reload via `RefreshConfig` console command

### F. Security & Offline

* Supabase JWT auto-refresh every 45 min, encrypted on-disk cache
* Offline fallback to last-cached config + “Offline Mode” banner

## 5. Architecture & Module Summary

| Module               | Key Responsibilities                                |
| -------------------- | --------------------------------------------------- |
| GestureManager       | Detects high-level gestures & emits events          |
| StateMachine         | Generic FSM driven by `transitions.json`            |
| ActionRunner         | Maps actionNames → Blueprint functions              |
| ShowroomManager      | Zone entry/exit, layout spawning                    |
| TeleportationManager | Handles teleport logic & fade                       |
| TrajectoryVisualizer | Renders teleport arc & hit reticle                  |
| UIManager            | Menu carousel, label, tooltips, onboarding overlays |
| AssetCacheManager    | Preload/refresh/clear cache for products & zones    |
| AIClient             | Executes “Ask AI” via Supabase Functions            |
| OrderClient          | Handles order/leads read/write                      |
| AnalyticsClient      | Batches & sends telemetry                           |
| SubscriptionManager  | Enforces feature flags & rate-limits                |
| NetworkManager       | Multi-user code paths (disabled in beta)            |


# Backend & Microservices Requirements & Feature Specification

## 1. Overview

Rebuild all backend components from scratch while retaining existing microservices. Components include:

* APIs & GraphQL via Directus
* Supabase data layer (Auth, RLS, Functions, Realtime)
* Microservices: 3D Segmentation, Asset Validation
* API Gateway / NGINX: rate-limiting, JWT validation
* Billing & Subscriptions via Stripe
* CI/CD: tests, schema validation

## 2. Functional Requirements

### A. Authentication & Authorization

1. **Supabase Auth** for all user types (SystemAdmins, Vendors, Clients)
2. **Row-Level Security** policies enforcing:

   * `vendor_id` isolation on `products`, `orders`, `builds`
   * `user_id` isolation on `builds`, `orders`
3. **Role Management** in Directus:

   * Super Admin, Workflow Admin, AI Governance Admin, Support Admin
   * Vendor Owner, Vendor Manager, Vendor Editor, Vendor Analyst
   * Client Registered, Client Admin, Client Designer
4. **JWT Lifecycle**: auto-refresh every 45m, revoke on logout

### B. Data Models & Schemas

1. **Supabase Tables**:

   * `users` (id, email, role, metadata)
   * `vendors` (vendor\_id, user\_id, profile, subscription\_id)
   * `products` (product\_id, vendor\_id, metadata, status, validation\_report)
   * `orders` (order\_id, user\_id, vendor\_id, product\_id, quantity, status)
   * `builds` (build\_id, user\_id, layout\_json, created\_at)
   * `subscriptions` (subscription\_id, vendor\_id, plan, start\_date, end\_date)
   * `environment_zones` (zone\_key, display\_name, asset\_urls)
   * `telemetry` (event\_id, event\_name, user\_id, properties, timestamp)
   * `cache_evictions` (eviction\_id, vendor\_id, asset\_id, timestamp)

2. **Directus Collections**:

   * `showroom_layouts`, `interaction_behaviors`, `ui_elements`, `transitions`, `actions`
   * **Vendor Portal Enhancements**:

     * AI Assistant Configuration UI
     * Team & User Management
     * Subscription & Usage Dashboard
     * Showroom Layout Editor
   * **Client (Web) Portal Collections**:

     * build records, order history, ratings, feedback forms

### C. API Endpoints & GraphQL

1. **Public API** (GraphQL/REST) for:

   * `GET products(published=true)`, `GET environment_zones`, `GET showroom_layouts`
   * WebSocket subscribe: products, orders, builds

2. **Protected API** for:

   * `POST /orders`, `GET /orders?vendor_id=me`
   * `POST /builds`, `GET /builds?user_id=me`
   * `POST /ai/chat` (Supabase Function)
   * Webhook endpoints: `SegmentationComplete`, `ValidationComplete`

3. **Admin API** for:

   * Managing `vendors`, `subscriptions`, `roles`
   * Reviewing `products` in `pending_review`

### D. Feature Modules

1. **Subscription & Billing**:

   * Stripe integration: Checkout sessions, webhooks (plan\_change, invoice\_paid)
   * Metered billing for API overages
   * Set up three account types based on best practices
   * Tokens to purchase additional services / features
   * SuperAdmin override controls

2. **Rate-Limiting & Quotas**:

   * NGINX `limit_req` per-tier
   * Usage metering in Redis/InfluxDB

3. **Microservices** (retain existing):

   * 3D Segmentation Service: DBSCAN clustering + glTF segment exports
   * Validation Service: GLTF metadata analysis + pass/fail reports

4. **Realtime & Notifications**:

   * Supabase Realtime for `orders`, `product` status
   * Directus Real-time server for `showroom_layouts`, `ui_elements`
   * Email/SMS alerts via Node.js Functions for `pending_review` → admin

## 3. Non-Functional Requirements

### A. Security & Compliance

* HTTPS/WSS for all transport
* Encrypt sensitive fields (JWT, API keys) at rest
* Audit logs in `audit_logs` table (user\_id, action, target, timestamp)
* GDPR compliance: data purge on account deletion

### B. Performance & Scalability

* Database indexing on `vendor_id`, `status`, `created_at`
* Horizontal autoscaling for Functions
* Cache frequently accessed data (e.g. `environment_zones`) in Redis

### C. CI/CD & Testing

* Database Migration: use Supabase migrations + Git-based backup
* Schema Linting: JSON schemas for Directus config
* Automated Tests:

  * Unit tests for Supabase Functions
  * Integration tests for REST/GraphQL endpoints
  * Load testing for critical APIs (orders, product list)

### D. Documentation & Onboarding

* OpenAPI spec for all endpoints
* Postman collection for QA
* Developer README with setup scripts (Docker Compose for Supabase + Directus)

## 4. Architecture & Module Summary

| Component     | Technology                     | Responsibilities                          |
| ------------- | ------------------------------ | ----------------------------------------- |
| Supabase      | Postgres, Auth, RLS, Functions | Data storage, auth, serverless functions  |
| Directus      | Node.js, Vue, Real-time        | CMS for config-driven UI, vendor portal   |
| API Gateway   | NGINX                          | JWT validation, rate-limiting, CORS       |
| Stripe        | SaaS                           | Subscriptions, metered billing            |
| Microservices | Docker + Node.js/Python        | Segmentation, Validation                  |
| CI/CD         | GitHub Actions, Docker         | Tests, migrations, schema linting, deploy |



# System Admin Development Document

## 1. Overview

`System Admins` control and configure global product features, support vendors, and manage organizational zones. They are distinct from `Vendor Admins`, who manage products and users within a specific vendor account.

There is a special **Super Admin** (single user) who can override and reassign zones and promote/demote other System Admins.

A dedicated admin portal lives under the `/admin` path, separate from the vendor-facing and client-facing portals.

---

## 2. Admin Roles & Hierarchy

| Role            | Scope  | Responsibilities              |
| --------------- | ------ | ----------------------------- |
| **Chief Admin** | Global | - Create/demote System Admins |

```
                                    - Reassign zones between admins  
                                    - Override any zone restriction  
                                    - Full audit log access  |
```

\| **Zone Admin**    | One or more geographic/organizational zones | - Approve vendor registrations in their zone
\- Configure zone-specific product defaults
\- Monitor zone analytics & quotas |
\| **Support Admin** | Global                         | - Triage vendor tickets & validation issues
\- Escalate to Zone or Chief Admin as needed |

---

## 3. System-Admin-Only Tasks

1. **User & Role Management**

   * Create, promote, or deactivate System Admin accounts
   * Only Chief Admin can assign the `chief_admin` role
   * Assign Zone Admins to one or more zones
2. **Zone Configuration**

   * Define new zones (regions) in the system
   * Set zone metadata (currency, locale, product defaults)
   * Reassign vendors or admins between zones (Chief Admin only)
3. **Vendor Onboarding & Approval**

   * Approve or reject vendor registrations within their zone
   * Set initial subscription tiers for new vendors
   * Reset vendor passwords and manage vendor support tickets
4. **Global Feature Toggles**

   * Enable/disable AI assistant features per zone or globally
   * Toggle beta features (multi-user, segmentation, etc.)
   * Configure global performance thresholds and default LOD parameters
5. **Content Moderation & Validation Oversight**

   * View and override `needs_revision` flags on product uploads
   * Bulk-approve or rollback product batches
6. **Audit & Analytics**

   * Access comprehensive audit logs (`users`, `products`, `orders`, `cache_evictions`)
   * Download zone and global analytics reports
7. **Billing & Subscription Management**

   * View and adjust vendor subscription plans and rate limits
   * Issue credits or apply overage charges
8. **System Maintenance**

   * Trigger cache purges or CDN syncs
   * Monitor system health dashboards (API Gateway, Supabase metrics)

---

## 4. Admin Portal (`/admin`)

### A. Authentication & Access

* Uses Supabase Auth with 2FA enforced for System Admins
* Single sign-on ensures separation from vendor and client logins

### B. Key Sections

1. **Dashboard**

   * Overview of zones, active System Admins, vendor counts
   * High-priority alerts (validation backlogs, system errors)
2. **Users & Roles**

   * List of System Admins (filterable by role and zone)
   * Create/Edit user modal to assign roles and zones
3. **Zones**

   * CRUD interface for zones
   * View zone-specific metrics and configurations
4. **Vendors**

   * Pending vendor approvals
   * Vendor search and detail view (profile, subscription, tickets)
5. **Features & Toggles**

   * Global and zone-specific feature flags
   * Beta feature enablement controls
6. **Audit Logs**

   * Searchable logs of all critical actions
   * Export logs as CSV or JSON
7. **Billing**

   * Overview of subscription revenue by zone
   * Manual plan adjustments and invoice management
8. **System Health**

   * API usage dashboards, Supabase metrics, cache health

---

## 5. Development Considerations

* **RBAC & RLS**: Enforce System Admin vs. Vendor Admin vs. Client policies via Supabase RLS and Directus permissions
* **Chief Admin Safeguards**: Demotion of Chief Admin requires two-factor confirmation and audit trail entry
* **API Endpoints**: All `/admin/*` routes protected by JWT and role checks; use a dedicated Admin API group
* **UI Framework**: Build `/admin` portal in Next.js or React Admin, consuming Directus GraphQL + Supabase for data

## 6. Vendor & Client Access Paths

* **Vendor Portal**: Vendors and their teams access the system at the /vendor path. This vendor-facing portal is branded per vendor and isolated by RLS, allowing product management, AI assistant configuration, and analytics.

* **Client Access**: Clients can use the base site or follow a vendor-generated hotlink/QR code that redirects to a branded client portal under the vendor’s subdomain or path. On first visit via QR, clients see a sign-up page to create their account and access saved builds, orders, and the AI chat.

---

*End of System Admin Development Document*
