<template>
  <div class="filter-builder">
    <div class="filter-header">
      <h5>Filter Conditions</h5>
      <div class="filter-actions">
        <button class="btn btn-secondary" @click="addFilterGroup" title="Add Filter Group">
          <i class="material-icons">folder</i> Add Group
        </button>
        <button class="btn btn-primary" @click="addFilter" title="Add Filter">
          <i class="material-icons">add</i> Add Filter
        </button>
      </div>
    </div>

    <div v-if="filters.length === 0" class="no-filters">
      <i class="material-icons">filter_alt</i>
      <p>No filters added yet. Click "Add Filter" to start filtering your data.</p>
    </div>

    <div v-else class="filter-groups">
      <div class="filter-group root-group">
        <div class="group-operator" v-if="filters.length > 1">
          <select v-model="rootOperator">
            <option value="and">AND</option>
            <option value="or">OR</option>
          </select>
        </div>

        <div class="filter-items">
          <div 
            v-for="(filter, index) in filters" 
            :key="filter.id"
            class="filter-item"
            :class="{ 'filter-group-item': filter.type === 'group' }"
          >
            <!-- Filter Group -->
            <div v-if="filter.type === 'group'" class="nested-filter-group">
              <div class="group-header">
                <div class="group-title">
                  <i class="material-icons">folder</i>
                  <span>Filter Group</span>
                </div>
                <div class="group-operator">
                  <select v-model="filter.operator">
                    <option value="and">AND</option>
                    <option value="or">OR</option>
                  </select>
                </div>
                <button class="remove-button" @click="removeFilter(index)" title="Remove Group">
                  <i class="material-icons">close</i>
                </button>
              </div>
              
              <div class="nested-filters">
                <div v-if="filter.filters.length === 0" class="empty-group">
                  <p>Empty group. Add filters to this group.</p>
                  <button class="btn btn-small" @click="addFilterToGroup(filter)">
                    <i class="material-icons">add</i> Add Filter
                  </button>
                </div>
                
                <div 
                  v-else
                  v-for="(nestedFilter, nestedIndex) in filter.filters" 
                  :key="nestedFilter.id"
                  class="nested-filter-item"
                >
                  <FilterCondition
                    :filter="nestedFilter"
                    :fields="availableFields"
                    @update:filter="updateNestedFilter(filter, nestedIndex, $event)"
                    @remove="removeNestedFilter(filter, nestedIndex)"
                  />
                </div>
                
                <div class="group-actions" v-if="filter.filters.length > 0">
                  <button class="btn btn-small" @click="addFilterToGroup(filter)">
                    <i class="material-icons">add</i> Add Filter
                  </button>
                </div>
              </div>
            </div>
            
            <!-- Regular Filter -->
            <FilterCondition
              v-else
              :filter="filter"
              :fields="availableFields"
              @update:filter="updateFilter(index, $event)"
              @remove="removeFilter(index)"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="filter-summary" v-if="filters.length > 0">
      <div class="summary-header">
        <h5>Filter Summary</h5>
        <button class="btn btn-small" @click="toggleSummary">
          {{ showSummary ? 'Hide' : 'Show' }}
        </button>
      </div>
      <div v-if="showSummary" class="summary-content">
        <div class="summary-text">{{ filterSummary }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import FilterCondition from './FilterCondition.vue';
import { v4 as uuidv4 } from 'uuid';

export default {
  name: 'FilterBuilder',
  
  components: {
    FilterCondition
  },

  props: {
    value: {
      type: Array,
      default: () => []
    },
    fields: {
      type: Array,
      required: true
    },
    dataSource: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      filters: this.initializeFilters(),
      rootOperator: 'and',
      showSummary: true
    };
  },

  computed: {
    availableFields() {
      return this.fields.map(field => ({
        id: field.id,
        name: field.name,
        type: field.type || 'string'
      }));
    },
    
    filterSummary() {
      if (this.filters.length === 0) {
        return 'No filters applied';
      }
      
      return this.generateFilterSummary(this.filters, this.rootOperator);
    }
  },

  watch: {
    value: {
      handler(newValue) {
        if (JSON.stringify(this.getFilterValue()) !== JSON.stringify(newValue)) {
          this.filters = this.initializeFilters();
        }
      },
      deep: true
    },
    
    filters: {
      handler() {
        this.emitChange();
      },
      deep: true
    },
    
    rootOperator() {
      this.emitChange();
    }
  },

  methods: {
    initializeFilters() {
      if (!this.value || this.value.length === 0) {
        return [];
      }
      
      // Clone the value to avoid modifying props directly
      const clonedValue = JSON.parse(JSON.stringify(this.value));
      
      // Ensure each filter has an ID
      return this.ensureFilterIds(clonedValue);
    },
    
    ensureFilterIds(filters) {
      return filters.map(filter => {
        const newFilter = { ...filter, id: filter.id || uuidv4() };
        
        if (newFilter.type === 'group' && newFilter.filters) {
          newFilter.filters = this.ensureFilterIds(newFilter.filters);
        }
        
        return newFilter;
      });
    },
    
    addFilter() {
      const newFilter = {
        id: uuidv4(),
        field: this.availableFields.length > 0 ? this.availableFields[0].id : null,
        operator: 'equals',
        value: '',
        type: 'condition'
      };
      
      this.filters.push(newFilter);
    },
    
    addFilterGroup() {
      const newGroup = {
        id: uuidv4(),
        type: 'group',
        operator: 'and',
        filters: []
      };
      
      this.filters.push(newGroup);
    },
    
    addFilterToGroup(group) {
      const newFilter = {
        id: uuidv4(),
        field: this.availableFields.length > 0 ? this.availableFields[0].id : null,
        operator: 'equals',
        value: '',
        type: 'condition'
      };
      
      group.filters.push(newFilter);
    },
    
    updateFilter(index, updatedFilter) {
      this.filters[index] = { ...this.filters[index], ...updatedFilter };
    },
    
    updateNestedFilter(group, index, updatedFilter) {
      group.filters[index] = { ...group.filters[index], ...updatedFilter };
    },
    
    removeFilter(index) {
      this.filters.splice(index, 1);
    },
    
    removeNestedFilter(group, index) {
      group.filters.splice(index, 1);
    },
    
    toggleSummary() {
      this.showSummary = !this.showSummary;
    },
    
    getFilterValue() {
      // Create a clean version of filters without IDs for the output value
      const cleanFilters = this.filters.map(filter => {
        const cleanFilter = { ...filter };
        delete cleanFilter.id;
        
        if (cleanFilter.type === 'group' && cleanFilter.filters) {
          cleanFilter.filters = cleanFilter.filters.map(f => {
            const cf = { ...f };
            delete cf.id;
            return cf;
          });
        }
        
        return cleanFilter;
      });
      
      return cleanFilters;
    },
    
    emitChange() {
      const value = this.getFilterValue();
      this.$emit('input', value);
      this.$emit('change', {
        filters: value,
        operator: this.rootOperator
      });
    },
    
    generateFilterSummary(filters, operator) {
      if (!filters || filters.length === 0) {
        return 'No filters';
      }
      
      const filterStrings = filters.map(filter => {
        if (filter.type === 'group') {
          const groupSummary = this.generateFilterSummary(filter.filters, filter.operator);
          return `(${groupSummary})`;
        } else {
          const field = this.availableFields.find(f => f.id === filter.field);
          const fieldName = field ? field.name : filter.field;
          
          let operatorText = '';
          switch (filter.operator) {
            case 'equals': operatorText = 'equals'; break;
            case 'not_equals': operatorText = 'does not equal'; break;
            case 'contains': operatorText = 'contains'; break;
            case 'not_contains': operatorText = 'does not contain'; break;
            case 'greater_than': operatorText = 'is greater than'; break;
            case 'less_than': operatorText = 'is less than'; break;
            case 'greater_than_equal': operatorText = 'is greater than or equal to'; break;
            case 'less_than_equal': operatorText = 'is less than or equal to'; break;
            case 'is_null': operatorText = 'is empty'; break;
            case 'is_not_null': operatorText = 'is not empty'; break;
            default: operatorText = filter.operator;
          }
          
          if (filter.operator === 'is_null' || filter.operator === 'is_not_null') {
            return `${fieldName} ${operatorText}`;
          }
          
          return `${fieldName} ${operatorText} ${filter.value}`;
        }
      });
      
      return filterStrings.join(` ${operator.toUpperCase()} `);
    }
  }
};
</script>

<style scoped>
.filter-builder {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-header h5 {
  margin: 0;
}

.filter-actions {
  display: flex;
  gap: 10px;
}

.no-filters {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.no-filters i {
  font-size: 48px;
  margin-bottom: 10px;
}

.filter-groups {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.root-group {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 15px;
}

.group-operator {
  margin-bottom: 10px;
}

.group-operator select {
  padding: 5px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-weight: bold;
}

.filter-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-item {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  overflow: hidden;
}

.filter-group-item {
  border: 1px solid var(--theme--primary-subdued);
  background-color: var(--theme--primary-background);
}

.nested-filter-group {
  display: flex;
  flex-direction: column;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: var(--theme--primary-subdued);
  color: var(--theme--primary);
}

.group-title {
  display: flex;
  align-items: center;
  gap: 5px;
  flex: 1;
  font-weight: bold;
}

.remove-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-button:hover {
  color: var(--theme--danger);
}

.nested-filters {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.empty-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 15px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.nested-filter-item {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  overflow: hidden;
}

.group-actions {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.btn-small {
  padding: 5px 10px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.filter-summary {
  margin-top: 10px;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  overflow: hidden;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
}

.summary-header h5 {
  margin: 0;
}

.summary-content {
  padding: 15px;
}

.summary-text {
  font-family: monospace;
  white-space: pre-wrap;
  word-break: break-word;
  background-color: var(--theme--background-subdued);
  padding: 10px;
  border-radius: var(--theme--border-radius);
  font-size: 14px;
}
</style>
