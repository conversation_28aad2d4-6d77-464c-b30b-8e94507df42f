/**
 * Middleware
 *
 * This module exports all middleware.
 */

export { authenticateRequest, authorizeRequest } from './auth.ts';
export { validateRequest } from './validation.ts';
export { errorMiddleware, ApiError } from './error.ts';

// Import and re-export the auth-middleware.js module
import * as authMiddleware from './auth-middleware.js';
export { authMiddleware };

// Import and re-export the api-key-middleware.js module
import * as apiKeyMiddleware from './api-key-middleware.js';
export { apiKeyMiddleware };

// Import and re-export the performance middleware
import { cacheControlMiddleware, withCacheControl } from '../../middleware/cache-control.ts';
export { cacheControlMiddleware, withCacheControl };

// Import and re-export the security middleware
import {
  securityHeadersMiddleware,
  withSecurityHeaders,
} from '../../middleware/security-headers.ts';
export { securityHeadersMiddleware, withSecurityHeaders };
