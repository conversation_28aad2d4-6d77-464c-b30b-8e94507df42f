# Kanousei VR Platform – Detailed Action Plan (Archived: May 18, 2025)

> **Note:** This is an archived version of the action plan. Please refer to the current development plan in the root directory: [DEVELOPMENT_PLAN.md](../DEVELOPMENT_PLAN.md)

## Project Overview

A web-based VR platform where Admins and Vendors import IFC/STEP architectural shells and glTF product assets, map proxies, and deploy highly interactive, AI-augmented environments via a plugin-driven runtime.

---

## Objectives

* End-to-end asset pipeline for products and architecture
* Modular interactivity via blueprint tags and triggers
* Unified admin/vendor tooling for configuration and QA
* Seamless VR runtime initialization and updates
* Robust security, performance, and analytics

---

## Detailed Task Breakdown

### Task 1: Plugin Bootstrap System

**Purpose:** Automate download, validation, and instantiation of scene bundles in VR.

* **Area: VR Runtime**

  * **Description:** Bootstrap logic runs at VR startup to fetch and cache manifests and assets.
  * **Subtasks:**

    1. Design `InitBootstrap()` API signature and lifecycle hooks
    2. Implement manifest loader parsing `bundle.manifest.json`
    3. Develop local caching (hash-folder structure) and retry logic
    4. Create error-reporting hooks to Admin backend for failures

### Task 2: Scene Flow Manager

**Purpose:** Manage hierarchical navigation (Location → Exhibition → Space).

* **Area: Administration Backend**

  * **Description:** Define and store scene flow configurations.
  * **Subtasks:**

    1. Design `scene.flow.json` schema (nodes, options, conditions)
    2. Build Admin UI to create and edit flows (drag‑and‑drop nodes)
    3. Integrate flow validation before saving
* **Area: VR Runtime**

  * **Description:** Client reads flow config and routes scenes.
  * **Subtasks:**

    1. Implement scene router to load the correct space prefab
    2. Handle entry/exit triggers and conditional branches
    3. Provide callback hooks for flow events (e.g., `onEnter`, `onExit`)

### Task 3: Fallback Asset Loader

**Purpose:** Ensure scene continuity when assets fail.

* **Area: VR Runtime**

  * **Description:** Detect missing assets at load time and apply defaults.
  * **Subtasks:**

    1. Develop asset health-check on manifest download
    2. Implement fallback logic (e.g., load `DefaultAsset.glb`)
    3. Log incidents to backend with asset ID and scene context
    4. Expose a warning UI overlay in VR for QA mode

### Task 4: Product Asset Import Workflow (glTF)

**Purpose:** Streamline import and QA of small, interactive product assets.

* **Area: Administration Backend**

  * **Description:** API and UI for asset upload, metadata tagging, and QA review.
  * **Subtasks:**

    1. Build `POST /admin/assets` multipart endpoint
    2. Automate QA checks: file size, polycount ≤100k, texture resolution
    3. Store metadata JSON and initiate review queue
* **Area: Vendor System**

  * **Description:** Vendor-facing portal for asset submission and status tracking.
  * **Subtasks:**

    1. Implement vendor upload form with preset filters
    2. Display QA status (pending, approved, revisions needed)
    3. Allow metadata corrections and re-submission

### Task 5: IFC/STEP Shell Import Pipeline

**Purpose:** Convert large BIM models (IFC/STEP) into browser/VR‑ready format.

* **Area: Administration Backend**

  * **Description:** Server-side conversion service using IfcOpenShell, supporting both IFC and STEP.
  * **Subtasks:**

    1. Integrate IfcOpenShell (Python) to parse IFC and STEP inputs
    2. Develop glTF exporter with companion JSON for metadata
    3. Optimize geometry: decimation, texture atlas generation
    4. Store converted files in S3 under versioned paths
* **Area: VR Runtime**

  * **Description:** Load converted glTF shells into the viewer with discipline toggles.
  * **Subtasks:**

    1. Use IFC.js or xeoKit to render shell geometry
    2. Implement UI controls for layer/discipline visibility
    3. Support Level‑of‑Detail selection per discipline group
