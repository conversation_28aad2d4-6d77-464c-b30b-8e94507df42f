import React, { useState, useEffect, useRef } from 'react';
import { Box, Button, Typography, TextField, Grid, Paper, IconButton, Dialog, DialogTitle, DialogContent, DialogActions, MenuItem, Select, FormControl, InputLabel, FormHelperText } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon } from '@mui/icons-material';
import ReactFlow, { 
  ReactFlowProvider, 
  Controls, 
  Background, 
  useNodesState, 
  useEdgesState, 
  addEdge,
  Connection,
  Edge,
  Node
} from 'reactflow';
import 'reactflow/dist/style.css';
import { v4 as uuidv4 } from 'uuid';
import { useSupabaseClient } from '@supabase/auth-helpers-react';

interface FlowEditorProps {
  sceneId: string;
  flow: any;
  onSave: (flow: any) => void;
}

interface FlowNode {
  id: string;
  space: string;
  next?: string;
  options?: Record<string, string>;
  entry_condition?: string;
  exit_trigger?: string;
  properties?: Record<string, any>;
}

const FlowEditor: React.FC<FlowEditorProps> = ({ sceneId, flow, onSave }) => {
  const supabase = useSupabaseClient();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [spaces, setSpaces] = useState<any[]>([]);
  const [showNodeDialog, setShowNodeDialog] = useState(false);
  const [currentNode, setCurrentNode] = useState<any>(null);
  const [flowName, setFlowName] = useState('Default Flow');
  const [flowDescription, setFlowDescription] = useState('');
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<any>(null);

  useEffect(() => {
    loadSpaces();
    initializeFlow();
  }, [sceneId, flow]);

  const loadSpaces = async () => {
    try {
      // Get scene configuration to extract spaces
      const { data, error } = await supabase
        .from('scenes')
        .select('configuration')
        .eq('id', sceneId)
        .single();

      if (error) {
        console.error('Error loading scene configuration:', error);
        return;
      }

      const configuration = data.configuration || {};
      const locations = configuration.locations || [];
      
      // Extract all spaces from all exhibitions in all locations
      const allSpaces: any[] = [];
      
      locations.forEach((location: any) => {
        (location.exhibitions || []).forEach((exhibition: any) => {
          (exhibition.spaces || []).forEach((space: any) => {
            allSpaces.push({
              id: space.id,
              name: space.name,
              locationName: location.name,
              exhibitionName: exhibition.name
            });
          });
        });
      });
      
      setSpaces(allSpaces);
    } catch (err) {
      console.error('Error loading spaces:', err);
    }
  };

  const initializeFlow = () => {
    if (!flow) return;

    const flowNodes: Node[] = [];
    const flowEdges: Edge[] = [];
    
    // Convert flow object to nodes and edges
    Object.entries(flow).forEach(([nodeId, nodeData]: [string, any]) => {
      // Skip non-node properties
      if (nodeId === 'name' || nodeId === 'description') {
        if (nodeId === 'name') setFlowName(nodeData);
        if (nodeId === 'description') setFlowDescription(nodeData);
        return;
      }

      // Create node
      const position = { x: Math.random() * 500, y: Math.random() * 300 };
      const spaceName = getSpaceName(nodeData.space);
      
      flowNodes.push({
        id: nodeId,
        type: 'default',
        position,
        data: { 
          label: `${nodeId}: ${spaceName}`,
          nodeData
        }
      });

      // Create edge if node has a next property
      if (nodeData.next) {
        flowEdges.push({
          id: `${nodeId}-${nodeData.next}`,
          source: nodeId,
          target: nodeData.next,
          animated: true
        });
      }

      // Create edges for options
      if (nodeData.options) {
        Object.entries(nodeData.options).forEach(([optionName, targetNodeId]: [string, any]) => {
          flowEdges.push({
            id: `${nodeId}-${targetNodeId}-${optionName}`,
            source: nodeId,
            target: targetNodeId,
            label: optionName,
            style: { strokeDasharray: '5,5' }
          });
        });
      }
    });

    setNodes(flowNodes);
    setEdges(flowEdges);
  };

  const getSpaceName = (spaceId: string) => {
    const space = spaces.find(s => s.id === spaceId);
    return space ? space.name : spaceId;
  };

  const onConnect = (params: Connection) => {
    // Create a new edge
    setEdges((eds) => addEdge({ ...params, animated: true }, eds));
    
    // Update the source node's next property
    setNodes((nds) => 
      nds.map((node) => {
        if (node.id === params.source) {
          const updatedNodeData = { ...node.data.nodeData, next: params.target };
          return {
            ...node,
            data: {
              ...node.data,
              nodeData: updatedNodeData
            }
          };
        }
        return node;
      })
    );
  };

  const handleAddNode = () => {
    setCurrentNode({
      id: uuidv4(),
      space: spaces.length > 0 ? spaces[0].id : '',
      next: '',
      options: {},
      entry_condition: '',
      exit_trigger: '',
      properties: {}
    });
    setShowNodeDialog(true);
  };

  const handleEditNode = (nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      setCurrentNode({ ...node.data.nodeData, id: node.id });
      setShowNodeDialog(true);
    }
  };

  const handleDeleteNode = (nodeId: string) => {
    // Remove node
    setNodes((nds) => nds.filter((n) => n.id !== nodeId));
    
    // Remove all edges connected to this node
    setEdges((eds) => eds.filter((e) => e.source !== nodeId && e.target !== nodeId));
  };

  const handleSaveNode = () => {
    if (!currentNode) return;

    // Update or add node
    setNodes((nds) => {
      const index = nds.findIndex(n => n.id === currentNode.id);
      const spaceName = getSpaceName(currentNode.space);
      const newNode = {
        id: currentNode.id,
        type: 'default',
        position: index >= 0 ? nds[index].position : { x: Math.random() * 500, y: Math.random() * 300 },
        data: { 
          label: `${currentNode.id}: ${spaceName}`,
          nodeData: { ...currentNode }
        }
      };

      if (index >= 0) {
        return nds.map((n, i) => i === index ? newNode : n);
      } else {
        return [...nds, newNode];
      }
    });

    setShowNodeDialog(false);
    setCurrentNode(null);
  };

  const handleSaveFlow = () => {
    // Convert nodes to flow object
    const flowObject: Record<string, any> = {
      name: flowName,
      description: flowDescription
    };

    nodes.forEach(node => {
      flowObject[node.id] = { ...node.data.nodeData };
    });

    // Ensure we have a startup node
    if (!flowObject.startup && nodes.length > 0) {
      flowObject.startup = {
        space: nodes[0].data.nodeData.space,
        next: nodes[0].data.nodeData.next || ''
      };
    }

    onSave(flowObject);
  };

  return (
    <Box sx={{ height: '100%' }}>
      <Box sx={{ mb: 2 }}>
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              label="Flow Name"
              fullWidth
              value={flowName}
              onChange={(e) => setFlowName(e.target.value)}
              margin="normal"
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              label="Description"
              fullWidth
              value={flowDescription}
              onChange={(e) => setFlowDescription(e.target.value)}
              margin="normal"
            />
          </Grid>
        </Grid>
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<AddIcon />}
          onClick={handleAddNode}
        >
          Add Node
        </Button>
        <Button 
          variant="contained" 
          color="primary" 
          startIcon={<SaveIcon />}
          onClick={handleSaveFlow}
        >
          Save Flow
        </Button>
      </Box>

      <Paper sx={{ height: 600, width: '100%' }}>
        <ReactFlowProvider>
          <Box ref={reactFlowWrapper} sx={{ height: '100%', width: '100%' }}>
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onInit={setReactFlowInstance}
              onNodeDoubleClick={(_, node) => handleEditNode(node.id)}
              fitView
            >
              <Controls />
              <Background />
            </ReactFlow>
          </Box>
        </ReactFlowProvider>
      </Paper>

      <Dialog open={showNodeDialog} onClose={() => setShowNodeDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{currentNode?.id ? 'Edit Node' : 'Add Node'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <TextField
                label="Node ID"
                fullWidth
                value={currentNode?.id || ''}
                disabled
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel id="space-select-label">Space</InputLabel>
                <Select
                  labelId="space-select-label"
                  value={currentNode?.space || ''}
                  onChange={(e) => setCurrentNode({...currentNode, space: e.target.value})}
                  label="Space"
                >
                  {spaces.map((space) => (
                    <MenuItem key={space.id} value={space.id}>
                      {space.name} ({space.locationName} / {space.exhibitionName})
                    </MenuItem>
                  ))}
                </Select>
                <FormHelperText>Select the space for this node</FormHelperText>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Entry Condition"
                fullWidth
                value={currentNode?.entry_condition || ''}
                onChange={(e) => setCurrentNode({...currentNode, entry_condition: e.target.value})}
                margin="normal"
                helperText="Condition that must be met to enter this node"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                label="Exit Trigger"
                fullWidth
                value={currentNode?.exit_trigger || ''}
                onChange={(e) => setCurrentNode({...currentNode, exit_trigger: e.target.value})}
                margin="normal"
                helperText="Trigger that causes exit from this node"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowNodeDialog(false)}>Cancel</Button>
          <Button onClick={handleSaveNode} variant="contained" color="primary">Save</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FlowEditor;
