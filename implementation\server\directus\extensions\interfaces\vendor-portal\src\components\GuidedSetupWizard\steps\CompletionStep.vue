<template>
  <wizard-step
    title="Setup Complete"
    description="Congratulations! You've completed the setup process for your vendor account."
    :step-data="stepData"
    @update:step-data="updateStepData"
    @validate="validateStep"
  >
    <div class="completion-content">
      <div class="completion-icon">
        <i class="material-icons">check_circle</i>
      </div>
      
      <h2 class="completion-title">Your Virtual Showroom is Ready!</h2>
      
      <p class="completion-message">
        You've successfully set up your vendor account and configured your virtual showroom.
        Your clients can now explore your products in an immersive 3D environment.
      </p>
      
      <div class="setup-summary">
        <h3 class="summary-title">Setup Summary</h3>
        
        <div class="summary-items">
          <div class="summary-item">
            <div class="summary-icon">
              <i class="material-icons">business</i>
            </div>
            <div class="summary-content">
              <h4 class="summary-item-title">Company Profile</h4>
              <p class="summary-item-description">
                Your company profile has been set up with your branding and contact information.
              </p>
            </div>
          </div>
          
          <div class="summary-item">
            <div class="summary-icon">
              <i class="material-icons">people</i>
            </div>
            <div class="summary-content">
              <h4 class="summary-item-title">User Accounts</h4>
              <p class="summary-item-description">
                You've configured user accounts for your team members to access the platform.
              </p>
            </div>
          </div>
          
          <div class="summary-item">
            <div class="summary-icon">
              <i class="material-icons">palette</i>
            </div>
            <div class="summary-content">
              <h4 class="summary-item-title">Branding</h4>
              <p class="summary-item-description">
                Your brand colors, fonts, and logo have been applied to your virtual showroom.
              </p>
            </div>
          </div>
          
          <div class="summary-item">
            <div class="summary-icon">
              <i class="material-icons">inventory_2</i>
            </div>
            <div class="summary-content">
              <h4 class="summary-item-title">Products</h4>
              <p class="summary-item-description">
                Your products have been uploaded and organized into categories.
              </p>
            </div>
          </div>
          
          <div class="summary-item">
            <div class="summary-icon">
              <i class="material-icons">view_in_ar</i>
            </div>
            <div class="summary-content">
              <h4 class="summary-item-title">Showroom</h4>
              <p class="summary-item-description">
                Your virtual showroom has been configured with your selected layout and settings.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="next-steps">
        <h3 class="next-steps-title">Next Steps</h3>
        
        <div class="next-steps-items">
          <div class="next-step-item">
            <div class="step-number">1</div>
            <div class="step-content">
              <h4 class="step-title">Explore Your Vendor Dashboard</h4>
              <p class="step-description">
                Familiarize yourself with the vendor dashboard to manage your products, showroom, and analytics.
              </p>
              <button class="step-action">Go to Dashboard</button>
            </div>
          </div>
          
          <div class="next-step-item">
            <div class="step-number">2</div>
            <div class="step-content">
              <h4 class="step-title">Preview Your Showroom</h4>
              <p class="step-description">
                Take a tour of your virtual showroom to see how it looks from a client's perspective.
              </p>
              <button class="step-action">Preview Showroom</button>
            </div>
          </div>
          
          <div class="next-step-item">
            <div class="step-number">3</div>
            <div class="step-content">
              <h4 class="step-title">Invite Clients</h4>
              <p class="step-description">
                Share your virtual showroom with clients to start showcasing your products.
              </p>
              <button class="step-action">Invite Clients</button>
            </div>
          </div>
          
          <div class="next-step-item">
            <div class="step-number">4</div>
            <div class="step-content">
              <h4 class="step-title">Get Support</h4>
              <p class="step-description">
                Access our support resources if you need help or have questions about the platform.
              </p>
              <button class="step-action">Support Center</button>
            </div>
          </div>
        </div>
      </div>
      
      <div class="resources">
        <h3 class="resources-title">Helpful Resources</h3>
        
        <div class="resources-items">
          <a href="#" class="resource-item">
            <i class="material-icons">menu_book</i>
            <span>User Guide</span>
          </a>
          
          <a href="#" class="resource-item">
            <i class="material-icons">play_circle</i>
            <span>Video Tutorials</span>
          </a>
          
          <a href="#" class="resource-item">
            <i class="material-icons">help</i>
            <span>FAQ</span>
          </a>
          
          <a href="#" class="resource-item">
            <i class="material-icons">support_agent</i>
            <span>Contact Support</span>
          </a>
        </div>
      </div>
    </div>
  </wizard-step>
</template>

<script>
import WizardStep from '../WizardStep.vue';

export default {
  name: 'CompletionStep',
  
  components: {
    WizardStep
  },
  
  props: {
    stepData: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      localStepData: {
        completed: true,
        completedAt: new Date().toISOString(),
        ...this.stepData
      }
    };
  },
  
  mounted() {
    // Mark this step as valid immediately
    this.$emit('validate', true);
    
    // Update step data with completion information
    this.updateStepData(this.localStepData);
  },
  
  methods: {
    updateStepData(data) {
      this.localStepData = { ...data };
      this.$emit('update:step-data', this.localStepData);
    },
    
    validateStep(isValid) {
      this.$emit('validate', isValid);
    }
  }
};
</script>

<style scoped>
.completion-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.completion-icon {
  font-size: 64px;
  color: var(--theme--primary);
  margin-bottom: 24px;
}

.completion-icon i {
  font-size: 64px;
}

.completion-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--theme--foreground);
}

.completion-message {
  font-size: 16px;
  color: var(--theme--foreground-subdued);
  margin: 0 0 32px 0;
  max-width: 600px;
}

.setup-summary,
.next-steps,
.resources {
  width: 100%;
  margin-bottom: 32px;
  background-color: var(--theme--background-subdued);
  border-radius: 8px;
  padding: 24px;
}

.summary-title,
.next-steps-title,
.resources-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--theme--foreground);
  text-align: left;
}

.summary-items {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.summary-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  text-align: left;
  background-color: var(--theme--background);
  border-radius: 8px;
  padding: 16px;
}

.summary-icon {
  color: var(--theme--primary);
}

.summary-icon i {
  font-size: 24px;
}

.summary-content {
  flex: 1;
}

.summary-item-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: var(--theme--foreground);
}

.summary-item-description {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin: 0;
}

.next-steps-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.next-step-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  text-align: left;
  background-color: var(--theme--background);
  border-radius: 8px;
  padding: 16px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  font-weight: 600;
}

.step-content {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: var(--theme--foreground);
}

.step-description {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  margin: 0 0 12px 0;
}

.step-action {
  padding: 8px 16px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.step-action:hover {
  background-color: var(--theme--primary-accent);
}

.resources-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
}

.resource-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background-color: var(--theme--background);
  border-radius: 8px;
  color: var(--theme--foreground);
  text-decoration: none;
  width: 120px;
  transition: all 0.2s ease;
}

.resource-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  color: var(--theme--primary);
}

.resource-item i {
  font-size: 24px;
}

.resource-item span {
  font-size: 14px;
  text-align: center;
}
</style>
