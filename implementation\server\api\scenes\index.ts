import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { SceneService } from '../../services/scene-service';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  vendor_id: z.string().uuid().optional(),
  limit: z.string().transform(Number).optional(),
  offset: z.string().transform(Number).optional(),
});

// Define the request body schema for POST
const PostBodySchema = z.object({
  vendor_id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable(),
  configuration: z.record(z.any()),
});

/**
 * Scenes API endpoint
 *
 * This endpoint handles listing and creating scenes.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create scene service
    const sceneService = new SceneService(supabase);

    // Handle GET request (list scenes)
    if (req.method === 'GET') {
      // Validate query parameters
      const queryResult = QueryParamsSchema.safeParse(req.query);
      if (!queryResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
      }

      // Extract parameters
      const { vendor_id, limit = 100, offset = 0 } = queryResult.data;

      // Log the request
      logger.info('Scenes request', {
        vendor_id,
        limit,
        offset,
      });

      // Get scenes
      let scenes;
      if (vendor_id) {
        scenes = await sceneService.getScenesByVendor(vendor_id);
      } else {
        // Only admins can list all scenes
        if (session.user.role !== 'admin') {
          return res.status(403).json({ error: 'Forbidden' });
        }

        // Get all scenes with pagination
        const { data, error } = await supabase
          .from('scenes')
          .select('*')
          .range(offset, offset + limit - 1);

        if (error) {
          logger.error('Error fetching scenes', { error });
          return errorHandler(error, res);
        }

        scenes = data;
      }

      // Get total count
      const { count: totalCount, error: countError } = await supabase
        .from('scenes')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        logger.error('Error getting total count', { error: countError });
        // Continue anyway, just don't return total count
      }

      // Return scenes
      return res.status(200).json({
        scenes,
        pagination: {
          limit,
          offset,
          total: totalCount,
        },
      });
    }

    // Handle POST request (create scene)
    if (req.method === 'POST') {
      // Validate request body
      const bodyResult = PostBodySchema.safeParse(req.body);
      if (!bodyResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid request body', details: bodyResult.error.format() });
      }

      // Extract request data
      const { vendor_id, name, description, configuration } = bodyResult.data;

      // Check if user has permission to create scenes for this vendor
      if (session.user.id !== vendor_id && session.user.role !== 'admin') {
        return res.status(403).json({ error: 'Forbidden' });
      }

      // Log the request
      logger.info('Create scene request', {
        vendor_id,
        name,
      });

      // Create scene
      const scene = await sceneService.createScene(vendor_id, name, description, configuration);
      if (!scene) {
        return res.status(500).json({ error: 'Failed to create scene' });
      }

      // Return created scene
      return res.status(201).json({ scene });
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Unexpected error in scenes endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
