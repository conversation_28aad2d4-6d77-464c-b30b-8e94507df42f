name: Scene Validator Service CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'mvs-vr-v2/implementation/server/services/scene/**'
      - 'mvs-vr-v2/implementation/server/api/scenes/validate/**'
      - 'mvs-vr-v2/implementation/server/tests/services/scene/**'
      - 'mvs-vr-v2/.github/workflows/scene-validator.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'mvs-vr-v2/implementation/server/services/scene/**'
      - 'mvs-vr-v2/implementation/server/api/scenes/validate/**'
      - 'mvs-vr-v2/implementation/server/tests/services/scene/**'
      - 'mvs-vr-v2/.github/workflows/scene-validator.yml'

jobs:
  test:
    name: Test Scene Validator Service
    runs-on: ubuntu-latest
    
    services:
      redis:
        image: redis
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'mvs-vr-v2/implementation/server/package-lock.json'
      
      - name: Install dependencies
        working-directory: mvs-vr-v2/implementation/server
        run: npm ci
      
      - name: Run tests
        working-directory: mvs-vr-v2/implementation/server
        run: npm test -- --coverage --testPathPattern=tests/services/scene
        env:
          REDIS_URL: redis://localhost:6379
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_KEY: ${{ secrets.SUPABASE_KEY }}
      
      - name: Upload test coverage
        uses: codecov/codecov-action@v3
        with:
          directory: mvs-vr-v2/implementation/server/coverage
          flags: scene-validator
  
  lint:
    name: Lint Scene Validator Service
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'mvs-vr-v2/implementation/server/package-lock.json'
      
      - name: Install dependencies
        working-directory: mvs-vr-v2/implementation/server
        run: npm ci
      
      - name: Run ESLint
        working-directory: mvs-vr-v2/implementation/server
        run: npx eslint services/scene api/scenes/validate --ext .ts
  
  build:
    name: Build Scene Validator Service
    runs-on: ubuntu-latest
    needs: [test, lint]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'mvs-vr-v2/implementation/server/package-lock.json'
      
      - name: Install dependencies
        working-directory: mvs-vr-v2/implementation/server
        run: npm ci
      
      - name: Build
        working-directory: mvs-vr-v2/implementation/server
        run: npm run build
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: scene-validator-build
          path: mvs-vr-v2/implementation/server/dist
  
  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: scene-validator-build
          path: mvs-vr-v2/implementation/server/dist
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: mvs-vr-v2/implementation/server
          push: true
          tags: ${{ secrets.DOCKER_HUB_USERNAME }}/scene-validator:dev
          cache-from: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/scene-validator:buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/scene-validator:buildcache,mode=max
      
      - name: Deploy to Development Environment
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DEV_SSH_HOST }}
          username: ${{ secrets.DEV_SSH_USERNAME }}
          key: ${{ secrets.DEV_SSH_KEY }}
          script: |
            cd /opt/mvs-vr
            docker-compose pull scene-validator
            docker-compose up -d scene-validator
  
  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: scene-validator-build
          path: mvs-vr-v2/implementation/server/dist
      
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: mvs-vr-v2/implementation/server
          push: true
          tags: ${{ secrets.DOCKER_HUB_USERNAME }}/scene-validator:latest,${{ secrets.DOCKER_HUB_USERNAME }}/scene-validator:${{ github.sha }}
          cache-from: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/scene-validator:buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/scene-validator:buildcache,mode=max
      
      - name: Deploy to Production Environment
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.PROD_SSH_HOST }}
          username: ${{ secrets.PROD_SSH_USERNAME }}
          key: ${{ secrets.PROD_SSH_KEY }}
          script: |
            cd /opt/mvs-vr
            docker-compose pull scene-validator
            docker-compose up -d scene-validator
