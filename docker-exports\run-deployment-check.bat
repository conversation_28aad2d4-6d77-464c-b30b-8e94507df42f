@echo off
echo ========================================
echo MVS-VR DigitalOcean Deployment Checker
echo ========================================
echo.

REM Check if PowerShell script exists
if not exist "Check-MVS-VR-Deployment.ps1" (
    echo ERROR: Check-MVS-VR-Deployment.ps1 not found!
    echo Please make sure you're running this from the docker-exports directory.
    pause
    exit /b 1
)

REM Check if SSH key exists
if not exist "C:\Users\<USER>\.ssh\mvs-vr" (
    echo WARNING: SSH key not found at C:\Users\<USER>\.ssh\mvs-vr
    echo Please ensure your SSH key is properly configured.
    echo.
)

echo Starting deployment check...
echo This will:
echo - Connect to your DigitalOcean server (**************)
echo - Check Docker installation and services
echo - Test all endpoints and connectivity
echo - Auto-fix any issues found
echo - Generate a comprehensive report
echo.

set /p confirm="Continue? (Y/N): "
if /i "%confirm%" neq "Y" (
    echo Cancelled by user.
    pause
    exit /b 0
)

echo.
echo Running PowerShell script...
echo.

REM Run the PowerShell script with execution policy bypass
powershell.exe -ExecutionPolicy Bypass -File "Check-MVS-VR-Deployment.ps1"

echo.
echo ========================================
echo Deployment check completed!
echo Check the generated report file for details.
echo ========================================
pause
