<template>
  <div class="analytics-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <select v-model="selectedPeriod" @change="updateChart">
          <option value="day">Last 24 Hours</option>
          <option value="week">Last 7 Days</option>
          <option value="month">Last 30 Days</option>
          <option value="year">Last 12 Months</option>
        </select>
      </div>
    </div>
    <div class="chart-container" ref="chartContainer">
      <canvas ref="chart"></canvas>
    </div>
    <div v-if="loading" class="chart-loading">
      <div class="spinner"></div>
      <span>Loading data...</span>
    </div>
    <div v-if="error" class="chart-error">
      <span>{{ error }}</span>
    </div>
  </div>
</template>

<script>
import { Chart, registerables } from 'chart.js';
import { defineComponent, ref, onMounted, watch } from 'vue';

// Register Chart.js components
Chart.register(...registerables);

export default defineComponent({
  name: 'AnalyticsChart',
  
  props: {
    title: {
      type: String,
      default: 'Analytics Chart'
    },
    type: {
      type: String,
      default: 'line',
      validator: (value) => ['line', 'bar', 'pie', 'doughnut'].includes(value)
    },
    data: {
      type: Object,
      default: () => ({})
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    }
  },
  
  setup(props, { emit }) {
    const chartContainer = ref(null);
    const chart = ref(null);
    const chartInstance = ref(null);
    const selectedPeriod = ref('week');
    
    // Generate random data for demo purposes
    const generateRandomData = (count, min, max) => {
      return Array.from({ length: count }, () => 
        Math.floor(Math.random() * (max - min + 1)) + min
      );
    };
    
    // Generate labels based on period
    const generateLabels = (period) => {
      const now = new Date();
      const labels = [];
      
      if (period === 'day') {
        // Last 24 hours
        for (let i = 23; i >= 0; i--) {
          const hour = new Date(now);
          hour.setHours(now.getHours() - i);
          labels.push(`${hour.getHours()}:00`);
        }
      } else if (period === 'week') {
        // Last 7 days
        for (let i = 6; i >= 0; i--) {
          const day = new Date(now);
          day.setDate(now.getDate() - i);
          labels.push(day.toLocaleDateString('en-US', { weekday: 'short' }));
        }
      } else if (period === 'month') {
        // Last 30 days
        for (let i = 29; i >= 0; i--) {
          const day = new Date(now);
          day.setDate(now.getDate() - i);
          labels.push(`${day.getDate()}/${day.getMonth() + 1}`);
        }
      } else if (period === 'year') {
        // Last 12 months
        for (let i = 11; i >= 0; i--) {
          const month = new Date(now);
          month.setMonth(now.getMonth() - i);
          labels.push(month.toLocaleDateString('en-US', { month: 'short' }));
        }
      }
      
      return labels;
    };
    
    // Create or update chart
    const createChart = () => {
      if (chartInstance.value) {
        chartInstance.value.destroy();
      }
      
      const ctx = chart.value.getContext('2d');
      
      // Generate demo data
      const labels = generateLabels(selectedPeriod.value);
      let datasets = [];
      
      if (props.data && props.data.datasets) {
        datasets = props.data.datasets;
      } else {
        // Demo data
        if (props.type === 'line' || props.type === 'bar') {
          datasets = [
            {
              label: 'Visits',
              data: generateRandomData(labels.length, 50, 200),
              borderColor: '#3498db',
              backgroundColor: 'rgba(52, 152, 219, 0.2)',
              borderWidth: 2,
              tension: 0.4
            },
            {
              label: 'Interactions',
              data: generateRandomData(labels.length, 20, 100),
              borderColor: '#2ecc71',
              backgroundColor: 'rgba(46, 204, 113, 0.2)',
              borderWidth: 2,
              tension: 0.4
            }
          ];
        } else {
          datasets = [
            {
              data: generateRandomData(5, 10, 100),
              backgroundColor: [
                'rgba(52, 152, 219, 0.7)',
                'rgba(46, 204, 113, 0.7)',
                'rgba(155, 89, 182, 0.7)',
                'rgba(241, 196, 15, 0.7)',
                'rgba(231, 76, 60, 0.7)'
              ],
              borderColor: [
                '#3498db',
                '#2ecc71',
                '#9b59b6',
                '#f1c40f',
                '#e74c3c'
              ],
              borderWidth: 1
            }
          ];
        }
      }
      
      // Chart options
      const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: {
              font: {
                family: 'var(--theme--font-family-sans-serif)',
                size: 12
              },
              color: 'var(--theme--foreground)'
            }
          },
          tooltip: {
            backgroundColor: 'var(--theme--background)',
            titleColor: 'var(--theme--foreground)',
            bodyColor: 'var(--theme--foreground-subdued)',
            borderColor: 'var(--theme--border-color)',
            borderWidth: 1,
            padding: 10,
            cornerRadius: 4,
            displayColors: true
          }
        },
        scales: props.type === 'line' || props.type === 'bar' ? {
          x: {
            grid: {
              color: 'var(--theme--border-color)',
              drawBorder: false
            },
            ticks: {
              color: 'var(--theme--foreground-subdued)',
              font: {
                family: 'var(--theme--font-family-sans-serif)',
                size: 11
              }
            }
          },
          y: {
            grid: {
              color: 'var(--theme--border-color)',
              drawBorder: false
            },
            ticks: {
              color: 'var(--theme--foreground-subdued)',
              font: {
                family: 'var(--theme--font-family-sans-serif)',
                size: 11
              }
            },
            beginAtZero: true
          }
        } : undefined
      };
      
      // Create chart
      chartInstance.value = new Chart(ctx, {
        type: props.type,
        data: {
          labels: props.type === 'pie' || props.type === 'doughnut' 
            ? ['Product A', 'Product B', 'Product C', 'Product D', 'Product E'] 
            : labels,
          datasets
        },
        options
      });
    };
    
    // Update chart when period changes
    const updateChart = () => {
      emit('period-change', selectedPeriod.value);
      createChart();
    };
    
    // Initialize chart on mount
    onMounted(() => {
      if (chart.value) {
        createChart();
      }
    });
    
    // Watch for data changes
    watch(() => props.data, () => {
      if (chart.value && !props.loading) {
        createChart();
      }
    }, { deep: true });
    
    // Watch for type changes
    watch(() => props.type, () => {
      if (chart.value) {
        createChart();
      }
    });
    
    return {
      chartContainer,
      chart,
      selectedPeriod,
      updateChart
    };
  }
});
</script>

<style scoped>
.analytics-chart {
  position: relative;
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  padding: 20px;
  margin-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chart-controls select {
  background-color: var(--theme--background-subdued);
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
  font-size: 14px;
  padding: 6px 10px;
  cursor: pointer;
}

.chart-container {
  height: 300px;
  position: relative;
}

.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--theme--background-rgb), 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.chart-error {
  padding: 10px;
  background-color: var(--theme--danger-background);
  color: var(--theme--danger);
  border-radius: var(--theme--border-radius);
  margin-top: 10px;
  text-align: center;
}
</style>
