/**
 * User Activity Monitoring API
 *
 * This module provides endpoints for monitoring user activity and sessions.
 */

import { Router, Request, Response } from 'express';
import { rateLimiter } from '../../middleware/rate-limiter';
import { logger } from '../../lib/logger';
import { supabase } from '../../lib/supabase';

const router = Router();

/**
 * Get active sessions
 *
 * @param req - Request
 * @param res - Response
 */
export const getActiveSessions = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get active sessions
    const activeSessions = await fetchActiveSessions();

    res.status(200).json({
      success: true,
      data: activeSessions,
    });
  } catch (error) {
    logger.error('Error getting active sessions:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting active sessions',
      },
    });
  }
};

/**
 * Get authentication events
 *
 * @param req - Request
 * @param res - Response
 */
export const getAuthEvents = async (req: Request, res: Response): Promise<void> => {
  try {
    const { event_type = 'all', start_time, end_time, limit = 100, offset = 0 } = req.query;

    // Get authentication events
    const authEvents = await fetchAuthEvents({
      eventType: event_type as string,
      startTime: start_time as string,
      endTime: end_time as string,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string),
    });

    res.status(200).json({
      success: true,
      data: authEvents,
    });
  } catch (error) {
    logger.error('Error getting authentication events:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting authentication events',
      },
    });
  }
};

/**
 * Get user activity statistics
 *
 * @param req - Request
 * @param res - Response
 */
export const getUserActivityStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const { period = '24h' } = req.query;

    // Get user activity statistics
    const userActivityStats = await fetchUserActivityStats(period as string);

    res.status(200).json({
      success: true,
      data: userActivityStats,
    });
  } catch (error) {
    logger.error('Error getting user activity statistics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Error getting user activity statistics',
      },
    });
  }
};

/**
 * Fetch active sessions
 *
 * @returns Active sessions
 */
async function fetchActiveSessions(): Promise<any> {
  // In a real implementation, this would query a session database
  // For now, we'll return mock data

  // Generate mock active sessions
  const activeSessions = [];
  const now = new Date();

  for (let i = 0; i < 25; i++) {
    const startTime = new Date(now.getTime() - Math.floor(Math.random() * 60 * 60 * 1000));
    const duration = Math.floor((now.getTime() - startTime.getTime()) / 1000);

    activeSessions.push({
      session_id: `session-${i}`,
      user_id: `user-${Math.floor(Math.random() * 1000)}`,
      email: `user${Math.floor(Math.random() * 1000)}@example.com`,
      role: ['admin', 'vendor', 'client'][Math.floor(Math.random() * 3)],
      ip_address: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
      user_agent: ['Chrome', 'Firefox', 'Safari', 'Edge'][Math.floor(Math.random() * 4)],
      location: ['New York', 'London', 'Tokyo', 'Sydney', 'Berlin'][Math.floor(Math.random() * 5)],
      start_time: startTime.toISOString(),
      duration: duration,
      last_activity: new Date(
        now.getTime() - Math.floor(Math.random() * 10 * 60 * 1000),
      ).toISOString(),
    });
  }

  // Sort sessions by duration (longest first)
  activeSessions.sort((a, b) => b.duration - a.duration);

  return {
    total_active_sessions: activeSessions.length,
    sessions: activeSessions,
  };
}

/**
 * Fetch authentication events
 *
 * @param options - Filter options
 * @returns Authentication events
 */
async function fetchAuthEvents(options: {
  eventType: string;
  startTime?: string;
  endTime?: string;
  limit: number;
  offset: number;
}): Promise<any> {
  // In a real implementation, this would query an auth events database
  // For now, we'll return mock data

  // Generate mock auth events
  const authEvents = [];
  const now = new Date();
  const eventTypes = ['login', 'logout', 'login_failed', 'password_reset', 'account_locked'];

  // Filter event types based on options
  const filteredEventTypes = options.eventType === 'all' ? eventTypes : [options.eventType];

  for (let i = 0; i < options.limit; i++) {
    const eventType = filteredEventTypes[Math.floor(Math.random() * filteredEventTypes.length)];
    const timestamp = new Date(now.getTime() - Math.floor(Math.random() * 24 * 60 * 60 * 1000));

    let status = 'success';
    let message = '';

    switch (eventType) {
      case 'login':
        status = Math.random() > 0.1 ? 'success' : 'failed';
        message = status === 'success' ? 'Login successful' : 'Login failed: Invalid credentials';
        break;
      case 'logout':
        message = 'Logout successful';
        break;
      case 'login_failed':
        status = 'failed';
        message = `Login failed: ${['Invalid credentials', 'Account locked', 'Account disabled'][Math.floor(Math.random() * 3)]}`;
        break;
      case 'password_reset':
        status = Math.random() > 0.05 ? 'success' : 'failed';
        message = status === 'success' ? 'Password reset successful' : 'Password reset failed';
        break;
      case 'account_locked':
        status = 'warning';
        message = 'Account locked due to too many failed login attempts';
        break;
    }

    authEvents.push({
      id: `event-${i + options.offset}`,
      timestamp: timestamp.toISOString(),
      event_type: eventType,
      user_id:
        eventType === 'login_failed' && Math.random() > 0.5
          ? null
          : `user-${Math.floor(Math.random() * 1000)}`,
      email:
        eventType === 'login_failed' && Math.random() > 0.5
          ? null
          : `user${Math.floor(Math.random() * 1000)}@example.com`,
      ip_address: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
      user_agent: ['Chrome', 'Firefox', 'Safari', 'Edge'][Math.floor(Math.random() * 4)],
      location: ['New York', 'London', 'Tokyo', 'Sydney', 'Berlin'][Math.floor(Math.random() * 5)],
      status: status,
      message: message,
    });
  }

  // Sort events by timestamp (newest first)
  authEvents.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  return {
    total_events: 1254,
    events: authEvents,
    limit: options.limit,
    offset: options.offset,
  };
}

/**
 * Fetch user activity statistics
 *
 * @param period - Time period
 * @returns User activity statistics
 */
async function fetchUserActivityStats(period: string): Promise<any> {
  // In a real implementation, this would query a user activity database
  // For now, we'll return mock data

  // Calculate start time based on period
  const now = new Date();
  let startTime: Date;

  switch (period) {
    case '1h':
      startTime = new Date(now.getTime() - 60 * 60 * 1000);
      break;
    case '6h':
      startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
      break;
    case '24h':
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    default:
      startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
  }

  // Generate time points for the chart
  const timePoints = generateTimePoints(startTime, now, period);

  // Generate mock data for active users
  const activeUsersData = {
    name: 'Active Users',
    color: '#2196F3',
    data: timePoints.map(point => ({
      time: point,
      value: Math.floor(Math.random() * 50) + 10, // 10-60 users
    })),
  };

  // Generate mock data for login events
  const loginEventsData = {
    name: 'Login Events',
    color: '#4CAF50',
    data: timePoints.map(point => ({
      time: point,
      value: Math.floor(Math.random() * 30) + 5, // 5-35 events
    })),
  };

  // Generate mock data for failed login events
  const failedLoginEventsData = {
    name: 'Failed Login Events',
    color: '#F44336',
    data: timePoints.map(point => ({
      time: point,
      value: Math.floor(Math.random() * 10) + 1, // 1-11 events
    })),
  };

  // Calculate totals
  const totalActiveUsers = activeUsersData.data.reduce(
    (max, point) => Math.max(max, point.value),
    0,
  );
  const totalLoginEvents = loginEventsData.data.reduce((sum, point) => sum + point.value, 0);
  const totalFailedLoginEvents = failedLoginEventsData.data.reduce(
    (sum, point) => sum + point.value,
    0,
  );

  // Generate mock data for user roles
  const userRoles = [
    { role: 'admin', count: Math.floor(Math.random() * 10) + 5 }, // 5-15 admins
    { role: 'vendor', count: Math.floor(Math.random() * 50) + 20 }, // 20-70 vendors
    { role: 'client', count: Math.floor(Math.random() * 200) + 100 }, // 100-300 clients
  ];

  // Calculate total users
  const totalUsers = userRoles.reduce((sum, role) => sum + role.count, 0);

  // Calculate percentages
  userRoles.forEach(role => {
    role['percentage'] = ((role.count / totalUsers) * 100).toFixed(2);
  });

  return {
    active_users: totalActiveUsers,
    total_login_events: totalLoginEvents,
    total_failed_login_events: totalFailedLoginEvents,
    active_users_trend: activeUsersData,
    login_events_trend: loginEventsData,
    failed_login_events_trend: failedLoginEventsData,
    user_roles: userRoles,
    total_users: totalUsers,
    period: period,
    start_time: startTime.toISOString(),
    end_time: now.toISOString(),
  };
}

/**
 * Generate time points for charts
 *
 * @param startTime - Start time
 * @param endTime - End time
 * @param period - Time period
 * @returns Array of time points
 */
function generateTimePoints(startTime: Date, endTime: Date, period: string): string[] {
  const timePoints: string[] = [];
  let interval: number;
  let format: string;

  // Determine interval and format based on period
  switch (period) {
    case '1h':
      interval = 5 * 60 * 1000; // 5 minutes
      format = 'HH:mm';
      break;
    case '6h':
      interval = 15 * 60 * 1000; // 15 minutes
      format = 'HH:mm';
      break;
    case '24h':
      interval = 60 * 60 * 1000; // 1 hour
      format = 'HH:mm';
      break;
    case '7d':
      interval = 6 * 60 * 60 * 1000; // 6 hours
      format = 'MM-DD HH:mm';
      break;
    case '30d':
      interval = 24 * 60 * 60 * 1000; // 1 day
      format = 'MM-DD';
      break;
    default:
      interval = 60 * 60 * 1000; // 1 hour
      format = 'HH:mm';
  }

  // Generate time points
  for (let time = startTime.getTime(); time <= endTime.getTime(); time += interval) {
    timePoints.push(new Date(time).toISOString());
  }

  return timePoints;
}

// Register routes
router.get('/sessions', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getActiveSessions);
router.get('/auth-events', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getAuthEvents);
router.get('/statistics', rateLimiter({ windowMs: 60 * 1000, max: 10 }), getUserActivityStats);

export default router;
