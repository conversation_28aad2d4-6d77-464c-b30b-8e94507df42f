# AI Memory Control Specification

## Overview

This document defines how the AI assistant embedded within the Kanousei VR ecosystem will handle memory persistence across sessions, ensuring continuity when desired and discretion when required.

---

## Objectives

* Enable the AI to retain context about user interactions across sessions (when allowed).
* Offer users and vendors full control over what is remembered and when.
* Support privacy-by-design defaults with explicit consent.

---

## Modes of Operation

### 1. **Session-Based (Default)**

* Memory is **wiped after each session**.
* Ideal for anonymous users or temporary logins.

### 2. **Persistent Memory Mode**

* AI can retain knowledge of:

  * Products viewed
  * Spaces visited
  * User preferences (e.g., color themes, brands)
  * Assistant conversations or favorite suggestions
* Enabled **only with explicit opt-in**.

---

## Control Points

### Admin/Vendor:

* Toggle persistence for specific assistants.
* Define max memory scope (per user, per room, global).
* View/edit/delete AI memory via panel.

### User:

* View assistant memory
* Clear specific items or full history
* Toggle persistent memory on/off anytime

---

## Implementation Notes

### Data Storage

* Memory is stored in a secure user-specific context bucket.
* Tied to user ID or guest session token.

### Metadata Sample:

```json
{
  "userId": "visitor_2351",
  "memory": {
    "favorites": ["glass_table_001", "sofa_modern_003"],
    "visitedRooms": ["dubai_mainhall", "vendorA_preview"],
    "lastPrompt": "show me more modern chairs"
  },
  "updatedAt": "2025-05-15T09:43:00Z"
}
```

### AI Runtime Access

* The AI has scoped read access only to what the user has approved.
* On memory-enabled sessions, this data is injected at prompt runtime.

---

## Security & Compliance

* Memory is encrypted in transit and at rest.
* Auto-expiry rules can be configured (e.g., forget after 30 days).
* GDPR-compliant structure with right to be forgotten.

---

## UX Summary

* Assistant will notify users when memory is active.
* Suggests “Would you like me to remember this?” after notable queries.
* Always offers a “forget this” option in interactions.
