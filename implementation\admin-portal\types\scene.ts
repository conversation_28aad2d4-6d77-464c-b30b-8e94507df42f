export interface AssetPlacement {
  id: string;
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
  properties?: Record<string, any>;
}

export interface Space {
  id: string;
  name: string;
  description: string;
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
  assets: AssetPlacement[];
  lighting?: string;
  audio?: string;
  properties?: Record<string, any>;
}

export interface Exhibition {
  id: string;
  name: string;
  description: string;
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
  spaces: Space[];
  properties?: Record<string, any>;
}

export interface Location {
  id: string;
  name: string;
  description: string;
  position: [number, number, number];
  rotation: [number, number, number];
  scale: [number, number, number];
  exhibitions: Exhibition[];
  properties?: Record<string, any>;
}

export interface SceneFlowNode {
  id: string;
  space: string;
  next?: string;
  options?: Record<string, string>;
  entry_condition?: string;
  exit_trigger?: string;
  properties?: Record<string, any>;
}

export interface SceneFlow {
  id: string;
  scene_id: string;
  name: string;
  description: string | null;
  flow: Record<string, any>;
  version: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Scene {
  id: string;
  vendor_id: string;
  name: string;
  description: string | null;
  version: string;
  configuration: {
    locations?: Location[];
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}

export interface SceneVersion {
  id: string;
  scene_id: string;
  version: string;
  configuration: Record<string, any>;
  is_current: boolean;
  created_at: string;
  updated_at: string;
}

export interface SceneAsset {
  id: string;
  scene_id: string;
  asset_id: string;
  created_at: string;
}

export interface SceneBlueprint {
  id: string;
  scene_id: string;
  blueprint_id: string;
  created_at: string;
}
