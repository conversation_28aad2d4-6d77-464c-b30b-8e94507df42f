<template>
  <div class="user-activity">
    <v-skeleton-loader
      v-if="loading"
      type="card"
      class="mx-auto"
    ></v-skeleton-loader>
    
    <div v-else class="activity-content">
      <!-- Activity Statistics -->
      <v-card class="mb-4">
        <v-card-title>User Activity Statistics</v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="primary" size="36">mdi-account-group</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ activityStats?.active_users || 0 }}</div>
                  <div class="metric-label">Active Users</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="success" size="36">mdi-login</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ activityStats?.total_login_events || 0 }}</div>
                  <div class="metric-label">Login Events</div>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="4">
              <div class="metric-card">
                <div class="metric-icon">
                  <v-icon color="error" size="36">mdi-login-variant</v-icon>
                </div>
                <div class="metric-details">
                  <div class="metric-value">{{ activityStats?.total_failed_login_events || 0 }}</div>
                  <div class="metric-label">Failed Login Events</div>
                </div>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <div class="period-selector">
                <v-btn-toggle
                  v-model="selectedPeriod"
                  mandatory
                  @change="periodChanged"
                >
                  <v-btn value="1h">1h</v-btn>
                  <v-btn value="6h">6h</v-btn>
                  <v-btn value="24h">24h</v-btn>
                  <v-btn value="7d">7d</v-btn>
                  <v-btn value="30d">30d</v-btn>
                </v-btn-toggle>
              </div>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="8">
              <div class="chart-container">
                <h3 class="chart-title">User Activity Trends</h3>
                <div class="chart-wrapper">
                  <canvas ref="activityTrendsChart"></canvas>
                </div>
              </div>
            </v-col>
            
            <v-col cols="12" md="4">
              <div class="chart-container">
                <h3 class="chart-title">User Roles Distribution</h3>
                <div class="chart-wrapper">
                  <canvas ref="userRolesChart"></canvas>
                </div>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      
      <!-- Active Sessions -->
      <v-card class="mb-4">
        <v-card-title>Active Sessions</v-card-title>
        
        <v-card-text>
          <v-data-table
            :headers="sessionHeaders"
            :items="activeSessions"
            :items-per-page="5"
            class="elevation-1"
            @click:row="showSessionDetails"
          >
            <template v-slot:item.user_id="{ item }">
              <div class="user-info">
                <div class="user-email">{{ item.email }}</div>
                <div class="user-role">
                  <v-chip
                    x-small
                    :color="getRoleColor(item.role)"
                    text-color="white"
                  >
                    {{ item.role }}
                  </v-chip>
                </div>
              </div>
            </template>
            
            <template v-slot:item.start_time="{ item }">
              {{ formatTimestamp(item.start_time) }}
            </template>
            
            <template v-slot:item.duration="{ item }">
              {{ formatDuration(item.duration) }}
            </template>
            
            <template v-slot:item.location="{ item }">
              <div class="location-info">
                <div>{{ item.location }}</div>
                <div class="ip-address">{{ item.ip_address }}</div>
              </div>
            </template>
          </v-data-table>
        </v-card-text>
      </v-card>
      
      <!-- Authentication Events -->
      <v-card>
        <v-card-title>Authentication Events</v-card-title>
        
        <v-card-text>
          <v-row>
            <v-col cols="12" md="4">
              <v-select
                v-model="authFilters.eventType"
                :items="eventTypes"
                label="Event Type"
                outlined
                dense
                @change="applyAuthFilters"
              ></v-select>
            </v-col>
            
            <v-col cols="12" md="4">
              <v-menu
                ref="startDateMenu"
                v-model="startDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="authFilters.startDate"
                    label="Start Date"
                    prepend-icon="mdi-calendar"
                    readonly
                    outlined
                    dense
                    v-bind="attrs"
                    v-on="on"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="authFilters.startDate"
                  @input="startDateMenu = false"
                ></v-date-picker>
              </v-menu>
            </v-col>
            
            <v-col cols="12" md="4">
              <v-menu
                ref="endDateMenu"
                v-model="endDateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="authFilters.endDate"
                    label="End Date"
                    prepend-icon="mdi-calendar"
                    readonly
                    outlined
                    dense
                    v-bind="attrs"
                    v-on="on"
                  ></v-text-field>
                </template>
                <v-date-picker
                  v-model="authFilters.endDate"
                  :min="authFilters.startDate"
                  @input="endDateMenu = false"
                ></v-date-picker>
              </v-menu>
            </v-col>
          </v-row>
          
          <v-data-table
            :headers="authEventHeaders"
            :items="authEvents"
            :items-per-page="5"
            class="elevation-1"
          >
            <template v-slot:item.timestamp="{ item }">
              {{ formatTimestamp(item.timestamp) }}
            </template>
            
            <template v-slot:item.event_type="{ item }">
              <v-chip
                small
                :color="getEventTypeColor(item.event_type)"
                text-color="white"
              >
                {{ formatEventType(item.event_type) }}
              </v-chip>
            </template>
            
            <template v-slot:item.user_id="{ item }">
              <div v-if="item.email">{{ item.email }}</div>
              <div v-else class="unknown-user">Unknown User</div>
            </template>
            
            <template v-slot:item.status="{ item }">
              <v-chip
                small
                :color="getStatusColor(item.status)"
                text-color="white"
              >
                {{ item.status }}
              </v-chip>
            </template>
          </v-data-table>
          
          <!-- Pagination -->
          <div class="pagination-controls">
            <v-btn
              :disabled="authFilters.offset === 0"
              @click="previousPage"
              icon
            >
              <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
            
            <span class="pagination-info">
              Showing {{ authFilters.offset + 1 }} - {{ Math.min(authFilters.offset + authFilters.limit, totalAuthEvents) }} of {{ totalAuthEvents }}
            </span>
            
            <v-btn
              :disabled="authFilters.offset + authFilters.limit >= totalAuthEvents"
              @click="nextPage"
              icon
            >
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn>
          </div>
        </v-card-text>
      </v-card>
      
      <!-- Session Details Dialog -->
      <v-dialog
        v-model="sessionDialog"
        max-width="600"
      >
        <v-card>
          <v-card-title class="headline">
            Session Details
          </v-card-title>
          
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>User</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ selectedSession?.email }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Role</v-list-item-subtitle>
                    <v-list-item-title>
                      <v-chip
                        small
                        :color="getRoleColor(selectedSession?.role)"
                        text-color="white"
                      >
                        {{ selectedSession?.role }}
                      </v-chip>
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Start Time</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ formatTimestamp(selectedSession?.start_time, true) }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Duration</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ formatDuration(selectedSession?.duration) }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Location</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ selectedSession?.location }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>IP Address</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ selectedSession?.ip_address }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>User Agent</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ selectedSession?.user_agent }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-subtitle>Last Activity</v-list-item-subtitle>
                    <v-list-item-title>
                      {{ formatTimestamp(selectedSession?.last_activity, true) }}
                    </v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </v-col>
            </v-row>
          </v-card-text>
          
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="primary"
              text
              @click="sessionDialog = false"
            >
              Close
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </div>
  </div>
</template>

<script>
import Chart from 'chart.js/auto';

export default {
  name: 'UserActivity',
  
  props: {
    activeSessions: {
      type: Array,
      default: () => []
    },
    authEvents: {
      type: Array,
      default: () => []
    },
    activityStats: {
      type: Object,
      default: null
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      selectedPeriod: '24h',
      activityTrendsChart: null,
      userRolesChart: null,
      sessionHeaders: [
        { text: 'User', value: 'user_id', width: '25%' },
        { text: 'Start Time', value: 'start_time', width: '20%' },
        { text: 'Duration', value: 'duration', width: '15%' },
        { text: 'Location', value: 'location', width: '25%' },
        { text: 'User Agent', value: 'user_agent', width: '15%' }
      ],
      authEventHeaders: [
        { text: 'Timestamp', value: 'timestamp', width: '20%' },
        { text: 'Event Type', value: 'event_type', width: '15%' },
        { text: 'User', value: 'user_id', width: '25%' },
        { text: 'Location', value: 'location', width: '20%' },
        { text: 'Status', value: 'status', width: '10%' }
      ],
      eventTypes: [
        { text: 'All Events', value: 'all' },
        { text: 'Login', value: 'login' },
        { text: 'Logout', value: 'logout' },
        { text: 'Login Failed', value: 'login_failed' },
        { text: 'Password Reset', value: 'password_reset' },
        { text: 'Account Locked', value: 'account_locked' }
      ],
      authFilters: {
        eventType: 'all',
        startDate: this.getDefaultStartDate(),
        endDate: new Date().toISOString().split('T')[0],
        limit: 5,
        offset: 0
      },
      totalAuthEvents: 0,
      startDateMenu: false,
      endDateMenu: false,
      sessionDialog: false,
      selectedSession: null
    };
  },
  
  watch: {
    activityStats() {
      this.$nextTick(() => {
        this.initActivityCharts();
      });
    }
  },
  
  mounted() {
    this.$nextTick(() => {
      if (this.activityStats) {
        this.initActivityCharts();
      }
      
      // Set total auth events from props
      if (this.authEvents && this.authEvents.length > 0) {
        this.totalAuthEvents = this.authEvents.length;
      }
    });
  },
  
  beforeDestroy() {
    if (this.activityTrendsChart) {
      this.activityTrendsChart.destroy();
    }
    
    if (this.userRolesChart) {
      this.userRolesChart.destroy();
    }
  },
  
  methods: {
    // Get default start date (30 days ago)
    getDefaultStartDate() {
      const date = new Date();
      date.setDate(date.getDate() - 30);
      return date.toISOString().split('T')[0];
    },
    
    // Initialize activity charts
    initActivityCharts() {
      this.initActivityTrendsChart();
      this.initUserRolesChart();
    },
    
    // Initialize activity trends chart
    initActivityTrendsChart() {
      if (!this.activityStats) return;
      
      const ctx = this.$refs.activityTrendsChart?.getContext('2d');
      if (!ctx) return;
      
      // Destroy existing chart if it exists
      if (this.activityTrendsChart) {
        this.activityTrendsChart.destroy();
      }
      
      // Prepare data for chart
      const datasets = [];
      
      if (this.activityStats.active_users_trend) {
        datasets.push({
          label: this.activityStats.active_users_trend.name,
          data: this.activityStats.active_users_trend.data.map(point => point.value),
          borderColor: this.activityStats.active_users_trend.color,
          backgroundColor: 'transparent',
          borderWidth: 2,
          tension: 0.4,
          fill: false,
          yAxisID: 'y'
        });
      }
      
      if (this.activityStats.login_events_trend) {
        datasets.push({
          label: this.activityStats.login_events_trend.name,
          data: this.activityStats.login_events_trend.data.map(point => point.value),
          borderColor: this.activityStats.login_events_trend.color,
          backgroundColor: this.activityStats.login_events_trend.color + '20',
          borderWidth: 2,
          tension: 0.4,
          fill: true,
          yAxisID: 'y1'
        });
      }
      
      if (this.activityStats.failed_login_events_trend) {
        datasets.push({
          label: this.activityStats.failed_login_events_trend.name,
          data: this.activityStats.failed_login_events_trend.data.map(point => point.value),
          borderColor: this.activityStats.failed_login_events_trend.color,
          backgroundColor: this.activityStats.failed_login_events_trend.color + '20',
          borderWidth: 2,
          tension: 0.4,
          fill: true,
          yAxisID: 'y1'
        });
      }
      
      // Create chart
      this.activityTrendsChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: this.activityStats.active_users_trend?.data.map(point => {
            const date = new Date(point.time);
            return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          }),
          datasets: datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top'
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              title: {
                display: true,
                text: 'Active Users'
              }
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              grid: {
                drawOnChartArea: false
              },
              title: {
                display: true,
                text: 'Login Events'
              }
            },
            x: {
              title: {
                display: true,
                text: 'Time'
              }
            }
          }
        }
      });
    },
    
    // Initialize user roles chart
    initUserRolesChart() {
      if (!this.activityStats || !this.activityStats.user_roles) return;
      
      const ctx = this.$refs.userRolesChart?.getContext('2d');
      if (!ctx) return;
      
      // Destroy existing chart if it exists
      if (this.userRolesChart) {
        this.userRolesChart.destroy();
      }
      
      // Prepare data for chart
      const labels = this.activityStats.user_roles.map(role => role.role);
      const data = this.activityStats.user_roles.map(role => role.count);
      const backgroundColor = [
        '#4CAF50', // admin
        '#2196F3', // vendor
        '#FFC107'  // client
      ];
      
      // Create chart
      this.userRolesChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: backgroundColor,
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'right'
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.raw || 0;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = Math.round((value / total) * 100);
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    },
    
    // Format timestamp
    formatTimestamp(timestamp, detailed = false) {
      if (!timestamp) return 'N/A';
      
      const date = new Date(timestamp);
      
      if (detailed) {
        return date.toLocaleString();
      }
      
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      
      if (diffMins < 60) {
        return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
      } else if (diffMins < 1440) {
        const hours = Math.floor(diffMins / 60);
        return `${hours} hour${hours === 1 ? '' : 's'} ago`;
      } else {
        return date.toLocaleDateString();
      }
    },
    
    // Format duration in seconds
    formatDuration(seconds) {
      if (!seconds) return 'N/A';
      
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      
      if (hours > 0) {
        return `${hours}h ${minutes}m ${remainingSeconds}s`;
      } else if (minutes > 0) {
        return `${minutes}m ${remainingSeconds}s`;
      } else {
        return `${remainingSeconds}s`;
      }
    },
    
    // Get role color
    getRoleColor(role) {
      switch (role) {
        case 'admin':
          return 'success';
        case 'vendor':
          return 'primary';
        case 'client':
          return 'warning';
        default:
          return 'grey';
      }
    },
    
    // Get event type color
    getEventTypeColor(eventType) {
      switch (eventType) {
        case 'login':
          return 'success';
        case 'logout':
          return 'info';
        case 'login_failed':
          return 'error';
        case 'password_reset':
          return 'warning';
        case 'account_locked':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    // Format event type
    formatEventType(eventType) {
      if (!eventType) return 'Unknown';
      
      return eventType
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    },
    
    // Get status color
    getStatusColor(status) {
      switch (status) {
        case 'success':
          return 'success';
        case 'failed':
          return 'error';
        case 'warning':
          return 'warning';
        default:
          return 'grey';
      }
    },
    
    // Apply auth filters
    applyAuthFilters() {
      // Reset offset when applying new filters
      this.authFilters.offset = 0;
      
      this.$emit('search', this.authFilters);
    },
    
    // Previous page
    previousPage() {
      if (this.authFilters.offset >= this.authFilters.limit) {
        this.authFilters.offset -= this.authFilters.limit;
        this.$emit('search', this.authFilters);
      }
    },
    
    // Next page
    nextPage() {
      this.authFilters.offset += this.authFilters.limit;
      this.$emit('search', this.authFilters);
    },
    
    // Show session details
    showSessionDetails(session) {
      this.selectedSession = session;
      this.sessionDialog = true;
    },
    
    // Handle period change
    periodChanged() {
      this.$emit('refresh', { period: this.selectedPeriod });
    }
  }
};
</script>

<style scoped>
.user-activity {
  padding: 16px;
}

.activity-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metric-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  height: 100%;
}

.metric-icon {
  margin-right: 16px;
}

.metric-details {
  flex: 1;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 4px;
}

.metric-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.period-selector {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.chart-container {
  margin-top: 16px;
}

.chart-title {
  margin-bottom: 16px;
  font-size: 1.1rem;
  font-weight: 500;
}

.chart-wrapper {
  height: 300px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-email {
  font-weight: 500;
}

.user-role {
  margin-top: 4px;
}

.location-info {
  display: flex;
  flex-direction: column;
}

.ip-address {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
  margin-top: 4px;
}

.unknown-user {
  font-style: italic;
  color: rgba(0, 0, 0, 0.6);
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
}

.pagination-info {
  margin: 0 16px;
  color: rgba(0, 0, 0, 0.6);
}
</style>
