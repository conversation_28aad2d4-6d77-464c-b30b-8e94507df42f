/**
 * LLM API
 *
 * This module provides API endpoints for LLM integration.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { llmService, LLMProvider, LLMMessageRole } from '../../services/llm/llm-service';

// Create logger instance
const apiLogger = new Logger();

/**
 * Create conversation handler
 *
 * @param req Request
 * @param res Response
 */
async function createConversation(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { title, model, initialMessages, metadata } = req.body;

    // Create conversation
    try {
      const conversation = await llmService.createConversation(
        session.user.id,
        title,
        model,
        initialMessages,
        metadata,
      );

      // Return success response
      res.status(201).json({
        success: true,
        data: conversation,
      });
    } catch (error) {
      logger.error('Error creating conversation', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'CONVERSATION_CREATION_ERROR',
          message: 'Failed to create conversation',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in createConversation', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Get conversations handler
 *
 * @param req Request
 * @param res Response
 */
async function getConversations(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    // Get conversations
    try {
      const conversations = await llmService.getConversationsByUser(session.user.id);

      // Return success response
      res.status(200).json({
        success: true,
        data: conversations,
      });
    } catch (error) {
      logger.error('Error getting conversations', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'QUERY_ERROR',
          message: 'Failed to get conversations',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in getConversations', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Send message handler
 *
 * @param req Request
 * @param res Response
 */
async function sendMessage(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { conversationId, message, functions, function_call } = req.body;

    // Get conversation
    try {
      const conversation = await llmService.getConversation(conversationId);

      // Check if user is authorized to access this conversation
      if (conversation.user_id !== session.user.id && session.user.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'You are not authorized to access this conversation',
          },
        });
        return;
      }

      // Add user message to conversation
      const userMessage = {
        role: LLMMessageRole.USER,
        content: message,
      };

      await llmService.addMessageToConversation(conversationId, userMessage);

      // Send request to LLM
      const llmResponse = await llmService.sendRequest({
        model: conversation.model,
        messages: [...conversation.messages, userMessage],
        functions,
        function_call,
        stream: false,
      });

      // Add assistant message to conversation
      await llmService.addMessageToConversation(conversationId, llmResponse.message);

      // Return success response
      res.status(200).json({
        success: true,
        data: {
          message: llmResponse.message,
          usage: llmResponse.usage,
        },
      });
    } catch (error) {
      logger.error('Error sending message', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'MESSAGE_ERROR',
          message: 'Failed to send message',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in sendMessage', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Handler for LLM API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'POST':
      if (req.query.action === 'send') {
        await sendMessage(req, res);
      } else {
        await createConversation(req, res);
      }
      break;
    case 'GET':
      await getConversations(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
