-- Enhance asset management tables
-- This migration adds asset_versions table and enhances existing tables

-- Create asset_versions table to track asset versions
CREATE TABLE IF NOT EXISTS asset_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  version TEXT NOT NULL,
  hash TEXT NOT NULL,
  url TEXT NOT NULL,
  size INTEGER NOT NULL,
  is_current BOOLEAN NOT NULL DEFAULT false,
  metadata JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(asset_id, version)
);

-- Create asset_dependencies table to track dependencies between assets
CREATE TABLE IF NOT EXISTS asset_dependencies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  dependency_id UUID NOT NULL REFERENCES assets(id) ON DELETE RESTRICT,
  is_required BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(asset_id, dependency_id)
);

-- Create asset_tags table for better asset organization and searching
CREATE TABLE IF NOT EXISTS asset_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  tag TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(asset_id, tag)
);

-- Add description column to asset_bundles if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_name = 'asset_bundles' AND column_name = 'description'
  ) THEN
    ALTER TABLE asset_bundles ADD COLUMN description TEXT;
  END IF;
END $$;

-- Apply updated_at trigger to new tables
CREATE TRIGGER update_asset_versions_updated_at
BEFORE UPDATE ON asset_versions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_asset_dependencies_updated_at
BEFORE UPDATE ON asset_dependencies
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on new tables
ALTER TABLE asset_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE asset_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE asset_tags ENABLE ROW LEVEL SECURITY;

-- Create policies for asset_versions table
CREATE POLICY "Asset versions are viewable by authenticated users" ON asset_versions
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Asset versions are editable by admins" ON asset_versions
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for asset_dependencies table
CREATE POLICY "Asset dependencies are viewable by authenticated users" ON asset_dependencies
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Asset dependencies are editable by admins" ON asset_dependencies
  FOR ALL USING (auth.role() = 'admin');

-- Create policies for asset_tags table
CREATE POLICY "Asset tags are viewable by authenticated users" ON asset_tags
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Asset tags are editable by admins" ON asset_tags
  FOR ALL USING (auth.role() = 'admin');

-- Create indexes for performance
CREATE INDEX idx_asset_versions_asset_id ON asset_versions(asset_id);
CREATE INDEX idx_asset_versions_is_current ON asset_versions(is_current);
CREATE INDEX idx_asset_dependencies_asset_id ON asset_dependencies(asset_id);
CREATE INDEX idx_asset_dependencies_dependency_id ON asset_dependencies(dependency_id);
CREATE INDEX idx_asset_tags_asset_id ON asset_tags(asset_id);
CREATE INDEX idx_asset_tags_tag ON asset_tags(tag);

-- Add function to get latest version of an asset
CREATE OR REPLACE FUNCTION get_latest_asset_version(p_asset_id UUID)
RETURNS TABLE (
  id UUID,
  asset_id UUID,
  version TEXT,
  hash TEXT,
  url TEXT,
  size INTEGER,
  is_current BOOLEAN,
  metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM asset_versions
  WHERE asset_id = p_asset_id
  ORDER BY created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Add function to get all assets in a bundle with their latest versions
CREATE OR REPLACE FUNCTION get_bundle_assets(p_bundle_id UUID)
RETURNS TABLE (
  asset_id UUID,
  name TEXT,
  type TEXT,
  description TEXT,
  thumbnail_url TEXT,
  version TEXT,
  hash TEXT,
  url TEXT,
  size INTEGER,
  metadata JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id AS asset_id,
    a.name,
    a.type,
    a.description,
    a.thumbnail_url,
    v.version,
    v.hash,
    v.url,
    v.size,
    v.metadata
  FROM 
    asset_bundles b
  JOIN 
    unnest(b.asset_ids) AS bundle_asset_id ON true
  JOIN 
    assets a ON a.id = bundle_asset_id
  LEFT JOIN 
    asset_versions v ON v.asset_id = a.id AND v.is_current = true
  WHERE 
    b.id = p_bundle_id;
END;
$$ LANGUAGE plpgsql;
