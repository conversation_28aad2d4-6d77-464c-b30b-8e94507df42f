<template>
  <div class="activity-widget">
    <div v-if="!activities || activities.length === 0" class="no-data">
      <v-icon size="48" color="grey lighten-1">mdi-clock-outline</v-icon>
      <p class="mt-2 grey--text">No activity data available</p>
    </div>
    
    <div v-else class="activity-list">
      <v-list two-line dense>
        <v-list-item v-for="(activity, index) in limitedActivities" :key="index">
          <v-list-item-avatar>
            <v-icon :color="getActivityColor(activity.type)">{{ getActivityIcon(activity.type) }}</v-icon>
          </v-list-item-avatar>
          
          <v-list-item-content>
            <v-list-item-title>{{ activity.message }}</v-list-item-title>
            <v-list-item-subtitle v-if="settings.showTimestamp">{{ formatTimestamp(activity.timestamp) }}</v-list-item-subtitle>
          </v-list-item-content>
          
          <v-list-item-action v-if="settings.showActions">
            <v-btn icon x-small :to="getActivityLink(activity)">
              <v-icon small>mdi-arrow-right</v-icon>
            </v-btn>
          </v-list-item-action>
        </v-list-item>
      </v-list>
      
      <div v-if="settings.showViewAll" class="text-center mt-2">
        <v-btn text color="primary" small to="/admin/activity">
          View All Activity
        </v-btn>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ActivityWidget',
  
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    settings: {
      type: Object,
      default: () => ({
        limit: 5,
        showTimestamp: true,
        showActions: true,
        showViewAll: true
      })
    }
  },
  
  computed: {
    activities() {
      if (!this.data.activities) {
        return this.getDefaultActivities();
      }
      
      return this.data.activities;
    },
    
    limitedActivities() {
      const limit = this.settings.limit || 5;
      return this.activities.slice(0, limit);
    }
  },
  
  methods: {
    getActivityIcon(type) {
      switch (type) {
        case 'user':
          return 'mdi-account';
        case 'vendor':
          return 'mdi-store';
        case 'showroom':
          return 'mdi-eye';
        case 'product':
          return 'mdi-package-variant-closed';
        case 'system':
          return 'mdi-cog';
        case 'login':
          return 'mdi-login';
        case 'logout':
          return 'mdi-logout';
        case 'create':
          return 'mdi-plus-circle';
        case 'update':
          return 'mdi-pencil';
        case 'delete':
          return 'mdi-delete';
        default:
          return 'mdi-information';
      }
    },
    
    getActivityColor(type) {
      switch (type) {
        case 'user':
          return 'primary';
        case 'vendor':
          return 'success';
        case 'showroom':
          return 'info';
        case 'product':
          return 'warning';
        case 'system':
          return 'grey';
        case 'login':
          return 'green';
        case 'logout':
          return 'orange';
        case 'create':
          return 'green';
        case 'update':
          return 'blue';
        case 'delete':
          return 'red';
        default:
          return 'grey';
      }
    },
    
    formatTimestamp(timestamp) {
      if (!timestamp) return 'N/A';
      
      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      
      if (diffMins < 60) {
        return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
      } else if (diffMins < 1440) {
        const hours = Math.floor(diffMins / 60);
        return `${hours} hour${hours === 1 ? '' : 's'} ago`;
      } else {
        return date.toLocaleDateString();
      }
    },
    
    getActivityLink(activity) {
      if (!activity.link) {
        // Generate default links based on activity type
        switch (activity.type) {
          case 'user':
            return '/admin/users';
          case 'vendor':
            return '/admin/vendors';
          case 'showroom':
            return '/admin/showrooms';
          case 'product':
            return '/admin/products';
          case 'system':
            return '/admin/system-monitoring';
          default:
            return '/admin/activity';
        }
      }
      
      return activity.link;
    },
    
    getDefaultActivities() {
      return [
        {
          type: 'user',
          message: 'New user registered: <EMAIL>',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
        },
        {
          type: 'vendor',
          message: 'New vendor registered: Acme Furniture',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          type: 'showroom',
          message: 'New showroom created: Modern Living Room',
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
        },
        {
          type: 'product',
          message: 'New product added: Ergonomic Office Chair',
          timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()
        },
        {
          type: 'system',
          message: 'System update completed successfully',
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
        }
      ];
    }
  }
};
</script>

<style scoped>
.activity-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.activity-list {
  flex: 1;
  overflow-y: auto;
}
</style>
