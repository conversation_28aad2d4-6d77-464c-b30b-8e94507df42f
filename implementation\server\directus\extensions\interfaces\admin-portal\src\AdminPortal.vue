<template>
  <div class="admin-portal">
    <v-app>
      <v-navigation-drawer
        v-model="drawer"
        app
        clipped
        :mini-variant="miniVariant"
      >
        <v-list>
          <v-list-item @click="miniVariant = !miniVariant">
            <v-list-item-icon>
              <v-icon>{{ miniVariant ? 'mdi-chevron-right' : 'mdi-chevron-left' }}</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>Collapse</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          
          <v-divider></v-divider>
          
          <v-list-item
            v-for="(item, i) in menuItems"
            :key="i"
            :to="item.to"
            :exact="item.exact"
            link
          >
            <v-list-item-icon>
              <v-icon>{{ item.icon }}</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>{{ item.title }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-navigation-drawer>
      
      <v-app-bar
        app
        clipped-left
        color="primary"
        dark
      >
        <v-app-bar-nav-icon @click.stop="drawer = !drawer"></v-app-bar-nav-icon>
        <v-toolbar-title>MVS-VR Admin Portal</v-toolbar-title>
        <v-spacer></v-spacer>
        
        <v-btn icon>
          <v-icon>mdi-bell</v-icon>
        </v-btn>
        
        <v-menu
          left
          bottom
          offset-y
        >
          <template v-slot:activator="{ on, attrs }">
            <v-btn
              icon
              v-bind="attrs"
              v-on="on"
            >
              <v-avatar size="32">
                <v-img src="https://cdn.vuetifyjs.com/images/john.jpg" alt="Admin"></v-img>
              </v-avatar>
            </v-btn>
          </template>
          
          <v-list>
            <v-list-item link>
              <v-list-item-icon>
                <v-icon>mdi-account</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title>Profile</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            
            <v-list-item link>
              <v-list-item-icon>
                <v-icon>mdi-cog</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title>Settings</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            
            <v-divider></v-divider>
            
            <v-list-item link>
              <v-list-item-icon>
                <v-icon>mdi-logout</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title>Logout</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </v-menu>
      </v-app-bar>
      
      <v-main>
        <v-container fluid>
          <router-view></router-view>
        </v-container>
      </v-main>
      
      <v-footer app>
        <span>&copy; {{ new Date().getFullYear() }} MVS-VR</span>
      </v-footer>
    </v-app>
  </div>
</template>

<script>
import { AdminDashboard, UserManagement } from './components';

export default {
  name: 'AdminPortal',
  
  components: {
    AdminDashboard,
    UserManagement
  },
  
  data() {
    return {
      drawer: true,
      miniVariant: false,
      menuItems: [
        {
          title: 'Dashboard',
          icon: 'mdi-view-dashboard',
          to: '/admin/dashboard',
          exact: true
        },
        {
          title: 'Users',
          icon: 'mdi-account-group',
          to: '/admin/users',
          exact: false
        },
        {
          title: 'Vendors',
          icon: 'mdi-store',
          to: '/admin/vendors',
          exact: false
        },
        {
          title: 'System Monitoring',
          icon: 'mdi-monitor-dashboard',
          to: '/admin/system-monitoring',
          exact: false
        },
        {
          title: 'Analytics',
          icon: 'mdi-chart-bar',
          to: '/admin/analytics',
          exact: false
        },
        {
          title: 'Settings',
          icon: 'mdi-cog',
          to: '/admin/settings',
          exact: false
        }
      ]
    };
  }
};
</script>

<style scoped>
.admin-portal {
  height: 100%;
}
</style>
