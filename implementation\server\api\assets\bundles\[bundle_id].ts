import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { AssetBundleService } from '../../../services/asset/asset-bundle-service';

// Initialize asset bundle service
const assetBundleService = new AssetBundleService(supabase);

/**
 * Get asset bundle by ID
 *
 * @param req - Request
 * @param res - Response
 */
export const getAssetBundle = async (req: Request, res: Response): Promise<void> => {
  try {
    const { bundle_id } = req.params;
    const { include_assets } = req.query;

    // Validate bundle ID
    if (!bundle_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_BUNDLE_ID',
          message: 'Bundle ID is required',
        },
      });
      return;
    }

    // Get bundle
    let bundle;
    if (include_assets === 'true') {
      bundle = await assetBundleService.getAssetBundleWithAssets(bundle_id);
    } else {
      bundle = await assetBundleService.getAssetBundle(bundle_id);
    }

    if (!bundle) {
      res.status(404).json({
        success: false,
        error: {
          code: 'BUNDLE_NOT_FOUND',
          message: 'Asset bundle not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: bundle,
    });
  } catch (error) {
    logger.error('Error getting asset bundle', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Update an asset bundle
 *
 * @param req - Request
 * @param res - Response
 */
export const updateAssetBundle = async (req: Request, res: Response): Promise<void> => {
  try {
    const { bundle_id } = req.params;
    const { asset_ids, name, description } = req.body;

    // Validate bundle ID
    if (!bundle_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_BUNDLE_ID',
          message: 'Bundle ID is required',
        },
      });
      return;
    }

    // Validate asset IDs
    if (!asset_ids || !Array.isArray(asset_ids) || asset_ids.length === 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ASSET_IDS',
          message: 'Asset IDs must be a non-empty array',
        },
      });
      return;
    }

    // Update bundle
    const bundle = await assetBundleService.updateAssetBundle(
      bundle_id,
      asset_ids,
      name,
      description,
    );

    if (!bundle) {
      res.status(404).json({
        success: false,
        error: {
          code: 'BUNDLE_UPDATE_FAILED',
          message: 'Failed to update asset bundle',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: bundle,
    });
  } catch (error) {
    logger.error('Error updating asset bundle', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Delete an asset bundle
 *
 * @param req - Request
 * @param res - Response
 */
export const deleteAssetBundle = async (req: Request, res: Response): Promise<void> => {
  try {
    const { bundle_id } = req.params;

    // Validate bundle ID
    if (!bundle_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_BUNDLE_ID',
          message: 'Bundle ID is required',
        },
      });
      return;
    }

    // Delete bundle
    const success = await assetBundleService.deleteAssetBundle(bundle_id);

    if (!success) {
      res.status(404).json({
        success: false,
        error: {
          code: 'BUNDLE_DELETION_FAILED',
          message: 'Failed to delete asset bundle',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        message: 'Asset bundle deleted successfully',
      },
    });
  } catch (error) {
    logger.error('Error deleting asset bundle', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
