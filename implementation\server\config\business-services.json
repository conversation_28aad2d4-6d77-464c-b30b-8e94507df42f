{"services": [{"id": "api-gateway", "name": "API Gateway", "description": "Main API gateway handling all external requests", "type": "core", "priority": 1, "healthEndpoint": "http://localhost:3000/health/api-gateway", "dependencies": ["authentication", "database"], "businessImpact": {"revenue": 5, "customer": 5, "operational": 5, "compliance": 4, "reputation": 5}, "sla": {"availability": 99.95, "responseTime": 100, "criticalRecoveryTime": 15, "unhealthyRecoveryTime": 10, "degradedRecoveryTime": 5}, "rto": 15, "rpo": 5, "recoveryProcedures": ["Check load balancer health", "Verify database connectivity", "Restart API gateway service", "Validate authentication service", "Run health checks"], "resources": {"personnel": 2, "infrastructure": "high-availability", "budget": "high", "expertise": "senior", "tools": ["kubernetes", "monitoring", "logging", "alerting"]}, "contacts": [{"role": "primary", "name": "API Team Lead", "email": "<EMAIL>", "phone": "******-0101"}]}, {"id": "authentication", "name": "Authentication Service", "description": "User authentication and authorization service", "type": "core", "priority": 1, "healthEndpoint": "http://localhost:3001/health", "dependencies": ["database", "user-directory"], "businessImpact": {"revenue": 5, "customer": 5, "operational": 4, "compliance": 5, "reputation": 4}, "sla": {"availability": 99.9, "responseTime": 200, "criticalRecoveryTime": 30, "unhealthyRecoveryTime": 20, "degradedRecoveryTime": 10}, "rto": 30, "rpo": 10, "recoveryProcedures": ["Check user directory connectivity", "Verify database access", "Restart authentication service", "Clear authentication cache", "Test login functionality"], "resources": {"personnel": 2, "infrastructure": "high-availability", "budget": "high", "expertise": "senior", "tools": ["o<PERSON>h", "ldap", "monitoring", "security-tools"]}, "contacts": [{"role": "primary", "name": "Security Team Lead", "email": "<EMAIL>", "phone": "******-0102"}]}, {"id": "database", "name": "Primary Database", "description": "Main application database", "type": "infrastructure", "priority": 1, "healthEndpoint": "http://localhost:3002/health", "dependencies": ["storage"], "businessImpact": {"revenue": 5, "customer": 5, "operational": 5, "compliance": 5, "reputation": 4}, "sla": {"availability": 99.99, "responseTime": 50, "criticalRecoveryTime": 60, "unhealthyRecoveryTime": 45, "degradedRecoveryTime": 30}, "rto": 60, "rpo": 15, "recoveryProcedures": ["Check database cluster status", "Verify storage connectivity", "Run database integrity checks", "<PERSON>ore from backup if needed", "Validate data consistency"], "resources": {"personnel": 3, "infrastructure": "enterprise", "budget": "critical", "expertise": "expert", "tools": ["database-tools", "backup-tools", "monitoring", "clustering"]}, "contacts": [{"role": "primary", "name": "Database Administrator", "email": "<EMAIL>", "phone": "******-0103"}]}, {"id": "storage", "name": "Storage Service", "description": "File and asset storage service", "type": "infrastructure", "priority": 2, "healthEndpoint": "http://localhost:3003/health", "dependencies": [], "businessImpact": {"revenue": 4, "customer": 4, "operational": 5, "compliance": 3, "reputation": 3}, "sla": {"availability": 99.9, "responseTime": 300, "criticalRecoveryTime": 120, "unhealthyRecoveryTime": 90, "degradedRecoveryTime": 60}, "rto": 120, "rpo": 30, "recoveryProcedures": ["Check storage cluster health", "Verify network connectivity", "Run storage integrity checks", "Restore from backup storage", "Validate file accessibility"], "resources": {"personnel": 2, "infrastructure": "distributed", "budget": "high", "expertise": "senior", "tools": ["storage-tools", "backup-tools", "monitoring", "replication"]}, "contacts": [{"role": "primary", "name": "Storage Administrator", "email": "<EMAIL>", "phone": "******-0104"}]}, {"id": "user-directory", "name": "User Directory Service", "description": "LDAP/Active Directory service for user management", "type": "supporting", "priority": 2, "healthEndpoint": "http://localhost:3004/health", "dependencies": [], "businessImpact": {"revenue": 3, "customer": 4, "operational": 4, "compliance": 5, "reputation": 3}, "sla": {"availability": 99.5, "responseTime": 500, "criticalRecoveryTime": 180, "unhealthyRecoveryTime": 120, "degradedRecoveryTime": 90}, "rto": 180, "rpo": 60, "recoveryProcedures": ["Check directory server status", "Verify replication health", "Restart directory service", "Synchronize with backup directory", "Test user lookups"], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "medium", "expertise": "standard", "tools": ["ldap-tools", "monitoring", "backup-tools"]}, "contacts": [{"role": "primary", "name": "Identity Administrator", "email": "<EMAIL>", "phone": "******-0105"}]}, {"id": "vendor-portal", "name": "Vendor Portal", "description": "Portal for vendor access and management", "type": "core", "priority": 2, "healthEndpoint": "http://localhost:3005/health", "dependencies": ["api-gateway", "authentication", "database"], "businessImpact": {"revenue": 4, "customer": 5, "operational": 4, "compliance": 3, "reputation": 4}, "sla": {"availability": 99.5, "responseTime": 1000, "criticalRecoveryTime": 240, "unhealthyRecoveryTime": 180, "degradedRecoveryTime": 120}, "rto": 240, "rpo": 60, "recoveryProcedures": ["Check API gateway connectivity", "Verify authentication service", "Restart vendor portal service", "Clear application cache", "Test vendor login flow"], "resources": {"personnel": 2, "infrastructure": "standard", "budget": "medium", "expertise": "standard", "tools": ["web-server", "monitoring", "caching"]}, "contacts": [{"role": "primary", "name": "Vendor Portal Manager", "email": "<EMAIL>", "phone": "******-0106"}]}, {"id": "admin-portal", "name": "Admin Portal", "description": "Administrative interface for system management", "type": "supporting", "priority": 3, "healthEndpoint": "http://localhost:3006/health", "dependencies": ["api-gateway", "authentication", "database"], "businessImpact": {"revenue": 2, "customer": 2, "operational": 5, "compliance": 4, "reputation": 2}, "sla": {"availability": 99.0, "responseTime": 2000, "criticalRecoveryTime": 480, "unhealthyRecoveryTime": 360, "degradedRecoveryTime": 240}, "rto": 480, "rpo": 120, "recoveryProcedures": ["Check API gateway connectivity", "Verify admin authentication", "Restart admin portal service", "Clear admin cache", "Test admin functionality"], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "low", "expertise": "standard", "tools": ["web-server", "monitoring"]}, "contacts": [{"role": "primary", "name": "System Administrator", "email": "<EMAIL>", "phone": "******-0107"}]}, {"id": "monitoring", "name": "Monitoring Service", "description": "System monitoring and alerting service", "type": "supporting", "priority": 2, "healthEndpoint": "http://localhost:3007/health", "dependencies": ["database"], "businessImpact": {"revenue": 2, "customer": 2, "operational": 5, "compliance": 3, "reputation": 2}, "sla": {"availability": 99.0, "responseTime": 1000, "criticalRecoveryTime": 300, "unhealthyRecoveryTime": 240, "degradedRecoveryTime": 180}, "rto": 300, "rpo": 60, "recoveryProcedures": ["Check monitoring agent status", "Verify database connectivity", "Restart monitoring service", "Reconfigure alert rules", "Test alert delivery"], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "medium", "expertise": "standard", "tools": ["monitoring-tools", "alerting", "dashboards"]}, "contacts": [{"role": "primary", "name": "Monitoring Engineer", "email": "<EMAIL>", "phone": "******-0108"}]}, {"id": "backup", "name": "Backup Service", "description": "Automated backup and recovery service", "type": "supporting", "priority": 2, "healthEndpoint": "http://localhost:3008/health", "dependencies": ["storage"], "businessImpact": {"revenue": 3, "customer": 3, "operational": 4, "compliance": 5, "reputation": 3}, "sla": {"availability": 98.0, "responseTime": 5000, "criticalRecoveryTime": 600, "unhealthyRecoveryTime": 480, "degradedRecoveryTime": 360}, "rto": 600, "rpo": 240, "recoveryProcedures": ["Check backup job status", "Verify storage connectivity", "Restart backup service", "Run backup integrity check", "Test restore functionality"], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "medium", "expertise": "standard", "tools": ["backup-tools", "scheduling", "monitoring"]}, "contacts": [{"role": "primary", "name": "Backup Administrator", "email": "<EMAIL>", "phone": "******-0109"}]}, {"id": "notification", "name": "Notification Service", "description": "Email and SMS notification service", "type": "supporting", "priority": 3, "healthEndpoint": "http://localhost:3009/health", "dependencies": ["external-email", "external-sms"], "businessImpact": {"revenue": 2, "customer": 4, "operational": 3, "compliance": 2, "reputation": 3}, "sla": {"availability": 95.0, "responseTime": 3000, "criticalRecoveryTime": 720, "unhealthyRecoveryTime": 600, "degradedRecoveryTime": 480}, "rto": 720, "rpo": 300, "recoveryProcedures": ["Check external service connectivity", "Verify notification queue", "Restart notification service", "Clear stuck messages", "Test notification delivery"], "resources": {"personnel": 1, "infrastructure": "standard", "budget": "low", "expertise": "standard", "tools": ["queue-management", "monitoring"]}, "contacts": [{"role": "primary", "name": "Communications Manager", "email": "<EMAIL>", "phone": "******-0110"}]}, {"id": "external-email", "name": "External Email Service", "description": "Third-party email delivery service", "type": "external", "priority": 4, "healthEndpoint": null, "dependencies": [], "businessImpact": {"revenue": 1, "customer": 3, "operational": 2, "compliance": 1, "reputation": 2}, "sla": {"availability": 99.0, "responseTime": 5000, "criticalRecoveryTime": 1440, "unhealthyRecoveryTime": 1080, "degradedRecoveryTime": 720}, "rto": 1440, "rpo": 720, "recoveryProcedures": ["Contact email service provider", "Check service status page", "Switch to backup email provider", "Update DNS records if needed", "Test email delivery"], "resources": {"personnel": 1, "infrastructure": "external", "budget": "low", "expertise": "standard", "tools": ["vendor-management"]}, "contacts": [{"role": "vendor", "name": "Email Service Provider", "email": "<EMAIL>", "phone": "******-EMAIL"}]}, {"id": "external-sms", "name": "External SMS Service", "description": "Third-party SMS delivery service", "type": "external", "priority": 4, "healthEndpoint": null, "dependencies": [], "businessImpact": {"revenue": 1, "customer": 2, "operational": 2, "compliance": 1, "reputation": 2}, "sla": {"availability": 95.0, "responseTime": 10000, "criticalRecoveryTime": 1440, "unhealthyRecoveryTime": 1080, "degradedRecoveryTime": 720}, "rto": 1440, "rpo": 720, "recoveryProcedures": ["Contact SMS service provider", "Check service status page", "Switch to backup SMS provider", "Update API credentials", "Test SMS delivery"], "resources": {"personnel": 1, "infrastructure": "external", "budget": "low", "expertise": "standard", "tools": ["vendor-management"]}, "contacts": [{"role": "vendor", "name": "SMS Service Provider", "email": "<EMAIL>", "phone": "******-TEXTSMS"}]}]}