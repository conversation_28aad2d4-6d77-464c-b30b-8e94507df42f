/**
 * Directus Security Configuration
 *
 * This file contains security configurations for Directus.
 * It is loaded by the Directus server at startup.
 */

module.exports = {
  // Security headers
  headers: {
    // Content Security Policy
    'Content-Security-Policy':
      "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self'; connect-src 'self' https://*.supabase.co wss://*.supabase.co; frame-ancestors 'none';",

    // X-Content-Type-Options
    'X-Content-Type-Options': 'nosniff',

    // X-Frame-Options
    'X-Frame-Options': 'DENY',

    // X-XSS-Protection
    'X-XSS-Protection': '1; mode=block',

    // Referrer-Policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',

    // Permissions-Policy
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), interest-cohort=()',

    // Strict-Transport-Security
    'Strict-Transport-Security': 'max-age=63072000; includeSubDomains; preload',
  },

  // Rate limiting
  rateLimiter: {
    enabled: true,
    store: 'memory', // Use 'redis' in production
    points: 50, // Number of points
    duration: 1, // Per second

    // Endpoints with custom rate limits
    customEndpoints: [
      {
        path: '/auth/login',
        points: 10,
        duration: 60, // 10 requests per minute
      },
      {
        path: '/auth/refresh',
        points: 10,
        duration: 60, // 10 requests per minute
      },
      {
        path: '/auth/password/request',
        points: 5,
        duration: 60, // 5 requests per minute
      },
    ],
  },

  // Authentication
  auth: {
    // Refresh token expiration (in seconds)
    refreshTokenExpiration: 604800, // 7 days

    // Access token expiration (in seconds)
    accessTokenExpiration: 900, // 15 minutes

    // Password policy
    passwordPolicy: {
      minLength: 10,
      requireNumeric: true,
      requireSpecialChar: true,
      requireUppercase: true,
      requireLowercase: true,
    },

    // Login attempts before lockout
    maxLoginAttempts: 5,

    // Lockout duration (in seconds)
    lockoutDuration: 900, // 15 minutes

    // Two-factor authentication
    twoFactor: {
      enabled: true,
      enforced: false, // Set to true to enforce 2FA for all users
    },
  },

  // CORS configuration
  cors: {
    enabled: true,
    origin: '*', // Restrict in production
    methods: ['GET', 'POST', 'PATCH', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control'],
    exposedHeaders: ['Content-Range', 'X-Total-Count'],
    credentials: true,
    maxAge: 86400, // 24 hours
  },

  // IP access control
  ipAccessControl: {
    enabled: false,
    allowList: [], // List of allowed IPs
    denyList: [], // List of denied IPs
  },

  // Audit logging
  auditLog: {
    enabled: true,
    // Actions to log
    actions: [
      'create',
      'update',
      'delete',
      'login',
      'logout',
      'request-password-reset',
      'reset-password',
      'update-password',
    ],
  },

  // File upload security
  fileUpload: {
    // Maximum file size (in bytes)
    maxFileSize: 100 * 1024 * 1024, // 100 MB

    // Allowed MIME types
    allowedMimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'application/pdf',
      'application/json',
      'application/gltf-binary',
      'model/gltf-binary',
      'model/gltf+json',
      'application/octet-stream',
    ],

    // Scan files for viruses (requires ClamAV)
    scanForViruses: false,
  },

  // Database security
  database: {
    // Query timeout (in milliseconds)
    queryTimeout: 30000, // 30 seconds

    // Maximum number of items per page
    maxLimit: 1000,
  },
};
