# Jest to Vitest Migration Guide

This document provides guidance on migrating tests from <PERSON><PERSON> to Vites<PERSON> in the MVS-VR project.

## Why Migrate to Vitest?

- **Performance**: Vitest is significantly faster than Jest, especially for large test suites
- **Vite Integration**: Vitest is built on top of Vite, which provides fast HMR and bundling
- **ESM Support**: Better support for ES modules
- **Jest Compatibility**: Vitest provides a Jest-compatible API, making migration easier
- **Watch Mode**: Improved watch mode for continuous testing during development
- **Coverage Reporting**: Built-in coverage reporting with v8

## Migration Steps

### 1. Update Dependencies

Add Vitest and related packages to your `package.json`:

```bash
npm install -D vitest @vitest/coverage-v8
```

For Vue components, also install:

```bash
npm install -D @vitejs/plugin-vue2 jsdom
```

### 2. Create Vitest Configuration

Create a `vitest.config.ts` or `vitest.config.js` file:

```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    environment: 'node', // or 'jsdom' for browser-like environment
    globals: true,
    setupFiles: ['./tests/vitest.setup.ts'],
    include: ['**/*.test.ts', '**/*.spec.ts'],
    exclude: ['**/node_modules/**', '**/dist/**'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './'),
    },
  },
});
```

For Vue components:

```typescript
// vitest.config.ts for Vue components
import { defineConfig } from 'vitest/config';
import { createVuePlugin } from 'vite-plugin-vue2';

export default defineConfig({
  plugins: [createVuePlugin()],
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./vitest.setup.js'],
    deps: {
      inline: ['vue', 'vue-router'],
    },
  },
  resolve: {
    alias: {
      '@': '/src',
    },
  },
});
```

### 3. Create Setup Files

Create a setup file for Vitest:

```typescript
// vitest.setup.ts
import { beforeAll, afterAll, expect, vi } from 'vitest';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Set up global hooks
beforeAll(() => {
  // Extend timeout for integration tests
  vi.setConfig({ testTimeout: 30000 });
});

afterAll(() => {
  // Cleanup resources
});

// Custom matchers
expect.extend({
  // Your custom matchers here
});
```

### 4. Update package.json Scripts

Update your `package.json` scripts to use Vitest:

```json
{
  "scripts": {
    "test": "vitest run",
    "test:unit": "vitest run --dir tests/unit",
    "test:integration": "vitest run --dir tests/integration",
    "test:coverage": "vitest run --coverage",
    "test:watch": "vitest"
  }
}
```

### 5. Migrate Test Files

#### Import Statements

Replace Jest imports with Vitest imports:

```typescript
// Before (Jest)
// No imports needed, globals available

// After (Vitest)
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
```

#### Mocking

Replace Jest mocks with Vitest mocks:

```typescript
// Before (Jest)
jest.mock('./module');
jest.spyOn(object, 'method').mockImplementation(() => 'mocked');
jest.useFakeTimers();
jest.advanceTimersByTime(1000);

// After (Vitest)
vi.mock('./module');
vi.spyOn(object, 'method').mockImplementation(() => 'mocked');
vi.useFakeTimers();
vi.advanceTimersByTime(1000);
```

#### Assertions

Most Jest assertions work the same in Vitest:

```typescript
// These work the same in Jest and Vitest
expect(value).toBe(expected);
expect(value).toEqual(expected);
expect(value).toContain(item);
expect(fn).toHaveBeenCalled();
```

#### Snapshot Testing

Snapshot testing works the same in Vitest:

```typescript
// Works the same in Jest and Vitest
expect(component).toMatchSnapshot();
```

### 6. CI/CD Integration

Update your CI/CD configuration to use Vitest:

```yaml
# GitHub Actions example
- name: Run tests
  run: npm test -- --coverage --reporter=junit --outputFile=junit.xml
```

## Common Issues and Solutions

### Module Resolution

If you encounter module resolution issues, check your `vitest.config.ts` aliases:

```typescript
resolve: {
  alias: {
    '@': path.resolve(__dirname, './src'),
  },
}
```

### JSDOM Environment

For component tests, ensure you're using the JSDOM environment:

```typescript
test: {
  environment: 'jsdom',
  // ...
}
```

### Vue Component Testing

For Vue component tests, use the appropriate plugin:

```typescript
// For Vue 2
import { createVuePlugin } from 'vite-plugin-vue2';
plugins: [createVuePlugin()],

// For Vue 3
import vue from '@vitejs/plugin-vue';
plugins: [vue()],
```

## Resources

- [Vitest Documentation](https://vitest.dev/)
- [Vitest Migration Guide](https://vitest.dev/guide/migration.html)
- [Vite Plugin Vue2](https://github.com/underfin/vite-plugin-vue2)

## MVS-VR Implementation

We've implemented the migration in the vendor portal extension with the following steps:

1. Created migration scripts in `directus/extensions/interfaces/vendor-portal/scripts/`:
   - `jest-to-vitest-migration.js`: Converts Jest syntax to Vitest
   - `run-migration.js`: Runs the migration and updates package.json

2. Added Vitest configuration files:
   - `vitest.config.js`: Configuration for Vitest
   - `vitest.setup.js`: Setup file for the testing environment

3. Created example test files using Vitest:
   - `GuidedSetupWizard.vitest.js`: Test for the GuidedSetupWizard component
   - `CompanyProfileStep.vitest.js`: Test for the CompanyProfileStep component

4. Updated package.json scripts to use Vitest

### Running the Tests

To run the tests with Vitest:

```bash
cd directus/extensions/interfaces/vendor-portal
npx vitest run
```

### Known Issues

- Some tests may fail due to differences in how Vue Test Utils works with Vitest
- Mock implementations may need to be updated to use the Vitest API
- Component lifecycle methods (mount/unmount) differ between Jest and Vitest
