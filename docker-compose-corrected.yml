version: '3.8'

services:
  # Authentication Service
  auth-service:
    image: mvs-vr-v2-auth-service:20250530-135418
    restart: always
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=staging
      - PORT=3005
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      - auth-data:/app/data
    networks:
      - mvs-network
    depends_on:
      - redis

  # API Gateway
  api-gateway:
    image: mvs-vr-v2-api:20250530-135418
    restart: always
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=staging
      - PORT=4000
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
    networks:
      - mvs-network
    depends_on:
      - auth-service
      - redis

  # Asset Service
  asset-service:
    image: mvs-vr-v2-asset-service:20250530-135418
    restart: always
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=5000
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - STORAGE_BUCKET=assets
      - STORAGE_URL=${STORAGE_URL}
    volumes:
      - asset-data:/app/data
      - asset-uploads:/app/uploads
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:5000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Blueprint Service
  blueprint-service:
    image: mvs-vr-v2-blueprint-service:20250530-135418
    restart: always
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=3003
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
    volumes:
      - blueprint-data:/app/data
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:3003/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # LLM Service
  llm-service:
    image: mvs-vr-v2-llm-service:20250530-135418
    restart: always
    ports:
      - "7000:7000"
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=7000
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - LLM_PROVIDER=${LLM_PROVIDER}
      - LLM_MODEL=${LLM_MODEL}
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:7000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring Service
  monitoring-service:
    image: mvs-vr-v2-monitoring-service:20250530-135418
    restart: always
    ports:
      - "9090:9090"
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=9090
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:9090/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Analytics Service
  analytics-service:
    image: mvs-vr-v2-analytics-service:20250530-135418
    restart: always
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=staging
      - SERVICE_PORT=8000
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - REDIS_URL=redis://:9elskdUeo@I!@redis:6379
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:8000/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Directus CMS
  directus:
    image: mvs-vr-v2-directus:20250530-135418
    restart: always
    ports:
      - "8055:8055"
    environment:
      - KEY=${DIRECTUS_KEY}
      - SECRET=${DIRECTUS_SECRET}
      - ADMIN_EMAIL=${DIRECTUS_ADMIN_EMAIL}
      - ADMIN_PASSWORD=${DIRECTUS_ADMIN_PASSWORD}
      - DB_CLIENT=pg
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_DATABASE=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - PUBLIC_URL=https://admin.mvs.kanousai.com
      - CORS_ENABLED=true
      - CORS_ORIGIN=true
    volumes:
      - directus-uploads:/directus/uploads
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost:8055/server/health']
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass 9elskdUeo@I!
    volumes:
      - redis-data:/data
    networks:
      - mvs-network
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - mvs-network
    depends_on:
      - api-gateway
      - directus
    healthcheck:
      test: ['CMD', 'wget', '--no-verbose', '--tries=1', '--spider', 'http://localhost/health']
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  auth-data:
  asset-data:
  asset-uploads:
  blueprint-data:
  directus-uploads:
  redis-data:

networks:
  mvs-network:
    driver: bridge
