-- Migration: Enhance LLM Service
-- Description: This migration adds tables for conversation history and LLM usage tracking

-- Create conversation_history table
CREATE TABLE IF NOT EXISTS conversation_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  conversation_id UUID NOT NULL,
  user_id UUID,
  request JSONB NOT NULL,
  response JSONB NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_conversation_history_conversation
    FOREIGN KEY (conversation_id)
    REFERENCES llm_conversations(id)
    ON DELETE CASCADE,
    
  CONSTRAINT fk_conversation_history_user
    FOREIGN KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE SET NULL
);

-- Create index on conversation_history
CREATE INDEX idx_conversation_history_conversation_id ON conversation_history(conversation_id);
CREATE INDEX idx_conversation_history_user_id ON conversation_history(user_id);
CREATE INDEX idx_conversation_history_timestamp ON conversation_history(timestamp);

-- Create llm_usage table
CREATE TABLE IF NOT EXISTS llm_usage (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID,
  vendor_id UUID,
  provider VARCHAR(50) NOT NULL,
  prompt_tokens INTEGER NOT NULL DEFAULT 0,
  completion_tokens INTEGER NOT NULL DEFAULT 0,
  total_tokens INTEGER NOT NULL DEFAULT 0,
  cached BOOLEAN NOT NULL DEFAULT FALSE,
  request_id UUID NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_llm_usage_user
    FOREIGN KEY (user_id)
    REFERENCES auth.users(id)
    ON DELETE SET NULL,
    
  CONSTRAINT fk_llm_usage_vendor
    FOREIGN KEY (vendor_id)
    REFERENCES vendors(id)
    ON DELETE SET NULL
);

-- Create index on llm_usage
CREATE INDEX idx_llm_usage_user_id ON llm_usage(user_id);
CREATE INDEX idx_llm_usage_vendor_id ON llm_usage(vendor_id);
CREATE INDEX idx_llm_usage_provider ON llm_usage(provider);
CREATE INDEX idx_llm_usage_timestamp ON llm_usage(timestamp);

-- Add RLS policies for conversation_history
ALTER TABLE conversation_history ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own conversation history
CREATE POLICY conversation_history_select_policy
  ON conversation_history
  FOR SELECT
  USING (
    auth.uid() = user_id OR
    auth.uid() IN (
      SELECT user_id FROM vendors WHERE id IN (
        SELECT vendor_id FROM llm_conversations WHERE id = conversation_id
      )
    ) OR
    auth.jwt() ->> 'role' = 'admin'
  );

-- Policy: Users can only insert their own conversation history
CREATE POLICY conversation_history_insert_policy
  ON conversation_history
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id OR
    auth.jwt() ->> 'role' = 'admin'
  );

-- Add RLS policies for llm_usage
ALTER TABLE llm_usage ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own usage
CREATE POLICY llm_usage_select_policy
  ON llm_usage
  FOR SELECT
  USING (
    auth.uid() = user_id OR
    auth.uid() IN (
      SELECT user_id FROM vendors WHERE id = vendor_id
    ) OR
    auth.jwt() ->> 'role' = 'admin'
  );

-- Policy: System can insert usage records
CREATE POLICY llm_usage_insert_policy
  ON llm_usage
  FOR INSERT
  WITH CHECK (
    auth.uid() = user_id OR
    auth.jwt() ->> 'role' = 'admin'
  );

-- Add new columns to llm_conversations table
ALTER TABLE llm_conversations
  ADD COLUMN IF NOT EXISTS last_message_at TIMESTAMPTZ,
  ADD COLUMN IF NOT EXISTS message_count INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS total_tokens INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS is_archived BOOLEAN DEFAULT FALSE;

-- Update existing conversations
UPDATE llm_conversations
SET 
  last_message_at = updated_at,
  message_count = COALESCE(jsonb_array_length(messages), 0),
  total_tokens = 0,
  is_archived = FALSE;

-- Create function to update conversation stats
CREATE OR REPLACE FUNCTION update_conversation_stats()
RETURNS TRIGGER AS $$
BEGIN
  NEW.message_count = COALESCE(jsonb_array_length(NEW.messages), 0);
  NEW.last_message_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update conversation stats
CREATE TRIGGER update_conversation_stats_trigger
BEFORE UPDATE ON llm_conversations
FOR EACH ROW
WHEN (OLD.messages IS DISTINCT FROM NEW.messages)
EXECUTE FUNCTION update_conversation_stats();

-- Create function to track LLM usage
CREATE OR REPLACE FUNCTION track_llm_usage()
RETURNS TRIGGER AS $$
DECLARE
  last_message JSONB;
  tokens_used INTEGER;
BEGIN
  -- Get the last message
  IF jsonb_array_length(NEW.messages) > 0 THEN
    last_message := NEW.messages->-1;
    
    -- Estimate tokens (simple approximation)
    tokens_used := length(last_message->>'content') / 4;
    
    -- Update total tokens
    NEW.total_tokens := COALESCE(OLD.total_tokens, 0) + tokens_used;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to track LLM usage
CREATE TRIGGER track_llm_usage_trigger
BEFORE UPDATE ON llm_conversations
FOR EACH ROW
WHEN (OLD.messages IS DISTINCT FROM NEW.messages)
EXECUTE FUNCTION track_llm_usage();
