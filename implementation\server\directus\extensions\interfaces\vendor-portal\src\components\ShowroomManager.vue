<template>
  <div class="showroom-manager">
    <div class="header">
      <h2>Showroom Management</h2>
      <div class="actions">
        <button class="btn btn-primary" @click="createShowroom">
          <i class="material-icons">add</i> Create Showroom
        </button>
        <div class="search">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search showrooms..."
            @input="filterShowrooms"
          />
          <i class="material-icons">search</i>
        </div>
      </div>
    </div>

    <div class="filters">
      <div class="filter">
        <label>Status:</label>
        <select v-model="statusFilter" @change="filterShowrooms">
          <option value="all">All</option>
          <option value="published">Published</option>
          <option value="draft">Draft</option>
          <option value="archived">Archived</option>
        </select>
      </div>
      <div class="filter">
        <label>Sort By:</label>
        <select v-model="sortBy" @change="filterShowrooms">
          <option value="name">Name</option>
          <option value="date_created">Date Created</option>
          <option value="date_updated">Last Updated</option>
          <option value="visits">Visits</option>
        </select>
      </div>
      <div class="filter">
        <label>Order:</label>
        <select v-model="sortOrder" @change="filterShowrooms">
          <option value="asc">Ascending</option>
          <option value="desc">Descending</option>
        </select>
      </div>
      <div class="view-toggle">
        <button
          class="btn btn-icon"
          :class="{ 'active': viewMode === 'grid' }"
          @click="viewMode = 'grid'"
        >
          <i class="material-icons">grid_view</i>
        </button>
        <button
          class="btn btn-icon"
          :class="{ 'active': viewMode === 'list' }"
          @click="viewMode = 'list'"
        >
          <i class="material-icons">view_list</i>
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading showrooms...</span>
    </div>

    <div v-else-if="filteredShowrooms.length === 0" class="empty-state">
      <i class="material-icons">view_in_ar</i>
      <h3>No showrooms found</h3>
      <p v-if="searchQuery || statusFilter !== 'all'">No showrooms match your search criteria. Try adjusting your filters.</p>
      <p v-else>You haven't created any showrooms yet. Click "Create Showroom" to get started.</p>
    </div>

    <!-- Grid View -->
    <div v-else-if="viewMode === 'grid'" class="showroom-grid">
      <div
        v-for="showroom in filteredShowrooms"
        :key="showroom.id"
        class="showroom-card"
        :class="{ 'showroom-card--draft': showroom.status === 'draft', 'showroom-card--archived': showroom.status === 'archived' }"
      >
        <div class="showroom-thumbnail">
          <img v-if="showroom.thumbnail" :src="showroom.thumbnail" :alt="showroom.name">
          <div v-else class="placeholder-thumbnail">
            <i class="material-icons">view_in_ar</i>
          </div>
          <div class="showroom-status" :class="showroom.status">
            {{ showroom.status }}
          </div>
        </div>
        <div class="showroom-info">
          <h3>{{ showroom.name }}</h3>
          <p class="showroom-description">{{ showroom.description }}</p>
          <div class="showroom-meta">
            <div class="meta-item">
              <i class="material-icons">visibility</i>
              <span>{{ showroom.visits }} visits</span>
            </div>
            <div class="meta-item">
              <i class="material-icons">schedule</i>
              <span>Avg. {{ showroom.avg_time }}</span>
            </div>
            <div class="meta-item">
              <i class="material-icons">touch_app</i>
              <span>{{ showroom.interactions }} interactions</span>
            </div>
          </div>
          <div class="showroom-dates">
            <span>Created: {{ formatDate(showroom.date_created) }}</span>
            <span>Updated: {{ formatDate(showroom.date_updated) }}</span>
          </div>
        </div>
        <div class="showroom-actions">
          <button class="btn btn-icon" @click="viewShowroom(showroom)">
            <i class="material-icons">visibility</i>
          </button>
          <button class="btn btn-icon" @click="viewAnalytics(showroom)">
            <i class="material-icons">insights</i>
          </button>
          <button class="btn btn-icon" @click="editShowroom(showroom)">
            <i class="material-icons">edit</i>
          </button>
          <button v-if="showroom.status !== 'published'" class="btn btn-icon" @click="publishShowroom(showroom)">
            <i class="material-icons">publish</i>
          </button>
          <button v-else class="btn btn-icon" @click="unpublishShowroom(showroom)">
            <i class="material-icons">unpublished</i>
          </button>
          <button class="btn btn-icon" @click="confirmDeleteShowroom(showroom)">
            <i class="material-icons">delete</i>
          </button>
        </div>
      </div>
    </div>

    <!-- List View -->
    <div v-else class="showroom-list">
      <table class="showroom-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Status</th>
            <th>Visits</th>
            <th>Avg. Time</th>
            <th>Interactions</th>
            <th>Last Updated</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="showroom in filteredShowrooms" :key="showroom.id">
            <td class="showroom-name-cell">
              <div class="showroom-name-wrapper">
                <div class="thumbnail-small">
                  <img v-if="showroom.thumbnail" :src="showroom.thumbnail" :alt="showroom.name">
                  <div v-else class="placeholder-thumbnail-small">
                    <i class="material-icons">view_in_ar</i>
                  </div>
                </div>
                <div>
                  <div class="showroom-name">{{ showroom.name }}</div>
                  <div class="showroom-description-small">{{ showroom.description }}</div>
                </div>
              </div>
            </td>
            <td>
              <div class="status-badge" :class="showroom.status">
                {{ showroom.status }}
              </div>
            </td>
            <td>{{ showroom.visits }}</td>
            <td>{{ showroom.avg_time }}</td>
            <td>{{ showroom.interactions }}</td>
            <td>{{ formatDate(showroom.date_updated) }}</td>
            <td>
              <div class="table-actions">
                <button class="btn btn-icon" @click="viewShowroom(showroom)">
                  <i class="material-icons">visibility</i>
                </button>
                <button class="btn btn-icon" @click="viewAnalytics(showroom)">
                  <i class="material-icons">insights</i>
                </button>
                <button class="btn btn-icon" @click="editShowroom(showroom)">
                  <i class="material-icons">edit</i>
                </button>
                <button v-if="showroom.status !== 'published'" class="btn btn-icon" @click="publishShowroom(showroom)">
                  <i class="material-icons">publish</i>
                </button>
                <button v-else class="btn btn-icon" @click="unpublishShowroom(showroom)">
                  <i class="material-icons">unpublished</i>
                </button>
                <button class="btn btn-icon" @click="confirmDeleteShowroom(showroom)">
                  <i class="material-icons">delete</i>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Create Showroom Modal -->
    <div v-if="showCreateModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Create New Showroom</h3>
          <button class="btn btn-icon" @click="closeCreateModal">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="showroom-name">Showroom Name</label>
            <input
              id="showroom-name"
              type="text"
              v-model="showroomForm.name"
              placeholder="Enter showroom name"
              required
            />
          </div>
          <div class="form-group">
            <label for="showroom-description">Description</label>
            <textarea
              id="showroom-description"
              v-model="showroomForm.description"
              placeholder="Enter showroom description"
              rows="3"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="showroom-thumbnail">Thumbnail Image</label>
            <div class="file-upload">
              <input
                id="showroom-thumbnail"
                type="file"
                accept="image/*"
                @change="handleThumbnailUpload"
              />
              <div class="file-upload-preview">
                <img v-if="thumbnailPreview" :src="thumbnailPreview" alt="Thumbnail preview" />
                <div v-else class="upload-placeholder">
                  <i class="material-icons">cloud_upload</i>
                  <span>Click to upload an image</span>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="showroom-status">Initial Status</label>
            <select id="showroom-status" v-model="showroomForm.status">
              <option value="draft">Draft</option>
              <option value="published">Published</option>
            </select>
          </div>
          <div class="form-group">
            <label>Template</label>
            <div class="template-selection">
              <div
                v-for="(template, index) in templates"
                :key="index"
                class="template-option"
                :class="{ 'selected': showroomForm.template === template.id }"
                @click="showroomForm.template = template.id"
              >
                <div class="template-preview">
                  <img v-if="template.thumbnail" :src="template.thumbnail" :alt="template.name">
                  <div v-else class="template-preview-placeholder">
                    <i class="material-icons">view_in_ar</i>
                  </div>
                </div>
                <div class="template-info">
                  <div class="template-name">{{ template.name }}</div>
                  <div class="template-description">{{ template.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeCreateModal">Cancel</button>
          <button
            class="btn btn-primary"
            @click="createShowroom"
            :disabled="!isFormValid"
          >
            Create Showroom
          </button>
        </div>
      </div>
    </div>

    <!-- Edit Showroom Modal -->
    <div v-if="showEditModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Edit Showroom</h3>
          <button class="btn btn-icon" @click="closeEditModal">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="edit-showroom-name">Showroom Name</label>
            <input
              id="edit-showroom-name"
              type="text"
              v-model="showroomForm.name"
              placeholder="Enter showroom name"
              required
            />
          </div>
          <div class="form-group">
            <label for="edit-showroom-description">Description</label>
            <textarea
              id="edit-showroom-description"
              v-model="showroomForm.description"
              placeholder="Enter showroom description"
              rows="3"
            ></textarea>
          </div>
          <div class="form-group">
            <label for="edit-showroom-thumbnail">Thumbnail Image</label>
            <div class="file-upload">
              <input
                id="edit-showroom-thumbnail"
                type="file"
                accept="image/*"
                @change="handleThumbnailUpload"
              />
              <div class="file-upload-preview">
                <img v-if="thumbnailPreview" :src="thumbnailPreview" alt="Thumbnail preview" />
                <div v-else class="upload-placeholder">
                  <i class="material-icons">cloud_upload</i>
                  <span>Click to upload an image</span>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="edit-showroom-status">Status</label>
            <select id="edit-showroom-status" v-model="showroomForm.status">
              <option value="draft">Draft</option>
              <option value="published">Published</option>
              <option value="archived">Archived</option>
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="closeEditModal">Cancel</button>
          <button
            class="btn btn-primary"
            @click="updateShowroom"
            :disabled="!isFormValid"
          >
            Update Showroom
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="modal">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Confirm Delete</h3>
          <button class="btn btn-icon" @click="showDeleteModal = false">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body">
          <p>Are you sure you want to delete the showroom "{{ showroomToDelete?.name }}"?</p>
          <p class="warning">This action cannot be undone.</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showDeleteModal = false">Cancel</button>
          <button class="btn btn-danger" @click="deleteShowroom(showroomToDelete)">Delete Showroom</button>
        </div>
      </div>
    </div>

    <!-- Showroom Configurator Modal -->
    <div v-if="showConfiguratorModal" class="modal modal-large">
      <div class="modal-content modal-content-large">
        <ShowroomConfigurator
          :showroom="selectedShowroom"
          :vendor-id="vendorId"
          @close="closeConfiguratorModal"
          @save="saveShowroomConfiguration"
        />
      </div>
    </div>

    <!-- Showroom Analytics Modal -->
    <div v-if="showAnalyticsModal" class="modal modal-large">
      <div class="modal-content modal-content-large">
        <div class="modal-header">
          <h3>Showroom Analytics</h3>
          <button class="btn btn-icon" @click="closeAnalyticsModal">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="modal-body modal-body-large">
          <ShowroomAnalytics
            :showroom="selectedShowroom"
            :vendor-id="vendorId"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ShowroomConfigurator from './ShowroomConfigurator.vue';
import ShowroomAnalytics from './ShowroomAnalytics.vue';

export default {
  name: 'ShowroomManager',

  components: {
    ShowroomConfigurator,
    ShowroomAnalytics
  },

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      showrooms: [],
      filteredShowrooms: [],
      searchQuery: '',
      statusFilter: 'all',
      sortBy: 'date_updated',
      sortOrder: 'desc',
      viewMode: 'grid',
      showCreateModal: false,
      showEditModal: false,
      showDeleteModal: false,
      showConfiguratorModal: false,
      showAnalyticsModal: false,
      showroomForm: {
        name: '',
        description: '',
        status: 'draft',
        template: null
      },
      thumbnailPreview: null,
      thumbnailFile: null,
      showroomToDelete: null,
      selectedShowroom: null,
      templates: [
        {
          id: 'template_1',
          name: 'Modern Showroom',
          description: 'A clean, modern layout for showcasing products with minimalist design.',
          thumbnail: null
        },
        {
          id: 'template_2',
          name: 'Classic Gallery',
          description: 'Traditional gallery layout with focus on product details and specifications.',
          thumbnail: null
        },
        {
          id: 'template_3',
          name: 'Interactive Experience',
          description: 'Highly interactive showroom with customer engagement features.',
          thumbnail: null
        }
      ]
    };
  },

  computed: {
    isFormValid() {
      return this.showroomForm.name.trim().length > 0;
    }
  },

  mounted() {
    this.loadShowrooms();
  },

  methods: {
    // Load showrooms from API
    async loadShowrooms() {
      this.loading = true;

      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/showrooms?vendor_id=${this.vendorId}`);
        // this.showrooms = response.data;

        // Mock data for demonstration
        setTimeout(() => {
          this.showrooms = [
            {
              id: '1',
              name: 'Main Showroom',
              description: 'Our main virtual showroom featuring our complete product line.',
              status: 'published',
              thumbnail: null,
              date_created: '2023-01-15T10:30:00Z',
              date_updated: '2023-05-10T14:45:00Z',
              visits: 856,
              avg_time: '5:32',
              interactions: 2450
            },
            {
              id: '2',
              name: 'New Collection',
              description: 'Featuring our newest products for the summer season.',
              status: 'draft',
              thumbnail: null,
              date_created: '2023-04-20T09:15:00Z',
              date_updated: '2023-05-15T11:20:00Z',
              visits: 0,
              avg_time: '0:00',
              interactions: 0
            },
            {
              id: '3',
              name: 'Office Furniture',
              description: 'Specialized showroom for our office furniture collection.',
              status: 'published',
              thumbnail: null,
              date_created: '2023-02-10T13:45:00Z',
              date_updated: '2023-04-05T16:30:00Z',
              visits: 392,
              avg_time: '4:15',
              interactions: 1392
            },
            {
              id: '4',
              name: 'Outdoor Collection',
              description: 'Patio and garden furniture for outdoor spaces.',
              status: 'archived',
              thumbnail: null,
              date_created: '2022-06-15T10:00:00Z',
              date_updated: '2022-09-20T14:30:00Z',
              visits: 1245,
              avg_time: '3:45',
              interactions: 3560
            }
          ];

          this.filterShowrooms();
          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading showrooms:', error);
        this.loading = false;
      }
    },

    // Filter showrooms based on search query and filters
    filterShowrooms() {
      let filtered = [...this.showrooms];

      // Apply search filter
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(showroom =>
          showroom.name.toLowerCase().includes(query) ||
          (showroom.description && showroom.description.toLowerCase().includes(query))
        );
      }

      // Apply status filter
      if (this.statusFilter !== 'all') {
        filtered = filtered.filter(showroom => showroom.status === this.statusFilter);
      }

      // Apply sorting
      filtered.sort((a, b) => {
        let valueA = a[this.sortBy];
        let valueB = b[this.sortBy];

        // Handle dates
        if (this.sortBy.includes('date')) {
          valueA = new Date(valueA);
          valueB = new Date(valueB);
        }

        if (this.sortOrder === 'asc') {
          return valueA > valueB ? 1 : -1;
        } else {
          return valueA < valueB ? 1 : -1;
        }
      });

      this.filteredShowrooms = filtered;
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      const date = new Date(dateString);
      return date.toLocaleDateString();
    },

    // View showroom details
    viewShowroom(showroom) {
      console.log('View showroom:', showroom.id);
      // This would navigate to showroom details or emit an event
      this.$emit('view-showroom', showroom.id);
    },

    // View showroom analytics
    viewAnalytics(showroom) {
      console.log('View analytics for showroom:', showroom.id);
      this.selectedShowroom = showroom;
      this.showAnalyticsModal = true;
    },

    // Close analytics modal
    closeAnalyticsModal() {
      this.showAnalyticsModal = false;
      this.selectedShowroom = null;
    },

    // Open showroom configurator for editing
    editShowroom(showroom) {
      console.log('Edit showroom:', showroom.id);
      this.selectedShowroom = showroom;
      this.showConfiguratorModal = true;
    },

    // Open showroom configurator for creation
    createShowroom() {
      this.selectedShowroom = null;
      this.showConfiguratorModal = true;
    },

    // Close configurator modal
    closeConfiguratorModal() {
      this.showConfiguratorModal = false;
      this.selectedShowroom = null;
    },

    // Save showroom configuration
    saveShowroomConfiguration(config) {
      console.log('Save showroom configuration:', config);

      if (this.selectedShowroom) {
        // Update existing showroom
        const index = this.showrooms.findIndex(s => s.id === this.selectedShowroom.id);
        if (index !== -1) {
          this.showrooms[index] = {
            ...this.showrooms[index],
            name: config.name,
            description: config.description,
            status: config.status,
            thumbnail: config.thumbnail,
            date_updated: new Date().toISOString()
          };
        }
      } else {
        // Create new showroom
        const newShowroom = {
          id: `new-${Date.now()}`,
          name: config.name,
          description: config.description,
          status: config.status,
          thumbnail: config.thumbnail,
          date_created: new Date().toISOString(),
          date_updated: new Date().toISOString(),
          visits: 0,
          avg_time: '0:00',
          interactions: 0
        };

        this.showrooms.push(newShowroom);
      }

      // Update filtered showrooms
      this.filterShowrooms();

      // Close modal
      this.closeConfiguratorModal();
    },

    // Publish showroom
    publishShowroom(showroom) {
      console.log('Publish showroom:', showroom.id);
      // This would be replaced with actual API call
      // await axios.post(`/api/showrooms/${showroom.id}/publish`);

      // Mock update
      const index = this.showrooms.findIndex(s => s.id === showroom.id);
      if (index !== -1) {
        this.showrooms[index] = {
          ...this.showrooms[index],
          status: 'published',
          date_updated: new Date().toISOString()
        };
        this.filterShowrooms();
      }
    },

    // Unpublish showroom
    unpublishShowroom(showroom) {
      console.log('Unpublish showroom:', showroom.id);
      // This would be replaced with actual API call
      // await axios.post(`/api/showrooms/${showroom.id}/unpublish`);

      // Mock update
      const index = this.showrooms.findIndex(s => s.id === showroom.id);
      if (index !== -1) {
        this.showrooms[index] = {
          ...this.showrooms[index],
          status: 'draft',
          date_updated: new Date().toISOString()
        };
        this.filterShowrooms();
      }
    },

    // Confirm delete showroom
    confirmDeleteShowroom(showroom) {
      console.log('Confirm delete showroom:', showroom.id);
      this.showroomToDelete = showroom;
      this.showDeleteModal = true;
    },

    // Delete showroom
    deleteShowroom(showroom) {
      if (!showroom) return;

      console.log('Delete showroom:', showroom.id);
      // This would be replaced with actual API call
      // await axios.delete(`/api/showrooms/${showroom.id}`);

      // Mock deletion
      this.showrooms = this.showrooms.filter(s => s.id !== showroom.id);
      this.filterShowrooms();
      this.showDeleteModal = false;
      this.showroomToDelete = null;
    },

    // Handle thumbnail upload
    handleThumbnailUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      this.thumbnailFile = file;

      // Create a preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.thumbnailPreview = e.target.result;
      };
      reader.readAsDataURL(file);
    },

    // Close create modal
    closeCreateModal() {
      this.showCreateModal = false;
      this.resetForm();
    },

    // Close edit modal
    closeEditModal() {
      this.showEditModal = false;
      this.resetForm();
    },

    // Reset form
    resetForm() {
      this.showroomForm = {
        name: '',
        description: '',
        status: 'draft',
        template: null
      };
      this.thumbnailPreview = null;
      this.thumbnailFile = null;
    },

    // Edit showroom
    editShowroom(showroom) {
      console.log('Edit showroom:', showroom.id);

      // Set form data
      this.showroomForm = {
        id: showroom.id,
        name: showroom.name,
        description: showroom.description || '',
        status: showroom.status
      };

      // Set thumbnail preview if available
      if (showroom.thumbnail) {
        this.thumbnailPreview = showroom.thumbnail;
      }

      // Show edit modal
      this.showEditModal = true;
    },

    // Create new showroom
    async createShowroom() {
      if (!this.isFormValid) return;

      console.log('Create showroom:', this.showroomForm);

      try {
        // This would be replaced with actual API call
        // const formData = new FormData();
        // formData.append('name', this.showroomForm.name);
        // formData.append('description', this.showroomForm.description);
        // formData.append('status', this.showroomForm.status);
        // formData.append('template', this.showroomForm.template);
        // if (this.thumbnailFile) {
        //   formData.append('thumbnail', this.thumbnailFile);
        // }
        // formData.append('vendor_id', this.vendorId);
        // const response = await axios.post('/api/showrooms', formData);
        // const newShowroom = response.data;

        // Mock creation
        const newShowroom = {
          id: `new-${Date.now()}`,
          name: this.showroomForm.name,
          description: this.showroomForm.description,
          status: this.showroomForm.status,
          thumbnail: this.thumbnailPreview,
          date_created: new Date().toISOString(),
          date_updated: new Date().toISOString(),
          visits: 0,
          avg_time: '0:00',
          interactions: 0
        };

        // Add to showrooms list
        this.showrooms.push(newShowroom);
        this.filterShowrooms();

        // Close modal and reset form
        this.closeCreateModal();
      } catch (error) {
        console.error('Error creating showroom:', error);
      }
    },

    // Update existing showroom
    async updateShowroom() {
      if (!this.isFormValid || !this.showroomForm.id) return;

      console.log('Update showroom:', this.showroomForm);

      try {
        // This would be replaced with actual API call
        // const formData = new FormData();
        // formData.append('name', this.showroomForm.name);
        // formData.append('description', this.showroomForm.description);
        // formData.append('status', this.showroomForm.status);
        // if (this.thumbnailFile) {
        //   formData.append('thumbnail', this.thumbnailFile);
        // }
        // const response = await axios.patch(`/api/showrooms/${this.showroomForm.id}`, formData);
        // const updatedShowroom = response.data;

        // Mock update
        const index = this.showrooms.findIndex(s => s.id === this.showroomForm.id);
        if (index !== -1) {
          this.showrooms[index] = {
            ...this.showrooms[index],
            name: this.showroomForm.name,
            description: this.showroomForm.description,
            status: this.showroomForm.status,
            thumbnail: this.thumbnailPreview || this.showrooms[index].thumbnail,
            date_updated: new Date().toISOString()
          };
          this.filterShowrooms();
        }

        // Close modal and reset form
        this.closeEditModal();
      } catch (error) {
        console.error('Error updating showroom:', error);
      }
    }
  }
};
</script>

<style scoped>
.showroom-manager {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.search {
  position: relative;
}

.search input {
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  width: 200px;
}

.search i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
  font-size: 18px;
}

.filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: center;
}

.filter {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter label {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.filter select {
  padding: 6px 10px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.view-toggle {
  margin-left: auto;
  display: flex;
  gap: 5px;
}

.view-toggle .btn-icon {
  padding: 6px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
}

.view-toggle .btn-icon.active {
  background-color: var(--theme--primary);
  color: white;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
}

.empty-state i {
  font-size: 48px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 16px;
}

.empty-state h3 {
  margin-bottom: 8px;
  color: var(--theme--foreground);
}

.empty-state p {
  color: var(--theme--foreground-subdued);
  max-width: 400px;
  margin: 0 auto;
}

/* Grid View */
.showroom-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.showroom-card {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background);
  transition: all 0.2s ease;
}

.showroom-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.showroom-card--draft {
  border-left: 4px solid var(--theme--warning);
}

.showroom-card--archived {
  border-left: 4px solid var(--theme--danger);
  opacity: 0.7;
}

.showroom-thumbnail {
  height: 160px;
  background-color: var(--theme--background-subdued);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.showroom-thumbnail img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.placeholder-thumbnail {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.placeholder-thumbnail i {
  font-size: 48px;
  color: var(--theme--foreground-subdued);
}

.showroom-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.showroom-status.published {
  background-color: var(--theme--primary);
  color: white;
}

.showroom-status.draft {
  background-color: var(--theme--warning);
  color: white;
}

.showroom-status.archived {
  background-color: var(--theme--danger);
  color: white;
}

.showroom-info {
  padding: 16px;
}

.showroom-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
}

.showroom-description {
  color: var(--theme--foreground-subdued);
  font-size: 14px;
  margin: 0 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.showroom-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.meta-item i {
  font-size: 14px;
  color: var(--theme--primary);
}

.showroom-dates {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.showroom-actions {
  display: flex;
  justify-content: flex-end;
  padding: 8px 16px 16px;
  gap: 8px;
}

/* List View */
.showroom-list {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.showroom-table {
  width: 100%;
  border-collapse: collapse;
}

.showroom-table th {
  text-align: left;
  padding: 12px 16px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
  font-weight: 600;
  color: var(--theme--foreground-subdued);
}

.showroom-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
  vertical-align: middle;
}

.showroom-table tr:last-child td {
  border-bottom: none;
}

.showroom-name-cell {
  min-width: 250px;
}

.showroom-name-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.thumbnail-small {
  width: 40px;
  height: 40px;
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.thumbnail-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-thumbnail-small {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-thumbnail-small i {
  font-size: 20px;
  color: var(--theme--foreground-subdued);
}

.showroom-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.showroom-description-small {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
}

.status-badge.published {
  background-color: var(--theme--primary);
  color: white;
}

.status-badge.draft {
  background-color: var(--theme--warning);
  color: white;
}

.status-badge.archived {
  background-color: var(--theme--danger);
  color: white;
}

.table-actions {
  display: flex;
  gap: 5px;
  justify-content: flex-end;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-large {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content-large {
  width: 90%;
  height: 90vh;
  max-width: 1200px;
  display: flex;
  flex-direction: column;
}

.modal-body-large {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid var(--theme--border-color);
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--border-color);
}

.btn-icon {
  padding: 6px;
  border-radius: var(--theme--border-radius);
  background-color: transparent;
}

.btn-icon i {
  margin-right: 0;
}

.btn-icon:hover {
  background-color: var(--theme--background-subdued);
}

.btn-danger {
  background-color: var(--theme--danger);
  color: white;
}

.btn-danger:hover {
  background-color: var(--theme--danger-accent);
}

/* Form Styles */
.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.warning {
  color: var(--theme--danger);
  font-weight: 500;
}

/* File Upload */
.file-upload {
  position: relative;
  margin-bottom: 10px;
}

.file-upload input[type="file"] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
  z-index: 2;
}

.file-upload-preview {
  width: 100%;
  height: 160px;
  border: 2px dashed var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: var(--theme--background-subdued);
}

.file-upload-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: var(--theme--foreground-subdued);
}

.upload-placeholder i {
  font-size: 36px;
}

/* Template Selection */
.template-selection {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.template-option {
  border: 2px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
}

.template-option:hover {
  border-color: var(--theme--primary);
}

.template-option.selected {
  border-color: var(--theme--primary);
  background-color: var(--theme--primary-background);
}

.template-preview {
  height: 120px;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.template-preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.template-preview-placeholder i {
  font-size: 36px;
  color: var(--theme--foreground-subdued);
}

.template-info {
  padding: 10px;
}

.template-name {
  font-weight: 600;
  margin-bottom: 5px;
}

.template-description {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
