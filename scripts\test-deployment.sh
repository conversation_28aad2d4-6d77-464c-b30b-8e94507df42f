#!/bin/bash
# Test script for smart deployment functionality
# This script validates the deployment scripts without actually deploying

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test function wrapper
run_test() {
    local test_name="$1"
    local test_function="$2"
    
    echo ""
    log_info "Running test: $test_name"
    
    if $test_function; then
        log_success "PASSED: $test_name"
        ((TESTS_PASSED++))
    else
        log_error "FAILED: $test_name"
        ((TESTS_FAILED++))
    fi
}

# Test 1: Check if required files exist
test_required_files() {
    local required_files=(
        "$PROJECT_DIR/deploy.sh"
        "$SCRIPT_DIR/smart-deploy-staging.sh"
        "$SCRIPT_DIR/smart-deploy-staging.ps1"
        "$PROJECT_DIR/docker-exports/deploy-to-digitalocean.sh"
        "$PROJECT_DIR/docker-exports/export-manifest.json"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Required file missing: $file"
            return 1
        fi
    done
    
    return 0
}

# Test 2: Check if scripts are executable
test_script_permissions() {
    local scripts=(
        "$PROJECT_DIR/deploy.sh"
        "$SCRIPT_DIR/smart-deploy-staging.sh"
        "$PROJECT_DIR/docker-exports/deploy-to-digitalocean.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [[ ! -x "$script" ]]; then
            log_warning "Script not executable: $script"
            # Try to make it executable
            chmod +x "$script" 2>/dev/null || {
                log_error "Cannot make script executable: $script"
                return 1
            }
        fi
    done
    
    return 0
}

# Test 3: Validate manifest file structure
test_manifest_structure() {
    local manifest_file="$PROJECT_DIR/docker-exports/export-manifest.json"
    
    if ! command -v jq >/dev/null 2>&1; then
        log_warning "jq not available, skipping detailed manifest validation"
        # Basic validation without jq
        if grep -q '"version"' "$manifest_file" && grep -q '"images"' "$manifest_file"; then
            return 0
        else
            log_error "Manifest file appears to be malformed"
            return 1
        fi
    fi
    
    # Detailed validation with jq
    if ! jq empty "$manifest_file" 2>/dev/null; then
        log_error "Manifest file is not valid JSON"
        return 1
    fi
    
    # Check required fields
    local required_fields=(".version" ".images" ".export_date")
    for field in "${required_fields[@]}"; do
        if ! jq -e "$field" "$manifest_file" >/dev/null 2>&1; then
            log_error "Manifest missing required field: $field"
            return 1
        fi
    done
    
    # Check if images have required properties
    local image_count
    image_count=$(jq '.images | length' "$manifest_file")
    
    if [[ "$image_count" -eq 0 ]]; then
        log_error "No images found in manifest"
        return 1
    fi
    
    log_info "Found $image_count images in manifest"
    return 0
}

# Test 4: Check if expected Docker export files exist
test_docker_export_files() {
    local manifest_file="$PROJECT_DIR/docker-exports/export-manifest.json"
    local docker_exports_dir="$PROJECT_DIR/docker-exports"
    
    local missing_files=()
    local corrupted_files=()
    
    if command -v jq >/dev/null 2>&1; then
        # Use jq for parsing
        while IFS= read -r filename; do
            if [[ -z "$filename" ]]; then
                continue
            fi
            
            local filepath="$docker_exports_dir/$filename"
            if [[ ! -f "$filepath" ]]; then
                missing_files+=("$filename")
            else
                # Check file size if available in manifest
                local expected_size
                expected_size=$(jq -r ".images[] | select(.file == \"$filename\") | .size" "$manifest_file" 2>/dev/null || echo "0")
                
                if [[ "$expected_size" != "0" ]] && [[ "$expected_size" != "null" ]]; then
                    local actual_size
                    actual_size=$(stat -c%s "$filepath" 2>/dev/null || wc -c < "$filepath" 2>/dev/null || echo "0")
                    
                    if [[ "$actual_size" != "$expected_size" ]]; then
                        corrupted_files+=("$filename (expected: $expected_size, actual: $actual_size)")
                    fi
                fi
            fi
        done < <(jq -r '.images[].file' "$manifest_file" 2>/dev/null)
    else
        # Fallback without jq
        while IFS= read -r filename; do
            if [[ -z "$filename" ]]; then
                continue
            fi
            
            local filepath="$docker_exports_dir/$filename"
            if [[ ! -f "$filepath" ]]; then
                missing_files+=("$filename")
            fi
        done < <(grep '"file"' "$manifest_file" | cut -d'"' -f4)
    fi
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_error "Missing Docker export files:"
        printf '  - %s\n' "${missing_files[@]}"
        return 1
    fi
    
    if [[ ${#corrupted_files[@]} -gt 0 ]]; then
        log_error "Corrupted Docker export files (size mismatch):"
        printf '  - %s\n' "${corrupted_files[@]}"
        return 1
    fi
    
    return 0
}

# Test 5: Validate script syntax
test_script_syntax() {
    local bash_scripts=(
        "$PROJECT_DIR/deploy.sh"
        "$SCRIPT_DIR/smart-deploy-staging.sh"
        "$PROJECT_DIR/docker-exports/deploy-to-digitalocean.sh"
    )
    
    for script in "${bash_scripts[@]}"; do
        if ! bash -n "$script"; then
            log_error "Syntax error in script: $script"
            return 1
        fi
    done
    
    # Test PowerShell script syntax if PowerShell is available
    if command -v powershell >/dev/null 2>&1; then
        if ! powershell -Command "Get-Content '$SCRIPT_DIR/smart-deploy-staging.ps1' | Out-Null"; then
            log_error "Syntax error in PowerShell script: $SCRIPT_DIR/smart-deploy-staging.ps1"
            return 1
        fi
    else
        log_warning "PowerShell not available, skipping PowerShell script syntax check"
    fi
    
    return 0
}

# Test 6: Test dry-run functionality
test_dry_run() {
    log_info "Testing dry-run functionality..."
    
    # Test the smart deployment script in dry-run mode
    if [[ -x "$SCRIPT_DIR/smart-deploy-staging.sh" ]]; then
        # This should not fail even without server connectivity
        if "$SCRIPT_DIR/smart-deploy-staging.sh" --dry-run --help >/dev/null 2>&1; then
            log_info "Smart deployment script help works"
        else
            log_error "Smart deployment script help failed"
            return 1
        fi
    fi
    
    return 0
}

# Test 7: Check required system dependencies
test_system_dependencies() {
    local required_commands=("ssh" "scp" "docker" "docker-compose")
    local optional_commands=("jq" "rsync" "powershell")
    
    local missing_required=()
    local missing_optional=()
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_required+=("$cmd")
        fi
    done
    
    for cmd in "${optional_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            missing_optional+=("$cmd")
        fi
    done
    
    if [[ ${#missing_required[@]} -gt 0 ]]; then
        log_error "Missing required commands:"
        printf '  - %s\n' "${missing_required[@]}"
        return 1
    fi
    
    if [[ ${#missing_optional[@]} -gt 0 ]]; then
        log_warning "Missing optional commands (may limit functionality):"
        printf '  - %s\n' "${missing_optional[@]}"
    fi
    
    return 0
}

# Main test runner
main() {
    echo "========================================"
    echo "Smart Deployment Scripts Test Suite"
    echo "========================================"
    
    run_test "Required files exist" test_required_files
    run_test "Script permissions" test_script_permissions
    run_test "Manifest structure" test_manifest_structure
    run_test "Docker export files" test_docker_export_files
    run_test "Script syntax" test_script_syntax
    run_test "Dry-run functionality" test_dry_run
    run_test "System dependencies" test_system_dependencies
    
    echo ""
    echo "========================================"
    echo "Test Results Summary"
    echo "========================================"
    log_success "Tests passed: $TESTS_PASSED"
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        log_error "Tests failed: $TESTS_FAILED"
        echo ""
        log_error "Some tests failed. Please fix the issues before deploying."
        exit 1
    else
        echo ""
        log_success "All tests passed! The deployment scripts are ready to use."
        echo ""
        echo "Next steps:"
        echo "1. Run a dry-run deployment: ./scripts/smart-deploy-staging.sh --dry-run"
        echo "2. If dry-run looks good, run actual deployment: ./scripts/smart-deploy-staging.sh"
        echo "3. Monitor the deployment logs and service health"
    fi
}

# Run tests
main "$@"
