services:
  auth-service:
    image: mvs-vr-v2-auth-service:20250601-171154
    restart: always
    ports:
      - "3005:3005"
    entrypoint: ["sh", "/app/start-auth-service.sh"]

  api-gateway:
    image: mvs-vr-v2-api-gateway:latest
    restart: always
    ports:
      - "4000:4000"
    depends_on:
      - auth-service
    entrypoint: ["sh", "/app/start-gateway.sh"]

  asset-service:
    image: mvs-vr-v2-asset-service:20250601-171154
    restart: always
    ports:
      - "5000:5000"
    entrypoint: ["sh", "/app/start-asset-service.sh"]

  scene-service:
    image: mvs-vr-v2-scene-service:latest
    restart: always
    ports:
      - "6000:6000"
    entrypoint: ["sh", "/app/start-scene-service.sh"]
