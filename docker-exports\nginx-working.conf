events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # PRIMARY DOMAIN - mvs.kanousai.com (Vendor Login Home Page)
    server {
        listen 80;
        server_name mvs.kanousai.com;

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Production - Healthy
Primary Domain: mvs.kanousai.com (Vendor Login)
API: mvs.kanousai.com/api
System Admin: adminmvs.kanousai.com
Staging: stagingmvs.kanousai.com
";
            add_header Content-Type text/plain;
        }

        # API Gateway - Under main domain as required
        location /api/ {
            return 200 "MVS-VR API Gateway - Ready
Endpoint: mvs.kanousai.com/api
Status: Configured for service integration
";
            add_header Content-Type text/plain;
        }

        # Vendor Portal Routes
        location /vendor/ {
            return 200 "Vendor Portal - Ready for Integration";
            add_header Content-Type text/plain;
        }

        # Vendor UX Home Page - Login Entry Point
        location / {
            return 200 "<!DOCTYPE html>
<html>
<head>
    <title>MVS-VR - Vendor Portal</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .status { background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .links { text-align: center; margin: 20px 0; }
        .links a { color: #007bff; text-decoration: none; margin: 0 10px; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🚀 MVS-VR Vendor Portal</h1>
        <div class='status'>
            <strong>✅ System Status:</strong> Online<br>
            <strong>🌐 Domain:</strong> mvs.kanousai.com<br>
            <strong>📡 Server:</strong> 157.245.103.57<br>
            <strong>🔗 Database:</strong> Supabase Connected
        </div>
        
        <h2>Vendor Login Portal</h2>
        <p>Welcome to the MVS-VR vendor portal. This is your entry point to access the vendor admin backend.</p>
        
        <div class='links'>
            <a href='/api/health'>API Status</a> |
            <a href='http://adminmvs.kanousai.com'>System Admin</a> |
            <a href='http://stagingmvs.kanousai.com'>Staging</a>
        </div>
        
        <p><strong>Note:</strong> This is the staging environment for testing vendor and system admin functionality.</p>
    </div>
</body>
</html>";
            add_header Content-Type text/html;
        }
    }

    # DEFAULT/FALLBACK - Handle IP access and unknown domains
    server {
        listen 80 default_server;
        server_name _;

        # Redirect IP access to main domain
        location / {
            return 301 http://mvs.kanousai.com$request_uri;
        }

        # Health check for IP access
        location /health {
            access_log off;
            return 200 "MVS-VR Infrastructure - Working
Primary: mvs.kanousai.com (Vendor Login)
API: mvs.kanousai.com/api (API Gateway)
System Admin: adminmvs.kanousai.com (System Administrators)
Staging: stagingmvs.kanousai.com (Full System Testing)
";
            add_header Content-Type text/plain;
        }
    }
}
