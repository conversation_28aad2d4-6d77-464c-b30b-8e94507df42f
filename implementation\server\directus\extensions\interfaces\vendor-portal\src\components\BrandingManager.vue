<template>
  <div class="branding-manager">
    <div class="header">
      <h2>Brand Management</h2>
      <div class="actions">
        <button class="btn btn-primary" @click="saveBranding" :disabled="!hasChanges">
          <i class="material-icons">save</i> Save Changes
        </button>
        <button class="btn btn-secondary" @click="resetBranding" :disabled="!hasChanges">
          <i class="material-icons">refresh</i> Reset
        </button>
      </div>
    </div>

    <div v-if="loading" class="loading">
      <div class="spinner"></div>
      <span>Loading brand settings...</span>
    </div>

    <div v-else class="branding-content">
      <div class="branding-form">
        <!-- Color Palette Section -->
        <div class="form-section">
          <h3>Color Palette</h3>
          <div class="color-grid">
            <div class="color-item">
              <label for="primary-color">Primary Color</label>
              <div class="color-picker">
                <input
                  id="primary-color"
                  type="color"
                  v-model="brandingForm.primaryColor"
                  @change="updateBranding"
                />
                <input
                  type="text"
                  v-model="brandingForm.primaryColor"
                  @input="updateBranding"
                />
              </div>
            </div>

            <div class="color-item">
              <label for="secondary-color">Secondary Color</label>
              <div class="color-picker">
                <input
                  id="secondary-color"
                  type="color"
                  v-model="brandingForm.secondaryColor"
                  @change="updateBranding"
                />
                <input
                  type="text"
                  v-model="brandingForm.secondaryColor"
                  @input="updateBranding"
                />
              </div>
            </div>

            <div class="color-item">
              <label for="accent-color">Accent Color</label>
              <div class="color-picker">
                <input
                  id="accent-color"
                  type="color"
                  v-model="brandingForm.accentColor"
                  @change="updateBranding"
                />
                <input
                  type="text"
                  v-model="brandingForm.accentColor"
                  @input="updateBranding"
                />
              </div>
            </div>

            <div class="color-item">
              <label for="text-color">Text Color</label>
              <div class="color-picker">
                <input
                  id="text-color"
                  type="color"
                  v-model="brandingForm.textColor"
                  @change="updateBranding"
                />
                <input
                  type="text"
                  v-model="brandingForm.textColor"
                  @input="updateBranding"
                />
              </div>
            </div>

            <div class="color-item">
              <label for="background-color">Background Color</label>
              <div class="color-picker">
                <input
                  id="background-color"
                  type="color"
                  v-model="brandingForm.backgroundColor"
                  @change="updateBranding"
                />
                <input
                  type="text"
                  v-model="brandingForm.backgroundColor"
                  @input="updateBranding"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Typography Section -->
        <div class="form-section">
          <h3>Typography</h3>
          <div class="form-group">
            <label for="font-family">Primary Font</label>
            <select
              id="font-family"
              v-model="brandingForm.fontFamily"
              @change="updateBranding"
            >
              <option value="Arial, sans-serif">Arial</option>
              <option value="'Helvetica Neue', Helvetica, sans-serif">Helvetica</option>
              <option value="'Open Sans', sans-serif">Open Sans</option>
              <option value="'Roboto', sans-serif">Roboto</option>
              <option value="'Montserrat', sans-serif">Montserrat</option>
              <option value="'Lato', sans-serif">Lato</option>
              <option value="'Poppins', sans-serif">Poppins</option>
              <option value="Georgia, serif">Georgia</option>
              <option value="'Times New Roman', Times, serif">Times New Roman</option>
            </select>
          </div>

          <div class="form-group">
            <label for="heading-font">Heading Font</label>
            <select
              id="heading-font"
              v-model="brandingForm.headingFont"
              @change="updateBranding"
            >
              <option value="inherit">Same as Primary Font</option>
              <option value="Arial, sans-serif">Arial</option>
              <option value="'Helvetica Neue', Helvetica, sans-serif">Helvetica</option>
              <option value="'Open Sans', sans-serif">Open Sans</option>
              <option value="'Roboto', sans-serif">Roboto</option>
              <option value="'Montserrat', sans-serif">Montserrat</option>
              <option value="'Lato', sans-serif">Lato</option>
              <option value="'Poppins', sans-serif">Poppins</option>
              <option value="Georgia, serif">Georgia</option>
              <option value="'Times New Roman', Times, serif">Times New Roman</option>
            </select>
          </div>

          <div class="form-group">
            <label for="base-font-size">Base Font Size</label>
            <div class="input-with-unit">
              <input
                id="base-font-size"
                type="number"
                v-model="brandingForm.baseFontSize"
                min="12"
                max="20"
                @change="updateBranding"
              />
              <span class="unit">px</span>
            </div>
          </div>
        </div>

        <!-- Logo Section -->
        <div class="form-section">
          <h3>Logo</h3>
          <div class="logo-upload">
            <div class="logo-preview">
              <img v-if="logoPreview" :src="logoPreview" alt="Logo preview" />
              <div v-else class="placeholder-logo">
                <i class="material-icons">image</i>
                <span>No logo uploaded</span>
              </div>
            </div>
            <div class="logo-actions">
              <label class="btn btn-primary">
                <i class="material-icons">cloud_upload</i> Upload Logo
                <input
                  type="file"
                  accept="image/*"
                  @change="handleLogoUpload"
                  style="display: none;"
                />
              </label>
              <button
                v-if="logoPreview"
                class="btn btn-secondary"
                @click="removeLogo"
              >
                <i class="material-icons">delete</i> Remove
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Brand Preview Section -->
      <div class="brand-preview">
        <h3>Preview</h3>
        <div
          class="preview-container"
          :style="{
            fontFamily: brandingForm.fontFamily,
            color: brandingForm.textColor,
            backgroundColor: brandingForm.backgroundColor,
            fontSize: `${brandingForm.baseFontSize}px`
          }"
        >
          <div class="preview-header">
            <div class="preview-logo">
              <img v-if="logoPreview" :src="logoPreview" alt="Logo" />
              <div v-else class="preview-company-name">Company Name</div>
            </div>
            <div
              class="preview-nav"
              :style="{ backgroundColor: brandingForm.primaryColor }"
            >
              <div class="preview-nav-item active">Home</div>
              <div class="preview-nav-item">Products</div>
              <div class="preview-nav-item">About</div>
              <div class="preview-nav-item">Contact</div>
            </div>
          </div>

          <div class="preview-content">
            <h1
              :style="{
                fontFamily: brandingForm.headingFont !== 'inherit' ? brandingForm.headingFont : brandingForm.fontFamily,
                color: brandingForm.primaryColor
              }"
            >
              Welcome to Our Showroom
            </h1>

            <p>This is a preview of how your branding will look in your virtual showroom.</p>

            <div
              class="preview-button primary"
              :style="{ backgroundColor: brandingForm.primaryColor }"
            >
              Primary Button
            </div>

            <div
              class="preview-button secondary"
              :style="{
                backgroundColor: brandingForm.secondaryColor,
                color: brandingForm.backgroundColor
              }"
            >
              Secondary Button
            </div>

            <div
              class="preview-button accent"
              :style="{ backgroundColor: brandingForm.accentColor }"
            >
              Accent Button
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BrandingManager',

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      loading: true,
      originalBranding: null,
      brandingForm: {
        primaryColor: '#3498db',
        secondaryColor: '#2c3e50',
        accentColor: '#e74c3c',
        textColor: '#333333',
        backgroundColor: '#ffffff',
        fontFamily: "'Open Sans', sans-serif",
        headingFont: 'inherit',
        baseFontSize: 16
      },
      logoPreview: null,
      logoFile: null,
      hasChanges: false
    };
  },

  mounted() {
    this.loadBranding();
  },

  methods: {
    // Load branding from API
    async loadBranding() {
      this.loading = true;

      try {
        // This would be replaced with actual API call
        // const response = await axios.get(`/api/branding?vendor_id=${this.vendorId}`);
        // const branding = response.data;

        // Mock data for demonstration
        setTimeout(() => {
          const branding = {
            primaryColor: '#3498db',
            secondaryColor: '#2c3e50',
            accentColor: '#e74c3c',
            textColor: '#333333',
            backgroundColor: '#ffffff',
            fontFamily: "'Open Sans', sans-serif",
            headingFont: 'inherit',
            baseFontSize: 16,
            logo: null
          };

          this.originalBranding = JSON.parse(JSON.stringify(branding));
          this.brandingForm = JSON.parse(JSON.stringify(branding));
          this.logoPreview = branding.logo;

          this.loading = false;
        }, 1000);
      } catch (error) {
        console.error('Error loading branding:', error);
        this.loading = false;
      }
    },

    // Update branding
    updateBranding() {
      this.checkForChanges();
    },

    // Check if there are unsaved changes
    checkForChanges() {
      const currentBranding = {
        primaryColor: this.brandingForm.primaryColor,
        secondaryColor: this.brandingForm.secondaryColor,
        accentColor: this.brandingForm.accentColor,
        textColor: this.brandingForm.textColor,
        backgroundColor: this.brandingForm.backgroundColor,
        fontFamily: this.brandingForm.fontFamily,
        headingFont: this.brandingForm.headingFont,
        baseFontSize: this.brandingForm.baseFontSize
      };

      // Check if logo has changed
      const logoChanged = (this.logoPreview !== this.originalBranding.logo);

      // Check if any form values have changed
      const formChanged = JSON.stringify(currentBranding) !== JSON.stringify(this.originalBranding);

      this.hasChanges = logoChanged || formChanged;
    },

    // Handle logo upload
    handleLogoUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      this.logoFile = file;

      // Create a preview
      const reader = new FileReader();
      reader.onload = (e) => {
        this.logoPreview = e.target.result;
        this.checkForChanges();
      };
      reader.readAsDataURL(file);
    },

    // Remove logo
    removeLogo() {
      this.logoPreview = null;
      this.logoFile = null;
      this.checkForChanges();
    },

    // Save branding changes
    async saveBranding() {
      try {
        // This would be replaced with actual API call
        // const formData = new FormData();
        // formData.append('primaryColor', this.brandingForm.primaryColor);
        // formData.append('secondaryColor', this.brandingForm.secondaryColor);
        // formData.append('accentColor', this.brandingForm.accentColor);
        // formData.append('textColor', this.brandingForm.textColor);
        // formData.append('backgroundColor', this.brandingForm.backgroundColor);
        // formData.append('fontFamily', this.brandingForm.fontFamily);
        // formData.append('headingFont', this.brandingForm.headingFont);
        // formData.append('baseFontSize', this.brandingForm.baseFontSize);
        // if (this.logoFile) {
        //   formData.append('logo', this.logoFile);
        // } else if (this.logoPreview === null && this.originalBranding.logo !== null) {
        //   formData.append('removeLogo', true);
        // }
        // formData.append('vendor_id', this.vendorId);
        // const response = await axios.post('/api/branding', formData);

        // Mock successful save
        console.log('Saving branding:', {
          ...this.brandingForm,
          logo: this.logoPreview ? '[logo data]' : null
        });

        // Update original branding to reflect saved state
        this.originalBranding = JSON.parse(JSON.stringify(this.brandingForm));
        this.originalBranding.logo = this.logoPreview;

        this.hasChanges = false;

        // Show success message
        alert('Branding settings saved successfully!');
      } catch (error) {
        console.error('Error saving branding:', error);
        alert('Error saving branding settings. Please try again.');
      }
    },

    // Reset branding to last saved state
    resetBranding() {
      this.brandingForm = JSON.parse(JSON.stringify(this.originalBranding));
      this.logoPreview = this.originalBranding.logo;
      this.logoFile = null;
      this.hasChanges = false;
    }
  }
};
</script>

<style scoped>
.branding-manager {
  font-family: var(--theme--font-family-sans-serif);
  color: var(--theme--foreground);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.actions {
  display: flex;
  gap: 10px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--border-color);
  border-top-color: var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.branding-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

@media (max-width: 1200px) {
  .branding-content {
    grid-template-columns: 1fr;
  }
}

.form-section {
  background-color: var(--theme--background);
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  padding: 20px;
  margin-bottom: 20px;
}

.form-section h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

/* Color Palette Styles */
.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.color-item {
  margin-bottom: 15px;
}

.color-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.color-picker {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-picker input[type="color"] {
  width: 40px;
  height: 40px;
  padding: 0;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  cursor: pointer;
}

.color-picker input[type="text"] {
  flex-grow: 1;
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

/* Typography Styles */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-family: var(--theme--font-family-sans-serif);
}

.input-with-unit {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-unit input {
  padding-right: 30px;
}

.unit {
  position: absolute;
  right: 12px;
  color: var(--theme--foreground-subdued);
}

/* Logo Styles */
.logo-upload {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.logo-preview {
  width: 100%;
  height: 150px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: var(--theme--background-subdued);
}

.logo-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.placeholder-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  color: var(--theme--foreground-subdued);
}

.placeholder-logo i {
  font-size: 36px;
}

.logo-actions {
  display: flex;
  gap: 10px;
}

/* Preview Styles */
.brand-preview {
  position: sticky;
  top: 20px;
}

.brand-preview h3 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.preview-container {
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-header {
  padding: 15px;
}

.preview-logo {
  margin-bottom: 15px;
  height: 60px;
  display: flex;
  align-items: center;
}

.preview-logo img {
  max-height: 100%;
  max-width: 200px;
}

.preview-company-name {
  font-size: 24px;
  font-weight: 600;
}

.preview-nav {
  display: flex;
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  color: white;
}

.preview-nav-item {
  padding: 10px 15px;
  cursor: pointer;
}

.preview-nav-item.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.preview-content {
  padding: 20px;
}

.preview-content h1 {
  margin-top: 0;
  margin-bottom: 15px;
}

.preview-content p {
  margin-bottom: 20px;
}

.preview-button {
  display: inline-block;
  padding: 10px 15px;
  border-radius: 4px;
  margin-right: 10px;
  margin-bottom: 10px;
  color: white;
  cursor: pointer;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
}

.btn i {
  font-size: 18px;
  margin-right: 4px;
}

.btn-primary {
  background-color: var(--theme--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme--primary-accent);
}

.btn-secondary {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.btn-secondary:hover {
  background-color: var(--theme--border-color);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
