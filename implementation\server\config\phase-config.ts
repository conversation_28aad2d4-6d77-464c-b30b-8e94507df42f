/**
 * Phase Configuration
 *
 * This file defines the phases of scene development and validation.
 */

// Define phase types
export enum PhaseType {
  PLANNING = 'planning',
  ASSET_CREATION = 'asset_creation',
  SCENE_CONSTRUCTION = 'scene_construction',
  INTERACTION_DEVELOPMENT = 'interaction_development',
  OPTIMIZATION = 'optimization',
  TESTING = 'testing',
  DEPLOYMENT = 'deployment',
  MAINTENANCE = 'maintenance',
  // Allow for custom phases with string literal types
  CUSTOM = 'custom',
}

// Define phase status
export enum PhaseStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  SKIPPED = 'skipped',
}

// Define validation level
export enum ValidationLevel {
  NONE = 'none',
  BASIC = 'basic',
  STANDARD = 'standard',
  STRICT = 'strict',
}

// Define phase configuration
export interface PhaseConfig {
  type: PhaseType;
  name: string;
  description: string;
  order: number;
  validationLevel: ValidationLevel;
  requiredValidations: string[];
  optionalValidations: string[];
  prerequisites: PhaseType[];
  allowSkip: boolean;
  autoTransition: boolean;
  // CI/CD integration
  ciCdEnabled?: boolean;
  ciCdPipeline?: string;
  // Team collaboration
  assignedRoles?: string[];
  // Custom phase properties
  isCustom?: boolean;
  customDefinition?: Record<string, unknown>;
}

// Define phase configurations
export const phaseConfigurations: Record<PhaseType, PhaseConfig> = {
  [PhaseType.PLANNING]: {
    type: PhaseType.PLANNING,
    name: 'Planning',
    description: 'Define scene requirements, structure, and flow',
    order: 1,
    validationLevel: ValidationLevel.BASIC,
    requiredValidations: ['scene_structure', 'flow_validation'],
    optionalValidations: [],
    prerequisites: [],
    allowSkip: false,
    autoTransition: false,
  },
  [PhaseType.ASSET_CREATION]: {
    type: PhaseType.ASSET_CREATION,
    name: 'Asset Creation',
    description: 'Create and import assets for the scene',
    order: 2,
    validationLevel: ValidationLevel.STANDARD,
    requiredValidations: ['asset_validation'],
    optionalValidations: ['performance_analysis'],
    prerequisites: [PhaseType.PLANNING],
    allowSkip: false,
    autoTransition: false,
  },
  [PhaseType.SCENE_CONSTRUCTION]: {
    type: PhaseType.SCENE_CONSTRUCTION,
    name: 'Scene Construction',
    description: 'Build the scene using assets and blueprints',
    order: 3,
    validationLevel: ValidationLevel.STANDARD,
    requiredValidations: ['scene_validation', 'blueprint_validation'],
    optionalValidations: ['performance_analysis', 'compatibility_check'],
    prerequisites: [PhaseType.ASSET_CREATION],
    allowSkip: false,
    autoTransition: false,
  },
  [PhaseType.INTERACTION_DEVELOPMENT]: {
    type: PhaseType.INTERACTION_DEVELOPMENT,
    name: 'Interaction Development',
    description: 'Implement interactions and behaviors',
    order: 4,
    validationLevel: ValidationLevel.STANDARD,
    requiredValidations: ['interaction_validation', 'flow_validation'],
    optionalValidations: ['performance_analysis'],
    prerequisites: [PhaseType.SCENE_CONSTRUCTION],
    allowSkip: false,
    autoTransition: false,
  },
  [PhaseType.OPTIMIZATION]: {
    type: PhaseType.OPTIMIZATION,
    name: 'Optimization',
    description: 'Optimize scene for performance and compatibility',
    order: 5,
    validationLevel: ValidationLevel.STRICT,
    requiredValidations: ['performance_analysis', 'compatibility_check'],
    optionalValidations: ['asset_optimization'],
    prerequisites: [PhaseType.INTERACTION_DEVELOPMENT],
    allowSkip: true,
    autoTransition: false,
  },
  [PhaseType.TESTING]: {
    type: PhaseType.TESTING,
    name: 'Testing',
    description: 'Test scene functionality and performance',
    order: 6,
    validationLevel: ValidationLevel.STRICT,
    requiredValidations: [
      'scene_validation',
      'performance_analysis',
      'compatibility_check',
      'flow_validation',
    ],
    optionalValidations: [],
    prerequisites: [PhaseType.OPTIMIZATION],
    allowSkip: false,
    autoTransition: false,
  },
  [PhaseType.DEPLOYMENT]: {
    type: PhaseType.DEPLOYMENT,
    name: 'Deployment',
    description: 'Prepare scene for deployment',
    order: 7,
    validationLevel: ValidationLevel.STRICT,
    requiredValidations: ['deployment_validation'],
    optionalValidations: [],
    prerequisites: [PhaseType.TESTING],
    allowSkip: false,
    autoTransition: false,
  },
  [PhaseType.MAINTENANCE]: {
    type: PhaseType.MAINTENANCE,
    name: 'Maintenance',
    description: 'Maintain and update the deployed scene',
    order: 8,
    validationLevel: ValidationLevel.STANDARD,
    requiredValidations: ['scene_validation', 'compatibility_check'],
    optionalValidations: ['performance_analysis'],
    prerequisites: [PhaseType.DEPLOYMENT],
    allowSkip: false,
    autoTransition: false,
    ciCdEnabled: true,
    ciCdPipeline: 'maintenance',
    assignedRoles: ['maintainer', 'developer'],
  },
  [PhaseType.CUSTOM]: {
    type: PhaseType.CUSTOM,
    name: 'Custom Phase',
    description: 'Custom phase with user-defined properties',
    order: 999,
    validationLevel: ValidationLevel.NONE,
    requiredValidations: [],
    optionalValidations: [],
    prerequisites: [],
    allowSkip: true,
    autoTransition: false,
    isCustom: true,
    customDefinition: {},
  },
};

// Define validation types
export const validationTypes = {
  scene_structure: {
    name: 'Scene Structure Validation',
    description: 'Validates the basic structure of the scene',
    endpoint: '/api/scenes/validate/:scene_id',
  },
  scene_validation: {
    name: 'Scene Validation',
    description: 'Validates the scene against schema and constraints',
    endpoint: '/api/scenes/validate/:scene_id',
  },
  flow_validation: {
    name: 'Flow Validation',
    description: 'Validates the scene flow configuration',
    endpoint: '/api/scenes/validate/flow',
  },
  asset_validation: {
    name: 'Asset Validation',
    description: 'Validates assets used in the scene',
    endpoint: '/api/scenes/validate/:scene_id/assets',
  },
  blueprint_validation: {
    name: 'Blueprint Validation',
    description: 'Validates blueprint instances in the scene',
    endpoint: '/api/scenes/validate/:scene_id/blueprints',
  },
  interaction_validation: {
    name: 'Interaction Validation',
    description: 'Validates interactions in the scene',
    endpoint: '/api/scenes/validate/:scene_id/interactions',
  },
  performance_analysis: {
    name: 'Performance Analysis',
    description: 'Analyzes scene performance',
    endpoint: '/api/scenes/validate/:scene_id/performance',
  },
  compatibility_check: {
    name: 'Compatibility Check',
    description: 'Checks scene compatibility with target environments',
    endpoint: '/api/scenes/validate/:scene_id/compatibility',
  },
  asset_optimization: {
    name: 'Asset Optimization',
    description: 'Suggests optimizations for assets',
    endpoint: '/api/scenes/validate/:scene_id/assets/optimize',
  },
  deployment_validation: {
    name: 'Deployment Validation',
    description: 'Validates scene for deployment',
    endpoint: '/api/scenes/validate/:scene_id/deployment',
  },
  maintenance_validation: {
    name: 'Maintenance Validation',
    description: 'Validates scene for maintenance updates',
    endpoint: '/api/scenes/validate/:scene_id/maintenance',
  },
  ci_cd_validation: {
    name: 'CI/CD Pipeline Validation',
    description: 'Validates scene through CI/CD pipeline',
    endpoint: '/api/scenes/validate/:scene_id/ci-cd',
  },
  custom_validation: {
    name: 'Custom Validation',
    description: 'Custom validation with user-defined criteria',
    endpoint: '/api/scenes/validate/:scene_id/custom',
  },
};

// Get next phase
export function getNextPhase(currentPhase: PhaseType): PhaseType | null {
  const currentOrder = phaseConfigurations[currentPhase].order;
  const nextPhase = Object.values(phaseConfigurations).find(
    phase => phase.order === currentOrder + 1,
  );
  return nextPhase ? nextPhase.type : null;
}

// Get previous phase
export function getPreviousPhase(currentPhase: PhaseType): PhaseType | null {
  const currentOrder = phaseConfigurations[currentPhase].order;
  const prevPhase = Object.values(phaseConfigurations).find(
    phase => phase.order === currentOrder - 1,
  );
  return prevPhase ? prevPhase.type : null;
}

// Check if phase transition is valid
export function isValidPhaseTransition(
  currentPhase: PhaseType,
  targetPhase: PhaseType,
  completedPhases: PhaseType[],
  userRoles?: string[],
): boolean {
  // Can't transition to the same phase
  if (currentPhase === targetPhase) {
    return false;
  }

  const targetConfig = phaseConfigurations[targetPhase];
  const currentConfig = phaseConfigurations[currentPhase];

  // Special handling for custom phases
  if (targetConfig.isCustom || currentConfig.isCustom) {
    // Custom phases have their own transition rules defined in customDefinition
    return true;
  }

  // Check if all prerequisites are completed
  const hasCompletedPrerequisites = targetConfig.prerequisites.every(prerequisite =>
    completedPhases.includes(prerequisite),
  );

  // Check role-based permissions if roles are provided
  let hasRequiredRole = true;
  if (userRoles && targetConfig.assignedRoles && targetConfig.assignedRoles.length > 0) {
    hasRequiredRole = userRoles.some(role => targetConfig.assignedRoles?.includes(role));
  }

  // Allow transition to the next phase or any completed phase
  return (
    hasCompletedPrerequisites &&
    hasRequiredRole &&
    (targetConfig.order === currentConfig.order + 1 || completedPhases.includes(targetPhase))
  );
}

// Get validation requirements for a phase
export function getPhaseValidationRequirements(phase: PhaseType): {
  required: string[];
  optional: string[];
} {
  const config = phaseConfigurations[phase];
  return {
    required: config.requiredValidations,
    optional: config.optionalValidations,
  };
}

// Get all phases in order
export function getAllPhases(): PhaseConfig[] {
  return Object.values(phaseConfigurations).sort((a, b) => a.order - b.order);
}

// Create a custom phase
export function createCustomPhase(
  name: string,
  description: string,
  order: number,
  validationLevel: ValidationLevel,
  requiredValidations: string[],
  optionalValidations: string[],
  prerequisites: PhaseType[],
  customDefinition: Record<string, unknown>,
): PhaseConfig {
  return {
    type: PhaseType.CUSTOM,
    name,
    description,
    order,
    validationLevel,
    requiredValidations,
    optionalValidations,
    prerequisites,
    allowSkip: true,
    autoTransition: false,
    isCustom: true,
    customDefinition,
  };
}

// Get CI/CD pipeline for a phase
export function getPhaseCiCdPipeline(phase: PhaseType): string | null {
  const config = phaseConfigurations[phase];
  return config.ciCdEnabled && config.ciCdPipeline ? config.ciCdPipeline : null;
}

// Check if a user has permission for a phase
export function hasPhasePermission(phase: PhaseType, userRoles: string[]): boolean {
  const config = phaseConfigurations[phase];

  // If no roles are assigned to the phase, anyone can access it
  if (!config.assignedRoles || config.assignedRoles.length === 0) {
    return true;
  }

  // Check if user has any of the required roles
  return userRoles.some(role => config.assignedRoles?.includes(role));
}

// Get phases accessible by a user role
export function getPhasesByRole(role: string): PhaseConfig[] {
  return Object.values(phaseConfigurations)
    .filter(phase => !phase.assignedRoles || phase.assignedRoles.includes(role))
    .sort((a, b) => a.order - b.order);
}
