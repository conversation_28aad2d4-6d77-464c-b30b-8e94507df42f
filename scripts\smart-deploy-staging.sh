#!/bin/bash
# Smart deployment script for MVS-VR v2 staging environment
# Supports incremental updates, missing file detection, and robust error handling

set -euo pipefail

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Configuration
DEFAULT_TARGET_DIR="/opt/mvs-vr-v2"
DEFAULT_SERVER_IP="**************"
DEFAULT_SERVER_USER="root"
DEFAULT_SSH_KEY="/root/.ssh/id_rsa"
DOCKER_EXPORTS_DIR="$PROJECT_DIR/docker-exports"
MANIFEST_FILE="$DOCKER_EXPORTS_DIR/export-manifest.json"

# Command line options
TARGET_DIR="${TARGET_DIR:-$DEFAULT_TARGET_DIR}"
SERVER_IP="${SERVER_IP:-$DEFAULT_SERVER_IP}"
SERVER_USER="${SERVER_USER:-$DEFAULT_SERVER_USER}"
SSH_KEY="${SSH_KEY:-$DEFAULT_SSH_KEY}"
FORCE_TRANSFER="${FORCE_TRANSFER:-false}"
DRY_RUN="${DRY_RUN:-false}"
VERBOSE="${VERBOSE:-false}"
SKIP_VERIFICATION="${SKIP_VERIFICATION:-false}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${CYAN}[DEBUG]${NC} $1"
    fi
}

# Show help
show_help() {
    cat << EOF
Smart Deployment Script for MVS-VR v2 Staging

Usage: $0 [OPTIONS]

OPTIONS:
    --server-ip IP          Server IP address (default: $DEFAULT_SERVER_IP)
    --server-user USER      SSH user (default: $DEFAULT_SERVER_USER)
    --ssh-key PATH          SSH key path (default: $DEFAULT_SSH_KEY)
    --target-dir DIR        Target directory on server (default: $DEFAULT_TARGET_DIR)
    --force                 Force transfer all files regardless of status
    --dry-run               Show what would be done without executing
    --verbose               Enable verbose output
    --skip-verification     Skip file verification after transfer
    --help                  Show this help message

ENVIRONMENT VARIABLES:
    SERVER_IP               Override default server IP
    SERVER_USER             Override default server user
    SSH_KEY                 Override default SSH key path
    TARGET_DIR              Override default target directory
    FORCE_TRANSFER          Set to 'true' to force transfer all files
    DRY_RUN                 Set to 'true' for dry run mode
    VERBOSE                 Set to 'true' for verbose output

EXAMPLES:
    # Basic deployment
    $0

    # Dry run to see what would be transferred
    $0 --dry-run

    # Force transfer all files
    $0 --force

    # Deploy to different server
    $0 --server-ip ************* --server-user ubuntu

EOF
}

# SSH command wrapper with error handling
ssh_exec() {
    local cmd="$*"
    log_debug "SSH: $cmd"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would execute SSH: $cmd"
        return 0
    fi
    
    if ! ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "$cmd"; then
        log_error "SSH command failed: $cmd"
        return 1
    fi
}

# SCP command wrapper with progress and error handling
scp_copy() {
    local source="$1"
    local filename
    filename=$(basename "$source")
    
    log_debug "SCP: $source -> $SERVER_USER@$SERVER_IP:$TARGET_DIR/$filename"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would copy: $source -> $SERVER_USER@$SERVER_IP:$TARGET_DIR/$filename"
        return 0
    fi
    
    if ! scp -o ConnectTimeout=30 -i "$SSH_KEY" "$source" "$SERVER_USER@$SERVER_IP:$TARGET_DIR/$filename"; then
        log_error "SCP failed for: $filename"
        return 1
    fi
}

# Test server connectivity
test_connection() {
    log_info "Testing connection to $SERVER_USER@$SERVER_IP..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        echo "[DRY RUN] Would test connection"
        return 0
    fi
    
    if ssh_exec "echo 'Connection successful'"; then
        log_success "Server connection established"
        return 0
    else
        log_error "Cannot connect to server"
        return 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if docker-exports directory exists
    if [[ ! -d "$DOCKER_EXPORTS_DIR" ]]; then
        log_error "Docker exports directory not found: $DOCKER_EXPORTS_DIR"
        exit 1
    fi
    
    # Check if manifest file exists
    if [[ ! -f "$MANIFEST_FILE" ]]; then
        log_error "Manifest file not found: $MANIFEST_FILE"
        exit 1
    fi
    
    # Check SSH key
    if [[ ! -f "$SSH_KEY" ]]; then
        log_error "SSH key not found: $SSH_KEY"
        exit 1
    fi
    
    # Check required commands
    for cmd in ssh scp jq; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            if [[ "$cmd" == "jq" ]]; then
                log_warning "jq not found, will use fallback JSON parsing"
            else
                log_error "Required command not found: $cmd"
                exit 1
            fi
        fi
    done
    
    log_success "Prerequisites check passed"
}

# Get file list from manifest
get_expected_files() {
    if command -v jq >/dev/null 2>&1; then
        jq -r '.images[].file' "$MANIFEST_FILE" 2>/dev/null || {
            log_error "Failed to parse manifest with jq"
            return 1
        }
    else
        # Fallback if jq is not available
        grep '"file"' "$MANIFEST_FILE" | cut -d'"' -f4 || {
            log_error "Failed to parse manifest with fallback method"
            return 1
        }
    fi
}

# Get file size from manifest
get_expected_size() {
    local filename="$1"
    if command -v jq >/dev/null 2>&1; then
        jq -r ".images[] | select(.file == \"$filename\") | .size" "$MANIFEST_FILE" 2>/dev/null || echo "0"
    else
        # Fallback parsing
        grep -A 10 "\"file\": \"$filename\"" "$MANIFEST_FILE" | grep '"size"' | head -1 | grep -o '[0-9]*' || echo "0"
    fi
}

# Get version from manifest
get_version() {
    if command -v jq >/dev/null 2>&1; then
        jq -r '.version' "$MANIFEST_FILE" 2>/dev/null || echo "unknown"
    else
        grep '"version"' "$MANIFEST_FILE" | cut -d'"' -f4 || echo "unknown"
    fi
}

# Setup remote environment
setup_remote_environment() {
    log_info "Setting up remote environment..."
    
    # Create target directory
    ssh_exec "mkdir -p $TARGET_DIR"
    
    # Create backup directory
    ssh_exec "mkdir -p $TARGET_DIR/backups"
    
    # Install required packages if needed
    ssh_exec "command -v docker >/dev/null 2>&1 || { echo 'Docker not found on remote server'; exit 1; }"
    ssh_exec "command -v docker-compose >/dev/null 2>&1 || { echo 'Docker Compose not found on remote server'; exit 1; }"
    
    log_success "Remote environment ready"
}

# Check what files exist on remote server
check_remote_files() {
    log_debug "Checking existing files on remote server..."
    
    # Get list of existing files with sizes and modification times
    ssh_exec "cd $TARGET_DIR 2>/dev/null && find . -maxdepth 1 \\( -name '*.tar.zip' -o -name '*.tar' -o -name 'export-manifest.json' \\) -type f -exec sh -c 'echo \"{} \$(stat -c%s \"{}\") \$(stat -c%Y \"{}\")\" 2>/dev/null || echo \"{} 0 0\"' \\; 2>/dev/null || echo ''"
}

# Calculate file checksum
calculate_checksum() {
    local file="$1"
    if [[ -f "$file" ]]; then
        sha256sum "$file" 2>/dev/null | cut -d' ' -f1 || echo "unknown"
    else
        echo "missing"
    fi
}

# Get remote file checksum
get_remote_checksum() {
    local filename="$1"
    ssh_exec "cd $TARGET_DIR && sha256sum '$filename' 2>/dev/null | cut -d' ' -f1 || echo 'missing'"
}

# Compare local and remote files to find what needs updating
find_files_to_transfer() {
    log_info "Analyzing files to determine transfer requirements..."

    local remote_files_info
    remote_files_info=$(check_remote_files)

    local files_to_transfer=()
    local expected_files
    expected_files=$(get_expected_files)

    local total_files=0
    local up_to_date_files=0
    local missing_files=0
    local size_mismatch_files=0
    local checksum_mismatch_files=0

    # Check each expected file
    while IFS= read -r filename; do
        if [[ -z "$filename" ]]; then
            continue
        fi

        ((total_files++))

        local local_file="$DOCKER_EXPORTS_DIR/$filename"
        local expected_size
        expected_size=$(get_expected_size "$filename")

        # Check if local file exists
        if [[ ! -f "$local_file" ]]; then
            log_warning "Local file missing: $filename"
            continue
        fi

        # Get actual local file size
        local actual_local_size
        actual_local_size=$(stat -c%s "$local_file" 2>/dev/null || wc -c < "$local_file" 2>/dev/null || echo "0")

        # Parse remote file info
        local remote_info
        remote_info=$(echo "$remote_files_info" | grep "./$filename " || echo "")

        local remote_size="0"
        local remote_mtime="0"

        if [[ -n "$remote_info" ]]; then
            remote_size=$(echo "$remote_info" | awk '{print $2}')
            remote_mtime=$(echo "$remote_info" | awk '{print $3}')
        fi

        local should_transfer=false
        local reason=""

        # Force transfer if requested
        if [[ "$FORCE_TRANSFER" == "true" ]]; then
            should_transfer=true
            reason="forced"
        # Check if file is missing on remote
        elif [[ "$remote_size" == "0" ]] || [[ -z "$remote_size" ]]; then
            should_transfer=true
            reason="missing on remote"
            ((missing_files++))
        # Check size mismatch
        elif [[ "$actual_local_size" != "$remote_size" ]]; then
            should_transfer=true
            reason="size mismatch (local: $actual_local_size, remote: $remote_size)"
            ((size_mismatch_files++))
        # Check if local file doesn't match expected size from manifest
        elif [[ -n "$expected_size" ]] && [[ "$expected_size" != "0" ]] && [[ "$actual_local_size" != "$expected_size" ]]; then
            log_warning "Local file size doesn't match manifest for $filename (actual: $actual_local_size, expected: $expected_size)"
            should_transfer=true
            reason="local file corrupted"
        # For critical files, also check checksums
        elif [[ "$filename" == *"api-gateway"* ]] || [[ "$filename" == *"auth-service"* ]]; then
            local local_checksum
            local_checksum=$(calculate_checksum "$local_file")
            local remote_checksum
            remote_checksum=$(get_remote_checksum "$filename")

            if [[ "$local_checksum" != "$remote_checksum" ]]; then
                should_transfer=true
                reason="checksum mismatch"
                ((checksum_mismatch_files++))
            else
                ((up_to_date_files++))
            fi
        else
            ((up_to_date_files++))
        fi

        if [[ "$should_transfer" == "true" ]]; then
            log_info "Will transfer $filename: $reason"
            files_to_transfer+=("$filename")
        else
            log_debug "File up to date: $filename"
        fi

    done <<< "$expected_files"

    # Always include manifest file unless specifically skipped
    if [[ "$FORCE_TRANSFER" == "true" ]] || [[ ! -f "$DOCKER_EXPORTS_DIR/export-manifest.json" ]]; then
        files_to_transfer+=("export-manifest.json")
    fi

    # Print summary
    log_info "File analysis summary:"
    log_info "  Total files: $total_files"
    log_info "  Up to date: $up_to_date_files"
    log_info "  Missing on remote: $missing_files"
    log_info "  Size mismatches: $size_mismatch_files"
    log_info "  Checksum mismatches: $checksum_mismatch_files"
    log_info "  Files to transfer: ${#files_to_transfer[@]}"

    printf '%s\n' "${files_to_transfer[@]}"
}

# Transfer files with progress tracking
transfer_files() {
    local files_to_transfer=("$@")

    if [[ ${#files_to_transfer[@]} -eq 0 ]]; then
        log_info "No files need to be transferred"
        return 0
    fi

    log_info "Transferring ${#files_to_transfer[@]} files..."

    local transferred=0
    local failed=0
    local total_size=0

    # Calculate total size
    for filename in "${files_to_transfer[@]}"; do
        local local_file="$DOCKER_EXPORTS_DIR/$filename"
        if [[ -f "$local_file" ]]; then
            local size
            size=$(stat -c%s "$local_file" 2>/dev/null || echo "0")
            ((total_size += size))
        fi
    done

    log_info "Total transfer size: $(numfmt --to=iec $total_size 2>/dev/null || echo "$total_size bytes")"

    # Transfer each file
    for filename in "${files_to_transfer[@]}"; do
        local local_file="$DOCKER_EXPORTS_DIR/$filename"

        if [[ -f "$local_file" ]]; then
            local file_size
            file_size=$(stat -c%s "$local_file" 2>/dev/null || echo "0")
            local file_size_human
            file_size_human=$(numfmt --to=iec "$file_size" 2>/dev/null || echo "$file_size bytes")

            log_info "Transferring: $filename ($file_size_human)"

            if scp_copy "$local_file"; then
                log_success "Transferred: $filename"
                ((transferred++))
            else
                log_error "Failed to transfer: $filename"
                ((failed++))
            fi
        else
            log_warning "Local file not found, skipping: $filename"
            ((failed++))
        fi
    done

    log_info "Transfer summary: $transferred successful, $failed failed"

    if [[ $failed -gt 0 ]]; then
        return 1
    fi

    return 0
}

# Verify transferred files
verify_transferred_files() {
    local files_to_verify=("$@")

    if [[ "$SKIP_VERIFICATION" == "true" ]]; then
        log_info "Skipping file verification (--skip-verification specified)"
        return 0
    fi

    log_info "Verifying transferred files..."

    local verified=0
    local failed=0

    for filename in "${files_to_verify[@]}"; do
        if [[ "$filename" == "export-manifest.json" ]]; then
            # Just check if manifest exists
            if ssh_exec "test -f $TARGET_DIR/$filename"; then
                log_success "Verified: $filename (exists)"
                ((verified++))
            else
                log_error "Verification failed: $filename (missing)"
                ((failed++))
            fi
            continue
        fi

        local expected_size
        expected_size=$(get_expected_size "$filename")

        if [[ -n "$expected_size" ]] && [[ "$expected_size" != "0" ]]; then
            local remote_size
            remote_size=$(ssh_exec "cd $TARGET_DIR && stat -c%s '$filename' 2>/dev/null || echo '0'")

            if [[ "$remote_size" == "$expected_size" ]]; then
                log_success "Verified: $filename (size: $(numfmt --to=iec "$remote_size" 2>/dev/null || echo "$remote_size bytes"))"
                ((verified++))
            else
                log_error "Verification failed for $filename (expected: $expected_size, actual: $remote_size)"
                ((failed++))
            fi
        else
            log_warning "Cannot verify $filename (no expected size in manifest)"
        fi
    done

    log_info "Verification summary: $verified verified, $failed failed"

    if [[ $failed -gt 0 ]]; then
        return 1
    fi

    return 0
}

# Deploy services on remote server
deploy_services() {
    log_info "Deploying services on remote server..."

    local version
    version=$(get_version)

    # Load Docker images
    log_info "Loading Docker images (version: $version)..."
    ssh_exec "cd $TARGET_DIR && if [ -f load-images.sh ]; then chmod +x load-images.sh && ./load-images.sh; elif [ -f load-images.ps1 ]; then powershell -File load-images.ps1; else echo 'No image loader script found'; fi"

    # Copy environment file if it exists
    if [[ -f "$PROJECT_DIR/.env.staging" ]]; then
        log_info "Copying staging environment file..."
        scp_copy "$PROJECT_DIR/.env.staging"
        ssh_exec "cd $TARGET_DIR && mv .env.staging .env"
    elif [[ -f "$PROJECT_DIR/.env" ]]; then
        log_info "Copying environment file..."
        scp_copy "$PROJECT_DIR/.env"
    fi

    # Stop existing services
    log_info "Stopping existing services..."
    ssh_exec "cd $TARGET_DIR && docker-compose down || true"

    # Start services
    log_info "Starting services..."
    ssh_exec "cd $TARGET_DIR && docker-compose pull && docker-compose up -d"

    # Wait for services to start
    log_info "Waiting for services to start..."
    sleep 10

    log_success "Services deployed successfully"
}

# Check service health
check_service_health() {
    log_info "Checking service health..."

    # Check if containers are running
    local running_services
    running_services=$(ssh_exec "cd $TARGET_DIR && docker-compose ps --services --filter status=running" || echo "")

    if [[ -n "$running_services" ]]; then
        log_success "Running services:"
        echo "$running_services" | while read -r service; do
            if [[ -n "$service" ]]; then
                log_success "  - $service"
            fi
        done
    else
        log_warning "No services appear to be running"
    fi

    # Show container status
    ssh_exec "cd $TARGET_DIR && docker-compose ps"

    # Check logs for any immediate errors
    log_info "Checking for immediate errors in logs..."
    ssh_exec "cd $TARGET_DIR && docker-compose logs --tail=10 | grep -i error || echo 'No immediate errors found'"
}

# Create backup before deployment
create_backup() {
    log_info "Creating backup before deployment..."

    local backup_dir="$TARGET_DIR/backups/$(date +%Y%m%d_%H%M%S)"

    # Create backup directory
    ssh_exec "mkdir -p $backup_dir"

    # Backup existing files
    ssh_exec "cd $TARGET_DIR && find . -maxdepth 1 -name '*.tar*' -o -name 'export-manifest.json' -o -name '.env' | xargs -I {} cp {} $backup_dir/ 2>/dev/null || true"

    # Backup database if possible
    ssh_exec "cd $TARGET_DIR && docker-compose exec -T postgres pg_dump -U postgres mvs_staging > $backup_dir/database_backup.sql 2>/dev/null || echo 'Database backup skipped (service not running)'"

    log_success "Backup created at: $backup_dir"
}

# Main deployment function
main() {
    local start_time
    start_time=$(date +%s)

    log_info "Starting smart deployment for MVS-VR v2 staging environment"
    log_info "Target: $SERVER_USER@$SERVER_IP:$TARGET_DIR"
    log_info "Version: $(get_version)"

    # Check prerequisites
    check_prerequisites

    # Test connection
    test_connection

    # Setup remote environment
    setup_remote_environment

    # Create backup
    if [[ "$DRY_RUN" != "true" ]]; then
        create_backup
    fi

    # Find files that need to be transferred
    local files_to_transfer
    mapfile -t files_to_transfer < <(find_files_to_transfer)

    if [[ ${#files_to_transfer[@]} -eq 0 ]]; then
        log_info "All files are up to date on the server"
    else
        # Transfer files
        if ! transfer_files "${files_to_transfer[@]}"; then
            log_error "File transfer failed"
            exit 1
        fi

        # Verify transferred files
        if ! verify_transferred_files "${files_to_transfer[@]}"; then
            log_error "File verification failed"
            exit 1
        fi
    fi

    # Deploy services
    if [[ "$DRY_RUN" != "true" ]]; then
        deploy_services
        check_service_health
    else
        echo "[DRY RUN] Would deploy services and check health"
    fi

    local end_time
    end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_success "Smart deployment completed successfully!"
    log_info "Total deployment time: ${duration}s"

    # Show final status
    if [[ "$DRY_RUN" != "true" ]]; then
        log_info "=== Deployment Summary ==="
        log_info "Server: $SERVER_IP"
        log_info "Target Directory: $TARGET_DIR"
        log_info "Version: $(get_version)"
        log_info "Files Transferred: ${#files_to_transfer[@]}"
        log_info "Deployment Time: ${duration}s"
        echo ""
        log_info "=== Service URLs ==="
        log_info "API: https://api.mvs.kanousai.com"
        log_info "Admin: https://admin.mvs.kanousai.com"
        log_info "Staging: https://staging.mvs.kanousai.com"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --server-ip)
            SERVER_IP="$2"
            shift 2
            ;;
        --server-user)
            SERVER_USER="$2"
            shift 2
            ;;
        --ssh-key)
            SSH_KEY="$2"
            shift 2
            ;;
        --target-dir)
            TARGET_DIR="$2"
            shift 2
            ;;
        --force)
            FORCE_TRANSFER=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --skip-verification)
            SKIP_VERIFICATION=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
