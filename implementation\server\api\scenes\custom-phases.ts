/**
 * Custom Phases API
 *
 * This file defines the API endpoints for custom phase management.
 */

import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { ScenePhaseManagerService } from '../../services/scene/scene-phase-manager';
import { PhaseType, ValidationLevel, createCustomPhase } from '../../config/phase-config';

// Initialize scene phase manager service
const phaseManager = new ScenePhaseManagerService(supabase);

/**
 * Create custom phase
 *
 * @param req - Request
 * @param res - Response
 */
export const createCustomPhase = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      name,
      description,
      order,
      validation_level,
      required_validations,
      optional_validations,
      prerequisites,
      custom_definition,
    } = req.body;

    // Validate parameters
    if (!name || !description || !order || !validation_level) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Name, description, order, and validation_level are required',
        },
      });
      return;
    }

    // Validate validation level
    if (!Object.values(ValidationLevel).includes(validation_level)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_VALIDATION_LEVEL',
          message: 'Invalid validation level',
        },
      });
      return;
    }

    // Validate prerequisites
    if (prerequisites && !Array.isArray(prerequisites)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PREREQUISITES',
          message: 'Prerequisites must be an array',
        },
      });
      return;
    }

    // Create custom phase
    const customPhase = createCustomPhase(
      name,
      description,
      order,
      validation_level,
      required_validations || [],
      optional_validations || [],
      prerequisites || [],
      custom_definition || {},
    );

    // Save custom phase to database
    const { data, error } = await supabase
      .from('custom_phases')
      .insert({
        name,
        description,
        order,
        validation_level,
        required_validations: required_validations || [],
        optional_validations: optional_validations || [],
        prerequisites: prerequisites || [],
        custom_definition: custom_definition || {},
        created_by: req.user.id,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      logger.error('Error creating custom phase', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error creating custom phase',
        },
      });
      return;
    }

    res.status(201).json({
      success: true,
      data: {
        ...data,
        phase_config: customPhase,
      },
    });
  } catch (error) {
    logger.error('Error in createCustomPhase', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get custom phases
 *
 * @param req - Request
 * @param res - Response
 */
export const getCustomPhases = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get custom phases from database
    const { data, error } = await supabase
      .from('custom_phases')
      .select('*')
      .order('order', { ascending: true });

    if (error) {
      logger.error('Error getting custom phases', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting custom phases',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getCustomPhases', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get custom phase by ID
 *
 * @param req - Request
 * @param res - Response
 */
export const getCustomPhaseById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Get custom phase from database
    const { data, error } = await supabase.from('custom_phases').select('*').eq('id', id).single();

    if (error) {
      logger.error('Error getting custom phase', { error, id });
      res.status(404).json({
        success: false,
        error: {
          code: 'PHASE_NOT_FOUND',
          message: 'Custom phase not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getCustomPhaseById', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Update custom phase
 *
 * @param req - Request
 * @param res - Response
 */
export const updateCustomPhase = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      order,
      validation_level,
      required_validations,
      optional_validations,
      prerequisites,
      custom_definition,
    } = req.body;

    // Update custom phase in database
    const { data, error } = await supabase
      .from('custom_phases')
      .update({
        name,
        description,
        order,
        validation_level,
        required_validations,
        optional_validations,
        prerequisites,
        custom_definition,
        updated_by: req.user.id,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      logger.error('Error updating custom phase', { error, id });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error updating custom phase',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in updateCustomPhase', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Delete custom phase
 *
 * @param req - Request
 * @param res - Response
 */
export const deleteCustomPhase = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // Delete custom phase from database
    const { error } = await supabase.from('custom_phases').delete().eq('id', id);

    if (error) {
      logger.error('Error deleting custom phase', { error, id });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error deleting custom phase',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      message: 'Custom phase deleted successfully',
    });
  } catch (error) {
    logger.error('Error in deleteCustomPhase', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on method and path
    if (req.method === 'GET' && req.params.id) {
      await getCustomPhaseById(req, res);
    } else if (req.method === 'GET') {
      await getCustomPhases(req, res);
    } else if (req.method === 'POST') {
      await createCustomPhase(req, res);
    } else if (req.method === 'PUT' && req.params.id) {
      await updateCustomPhase(req, res);
    } else if (req.method === 'DELETE' && req.params.id) {
      await deleteCustomPhase(req, res);
    } else {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
    }
  } catch (error) {
    logger.error('Error in custom phase handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
}
