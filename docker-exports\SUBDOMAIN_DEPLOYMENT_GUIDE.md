# 🌐 MVS-VR Subdomain Deployment Guide

## 🎯 **Overview**

Your MVS-VR platform is now configured with proper subdomain architecture as specified in your staging deployment plan:

### **🏗️ Subdomain Architecture**

```
mvs.kanousai.com (Primary Domain)
├── 🏠 Vendor UX Home Page (Main Entry Point)
├── 🔗 Links to all platform services
└── 📊 System status and overview

api.mvs.kanousai.com (API Gateway)
├── 🔌 RESTful API endpoints
├── 🔐 Authentication services
├── 📁 Asset management APIs
└── 📈 Analytics APIs

admin.mvs.kanousai.com (Admin Panel)
├── ⚙️ Directus CMS interface
├── 👥 User management
├── 🎨 Content management
└── 📊 Admin dashboard

staging.mvs.kanousai.com (Staging Environment)
├── 🧪 Development testing
├── 🔄 Feature previews
├── 🐛 Bug testing
└── 📋 Integration testing
```

## ✅ **What's Been Configured**

### **1. Nginx Subdomain Routing**
- ✅ **Primary Domain**: Professional vendor landing page
- ✅ **API Subdomain**: API gateway with endpoint documentation
- ✅ **Admin Subdomain**: Admin panel preparation for Directus
- ✅ **Staging Subdomain**: Staging environment setup
- ✅ **Fallback Handling**: IP access redirects to main domain

### **2. Vendor UX Home Page**
The primary domain (`mvs.kanousai.com`) now serves as the **main entry point** with:
- Professional landing page design
- System status indicators
- Links to all platform services
- Platform feature overview
- Responsive design for all devices

### **3. Service-Specific Pages**
Each subdomain has its own purpose-built interface:
- **API**: Developer-focused documentation
- **Admin**: Admin panel preparation
- **Staging**: Development environment warnings

## 🚀 **Deployment Steps**

### **Step 1: Update Server Configuration**
The nginx configuration has been updated. Deploy it:

```bash
# SSH into your server
ssh -i C:\Users\<USER>\.ssh\id_rsa root@**************

# Navigate to project directory
cd /home/<USER>/mvs-vr-deployment

# Update nginx configuration
docker-compose -f docker-compose.exported.yml restart nginx

# Verify nginx is running
docker-compose -f docker-compose.exported.yml ps nginx
```

### **Step 2: Configure DNS Records**
**CRITICAL**: You must configure DNS for all subdomains to work.

Add these A records to your DNS provider:
```
mvs.kanousai.com      A    **************
api.mvs.kanousai.com  A    **************
admin.mvs.kanousai.com A   **************
staging.mvs.kanousai.com A **************
```

**See `DNS_CONFIGURATION_GUIDE.md` for detailed instructions.**

### **Step 3: Test All Subdomains**
Once DNS propagates (5-60 minutes), test each subdomain:

```bash
# Test primary domain
curl -I http://mvs.kanousai.com/

# Test API subdomain  
curl -I http://api.mvs.kanousai.com/

# Test admin subdomain
curl -I http://admin.mvs.kanousai.com/

# Test staging subdomain
curl -I http://staging.mvs.kanousai.com/
```

All should return `HTTP/1.1 200 OK`.

## 🎨 **Vendor UX Experience**

### **Primary Landing Page Features**
When users visit `mvs.kanousai.com`, they see:

1. **Professional Branding**
   - MVS-VR platform title
   - Clean, modern design
   - Responsive layout

2. **System Status**
   - Real-time system health
   - Server information
   - Connection status

3. **Platform Access Points**
   - Direct links to API documentation
   - Admin panel access
   - Staging environment
   - Health monitoring

4. **Feature Overview**
   - VR content management
   - Asset management
   - Scene creation tools
   - Analytics capabilities

### **User Journey Flow**
```
Vendor visits mvs.kanousai.com
    ↓
Sees professional landing page
    ↓
Clicks "API Gateway" → api.mvs.kanousai.com
    ↓
Reviews API documentation
    ↓
Clicks "Admin Panel" → admin.mvs.kanousai.com
    ↓
Accesses content management
```

## 🔧 **Service Integration**

### **When Services Are Running**
Once your Docker containers are properly built and running:

1. **Uncomment Proxy Lines** in `nginx-simple.conf`:
   ```nginx
   # Change this:
   # proxy_pass http://api_gateway;
   
   # To this:
   proxy_pass http://api_gateway;
   ```

2. **Enable Service Routing**:
   - Primary domain → API Gateway (main app)
   - API subdomain → API Gateway (API endpoints)
   - Admin subdomain → Directus CMS
   - Staging subdomain → Staging services

### **Current Status (Temporary Pages)**
Until services are running, each subdomain shows:
- **Informational pages** explaining the service
- **Status indicators** showing preparation state
- **Feature descriptions** for each platform component
- **Professional appearance** for vendor confidence

## 📋 **Deployment Checklist**

### **Infrastructure Setup**
- [x] **Nginx Configuration**: Updated with subdomain routing
- [x] **Docker Compose**: Configured for multi-service deployment
- [x] **Landing Pages**: Professional pages for each subdomain
- [ ] **DNS Configuration**: Must be done by you (see DNS guide)
- [ ] **SSL Certificates**: Future enhancement

### **Service Deployment**
- [x] **Nginx**: Running and configured
- [x] **Redis**: Running for caching
- [ ] **API Gateway**: Needs container rebuild
- [ ] **Directus**: Needs container deployment
- [ ] **Auth Service**: Needs container rebuild
- [ ] **Asset Service**: Needs container rebuild

### **Testing & Validation**
- [x] **Local Testing**: All subdomains work via IP
- [ ] **DNS Testing**: Requires DNS configuration
- [ ] **External Testing**: Requires DNS propagation
- [ ] **SSL Testing**: Future enhancement

## 🚨 **Critical Next Steps**

### **1. Configure DNS (URGENT)**
Without DNS configuration, the subdomains won't work externally:
- Follow the `DNS_CONFIGURATION_GUIDE.md`
- Add A records for all subdomains
- Wait for DNS propagation

### **2. Fix Docker Container Issues**
The microservices need container rebuilds:
- Auth Service: Missing shared dependencies
- API Gateway: Missing shared dependencies
- Asset Service: Missing shared dependencies
- Analytics Service: Missing shared dependencies

### **3. Deploy Directus**
The admin panel needs Directus deployment:
- Add Directus to docker-compose
- Configure database connection
- Set up admin credentials

## 🎯 **Expected Results**

### **After DNS Configuration**
- `mvs.kanousai.com` → Professional vendor landing page
- `api.mvs.kanousai.com` → API documentation page
- `admin.mvs.kanousai.com` → Admin panel preparation page
- `staging.mvs.kanousai.com` → Staging environment page

### **After Service Deployment**
- `mvs.kanousai.com` → Full vendor application
- `api.mvs.kanousai.com` → Live API endpoints
- `admin.mvs.kanousai.com` → Directus CMS interface
- `staging.mvs.kanousai.com` → Staging application

## 📞 **Support**

The subdomain architecture is now properly configured. The main requirements are:

1. **DNS Configuration** (you must do this)
2. **Container Rebuilds** (can be done later)
3. **Service Integration** (when containers work)

**Priority**: Configure DNS first to see the subdomain structure working!
