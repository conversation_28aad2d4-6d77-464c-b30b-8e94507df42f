import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { AssetProcessorService } from '../../services/asset/asset-processor';
import { AssetProcessingOptions, AssetProcessingType } from '../../shared/models/asset-management';
import { validateRequest } from '../middleware/validation';

// Initialize asset processor service
const assetProcessor = new AssetProcessorService(supabase);

/**
 * Process an asset
 *
 * @param req - Request
 * @param res - Response
 */
export const processAsset = async (req: Request, res: Response): Promise<void> => {
  try {
    const { assetId } = req.params;
    const options: AssetProcessingOptions = req.body;

    // Validate asset ID
    if (!assetId) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ASSET_ID',
          message: 'Asset ID is required',
        },
      });
      return;
    }

    // Process asset
    const job = await assetProcessor.processAsset(assetId, options);

    if (!job) {
      res.status(500).json({
        success: false,
        error: {
          code: 'PROCESSING_FAILED',
          message: 'Failed to create processing job',
        },
      });
      return;
    }

    res.status(202).json({
      success: true,
      data: {
        job,
        message: 'Asset processing started',
      },
    });
  } catch (error) {
    logger.error('Error processing asset', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get processing job status
 *
 * @param req - Request
 * @param res - Response
 */
export const getProcessingJobStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { jobId } = req.params;

    // Validate job ID
    if (!jobId) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_JOB_ID',
          message: 'Job ID is required',
        },
      });
      return;
    }

    // Get job status
    const job = await assetProcessor.getProcessingJob(jobId);

    if (!job) {
      res.status(404).json({
        success: false,
        error: {
          code: 'JOB_NOT_FOUND',
          message: 'Processing job not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: job,
    });
  } catch (error) {
    logger.error('Error getting processing job status', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get processing jobs for an asset
 *
 * @param req - Request
 * @param res - Response
 */
export const getAssetProcessingJobs = async (req: Request, res: Response): Promise<void> => {
  try {
    const { assetId } = req.params;

    // Validate asset ID
    if (!assetId) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ASSET_ID',
          message: 'Asset ID is required',
        },
      });
      return;
    }

    // Get jobs
    const jobs = await assetProcessor.getProcessingJobsByAsset(assetId);

    res.status(200).json({
      success: true,
      data: jobs,
    });
  } catch (error) {
    logger.error('Error getting asset processing jobs', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
