# Integration Module

## Overview

The Integration Module is responsible for integrating all server components, including:

- Bootstrap System
- Asset Management System
- Scene Configuration System
- Blueprint Injection System

It provides a unified interface for the MVS-VR platform, ensuring that all components work together seamlessly. It handles the coordination between different services, manages dependencies, and provides a consistent error handling and logging mechanism.

## Components

### Integration Manager

The Integration Manager is the main entry point for the Integration Module. It coordinates the interactions between different services and provides a unified interface for the MVS-VR platform.

```typescript
// Example usage
import { IntegrationManager } from '../services/integration';

// Create a new integration manager
const integrationManager = new IntegrationManager();

// Initialize all services
await integrationManager.initialize();

// Handle a client request
const request = { path: '/bootstrap/config' };
const response = await integrationManager.handleRequest(request);
```

### Service Registry

The Service Registry maintains a registry of all available services and their dependencies. It ensures that services are initialized in the correct order and provides a mechanism for services to discover and interact with each other.

```typescript
// Example usage
import { ServiceRegistry, Logger } from '../services/integration';

// Create a new logger
const logger = new Logger();

// Create a new service registry
const serviceRegistry = new ServiceRegistry(logger);

// Register a service
serviceRegistry.register('myService', myService);

// Get a service
const service = serviceRegistry.get('myService');

// Check if a service is registered
const hasService = serviceRegistry.has('myService');

// Get all registered services
const services = serviceRegistry.getAll();

// Get the names of all registered services
const serviceNames = serviceRegistry.getServiceNames();

// Get the number of registered services
const serviceCount = serviceRegistry.getServiceCount();

// Clear the registry
serviceRegistry.clear();
```

### Error Handler

The Error Handler provides a consistent error handling mechanism across all services. It ensures that errors are properly logged, reported, and handled according to the platform's error handling policies.

```typescript
// Example usage
import { ErrorHandler, Logger } from '../services/integration';

// Create a new logger
const logger = new Logger();

// Create a new error handler
const errorHandler = new ErrorHandler(logger);

// Handle an error
const error = new Error('Something went wrong');
errorHandler.handleError(error, 'MyService');

// Create a standardized error response
const response = errorHandler.createErrorResponse(error, 'MyService');
```

### Logger

The Logger provides a unified logging mechanism across all services. It ensures that logs are properly formatted, categorized, and stored according to the platform's logging policies.

```typescript
// Example usage
import { Logger, LogLevel } from '../services/integration';

// Create a new logger
const logger = new Logger(LogLevel.DEBUG);

// Log messages
logger.debug('Debug message');
logger.info('Info message');
logger.warn('Warning message');
logger.error('Error message');

// Log messages with metadata
logger.info('Info message with metadata', { key: 'value' });

// Set the log level
logger.setLogLevel(LogLevel.INFO);

// Get the current log level
const logLevel = logger.getLogLevel();
```

## Integration with API Gateway

The Integration Module is used by the API Gateway to handle client requests. It provides a unified interface for the MVS-VR platform, ensuring that all components work together seamlessly.

```typescript
// Example usage in API Gateway
import { IntegrationManager } from '../services/integration';

// Create a new integration manager
const integrationManager = new IntegrationManager();

// Initialize all services
await integrationManager.initialize();

// Handle a client request
app.use('/api', async (req, res) => {
  try {
    const response = await integrationManager.handleRequest({
      path: req.path,
      method: req.method,
      body: req.body,
      headers: req.headers
    });
    
    res.status(200).json(response);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        message: error.message
      }
    });
  }
});
```

## Testing

The Integration Module includes comprehensive tests for all components. These tests ensure that the module works as expected and will continue to work as the platform evolves.

To run the tests:

```bash
npm test -- --testPathPattern=integration
```

## Conclusion

The Integration Module provides a robust foundation for integrating all server components in the MVS-VR platform. It ensures that all components work together seamlessly, providing a unified interface for the platform.
