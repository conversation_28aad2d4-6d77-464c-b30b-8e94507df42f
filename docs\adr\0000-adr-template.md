# [ADR-0000] Architecture Decision Record Template

## Status

[Proposed | Accepted | Deprecated | Superseded]

If superseded, include a link to the new ADR:

## Context

[Describe the context and problem statement, e.g., in free form using two to three sentences. You may want to articulate the problem in form of a question.]

## Decision

[Describe the decision that was made, e.g., "We will use GraphQL as the primary API layer."]

## Consequences

[Describe the consequences of the decision, both positive and negative.]

### Positive

- [List positive consequences]

### Negative

- [List negative consequences]

### Neutral

- [List neutral consequences]

## Alternatives Considered

[Describe the alternatives that were considered and why they were not chosen.]

### Alternative 1: [Name]

[Describe the alternative]

#### Pros

- [List pros]

#### Cons

- [List cons]

### Alternative 2: [Name]

[Describe the alternative]

#### Pros

- [List pros]

#### Cons

- [List cons]

## Related Decisions

[List related decisions and how they affect this decision.]

## Notes

[Any additional notes or references.]
