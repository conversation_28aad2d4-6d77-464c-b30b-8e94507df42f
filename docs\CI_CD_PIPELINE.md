# CI/CD Pipeline Documentation

This document provides comprehensive documentation for the MVS-VR CI/CD pipeline, including architecture, configuration, usage, and troubleshooting.

## Table of Contents

1. [Pipeline Architecture](#pipeline-architecture)
2. [Pipeline Configuration](#pipeline-configuration)
3. [Pipeline Usage](#pipeline-usage)
4. [Pipeline Monitoring](#pipeline-monitoring)
5. [Troubleshooting](#troubleshooting)
6. [Best Practices](#best-practices)

## Pipeline Architecture

The MVS-VR CI/CD pipeline is designed to automate the build, test, and deployment processes for the MVS-VR server. It follows a modern GitOps approach, where changes to the codebase trigger automated workflows that validate, build, and deploy the application.

### Components

The pipeline consists of the following components:

1. **GitHub Actions**: Provides the workflow automation platform
2. **Docker**: Used for containerization of the application
3. **Kubernetes**: Target deployment platform
4. **Terraform**: Infrastructure as Code for provisioning resources
5. **Prometheus & Grafana**: Monitoring and alerting

### Workflow Stages

The pipeline workflow includes the following stages:

1. **Lint**: Code quality checks
2. **Test**: Unit and integration tests
3. **Build**: Docker image building
4. **Deploy**: Deployment to target environments
5. **Verify**: Post-deployment verification

### Environment Strategy

The pipeline supports three environments:

1. **Development**: For development and feature testing
2. **Staging**: For pre-production testing
3. **Production**: For live deployment

## Pipeline Configuration

### GitHub Actions Workflow

The main workflow file is located at `.github/workflows/server-ci.yml`. It defines the CI/CD pipeline for the MVS-VR server.

#### Triggers

The workflow is triggered by:

- Pushes to `main` and `develop` branches
- Pull requests to `main` and `develop` branches
- Manual workflow dispatch

#### Jobs

The workflow includes the following jobs:

1. **lint**: Code linting and formatting checks
2. **test**: Unit and integration tests
3. **build**: Docker image building and pushing
4. **deploy-staging**: Deployment to staging environment
5. **deploy-production**: Deployment to production environment

### Environment Variables

The pipeline uses environment variables defined in:

- GitHub repository secrets
- Environment-specific `.env` files
- Workflow-level environment variables

Key environment variables include:

- `DOCKER_REGISTRY`: Docker registry URL
- `SUPABASE_URL`: Supabase project URL
- `SUPABASE_ANON_KEY`: Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Supabase service role key
- `DIRECTUS_SECRET`: Directus secret key

### Terraform Configuration

Infrastructure as Code is managed using Terraform, with configuration files located in the `terraform/` directory:

- `main.tf`: Main Terraform configuration
- `variables.tf`: Variable definitions
- `modules/`: Reusable Terraform modules

## Pipeline Usage

### Running the Pipeline

The pipeline can be triggered in several ways:

1. **Automatically**: By pushing to `main` or `develop` branches
2. **Pull Requests**: When creating or updating pull requests
3. **Manually**: Using the GitHub Actions workflow dispatch

### Manual Deployment

For manual deployment, use the deployment scripts:

#### Windows

```powershell
.\scripts\deployment\deploy.ps1 -Environment staging -Version 1.0.0 -ApplyTerraform
```

#### Linux/macOS

```bash
./scripts/deployment/deploy.sh --environment staging --version 1.0.0 --terraform
```

### Deployment Verification

After deployment, verify the application is running correctly:

1. Check the deployment status in GitHub Actions
2. Verify the application is accessible at the expected URL
3. Run smoke tests against the deployed environment

## Pipeline Monitoring

### Metrics Collection

The pipeline collects the following metrics:

- Pipeline status (success/failure)
- Pipeline duration
- Test results
- Test coverage
- Deployment frequency

### Grafana Dashboard

A Grafana dashboard is available for monitoring the pipeline:

- Dashboard name: CI/CD Pipeline Monitoring
- URL: `https://<grafana-url>/d/ci-pipeline-monitoring`

### Alerting

Alerts are configured for:

- Pipeline failures
- Test coverage below threshold
- Deployment failures

## Troubleshooting

### Common Issues

#### Pipeline Failure

If the pipeline fails, check:

1. GitHub Actions logs for error messages
2. Test results for failing tests
3. Build logs for compilation errors

#### Deployment Failure

If deployment fails, check:

1. Kubernetes logs for deployment errors
2. Environment variables for missing or incorrect values
3. Network connectivity to the deployment environment

### Recovery Procedures

#### Rollback Procedure

To rollback to a previous version:

1. Identify the previous stable version
2. Run the deployment script with the previous version:

```powershell
.\scripts\deployment\deploy.ps1 -Environment production -Version <previous-version>
```

#### Manual Intervention

For manual intervention:

1. Access the Kubernetes cluster
2. Check pod status and logs
3. Apply necessary fixes or rollback manually

## Best Practices

### Code Quality

- Write comprehensive tests for all new features
- Maintain test coverage above 80%
- Follow code style guidelines
- Use linting tools to ensure code quality

### Deployment Strategy

- Use feature branches for development
- Merge to `develop` for staging deployment
- Merge to `main` for production deployment
- Use semantic versioning for releases

### Security

- Store sensitive information in GitHub Secrets
- Regularly rotate API keys and credentials
- Scan Docker images for vulnerabilities
- Implement least privilege access control

### Monitoring

- Monitor pipeline metrics
- Set up alerts for critical failures
- Review pipeline performance regularly
- Optimize pipeline for faster execution
