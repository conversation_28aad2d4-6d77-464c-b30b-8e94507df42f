#### **`scene_flow_manager.md`**

# Scene Flow Manager

## Overview
Manages the hierarchical progression of VR environments: **Location → Exhibition Center → Space**.

## JSON <PERSON>hema (`scene.flow.json`)
```json
{
  "startup": { "space": "StartupScreen", "next": "locationSelect" },
  "locationSelect": {
    "space": "LocationMenu",
    "options": { "Dubai": "dubaiHub", "Lebanon": "lebanonHub" }
  },
  "dubaiHub": { "space": "DubaiMainHall", "next": "storePreview" }
}
````

## Layers

1. **Location**
2. **Exhibition Center**
3. **Space**

## Transition Logic

* **EntryCondition** (optional)
* **ExitTrigger** (player action, timer)
* **Conditional Branching** via `options`

## Editor UI

* Drag-and-drop nodes
* Numbered flow steps
* Preview transitions in 3D sandbox
