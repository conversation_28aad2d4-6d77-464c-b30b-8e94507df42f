<template>
  <div class="system-widget">
    <div v-if="!systemData" class="no-data">
      <v-icon size="48" color="grey lighten-1">mdi-server</v-icon>
      <p class="mt-2 grey--text">No system data available</p>
    </div>
    
    <div v-else>
      <!-- System Status -->
      <div class="system-status">
        <v-chip
          :color="getStatusColor(systemStatus)"
          text-color="white"
          class="status-chip"
        >
          {{ getStatusText(systemStatus) }}
        </v-chip>
      </div>
      
      <!-- System Metrics -->
      <div class="system-metrics">
        <v-row>
          <v-col cols="6">
            <div class="metric-item">
              <div class="metric-label">CPU</div>
              <v-progress-linear
                :value="systemData.cpu?.usage || 0"
                :color="getResourceColor(systemData.cpu?.usage || 0)"
                height="10"
                rounded
              ></v-progress-linear>
              <div class="metric-value">{{ systemData.cpu?.usage || 0 }}%</div>
            </div>
          </v-col>
          
          <v-col cols="6">
            <div class="metric-item">
              <div class="metric-label">Memory</div>
              <v-progress-linear
                :value="systemData.memory?.usage || 0"
                :color="getResourceColor(systemData.memory?.usage || 0)"
                height="10"
                rounded
              ></v-progress-linear>
              <div class="metric-value">{{ systemData.memory?.usage || 0 }}%</div>
            </div>
          </v-col>
          
          <v-col cols="6">
            <div class="metric-item">
              <div class="metric-label">Disk</div>
              <v-progress-linear
                :value="systemData.disk?.usage || 0"
                :color="getResourceColor(systemData.disk?.usage || 0)"
                height="10"
                rounded
              ></v-progress-linear>
              <div class="metric-value">{{ systemData.disk?.usage || 0 }}%</div>
            </div>
          </v-col>
          
          <v-col cols="6">
            <div class="metric-item">
              <div class="metric-label">Network</div>
              <v-progress-linear
                :value="systemData.network?.usage || 0"
                :color="getResourceColor(systemData.network?.usage || 0)"
                height="10"
                rounded
              ></v-progress-linear>
              <div class="metric-value">{{ systemData.network?.usage || 0 }}%</div>
            </div>
          </v-col>
        </v-row>
      </div>
      
      <!-- Service Status -->
      <div v-if="settings.showServices && systemData.services" class="service-status">
        <div class="section-title">Services</div>
        <div class="service-chips">
          <v-chip
            v-for="(service, key) in systemData.services"
            :key="key"
            :color="getServiceStatusColor(service.status)"
            text-color="white"
            small
            class="ma-1"
          >
            {{ formatServiceName(key) }}: {{ formatStatus(service.status) }}
          </v-chip>
        </div>
      </div>
      
      <!-- Alerts -->
      <div v-if="settings.showAlerts && systemData.alerts" class="system-alerts">
        <div class="section-title">Alerts</div>
        <v-list dense>
          <v-list-item v-if="systemData.alerts.critical > 0" class="alert-item critical">
            <v-list-item-icon>
              <v-icon color="error">mdi-alert-circle</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>{{ systemData.alerts.critical }} Critical Alert{{ systemData.alerts.critical > 1 ? 's' : '' }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          
          <v-list-item v-if="systemData.alerts.warning > 0" class="alert-item warning">
            <v-list-item-icon>
              <v-icon color="warning">mdi-alert</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>{{ systemData.alerts.warning }} Warning{{ systemData.alerts.warning > 1 ? 's' : '' }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          
          <v-list-item v-if="systemData.alerts.info > 0" class="alert-item info">
            <v-list-item-icon>
              <v-icon color="info">mdi-information</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>{{ systemData.alerts.info }} Info Alert{{ systemData.alerts.info > 1 ? 's' : '' }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          
          <v-list-item v-if="!systemData.alerts.critical && !systemData.alerts.warning && !systemData.alerts.info" class="alert-item healthy">
            <v-list-item-icon>
              <v-icon color="success">mdi-check-circle</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>No active alerts</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SystemWidget',
  
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    settings: {
      type: Object,
      default: () => ({
        showServices: true,
        showAlerts: true
      })
    }
  },
  
  computed: {
    systemData() {
      if (!this.data.system) {
        return this.getDefaultSystemData();
      }
      
      return this.data.system;
    },
    
    systemStatus() {
      if (!this.systemData) {
        return 'unknown';
      }
      
      // Check if any service is unhealthy
      const services = this.systemData.services || {};
      const hasUnhealthyService = Object.values(services).some(
        service => service.status === 'unhealthy'
      );
      
      if (hasUnhealthyService) {
        return 'unhealthy';
      }
      
      // Check if any service is degraded
      const hasDegradedService = Object.values(services).some(
        service => service.status === 'degraded'
      );
      
      if (hasDegradedService) {
        return 'degraded';
      }
      
      // Check if there are critical alerts
      const alerts = this.systemData.alerts || {};
      if (alerts.critical && alerts.critical > 0) {
        return 'unhealthy';
      }
      
      // Check if there are warning alerts
      if (alerts.warning && alerts.warning > 0) {
        return 'degraded';
      }
      
      // Check CPU and memory usage
      const cpuUsage = this.systemData.cpu?.usage ? parseFloat(this.systemData.cpu.usage) : 0;
      const memoryUsage = this.systemData.memory?.usage ? parseFloat(this.systemData.memory.usage) : 0;
      
      if (cpuUsage > 90 || memoryUsage > 90) {
        return 'degraded';
      }
      
      return 'healthy';
    }
  },
  
  methods: {
    getStatusColor(status) {
      switch (status) {
        case 'healthy':
          return 'success';
        case 'degraded':
          return 'warning';
        case 'unhealthy':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    getStatusText(status) {
      switch (status) {
        case 'healthy':
          return 'Healthy';
        case 'degraded':
          return 'Degraded';
        case 'unhealthy':
          return 'Unhealthy';
        default:
          return 'Unknown';
      }
    },
    
    getResourceColor(usage) {
      if (usage >= 90) return 'error';
      if (usage >= 70) return 'warning';
      if (usage >= 50) return 'info';
      return 'success';
    },
    
    getServiceStatusColor(status) {
      switch (status) {
        case 'healthy':
          return 'success';
        case 'degraded':
          return 'warning';
        case 'unhealthy':
          return 'error';
        default:
          return 'grey';
      }
    },
    
    formatServiceName(key) {
      return key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    },
    
    formatStatus(status) {
      return status.charAt(0).toUpperCase() + status.slice(1);
    },
    
    getDefaultSystemData() {
      return {
        cpu: {
          usage: 45,
          cores: 8
        },
        memory: {
          usage: 62,
          total: '16GB'
        },
        disk: {
          usage: 78,
          total: '500GB'
        },
        network: {
          usage: 35,
          bandwidth: '1Gbps'
        },
        services: {
          api_gateway: { status: 'healthy' },
          database: { status: 'healthy' },
          auth_service: { status: 'healthy' },
          storage_service: { status: 'healthy' }
        },
        alerts: {
          critical: 0,
          warning: 2,
          info: 3
        }
      };
    }
  }
};
</script>

<style scoped>
.system-widget {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.system-status {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.status-chip {
  font-size: 1rem;
  height: 32px;
}

.system-metrics {
  margin-bottom: 16px;
}

.metric-item {
  margin-bottom: 8px;
}

.metric-label {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 4px;
}

.metric-value {
  font-size: 0.75rem;
  font-weight: bold;
  text-align: right;
  margin-top: 2px;
}

.section-title {
  font-size: 0.875rem;
  font-weight: bold;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.6);
}

.service-status {
  margin-bottom: 16px;
}

.service-chips {
  display: flex;
  flex-wrap: wrap;
}

.system-alerts {
  margin-top: 8px;
}

.alert-item {
  border-radius: 4px;
  margin-bottom: 4px;
}

.alert-item.critical {
  background-color: rgba(244, 67, 54, 0.1);
}

.alert-item.warning {
  background-color: rgba(255, 152, 0, 0.1);
}

.alert-item.info {
  background-color: rgba(33, 150, 243, 0.1);
}

.alert-item.healthy {
  background-color: rgba(76, 175, 80, 0.1);
}
</style>
