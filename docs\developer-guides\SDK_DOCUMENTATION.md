# MVS-VR SDK Documentation

This document provides comprehensive documentation for the MVS-VR Software Development Kits (SDKs). It covers the JavaScript SDK, Python SDK, C++ SDK, and C# SDK.

## Table of Contents

1. [JavaScript SDK](#javascript-sdk)
2. [Python SDK](#python-sdk)
3. [C++ SDK](#c-sdk)
4. [C# SDK](#c-sdk-1)
5. [SDK Versioning](#sdk-versioning)
6. [Common Patterns](#common-patterns)
7. [Troubleshooting](#troubleshooting)

## JavaScript SDK

The JavaScript SDK provides a comprehensive set of tools for integrating with the MVS-VR platform in web applications, Node.js applications, and React Native applications.

### Installation

```bash
# npm
npm install @mvs-vr/client-sdk

# yarn
yarn add @mvs-vr/client-sdk

# pnpm
pnpm add @mvs-vr/client-sdk
```

### Initialization

```javascript
import { MVSClient } from '@mvs-vr/client-sdk';

// Initialize with API key
const client = new MVSClient({
  apiUrl: 'https://api.mvs-vr.com',
  apiKey: 'your-api-key',
});

// Or initialize with auth token
const client = new MVSClient({
  apiUrl: 'https://api.mvs-vr.com',
  authToken: 'your-auth-token',
});

// With additional options
const client = new MVSClient({
  apiUrl: 'https://api.mvs-vr.com',
  apiKey: 'your-api-key',
  options: {
    timeout: 30000, // 30 seconds
    retries: 3,
    retryDelay: 1000, // 1 second
    debug: true,
  },
});
```

### Authentication

```javascript
// Login with email and password
const { user, accessToken, refreshToken } = await client.auth.login({
  email: '<EMAIL>',
  password: 'password',
});

// Login with API key
const { user, accessToken, refreshToken } = await client.auth.loginWithApiKey('your-api-key');

// Refresh token
const { accessToken: newAccessToken } = await client.auth.refreshToken(refreshToken);

// Logout
await client.auth.logout();

// Get current user
const user = await client.auth.getUser();

// Check if user has permission
const hasPermission = await client.auth.hasPermission('assets.create');
```

### Asset Management

```javascript
// Create an asset
const asset = await client.assets.create({
  name: 'Example Asset',
  type: 'model',
  tags: ['furniture', 'chair'],
});

// Upload asset file
const uploadResult = await client.assets.upload(asset.id, fileBlob, {
  onProgress: (progress) => {
    console.log(`Upload progress: ${progress.percent}%`);
  },
});

// Get asset by ID
const asset = await client.assets.get(assetId);

// List assets
const assets = await client.assets.list({
  type: 'model',
  tags: ['furniture'],
  page: 1,
  limit: 20,
  sort: 'createdAt:desc',
});

// Update asset
const updatedAsset = await client.assets.update(assetId, {
  name: 'Updated Asset Name',
  tags: ['furniture', 'chair', 'new-tag'],
});

// Delete asset
await client.assets.delete(assetId);

// Search assets
const searchResults = await client.assets.search('chair', {
  type: 'model',
  limit: 20,
});
```

### Scene Management

```javascript
// Create a scene
const scene = await client.scenes.create({
  name: 'Example Scene',
  description: 'An example scene',
  assets: [assetId1, assetId2],
  configuration: {
    // Scene configuration
  },
});

// Get scene by ID
const scene = await client.scenes.get(sceneId);

// List scenes
const scenes = await client.scenes.list({
  page: 1,
  limit: 20,
  sort: 'createdAt:desc',
});

// Update scene
const updatedScene = await client.scenes.update(sceneId, {
  name: 'Updated Scene Name',
  assets: [assetId1, assetId2, assetId3],
});

// Delete scene
await client.scenes.delete(sceneId);

// Publish scene
await client.scenes.publish(sceneId);

// Unpublish scene
await client.scenes.unpublish(sceneId);
```

### LLM Integration

```javascript
// Send a message to the LLM
const response = await client.llm.sendMessage({
  message: 'Tell me about this chair',
  context: {
    assetId: 'asset-id',
    sceneId: 'scene-id',
  },
});

// Get conversation history
const history = await client.llm.getConversationHistory(conversationId);

// Create a new conversation
const conversation = await client.llm.createConversation({
  title: 'Product Inquiry',
  context: {
    assetId: 'asset-id',
  },
});

// Send a message in a conversation
const response = await client.llm.sendMessageInConversation(conversationId, {
  message: 'What are the dimensions of this chair?',
});
```

### Offline Mode

```javascript
// Configure offline mode
client.offline.configure({
  enabled: true,
  syncInterval: 60, // seconds
  maxCacheSize: 500, // MB
  persistentCache: true,
});

// Check if offline
const isOffline = client.offline.isOffline();

// Manually sync
await client.offline.sync();

// Preload assets for offline use
await client.offline.preloadAssets({
  assets: [assetId1, assetId2],
  scenes: [sceneId],
});

// Get offline storage usage
const usage = await client.offline.getStorageUsage();

// Clear offline cache
await client.offline.clearCache();
```

### Error Handling

```javascript
try {
  const asset = await client.assets.get('non-existent-id');
} catch (error) {
  if (error.code === 'RESOURCE_NOT_FOUND') {
    console.error('Asset not found');
  } else if (error.code === 'AUTHENTICATION_ERROR') {
    console.error('Authentication error');
  } else {
    console.error('Unknown error:', error);
  }
}
```

### React Hooks

The JavaScript SDK also provides React hooks for easy integration with React applications:

```javascript
import { MVSProvider, useAsset, useScene, useLLM } from '@mvs-vr/react-hooks';

function App() {
  return (
    <MVSProvider apiUrl="https://api.mvs-vr.com" apiKey="your-api-key">
      <AssetViewer assetId="asset-id" />
    </MVSProvider>
  );
}

function AssetViewer({ assetId }) {
  const { asset, loading, error } = useAsset(assetId);
  
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <div>
      <h2>{asset.name}</h2>
      <p>{asset.description}</p>
      {/* Render asset */}
    </div>
  );
}
```

## Python SDK

The Python SDK provides a comprehensive set of tools for integrating with the MVS-VR platform in Python applications, including server-side applications, data processing scripts, and machine learning applications.

### Installation

```bash
# pip
pip install mvs-vr-sdk

# poetry
poetry add mvs-vr-sdk
```

### Initialization

```python
from mvs_vr_sdk import MVSClient

# Initialize with API key
client = MVSClient(
    api_url="https://api.mvs-vr.com",
    api_key="your-api-key"
)

# Or initialize with auth token
client = MVSClient(
    api_url="https://api.mvs-vr.com",
    auth_token="your-auth-token"
)

# With additional options
client = MVSClient(
    api_url="https://api.mvs-vr.com",
    api_key="your-api-key",
    timeout=30,  # 30 seconds
    retries=3,
    retry_delay=1,  # 1 second
    debug=True
)
```

### Authentication

```python
# Login with email and password
auth_result = client.auth.login(
    email="<EMAIL>",
    password="password"
)
user = auth_result["user"]
access_token = auth_result["access_token"]
refresh_token = auth_result["refresh_token"]

# Login with API key
auth_result = client.auth.login_with_api_key("your-api-key")

# Refresh token
new_tokens = client.auth.refresh_token(refresh_token)
new_access_token = new_tokens["access_token"]

# Logout
client.auth.logout()

# Get current user
user = client.auth.get_user()

# Check if user has permission
has_permission = client.auth.has_permission("assets.create")
```

### Asset Management

```python
# Create an asset
asset = client.assets.create({
    "name": "Example Asset",
    "type": "model",
    "tags": ["furniture", "chair"]
})

# Upload asset file
with open("model.glb", "rb") as file:
    upload_result = client.assets.upload(
        asset_id=asset["id"],
        file=file,
        on_progress=lambda progress: print(f"Upload progress: {progress['percent']}%")
    )

# Get asset by ID
asset = client.assets.get(asset_id)

# List assets
assets = client.assets.list(
    type="model",
    tags=["furniture"],
    page=1,
    limit=20,
    sort="created_at:desc"
)

# Update asset
updated_asset = client.assets.update(
    asset_id,
    {
        "name": "Updated Asset Name",
        "tags": ["furniture", "chair", "new-tag"]
    }
)

# Delete asset
client.assets.delete(asset_id)

# Search assets
search_results = client.assets.search(
    "chair",
    type="model",
    limit=20
)
```

### Scene Management

```python
# Create a scene
scene = client.scenes.create({
    "name": "Example Scene",
    "description": "An example scene",
    "assets": [asset_id1, asset_id2],
    "configuration": {
        # Scene configuration
    }
})

# Get scene by ID
scene = client.scenes.get(scene_id)

# List scenes
scenes = client.scenes.list(
    page=1,
    limit=20,
    sort="created_at:desc"
)

# Update scene
updated_scene = client.scenes.update(
    scene_id,
    {
        "name": "Updated Scene Name",
        "assets": [asset_id1, asset_id2, asset_id3]
    }
)

# Delete scene
client.scenes.delete(scene_id)

# Publish scene
client.scenes.publish(scene_id)

# Unpublish scene
client.scenes.unpublish(scene_id)
```

### Error Handling

```python
from mvs_vr_sdk.exceptions import MVSError, ResourceNotFoundError, AuthenticationError

try:
    asset = client.assets.get("non-existent-id")
except ResourceNotFoundError:
    print("Asset not found")
except AuthenticationError:
    print("Authentication error")
except MVSError as e:
    print(f"MVS error: {e.code} - {e.message}")
except Exception as e:
    print(f"Unknown error: {str(e)}")
```

## C++ SDK

The C++ SDK provides a comprehensive set of tools for integrating with the MVS-VR platform in C++ applications, including Unreal Engine plugins, native applications, and high-performance computing applications.

### Installation

#### CMake

```cmake
# CMakeLists.txt
find_package(MVSVR REQUIRED)
target_link_libraries(your_target PRIVATE MVSVR::SDK)
```

#### Vcpkg

```bash
vcpkg install mvs-vr-sdk
```

### Initialization

```cpp
#include <mvsvr/Client.h>

// Initialize with API key
mvsvr::Client client("https://api.mvs-vr.com", "your-api-key");

// Or initialize with auth token
mvsvr::Client client("https://api.mvs-vr.com", mvsvr::AuthToken("your-auth-token"));

// With additional options
mvsvr::ClientOptions options;
options.timeout = 30000; // 30 seconds
options.retries = 3;
options.retryDelay = 1000; // 1 second
options.debug = true;

mvsvr::Client client("https://api.mvs-vr.com", "your-api-key", options);
```

### Authentication

```cpp
// Login with email and password
mvsvr::AuthResult authResult = client.Auth().Login("<EMAIL>", "password");
mvsvr::User user = authResult.GetUser();
std::string accessToken = authResult.GetAccessToken();
std::string refreshToken = authResult.GetRefreshToken();

// Login with API key
mvsvr::AuthResult authResult = client.Auth().LoginWithApiKey("your-api-key");

// Refresh token
mvsvr::TokenResult tokenResult = client.Auth().RefreshToken(refreshToken);
std::string newAccessToken = tokenResult.GetAccessToken();

// Logout
client.Auth().Logout();

// Get current user
mvsvr::User user = client.Auth().GetUser();

// Check if user has permission
bool hasPermission = client.Auth().HasPermission("assets.create");
```

### Asset Management

```cpp
// Create an asset
mvsvr::AssetCreateParams params;
params.name = "Example Asset";
params.type = "model";
params.tags = {"furniture", "chair"};

mvsvr::Asset asset = client.Assets().Create(params);

// Upload asset file
mvsvr::UploadProgressCallback progressCallback = [](float percent) {
    std::cout << "Upload progress: " << percent << "%" << std::endl;
};

mvsvr::UploadResult uploadResult = client.Assets().Upload(
    asset.GetId(),
    "path/to/model.glb",
    progressCallback
);

// Get asset by ID
mvsvr::Asset asset = client.Assets().Get(assetId);

// List assets
mvsvr::AssetListParams listParams;
listParams.type = "model";
listParams.tags = {"furniture"};
listParams.page = 1;
listParams.limit = 20;
listParams.sort = "createdAt:desc";

mvsvr::AssetList assets = client.Assets().List(listParams);

// Update asset
mvsvr::AssetUpdateParams updateParams;
updateParams.name = "Updated Asset Name";
updateParams.tags = {"furniture", "chair", "new-tag"};

mvsvr::Asset updatedAsset = client.Assets().Update(assetId, updateParams);

// Delete asset
client.Assets().Delete(assetId);

// Search assets
mvsvr::AssetSearchParams searchParams;
searchParams.query = "chair";
searchParams.type = "model";
searchParams.limit = 20;

mvsvr::AssetList searchResults = client.Assets().Search(searchParams);
```

### Error Handling

```cpp
#include <mvsvr/Exceptions.h>

try {
    mvsvr::Asset asset = client.Assets().Get("non-existent-id");
} catch (const mvsvr::ResourceNotFoundException& e) {
    std::cerr << "Asset not found: " << e.what() << std::endl;
} catch (const mvsvr::AuthenticationException& e) {
    std::cerr << "Authentication error: " << e.what() << std::endl;
} catch (const mvsvr::MVSException& e) {
    std::cerr << "MVS error: " << e.GetCode() << " - " << e.what() << std::endl;
} catch (const std::exception& e) {
    std::cerr << "Unknown error: " << e.what() << std::endl;
}
```

## C# SDK

The C# SDK provides a comprehensive set of tools for integrating with the MVS-VR platform in C# applications, including Unity applications, .NET applications, and Xamarin applications.

### Installation

```bash
# NuGet
dotnet add package MVSVR.SDK

# Package Manager
Install-Package MVSVR.SDK
```

### Initialization

```csharp
using MVSVR;

// Initialize with API key
var client = new MVSClient("https://api.mvs-vr.com", "your-api-key");

// Or initialize with auth token
var client = new MVSClient("https://api.mvs-vr.com", new AuthToken("your-auth-token"));

// With additional options
var options = new ClientOptions
{
    Timeout = TimeSpan.FromSeconds(30),
    Retries = 3,
    RetryDelay = TimeSpan.FromSeconds(1),
    Debug = true
};

var client = new MVSClient("https://api.mvs-vr.com", "your-api-key", options);
```

### Authentication

```csharp
// Login with email and password
var authResult = await client.Auth.LoginAsync("<EMAIL>", "password");
var user = authResult.User;
var accessToken = authResult.AccessToken;
var refreshToken = authResult.RefreshToken;

// Login with API key
var authResult = await client.Auth.LoginWithApiKeyAsync("your-api-key");

// Refresh token
var tokenResult = await client.Auth.RefreshTokenAsync(refreshToken);
var newAccessToken = tokenResult.AccessToken;

// Logout
await client.Auth.LogoutAsync();

// Get current user
var user = await client.Auth.GetUserAsync();

// Check if user has permission
var hasPermission = await client.Auth.HasPermissionAsync("assets.create");
```

### Asset Management

```csharp
// Create an asset
var asset = await client.Assets.CreateAsync(new AssetCreateParams
{
    Name = "Example Asset",
    Type = "model",
    Tags = new[] { "furniture", "chair" }
});

// Upload asset file
var uploadResult = await client.Assets.UploadAsync(
    asset.Id,
    "path/to/model.glb",
    progress => Console.WriteLine($"Upload progress: {progress.Percent}%")
);

// Get asset by ID
var asset = await client.Assets.GetAsync(assetId);

// List assets
var assets = await client.Assets.ListAsync(new AssetListParams
{
    Type = "model",
    Tags = new[] { "furniture" },
    Page = 1,
    Limit = 20,
    Sort = "CreatedAt:desc"
});

// Update asset
var updatedAsset = await client.Assets.UpdateAsync(assetId, new AssetUpdateParams
{
    Name = "Updated Asset Name",
    Tags = new[] { "furniture", "chair", "new-tag" }
});

// Delete asset
await client.Assets.DeleteAsync(assetId);

// Search assets
var searchResults = await client.Assets.SearchAsync(new AssetSearchParams
{
    Query = "chair",
    Type = "model",
    Limit = 20
});
```

### Error Handling

```csharp
using MVSVR.Exceptions;

try
{
    var asset = await client.Assets.GetAsync("non-existent-id");
}
catch (ResourceNotFoundException ex)
{
    Console.Error.WriteLine($"Asset not found: {ex.Message}");
}
catch (AuthenticationException ex)
{
    Console.Error.WriteLine($"Authentication error: {ex.Message}");
}
catch (MVSException ex)
{
    Console.Error.WriteLine($"MVS error: {ex.Code} - {ex.Message}");
}
catch (Exception ex)
{
    Console.Error.WriteLine($"Unknown error: {ex.Message}");
}
```

## SDK Versioning

The MVS-VR SDKs follow semantic versioning (SemVer):

- **Major version**: Breaking changes
- **Minor version**: New features, non-breaking changes
- **Patch version**: Bug fixes, non-breaking changes

### Version Compatibility

| SDK Version | API Version | Minimum Required API Version |
|-------------|-------------|------------------------------|
| 1.0.x       | 1.0         | 1.0                          |
| 1.1.x       | 1.1         | 1.0                          |
| 2.0.x       | 2.0         | 2.0                          |

### Upgrading

When upgrading to a new version of the SDK, refer to the migration guides:

- [JavaScript SDK Migration Guide](./js-sdk-migration.md)
- [Python SDK Migration Guide](./python-sdk-migration.md)
- [C++ SDK Migration Guide](./cpp-sdk-migration.md)
- [C# SDK Migration Guide](./csharp-sdk-migration.md)

## Common Patterns

### Pagination

All SDKs support pagination for list operations:

```javascript
// JavaScript
const allAssets = [];
let page = 1;
const limit = 100;

while (true) {
  const result = await client.assets.list({ page, limit });
  allAssets.push(...result.items);
  
  if (result.items.length < limit || !result.hasMore) {
    break;
  }
  
  page++;
}
```

```python
# Python
all_assets = []
page = 1
limit = 100

while True:
    result = client.assets.list(page=page, limit=limit)
    all_assets.extend(result["items"])
    
    if len(result["items"]) < limit or not result["has_more"]:
        break
    
    page += 1
```

### Batch Operations

All SDKs support batch operations for improved performance:

```javascript
// JavaScript
const results = await client.batch.create('assets', [
  { name: 'Asset 1', type: 'model' },
  { name: 'Asset 2', type: 'texture' },
]);
```

```python
# Python
results = client.batch.create('assets', [
    {"name": "Asset 1", "type": "model"},
    {"name": "Asset 2", "type": "texture"},
])
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check that your API credentials are correct
   - Ensure your token has not expired
   - Verify that you have the necessary permissions

2. **Rate Limiting**
   - Implement exponential backoff for retries
   - Use batch operations to reduce the number of API calls
   - Consider caching frequently accessed data

3. **Network Issues**
   - Check your network connection
   - Verify that the API URL is correct
   - Implement proper error handling and retries

### Getting Help

If you encounter issues not covered in this guide, you can:

- Check the [API Reference](./API_REFERENCE.md)
- Visit the [MVS-VR Developer Forum](https://forum.mvs-vr.com)
- Contact <NAME_EMAIL>
