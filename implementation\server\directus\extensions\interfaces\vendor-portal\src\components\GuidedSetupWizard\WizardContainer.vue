<template>
  <div class="wizard-container">
    <div class="wizard-header">
      <div class="wizard-title-section">
        <h1 class="wizard-title">{{ title }}</h1>
        <p class="wizard-description">{{ description }}</p>
      </div>

      <div class="wizard-progress">
        <div class="progress-bar">
          <div
            class="progress-bar-fill"
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        <div class="progress-steps">
          <div
            v-for="(step, index) in steps"
            :key="index"
            class="progress-step"
            :class="{
              'active': currentStepIndex === index,
              'completed': isStepCompleted(index),
              'clickable': allowStepNavigation && isStepAccessible(index),
              'disabled': !isStepAccessible(index)
            }"
            @click="handleStepClick(index)"
            @keydown.enter="handleStepClick(index)"
            @keydown.space="handleStepClick(index)"
            tabindex="0"
            role="button"
            :aria-label="`Go to step ${index + 1}: ${step.title}`"
            :aria-disabled="!isStepAccessible(index)"
            :aria-current="currentStepIndex === index ? 'step' : null"
          >
            <div class="step-indicator">
              <span v-if="isStepCompleted(index)" class="step-completed-icon" aria-hidden="true">✓</span>
              <span v-else class="step-number" aria-hidden="true">{{ index + 1 }}</span>
            </div>
            <span class="step-title">{{ step.title }}</span>
            <span v-if="isStepAccessible(index) && !isStepCompleted(index) && currentStepIndex !== index"
                  class="step-status">Available</span>
            <span v-if="!isStepAccessible(index)" class="step-status">Locked</span>
          </div>
        </div>
      </div>
    </div>

    <div class="wizard-content">
      <div v-if="currentStepDependencyErrors.length > 0" class="dependency-errors">
        <h3 class="dependency-errors-title">Please resolve the following issues:</h3>
        <ul class="dependency-errors-list">
          <li v-for="(error, index) in currentStepDependencyErrors" :key="index" class="dependency-error-item">
            {{ error }}
          </li>
        </ul>
      </div>

      <transition name="fade" mode="out-in">
        <component
          :is="currentStep.component"
          :key="currentStepIndex"
          :step-data="currentStepData"
          :disabled-fields="dependencyManager ? dependencyManager.getDisabledFields(currentStepIndex) : []"
          @update:step-data="updateStepData"
          @validate="validateCurrentStep"
        ></component>
      </transition>
    </div>

    <div class="wizard-footer">
      <div class="wizard-navigation">
        <button
          v-if="showBackButton && !isFirstStep"
          class="wizard-back-button"
          @click="goToPreviousStep"
        >
          Back
        </button>

        <button
          v-if="!isLastStep"
          class="wizard-next-button"
          :disabled="!canProceedToNextStep"
          @click="goToNextStep"
        >
          {{ nextButtonText }}
        </button>

        <button
          v-else
          class="wizard-finish-button"
          :disabled="!isCurrentStepValid"
          @click="completeWizard"
        >
          {{ finishButtonText }}
        </button>

        <button
          v-if="showSaveButton"
          class="wizard-save-button"
          @click="saveProgress"
        >
          Save Progress
        </button>
      </div>

      <div v-if="showHelpSection && currentStep.helpTitle" class="wizard-help">
        <div class="help-header">
          <h3 class="help-title">{{ currentStep.helpTitle }}</h3>
          <button class="help-toggle" @click="toggleHelp">
            {{ isHelpExpanded ? 'Hide Help' : 'Show Help' }}
          </button>
        </div>

        <div v-if="isHelpExpanded" class="help-content">
          <div v-html="currentStep.helpContent"></div>

          <div v-if="currentStep.videoTutorial" class="help-video">
            <h4>Video Tutorial</h4>
            <video
              controls
              :src="currentStep.videoTutorial"
              poster="/assets/video-placeholder.jpg"
            ></video>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import StepDependencyManager from './utils/StepDependencyManager';

export default {
  name: 'WizardContainer',

  props: {
    title: {
      type: String,
      default: 'Guided Setup Wizard'
    },
    description: {
      type: String,
      default: 'Complete the following steps to set up your account'
    },
    steps: {
      type: Array,
      required: true,
      validator: (steps) => {
        return steps.every(step =>
          step.title &&
          step.component &&
          typeof step.component === 'object'
        );
      }
    },
    initialStepIndex: {
      type: Number,
      default: 0
    },
    allowStepNavigation: {
      type: Boolean,
      default: true
    },
    showBackButton: {
      type: Boolean,
      default: true
    },
    showSaveButton: {
      type: Boolean,
      default: true
    },
    showHelpSection: {
      type: Boolean,
      default: true
    },
    nextButtonText: {
      type: String,
      default: 'Next'
    },
    finishButtonText: {
      type: String,
      default: 'Finish'
    },
    storageKey: {
      type: String,
      default: 'wizard-progress'
    },
    stepDependencies: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      currentStepIndex: this.initialStepIndex,
      stepsData: {},
      stepsValidity: {},
      isHelpExpanded: false,
      dependencyManager: null,
      dependencyErrors: {}
    };
  },

  computed: {
    currentStep() {
      return this.steps[this.currentStepIndex];
    },

    currentStepData() {
      return this.stepsData[this.currentStepIndex] || {};
    },

    isFirstStep() {
      return this.currentStepIndex === 0;
    },

    isLastStep() {
      return this.currentStepIndex === this.steps.length - 1;
    },

    isCurrentStepValid() {
      return !!this.stepsValidity[this.currentStepIndex];
    },

    currentStepDependenciesValid() {
      if (!this.dependencyManager) return true;

      const validationResult = this.dependencyManager.validateDependencies(this.currentStepIndex);
      this.dependencyErrors = validationResult.errors || [];

      return validationResult.isValid;
    },

    canProceedToNextStep() {
      return this.isCurrentStepValid && this.currentStepDependenciesValid;
    },

    currentStepDependencyErrors() {
      return this.dependencyErrors || [];
    },

    progressPercentage() {
      const completedSteps = Object.values(this.stepsValidity).filter(Boolean).length;
      const totalSteps = this.steps.length;

      return Math.round((completedSteps / totalSteps) * 100);
    }
  },

  created() {
    // Initialize dependency manager
    this.dependencyManager = new StepDependencyManager(
      this.stepDependencies,
      (stepIndex) => this.stepsData[stepIndex] || {},
      (stepIndex, data) => this.updateStepDataWithoutPropagation(stepIndex, data)
    );

    this.loadSavedProgress();
    this.trackWizardStart();
  },

  mounted() {
    // Add keyboard event listeners for navigation
    window.addEventListener('keydown', this.handleKeyboardNavigation);
  },

  beforeDestroy() {
    // Remove keyboard event listeners
    window.removeEventListener('keydown', this.handleKeyboardNavigation);
  },

  methods: {
    isStepCompleted(index) {
      return !!this.stepsValidity[index];
    },

    isStepAccessible(index) {
      // A step is accessible if all previous steps are completed
      // or if it's the current step or a completed step
      if (index === this.currentStepIndex) return true;
      if (this.isStepCompleted(index)) return true;

      for (let i = 0; i < index; i++) {
        if (!this.isStepCompleted(i)) return false;
      }

      return true;
    },

    handleStepClick(index) {
      if (this.allowStepNavigation && this.isStepAccessible(index)) {
        this.goToStep(index);
      }
    },

    goToStep(index) {
      if (index >= 0 && index < this.steps.length) {
        this.currentStepIndex = index;
        this.trackStepView(index);
      }
    },

    goToNextStep() {
      if (!this.isLastStep && this.canProceedToNextStep) {
        this.goToStep(this.currentStepIndex + 1);
      }
    },

    goToPreviousStep() {
      if (!this.isFirstStep) {
        this.goToStep(this.currentStepIndex - 1);
      }
    },

    updateStepData(data) {
      // Update the current step data
      this.$set(this.stepsData, this.currentStepIndex, data);

      // Propagate data to dependent steps
      if (this.dependencyManager) {
        this.dependencyManager.propagateData(this.currentStepIndex, data);
      }

      // Save progress
      this.saveProgress();
    },

    updateStepDataWithoutPropagation(stepIndex, data) {
      // Update step data without triggering propagation (used by dependency manager)
      this.$set(this.stepsData, stepIndex, data);
      this.saveProgress();
    },

    validateCurrentStep(isValid) {
      this.$set(this.stepsValidity, this.currentStepIndex, isValid);
      this.saveProgress();
    },

    completeWizard() {
      if (this.isLastStep && this.isCurrentStepValid) {
        this.trackWizardCompletion();
        this.$emit('complete', this.stepsData);
        this.clearSavedProgress();
      }
    },

    saveProgress() {
      // Save progress to local storage
      const progressData = {
        stepsData: this.stepsData,
        stepsValidity: this.stepsValidity,
        currentStepIndex: this.currentStepIndex
      };

      localStorage.setItem(this.storageKey, JSON.stringify(progressData));

      // Emit save progress event
      this.$emit('save-progress', progressData);
    },

    loadSavedProgress() {
      try {
        const savedProgress = localStorage.getItem(this.storageKey);

        if (savedProgress) {
          const progressData = JSON.parse(savedProgress);

          // Restore saved data
          this.stepsData = progressData.stepsData || {};
          this.stepsValidity = progressData.stepsValidity || {};
          this.currentStepIndex = progressData.currentStepIndex || 0;

          // Emit load progress event
          this.$emit('load-progress', progressData);
        }
      } catch (error) {
        console.error('Error loading saved progress:', error);
      }
    },

    clearSavedProgress() {
      localStorage.removeItem(this.storageKey);
    },

    toggleHelp() {
      this.isHelpExpanded = !this.isHelpExpanded;
    },

    trackStepView(index) {
      this.$emit('analytics', {
        type: 'step_view',
        data: {
          stepIndex: index,
          stepTitle: this.steps[index].title,
          timestamp: new Date().toISOString()
        }
      });
    },

    trackWizardStart() {
      this.$emit('analytics', {
        type: 'wizard_start',
        data: {
          timestamp: new Date().toISOString()
        }
      });
    },

    trackWizardCompletion() {
      this.$emit('analytics', {
        type: 'wizard_complete',
        data: {
          timestamp: new Date().toISOString()
        }
      });
    },

    handleKeyboardNavigation(event) {
      // Skip if user is typing in an input field
      if (
        event.target.tagName === 'INPUT' ||
        event.target.tagName === 'TEXTAREA' ||
        event.target.tagName === 'SELECT' ||
        event.target.isContentEditable
      ) {
        return;
      }

      // Handle keyboard navigation
      switch (event.key) {
        case 'ArrowRight':
        case 'PageDown':
          // Navigate to next step
          if (!this.isLastStep && this.canProceedToNextStep) {
            event.preventDefault();
            this.goToNextStep();
          }
          break;

        case 'ArrowLeft':
        case 'PageUp':
          // Navigate to previous step
          if (!this.isFirstStep) {
            event.preventDefault();
            this.goToPreviousStep();
          }
          break;

        case 'Home':
          // Navigate to first step
          if (!this.isFirstStep && this.allowStepNavigation) {
            event.preventDefault();
            this.goToStep(0);
          }
          break;

        case 'End':
          // Navigate to last accessible step
          if (!this.isLastStep && this.allowStepNavigation) {
            event.preventDefault();
            // Find the last accessible step
            let lastAccessibleStep = this.steps.length - 1;
            while (lastAccessibleStep > 0 && !this.isStepAccessible(lastAccessibleStep)) {
              lastAccessibleStep--;
            }
            this.goToStep(lastAccessibleStep);
          }
          break;

        case 'Escape':
          // Show exit confirmation
          event.preventDefault();
          this.$emit('exit-requested');
          break;

        case 'Enter':
          // Proceed to next step or complete wizard
          if (this.isLastStep && this.isCurrentStepValid) {
            event.preventDefault();
            this.completeWizard();
          } else if (!this.isLastStep && this.canProceedToNextStep) {
            event.preventDefault();
            this.goToNextStep();
          }
          break;

        case '1': case '2': case '3': case '4': case '5':
        case '6': case '7': case '8': case '9':
          // Navigate to step by number (if accessible)
          if (this.allowStepNavigation) {
            const stepIndex = parseInt(event.key) - 1;
            if (stepIndex >= 0 && stepIndex < this.steps.length && this.isStepAccessible(stepIndex)) {
              event.preventDefault();
              this.goToStep(stepIndex);
            }
          }
          break;
      }
    }
  }
};
</script>

<style scoped>
.wizard-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.wizard-header {
  padding: 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.wizard-title-section {
  margin-bottom: 24px;
}

.wizard-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.wizard-description {
  font-size: 16px;
  color: var(--theme--foreground-subdued);
  margin: 0;
}

.wizard-progress {
  margin-top: 24px;
}

.progress-bar {
  height: 4px;
  background-color: var(--theme--background-subdued);
  border-radius: 2px;
  margin-bottom: 16px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--theme--primary);
  transition: width 0.3s ease;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  position: relative;
  outline: none;
}

.step-indicator {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.progress-step.active .step-indicator {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  transform: scale(1.1);
  box-shadow: 0 0 0 4px rgba(var(--theme--primary-rgb), 0.2);
}

.progress-step.completed .step-indicator {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.step-title {
  font-size: 13px;
  color: var(--theme--foreground-subdued);
  text-align: center;
  margin-bottom: 4px;
  transition: color 0.2s ease;
}

.step-status {
  font-size: 11px;
  color: var(--theme--foreground-subdued);
  opacity: 0.7;
}

.progress-step.active .step-title {
  color: var(--theme--foreground);
  font-weight: 600;
}

.progress-step.clickable {
  cursor: pointer;
}

.progress-step.clickable:hover .step-indicator {
  border-color: var(--theme--primary);
  transform: scale(1.05);
}

.progress-step.clickable:focus .step-indicator {
  border-color: var(--theme--primary);
  box-shadow: 0 0 0 4px rgba(var(--theme--primary-rgb), 0.2);
}

.progress-step.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.progress-step.disabled .step-indicator {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
}

/* Pulse animation for active step */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--theme--primary-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(var(--theme--primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--theme--primary-rgb), 0);
  }
}

.progress-step.active .step-indicator {
  animation: pulse 2s infinite;
}

.wizard-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.dependency-errors {
  margin-bottom: 24px;
  padding: 16px;
  background-color: rgba(var(--theme--warning-rgb), 0.1);
  border-left: 4px solid var(--theme--warning);
  border-radius: 4px;
}

.dependency-errors-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--theme--warning);
}

.dependency-errors-list {
  margin: 0;
  padding-left: 20px;
}

.dependency-error-item {
  font-size: 14px;
  margin-bottom: 4px;
  color: var(--theme--warning-subdued);
}

.wizard-footer {
  padding: 24px;
  border-top: 1px solid var(--theme--border-color);
}

.wizard-navigation {
  display: flex;
  justify-content: space-between;
}

.wizard-back-button,
.wizard-next-button,
.wizard-finish-button,
.wizard-save-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.wizard-back-button {
  background-color: transparent;
  border: 1px solid var(--theme--border-color);
  color: var(--theme--foreground);
}

.wizard-next-button,
.wizard-finish-button {
  background-color: var(--theme--primary);
  border: none;
  color: var(--theme--primary-background);
}

.wizard-save-button {
  background-color: transparent;
  border: 1px solid var(--theme--border-color);
  color: var(--theme--foreground-subdued);
}

.wizard-back-button:hover {
  background-color: var(--theme--background-accent);
}

.wizard-next-button:hover,
.wizard-finish-button:hover {
  background-color: var(--theme--primary-accent);
}

.wizard-save-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.wizard-next-button:disabled,
.wizard-finish-button:disabled {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
  cursor: not-allowed;
}

.wizard-help {
  margin-top: 24px;
  border-top: 1px solid var(--theme--border-color);
  padding-top: 16px;
}

.help-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.help-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.help-toggle {
  background-color: transparent;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.help-toggle:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.help-content {
  padding: 16px;
  background-color: var(--theme--background-subdued);
  border-radius: 4px;
}

.help-video {
  margin-top: 16px;
}

.help-video h4 {
  font-size: 14px;
  margin: 0 0 8px 0;
}

.help-video video {
  width: 100%;
  max-width: 400px;
  border-radius: 4px;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}
</style>
