import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { SceneService } from '../../services/scene-service';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  scene_id: z.string().uuid(),
});

// Define the request body schema for PATCH
const PatchBodySchema = z.object({
  name: z.string().optional(),
  description: z.string().nullable().optional(),
  configuration: z.record(z.any()).optional(),
});

/**
 * Scene API endpoint
 *
 * This endpoint handles getting, updating, and deleting a specific scene.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse({
      scene_id: req.query.scene_id,
    });

    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { scene_id } = queryResult.data;

    // Log the request
    logger.info('Scene request', {
      scene_id,
      method: req.method,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create scene service
    const sceneService = new SceneService(supabase);

    // Get scene
    const scene = await sceneService.getScene(scene_id);
    if (!scene) {
      return res.status(404).json({ error: 'Scene not found' });
    }

    // Check if user has permission to access this scene
    if (session.user.id !== scene.vendor_id && session.user.role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Handle GET request
    if (req.method === 'GET') {
      // Return scene
      return res.status(200).json({ scene });
    }

    // Handle PATCH request (update scene)
    if (req.method === 'PATCH') {
      // Validate request body
      const bodyResult = PatchBodySchema.safeParse(req.body);
      if (!bodyResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid request body', details: bodyResult.error.format() });
      }

      // Extract request data
      const { name, description, configuration } = bodyResult.data;

      // Update scene
      const updatedScene = await sceneService.updateScene(
        scene_id,
        name,
        description,
        configuration,
      );
      if (!updatedScene) {
        return res.status(500).json({ error: 'Failed to update scene' });
      }

      // Return updated scene
      return res.status(200).json({ scene: updatedScene });
    }

    // Handle DELETE request
    if (req.method === 'DELETE') {
      // Delete scene
      const success = await sceneService.deleteScene(scene_id);
      if (!success) {
        return res.status(500).json({ error: 'Failed to delete scene' });
      }

      // Return success
      return res.status(204).end();
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Unexpected error in scene endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
