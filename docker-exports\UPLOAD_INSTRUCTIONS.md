# Docker Image Upload Instructions for DigitalOcean (Windows)

## Overview
This directory contains exported Docker images and deployment scripts for MVS-VR v2.

## Files Included
- `*.tar` or `*.tar.zip` - Exported Docker images
- `export-manifest.json` - Image metadata and information
- `load-images.ps1` - PowerShell script to load images on target server
- `deploy-to-digitalocean.sh` - Complete deployment script
- `UPLOAD_INSTRUCTIONS.md` - This file

## Method 1: Upload via SCP/RSYNC (Recommended)

### Using Windows Subsystem for Linux (WSL)
```bash
# From WSL terminal
rsync -avz --progress docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/docker-exports/
```

### Using PowerShell with SCP
```powershell
# Install OpenSSH client if not available
# Upload files
scp -r docker-exports/ root@YOUR_SERVER_IP:/opt/mvs-vr/
```

### Using WinSCP (GUI Option)
1. Download and install WinSCP
2. Connect to your DigitalOcean server
3. Upload the entire docker-exports folder to /opt/mvs-vr/

## Method 2: Using DigitalOcean Container Registry

### Step 1: Push images to registry (from Windows)
```powershell
# Run the export script with registry push
.\scripts\export-docker-images.ps1 -PushRegistry

# Or manually push after export
doctl registry login
docker push registry.digitalocean.com/mvs-vr/api-gateway:VERSION_TAG
```

## Verification

After deployment, verify services are running:
```bash
# Check service status
docker-compose ps

# Check service health
curl http://localhost:3000/health
curl http://localhost:8055/server/health
```

## Windows-Specific Notes

### PowerShell Execution Policy
If you encounter execution policy errors:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Docker Desktop Requirements
- Ensure Docker Desktop is running
- Enable WSL 2 backend for better performance
- Allocate sufficient resources (4GB+ RAM, 2+ CPUs)

### File Path Considerations
- Use forward slashes in Docker commands
- Be aware of Windows path length limitations
- Consider using shorter export directory paths
