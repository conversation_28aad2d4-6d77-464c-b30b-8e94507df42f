import Vue from 'vue';
import VueRouter from 'vue-router';
import AdminDashboard from '../components/AdminDashboard.vue';
import SystemMonitoringDashboard from '../components/SystemMonitoring/SystemMonitoringDashboard.vue';
import UserManagement from '../components/UserManagement/UserManagement.vue';
import VendorManagement from '../components/VendorManagement/VendorManagement.vue';
import ContentManagement from '../components/ContentManagement/ContentManagement.vue';
import Settings from '../components/Settings/Settings.vue';

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    redirect: '/admin/dashboard',
  },
  {
    path: '/admin/dashboard',
    name: 'Dashboard',
    component: AdminDashboard,
  },
  {
    path: '/admin/system-monitoring',
    name: 'SystemMonitoring',
    component: SystemMonitoringDashboard,
  },
  {
    path: '/admin/users',
    name: 'UserManagement',
    component: UserManagement,
  },
  {
    path: '/admin/vendors',
    name: 'VendorManagement',
    component: VendorManagement,
  },
  {
    path: '/admin/content',
    name: 'ContentManagement',
    component: ContentManagement,
  },
  {
    path: '/admin/settings',
    name: 'Settings',
    component: Settings,
  },
];

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes,
});

// Navigation guard for authentication
router.beforeEach((to, from, next) => {
  // Check if user is authenticated
  const isAuthenticated = localStorage.getItem('admin_token');

  // If not authenticated and not on login page, redirect to login
  if (!isAuthenticated && to.path !== '/admin/login') {
    next('/admin/login');
  } else {
    next();
  }
});

export default router;
