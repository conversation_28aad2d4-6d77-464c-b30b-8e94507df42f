<template>
  <div class="help-panel" :class="{ 'help-panel-open': isOpen }">
    <div class="help-panel-toggle" @click="togglePanel">
      <i class="material-icons">{{ isOpen ? 'close' : 'help_outline' }}</i>
      <span v-if="!isOpen">Help</span>
    </div>
    
    <div class="help-panel-content">
      <div class="help-panel-header">
        <h3 class="help-panel-title">{{ title }}</h3>
        <div class="help-panel-tabs" v-if="tabs.length > 1">
          <button 
            v-for="(tab, index) in tabs" 
            :key="index"
            class="help-panel-tab"
            :class="{ 'active': activeTab === index }"
            @click="activeTab = index"
          >
            {{ tab.title }}
          </button>
        </div>
      </div>
      
      <div class="help-panel-body">
        <div v-if="tabs.length > 0" class="help-panel-tab-content">
          <div v-html="tabs[activeTab].content"></div>
          
          <div v-if="tabs[activeTab].videoUrl" class="help-panel-video">
            <h4>Video Tutorial</h4>
            <div class="help-panel-video-container">
              <iframe 
                :src="tabs[activeTab].videoUrl" 
                frameborder="0" 
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                allowfullscreen
              ></iframe>
            </div>
          </div>
        </div>
        
        <div v-else class="help-panel-default-content">
          <slot></slot>
        </div>
      </div>
      
      <div class="help-panel-footer">
        <a v-if="documentationUrl" :href="documentationUrl" target="_blank" class="help-panel-link">
          <i class="material-icons">description</i>
          <span>Full Documentation</span>
        </a>
        <button class="help-panel-close" @click="isOpen = false">Close</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HelpPanel',
  props: {
    title: {
      type: String,
      default: 'Help'
    },
    tabs: {
      type: Array,
      default: () => []
      // Each tab should have: { title, content, videoUrl (optional) }
    },
    documentationUrl: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isOpen: false,
      activeTab: 0
    };
  },
  methods: {
    togglePanel() {
      this.isOpen = !this.isOpen;
    }
  }
};
</script>

<style scoped>
.help-panel {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  pointer-events: none;
}

.help-panel-toggle {
  position: fixed;
  right: 20px;
  bottom: 20px;
  background-color: var(--theme--primary);
  color: white;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  pointer-events: auto;
  transition: background-color 0.2s ease;
  z-index: 1001;
}

.help-panel-toggle:hover {
  background-color: var(--theme--primary-accent);
}

.help-panel-toggle span {
  display: none;
}

.help-panel-content {
  width: 0;
  height: 100%;
  background-color: var(--theme--background);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transition: width 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
}

.help-panel-open .help-panel-content {
  width: 400px;
  border-left: 1px solid var(--theme--border-color);
}

.help-panel-header {
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.help-panel-title {
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
}

.help-panel-tabs {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.help-panel-tab {
  padding: 6px 12px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.help-panel-tab.active {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.help-panel-body {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.help-panel-tab-content {
  line-height: 1.5;
}

.help-panel-video {
  margin-top: 24px;
}

.help-panel-video h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.help-panel-video-container {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: 4px;
}

.help-panel-video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.help-panel-footer {
  padding: 12px 16px;
  border-top: 1px solid var(--theme--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.help-panel-link {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--theme--primary);
  text-decoration: none;
  font-size: 14px;
}

.help-panel-link i {
  font-size: 18px;
}

.help-panel-close {
  padding: 6px 12px;
  border: none;
  background-color: var(--theme--background-accent);
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: var(--theme--foreground);
}

.help-panel-close:hover {
  background-color: var(--theme--background-accent-hover);
}

@media (max-width: 768px) {
  .help-panel-open .help-panel-content {
    width: 100%;
  }
}
</style>
