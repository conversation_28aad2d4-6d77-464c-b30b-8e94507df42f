import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';

// Define the response schema
const BlueprintResponseSchema = z.object({
  blueprint_id: z.string().uuid(),
  vendor_id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable(),
  tags: z.array(z.string()),
  version: z.string(),
  created_at: z.string().datetime(),
  updated_at: z.string().datetime(),
  script: z.record(z.any()),
});

// Define the query parameters schema
const QueryParamsSchema = z.object({
  blueprint_id: z.string().uuid(),
});

/**
 * Blueprint API endpoint
 *
 * This endpoint returns information about a blueprint.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse(req.query);
    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { blueprint_id } = queryResult.data;

    // Log the request
    logger.info('Blueprint request', {
      blueprint_id,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get blueprint
    const { data: blueprint, error: blueprintError } = await supabase
      .from('blueprints')
      .select('id, vendor_id, name, description, tags, script, version, created_at, updated_at')
      .eq('id', blueprint_id)
      .single();

    if (blueprintError) {
      logger.error('Error fetching blueprint', { error: blueprintError, blueprint_id });
      return res.status(500).json({ error: 'Error fetching blueprint' });
    }

    if (!blueprint) {
      return res.status(404).json({ error: 'Blueprint not found' });
    }

    // Prepare response
    const response = {
      status: 'success',
      data: {
        blueprint_id: blueprint.id,
        vendor_id: blueprint.vendor_id,
        name: blueprint.name,
        description: blueprint.description,
        tags: blueprint.tags,
        version: blueprint.version,
        created_at: blueprint.created_at,
        updated_at: blueprint.updated_at,
        script: blueprint.script,
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      },
    };

    // Log success
    logger.info('Blueprint response', {
      blueprint_id,
    });

    // Return response
    return res.status(200).json(response);
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
