/**
 * Comparative Analytics API
 *
 * This file contains API endpoints for comparative analytics data.
 */

import express from 'express';
import { supabase } from '../../lib/supabase';
import { logger } from '../../lib/logger';
import { expressAuthenticate } from '../../middleware/auth';
import { expressRateLimiter } from '../../middleware/rate-limiter';

// Create router
const router: express.Router = express.Router();

/**
 * Compare showroom performance
 *
 * @param req - Request
 * @param res - Response
 */
export const compareShowrooms = async (
  req: express.Request,
  res: express.Response,
): Promise<void> => {
  try {
    const { vendor_id, showroom_ids, metrics, start_date, end_date } = req.query;

    // Validate required parameters
    if (!vendor_id || !showroom_ids) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID and showroom IDs are required',
        },
      });
      return;
    }

    // Parse showroom IDs
    const showroomIdArray = (showroom_ids as string).split(',');

    // Parse metrics
    const metricsArray = metrics
      ? (metrics as string).split(',')
      : ['visits', 'unique_visitors', 'avg_duration', 'bounce_rate'];

    // Build query
    let query = supabase
      .from('showroom_analytics')
      .select('showroom_id, ' + metricsArray.join(', ') + ', created_at')
      .eq('vendor_id', vendor_id)
      .in('showroom_id', showroomIdArray);

    // Add date filters if provided
    if (start_date) {
      query = query.gte('created_at', start_date);
    }

    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error comparing showrooms', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error comparing showrooms',
        },
      });
      return;
    }

    // Process data for comparison
    const processedData = processShowroomComparisonData(data, showroomIdArray, metricsArray);

    // Return data
    res.status(200).json({
      success: true,
      data: processedData,
    });
  } catch (error) {
    logger.error('Error in compareShowrooms', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Compare product performance
 *
 * @param req - Request
 * @param res - Response
 */
export const compareProducts = async (
  req: express.Request,
  res: express.Response,
): Promise<void> => {
  try {
    const { vendor_id, product_ids, metrics, start_date, end_date } = req.query;

    // Validate required parameters
    if (!vendor_id || !product_ids) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID and product IDs are required',
        },
      });
      return;
    }

    // Parse product IDs
    const productIdArray = (product_ids as string).split(',');

    // Parse metrics
    const metricsArray = metrics
      ? (metrics as string).split(',')
      : ['views', 'interactions', 'avg_duration', 'conversion_rate'];

    // Build query
    let query = supabase
      .from('product_analytics')
      .select('product_id, ' + metricsArray.join(', ') + ', created_at')
      .eq('vendor_id', vendor_id)
      .in('product_id', productIdArray);

    // Add date filters if provided
    if (start_date) {
      query = query.gte('created_at', start_date);
    }

    if (end_date) {
      query = query.lte('created_at', end_date);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error comparing products', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error comparing products',
        },
      });
      return;
    }

    // Process data for comparison
    const processedData = processProductComparisonData(data, productIdArray, metricsArray);

    // Return data
    res.status(200).json({
      success: true,
      data: processedData,
    });
  } catch (error) {
    logger.error('Error in compareProducts', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Compare time periods
 *
 * @param req - Request
 * @param res - Response
 */
export const compareTimePeriods = async (
  req: express.Request,
  res: express.Response,
): Promise<void> => {
  try {
    const {
      vendor_id,
      showroom_id,
      metrics,
      period_1_start,
      period_1_end,
      period_2_start,
      period_2_end,
    } = req.query;

    // Validate required parameters
    if (!vendor_id || !period_1_start || !period_1_end || !period_2_start || !period_2_end) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID and time periods are required',
        },
      });
      return;
    }

    // Parse metrics
    const metricsArray = metrics
      ? (metrics as string).split(',')
      : ['visits', 'unique_visitors', 'avg_duration', 'bounce_rate'];

    // Get data for period 1
    const period1Data = await getTimePeriodData(
      vendor_id as string,
      showroom_id as string,
      metricsArray,
      period_1_start as string,
      period_1_end as string,
    );

    // Get data for period 2
    const period2Data = await getTimePeriodData(
      vendor_id as string,
      showroom_id as string,
      metricsArray,
      period_2_start as string,
      period_2_end as string,
    );

    // Compare periods
    const comparison = compareTimePeriodData(period1Data, period2Data, metricsArray);

    // Return data
    res.status(200).json({
      success: true,
      data: {
        period1: {
          start: period_1_start,
          end: period_1_end,
          data: period1Data,
        },
        period2: {
          start: period_2_start,
          end: period_2_end,
          data: period2Data,
        },
        comparison,
      },
    });
  } catch (error) {
    logger.error('Error in compareTimePeriods', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Process showroom comparison data
 *
 * @param data - Raw data
 * @param showroomIds - Showroom IDs
 * @param metrics - Metrics to compare
 * @returns Processed comparison data
 */
function processShowroomComparisonData(data: any[], showroomIds: string[], metrics: string[]) {
  // Group data by showroom
  const groupedData: Record<string, any[]> = {};

  showroomIds.forEach(id => {
    groupedData[id] = data.filter(item => item.showroom_id === id);
  });

  // Calculate aggregates for each showroom
  const result: Record<string, any> = {};

  showroomIds.forEach(id => {
    const showroomData = groupedData[id];

    if (showroomData.length === 0) {
      result[id] = null;
      return;
    }

    const aggregates: Record<string, number> = {};

    metrics.forEach(metric => {
      // Calculate average for the metric
      const values = showroomData.map(item => item[metric] || 0);
      const sum = values.reduce((a, b) => a + b, 0);
      aggregates[metric] = sum / values.length;
    });

    result[id] = aggregates;
  });

  return result;
}

/**
 * Process product comparison data
 *
 * @param data - Raw data
 * @param productIds - Product IDs
 * @param metrics - Metrics to compare
 * @returns Processed comparison data
 */
function processProductComparisonData(data: any[], productIds: string[], metrics: string[]) {
  // Group data by product
  const groupedData: Record<string, any[]> = {};

  productIds.forEach(id => {
    groupedData[id] = data.filter(item => item.product_id === id);
  });

  // Calculate aggregates for each product
  const result: Record<string, any> = {};

  productIds.forEach(id => {
    const productData = groupedData[id];

    if (productData.length === 0) {
      result[id] = null;
      return;
    }

    const aggregates: Record<string, number> = {};

    metrics.forEach(metric => {
      // Calculate average for the metric
      const values = productData.map(item => item[metric] || 0);
      const sum = values.reduce((a, b) => a + b, 0);
      aggregates[metric] = sum / values.length;
    });

    result[id] = aggregates;
  });

  return result;
}

/**
 * Get data for a time period
 *
 * @param vendorId - Vendor ID
 * @param showroomId - Showroom ID
 * @param metrics - Metrics to get
 * @param startDate - Start date
 * @param endDate - End date
 * @returns Time period data
 */
async function getTimePeriodData(
  vendorId: string,
  showroomId: string | undefined,
  metrics: string[],
  startDate: string,
  endDate: string,
) {
  // Build query
  let query = supabase
    .from('showroom_analytics')
    .select('showroom_id, ' + metrics.join(', ') + ', created_at')
    .eq('vendor_id', vendorId)
    .gte('created_at', startDate)
    .lte('created_at', endDate);

  // Add showroom filter if provided
  if (showroomId) {
    query = query.eq('showroom_id', showroomId);
  }

  // Execute query
  const { data, error } = await query;

  if (error) {
    logger.error('Error getting time period data', { error });
    throw error;
  }

  return data || [];
}

/**
 * Compare time period data
 *
 * @param period1Data - Period 1 data
 * @param period2Data - Period 2 data
 * @param metrics - Metrics to compare
 * @returns Comparison data
 */
function compareTimePeriodData(period1Data: any[], period2Data: any[], metrics: string[]) {
  // Calculate aggregates for each period
  const period1Aggregates: Record<string, number> = {};
  const period2Aggregates: Record<string, number> = {};

  metrics.forEach(metric => {
    // Calculate average for period 1
    const period1Values = period1Data.map(item => item[metric] || 0);
    const period1Sum = period1Values.reduce((a, b) => a + b, 0);
    period1Aggregates[metric] = period1Sum / (period1Values.length || 1);

    // Calculate average for period 2
    const period2Values = period2Data.map(item => item[metric] || 0);
    const period2Sum = period2Values.reduce((a, b) => a + b, 0);
    period2Aggregates[metric] = period2Sum / (period2Values.length || 1);
  });

  // Calculate differences and percentages
  const comparison: Record<string, any> = {};

  metrics.forEach(metric => {
    const period1Value = period1Aggregates[metric];
    const period2Value = period2Aggregates[metric];
    const difference = period2Value - period1Value;
    const percentChange = period1Value !== 0 ? (difference / period1Value) * 100 : 0;

    comparison[metric] = {
      period1Value,
      period2Value,
      difference,
      percentChange,
    };
  });

  return comparison;
}

// Register routes
router.get('/comparative/showrooms', expressAuthenticate, expressRateLimiter, compareShowrooms);
router.get('/comparative/products', expressAuthenticate, expressRateLimiter, compareProducts);
router.get(
  '/comparative/time-periods',
  expressAuthenticate,
  expressRateLimiter,
  compareTimePeriods,
);

export default router;
