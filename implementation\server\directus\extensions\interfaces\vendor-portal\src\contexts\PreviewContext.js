import { ref, reactive, computed, provide, inject } from 'vue';
import { logger } from '../shared/utils/logger';

// Symbol for providing/injecting the preview context
const PreviewContextSymbol = Symbol('PreviewContext');

/**
 * Creates and provides the preview context to child components
 * 
 * @param {Object} options - Configuration options for the preview context
 * @returns {Object} The preview context object
 */
export function providePreviewContext(options = {}) {
  // Preview state
  const previewMode = ref(options.initialMode || 'edit'); // 'edit', 'preview', 'split'
  const previewDevice = ref(options.initialDevice || 'desktop'); // 'desktop', 'tablet', 'mobile', 'tv'
  const previewOrientation = ref(options.initialOrientation || 'portrait'); // 'portrait', 'landscape'
  const previewScale = ref(options.initialScale || 1);
  const previewData = reactive(options.initialData || {});
  const previewLoading = ref(false);
  const previewError = ref(null);
  const previewUrl = ref(options.initialUrl || '');
  const previewNetworkCondition = ref(options.initialNetworkCondition || 'online'); // 'online', '3g', '4g', 'slow-2g'
  const lastUpdated = ref(new Date());

  // WebSocket connection for live updates
  const wsConnected = ref(false);
  const wsConnection = ref(null);

  // Computed properties
  const isPreviewMode = computed(() => previewMode.value === 'preview');
  const isEditMode = computed(() => previewMode.value === 'edit');
  const isSplitMode = computed(() => previewMode.value === 'split');
  
  const deviceDimensions = computed(() => {
    switch (previewDevice.value) {
      case 'mobile':
        return previewOrientation.value === 'portrait' ? { width: 375, height: 667 } : { width: 667, height: 375 };
      case 'tablet':
        return previewOrientation.value === 'portrait' ? { width: 768, height: 1024 } : { width: 1024, height: 768 };
      case 'tv':
        return { width: 1920, height: 1080 };
      case 'desktop':
      default:
        return { width: 1280, height: 800 };
    }
  });

  // Methods
  const setPreviewMode = (mode) => {
    if (['edit', 'preview', 'split'].includes(mode)) {
      previewMode.value = mode;
    }
  };

  const setPreviewDevice = (device) => {
    if (['desktop', 'tablet', 'mobile', 'tv'].includes(device)) {
      previewDevice.value = device;
    }
  };

  const setPreviewOrientation = (orientation) => {
    if (['portrait', 'landscape'].includes(orientation)) {
      previewOrientation.value = orientation;
    }
  };

  const setPreviewScale = (scale) => {
    if (scale >= 0.25 && scale <= 2) {
      previewScale.value = scale;
    }
  };

  const setPreviewData = (data) => {
    Object.assign(previewData, data);
    lastUpdated.value = new Date();
  };

  const setPreviewUrl = (url) => {
    previewUrl.value = url;
  };

  const setNetworkCondition = (condition) => {
    if (['online', '3g', '4g', 'slow-2g'].includes(condition)) {
      previewNetworkCondition.value = condition;
    }
  };

  const refreshPreview = async () => {
    previewLoading.value = true;
    previewError.value = null;
    
    try {
      // In a real implementation, this would make an API call to refresh the preview
      // For now, we'll just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      lastUpdated.value = new Date();
      previewLoading.value = false;
    } catch (error) {
      previewError.value = error.message || 'Failed to refresh preview';
      previewLoading.value = false;
    }
  };

  const resetPreview = () => {
    // Reset to initial state
    previewMode.value = options.initialMode || 'edit';
    previewDevice.value = options.initialDevice || 'desktop';
    previewOrientation.value = options.initialOrientation || 'portrait';
    previewScale.value = options.initialScale || 1;
    Object.assign(previewData, options.initialData || {});
    previewUrl.value = options.initialUrl || '';
    previewNetworkCondition.value = options.initialNetworkCondition || 'online';
    lastUpdated.value = new Date();
  };

  // WebSocket methods for live updates
  const connectWebSocket = (url) => {
    if (wsConnection.value) {
      disconnectWebSocket();
    }

    try {
      // In a real implementation, this would connect to a WebSocket server
      // For now, we'll just simulate a connection
      wsConnected.value = true;
      logger.info(`Connected to WebSocket: ${url}`);
    } catch (error) {
      console.error('WebSocket connection error:', error);
      wsConnected.value = false;
    }
  };

  const disconnectWebSocket = () => {
    if (wsConnection.value) {
      // In a real implementation, this would close the WebSocket connection
      wsConnected.value = false;
      wsConnection.value = null;
      logger.info('Disconnected from WebSocket');
    }
  };

  // Create the context object
  const previewContext = {
    // State
    previewMode,
    previewDevice,
    previewOrientation,
    previewScale,
    previewData,
    previewLoading,
    previewError,
    previewUrl,
    previewNetworkCondition,
    lastUpdated,
    wsConnected,
    
    // Computed
    isPreviewMode,
    isEditMode,
    isSplitMode,
    deviceDimensions,
    
    // Methods
    setPreviewMode,
    setPreviewDevice,
    setPreviewOrientation,
    setPreviewScale,
    setPreviewData,
    setPreviewUrl,
    setNetworkCondition,
    refreshPreview,
    resetPreview,
    connectWebSocket,
    disconnectWebSocket
  };

  // Provide the context to child components
  provide(PreviewContextSymbol, previewContext);

  return previewContext;
}

/**
 * Injects the preview context from a parent component
 * 
 * @returns {Object} The preview context object
 * @throws {Error} If used outside of a component with a provided preview context
 */
export function usePreviewContext() {
  const context = inject(PreviewContextSymbol);
  
  if (!context) {
    throw new Error('usePreviewContext must be used within a component that has called providePreviewContext');
  }
  
  return context;
}
