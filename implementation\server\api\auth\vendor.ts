/**
 * Vendor Management API
 *
 * This module provides API endpoints for vendor management.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase, supabaseAdmin, executeSupabaseQuery } from '../../shared/utils/supabase-client';
import { validateRequest } from '../middleware/validation';
import { authenticateRequest, authorizeRequest } from '../middleware/auth';
import {
  errorMiddleware,
  notFoundError,
  badRequestError,
  unauthorizedError,
} from '../middleware/error';
import { Logger } from '../../services/integration/logger';

// Create logger
const logger = new Logger();

/**
 * Vendor registration handler
 *
 * @param req Request
 * @param res Response
 */
async function registerVendor(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Validate request
    validateRequest('registerVendor')(req, res, async () => {
      const { name, email, password, profile, subscription_tier } = req.body;

      // Register user with Supabase Auth
      const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
        email,
        password,
        email_confirm: true,
        user_metadata: {
          vendor: true,
          ...profile,
        },
      });

      if (authError) {
        logger.error('Error registering vendor user', { error: authError });
        res.status(400).json({
          success: false,
          error: {
            code: 'REGISTRATION_ERROR',
            message: authError.message,
          },
        });
        return;
      }

      // Create user profile in users table
      const { data: userData, error: userError } = await supabaseAdmin
        .from('users')
        .insert({
          id: authData.user.id,
          name,
          role: 'vendor',
          metadata: {
            vendor: true,
            ...profile,
          },
        })
        .select()
        .single();

      if (userError) {
        logger.error('Error creating vendor user profile', { error: userError });

        // Clean up auth user if profile creation fails
        await supabaseAdmin.auth.admin.deleteUser(authData.user.id);

        res.status(500).json({
          success: false,
          error: {
            code: 'PROFILE_CREATION_ERROR',
            message: 'Failed to create vendor user profile',
          },
        });
        return;
      }

      // Create vendor profile
      const { data: vendorData, error: vendorError } = await supabaseAdmin
        .from('vendors')
        .insert({
          user_id: authData.user.id,
          name,
          profile,
          status: 'pending',
        })
        .select()
        .single();

      if (vendorError) {
        logger.error('Error creating vendor profile', { error: vendorError });

        // Clean up auth user and user profile if vendor profile creation fails
        await supabaseAdmin.auth.admin.deleteUser(authData.user.id);

        res.status(500).json({
          success: false,
          error: {
            code: 'VENDOR_CREATION_ERROR',
            message: 'Failed to create vendor profile',
          },
        });
        return;
      }

      // Create subscription if tier is provided
      if (subscription_tier) {
        const { error: subscriptionError } = await supabaseAdmin.from('subscriptions').insert({
          vendor_id: vendorData.id,
          plan: subscription_tier,
          status: 'pending',
        });

        if (subscriptionError) {
          logger.error('Error creating vendor subscription', { error: subscriptionError });
          // We don't need to roll back everything for subscription error
        }
      }

      // Return success response
      res.status(201).json({
        success: true,
        data: {
          id: vendorData.id,
          user_id: userData.id,
          name: vendorData.name,
          email: authData.user.email,
          profile: vendorData.profile,
          status: vendorData.status,
        },
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Approve vendor handler
 *
 * @param req Request
 * @param res Response
 */
async function approveVendor(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request and authorize admin
    authenticateRequest(req, res, () => {
      authorizeRequest(['admin'])(req, res, async () => {
        // Validate request
        validateRequest('approveVendor')(req, res, async () => {
          const { id } = req.query;
          const { approved, message } = req.body;

          // Update vendor status
          const { data: vendorData, error: vendorError } = await supabaseAdmin
            .from('vendors')
            .update({
              status: approved ? 'active' : 'rejected',
              profile: {
                ...vendorData?.profile,
                approval_message: message,
              },
            })
            .eq('id', id)
            .select()
            .single();

          if (vendorError) {
            logger.error('Error updating vendor status', { error: vendorError });
            res.status(500).json({
              success: false,
              error: {
                code: 'UPDATE_ERROR',
                message: 'Failed to update vendor status',
              },
            });
            return;
          }

          // Update subscription status if vendor is approved
          if (approved) {
            const { error: subscriptionError } = await supabaseAdmin
              .from('subscriptions')
              .update({
                status: 'active',
              })
              .eq('vendor_id', id)
              .eq('status', 'pending');

            if (subscriptionError) {
              logger.error('Error updating subscription status', { error: subscriptionError });
              // We don't need to roll back everything for subscription error
            }
          }

          // Return success response
          res.status(200).json({
            success: true,
            data: vendorData,
          });
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Get vendor profile handler
 *
 * @param req Request
 * @param res Response
 */
async function getVendorProfile(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('getVendorProfile')(req, res, async () => {
        // Get vendor ID from params or authenticated user
        const vendorId = req.query.id;

        if (!vendorId) {
          // Try to get vendor by user ID
          const { data: vendorData, error: vendorError } = await supabase
            .from('vendors')
            .select('*')
            .eq('user_id', req.user?.id)
            .single();

          if (vendorError) {
            logger.error('Error getting vendor profile by user ID', { error: vendorError });
            res.status(404).json({
              success: false,
              error: {
                code: 'VENDOR_NOT_FOUND',
                message: 'Vendor not found',
              },
            });
            return;
          }

          // Return success response
          res.status(200).json({
            success: true,
            data: vendorData,
          });
          return;
        }

        // Get vendor by ID
        const { data: vendorData, error: vendorError } = await supabase
          .from('vendors')
          .select('*')
          .eq('id', vendorId)
          .single();

        if (vendorError) {
          logger.error('Error getting vendor profile by ID', { error: vendorError });
          res.status(404).json({
            success: false,
            error: {
              code: 'VENDOR_NOT_FOUND',
              message: 'Vendor not found',
            },
          });
          return;
        }

        // Return success response
        res.status(200).json({
          success: true,
          data: vendorData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Update vendor profile handler
 *
 * @param req Request
 * @param res Response
 */
async function updateVendorProfile(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('updateVendorProfile')(req, res, async () => {
        const { name, profile } = req.body;

        // Get vendor ID from params or authenticated user
        const vendorId = req.query.id;

        if (!vendorId) {
          // Try to get vendor by user ID
          const { data: vendorData, error: vendorError } = await supabase
            .from('vendors')
            .select('id')
            .eq('user_id', req.user?.id)
            .single();

          if (vendorError) {
            logger.error('Error getting vendor ID by user ID', { error: vendorError });
            res.status(404).json({
              success: false,
              error: {
                code: 'VENDOR_NOT_FOUND',
                message: 'Vendor not found',
              },
            });
            return;
          }

          // Update vendor profile
          const { data: updatedVendorData, error: updateError } = await supabase
            .from('vendors')
            .update({
              ...(name && { name }),
              ...(profile && { profile: { ...vendorData.profile, ...profile } }),
            })
            .eq('id', vendorData.id)
            .select()
            .single();

          if (updateError) {
            logger.error('Error updating vendor profile', { error: updateError });
            res.status(500).json({
              success: false,
              error: {
                code: 'UPDATE_ERROR',
                message: 'Failed to update vendor profile',
              },
            });
            return;
          }

          // Return success response
          res.status(200).json({
            success: true,
            data: updatedVendorData,
          });
          return;
        }

        // Check if user is authorized to update this vendor
        const { data: vendorData, error: vendorError } = await supabase
          .from('vendors')
          .select('user_id')
          .eq('id', vendorId)
          .single();

        if (vendorError) {
          logger.error('Error getting vendor for authorization check', { error: vendorError });
          res.status(404).json({
            success: false,
            error: {
              code: 'VENDOR_NOT_FOUND',
              message: 'Vendor not found',
            },
          });
          return;
        }

        if (req.user?.id !== vendorData.user_id && req.user?.role !== 'admin') {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to update this vendor profile',
            },
          });
          return;
        }

        // Update vendor profile
        const { data: updatedVendorData, error: updateError } = await supabase
          .from('vendors')
          .update({
            ...(name && { name }),
            ...(profile && { profile: { ...vendorData.profile, ...profile } }),
          })
          .eq('id', vendorId)
          .select()
          .single();

        if (updateError) {
          logger.error('Error updating vendor profile', { error: updateError });
          res.status(500).json({
            success: false,
            error: {
              code: 'UPDATE_ERROR',
              message: 'Failed to update vendor profile',
            },
          });
          return;
        }

        // Return success response
        res.status(200).json({
          success: true,
          data: updatedVendorData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Handler for vendor API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'POST':
      if (req.query.action === 'approve') {
        await approveVendor(req, res);
      } else {
        await registerVendor(req, res);
      }
      break;
    case 'GET':
      await getVendorProfile(req, res);
      break;
    case 'PUT':
      await updateVendorProfile(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
