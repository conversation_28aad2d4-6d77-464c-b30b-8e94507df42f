# Guided Setup Wizard Architecture Plan

This document outlines the architectural decisions and planning for the Guided Setup Wizard component.

## State Management Strategy

After evaluating the requirements, we've decided to use a hybrid approach for state management:

### Component State (Current Implementation)
- Each step component maintains its own local state for form fields and validation
- The WizardContainer component manages the overall wizard state (current step, progress, etc.)
- The GuidedSetupWizard component handles API interactions and persistence

### Vuex Integration (To Be Implemented)
- Create a dedicated Vuex module for the wizard state
- Store the following in Vuex:
  - Step data for all steps
  - Validation state
  - Progress tracking
  - API interaction state (loading, errors)
- Benefits:
  - Centralized state management
  - Better debugging capabilities
  - Easier cross-step data dependencies
  - Simplified persistence

### Implementation Plan
1. Create a new Vuex module: `guidedSetupWizard.js`
2. Define state, getters, mutations, and actions
3. Refactor components to use Vuex store
4. Maintain backward compatibility for non-Vuex usage

## API Integration Points

The wizard will integrate with the following API endpoints:

### Data Persistence
- `POST /items/vendor_onboarding`: Create new onboarding record
- `PATCH /items/vendor_onboarding/:id`: Update onboarding progress
- `GET /items/vendor_onboarding/:id`: Retrieve onboarding progress

### User Management
- `POST /users`: Create new user accounts
- `PATCH /users/:id`: Update user information
- `GET /users`: Retrieve users for the vendor

### File Upload
- `POST /files`: Upload company logo, product images, etc.
- `GET /files/:id`: Retrieve file information

### Vendor Information
- `PATCH /items/vendors/:id`: Update vendor information
- `GET /items/vendors/:id`: Retrieve vendor information

### Analytics
- `POST /items/wizard_analytics`: Track wizard usage analytics

## Animation and Transition Specifications

### Step Transitions
- Use Vue's built-in transition component with custom classes
- Fade out current step, fade in new step
- Add slide effect for direction (next/previous)
- Duration: 300ms
- Easing: ease-in-out

### Interactive Elements
- Button hover: Scale up slightly (1.05) with color change
- Form field focus: Subtle border glow effect
- Validation errors: Shake animation for invalid fields
- Success indicators: Fade in with slight bounce

### Progress Indicators
- Step completion: Fill animation for progress bar
- Step navigation: Pulse animation when clicking on step indicator

## Accessibility Guidelines

### Keyboard Navigation
- Tab navigation through all interactive elements
- Enter/Space to activate buttons
- Arrow keys for step navigation
- Escape to open exit confirmation dialog

### ARIA Attributes
- `aria-label` for all interactive elements
- `aria-required` for required form fields
- `aria-invalid` for validation errors
- `aria-live` regions for dynamic content
- `aria-current` for current step indicator

### Focus Management
- Trap focus within the wizard when active
- Return focus to trigger element when wizard closes
- Focus first field of each step when navigating

### Screen Reader Support
- Meaningful alt text for all images
- Descriptive button labels
- Error messages linked to form fields
- Progress announcements for screen readers

### Color and Contrast
- Minimum contrast ratio of 4.5:1 for all text
- Visual indicators beyond color alone
- High contrast mode support

## User Flow Alternatives

### Save & Exit
- User can save progress at any point
- Confirmation dialog before exiting
- Email with resume link sent to user
- Local storage backup of progress

### Skip Steps
- Optional steps can be skipped
- Clear indication of required vs. optional steps
- Ability to return to skipped steps later
- Validation of dependencies for skipped steps

### Partial Completion
- Allow completion with minimum required information
- Clear indication of incomplete sections
- Easy way to return and complete remaining sections
- Reminder notifications for incomplete sections

## Error Handling and Recovery

### Form Validation
- Immediate field-level validation
- Summary of errors at step level
- Clear error messages with resolution guidance
- Visual indicators for invalid fields

### API Errors
- Retry mechanism for failed API calls
- Offline mode with local storage backup
- Clear error messages for different failure types
- Recovery options for each error scenario

### Session Recovery
- Automatic saving of progress (every 30 seconds)
- Browser storage backup
- Session expiration warnings
- Recovery from browser crash/refresh

## Validation Feedback Loops

### Immediate Feedback
- Field-level validation as user types
- Visual indicators (colors, icons) for validation state
- Clear error messages below invalid fields

### Step Validation
- Validate all fields when attempting to proceed
- Show summary of errors at top of step
- Scroll to first error automatically
- Disable next button until all required fields are valid

### Cross-Step Validation
- Validate dependencies between steps
- Show warnings for potential conflicts
- Suggest corrections based on previous inputs
- Allow user to navigate back to fix issues
