/**
 * Authentication Middleware
 *
 * This module provides middleware for authenticating requests.
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';

// Create Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

// Add user property to Request interface
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

/**
 * Authenticate a request using JWT
 *
 * @param req Request
 * @param res Response
 * @param next Next function
 */
export const authenticateRequest = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    // Get the authorization header
    const authHeader = req.headers.authorization;

    // Check if the authorization header exists
    if (!authHeader) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'No authorization header provided',
        },
      });
      return;
    }

    // Check if the authorization header is in the correct format
    if (!authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Invalid authorization header format',
        },
      });
      return;
    }

    // Extract the token
    const token = authHeader.substring(7);

    // Verify the token with Supabase
    const { data, error } = await supabase.auth.getUser(token);

    // Check if there was an error
    if (error) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Invalid token',
        },
      });
      return;
    }

    // Add the user to the request
    req.user = data.user;

    // Continue to the next middleware
    next();
  } catch (error) {
    // Handle any errors
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An error occurred while authenticating the request',
      },
    });
  }
};

/**
 * Authorize a request based on user roles
 *
 * @param roles Roles that are allowed to access the resource
 * @returns Middleware function
 */
export const authorizeRequest = (
  roles: string[],
): ((req: Request, res: Response, next: NextFunction) => void) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Check if the user exists
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'User not authenticated',
          },
        });
        return;
      }

      // Check if the user has the required role
      const userRole = req.user.app_metadata?.role || 'user';

      if (!roles.includes(userRole)) {
        res.status(403).json({
          success: false,
          error: {
            code: 'FORBIDDEN',
            message: 'User does not have the required role',
          },
        });
        return;
      }

      // Continue to the next middleware
      next();
    } catch (error) {
      // Handle any errors
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An error occurred while authorizing the request',
        },
      });
    }
  };
};
