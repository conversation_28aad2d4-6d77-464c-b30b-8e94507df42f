import React, { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Button,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  IconButton,
  Tooltip,
  LinearProgress,
} from '@mui/material'
import {
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Timeline as TimelineIcon,
  Speed as SpeedIcon,
  Devices as DevicesIcon,
  Image as ImageIcon,
  Code as CodeIcon,
  ViewQuilt as ViewQuiltIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material'
import ValidationReport from './ValidationReport'
import PhaseProgressIndicator from '../phases/PhaseProgressIndicator'
import { apiClient } from '../../services/apiClient'

interface ValidationDashboardProps {
  sceneId: string
}

/**
 * Enhanced Validation Dashboard Component with Phase Management
 * 
 * Comprehensive dashboard for scene validation with phase tracking
 */
const ValidationDashboardWithPhases: React.FC<ValidationDashboardProps> = ({ sceneId }) => {
  const [activeTab, setActiveTab] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [validationData, setValidationData] = useState<any>(null)
  const [assetsData, setAssetsData] = useState<any>(null)
  const [blueprintsData, setBlueprintsData] = useState<any>(null)
  const [flowData, setFlowData] = useState<any>(null)
  const [performanceData, setPerformanceData] = useState<any>(null)
  const [compatibilityData, setCompatibilityData] = useState<any>(null)
  const [overallScore, setOverallScore] = useState<number>(0)

  // Load initial data
  useEffect(() => {
    if (sceneId) {
      loadValidationData()
    }
  }, [sceneId])

  // Calculate overall score
  useEffect(() => {
    let score = 0
    let count = 0

    if (validationData) {
      score += validationData.valid ? 100 : (50 - validationData.errors.length * 10)
      count += 1
    }

    if (assetsData) {
      const assetScore = Math.max(0, 100 - (assetsData.summary.assetsWithErrors * 20) - (assetsData.summary.assetsWithWarnings * 5))
      score += assetScore
      count += 1
    }

    if (blueprintsData) {
      const blueprintScore = Math.max(0, 100 - (blueprintsData.summary.instancesWithErrors * 20) - (blueprintsData.summary.instancesWithWarnings * 5))
      score += blueprintScore
      count += 1
    }

    if (performanceData) {
      const performanceScore = performanceData.impact === 'low' ? 100 : (performanceData.impact === 'medium' ? 70 : 40)
      score += performanceScore
      count += 1
    }

    if (compatibilityData) {
      score += compatibilityData.overallScore || (compatibilityData.compatible ? 100 : 50)
      count += 1
    }

    setOverallScore(count > 0 ? Math.round(score / count) : 0)
  }, [validationData, assetsData, blueprintsData, performanceData, compatibilityData])

  // Load all validation data
  const loadValidationData = async () => {
    setLoading(true)
    setError(null)

    try {
      // Load basic validation data
      await validateScene()
      
      // Load assets data
      await validateAssets()
      
      // Load blueprints data
      await validateBlueprints()
      
      // Load performance data
      await analyzePerformance()
      
      // Load compatibility data
      await checkCompatibility('quest2')
    } catch (error: any) {
      setError(error.message || 'An error occurred while loading validation data')
    } finally {
      setLoading(false)
    }
  }

  // Validate scene
  const validateScene = async () => {
    try {
      const response = await apiClient.get(`/api/scenes/validate/${sceneId}`)
      
      if (response.data.success) {
        setValidationData(response.data.data)
        
        // Record validation result
        recordValidationResult('scene_validation', {
          valid: response.data.data.valid,
          details: response.data.data,
        })
      } else {
        setError(response.data.error?.message || 'Failed to validate scene')
      }
    } catch (error: any) {
      setError(error.message || 'An error occurred while validating the scene')
    }
  }

  // Validate assets
  const validateAssets = async () => {
    try {
      const response = await apiClient.get(`/api/scenes/validate/${sceneId}/assets`)
      
      if (response.data.success) {
        setAssetsData(response.data.data)
        
        // Record validation result
        recordValidationResult('asset_validation', {
          valid: response.data.data.summary.assetsWithErrors === 0,
          details: response.data.data,
        })
      } else {
        console.error('Failed to validate assets', response.data.error)
      }
    } catch (error: any) {
      console.error('Error validating assets', error)
    }
  }

  // Validate blueprints
  const validateBlueprints = async () => {
    try {
      const response = await apiClient.get(`/api/scenes/validate/${sceneId}/blueprints`)
      
      if (response.data.success) {
        setBlueprintsData(response.data.data)
        
        // Record validation result
        recordValidationResult('blueprint_validation', {
          valid: response.data.data.summary.instancesWithErrors === 0,
          details: response.data.data,
        })
      } else {
        console.error('Failed to validate blueprints', response.data.error)
      }
    } catch (error: any) {
      console.error('Error validating blueprints', error)
    }
  }

  // Analyze performance
  const analyzePerformance = async () => {
    try {
      const response = await apiClient.get(`/api/scenes/validate/${sceneId}/performance`)
      
      if (response.data.success) {
        setPerformanceData(response.data.data)
        
        // Record validation result
        recordValidationResult('performance_analysis', {
          valid: response.data.data.impact !== 'high',
          details: response.data.data,
        })
      } else {
        console.error('Failed to analyze performance', response.data.error)
      }
    } catch (error: any) {
      console.error('Error analyzing performance', error)
    }
  }

  // Check compatibility
  const checkCompatibility = async (environment: string) => {
    try {
      const response = await apiClient.get(`/api/scenes/validate/${sceneId}/compatibility`, {
        params: { target_environment: environment },
      })
      
      if (response.data.success) {
        setCompatibilityData(response.data.data)
        
        // Record validation result
        recordValidationResult('compatibility_check', {
          valid: response.data.data.compatible,
          details: response.data.data,
        })
      } else {
        console.error('Failed to check compatibility', response.data.error)
      }
    } catch (error: any) {
      console.error('Error checking compatibility', error)
    }
  }

  // Record validation result
  const recordValidationResult = async (validationType: string, result: { valid: boolean; details?: any }) => {
    try {
      await apiClient.post(`/api/scenes/${sceneId}/phases/validation`, {
        validation_type: validationType,
        result,
      })
    } catch (error: any) {
      console.error('Error recording validation result', error)
    }
  }

  // Export validation report
  const exportReport = () => {
    const report = {
      sceneId,
      timestamp: new Date().toISOString(),
      overallScore,
      validation: validationData,
      assets: assetsData,
      blueprints: blueprintsData,
      performance: performanceData,
      compatibility: compatibilityData,
    }

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `validation-report-${sceneId}-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue)
  }

  // Get score color
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'success.main'
    if (score >= 70) return 'info.main'
    if (score >= 50) return 'warning.main'
    return 'error.main'
  }

  // Handle validation result from phase progress indicator
  const handleValidationResult = (validationType: string, result: { valid: boolean; details?: any }) => {
    // Update local state based on validation type
    if (validationType === 'scene_structure' || validationType === 'scene_validation') {
      setValidationData(prev => ({
        ...prev,
        valid: result.valid,
        details: result.details,
      }))
    } else if (validationType === 'asset_validation') {
      setAssetsData(result.details)
    } else if (validationType === 'blueprint_validation') {
      setBlueprintsData(result.details)
    } else if (validationType === 'performance_analysis') {
      setPerformanceData(result.details)
    } else if (validationType === 'compatibility_check') {
      setCompatibilityData(result.details)
    }
  }

  // Handle phase change
  const handlePhaseChange = (phase: string) => {
    // Reload validation data when phase changes
    loadValidationData()
  }

  // Render summary card
  const renderSummaryCard = () => {
    return (
      <Card variant="outlined" sx={{ mb: 3 }}>
        <CardHeader 
          title="Validation Summary" 
          action={
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Tooltip title="Export Report">
                <IconButton onClick={exportReport} disabled={loading}>
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Refresh All">
                <IconButton onClick={loadValidationData} disabled={loading}>
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          }
        />
        <CardContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Typography variant="h6" gutterBottom>Overall Score</Typography>
                <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                  <CircularProgress
                    variant="determinate"
                    value={overallScore}
                    size={100}
                    thickness={5}
                    sx={{ color: getScoreColor(overallScore) }}
                  />
                  <Box
                    sx={{
                      top: 0,
                      left: 0,
                      bottom: 0,
                      right: 0,
                      position: 'absolute',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="h4" component="div" color={getScoreColor(overallScore)}>
                      {overallScore}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={8}>
              <Grid container spacing={1}>
                <Grid item xs={6} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 1 }}>
                    <ViewQuiltIcon color={validationData?.valid ? 'success' : 'error'} />
                    <Typography variant="body2">Scene Structure</Typography>
                    <Chip 
                      label={validationData?.valid ? 'Valid' : 'Invalid'} 
                      color={validationData?.valid ? 'success' : 'error'} 
                      size="small" 
                    />
                  </Box>
                </Grid>
                <Grid item xs={6} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 1 }}>
                    <ImageIcon color={assetsData?.summary?.assetsWithErrors === 0 ? 'success' : 'error'} />
                    <Typography variant="body2">Assets</Typography>
                    <Chip 
                      label={assetsData?.summary?.assetsWithErrors === 0 ? 'Valid' : 'Issues'} 
                      color={assetsData?.summary?.assetsWithErrors === 0 ? 'success' : 'error'} 
                      size="small" 
                    />
                  </Box>
                </Grid>
                <Grid item xs={6} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 1 }}>
                    <CodeIcon color={blueprintsData?.summary?.instancesWithErrors === 0 ? 'success' : 'error'} />
                    <Typography variant="body2">Blueprints</Typography>
                    <Chip 
                      label={blueprintsData?.summary?.instancesWithErrors === 0 ? 'Valid' : 'Issues'} 
                      color={blueprintsData?.summary?.instancesWithErrors === 0 ? 'success' : 'error'} 
                      size="small" 
                    />
                  </Box>
                </Grid>
                <Grid item xs={6} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 1 }}>
                    <SpeedIcon color={performanceData?.impact === 'low' ? 'success' : (performanceData?.impact === 'medium' ? 'warning' : 'error')} />
                    <Typography variant="body2">Performance</Typography>
                    <Chip 
                      label={performanceData?.impact || 'Unknown'} 
                      color={performanceData?.impact === 'low' ? 'success' : (performanceData?.impact === 'medium' ? 'warning' : 'error')} 
                      size="small" 
                    />
                  </Box>
                </Grid>
                <Grid item xs={6} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 1 }}>
                    <DevicesIcon color={compatibilityData?.compatible ? 'success' : 'error'} />
                    <Typography variant="body2">Compatibility</Typography>
                    <Chip 
                      label={compatibilityData?.compatible ? 'Compatible' : 'Issues'} 
                      color={compatibilityData?.compatible ? 'success' : 'error'} 
                      size="small" 
                    />
                  </Box>
                </Grid>
                <Grid item xs={6} sm={4}>
                  <Box sx={{ textAlign: 'center', p: 1 }}>
                    <TimelineIcon color="info" />
                    <Typography variant="body2">Flow</Typography>
                    <Chip 
                      label={flowData ? 'Analyzed' : 'Not Analyzed'} 
                      color={flowData ? 'info' : 'default'} 
                      size="small" 
                    />
                  </Box>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    )
  }

  return (
    <Box>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      
      {loading && <LinearProgress sx={{ mb: 2 }} />}
      
      {/* Phase Progress Indicator */}
      <PhaseProgressIndicator 
        sceneId={sceneId}
        onPhaseChange={handlePhaseChange}
        onValidationComplete={handleValidationResult}
      />
      
      {renderSummaryCard()}
      
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
          <Tab label="Scene" icon={<ViewQuiltIcon />} iconPosition="start" />
          <Tab label="Assets" icon={<ImageIcon />} iconPosition="start" />
          <Tab label="Blueprints" icon={<CodeIcon />} iconPosition="start" />
          <Tab label="Performance" icon={<SpeedIcon />} iconPosition="start" />
          <Tab label="Compatibility" icon={<DevicesIcon />} iconPosition="start" />
          <Tab label="Flow" icon={<TimelineIcon />} iconPosition="start" />
        </Tabs>
        
        <Box sx={{ p: 3 }}>
          {activeTab === 0 && (
            <ValidationReport
              sceneId={sceneId}
              validationResult={validationData}
              loading={loading}
              error={error || undefined}
              onValidate={validateScene}
            />
          )}
          
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" gutterBottom>Asset Validation</Typography>
              {/* Asset validation component would go here */}
            </Box>
          )}
          
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" gutterBottom>Blueprint Validation</Typography>
              {/* Blueprint validation component would go here */}
            </Box>
          )}
          
          {activeTab === 3 && (
            <ValidationReport
              sceneId={sceneId}
              performanceAnalysis={performanceData}
              loading={loading}
              error={error || undefined}
              onAnalyzePerformance={analyzePerformance}
            />
          )}
          
          {activeTab === 4 && (
            <ValidationReport
              sceneId={sceneId}
              compatibilityResult={compatibilityData}
              loading={loading}
              error={error || undefined}
              onCheckCompatibility={checkCompatibility}
            />
          )}
          
          {activeTab === 5 && (
            <Box>
              <Typography variant="h6" gutterBottom>Flow Analysis</Typography>
              {/* Flow analysis component would go here */}
            </Box>
          )}
        </Box>
      </Paper>
    </Box>
  )
}

export default ValidationDashboardWithPhases
