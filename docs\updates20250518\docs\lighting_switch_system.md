#### **`lighting_switch_system.md`**

# Lighting & Switch System

## Lights
- **Types**: Spot, Point, Area, Emissive.  
- **Config**: Color, Intensity, Range, Profile.

## Switch Types
- **Wall-mounted**  
- **Self-contained** (lamp touch)  
- **External** (Bluetooth, console)

## JSO<PERSON> Sample
```json
{
  "lightId": "light_01",
  "type": "spot",
  "color": "#FFEED1",
  "intensity": 2.5,
  "controlledBy": ["switch_0021"]
}
````

## Linking Flow

1. Add light to object.
2. Click “+ Switch” UI.
3. Select switch(s) by ID or location.
4. Confirm binding and preview.

