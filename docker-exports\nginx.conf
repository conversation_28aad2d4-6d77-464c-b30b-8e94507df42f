events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;

    # Health check endpoint
    server {
        listen 80;
        server_name _;
        
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }

    # Main server block for mvs.kanousai.com
    server {
        listen 80;
        server_name mvs.kanousai.com www.mvs.kanousai.com;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Referrer-Policy strict-origin-when-cross-origin;

        # API Gateway - Main API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Authentication Service
        location /auth/ {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://auth_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Asset Service
        location /assets/ {
            proxy_pass http://asset_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            client_max_body_size 100M;
        }



        # Analytics Service
        location /analytics/ {
            proxy_pass http://analytics_service;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            access_log off;
            return 200 "MVS-VR Infrastructure - Port 80 Accessible
Nginx: Running
Supabase: Connected
Docker Network: Active
Services: Configured for remote Supabase
";
            add_header Content-Type text/plain;
        }

        # Default location - serve static content or redirect to API
        location / {
            return 200 "MVS-VR System Status

Infrastructure: ✓ Running
Port 80: ✓ Accessible
Nginx: ✓ Active
Docker Network: ✓ Connected
Supabase: ✓ Remote connection configured

Services Status:
- Auth Service: Configured for Supabase auth
- API Gateway: Configured for service routing
- Asset Service: Configured for Supabase storage
- Analytics Service: Configured for data collection

Next Steps:
1. Rebuild Docker images with proper dependencies
2. Configure DNS: mvs.kanousai.com -> **************
3. Test individual service endpoints

Health Check: /health
Supabase URL: https://hiyqiqbgiueyyvqoqhht.supabase.co
";
            add_header Content-Type text/plain;
        }
    }

    # API subdomain (alternative access)
    server {
        listen 80;
        server_name api.mvs.kanousai.com;

        location / {
            proxy_pass http://api_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
