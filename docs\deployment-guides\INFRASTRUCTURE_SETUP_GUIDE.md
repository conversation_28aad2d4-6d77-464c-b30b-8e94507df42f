# MVS-VR Infrastructure Setup Guide

This guide provides comprehensive instructions for setting up the infrastructure required for the MVS-VR platform. It covers server infrastructure, database setup, storage infrastructure, CDN setup, and monitoring infrastructure.

## Table of Contents

1. [Server Infrastructure](#server-infrastructure)
2. [Database Setup](#database-setup)
3. [Storage Infrastructure](#storage-infrastructure)
4. [CDN Setup](#cdn-setup)
5. [Monitoring Infrastructure](#monitoring-infrastructure)
6. [Security Infrastructure](#security-infrastructure)
7. [Scaling Considerations](#scaling-considerations)
8. [Disaster Recovery](#disaster-recovery)

## Server Infrastructure

The MVS-VR platform requires a robust server infrastructure to handle API requests, process assets, and serve web applications.

### Kubernetes Cluster Setup

We recommend using Kubernetes for orchestrating the MVS-VR platform. Here's how to set up a Kubernetes cluster on different cloud providers:

#### AWS EKS

1. **Install the AWS CLI and eksctl**

```bash
# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Install eksctl
curl --silent --location "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_$(uname -s)_amd64.tar.gz" | tar xz -C /tmp
sudo mv /tmp/eksctl /usr/local/bin
```

2. **Configure AWS CLI**

```bash
aws configure
```

3. **Create an EKS Cluster**

```bash
eksctl create cluster \
  --name mvs-vr-cluster \
  --region us-west-2 \
  --version 1.24 \
  --nodegroup-name standard-workers \
  --node-type m5.large \
  --nodes 3 \
  --nodes-min 1 \
  --nodes-max 5 \
  --managed
```

4. **Configure kubectl**

```bash
aws eks update-kubeconfig --name mvs-vr-cluster --region us-west-2
```

#### Azure AKS

1. **Install the Azure CLI**

```bash
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

2. **Login to Azure**

```bash
az login
```

3. **Create a Resource Group**

```bash
az group create --name mvs-vr-rg --location eastus
```

4. **Create an AKS Cluster**

```bash
az aks create \
  --resource-group mvs-vr-rg \
  --name mvs-vr-cluster \
  --node-count 3 \
  --enable-addons monitoring \
  --generate-ssh-keys
```

5. **Configure kubectl**

```bash
az aks get-credentials --resource-group mvs-vr-rg --name mvs-vr-cluster
```

#### GCP GKE

1. **Install the Google Cloud SDK**

```bash
curl https://sdk.cloud.google.com | bash
exec -l $SHELL
gcloud init
```

2. **Create a GKE Cluster**

```bash
gcloud container clusters create mvs-vr-cluster \
  --zone us-central1-a \
  --num-nodes 3 \
  --machine-type e2-standard-4
```

3. **Configure kubectl**

```bash
gcloud container clusters get-credentials mvs-vr-cluster --zone us-central1-a
```

### Kubernetes Resources

The MVS-VR platform requires the following Kubernetes resources:

1. **Namespaces**

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: mvs-vr
```

2. **Deployments**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mvs-vr-server
  namespace: mvs-vr
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mvs-vr-server
  template:
    metadata:
      labels:
        app: mvs-vr-server
    spec:
      containers:
      - name: mvs-vr-server
        image: your-registry/mvs-vr-server:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: production
        - name: PORT
          value: "3000"
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: mvs-vr-secrets
              key: supabase-url
        - name: SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: mvs-vr-secrets
              key: supabase-anon-key
        - name: SUPABASE_SERVICE_ROLE_KEY
          valueFrom:
            secretKeyRef:
              name: mvs-vr-secrets
              key: supabase-service-role-key
        resources:
          limits:
            cpu: "1"
            memory: "2Gi"
          requests:
            cpu: "500m"
            memory: "1Gi"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

3. **Services**

```yaml
apiVersion: v1
kind: Service
metadata:
  name: mvs-vr-server
  namespace: mvs-vr
spec:
  selector:
    app: mvs-vr-server
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP
```

4. **Ingress**

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mvs-vr-ingress
  namespace: mvs-vr
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  rules:
  - host: api.mvs-vr.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: mvs-vr-server
            port:
              number: 80
  tls:
  - hosts:
    - api.mvs-vr.com
    secretName: mvs-vr-tls
```

5. **Secrets**

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: mvs-vr-secrets
  namespace: mvs-vr
type: Opaque
data:
  supabase-url: <base64-encoded-url>
  supabase-anon-key: <base64-encoded-key>
  supabase-service-role-key: <base64-encoded-key>
  jwt-secret: <base64-encoded-secret>
```

### Helm Chart

For easier deployment and management, we recommend using Helm. Here's a basic Helm chart structure for the MVS-VR platform:

```
mvs-vr/
├── Chart.yaml
├── values.yaml
├── templates/
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── ingress.yaml
│   └── secrets.yaml
└── charts/
```

To deploy the Helm chart:

```bash
helm upgrade --install mvs-vr ./mvs-vr --namespace mvs-vr --create-namespace
```

## Database Setup

The MVS-VR platform uses Supabase, which is built on PostgreSQL, for its database needs.

### Supabase Setup

1. **Create a Supabase Project**

- Go to [https://app.supabase.io](https://app.supabase.io)
- Sign up or log in
- Click "New Project"
- Enter a name for your project
- Choose a database password
- Select a region close to your users
- Click "Create new project"

2. **Configure Database Settings**

- Go to the "Settings" section
- Click "Database"
- Configure the following settings:
  - **Pooler Mode**: Transaction
  - **Connection Pooling**: Enabled
  - **Connection Timeout**: 30 seconds
  - **Statement Timeout**: 60 seconds
  - **Idle in Transaction Timeout**: 60 seconds

3. **Run Database Migrations**

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your Supabase project
supabase link --project-ref your-project-ref

# Run migrations
supabase db push
```

### Database Scaling

For high-traffic applications, consider the following scaling options:

1. **Vertical Scaling**
   - Upgrade to a higher tier in Supabase
   - Increase CPU and memory resources

2. **Read Replicas**
   - Set up read replicas for read-heavy workloads
   - Configure your application to use read replicas for queries

3. **Connection Pooling**
   - Use connection pooling to manage database connections
   - Configure appropriate pool sizes based on your workload

4. **Query Optimization**
   - Optimize queries for performance
   - Use indexes for frequently queried columns
   - Implement caching for frequently accessed data

## Storage Infrastructure

The MVS-VR platform requires storage for assets, including 3D models, textures, and other files.

### Supabase Storage Setup

1. **Create Storage Buckets**

- Go to the "Storage" section in Supabase
- Click "Create new bucket"
- Create the following buckets:
  - `assets`: For general assets
  - `models`: For 3D models
  - `textures`: For textures
  - `thumbnails`: For thumbnails
  - `exports`: For exported files

2. **Configure Bucket Policies**

- Click on a bucket
- Go to the "Policies" tab
- Create the following policies:
  - Allow authenticated users to upload files
  - Allow authenticated users to download their own files
  - Allow administrators to manage all files

3. **Configure CORS**

- Go to the "Settings" section
- Click "API"
- Configure CORS to allow requests from your domains

### External Storage Options

For large-scale deployments, consider using external storage solutions:

1. **AWS S3**

```bash
# Create an S3 bucket
aws s3 mb s3://mvs-vr-assets --region us-west-2

# Configure bucket policy
aws s3api put-bucket-policy --bucket mvs-vr-assets --policy file://bucket-policy.json

# Configure CORS
aws s3api put-bucket-cors --bucket mvs-vr-assets --cors-configuration file://cors-config.json
```

2. **Azure Blob Storage**

```bash
# Create a storage account
az storage account create --name mvsvrassets --resource-group mvs-vr-rg --location eastus --sku Standard_LRS

# Create a container
az storage container create --name assets --account-name mvsvrassets --auth-mode login

# Configure CORS
az storage cors add --account-name mvsvrassets --services b --methods GET POST PUT DELETE OPTIONS --origins "https://your-domain.com" --allowed-headers "*" --exposed-headers "*" --max-age 3600
```

3. **Google Cloud Storage**

```bash
# Create a GCS bucket
gsutil mb -l us-central1 gs://mvs-vr-assets

# Configure bucket policy
gsutil iam ch allUsers:objectViewer gs://mvs-vr-assets

# Configure CORS
gsutil cors set cors-config.json gs://mvs-vr-assets
```

## CDN Setup

A Content Delivery Network (CDN) is essential for delivering assets quickly to users around the world.

### Cloudflare CDN Setup

1. **Sign Up for Cloudflare**

- Go to [https://www.cloudflare.com](https://www.cloudflare.com)
- Sign up or log in
- Add your domain
- Update your domain's nameservers to Cloudflare's nameservers

2. **Configure Cloudflare Settings**

- Go to the "Rules" section
- Create a cache rule for asset paths
- Configure the following settings:
  - **Cache Level**: Cache Everything
  - **Edge Cache TTL**: 1 month
  - **Browser Cache TTL**: 1 day

3. **Configure Origin Shield**

- Go to the "Caching" section
- Enable Argo Tiered Cache
- Configure origin shield settings

### AWS CloudFront Setup

1. **Create a CloudFront Distribution**

```bash
aws cloudfront create-distribution \
  --origin-domain-name mvs-vr-assets.s3.amazonaws.com \
  --default-root-object index.html \
  --enabled \
  --default-cache-behavior '{"TargetOriginId":"S3-mvs-vr-assets","ViewerProtocolPolicy":"redirect-to-https","AllowedMethods":{"Quantity":7,"Items":["GET","HEAD","OPTIONS","PUT","POST","PATCH","DELETE"]},"CachePolicyId":"658327ea-f89d-4fab-a63d-7e88639e58f6"}'
```

2. **Configure Cache Behavior**

- Go to the CloudFront console
- Select your distribution
- Click "Behaviors"
- Create a behavior for asset paths with appropriate caching settings

3. **Configure Origin Shield**

- Go to the "Origins" tab
- Edit your origin
- Enable Origin Shield
- Select a region close to your origin

### Azure CDN Setup

1. **Create an Azure CDN Profile**

```bash
az cdn profile create --name mvs-vr-cdn --resource-group mvs-vr-rg --sku Standard_Microsoft
```

2. **Create a CDN Endpoint**

```bash
az cdn endpoint create \
  --name mvs-vr-assets \
  --profile-name mvs-vr-cdn \
  --resource-group mvs-vr-rg \
  --origin-host-header mvsvrassets.blob.core.windows.net \
  --origin mvsvrassets.blob.core.windows.net \
  --enable-compression
```

3. **Configure Caching Rules**

- Go to the Azure portal
- Navigate to your CDN endpoint
- Click "Caching rules"
- Create rules for different asset types with appropriate caching settings

## Monitoring Infrastructure

Monitoring is essential for ensuring the health and performance of the MVS-VR platform.

### Prometheus and Grafana Setup

1. **Install Prometheus and Grafana using Helm**

```bash
# Add Prometheus Helm repository
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# Install Prometheus
helm install prometheus prometheus-community/prometheus \
  --namespace monitoring \
  --create-namespace

# Install Grafana
helm repo add grafana https://grafana.github.io/helm-charts
helm repo update
helm install grafana grafana/grafana \
  --namespace monitoring \
  --set persistence.enabled=true \
  --set persistence.size=10Gi
```

2. **Configure Prometheus Scraping**

Create a `prometheus-config.yaml` file:

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-server-conf
  namespace: monitoring
  labels:
    name: prometheus-server-conf
data:
  prometheus.yml: |-
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    scrape_configs:
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
        - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
          action: keep
          regex: default;kubernetes;https
      - job_name: 'mvs-vr-server'
        kubernetes_sd_configs:
        - role: endpoints
        relabel_configs:
        - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name]
          action: keep
          regex: mvs-vr;mvs-vr-server
        - source_labels: [__meta_kubernetes_pod_container_port_name]
          action: keep
          regex: metrics
```

Apply the configuration:

```bash
kubectl apply -f prometheus-config.yaml
```

3. **Set Up Grafana Dashboards**

- Get the Grafana admin password:

```bash
kubectl get secret --namespace monitoring grafana -o jsonpath="{.data.admin-password}" | base64 --decode
```

- Port forward Grafana:

```bash
kubectl port-forward --namespace monitoring svc/grafana 3000:80
```

- Open Grafana at http://localhost:3000
- Log in with admin and the password from the previous step
- Add Prometheus as a data source
- Import dashboards for Kubernetes, Node.js, and PostgreSQL

### Logging Setup with ELK Stack

1. **Install Elasticsearch, Logstash, and Kibana using Helm**

```bash
# Add Elastic Helm repository
helm repo add elastic https://helm.elastic.co
helm repo update

# Install Elasticsearch
helm install elasticsearch elastic/elasticsearch \
  --namespace logging \
  --create-namespace \
  --set replicas=3 \
  --set minimumMasterNodes=2

# Install Logstash
helm install logstash elastic/logstash \
  --namespace logging

# Install Kibana
helm install kibana elastic/kibana \
  --namespace logging \
  --set service.type=LoadBalancer
```

2. **Configure Filebeat for Log Collection**

```bash
# Install Filebeat
helm install filebeat elastic/filebeat \
  --namespace logging \
  --set filebeatConfig.filebeat.yml="
filebeat.inputs:
- type: container
  paths:
    - /var/log/containers/*.log
  processors:
    - add_kubernetes_metadata:
        host: \${NODE_NAME}
        matchers:
        - logs_path:
            logs_path: \"/var/log/containers/\"

output.elasticsearch:
  host: '\${ELASTICSEARCH_HOST:elasticsearch-master}:\${ELASTICSEARCH_PORT:9200}'
"
```

3. **Access Kibana**

- Get the Kibana URL:

```bash
kubectl get svc kibana-kibana -n logging
```

- Open Kibana in your browser
- Create an index pattern for Filebeat
- Explore your logs

## Security Infrastructure

Security is a critical aspect of the MVS-VR platform infrastructure.

### Network Security

1. **Network Policies**

Create a `network-policy.yaml` file:

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mvs-vr-network-policy
  namespace: mvs-vr
spec:
  podSelector:
    matchLabels:
      app: mvs-vr-server
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  egress:
  - to:
    - ipBlock:
        cidr: 0.0.0.0/0
        except:
        - ***********/16
        - 10.0.0.0/8
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 80
```

Apply the network policy:

```bash
kubectl apply -f network-policy.yaml
```

2. **Web Application Firewall (WAF)**

For AWS:

```bash
# Create a WAF Web ACL
aws wafv2 create-web-acl \
  --name mvs-vr-waf \
  --scope REGIONAL \
  --default-action Allow={} \
  --visibility-config SampledRequestsEnabled=true,CloudWatchMetricsEnabled=true,MetricName=mvs-vr-waf \
  --rules file://waf-rules.json \
  --region us-west-2
```

For Azure:

```bash
# Create an Application Gateway with WAF
az network application-gateway create \
  --name mvs-vr-appgw \
  --resource-group mvs-vr-rg \
  --location eastus \
  --sku WAF_v2 \
  --capacity 2 \
  --vnet-name mvs-vr-vnet \
  --subnet appgw \
  --http-settings-cookie-based-affinity Disabled \
  --frontend-port 80 \
  --http-settings-port 80 \
  --http-settings-protocol Http \
  --public-ip-address mvs-vr-appgw-pip
```

### Certificate Management

1. **Install cert-manager**

```bash
# Add cert-manager Helm repository
helm repo add jetstack https://charts.jetstack.io
helm repo update

# Install cert-manager
helm install cert-manager jetstack/cert-manager \
  --namespace cert-manager \
  --create-namespace \
  --version v1.10.0 \
  --set installCRDs=true
```

2. **Create a ClusterIssuer for Let's Encrypt**

Create a `cluster-issuer.yaml` file:

```yaml
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
```

Apply the ClusterIssuer:

```bash
kubectl apply -f cluster-issuer.yaml
```

## Scaling Considerations

### Horizontal Pod Autoscaling

1. **Enable Metrics Server**

```bash
kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
```

2. **Create a Horizontal Pod Autoscaler**

Create an `hpa.yaml` file:

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: mvs-vr-server-hpa
  namespace: mvs-vr
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mvs-vr-server
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

Apply the HPA:

```bash
kubectl apply -f hpa.yaml
```

### Vertical Pod Autoscaling

1. **Install Vertical Pod Autoscaler**

```bash
git clone https://github.com/kubernetes/autoscaler.git
cd autoscaler/vertical-pod-autoscaler
./hack/vpa-up.sh
```

2. **Create a Vertical Pod Autoscaler**

Create a `vpa.yaml` file:

```yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: mvs-vr-server-vpa
  namespace: mvs-vr
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mvs-vr-server
  updatePolicy:
    updateMode: Auto
  resourcePolicy:
    containerPolicies:
    - containerName: '*'
      minAllowed:
        cpu: 100m
        memory: 200Mi
      maxAllowed:
        cpu: 2
        memory: 4Gi
      controlledResources: ["cpu", "memory"]
```

Apply the VPA:

```bash
kubectl apply -f vpa.yaml
```

## Disaster Recovery

### Backup and Restore

1. **Database Backup**

For Supabase, you can use the built-in backup functionality:

- Go to the "Settings" section
- Click "Database"
- Configure automatic backups
- Download manual backups as needed

2. **Storage Backup**

For AWS S3:

```bash
# Enable versioning
aws s3api put-bucket-versioning --bucket mvs-vr-assets --versioning-configuration Status=Enabled

# Configure lifecycle rules
aws s3api put-bucket-lifecycle-configuration --bucket mvs-vr-assets --lifecycle-configuration file://lifecycle-config.json
```

For Azure Blob Storage:

```bash
# Enable soft delete
az storage blob service-properties update --account-name mvsvrassets --enable-delete-retention true --delete-retention-days 7
```

3. **Kubernetes Resource Backup**

Use Velero for Kubernetes resource backup:

```bash
# Install Velero
velero install \
  --provider aws \
  --plugins velero/velero-plugin-for-aws:v1.5.0 \
  --bucket velero-backups \
  --backup-location-config region=us-west-2 \
  --snapshot-location-config region=us-west-2 \
  --secret-file ./credentials-velero

# Create a backup
velero backup create mvs-vr-backup --include-namespaces mvs-vr

# Restore from backup
velero restore create --from-backup mvs-vr-backup
```

### High Availability

1. **Multi-Zone Deployment**

Deploy your Kubernetes cluster across multiple availability zones:

```bash
# AWS EKS
eksctl create cluster \
  --name mvs-vr-cluster \
  --region us-west-2 \
  --zones us-west-2a,us-west-2b,us-west-2c \
  --nodegroup-name standard-workers \
  --node-type m5.large \
  --nodes 3 \
  --nodes-min 1 \
  --nodes-max 5 \
  --managed
```

2. **Multi-Region Deployment**

For global high availability, deploy to multiple regions and use a global load balancer:

- Deploy Kubernetes clusters in multiple regions
- Set up a global load balancer (AWS Global Accelerator, Azure Front Door, etc.)
- Configure DNS to route traffic to the nearest region
- Implement data replication between regions
