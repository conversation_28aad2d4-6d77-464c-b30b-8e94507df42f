#!/bin/bash
# Enhanced deployment script for MVS-VR v2 to DigitalOcean
# Supports incremental updates and missing file detection

set -euo pipefail

# Configuration
TARGET_DIR="/opt/mvs-vr-v2"
SERVER_IP="**************"
SERVER_USER="root"
SSH_KEY="/root/.ssh/id_rsa"
LOCAL_EXPORTS_DIR="/deploy/docker-exports"
MANIFEST_FILE="$LOCAL_EXPORTS_DIR/export-manifest.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# SSH command wrapper
ssh_exec() {
    ssh -o StrictHostKeyChecking=no -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "$@"
}

# SCP command wrapper
scp_copy() {
    scp -i "$SSH_KEY" "$@" "$SERVER_USER@$SERVER_IP:$TARGET_DIR/"
}

# Check if manifest file exists
check_manifest() {
    if [[ ! -f "$MANIFEST_FILE" ]]; then
        log_error "Manifest file not found: $MANIFEST_FILE"
        exit 1
    fi
    log_info "Found manifest file: $MANIFEST_FILE"
}

# Get file list from manifest
get_expected_files() {
    if command -v jq >/dev/null 2>&1; then
        jq -r '.images[].file' "$MANIFEST_FILE"
    else
        # Fallback if jq is not available
        grep '"file"' "$MANIFEST_FILE" | cut -d'"' -f4
    fi
}

# Get file size from manifest
get_expected_size() {
    local filename="$1"
    if command -v jq >/dev/null 2>&1; then
        jq -r ".images[] | select(.file == \"$filename\") | .size" "$MANIFEST_FILE"
    else
        # Fallback parsing
        grep -A 10 "\"file\": \"$filename\"" "$MANIFEST_FILE" | grep '"size"' | head -1 | grep -o '[0-9]*'
    fi
}

# Check what files exist on remote server
check_remote_files() {
    log_info "Checking existing files on remote server..."

    # Create target directory if it doesn't exist
    ssh_exec "mkdir -p $TARGET_DIR"

    # Get list of existing files with sizes
    ssh_exec "cd $TARGET_DIR && find . -name '*.tar.zip' -o -name '*.tar' -o -name 'export-manifest.json' | xargs -I {} sh -c 'if [ -f \"{}\" ]; then echo \"{} \$(stat -c%s \"{}\")\" 2>/dev/null || echo \"{} \$(wc -c < \"{}\")\" 2>/dev/null || echo \"{} 0\"; fi' 2>/dev/null || echo ''"
}

# Compare local and remote files
find_missing_or_updated_files() {
    log_info "Analyzing files to determine what needs to be transferred..."

    local remote_files_info
    remote_files_info=$(check_remote_files)

    local files_to_transfer=()
    local expected_files
    expected_files=$(get_expected_files)

    # Check each expected file
    while IFS= read -r filename; do
        if [[ -z "$filename" ]]; then
            continue
        fi

        local local_file="$LOCAL_EXPORTS_DIR/$filename"
        local expected_size
        expected_size=$(get_expected_size "$filename")

        # Check if local file exists
        if [[ ! -f "$local_file" ]]; then
            log_warning "Local file missing: $filename"
            continue
        fi

        # Get actual local file size
        local actual_local_size
        actual_local_size=$(stat -c%s "$local_file" 2>/dev/null || wc -c < "$local_file" 2>/dev/null || echo "0")

        # Check if file exists on remote and get its size
        local remote_size
        remote_size=$(echo "$remote_files_info" | grep "./$filename " | awk '{print $2}' || echo "0")

        if [[ "$remote_size" == "0" ]] || [[ -z "$remote_size" ]]; then
            log_info "File missing on remote: $filename"
            files_to_transfer+=("$filename")
        elif [[ "$actual_local_size" != "$remote_size" ]]; then
            log_info "File size mismatch for $filename (local: $actual_local_size, remote: $remote_size)"
            files_to_transfer+=("$filename")
        elif [[ -n "$expected_size" ]] && [[ "$actual_local_size" != "$expected_size" ]]; then
            log_warning "Local file size doesn't match manifest for $filename (actual: $actual_local_size, expected: $expected_size)"
            files_to_transfer+=("$filename")
        else
            log_success "File up to date: $filename"
        fi
    done <<< "$expected_files"

    # Always include manifest file
    files_to_transfer+=("export-manifest.json")

    printf '%s\n' "${files_to_transfer[@]}"
}

# Transfer specific files
transfer_files() {
    local files_to_transfer=("$@")

    if [[ ${#files_to_transfer[@]} -eq 0 ]]; then
        log_info "No files need to be transferred"
        return 0
    fi

    log_info "Transferring ${#files_to_transfer[@]} files..."

    for filename in "${files_to_transfer[@]}"; do
        local local_file="$LOCAL_EXPORTS_DIR/$filename"

        if [[ -f "$local_file" ]]; then
            log_info "Transferring: $filename"
            if scp_copy "$local_file"; then
                log_success "Transferred: $filename"
            else
                log_error "Failed to transfer: $filename"
                return 1
            fi
        else
            log_warning "Local file not found, skipping: $filename"
        fi
    done
}

# Verify transferred files
verify_transferred_files() {
    local files_to_verify=("$@")

    log_info "Verifying transferred files..."

    for filename in "${files_to_verify[@]}"; do
        if [[ "$filename" == "export-manifest.json" ]]; then
            continue  # Skip manifest verification
        fi

        local expected_size
        expected_size=$(get_expected_size "$filename")

        if [[ -n "$expected_size" ]] && [[ "$expected_size" != "0" ]]; then
            local remote_size
            remote_size=$(ssh_exec "cd $TARGET_DIR && stat -c%s '$filename' 2>/dev/null || wc -c < '$filename' 2>/dev/null || echo '0'")

            if [[ "$remote_size" == "$expected_size" ]]; then
                log_success "Verified: $filename (size: $remote_size)"
            else
                log_error "Verification failed for $filename (expected: $expected_size, actual: $remote_size)"
                return 1
            fi
        fi
    done
}

# Main deployment function
main() {
    log_info "Starting enhanced MVS-VR v2 deployment to DigitalOcean..."

    # Check prerequisites
    check_manifest

    # Find files that need to be transferred
    local files_to_transfer
    mapfile -t files_to_transfer < <(find_missing_or_updated_files)

    if [[ ${#files_to_transfer[@]} -eq 0 ]]; then
        log_info "All files are up to date on the server"
    else
        # Transfer files
        transfer_files "${files_to_transfer[@]}"

        # Verify transferred files
        verify_transferred_files "${files_to_transfer[@]}"
    fi

    # Copy environment file
    log_info "Copying environment configuration..."
    if [[ -f "/deploy/.env" ]]; then
        scp_copy "/deploy/.env"
        log_success "Environment file copied"
    else
        log_warning "Environment file not found: /deploy/.env"
    fi

    # Execute deployment on remote server
    log_info "Executing deployment on remote server..."
    ssh_exec "cd $TARGET_DIR && docker-compose pull && docker-compose up -d"

    log_success "Deployment completed successfully!"

    # Show deployment status
    log_info "Checking deployment status..."
    ssh_exec "cd $TARGET_DIR && docker-compose ps"
}

# Run main function
main "$@"
