# Export available Docker images for testing deployment scripts
# This script exports only the images that are currently available

param(
    [switch]$Verbose
)

# Script configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir
$ExportDir = Join-Path $ProjectDir "docker-exports"
$Version = Get-Date -Format "yyyyMMdd-HHmmss"

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

# Logging functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

function Write-Debug {
    param([string]$Message)
    if ($Verbose) {
        Write-Host "[DEBUG] $Message" -ForegroundColor $Colors.Cyan
    }
}

# Get available images
function Get-AvailableImages {
    Write-Info "Checking available Docker images..."
    
    $images = docker images --format "{{.Repository}}:{{.Tag}}" | Where-Object { $_ -match "mvs-vr-v2-" }
    
    $availableImages = @()
    foreach ($image in $images) {
        if ($image -match "mvs-vr-v2-(.+?):(.*)" -and $Matches[2] -ne "<none>") {
            $serviceName = $Matches[1]
            $tag = $Matches[2]
            
            # Skip if it's just "latest" and we have a versioned tag
            if ($tag -eq "latest") {
                $pattern = "mvs-vr-v2-$serviceName" + ":\d{8}-\d{6}"
                $versionedExists = $images | Where-Object { $_ -match $pattern }
                if ($versionedExists) {
                    Write-Debug "Skipping $image (versioned tag exists)"
                    continue
                }
            }
            
            $availableImages += @{
                Service = $serviceName
                Tag = $tag
                FullName = $image
            }
        }
    }
    
    Write-Info "Found $($availableImages.Count) available images:"
    foreach ($img in $availableImages) {
        Write-Info "  - $($img.FullName)"
    }
    
    return $availableImages
}

# Export single image
function Export-Image {
    param(
        [hashtable]$ImageInfo,
        [string]$OutputPath
    )
    
    $imageName = $ImageInfo.FullName
    $serviceName = $ImageInfo.Service
    
    Write-Info "Exporting $imageName..."
    
    try {
        # Export the image
        docker save $imageName | Compress-Archive -DestinationPath $OutputPath -Force
        
        if (Test-Path $OutputPath) {
            $size = (Get-Item $OutputPath).Length
            $sizeHuman = if ($size -gt 1GB) { "{0:N2} GB" -f ($size / 1GB) }
                        elseif ($size -gt 1MB) { "{0:N2} MB" -f ($size / 1MB) }
                        elseif ($size -gt 1KB) { "{0:N2} KB" -f ($size / 1KB) }
                        else { "$size bytes" }
            
            Write-Success "Exported $serviceName`: $sizeHuman"
            
            return @{
                Service = $serviceName
                File = Split-Path -Leaf $OutputPath
                Size = $size
                Image = $imageName
            }
        }
        else {
            Write-Error "Export failed for $serviceName"
            return $null
        }
    }
    catch {
        Write-Error "Failed to export $serviceName`: $($_.Exception.Message)"
        return $null
    }
}

# Create manifest file
function New-Manifest {
    param([array]$ExportedImages)
    
    $manifest = @{
        version = $Version
        export_date = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        images = @{}
    }
    
    foreach ($img in $ExportedImages) {
        if ($img) {
            $manifest.images[$img.Service] = @{
                file = $img.File
                size = $img.Size
                image = $img.Image
                service = $img.Service
            }
        }
    }
    
    $manifestPath = Join-Path $ExportDir "export-manifest.json"
    $manifest | ConvertTo-Json -Depth 10 | Set-Content $manifestPath -Encoding UTF8
    
    Write-Success "Created manifest: $manifestPath"
    Write-Info "Manifest contains $($manifest.images.Count) images"
}

# Main export function
function Start-Export {
    Write-Info "Starting Docker image export for testing..."
    
    # Create export directory
    if (-not (Test-Path $ExportDir)) {
        New-Item -ItemType Directory -Path $ExportDir -Force | Out-Null
    }
    
    # Get available images
    $availableImages = Get-AvailableImages
    
    if ($availableImages.Count -eq 0) {
        Write-Error "No MVS-VR v2 images found. Please build some images first."
        exit 1
    }
    
    # Export each image
    $exportedImages = @()
    $totalSize = 0
    
    foreach ($imageInfo in $availableImages) {
        $serviceName = $imageInfo.Service
        $filename = "$serviceName-$Version.tar.zip"
        $outputPath = Join-Path $ExportDir $filename
        
        $result = Export-Image -ImageInfo $imageInfo -OutputPath $outputPath
        if ($result) {
            $exportedImages += $result
            $totalSize += $result.Size
        }
    }
    
    # Create manifest
    New-Manifest -ExportedImages $exportedImages
    
    # Summary
    $totalSizeHuman = if ($totalSize -gt 1GB) { "{0:N2} GB" -f ($totalSize / 1GB) }
                     elseif ($totalSize -gt 1MB) { "{0:N2} MB" -f ($totalSize / 1MB) }
                     elseif ($totalSize -gt 1KB) { "{0:N2} KB" -f ($totalSize / 1KB) }
                     else { "$totalSize bytes" }
    
    Write-Success "Export completed successfully!"
    Write-Info "Exported $($exportedImages.Count) images"
    Write-Info "Total size: $totalSizeHuman"
    Write-Info "Export directory: $ExportDir"
    
    # List exported files
    Write-Info "Exported files:"
    Get-ChildItem $ExportDir -Filter "*.tar.zip" | ForEach-Object {
        $size = if ($_.Length -gt 1MB) { "{0:N2} MB" -f ($_.Length / 1MB) } else { "{0:N2} KB" -f ($_.Length / 1KB) }
        Write-Info "  - $($_.Name) ($size)"
    }
}

# Run export
Start-Export
