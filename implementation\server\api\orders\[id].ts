/**
 * Order Detail API
 *
 * This module provides API endpoints for order detail operations.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase, executeSupabaseQuery } from '../../shared/utils/supabase-client';
import { validateRequest } from '../middleware/validation';
import { authenticateRequest } from '../middleware/auth';
import {
  errorMiddleware,
  notFoundError,
  badRequestError,
  unauthorizedError,
} from '../middleware/error';
import { Logger } from '../../services/integration/logger';

// Create logger
const logger = new Logger();

/**
 * Get order handler
 *
 * @param req Request
 * @param res Response
 */
async function getOrder(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('getOrder')(req, res, async () => {
        const { id } = req.query;

        // Get order
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select('*')
          .eq('id', id)
          .single();

        if (orderError) {
          logger.error('Error getting order', { error: orderError });
          res.status(404).json({
            success: false,
            error: {
              code: 'ORDER_NOT_FOUND',
              message: 'Order not found',
            },
          });
          return;
        }

        // Check if user is authorized to view this order
        if (req.user?.id !== orderData.user_id && req.user?.role !== 'admin') {
          // Check if user is the vendor for this order
          const { data: vendorData, error: vendorError } = await supabase
            .from('vendors')
            .select('user_id')
            .eq('id', orderData.vendor_id)
            .single();

          if (vendorError || req.user?.id !== vendorData.user_id) {
            res.status(403).json({
              success: false,
              error: {
                code: 'UNAUTHORIZED',
                message: 'You are not authorized to view this order',
              },
            });
            return;
          }
        }

        // Return success response
        res.status(200).json({
          success: true,
          data: orderData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Update order handler
 *
 * @param req Request
 * @param res Response
 */
async function updateOrder(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('updateOrder')(req, res, async () => {
        const { id } = req.query;
        const { status, quantity, metadata } = req.body;

        // Get order to check authorization
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select('*')
          .eq('id', id)
          .single();

        if (orderError) {
          logger.error('Error getting order for authorization check', { error: orderError });
          res.status(404).json({
            success: false,
            error: {
              code: 'ORDER_NOT_FOUND',
              message: 'Order not found',
            },
          });
          return;
        }

        // Check if user is authorized to update this order
        let isAuthorized = false;

        // Admin can update any order
        if (req.user?.role === 'admin') {
          isAuthorized = true;
        }
        // User can update their own order (but only certain fields)
        else if (req.user?.id === orderData.user_id) {
          // Users can only update quantity and metadata, not status
          if (status && status !== orderData.status) {
            res.status(403).json({
              success: false,
              error: {
                code: 'UNAUTHORIZED',
                message: 'You are not authorized to update the status of this order',
              },
            });
            return;
          }
          isAuthorized = true;
        }
        // Vendor can update orders for their products (but only status)
        else {
          const { data: vendorData, error: vendorError } = await supabase
            .from('vendors')
            .select('user_id')
            .eq('id', orderData.vendor_id)
            .single();

          if (!vendorError && req.user?.id === vendorData.user_id) {
            // Vendors can only update status, not quantity or metadata
            if ((quantity && quantity !== orderData.quantity) || metadata) {
              res.status(403).json({
                success: false,
                error: {
                  code: 'UNAUTHORIZED',
                  message:
                    'You are not authorized to update the quantity or metadata of this order',
                },
              });
              return;
            }
            isAuthorized = true;
          }
        }

        if (!isAuthorized) {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to update this order',
            },
          });
          return;
        }

        // Update order
        const { data: updatedOrderData, error: updateError } = await supabase
          .from('orders')
          .update({
            ...(status && { status }),
            ...(quantity && { quantity }),
            ...(metadata && { metadata: { ...orderData.metadata, ...metadata } }),
          })
          .eq('id', id)
          .select()
          .single();

        if (updateError) {
          logger.error('Error updating order', { error: updateError });
          res.status(500).json({
            success: false,
            error: {
              code: 'UPDATE_ERROR',
              message: 'Failed to update order',
            },
          });
          return;
        }

        // Return success response
        res.status(200).json({
          success: true,
          data: updatedOrderData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Cancel order handler
 *
 * @param req Request
 * @param res Response
 */
async function cancelOrder(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('cancelOrder')(req, res, async () => {
        const { id } = req.query;
        const { reason } = req.body;

        // Get order to check authorization
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select('*')
          .eq('id', id)
          .single();

        if (orderError) {
          logger.error('Error getting order for authorization check', { error: orderError });
          res.status(404).json({
            success: false,
            error: {
              code: 'ORDER_NOT_FOUND',
              message: 'Order not found',
            },
          });
          return;
        }

        // Check if order can be canceled
        if (orderData.status === 'completed' || orderData.status === 'cancelled') {
          res.status(400).json({
            success: false,
            error: {
              code: 'INVALID_STATUS',
              message: `Order cannot be cancelled because it is already ${orderData.status}`,
            },
          });
          return;
        }

        // Check if user is authorized to cancel this order
        let isAuthorized = false;

        // Admin can cancel any order
        if (req.user?.role === 'admin') {
          isAuthorized = true;
        }
        // User can cancel their own order
        else if (req.user?.id === orderData.user_id) {
          isAuthorized = true;
        }
        // Vendor can cancel orders for their products
        else {
          const { data: vendorData, error: vendorError } = await supabase
            .from('vendors')
            .select('user_id')
            .eq('id', orderData.vendor_id)
            .single();

          if (!vendorError && req.user?.id === vendorData.user_id) {
            isAuthorized = true;
          }
        }

        if (!isAuthorized) {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to cancel this order',
            },
          });
          return;
        }

        // Update order status to cancelled
        const { data: updatedOrderData, error: updateError } = await supabase
          .from('orders')
          .update({
            status: 'cancelled',
            metadata: {
              ...orderData.metadata,
              cancellation_reason: reason || 'No reason provided',
              cancelled_by: req.user?.id,
              cancelled_at: new Date().toISOString(),
            },
          })
          .eq('id', id)
          .select()
          .single();

        if (updateError) {
          logger.error('Error cancelling order', { error: updateError });
          res.status(500).json({
            success: false,
            error: {
              code: 'UPDATE_ERROR',
              message: 'Failed to cancel order',
            },
          });
          return;
        }

        // Return success response
        res.status(200).json({
          success: true,
          data: updatedOrderData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Handler for order detail API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'GET':
      await getOrder(req, res);
      break;
    case 'PUT':
      await updateOrder(req, res);
      break;
    case 'DELETE':
      await cancelOrder(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
