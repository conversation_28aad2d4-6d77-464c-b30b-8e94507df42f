/**
 * Progressive Loading API
 *
 * This API provides endpoints for progressive loading of assets.
 */

import { Router } from 'express';
import {
  ProgressiveLoadingService,
  LoadPriority,
  NetworkQuality,
} from '../../services/asset/progressive-loading-service';
import { CdnIntegrationService } from '../../services/asset/cdn-integration-service';
import { AssetBundleOptimizer } from '../../services/asset/asset-bundle-optimizer';
import { logger } from '../../shared/utils/logger';
import { supabase } from '../../shared/utils/supabase-client';

const router = Router();
const cdnService = new CdnIntegrationService(supabase);
const bundleOptimizer = new AssetBundleOptimizer(supabase);
const progressiveLoadingService = new ProgressiveLoadingService(
  supabase,
  cdnService,
  bundleOptimizer,
  {
    maxConcurrentLoads: 4,
    chunkSize: 1024 * 1024, // 1MB
    adaptiveQuality: true,
    preloadCriticalAssets: true,
    prioritizeVisibleAssets: true,
  },
);

/**
 * Load an asset
 *
 * @route POST /api/assets/progressive/load
 * @body {string} assetId - Asset ID
 * @body {number} priority - Load priority (0-4)
 * @body {string} quality - Quality level (optional)
 * @body {string} region - User region (optional)
 * @returns {object} Load status
 */
router.post('/load', async (req, res) => {
  try {
    const { assetId, priority, quality, region } = req.body;

    // Validate asset ID
    if (!assetId) {
      return res.status(400).json({ error: 'Asset ID is required' });
    }

    // Validate priority
    if (priority === undefined || priority < 0 || priority > 4) {
      return res.status(400).json({ error: 'Priority must be between 0 and 4' });
    }

    // Load asset
    const url = await progressiveLoadingService.loadAsset({
      assetId,
      priority: priority as LoadPriority,
      quality,
      region,
    });

    return res.json({ url });
  } catch (error) {
    logger.error('Error loading asset', { error });
    return res.status(500).json({ error: 'Error loading asset' });
  }
});

/**
 * Get load status for an asset
 *
 * @route GET /api/assets/progressive/status/:assetId
 * @param {string} assetId - Asset ID
 * @returns {object} Load status
 */
router.get('/status/:assetId', (req, res) => {
  try {
    const { assetId } = req.params;

    // Validate asset ID
    if (!assetId) {
      return res.status(400).json({ error: 'Asset ID is required' });
    }

    // Get load status
    const status = progressiveLoadingService.getLoadStatus(assetId);
    if (!status) {
      return res.status(404).json({ error: 'Asset not found' });
    }

    return res.json(status);
  } catch (error) {
    logger.error('Error getting load status', { error });
    return res.status(500).json({ error: 'Error getting load status' });
  }
});

/**
 * Get all load statuses
 *
 * @route GET /api/assets/progressive/status
 * @returns {object} All load statuses
 */
router.get('/status', (req, res) => {
  try {
    // Get all load statuses
    const statuses = progressiveLoadingService.getAllLoadStatuses();

    return res.json({
      statuses: Array.from(statuses.values()),
    });
  } catch (error) {
    logger.error('Error getting all load statuses', { error });
    return res.status(500).json({ error: 'Error getting all load statuses' });
  }
});

/**
 * Cancel loading an asset
 *
 * @route DELETE /api/assets/progressive/load/:assetId
 * @param {string} assetId - Asset ID
 * @returns {object} Success message
 */
router.delete('/load/:assetId', (req, res) => {
  try {
    const { assetId } = req.params;

    // Validate asset ID
    if (!assetId) {
      return res.status(400).json({ error: 'Asset ID is required' });
    }

    // Cancel loading
    progressiveLoadingService.cancelLoading(assetId);

    return res.json({ message: 'Loading cancelled' });
  } catch (error) {
    logger.error('Error cancelling loading', { error });
    return res.status(500).json({ error: 'Error cancelling loading' });
  }
});

/**
 * Set network quality
 *
 * @route POST /api/assets/progressive/network-quality
 * @body {number} quality - Network quality (0-3)
 * @returns {object} Success message
 */
router.post('/network-quality', (req, res) => {
  try {
    const { quality } = req.body;

    // Validate quality
    if (quality === undefined || quality < 0 || quality > 3) {
      return res.status(400).json({ error: 'Quality must be between 0 and 3' });
    }

    // Set network quality
    progressiveLoadingService.setNetworkQuality(quality as NetworkQuality);

    return res.json({ message: 'Network quality set' });
  } catch (error) {
    logger.error('Error setting network quality', { error });
    return res.status(500).json({ error: 'Error setting network quality' });
  }
});

export default router;
