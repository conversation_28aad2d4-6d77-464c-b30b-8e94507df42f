<template>
  <div class="admin-portal">
    <v-app>
      <v-navigation-drawer
        v-model="drawer"
        app
        clipped
        :width="240"
      >
        <v-list>
          <v-list-item>
            <v-list-item-content>
              <v-list-item-title class="text-h6">
                Admin Portal
              </v-list-item-title>
              <v-list-item-subtitle>
                MVS-VR Platform
              </v-list-item-subtitle>
            </v-list-item-content>
          </v-list-item>

          <v-divider></v-divider>

          <v-list-item
            v-for="item in menuItems"
            :key="item.title"
            :to="item.to"
            link
          >
            <v-list-item-icon>
              <v-icon>{{ item.icon }}</v-icon>
            </v-list-item-icon>

            <v-list-item-content>
              <v-list-item-title>{{ item.title }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-navigation-drawer>

      <v-app-bar
        app
        clipped-left
        color="primary"
        dark
      >
        <v-app-bar-nav-icon @click.stop="drawer = !drawer"></v-app-bar-nav-icon>
        <v-toolbar-title>{{ pageTitle }}</v-toolbar-title>
        <v-spacer></v-spacer>
        <v-btn icon>
          <v-icon>mdi-bell</v-icon>
        </v-btn>
        <v-btn icon>
          <v-icon>mdi-account-circle</v-icon>
        </v-btn>
      </v-app-bar>

      <v-main>
        <v-container fluid>
          <router-view></router-view>
        </v-container>
      </v-main>
    </v-app>
  </div>
</template>

<script>
export default {
  name: 'AdminPortal',
  
  data() {
    return {
      drawer: true,
      menuItems: [
        {
          title: 'Dashboard',
          icon: 'mdi-view-dashboard',
          to: '/admin/dashboard'
        },
        {
          title: 'System Monitoring',
          icon: 'mdi-monitor-dashboard',
          to: '/admin/system-monitoring'
        },
        {
          title: 'User Management',
          icon: 'mdi-account-group',
          to: '/admin/users'
        },
        {
          title: 'Vendor Management',
          icon: 'mdi-store',
          to: '/admin/vendors'
        },
        {
          title: 'Content Management',
          icon: 'mdi-file-document-multiple',
          to: '/admin/content'
        },
        {
          title: 'Settings',
          icon: 'mdi-cog',
          to: '/admin/settings'
        }
      ]
    };
  },
  
  computed: {
    pageTitle() {
      const currentRoute = this.$route.path;
      const menuItem = this.menuItems.find(item => item.to === currentRoute);
      return menuItem ? menuItem.title : 'Admin Portal';
    }
  }
};
</script>

<style scoped>
.admin-portal {
  height: 100%;
}
</style>
