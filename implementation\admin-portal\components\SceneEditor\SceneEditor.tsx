import React, { useState, useEffect } from 'react';
import { Box, Button, Typography, TextField, Grid, Paper, Tabs, Tab, IconButton } from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon, Save as SaveIcon } from '@mui/icons-material';
import { useSupabaseClient } from '@supabase/auth-helpers-react';
import { Scene, SceneFlow } from '../../types/scene';
import LocationEditor from './LocationEditor';
import SpaceEditor from './SpaceEditor';
import FlowEditor from './FlowEditor';

interface SceneEditorProps {
  sceneId?: string;
  vendorId: string;
  onSave?: (scene: Scene) => void;
  onCancel?: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`scene-tabpanel-${index}`}
      aria-labelledby={`scene-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const SceneEditor: React.FC<SceneEditorProps> = ({ sceneId, vendorId, onSave, onCancel }) => {
  const supabase = useSupabaseClient();
  const [tabValue, setTabValue] = useState(0);
  const [scene, setScene] = useState<Scene | null>(null);
  const [flow, setFlow] = useState<SceneFlow | null>(null);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (sceneId) {
      loadScene();
      loadFlow();
    }
  }, [sceneId]);

  const loadScene = async () => {
    if (!sceneId) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('scenes')
        .select('*')
        .eq('id', sceneId)
        .single();

      if (error) {
        throw error;
      }

      setScene(data);
      setName(data.name);
      setDescription(data.description || '');
    } catch (err: any) {
      console.error('Error loading scene:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadFlow = async () => {
    if (!sceneId) return;

    try {
      const { data, error } = await supabase
        .from('scene_flows')
        .select('*')
        .eq('scene_id', sceneId)
        .eq('is_active', true)
        .single();

      if (error) {
        // If no active flow, try to get any flow
        const { data: anyFlow, error: anyFlowError } = await supabase
          .from('scene_flows')
          .select('*')
          .eq('scene_id', sceneId)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        if (anyFlowError) {
          console.error('Error loading scene flow:', anyFlowError);
          return;
        }

        setFlow(anyFlow);
      } else {
        setFlow(data);
      }
    } catch (err: any) {
      console.error('Error loading scene flow:', err);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSave = async () => {
    setLoading(true);
    setError(null);

    try {
      let sceneData: Scene;

      if (sceneId) {
        // Update existing scene
        const { data, error } = await supabase
          .from('scenes')
          .update({
            name,
            description,
            updated_at: new Date().toISOString(),
          })
          .eq('id', sceneId)
          .select()
          .single();

        if (error) {
          throw error;
        }

        sceneData = data;
      } else {
        // Create new scene
        const { data, error } = await supabase
          .from('scenes')
          .insert({
            vendor_id: vendorId,
            name,
            description,
            version: '1.0.0',
            configuration: {},
          })
          .select()
          .single();

        if (error) {
          throw error;
        }

        sceneData = data;
      }

      if (onSave) {
        onSave(sceneData);
      }
    } catch (err: any) {
      console.error('Error saving scene:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSaveFlow = async (updatedFlow: any) => {
    setLoading(true);
    setError(null);

    try {
      if (!sceneId) {
        throw new Error('Scene ID is required to save flow');
      }

      const flowName = updatedFlow.name || 'Default Flow';
      const flowDescription = updatedFlow.description || '';

      if (flow?.id) {
        // Update existing flow
        const { data, error } = await supabase
          .from('scene_flows')
          .update({
            name: flowName,
            description: flowDescription,
            flow: updatedFlow,
            is_active: true,
            updated_at: new Date().toISOString(),
          })
          .eq('id', flow.id)
          .select()
          .single();

        if (error) {
          throw error;
        }

        setFlow(data);
      } else {
        // Create new flow
        const { data, error } = await supabase
          .from('scene_flows')
          .insert({
            scene_id: sceneId,
            name: flowName,
            description: flowDescription,
            flow: updatedFlow,
            version: '1.0.0',
            is_active: true,
          })
          .select()
          .single();

        if (error) {
          throw error;
        }

        setFlow(data);
      }
    } catch (err: any) {
      console.error('Error saving scene flow:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ width: '100%' }}>
      <Paper sx={{ p: 2, mb: 2 }}>
        <Typography variant="h5" gutterBottom>
          {sceneId ? 'Edit Scene' : 'Create New Scene'}
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              label="Scene Name"
              fullWidth
              value={name}
              onChange={(e) => setName(e.target.value)}
              margin="normal"
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              label="Description"
              fullWidth
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              margin="normal"
              multiline
              rows={2}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
          {onCancel && (
            <Button onClick={onCancel} sx={{ mr: 1 }}>
              Cancel
            </Button>
          )}
          <Button
            variant="contained"
            color="primary"
            onClick={handleSave}
            disabled={loading || !name}
            startIcon={<SaveIcon />}
          >
            Save Scene
          </Button>
        </Box>

        {error && (
          <Typography color="error" sx={{ mt: 2 }}>
            {error}
          </Typography>
        )}
      </Paper>

      {sceneId && (
        <Paper sx={{ width: '100%' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
          >
            <Tab label="Locations" />
            <Tab label="Flow" />
            <Tab label="Preview" />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <LocationEditor 
              sceneId={sceneId} 
              locations={scene?.configuration?.locations || []} 
              onSave={(locations) => {
                // Update scene configuration with new locations
                const updatedConfig = { ...scene?.configuration, locations };
                // TODO: Save updated configuration
              }} 
            />
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <FlowEditor 
              sceneId={sceneId} 
              flow={flow?.flow || {}} 
              onSave={handleSaveFlow} 
            />
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Typography variant="h6">Scene Preview</Typography>
            <Box sx={{ mt: 2, p: 2, border: '1px dashed grey', borderRadius: 1, height: '400px' }}>
              <Typography variant="body2" color="text.secondary">
                Scene preview will be implemented here
              </Typography>
            </Box>
          </TabPanel>
        </Paper>
      )}
    </Box>
  );
};

export default SceneEditor;
