/**
 * Error Middleware
 *
 * This module provides middleware for handling errors.
 */

import { Request, Response, NextFunction } from 'express';
import { Logger } from '../../services/integration/logger';
import { ErrorHandler } from '../../services/integration/errorHandler';

// Create logger and error handler
const logger = new Logger();
const errorHandler = new ErrorHandler(logger);

/**
 * Custom error class
 */
export class ApiError extends Error {
  public readonly statusCode: number;
  public readonly code: string;
  public readonly details?: any;

  /**
   * Constructor
   *
   * @param message Error message
   * @param statusCode HTTP status code
   * @param code Error code
   * @param details Additional error details
   */
  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    details?: any,
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
  }
}

/**
 * Create a not found error
 *
 * @param message Error message
 * @param details Error details
 * @returns API error
 */
export const notFoundError = (message: string = 'Resource not found', details?: any): ApiError => {
  return new ApiError(message, 404, 'NOT_FOUND', details);
};

/**
 * Create a bad request error
 *
 * @param message Error message
 * @param details Error details
 * @returns API error
 */
export const badRequestError = (message: string = 'Bad request', details?: any): ApiError => {
  return new ApiError(message, 400, 'BAD_REQUEST', details);
};

/**
 * Create an unauthorized error
 *
 * @param message Error message
 * @param details Error details
 * @returns API error
 */
export const unauthorizedError = (message: string = 'Unauthorized', details?: any): ApiError => {
  return new ApiError(message, 401, 'UNAUTHORIZED', details);
};

/**
 * Create a forbidden error
 *
 * @param message Error message
 * @param details Error details
 * @returns API error
 */
export const forbiddenError = (message: string = 'Forbidden', details?: any): ApiError => {
  return new ApiError(message, 403, 'FORBIDDEN', details);
};

/**
 * Create an internal server error
 *
 * @param message Error message
 * @param details Error details
 * @returns API error
 */
export const internalServerError = (
  message: string = 'Internal server error',
  details?: any,
): ApiError => {
  return new ApiError(message, 500, 'INTERNAL_SERVER_ERROR', details);
};

/**
 * Create a service unavailable error
 *
 * @param message Error message
 * @param details Error details
 * @returns API error
 */
export const serviceUnavailableError = (
  message: string = 'Service unavailable',
  details?: any,
): ApiError => {
  return new ApiError(message, 503, 'SERVICE_UNAVAILABLE', details);
};

/**
 * Error middleware
 *
 * @param error Error
 * @param req Request
 * @param res Response
 * @param _next Next function (unused)
 */
export const errorMiddleware = (
  error: unknown,
  req: Request,
  res: Response,
  _next: NextFunction,
): void => {
  try {
    // Log the error
    errorHandler.handleError(error, `${req.method} ${req.path}`);

    // Determine the status code
    let statusCode = 500;
    let errorCode = 'INTERNAL_ERROR';
    let errorMessage = 'An unexpected error occurred';
    let errorDetails = undefined;

    // Handle ApiError
    if (error instanceof ApiError) {
      statusCode = error.statusCode;
      errorCode = error.code;
      errorMessage = error.message;
      errorDetails = error.details;
    }
    // Handle other known error types
    else if (error instanceof Error) {
      errorMessage = error.message;
    }

    // Send the error response
    res.status(statusCode).json({
      success: false,
      error: {
        code: errorCode,
        message: errorMessage,
        ...(errorDetails && { details: errorDetails }),
      },
    });
  } catch (err) {
    // If an error occurs while handling the error, log it and send a generic error response
    logger.error('Error in error middleware', { error: err });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
      },
    });
  }
};

export default {
  ApiError,
  notFoundError,
  badRequestError,
  unauthorizedError,
  forbiddenError,
  internalServerError,
  serviceUnavailableError,
  errorMiddleware,
};
