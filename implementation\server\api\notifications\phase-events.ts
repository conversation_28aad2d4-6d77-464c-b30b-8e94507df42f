/**
 * Phase Notification API
 *
 * This file defines the API endpoints for phase notifications.
 */

import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import {
  PhaseNotificationService,
  NotificationType,
  NotificationPriority,
} from '../../services/notifications/phase-notifications';

// Initialize phase notification service
const notificationService = new PhaseNotificationService(supabase);

/**
 * Get notifications for user
 *
 * @param req - Request
 * @param res - Response
 */
export const getUserNotifications = async (req: Request, res: Response): Promise<void> => {
  try {
    const { user_id } = req.params;
    const { limit, offset, read } = req.query;

    // Validate parameters
    if (!user_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_USER_ID',
          message: 'User ID is required',
        },
      });
      return;
    }

    // Build query
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('user_id', user_id)
      .order('created_at', { ascending: false });

    // Add filters
    if (read !== undefined) {
      query = query.eq('read', read === 'true');
    }

    // Add pagination
    if (limit) {
      query = query.limit(parseInt(limit as string));
    }

    if (offset) {
      query = query.offset(parseInt(offset as string));
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error getting user notifications', { error, userId: user_id });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting user notifications',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getUserNotifications', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Get notifications for scene
 *
 * @param req - Request
 * @param res - Response
 */
export const getSceneNotifications = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { limit, offset, type } = req.query;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Build query
    let query = supabase
      .from('notifications')
      .select('*')
      .eq('scene_id', scene_id)
      .order('created_at', { ascending: false });

    // Add filters
    if (type) {
      query = query.eq('type', type);
    }

    // Add pagination
    if (limit) {
      query = query.limit(parseInt(limit as string));
    }

    if (offset) {
      query = query.offset(parseInt(offset as string));
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error getting scene notifications', { error, sceneId: scene_id });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error getting scene notifications',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in getSceneNotifications', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Mark notification as read
 *
 * @param req - Request
 * @param res - Response
 */
export const markNotificationAsRead = async (req: Request, res: Response): Promise<void> => {
  try {
    const { notification_id } = req.params;

    // Validate parameters
    if (!notification_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_NOTIFICATION_ID',
          message: 'Notification ID is required',
        },
      });
      return;
    }

    // Update notification
    const { data, error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notification_id)
      .select()
      .single();

    if (error) {
      logger.error('Error marking notification as read', {
        error,
        notificationId: notification_id,
      });
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error marking notification as read',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data,
    });
  } catch (error) {
    logger.error('Error in markNotificationAsRead', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Create custom notification
 *
 * @param req - Request
 * @param res - Response
 */
export const createCustomNotification = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type, priority, title, message, scene_id, phase, user_id, metadata } = req.body;

    // Validate parameters
    if (!type || !priority || !title || !message || !scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Type, priority, title, message, and scene_id are required',
        },
      });
      return;
    }

    // Validate type
    if (!Object.values(NotificationType).includes(type)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_TYPE',
          message: 'Invalid notification type',
        },
      });
      return;
    }

    // Validate priority
    if (!Object.values(NotificationPriority).includes(priority)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PRIORITY',
          message: 'Invalid notification priority',
        },
      });
      return;
    }

    // Create notification
    const result = await notificationService.createNotification({
      type,
      priority,
      title,
      message,
      scene_id,
      phase,
      user_id,
      metadata,
    });

    if (!result.success) {
      res.status(500).json({
        success: false,
        error: {
          code: 'NOTIFICATION_FAILED',
          message: result.error || 'Failed to create notification',
        },
      });
      return;
    }

    res.status(201).json({
      success: true,
      data: result.notification,
    });
  } catch (error) {
    logger.error('Error in createCustomNotification', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on method and path
    if (req.method === 'GET' && req.path.includes('/user/')) {
      await getUserNotifications(req, res);
    } else if (req.method === 'GET' && req.path.includes('/scene/')) {
      await getSceneNotifications(req, res);
    } else if (req.method === 'PATCH' && req.path.includes('/read/')) {
      await markNotificationAsRead(req, res);
    } else if (req.method === 'POST') {
      await createCustomNotification(req, res);
    } else {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
    }
  } catch (error) {
    logger.error('Error in notification handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
}
