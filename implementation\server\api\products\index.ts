/**
 * Product Management API
 *
 * This module provides API endpoints for product management.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { supabase, executeSupabaseQuery } from '../../shared/utils/supabase-client';
import { validateRequest } from '../middleware/validation';
import { authenticateRequest } from '../middleware/auth';
import {
  errorMiddleware,
  notFoundError,
  badRequestError,
  unauthorizedError,
} from '../middleware/error';
import { Logger } from '../../services/integration/logger';

// Create logger
const logger = new Logger();

/**
 * Create product handler
 *
 * @param req Request
 * @param res Response
 */
async function createProduct(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      // Validate request
      validateRequest('createProduct')(req, res, async () => {
        const { vendor_id, name, description, metadata, status } = req.body;

        // Check if user is authorized to create product for this vendor
        const { data: vendorData, error: vendorError } = await supabase
          .from('vendors')
          .select('user_id')
          .eq('id', vendor_id)
          .single();

        if (vendorError) {
          logger.error('Error getting vendor for authorization check', { error: vendorError });
          res.status(404).json({
            success: false,
            error: {
              code: 'VENDOR_NOT_FOUND',
              message: 'Vendor not found',
            },
          });
          return;
        }

        if (req.user?.id !== vendorData.user_id && req.user?.role !== 'admin') {
          res.status(403).json({
            success: false,
            error: {
              code: 'UNAUTHORIZED',
              message: 'You are not authorized to create products for this vendor',
            },
          });
          return;
        }

        // Create product
        const { data: productData, error: productError } = await supabase
          .from('products')
          .insert({
            vendor_id,
            name,
            description,
            metadata: metadata || {},
            status: status || 'draft',
          })
          .select()
          .single();

        if (productError) {
          logger.error('Error creating product', { error: productError });
          res.status(500).json({
            success: false,
            error: {
              code: 'PRODUCT_CREATION_ERROR',
              message: 'Failed to create product',
            },
          });
          return;
        }

        // Return success response
        res.status(201).json({
          success: true,
          data: productData,
        });
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Get products handler
 *
 * @param req Request
 * @param res Response
 */
async function getProducts(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Authenticate request
    authenticateRequest(req, res, async () => {
      const { vendor_id, status } = req.query;

      // Build query
      let query = supabase.from('products').select('*');

      // Filter by vendor ID if provided
      if (vendor_id) {
        query = query.eq('vendor_id', vendor_id);
      }

      // Filter by status if provided
      if (status) {
        query = query.eq('status', status);
      }

      // Execute query
      const { data: productsData, error: productsError } = await query;

      if (productsError) {
        logger.error('Error getting products', { error: productsError });
        res.status(500).json({
          success: false,
          error: {
            code: 'QUERY_ERROR',
            message: 'Failed to get products',
          },
        });
        return;
      }

      // Return success response
      res.status(200).json({
        success: true,
        data: productsData,
      });
    });
  } catch (error) {
    errorMiddleware(error, req, res, () => {});
  }
}

/**
 * Handler for product API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'POST':
      await createProduct(req, res);
      break;
    case 'GET':
      await getProducts(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
