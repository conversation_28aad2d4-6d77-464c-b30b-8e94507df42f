/**
 * Monitoring API Routes
 * 
 * API endpoints for predictive monitoring, anomaly detection, and alerting
 */

const express = require('express');
const { PredictiveMonitoringService } = require('../../services/monitoring/predictive-monitoring-service');
const { runPredictiveMonitoringTests } = require('../../tests/predictive-monitoring-test-framework');

const logger = require('../../utils/logger').getLogger('monitoring-api');

const router = express.Router();

// Initialize predictive monitoring service
const monitoringService = new PredictiveMonitoringService({
  anomalyOutputPath: process.env.ANOMALY_OUTPUT_PATH,
  alertOutputPath: process.env.ALERT_OUTPUT_PATH,
  notificationChannels: (process.env.NOTIFICATION_CHANNELS || 'email').split(','),
  visualizationEnabled: process.env.MONITORING_VISUALIZATION_ENABLED !== 'false'
});

// Initialize service
monitoringService.initialize().catch(error => {
  logger.error('Failed to initialize monitoring service:', error);
});

/**
 * @swagger
 * /api/monitoring/health:
 *   get:
 *     summary: Get monitoring service health status
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Health status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                 components:
 *                   type: object
 *                 monitoredMetrics:
 *                   type: number
 *                 activeAlerts:
 *                   type: number
 */
router.get('/health', (req, res) => {
  try {
    const health = monitoringService.getHealthStatus();
    res.json({
      success: true,
      data: health
    });
  } catch (error) {
    logger.error('Error getting monitoring health:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: 'Failed to get monitoring health status'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/statistics:
 *   get:
 *     summary: Get monitoring statistics
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Monitoring statistics
 */
router.get('/statistics', (req, res) => {
  try {
    const stats = monitoringService.getStatistics();
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logger.error('Error getting monitoring statistics:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STATS_FAILED',
        message: 'Failed to get monitoring statistics'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/metrics/{metricName}/data:
 *   get:
 *     summary: Get metric data
 *     tags: [Monitoring]
 *     parameters:
 *       - in: path
 *         name: metricName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the metric
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Maximum number of data points to return
 *     responses:
 *       200:
 *         description: Metric data
 */
router.get('/metrics/:metricName/data', (req, res) => {
  try {
    const { metricName } = req.params;
    const limit = parseInt(req.query.limit || '100', 10);
    
    const data = monitoringService.getMetricData(metricName, limit);
    
    res.json({
      success: true,
      data: {
        metric: metricName,
        dataPoints: data.length,
        data
      }
    });
  } catch (error) {
    logger.error('Error getting metric data:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'METRIC_DATA_FAILED',
        message: 'Failed to get metric data'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/metrics/{metricName}/data:
 *   post:
 *     summary: Add metric data point
 *     tags: [Monitoring]
 *     parameters:
 *       - in: path
 *         name: metricName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the metric
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - value
 *             properties:
 *               value:
 *                 type: number
 *                 description: Metric value
 *               timestamp:
 *                 type: string
 *                 format: date-time
 *                 description: Timestamp (optional, defaults to current time)
 *     responses:
 *       201:
 *         description: Data point added successfully
 */
router.post('/metrics/:metricName/data', (req, res) => {
  try {
    const { metricName } = req.params;
    const { value, timestamp } = req.body;
    
    if (typeof value !== 'number') {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_VALUE',
          message: 'Value must be a number'
        }
      });
    }
    
    monitoringService.addMetricData(metricName, value, timestamp);
    
    res.status(201).json({
      success: true,
      message: 'Metric data point added successfully'
    });
  } catch (error) {
    logger.error('Error adding metric data:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'ADD_METRIC_FAILED',
        message: 'Failed to add metric data point'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/metrics/{metricName}/forecast:
 *   get:
 *     summary: Get forecast for metric
 *     tags: [Monitoring]
 *     parameters:
 *       - in: path
 *         name: metricName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the metric
 *     responses:
 *       200:
 *         description: Metric forecast
 */
router.get('/metrics/:metricName/forecast', (req, res) => {
  try {
    const { metricName } = req.params;
    
    const forecast = monitoringService.getForecast(metricName);
    
    if (!forecast) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'FORECAST_NOT_FOUND',
          message: 'No forecast available for this metric'
        }
      });
    }
    
    res.json({
      success: true,
      data: {
        metric: metricName,
        forecast
      }
    });
  } catch (error) {
    logger.error('Error getting forecast:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'FORECAST_FAILED',
        message: 'Failed to get forecast'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/anomalies:
 *   get:
 *     summary: Get recent anomalies
 *     tags: [Monitoring]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Maximum number of anomalies to return
 *     responses:
 *       200:
 *         description: Recent anomalies
 */
router.get('/anomalies', (req, res) => {
  try {
    const limit = parseInt(req.query.limit || '50', 10);
    
    const anomalies = monitoringService.anomalyDetectionService.getRecentAnomalies(limit);
    
    res.json({
      success: true,
      data: {
        count: anomalies.length,
        anomalies
      }
    });
  } catch (error) {
    logger.error('Error getting anomalies:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'ANOMALIES_FAILED',
        message: 'Failed to get anomalies'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/alerts:
 *   get:
 *     summary: Get active alerts
 *     tags: [Monitoring]
 *     parameters:
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [info, warning, error, critical]
 *         description: Filter by severity
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [predictive, anomaly, threshold, trend, seasonal]
 *         description: Filter by type
 *       - in: query
 *         name: metric
 *         schema:
 *           type: string
 *         description: Filter by metric name
 *     responses:
 *       200:
 *         description: Active alerts
 */
router.get('/alerts', (req, res) => {
  try {
    const filters = {
      severity: req.query.severity,
      type: req.query.type,
      metric: req.query.metric
    };
    
    // Remove undefined filters
    Object.keys(filters).forEach(key => {
      if (filters[key] === undefined) {
        delete filters[key];
      }
    });
    
    const alerts = monitoringService.alertManager.getActiveAlerts(filters);
    
    res.json({
      success: true,
      data: {
        count: alerts.length,
        alerts
      }
    });
  } catch (error) {
    logger.error('Error getting alerts:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'ALERTS_FAILED',
        message: 'Failed to get alerts'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/alerts/{alertId}/acknowledge:
 *   post:
 *     summary: Acknowledge an alert
 *     tags: [Monitoring]
 *     parameters:
 *       - in: path
 *         name: alertId
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert ID
 *     responses:
 *       200:
 *         description: Alert acknowledged
 */
router.post('/alerts/:alertId/acknowledge', (req, res) => {
  try {
    const { alertId } = req.params;
    const acknowledgedBy = req.user?.id || 'api-user';
    
    const alert = monitoringService.alertManager.acknowledgeAlert(alertId, acknowledgedBy);
    
    res.json({
      success: true,
      data: alert,
      message: 'Alert acknowledged successfully'
    });
  } catch (error) {
    logger.error('Error acknowledging alert:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'ACKNOWLEDGE_FAILED',
        message: 'Failed to acknowledge alert'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/alerts/{alertId}/resolve:
 *   post:
 *     summary: Resolve an alert
 *     tags: [Monitoring]
 *     parameters:
 *       - in: path
 *         name: alertId
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert ID
 *     responses:
 *       200:
 *         description: Alert resolved
 */
router.post('/alerts/:alertId/resolve', (req, res) => {
  try {
    const { alertId } = req.params;
    const resolvedBy = req.user?.id || 'api-user';
    
    const alert = monitoringService.alertManager.resolveAlert(alertId, resolvedBy);
    
    res.json({
      success: true,
      data: alert,
      message: 'Alert resolved successfully'
    });
  } catch (error) {
    logger.error('Error resolving alert:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'RESOLVE_FAILED',
        message: 'Failed to resolve alert'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/test:
 *   post:
 *     summary: Run predictive monitoring tests
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: Test results
 */
router.post('/test', async (req, res) => {
  try {
    // Check if user has admin privileges
    if (!req.user || req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PRIVILEGES',
          message: 'Admin privileges required to run tests'
        }
      });
    }
    
    logger.info('Running predictive monitoring tests');
    
    const results = await runPredictiveMonitoringTests();
    
    res.json({
      success: true,
      data: results,
      message: 'Predictive monitoring tests completed'
    });
  } catch (error) {
    logger.error('Error running monitoring tests:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'TEST_FAILED',
        message: 'Failed to run monitoring tests'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/metrics/{metricName}/config:
 *   post:
 *     summary: Add custom metric configuration
 *     tags: [Monitoring]
 *     parameters:
 *       - in: path
 *         name: metricName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the metric
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - algorithms
 *               - alertTemplate
 *             properties:
 *               algorithms:
 *                 type: array
 *                 items:
 *                   type: string
 *               threshold:
 *                 type: number
 *               alertTemplate:
 *                 type: string
 *               forecastEnabled:
 *                 type: boolean
 *               seasonality:
 *                 type: boolean
 *               seasonalPeriod:
 *                 type: number
 *     responses:
 *       201:
 *         description: Metric configuration added
 */
router.post('/metrics/:metricName/config', (req, res) => {
  try {
    // Check if user has admin privileges
    if (!req.user || req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PRIVILEGES',
          message: 'Admin privileges required to add metric configuration'
        }
      });
    }
    
    const { metricName } = req.params;
    const config = req.body;
    
    // Validate required fields
    if (!config.algorithms || !Array.isArray(config.algorithms)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CONFIG',
          message: 'algorithms field is required and must be an array'
        }
      });
    }
    
    if (!config.alertTemplate) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CONFIG',
          message: 'alertTemplate field is required'
        }
      });
    }
    
    monitoringService.addMetricConfig(metricName, config);
    
    res.status(201).json({
      success: true,
      message: 'Metric configuration added successfully'
    });
  } catch (error) {
    logger.error('Error adding metric configuration:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CONFIG_FAILED',
        message: 'Failed to add metric configuration'
      }
    });
  }
});

/**
 * @swagger
 * /api/monitoring/metrics/{metricName}/config:
 *   delete:
 *     summary: Remove metric configuration
 *     tags: [Monitoring]
 *     parameters:
 *       - in: path
 *         name: metricName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the metric
 *     responses:
 *       200:
 *         description: Metric configuration removed
 */
router.delete('/metrics/:metricName/config', (req, res) => {
  try {
    // Check if user has admin privileges
    if (!req.user || req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PRIVILEGES',
          message: 'Admin privileges required to remove metric configuration'
        }
      });
    }
    
    const { metricName } = req.params;
    
    monitoringService.removeMetricConfig(metricName);
    
    res.json({
      success: true,
      message: 'Metric configuration removed successfully'
    });
  } catch (error) {
    logger.error('Error removing metric configuration:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'REMOVE_CONFIG_FAILED',
        message: 'Failed to remove metric configuration'
      }
    });
  }
});

// Export the monitoring service for use in other modules
router.monitoringService = monitoringService;

module.exports = router;
