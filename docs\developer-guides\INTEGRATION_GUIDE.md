# MVS-VR Integration Guide

This guide provides comprehensive instructions for integrating with the MVS-VR platform. It covers frontend integration, mobile integration, UE plugin integration, webhook integration, and third-party integration.

## Table of Contents

1. [Frontend Integration](#frontend-integration)
2. [Mobile Integration](#mobile-integration)
3. [UE Plugin Integration](#ue-plugin-integration)
4. [Webhook Integration](#webhook-integration)
5. [Third-Party Integration](#third-party-integration)
6. [Authentication Integration](#authentication-integration)
7. [API Integration](#api-integration)
8. [Troubleshooting](#troubleshooting)

## Frontend Integration

### Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Basic knowledge of React, Vue, or Angular

### Integration Steps

1. **Install the MVS-VR Client SDK**

```bash
npm install @mvs-vr/client-sdk
```

2. **Initialize the SDK**

```javascript
// React example
import { MVSClient } from '@mvs-vr/client-sdk';

const client = new MVSClient({
  apiUrl: 'https://api.mvs-vr.com',
  authToken: 'your-auth-token',
});
```

3. **Fetch Assets**

```javascript
// React example
import { useEffect, useState } from 'react';

function AssetList() {
  const [assets, setAssets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchAssets() {
      try {
        const assetList = await client.assets.list();
        setAssets(assetList);
        setLoading(false);
      } catch (err) {
        setError(err.message);
        setLoading(false);
      }
    }

    fetchAssets();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Assets</h2>
      <ul>
        {assets.map(asset => (
          <li key={asset.id}>{asset.name}</li>
        ))}
      </ul>
    </div>
  );
}
```

4. **Display 3D Models**

```javascript
// React example with Three.js
import { useEffect, useRef } from 'react';
import * as THREE from 'three';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

function ModelViewer({ assetId }) {
  const containerRef = useRef();

  useEffect(() => {
    async function loadModel() {
      // Get asset URL
      const assetUrl = await client.assets.getUrl(assetId);
      
      // Set up Three.js scene
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
      const renderer = new THREE.WebGLRenderer();
      renderer.setSize(window.innerWidth, window.innerHeight);
      containerRef.current.appendChild(renderer.domElement);
      
      // Load model
      const loader = new GLTFLoader();
      loader.load(assetUrl, (gltf) => {
        scene.add(gltf.scene);
      });
      
      // Set up camera and lighting
      camera.position.z = 5;
      const light = new THREE.AmbientLight(0xffffff, 0.5);
      scene.add(light);
      
      // Animation loop
      function animate() {
        requestAnimationFrame(animate);
        renderer.render(scene, camera);
      }
      animate();
    }
    
    loadModel();
  }, [assetId]);

  return <div ref={containerRef}></div>;
}
```

### Advanced Frontend Integration

For more advanced frontend integration, you can use the MVS-VR React components:

```bash
npm install @mvs-vr/react-components
```

```javascript
// React example with MVS-VR components
import { MVSProvider, AssetViewer, SceneViewer } from '@mvs-vr/react-components';

function App() {
  return (
    <MVSProvider apiUrl="https://api.mvs-vr.com" authToken="your-auth-token">
      <h1>MVS-VR Demo</h1>
      <AssetViewer assetId="asset-id" />
      <SceneViewer sceneId="scene-id" />
    </MVSProvider>
  );
}
```

## Mobile Integration

### Prerequisites

- React Native (for cross-platform)
- iOS: Xcode, Swift knowledge
- Android: Android Studio, Kotlin knowledge

### React Native Integration

1. **Install the MVS-VR React Native SDK**

```bash
npm install @mvs-vr/react-native-sdk
```

2. **Initialize the SDK**

```javascript
// App.js
import { MVSProvider } from '@mvs-vr/react-native-sdk';

export default function App() {
  return (
    <MVSProvider apiUrl="https://api.mvs-vr.com" authToken="your-auth-token">
      {/* Your app components */}
    </MVSProvider>
  );
}
```

3. **Use MVS-VR Components**

```javascript
// HomeScreen.js
import { AssetViewer, SceneViewer } from '@mvs-vr/react-native-sdk';

export default function HomeScreen() {
  return (
    <View style={styles.container}>
      <AssetViewer assetId="asset-id" style={styles.viewer} />
      <SceneViewer sceneId="scene-id" style={styles.viewer} />
    </View>
  );
}
```

### Native iOS Integration

1. **Install the MVS-VR iOS SDK**

Add the following to your `Podfile`:

```ruby
pod 'MVSVR', '~> 1.0'
```

Then run:

```bash
pod install
```

2. **Initialize the SDK**

```swift
// AppDelegate.swift
import MVSVR

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        MVSClient.shared.configure(apiUrl: "https://api.mvs-vr.com", authToken: "your-auth-token")
        return true
    }
}
```

3. **Use MVS-VR Components**

```swift
// ViewController.swift
import MVSVR

class ViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        
        let assetViewer = MVSAssetViewer(frame: CGRect(x: 0, y: 0, width: 300, height: 300))
        assetViewer.assetId = "asset-id"
        view.addSubview(assetViewer)
    }
}
```

### Native Android Integration

1. **Install the MVS-VR Android SDK**

Add the following to your `build.gradle`:

```gradle
dependencies {
    implementation 'com.mvsvr:sdk:1.0.0'
}
```

2. **Initialize the SDK**

```kotlin
// Application.kt
import com.mvsvr.MVSClient

class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        MVSClient.configure(apiUrl = "https://api.mvs-vr.com", authToken = "your-auth-token")
    }
}
```

3. **Use MVS-VR Components**

```kotlin
// MainActivity.kt
import com.mvsvr.MVSAssetViewer

class MainActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        val assetViewer = findViewById<MVSAssetViewer>(R.id.asset_viewer)
        assetViewer.setAssetId("asset-id")
    }
}
```

## UE Plugin Integration

### Prerequisites

- Unreal Engine 5.1 or later
- C++ knowledge for advanced customization

### Integration Steps

1. **Install the MVS-VR UE Plugin**

   - Download the plugin from the MVS-VR developer portal
   - Extract the plugin to your project's `Plugins` directory
   - Restart the Unreal Editor

2. **Configure the Plugin**

   - Open the MVS-VR Settings panel in the Project Settings
   - Enter your API credentials
   - Configure the cache directory and other settings

3. **Use MVS-VR Components in Blueprints**

   - Drag and drop MVS-VR components from the Components panel
   - Configure the component properties
   - Connect events to your game logic

4. **Load Assets Dynamically**

```cpp
// C++ example
#include "MVSClient.h"
#include "MVSAssetLoader.h"

void AMyActor::LoadAsset(const FString& AssetId)
{
    UMVSAssetLoader* AssetLoader = UMVSClient::Get()->GetAssetLoader();
    AssetLoader->LoadAsset(AssetId, FMVSAssetLoadDelegate::CreateUObject(this, &AMyActor::OnAssetLoaded));
}

void AMyActor::OnAssetLoaded(bool bSuccess, const FMVSAsset& Asset)
{
    if (bSuccess)
    {
        // Use the loaded asset
        UStaticMeshComponent* MeshComponent = GetComponentByClass<UStaticMeshComponent>();
        if (MeshComponent && Asset.Mesh)
        {
            MeshComponent->SetStaticMesh(Asset.Mesh);
        }
    }
}
```

## Webhook Integration

### Prerequisites

- Server capable of receiving HTTP requests
- Basic knowledge of HTTP and JSON

### Integration Steps

1. **Register a Webhook**

```javascript
// Node.js example
const response = await client.webhooks.create({
  url: 'https://your-server.com/webhooks/mvs-vr',
  events: ['asset.created', 'asset.updated', 'scene.published'],
  secret: 'your-webhook-secret',
});

const webhookId = response.id;
```

2. **Verify Webhook Signatures**

```javascript
// Node.js example with Express
const express = require('express');
const crypto = require('crypto');
const bodyParser = require('body-parser');

const app = express();
app.use(bodyParser.json());

app.post('/webhooks/mvs-vr', (req, res) => {
  const signature = req.headers['x-mvs-signature'];
  const timestamp = req.headers['x-mvs-timestamp'];
  const webhookSecret = 'your-webhook-secret';
  
  // Verify signature
  const payload = JSON.stringify(req.body);
  const expectedSignature = crypto
    .createHmac('sha256', webhookSecret)
    .update(`${timestamp}.${payload}`)
    .digest('hex');
  
  if (signature !== expectedSignature) {
    return res.status(401).send('Invalid signature');
  }
  
  // Process webhook event
  const event = req.body;
  console.log(`Received event: ${event.type}`);
  
  // Handle different event types
  switch (event.type) {
    case 'asset.created':
      // Handle asset created event
      break;
    case 'asset.updated':
      // Handle asset updated event
      break;
    case 'scene.published':
      // Handle scene published event
      break;
    default:
      console.log(`Unknown event type: ${event.type}`);
  }
  
  res.status(200).send('Webhook received');
});

app.listen(3000, () => {
  console.log('Webhook server listening on port 3000');
});
```

## Third-Party Integration

The MVS-VR platform supports integration with various third-party services:

### Analytics Integration

```javascript
// Google Analytics example
client.analytics.configure({
  provider: 'google-analytics',
  trackingId: 'UA-XXXXXXXX-X',
});

// Custom analytics event
client.analytics.trackEvent({
  category: 'Asset',
  action: 'View',
  label: 'Product Model',
  value: 1,
});
```

### Payment Gateway Integration

```javascript
// Stripe example
const response = await client.payments.createCheckoutSession({
  provider: 'stripe',
  items: [
    {
      type: 'subscription',
      planId: 'plan_XXXXXXXX',
      quantity: 1,
    },
  ],
  successUrl: 'https://your-site.com/success',
  cancelUrl: 'https://your-site.com/cancel',
});

// Redirect to checkout
window.location.href = response.url;
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Check that your API credentials are correct
   - Ensure your token has not expired
   - Verify that you have the necessary permissions

2. **Asset Loading Errors**
   - Check that the asset ID is correct
   - Verify that the asset is published
   - Check your network connection

3. **Webhook Issues**
   - Ensure your webhook URL is publicly accessible
   - Verify that your webhook secret is correct
   - Check server logs for detailed error messages

### Getting Help

If you encounter issues not covered in this guide, you can:

- Check the [API Reference](./API_REFERENCE.md)
- Visit the [MVS-VR Developer Forum](https://forum.mvs-vr.com)
- Contact <NAME_EMAIL>
