<template>
  <div class="commenting-feedback-system">
    <div class="commenting-header">
      <h2 class="commenting-title">Comments & Feedback</h2>
      <div class="commenting-actions">
        <button
          class="view-mode-button"
          :class="{ active: !isSelectionMode }"
          @click="isSelectionMode = false"
          title="View comments"
        >
          <i class="material-icons">comment</i>
          <span>View</span>
        </button>

        <button
          class="selection-mode-button"
          :class="{ active: isSelectionMode }"
          @click="isSelectionMode = true"
          title="Add comment"
        >
          <i class="material-icons">add_comment</i>
          <span>Add</span>
        </button>
      </div>
    </div>

    <div class="commenting-content">
      <div class="content-preview" :class="{ 'selection-mode': isSelectionMode }">
        <div class="preview-container">
          <div class="preview-overlay" v-if="isSelectionMode">
            <div class="selection-instructions">
              <i class="material-icons">touch_app</i>
              <p>Click on an element to add a comment</p>
            </div>
          </div>

          <div class="preview-frame">
            <div class="preview-content">
              <!-- Mock content for demonstration -->
              <div class="mock-header">
                <div class="mock-logo" data-element-id="logo" @click="handleElementClick">Logo</div>
                <div class="mock-nav" data-element-id="navigation" @click="handleElementClick">
                  <div class="mock-nav-item">Home</div>
                  <div class="mock-nav-item">Products</div>
                  <div class="mock-nav-item">About</div>
                  <div class="mock-nav-item">Contact</div>
                </div>
              </div>

              <div class="mock-hero" data-element-id="hero" @click="handleElementClick">
                <div class="mock-hero-content">
                  <div class="mock-title" data-element-id="hero-title" @click="handleElementClick">Welcome to Our Store</div>
                  <div class="mock-subtitle" data-element-id="hero-subtitle" @click="handleElementClick">Discover amazing products</div>
                  <div class="mock-button" data-element-id="cta-button" @click="handleElementClick">Shop Now</div>
                </div>
                <div class="mock-hero-image" data-element-id="hero-image" @click="handleElementClick">Image</div>
              </div>

              <div class="mock-features" data-element-id="features" @click="handleElementClick">
                <div class="mock-feature" data-element-id="feature-1" @click="handleElementClick">
                  <div class="mock-feature-icon">Icon</div>
                  <div class="mock-feature-title">Feature 1</div>
                  <div class="mock-feature-text">Description text</div>
                </div>
                <div class="mock-feature" data-element-id="feature-2" @click="handleElementClick">
                  <div class="mock-feature-icon">Icon</div>
                  <div class="mock-feature-title">Feature 2</div>
                  <div class="mock-feature-text">Description text</div>
                </div>
                <div class="mock-feature" data-element-id="feature-3" @click="handleElementClick">
                  <div class="mock-feature-icon">Icon</div>
                  <div class="mock-feature-title">Feature 3</div>
                  <div class="mock-feature-text">Description text</div>
                </div>
              </div>
            </div>

            <!-- Comment Indicators -->
            <div
              v-for="comment in activeComments"
              :key="comment.id"
              class="comment-indicator"
              :style="getCommentPosition(comment)"
              @click="selectComment(comment)"
              :class="{ active: selectedComment && selectedComment.id === comment.id }"
            >
              <div class="indicator-dot">{{ comment.replies.length + 1 }}</div>
            </div>
          </div>
        </div>

        <div class="comments-panel">
          <div v-if="!selectedComment && !isSelectionMode" class="empty-state">
            <i class="material-icons">forum</i>
            <p>Select a comment or add a new one</p>
          </div>

          <div v-else-if="isSelectionMode && !selectedElement" class="empty-state">
            <i class="material-icons">touch_app</i>
            <p>Click on an element in the preview to add a comment</p>
          </div>

          <div v-else-if="isSelectionMode && selectedElement" class="comment-composer">
            <div class="composer-header">
              <h3>New Comment</h3>
              <div class="element-info">
                <span class="element-label">Element:</span>
                <span class="element-id">{{ selectedElement }}</span>
              </div>
            </div>

            <div class="composer-content">
              <textarea
                v-model="newCommentText"
                placeholder="Write your comment here..."
                rows="4"
                class="comment-textarea"
              ></textarea>

              <div class="formatting-toolbar">
                <button class="format-button" title="Bold">
                  <i class="material-icons">format_bold</i>
                </button>
                <button class="format-button" title="Italic">
                  <i class="material-icons">format_italic</i>
                </button>
                <button class="format-button" title="Bulleted List">
                  <i class="material-icons">format_list_bulleted</i>
                </button>
                <button class="format-button" title="Numbered List">
                  <i class="material-icons">format_list_numbered</i>
                </button>
                <button class="format-button" title="Add Link">
                  <i class="material-icons">link</i>
                </button>
                <button class="format-button" title="Add Image">
                  <i class="material-icons">image</i>
                </button>
                <button class="format-button" title="Mention">
                  <i class="material-icons">alternate_email</i>
                </button>
              </div>

              <div class="attachment-area">
                <button class="attachment-button">
                  <i class="material-icons">attach_file</i>
                  <span>Add Attachment</span>
                </button>
              </div>
            </div>

            <div class="composer-footer">
              <button class="cancel-button" @click="cancelNewComment">Cancel</button>
              <button
                class="submit-button"
                @click="submitNewComment"
                :disabled="!newCommentText.trim()"
              >
                Submit Comment
              </button>
            </div>
          </div>

          <div v-else-if="selectedComment" class="comment-thread">
            <div class="thread-header">
              <h3>Comment Thread</h3>
              <div class="element-info">
                <span class="element-label">Element:</span>
                <span class="element-id">{{ selectedComment.elementId }}</span>
              </div>
              <button class="close-thread-button" @click="selectedComment = null">
                <i class="material-icons">close</i>
              </button>
            </div>

            <div class="thread-content">
              <!-- Original Comment -->
              <div class="comment-item original">
                <div class="comment-header">
                  <div class="comment-author">
                    <div
                      class="author-avatar"
                      :style="{ backgroundColor: getAvatarColor(selectedComment.author) }"
                    >
                      {{ getInitials(selectedComment.author) }}
                    </div>
                    <div class="author-info">
                      <div class="author-name">{{ selectedComment.author }}</div>
                      <div class="comment-date">{{ formatDate(selectedComment.date) }}</div>
                    </div>
                  </div>

                  <div class="comment-actions">
                    <button class="action-button" title="Edit">
                      <i class="material-icons">edit</i>
                    </button>
                    <button class="action-button" title="Delete">
                      <i class="material-icons">delete</i>
                    </button>
                  </div>
                </div>

                <div class="comment-body">
                  {{ selectedComment.text }}
                </div>

                <div class="comment-footer">
                  <button class="reaction-button">
                    <i class="material-icons">thumb_up</i>
                    <span>{{ selectedComment.likes }}</span>
                  </button>
                </div>
              </div>

              <!-- Replies -->
              <div
                v-for="reply in selectedComment.replies"
                :key="reply.id"
                class="comment-item reply"
              >
                <div class="comment-header">
                  <div class="comment-author">
                    <div
                      class="author-avatar"
                      :style="{ backgroundColor: getAvatarColor(reply.author) }"
                    >
                      {{ getInitials(reply.author) }}
                    </div>
                    <div class="author-info">
                      <div class="author-name">{{ reply.author }}</div>
                      <div class="comment-date">{{ formatDate(reply.date) }}</div>
                    </div>
                  </div>

                  <div class="comment-actions">
                    <button class="action-button" title="Edit">
                      <i class="material-icons">edit</i>
                    </button>
                    <button class="action-button" title="Delete">
                      <i class="material-icons">delete</i>
                    </button>
                  </div>
                </div>

                <div class="comment-body">
                  {{ reply.text }}
                </div>

                <div class="comment-footer">
                  <button class="reaction-button">
                    <i class="material-icons">thumb_up</i>
                    <span>{{ reply.likes }}</span>
                  </button>
                </div>
              </div>
            </div>

            <div class="reply-composer">
              <textarea
                v-model="replyText"
                placeholder="Write a reply..."
                rows="2"
                class="reply-textarea"
              ></textarea>

              <div class="reply-actions">
                <button class="format-button" title="Formatting">
                  <i class="material-icons">text_format</i>
                </button>
                <button class="format-button" title="Attachment">
                  <i class="material-icons">attach_file</i>
                </button>
                <button class="format-button" title="Mention">
                  <i class="material-icons">alternate_email</i>
                </button>
                <button
                  class="reply-button"
                  @click="submitReply"
                  :disabled="!replyText.trim()"
                >
                  Reply
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, reactive } from 'vue';

export default {
  name: 'CommentingFeedbackSystem',

  setup() {
    // Mode state
    const isSelectionMode = ref(false);

    // Selected element for new comment
    const selectedElement = ref(null);

    // New comment text
    const newCommentText = ref('');

    // Reply text
    const replyText = ref('');

    // Selected comment
    const selectedComment = ref(null);

    // Mock comments data
    const comments = reactive([
      {
        id: 1,
        elementId: 'hero-title',
        author: 'John Doe',
        date: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
        text: 'I think we should make the title more impactful. Maybe use a stronger call to action?',
        position: { x: 30, y: 40 },
        status: 'active',
        likes: 3,
        replies: [
          {
            id: 101,
            author: 'Jane Smith',
            date: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
            text: 'I agree. How about "Discover Extraordinary Products Today"?',
            likes: 2
          },
          {
            id: 102,
            author: 'Bob Johnson',
            date: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
            text: 'Good suggestion. We should also consider making it more personal.',
            likes: 1
          }
        ]
      },
      {
        id: 2,
        elementId: 'cta-button',
        author: 'Alice Williams',
        date: new Date(Date.now() - 1000 * 60 * 60 * 36), // 36 hours ago
        text: 'The button color doesn\'t stand out enough. Can we use a brighter color?',
        position: { x: 50, y: 60 },
        status: 'active',
        likes: 5,
        replies: [
          {
            id: 201,
            author: 'John Doe',
            date: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
            text: 'How about using our brand\'s primary color?',
            likes: 3
          }
        ]
      },
      {
        id: 3,
        elementId: 'feature-2',
        author: 'Bob Johnson',
        date: new Date(Date.now() - 1000 * 60 * 60 * 48), // 48 hours ago
        text: 'This feature description is too vague. We should be more specific about the benefits.',
        position: { x: 70, y: 80 },
        status: 'resolved',
        likes: 2,
        replies: []
      }
    ]);

    // Active (unresolved) comments
    const activeComments = computed(() => {
      return comments.filter(comment => comment.status === 'active');
    });

    // Methods
    const handleElementClick = (event) => {
      if (!isSelectionMode.value) return;

      // Prevent event bubbling
      event.stopPropagation();

      // Get element ID
      const elementId = event.currentTarget.dataset.elementId;
      selectedElement.value = elementId;

      // Calculate position for the new comment
      const rect = event.currentTarget.getBoundingClientRect();
      const containerRect = document.querySelector('.preview-content').getBoundingClientRect();

      // Store position relative to the container
      const x = ((rect.left + rect.width / 2) - containerRect.left) / containerRect.width * 100;
      const y = ((rect.top + rect.height / 2) - containerRect.top) / containerRect.height * 100;

      // Store position for the new comment
      newCommentPosition.value = { x, y };
    };

    // New comment position
    const newCommentPosition = ref({ x: 0, y: 0 });

    const submitNewComment = () => {
      // Create new comment
      const newComment = {
        id: Date.now(),
        elementId: selectedElement.value,
        author: 'Current User', // In a real app, this would be the current user
        date: new Date(),
        text: newCommentText.value,
        position: newCommentPosition.value,
        status: 'active',
        likes: 0,
        replies: []
      };

      // Add to comments
      comments.push(newComment);

      // Select the new comment
      selectedComment.value = newComment;

      // Reset state
      newCommentText.value = '';
      selectedElement.value = null;
      isSelectionMode.value = false;
    };

    const cancelNewComment = () => {
      // Reset state
      newCommentText.value = '';
      selectedElement.value = null;
    };

    const selectComment = (comment) => {
      selectedComment.value = comment;
      isSelectionMode.value = false;
    };

    const submitReply = () => {
      if (!selectedComment.value || !replyText.value.trim()) return;

      // Create new reply
      const newReply = {
        id: Date.now(),
        author: 'Current User', // In a real app, this would be the current user
        date: new Date(),
        text: replyText.value,
        likes: 0
      };

      // Add to selected comment replies
      selectedComment.value.replies.push(newReply);

      // Reset reply text
      replyText.value = '';
    };

    const getCommentPosition = (comment) => {
      return {
        left: `${comment.position.x}%`,
        top: `${comment.position.y}%`
      };
    };

    const getInitials = (name) => {
      return name
        .split(' ')
        .map(part => part.charAt(0))
        .join('')
        .toUpperCase()
        .substring(0, 2);
    };

    const getAvatarColor = (name) => {
      // Generate a deterministic color based on the name
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      const hue = hash % 360;
      return `hsl(${hue}, 70%, 60%)`;
    };

    const formatDate = (date) => {
      const now = new Date();
      const diff = now - date;

      // Less than a minute
      if (diff < 60 * 1000) {
        return 'Just now';
      }

      // Less than an hour
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
      }

      // Less than a day
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
      }

      // Less than a week
      if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days} day${days !== 1 ? 's' : ''} ago`;
      }

      // Format as date
      return date.toLocaleDateString();
    };

    return {
      isSelectionMode,
      selectedElement,
      newCommentText,
      replyText,
      selectedComment,
      comments,
      activeComments,
      newCommentPosition,
      handleElementClick,
      submitNewComment,
      cancelNewComment,
      selectComment,
      submitReply,
      getCommentPosition,
      getInitials,
      getAvatarColor,
      formatDate
    };
  }
};
</script>

<style scoped>
.commenting-feedback-system {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
}

.commenting-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.commenting-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.commenting-actions {
  display: flex;
  gap: 8px;
}

.view-mode-button,
.selection-mode-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.view-mode-button:hover,
.selection-mode-button:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.view-mode-button.active,
.selection-mode-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border-color: var(--theme--primary);
}

.commenting-content {
  flex: 1;
  padding: 24px;
  overflow: hidden;
}

.content-preview {
  display: flex;
  height: 100%;
  gap: 24px;
}

.preview-container {
  flex: 1;
  position: relative;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selection-instructions {
  background-color: var(--theme--background);
  padding: 16px 24px;
  border-radius: var(--theme--border-radius);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.selection-instructions i {
  font-size: 32px;
  color: var(--theme--primary);
}

.preview-frame {
  position: relative;
  height: 100%;
  overflow: auto;
}

.preview-content {
  position: relative;
  min-height: 100%;
}

.comments-panel {
  width: 350px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
}

/* Comment Indicator */
.comment-indicator {
  position: absolute;
  transform: translate(-50%, -50%);
  z-index: 2;
  cursor: pointer;
}

.indicator-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s;
}

.comment-indicator:hover .indicator-dot {
  transform: scale(1.1);
}

.comment-indicator.active .indicator-dot {
  background-color: var(--theme--danger);
}

/* Comment Composer */
.comment-composer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.composer-header {
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.composer-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.element-info {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.element-label {
  font-weight: 500;
  margin-right: 4px;
}

.element-id {
  font-family: monospace;
  background-color: var(--theme--background-subdued);
  padding: 2px 4px;
  border-radius: 4px;
}

.composer-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comment-textarea {
  width: 100%;
  padding: 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  resize: none;
}

.formatting-toolbar {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.format-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.format-button:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.attachment-area {
  margin-top: 8px;
}

.attachment-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: var(--theme--border-radius);
  border: 1px dashed var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  width: 100%;
  justify-content: center;
}

.attachment-button:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.composer-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
}

.cancel-button,
.submit-button {
  padding: 8px 16px;
  border-radius: var(--theme--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  border: 1px solid var(--theme--border-color);
}

.cancel-button:hover {
  background-color: var(--theme--background-subdued);
}

.submit-button {
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border: none;
}

.submit-button:hover:not(:disabled) {
  background-color: var(--theme--primary-dark);
}

.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Comment Thread */
.comment-thread {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.thread-header {
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.thread-header h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
}

.close-thread-button {
  background: none;
  border: none;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thread-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comment-item {
  background-color: var(--theme--background-subdued);
  border-radius: var(--theme--border-radius);
  padding: 12px;
}

.comment-item.original {
  border-left: 4px solid var(--theme--primary);
}

.comment-item.reply {
  margin-left: 16px;
  border-left: 4px solid var(--theme--background-subdued);
}

.comment-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 8px;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.author-info {
  display: flex;
  flex-direction: column;
}

.author-name {
  font-weight: 500;
}

.comment-date {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.comment-actions {
  display: flex;
  gap: 4px;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: var(--theme--border-radius);
  border: none;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.action-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--theme--foreground);
}

.comment-body {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.comment-footer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reaction-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  font-size: 12px;
  cursor: pointer;
}

.reaction-button:hover {
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
}

.reaction-button i {
  font-size: 14px;
}

.reply-composer {
  padding: 16px;
  border-top: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
}

.reply-textarea {
  width: 100%;
  padding: 12px;
  border-radius: var(--theme--border-radius);
  border: 1px solid var(--theme--border-color);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
  resize: none;
  margin-bottom: 8px;
}

.reply-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reply-button {
  padding: 6px 12px;
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--primary);
  color: var(--theme--primary-foreground);
  border: none;
  font-weight: 500;
  cursor: pointer;
}

.reply-button:hover:not(:disabled) {
  background-color: var(--theme--primary-dark);
}

.reply-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Mock Content Styles */
.mock-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
}

.mock-logo {
  width: 100px;
  height: 40px;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
}

.mock-nav {
  display: flex;
  gap: 16px;
  cursor: pointer;
}

.mock-nav-item {
  padding: 8px 12px;
  background-color: #e0e0e0;
  border-radius: 4px;
}

.mock-hero {
  display: flex;
  padding: 48px 24px;
  background-color: #f0f0f0;
  gap: 24px;
  cursor: pointer;
}

.mock-hero-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mock-title {
  font-size: 24px;
  font-weight: 600;
  background-color: #e0e0e0;
  padding: 12px;
  border-radius: 4px;
  width: 80%;
  cursor: pointer;
}

.mock-subtitle {
  font-size: 16px;
  background-color: #e0e0e0;
  padding: 8px;
  border-radius: 4px;
  width: 60%;
  cursor: pointer;
}

.mock-button {
  width: 120px;
  padding: 12px 24px;
  background-color: #2196f3;
  color: white;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
}

.mock-hero-image {
  width: 300px;
  height: 200px;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
}

.mock-features {
  display: flex;
  padding: 48px 24px;
  gap: 24px;
  background-color: white;
  cursor: pointer;
}

.mock-feature {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px;
  background-color: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
}

.mock-feature-icon {
  width: 64px;
  height: 64px;
  background-color: #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mock-feature-title {
  font-weight: 600;
}

.mock-feature-text {
  text-align: center;
  color: #757575;
}
</style>
