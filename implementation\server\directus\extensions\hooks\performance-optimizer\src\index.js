/**
 * Performance Optimizer Hook for Directus
 *
 * This hook implements various performance optimizations for Directus:
 * 1. Query caching with Redis or in-memory LRU cache
 * 2. Response compression
 * 3. Database query optimization
 * 4. Asset optimization
 */

const Redis = require('ioredis');
const { LRUCache } = require('lru-cache');
const crypto = require('crypto');

module.exports = function registerHook({ env, logger, database, getSchema }) {
  // Configuration
  const config = {
    // Cache settings
    cache: {
      enabled: env.CACHE_ENABLED !== 'false',
      store: env.CACHE_STORE || 'memory', // 'memory' or 'redis'
      ttl: parseInt(env.CACHE_TTL || '300', 10), // 5 minutes in seconds
      maxItems: parseInt(env.CACHE_MAX_ITEMS || '1000', 10),
      collections: (
        env.CACHE_COLLECTIONS || 'showroom_layouts,product_displays,vendor_branding'
      ).split(','),
      excludedPaths: (env.CACHE_EXCLUDED_PATHS || '/auth,/users/me').split(','),
    },

    // Query optimization settings
    queryOptimization: {
      enabled: env.QUERY_OPTIMIZATION_ENABLED !== 'false',
      maxLimit: parseInt(env.QUERY_MAX_LIMIT || '100', 10),
      defaultFields: env.QUERY_DEFAULT_FIELDS || 'id,name,status',
    },

    // Asset optimization settings
    assetOptimization: {
      enabled: env.ASSET_OPTIMIZATION_ENABLED !== 'false',
      imageCompression: env.ASSET_IMAGE_COMPRESSION !== 'false',
      cacheControl: env.ASSET_CACHE_CONTROL || 'public, max-age=86400',
    },
  };

  // Initialize cache
  let cache;

  if (config.cache.enabled) {
    if (config.cache.store === 'redis') {
      const redisUrl = env.REDIS_URL || 'redis://localhost:6379';
      cache = new Redis(redisUrl);
      logger.info(`Performance optimizer: Redis cache initialized at ${redisUrl}`);
    } else {
      cache = new LRUCache({
        max: config.cache.maxItems,
        ttl: config.cache.ttl * 1000, // Convert to milliseconds
      });
      logger.info(
        `Performance optimizer: In-memory LRU cache initialized with ${config.cache.maxItems} max items`,
      );
    }
  }

  // Helper function to generate cache key
  function generateCacheKey(method, path, query) {
    const data = `${method}:${path}:${JSON.stringify(query || {})}`;
    return crypto.createHash('md5').update(data).digest('hex');
  }

  // Helper function to check if path should be cached
  function shouldCache(method, path, collection) {
    // Only cache GET requests
    if (method !== 'GET') return false;

    // Check if path is excluded
    if (config.cache.excludedPaths.some(excluded => path.startsWith(excluded))) {
      return false;
    }

    // Check if collection is included
    return config.cache.collections.includes(collection);
  }

  // Helper function to get cache value
  async function getCacheValue(key) {
    try {
      if (config.cache.store === 'redis') {
        const value = await cache.get(key);
        return value ? JSON.parse(value) : null;
      } else {
        return cache.get(key);
      }
    } catch (error) {
      logger.error(`Performance optimizer: Error getting cache value: ${error.message}`);
      return null;
    }
  }

  // Helper function to set cache value
  async function setCacheValue(key, value) {
    try {
      if (config.cache.store === 'redis') {
        await cache.set(key, JSON.stringify(value), 'EX', config.cache.ttl);
      } else {
        cache.set(key, value);
      }
    } catch (error) {
      logger.error(`Performance optimizer: Error setting cache value: ${error.message}`);
    }
  }

  // Helper function to invalidate cache for a collection
  async function invalidateCollectionCache(collection) {
    try {
      if (config.cache.store === 'redis') {
        const keys = await cache.keys(`*:items/${collection}:*`);
        if (keys.length > 0) {
          await cache.del(...keys);
          logger.info(
            `Performance optimizer: Invalidated ${keys.length} cache entries for collection ${collection}`,
          );
        }
      } else {
        // For LRU cache, we need to iterate through all keys and check if they match
        const keysToDelete = [];
        for (const key of cache.keys()) {
          if (key.includes(`:items/${collection}:`)) {
            keysToDelete.push(key);
          }
        }

        keysToDelete.forEach(key => cache.delete(key));
        logger.info(
          `Performance optimizer: Invalidated ${keysToDelete.length} cache entries for collection ${collection}`,
        );
      }
    } catch (error) {
      logger.error(`Performance optimizer: Error invalidating collection cache: ${error.message}`);
    }
  }

  // Helper function to optimize query
  function optimizeQuery(collection, query) {
    if (!config.queryOptimization.enabled) return query;

    const optimizedQuery = { ...query };

    // Limit the number of items returned
    if (optimizedQuery.limit && optimizedQuery.limit > config.queryOptimization.maxLimit) {
      optimizedQuery.limit = config.queryOptimization.maxLimit;
    }

    // Set default fields if not specified
    if (!optimizedQuery.fields && config.queryOptimization.defaultFields) {
      optimizedQuery.fields = config.queryOptimization.defaultFields.split(',');
    }

    return optimizedQuery;
  }

  // Register hooks
  return {
    // Hook into the request to implement caching
    request: async (request, response, context) => {
      if (!config.cache.enabled) return;

      const { method, path } = request;

      // Check if this is a request for items in a collection
      const match = path.match(/^\/items\/([^\/]+)/);
      if (!match) return;

      const collection = match[1];

      // Check if we should cache this request
      if (!shouldCache(method, path, collection)) return;

      // Generate cache key
      const cacheKey = generateCacheKey(method, path, request.query);

      // Try to get from cache
      const cachedValue = await getCacheValue(cacheKey);

      if (cachedValue) {
        // Return cached value
        response.setHeader('X-Cache', 'HIT');
        response.send(cachedValue);
        return true; // Skip further processing
      }

      // Mark as cache miss
      response.setHeader('X-Cache', 'MISS');

      // Optimize query
      request.query = optimizeQuery(collection, request.query);
    },

    // Hook into the response to cache the result
    response: async (request, response, context) => {
      if (!config.cache.enabled) return;

      const { method, path } = request;

      // Check if this is a request for items in a collection
      const match = path.match(/^\/items\/([^\/]+)/);
      if (!match) return;

      const collection = match[1];

      // Check if we should cache this request
      if (!shouldCache(method, path, collection)) return;

      // Only cache successful responses
      if (response.statusCode !== 200) return;

      // Generate cache key
      const cacheKey = generateCacheKey(method, path, request.query);

      // Cache the response
      await setCacheValue(cacheKey, response.body);
    },

    // Hook into item creation to invalidate cache
    'items.create': async (payload, meta) => {
      if (!config.cache.enabled) return;

      await invalidateCollectionCache(meta.collection);
    },

    // Hook into item update to invalidate cache
    'items.update': async (payload, meta) => {
      if (!config.cache.enabled) return;

      await invalidateCollectionCache(meta.collection);
    },

    // Hook into item deletion to invalidate cache
    'items.delete': async (payload, meta) => {
      if (!config.cache.enabled) return;

      await invalidateCollectionCache(meta.collection);
    },

    // Hook into asset requests to optimize assets
    'assets.create': async (payload, meta) => {
      if (!config.assetOptimization.enabled) return;

      // Set cache control header for assets
      if (meta.response) {
        meta.response.setHeader('Cache-Control', config.assetOptimization.cacheControl);
      }
    },

    // Hook into server start to log configuration
    'server.start': async () => {
      logger.info('Performance optimizer: Hook initialized');
      logger.info(`Performance optimizer: Cache ${config.cache.enabled ? 'enabled' : 'disabled'}`);
      logger.info(
        `Performance optimizer: Query optimization ${config.queryOptimization.enabled ? 'enabled' : 'disabled'}`,
      );
      logger.info(
        `Performance optimizer: Asset optimization ${config.assetOptimization.enabled ? 'enabled' : 'disabled'}`,
      );
    },
  };
};
