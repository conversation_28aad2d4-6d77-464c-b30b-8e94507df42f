/**
 * MVS Metrics SDK for React Native
 * 
 * This SDK collects and sends metrics from mobile applications to the monitoring service.
 */

import { Platform, Dimensions, NativeModules, NativeEventEmitter } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import NetInfo from '@react-native-community/netinfo';
import { PerformanceObserver, performance } from 'react-native-performance';

// Default configuration
const DEFAULT_CONFIG = {
  apiUrl: 'https://api.mvs-vr.com',
  metricsEndpoint: '/api/metrics',
  appId: 'mvs-vr-mobile',
  collectionInterval: 60000, // 1 minute
  enableDetailedMetrics: false,
  enableAutomaticCollection: true,
  enableAutomaticSubmission: true,
  submissionInterval: 300000, // 5 minutes
  maxBatchSize: 100,
  maxStorageSize: 5 * 1024 * 1024, // 5 MB
  persistMetrics: true,
  enableNetworkMonitoring: true,
  enablePerformanceMonitoring: true,
  enableErrorTracking: true,
  enableSessionTracking: true,
  enableARTracking: false,
  samplingRate: 1.0, // 100%
};

class MVSMetricsSDK {
  constructor() {
    this.config = { ...DEFAULT_CONFIG };
    this.initialized = false;
    this.metricsQueue = [];
    this.collectionTimer = null;
    this.submissionTimer = null;
    this.sessionStartTime = null;
    this.lastCollectionTime = 0;
    this.lastSubmissionTime = 0;
    this.networkType = 'unknown';
    this.observers = [];
    
    // Metrics storage
    this.performanceMetrics = {
      appStartTime: 0,
      frameRate: 0,
      memoryUsage: 0,
      batteryLevel: 0,
    };
    
    this.networkMetrics = {
      latencies: {},
      bandwidth: {
        download: 0,
        upload: 0,
        networkType: 'unknown',
      },
    };
    
    this.apiMetrics = {
      requests: [],
    };
    
    this.assetLoadingMetrics = {
      loadTimes: {},
      cacheHitRates: {},
    };
    
    this.interactionMetrics = {
      screenLoadTimes: {},
      latencies: {},
      screenViews: {},
    };
    
    this.sessionMetrics = {
      duration: 0,
    };
    
    this.arMetrics = {
      sessionDuration: 0,
      trackingQuality: 0,
    };
    
    this.errorMetrics = {
      errors: {},
      crashes: 0,
      anr: 0,
    };
  }
  
  /**
   * Initialize the SDK
   * 
   * @param {Object} config - Configuration options
   * @returns {Promise<void>}
   */
  async initialize(config = {}) {
    try {
      // Merge configuration
      this.config = { ...this.config, ...config };
      
      // Get device information
      this.deviceType = Platform.OS;
      this.osVersion = Platform.Version.toString();
      this.appVersion = await DeviceInfo.getVersion();
      
      // Start session tracking
      if (this.config.enableSessionTracking) {
        this.startSession();
      }
      
      // Start network monitoring
      if (this.config.enableNetworkMonitoring) {
        this.startNetworkMonitoring();
      }
      
      // Start performance monitoring
      if (this.config.enablePerformanceMonitoring) {
        this.startPerformanceMonitoring();
      }
      
      // Start error tracking
      if (this.config.enableErrorTracking) {
        this.startErrorTracking();
      }
      
      // Start AR tracking if enabled
      if (this.config.enableARTracking) {
        this.startARTracking();
      }
      
      // Start automatic collection if enabled
      if (this.config.enableAutomaticCollection) {
        this.startAutomaticCollection();
      }
      
      // Start automatic submission if enabled
      if (this.config.enableAutomaticSubmission) {
        this.startAutomaticSubmission();
      }
      
      // Record app start time
      this.recordAppStartTime();
      
      this.initialized = true;
      console.log('MVS Metrics SDK initialized');
    } catch (error) {
      console.error('Error initializing MVS Metrics SDK', error);
    }
  }
  
  /**
   * Start session tracking
   */
  startSession() {
    this.sessionStartTime = Date.now();
    
    // Track app state changes
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }
  
  /**
   * Handle app state change
   * 
   * @param {string} nextAppState - Next app state
   */
  handleAppStateChange = (nextAppState) => {
    if (nextAppState === 'active' && !this.sessionStartTime) {
      // App came to foreground, start new session
      this.sessionStartTime = Date.now();
    } else if (nextAppState === 'background' && this.sessionStartTime) {
      // App went to background, record session duration
      const sessionDuration = (Date.now() - this.sessionStartTime) / 1000; // in seconds
      this.sessionMetrics.duration = sessionDuration;
      
      // Submit metrics when app goes to background
      this.submitMetrics();
      
      this.sessionStartTime = null;
    }
  };
  
  /**
   * Start network monitoring
   */
  startNetworkMonitoring() {
    // Monitor network state
    this.netInfoSubscription = NetInfo.addEventListener(state => {
      this.networkType = state.type;
      this.networkMetrics.bandwidth.networkType = state.type;
    });
  }
  
  /**
   * Start performance monitoring
   */
  startPerformanceMonitoring() {
    // Create performance observer
    const performanceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach(entry => {
        if (entry.entryType === 'navigation') {
          // Record app start time
          this.performanceMetrics.appStartTime = entry.duration;
        } else if (entry.entryType === 'resource') {
          // Record API request
          if (entry.initiatorType === 'fetch' || entry.initiatorType === 'xmlhttprequest') {
            this.recordApiRequest({
              endpoint: entry.name,
              method: 'GET', // We can't know the method from PerformanceEntry
              responseTime: entry.duration,
              status: 200, // We can't know the status from PerformanceEntry
            });
          }
        } else if (entry.entryType === 'mark' && entry.name.startsWith('screen_load_')) {
          // Record screen load time
          const screenName = entry.name.replace('screen_load_', '');
          this.recordScreenLoadTime(screenName, entry.duration);
        }
      });
    });
    
    // Observe performance entries
    performanceObserver.observe({ entryTypes: ['navigation', 'resource', 'mark'] });
    
    this.observers.push(performanceObserver);
    
    // Monitor memory usage
    if (Platform.OS === 'android' && NativeModules.PerformanceMonitor) {
      const performanceEmitter = new NativeEventEmitter(NativeModules.PerformanceMonitor);
      
      this.memorySubscription = performanceEmitter.addListener('memoryUsage', (data) => {
        this.performanceMetrics.memoryUsage = data.usedMemory / 1024 / 1024; // Convert to MB
      });
      
      this.frameRateSubscription = performanceEmitter.addListener('frameRate', (data) => {
        this.performanceMetrics.frameRate = data.fps;
      });
      
      // Start native monitoring
      NativeModules.PerformanceMonitor.startMonitoring();
    }
    
    // Monitor battery level
    DeviceInfo.getBatteryLevel().then(batteryLevel => {
      this.performanceMetrics.batteryLevel = batteryLevel * 100; // Convert to percentage
    });
  }
  
  /**
   * Start error tracking
   */
  startErrorTracking() {
    // Override global error handler
    const originalErrorHandler = ErrorUtils.getGlobalHandler();
    
    ErrorUtils.setGlobalHandler((error, isFatal) => {
      // Record error
      this.recordError('javascript', 'app', isFatal ? 'crash' : 'error');
      
      // Call original error handler
      originalErrorHandler(error, isFatal);
    });
  }
  
  /**
   * Start AR tracking
   */
  startARTracking() {
    // This would be implemented to track AR session metrics
    // For now, just set up placeholders
    
    this.arSessionStartTime = null;
    
    // Mock AR session start/end events
    if (NativeModules.ARTracker) {
      const arEmitter = new NativeEventEmitter(NativeModules.ARTracker);
      
      this.arSessionStartSubscription = arEmitter.addListener('arSessionStart', () => {
        this.arSessionStartTime = Date.now();
      });
      
      this.arSessionEndSubscription = arEmitter.addListener('arSessionEnd', () => {
        if (this.arSessionStartTime) {
          const sessionDuration = (Date.now() - this.arSessionStartTime) / 1000; // in seconds
          this.arMetrics.sessionDuration = sessionDuration;
          this.arSessionStartTime = null;
        }
      });
      
      this.arTrackingQualitySubscription = arEmitter.addListener('arTrackingQuality', (data) => {
        this.arMetrics.trackingQuality = data.quality;
      });
    }
  }
  
  /**
   * Start automatic collection
   */
  startAutomaticCollection() {
    this.collectionTimer = setInterval(() => {
      this.collectMetrics();
    }, this.config.collectionInterval);
  }
  
  /**
   * Start automatic submission
   */
  startAutomaticSubmission() {
    this.submissionTimer = setInterval(() => {
      this.submitMetrics();
    }, this.config.submissionInterval);
  }
  
  /**
   * Record app start time
   */
  recordAppStartTime() {
    // This would be implemented to record app start time
    // For now, just use a placeholder value
    this.performanceMetrics.appStartTime = 500; // 500ms
  }
  
  /**
   * Collect metrics
   */
  collectMetrics() {
    try {
      // Update collection time
      this.lastCollectionTime = Date.now();
      
      // Create metrics payload
      const metricsPayload = {
        appId: this.config.appId,
        deviceType: this.deviceType,
        osVersion: this.osVersion,
        appVersion: this.appVersion,
        timestamp: new Date().toISOString(),
        metrics: {
          performance: { ...this.performanceMetrics },
          network: { ...this.networkMetrics },
          api: { requests: [...this.apiMetrics.requests] },
          assetLoading: { ...this.assetLoadingMetrics },
          interactions: { ...this.interactionMetrics },
          session: { ...this.sessionMetrics },
          errors: { ...this.errorMetrics },
        },
      };
      
      // Add AR metrics if enabled
      if (this.config.enableARTracking) {
        metricsPayload.metrics.ar = { ...this.arMetrics };
      }
      
      // Add to queue
      this.metricsQueue.push(metricsPayload);
      
      // Reset metrics
      this.resetMetrics();
      
      console.log('Metrics collected');
    } catch (error) {
      console.error('Error collecting metrics', error);
    }
  }
  
  /**
   * Submit metrics
   * 
   * @returns {Promise<void>}
   */
  async submitMetrics() {
    try {
      if (this.metricsQueue.length === 0) {
        return;
      }
      
      // Update submission time
      this.lastSubmissionTime = Date.now();
      
      // Get metrics to submit
      const metricsToSubmit = [...this.metricsQueue];
      this.metricsQueue = [];
      
      // Submit metrics
      const response = await fetch(`${this.config.apiUrl}${this.config.metricsEndpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(metricsToSubmit[0]), // Submit one at a time for now
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      
      console.log('Metrics submitted');
    } catch (error) {
      console.error('Error submitting metrics', error);
      
      // Put metrics back in queue
      this.metricsQueue = [...this.metricsQueue, ...metricsToSubmit];
    }
  }
  
  /**
   * Reset metrics
   */
  resetMetrics() {
    // Reset API metrics
    this.apiMetrics.requests = [];
    
    // Reset asset loading metrics
    this.assetLoadingMetrics.loadTimes = {};
    this.assetLoadingMetrics.cacheHitRates = {};
    
    // Reset interaction metrics
    this.interactionMetrics.screenLoadTimes = {};
    this.interactionMetrics.latencies = {};
    this.interactionMetrics.screenViews = {};
    
    // Reset error metrics
    this.errorMetrics.errors = {};
    this.errorMetrics.crashes = 0;
    this.errorMetrics.anr = 0;
  }
  
  /**
   * Record API request
   * 
   * @param {Object} request - API request
   */
  recordApiRequest(request) {
    this.apiMetrics.requests.push(request);
  }
  
  /**
   * Record screen load time
   * 
   * @param {string} screenName - Screen name
   * @param {number} loadTime - Load time in milliseconds
   */
  recordScreenLoadTime(screenName, loadTime) {
    this.interactionMetrics.screenLoadTimes[screenName] = loadTime;
    
    // Increment screen view count
    if (!this.interactionMetrics.screenViews[screenName]) {
      this.interactionMetrics.screenViews[screenName] = 0;
    }
    
    this.interactionMetrics.screenViews[screenName]++;
  }
  
  /**
   * Record interaction latency
   * 
   * @param {string} interactionType - Interaction type
   * @param {number} latency - Latency in milliseconds
   */
  recordInteractionLatency(interactionType, latency) {
    this.interactionMetrics.latencies[interactionType] = latency;
  }
  
  /**
   * Record asset load time
   * 
   * @param {string} assetType - Asset type
   * @param {number} loadTime - Load time in seconds
   */
  recordAssetLoadTime(assetType, loadTime) {
    this.assetLoadingMetrics.loadTimes[assetType] = loadTime;
  }
  
  /**
   * Record asset cache hit rate
   * 
   * @param {string} assetType - Asset type
   * @param {number} hitRate - Hit rate in percent
   */
  recordAssetCacheHitRate(assetType, hitRate) {
    this.assetLoadingMetrics.cacheHitRates[assetType] = hitRate;
  }
  
  /**
   * Record error
   * 
   * @param {string} errorType - Error type
   * @param {string} component - Component
   * @param {string} severity - Severity
   */
  recordError(errorType, component, severity = 'error') {
    if (!this.errorMetrics.errors[errorType]) {
      this.errorMetrics.errors[errorType] = {};
    }
    
    if (!this.errorMetrics.errors[errorType][component]) {
      this.errorMetrics.errors[errorType][component] = 0;
    }
    
    this.errorMetrics.errors[errorType][component]++;
    
    // Increment crash count if severity is crash
    if (severity === 'crash') {
      this.errorMetrics.crashes++;
    }
    
    // Increment ANR count if severity is anr
    if (severity === 'anr') {
      this.errorMetrics.anr++;
    }
  }
  
  /**
   * Cleanup
   */
  cleanup() {
    // Stop timers
    if (this.collectionTimer) {
      clearInterval(this.collectionTimer);
    }
    
    if (this.submissionTimer) {
      clearInterval(this.submissionTimer);
    }
    
    // Remove event listeners
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    
    if (this.netInfoSubscription) {
      this.netInfoSubscription();
    }
    
    if (this.memorySubscription) {
      this.memorySubscription.remove();
    }
    
    if (this.frameRateSubscription) {
      this.frameRateSubscription.remove();
    }
    
    if (this.arSessionStartSubscription) {
      this.arSessionStartSubscription.remove();
    }
    
    if (this.arSessionEndSubscription) {
      this.arSessionEndSubscription.remove();
    }
    
    if (this.arTrackingQualitySubscription) {
      this.arTrackingQualitySubscription.remove();
    }
    
    // Remove observers
    this.observers.forEach(observer => {
      observer.disconnect();
    });
    
    // Submit any remaining metrics
    this.submitMetrics();
    
    this.initialized = false;
  }
}

// Create singleton instance
const instance = new MVSMetricsSDK();

export default instance;
