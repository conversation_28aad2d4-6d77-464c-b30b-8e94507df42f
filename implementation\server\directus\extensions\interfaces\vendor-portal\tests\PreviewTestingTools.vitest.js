/**
 * Vitest tests for PreviewTestingTools component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createWrapper } from '../src/test-utils/component-test-utils.js';

// Create a mock PreviewTestingTools component
const PreviewTestingToolsMock = {
  name: 'PreviewTestingTools',
  props: {
    previewUrl: {
      type: String,
      required: true,
    },
    deviceType: {
      type: String,
      default: 'desktop',
    },
  },
  data() {
    return {
      activeTab: 'performance',
      performanceMetrics: {
        loadTime: 0,
        renderTime: 0,
        interactionTime: 0,
        memoryUsage: 0,
      },
      accessibilityIssues: [],
      compatibilityStatus: {
        browsers: {
          chrome: 'compatible',
          firefox: 'compatible',
          safari: 'compatible',
          edge: 'compatible',
        },
        devices: {
          desktop: 'compatible',
          tablet: 'compatible',
          mobile: 'compatible',
        },
      },
      isRunningTest: false,
    };
  },
  methods: {
    runPerformanceTest() {
      this.isRunningTest = true;
      
      // Simulate a performance test
      setTimeout(() => {
        this.performanceMetrics = {
          loadTime: Math.random() * 1000,
          renderTime: Math.random() * 500,
          interactionTime: Math.random() * 200,
          memoryUsage: Math.random() * 100,
        };
        
        this.isRunningTest = false;
        this.$emit('test-complete', 'performance', this.performanceMetrics);
      }, 500);
    },
    
    runAccessibilityTest() {
      this.isRunningTest = true;
      
      // Simulate an accessibility test
      setTimeout(() => {
        this.accessibilityIssues = [
          { severity: 'critical', message: 'Missing alt text on images', count: 2 },
          { severity: 'warning', message: 'Low contrast text', count: 3 },
        ];
        
        this.isRunningTest = false;
        this.$emit('test-complete', 'accessibility', this.accessibilityIssues);
      }, 500);
    },
    
    runCompatibilityTest() {
      this.isRunningTest = true;
      
      // Simulate a compatibility test
      setTimeout(() => {
        this.compatibilityStatus = {
          browsers: {
            chrome: 'compatible',
            firefox: 'compatible',
            safari: 'compatible',
            edge: 'compatible',
          },
          devices: {
            desktop: 'compatible',
            tablet: 'compatible',
            mobile: 'compatible',
          },
        };
        
        this.isRunningTest = false;
        this.$emit('test-complete', 'compatibility', this.compatibilityStatus);
      }, 500);
    },
    
    setActiveTab(tab) {
      this.activeTab = tab;
    },
  },
  template: `
    <div class="preview-testing-tools">
      <div class="tabs">
        <button class="tab-button" :class="{ active: activeTab === 'performance' }" @click="setActiveTab('performance')">Performance</button>
        <button class="tab-button" :class="{ active: activeTab === 'accessibility' }" @click="setActiveTab('accessibility')">Accessibility</button>
        <button class="tab-button" :class="{ active: activeTab === 'compatibility' }" @click="setActiveTab('compatibility')">Compatibility</button>
      </div>
      
      <div class="tab-content">
        <div v-if="activeTab === 'performance'" class="performance-tab">
          <button class="run-test-button" @click="runPerformanceTest" :disabled="isRunningTest">Run Performance Test</button>
          <div class="metrics">
            <div class="metric">
              <span class="metric-label">Load Time:</span>
              <span class="metric-value">{{ performanceMetrics.loadTime.toFixed(2) }}ms</span>
            </div>
            <div class="metric">
              <span class="metric-label">Render Time:</span>
              <span class="metric-value">{{ performanceMetrics.renderTime.toFixed(2) }}ms</span>
            </div>
            <div class="metric">
              <span class="metric-label">Interaction Time:</span>
              <span class="metric-value">{{ performanceMetrics.interactionTime.toFixed(2) }}ms</span>
            </div>
            <div class="metric">
              <span class="metric-label">Memory Usage:</span>
              <span class="metric-value">{{ performanceMetrics.memoryUsage.toFixed(2) }}MB</span>
            </div>
          </div>
        </div>
        
        <div v-if="activeTab === 'accessibility'" class="accessibility-tab">
          <button class="run-test-button" @click="runAccessibilityTest" :disabled="isRunningTest">Run Accessibility Test</button>
          <div class="issues">
            <div v-for="(issue, index) in accessibilityIssues" :key="index" class="issue" :class="issue.severity">
              <span class="issue-severity">{{ issue.severity }}</span>
              <span class="issue-message">{{ issue.message }}</span>
              <span class="issue-count">{{ issue.count }}</span>
            </div>
          </div>
        </div>
        
        <div v-if="activeTab === 'compatibility'" class="compatibility-tab">
          <button class="run-test-button" @click="runCompatibilityTest" :disabled="isRunningTest">Run Compatibility Test</button>
          <div class="compatibility-status">
            <div class="browsers">
              <h4>Browsers</h4>
              <div v-for="(status, browser) in compatibilityStatus.browsers" :key="browser" class="browser-status">
                <span class="browser-name">{{ browser }}</span>
                <span class="status" :class="status">{{ status }}</span>
              </div>
            </div>
            <div class="devices">
              <h4>Devices</h4>
              <div v-for="(status, device) in compatibilityStatus.devices" :key="device" class="device-status">
                <span class="device-name">{{ device }}</span>
                <span class="status" :class="status">{{ status }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
};

describe('PreviewTestingTools', () => {
  let wrapper;
  const mockProps = {
    previewUrl: 'https://example.com/preview',
    deviceType: 'desktop',
  };

  beforeEach(() => {
    vi.clearAllMocks();
    wrapper = createWrapper(PreviewTestingToolsMock, {
      propsData: mockProps,
    });
    
    // Mock setTimeout
    vi.useFakeTimers();
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.preview-testing-tools').exists()).toBe(true);
  });

  it('renders the tabs correctly', () => {
    const tabs = wrapper.findAll('.tab-button');
    expect(tabs.length).toBe(3);
    
    // Check tab names
    expect(tabs.at(0).text()).toContain('Performance');
    expect(tabs.at(1).text()).toContain('Accessibility');
    expect(tabs.at(2).text()).toContain('Compatibility');
  });

  it('shows the Performance tab by default', () => {
    expect(wrapper.find('.performance-tab').exists()).toBe(true);
    expect(wrapper.find('.accessibility-tab').exists()).toBe(false);
    expect(wrapper.find('.compatibility-tab').exists()).toBe(false);
  });

  it('switches to Accessibility tab when clicking the Accessibility tab button', async () => {
    // Set the active tab to 'accessibility'
    await wrapper.vm.setActiveTab('accessibility');
    await wrapper.vm.$nextTick();
    
    // Check that the correct tab is shown
    expect(wrapper.find('.performance-tab').exists()).toBe(false);
    expect(wrapper.find('.accessibility-tab').exists()).toBe(true);
    expect(wrapper.find('.compatibility-tab').exists()).toBe(false);
  });

  it('runs a performance test and emits the results', async () => {
    // Run the performance test
    wrapper.vm.runPerformanceTest();
    
    // Check that the test is running
    expect(wrapper.vm.isRunningTest).toBe(true);
    
    // Fast-forward time to complete the test
    vi.advanceTimersByTime(500);
    await wrapper.vm.$nextTick();
    
    // Check that the test is complete
    expect(wrapper.vm.isRunningTest).toBe(false);
    
    // Check that the metrics are updated
    expect(wrapper.vm.performanceMetrics.loadTime).toBeGreaterThan(0);
    expect(wrapper.vm.performanceMetrics.renderTime).toBeGreaterThan(0);
    expect(wrapper.vm.performanceMetrics.interactionTime).toBeGreaterThan(0);
    expect(wrapper.vm.performanceMetrics.memoryUsage).toBeGreaterThan(0);
    
    // Check that the test-complete event is emitted
    expect(wrapper.emitted()['test-complete']).toBeTruthy();
    expect(wrapper.emitted()['test-complete'][0][0]).toBe('performance');
    expect(wrapper.emitted()['test-complete'][0][1]).toEqual(wrapper.vm.performanceMetrics);
  });
});
