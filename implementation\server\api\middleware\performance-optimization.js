/**
 * Performance Optimization Middleware
 *
 * This middleware implements various performance optimizations for the API,
 * including response caching, ETags, conditional requests, and response compression.
 */

const { createClient } = require('@supabase/supabase-js');
const Redis = require('ioredis');
const crypto = require('crypto');
const zlib = require('zlib');
const { logger } = require('./auth-middleware');

// Initialize Redis client
const redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379');

// Cache configuration
const DEFAULT_CACHE_TTL = 60 * 5; // 5 minutes in seconds
const MAX_CACHE_SIZE = 1024 * 1024 * 10; // 10 MB in bytes

/**
 * Generate a cache key from request
 * @param {Object} req - Express request object
 * @returns {string} Cache key
 */
function generateCacheKey(req) {
  // Create a string that uniquely identifies this request
  const parts = [
    req.method,
    req.originalUrl || req.url,
    // Include user ID if authenticated
    req.user?.id || 'anonymous',
    // Include query parameters
    JSON.stringify(req.query || {}),
    // Include body for POST/PUT requests (if not too large)
    req.method !== 'GET' && req.body && JSON.stringify(req.body).length < 1000
      ? JSON.stringify(req.body)
      : '',
  ];

  // Generate hash of the parts
  return crypto.createHash('sha256').update(parts.join('|')).digest('hex');
}

/**
 * Generate ETag for response
 * @param {Object|string|Buffer} data - Response data
 * @returns {string} ETag
 */
function generateETag(data) {
  const content = typeof data === 'string' ? data : JSON.stringify(data);
  return crypto.createHash('md5').update(content).digest('hex');
}

/**
 * Cache middleware
 * @param {Object} options - Cache options
 * @returns {Function} Express middleware
 */
function cache(options = {}) {
  const {
    ttl = DEFAULT_CACHE_TTL,
    methods = ['GET'],
    condition = () => true,
    keyGenerator = generateCacheKey,
  } = options;

  return async (req, res, next) => {
    // Skip caching for non-cacheable methods
    if (!methods.includes(req.method)) {
      return next();
    }

    // Skip caching based on custom condition
    if (!condition(req)) {
      return next();
    }

    // Generate cache key
    const cacheKey = `api:cache:${keyGenerator(req)}`;

    try {
      // Check if response is in cache
      const cachedResponse = await redis.get(cacheKey);

      if (cachedResponse) {
        // Parse cached response
        const { data, headers, status } = JSON.parse(cachedResponse);

        // Set headers from cache
        Object.entries(headers).forEach(([key, value]) => {
          res.set(key, value);
        });

        // Add cache hit header
        res.set('X-Cache', 'HIT');

        // Send cached response
        return res.status(status).send(data);
      }

      // Cache miss, continue with request
      res.set('X-Cache', 'MISS');

      // Store original send method
      const originalSend = res.send;

      // Override send method to cache response
      res.send = function (body) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // Get response data
          const responseData = {
            data: body,
            headers: {},
            status: res.statusCode,
          };

          // Copy headers
          Object.entries(res.getHeaders()).forEach(([key, value]) => {
            responseData.headers[key] = value;
          });

          // Calculate response size
          const responseSize = JSON.stringify(responseData).length;

          // Only cache if response is not too large
          if (responseSize <= MAX_CACHE_SIZE) {
            // Store in cache
            redis.set(cacheKey, JSON.stringify(responseData), 'EX', ttl).catch(err => {
              logger.error('Error caching response:', err);
            });
          }
        }

        // Call original send method
        return originalSend.call(this, body);
      };

      next();
    } catch (error) {
      logger.error('Error in cache middleware:', error);
      next();
    }
  };
}

/**
 * ETag middleware
 * @returns {Function} Express middleware
 */
function etag() {
  return (req, res, next) => {
    // Store original send method
    const originalSend = res.send;

    // Override send method to add ETag
    res.send = function (body) {
      // Generate ETag
      const etagValue = generateETag(body);

      // Set ETag header
      this.set('ETag', `"${etagValue}"`);

      // Check if client sent If-None-Match header
      const ifNoneMatch = req.headers['if-none-match'];

      // If ETag matches, return 304 Not Modified
      if (ifNoneMatch && ifNoneMatch === `"${etagValue}"`) {
        return this.status(304).send();
      }

      // Call original send method
      return originalSend.call(this, body);
    };

    next();
  };
}

/**
 * Compression middleware
 * @param {Object} options - Compression options
 * @returns {Function} Express middleware
 */
function compress(options = {}) {
  const {
    threshold = 1024, // Only compress responses larger than 1KB
    level = 6, // Compression level (1-9, 9 being highest)
  } = options;

  return (req, res, next) => {
    // Check if client accepts compression
    const acceptEncoding = req.headers['accept-encoding'] || '';
    const supportsGzip = acceptEncoding.includes('gzip');
    const supportsDeflate = acceptEncoding.includes('deflate');
    const supportsBrotli = acceptEncoding.includes('br');

    // Skip compression if not supported
    if (!supportsGzip && !supportsDeflate && !supportsBrotli) {
      return next();
    }

    // Store original send method
    const originalSend = res.send;

    // Override send method to compress response
    res.send = function (body) {
      // Skip compression for small responses
      if (!body || (typeof body === 'string' && body.length < threshold)) {
        return originalSend.call(this, body);
      }

      // Convert body to Buffer if it's not already
      const buffer = Buffer.isBuffer(body)
        ? body
        : Buffer.from(typeof body === 'string' ? body : JSON.stringify(body));

      // Skip compression for small buffers
      if (buffer.length < threshold) {
        return originalSend.call(this, body);
      }

      // Choose compression method based on client support
      let compressedBody;
      let encoding;

      try {
        if (supportsBrotli) {
          // Brotli compression (best compression)
          // Note: Node.js native brotli support requires v11.7.0+
          // For older versions, use the 'brotli' npm package
          if (zlib.brotliCompress) {
            compressedBody = zlib.brotliCompressSync(buffer, {
              params: {
                [zlib.constants.BROTLI_PARAM_QUALITY]: level,
              },
            });
            encoding = 'br';
          } else if (supportsGzip) {
            // Fall back to gzip if brotli not available
            compressedBody = zlib.gzipSync(buffer, { level });
            encoding = 'gzip';
          }
        } else if (supportsGzip) {
          // Gzip compression
          compressedBody = zlib.gzipSync(buffer, { level });
          encoding = 'gzip';
        } else if (supportsDeflate) {
          // Deflate compression
          compressedBody = zlib.deflateSync(buffer, { level });
          encoding = 'deflate';
        }

        // If compression succeeded and reduced size
        if (compressedBody && compressedBody.length < buffer.length) {
          // Set compression headers
          this.set('Content-Encoding', encoding);
          this.set('Vary', 'Accept-Encoding');
          this.set('X-Compression-Ratio', (compressedBody.length / buffer.length).toFixed(2));

          // Send compressed response
          return originalSend.call(this, compressedBody);
        }
      } catch (error) {
        logger.error('Error compressing response:', error);
      }

      // If compression failed or didn't reduce size, send original
      return originalSend.call(this, body);
    };

    next();
  };
}

module.exports = {
  cache,
  etag,
  compress,
  generateCacheKey,
  generateETag,
};
