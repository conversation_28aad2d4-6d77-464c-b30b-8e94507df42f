#### **`asset_bundle_versioning.md`**


# Asset & Bundle Versioning

## Manifest Format
```json
{
  "bundleId": "vendorA_dubai_v1",
  "hash": "f0931aab23",
  "assets": [
    "chair_modern_01.glb",
    "layout_floorA.json",
    "blueprint_drawer.json"
  ]
}
````

## Versioning Strategy

* Each upload generates new hash.
* Clients compare local vs server; update only if mismatch.
* Support rollback to previous hash via admin panel.

## Update Flow

1. Check manifest on plugin start.
2. Download changed assets in background.
3. Notify user/admin of pending update.

