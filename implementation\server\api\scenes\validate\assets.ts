import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { SceneAssetValidatorService } from '../../../services/scene/scene-asset-validator';

// Initialize scene asset validator service
const sceneAssetValidator = new SceneAssetValidatorService(supabase);

/**
 * Validate a single asset
 *
 * @param req - Request
 * @param res - Response
 */
export const validateAsset = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset_id } = req.params;

    // Validate parameters
    if (!asset_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ASSET_ID',
          message: 'Asset ID is required',
        },
      });
      return;
    }

    // Validate asset
    const result = await sceneAssetValidator.validateAsset(asset_id);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating asset', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Validate multiple assets
 *
 * @param req - Request
 * @param res - Response
 */
export const validateAssets = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset_ids } = req.body;

    // Validate parameters
    if (!asset_ids || !Array.isArray(asset_ids) || asset_ids.length === 0) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_ASSET_IDS',
          message: 'Asset IDs are required',
        },
      });
      return;
    }

    // Validate assets
    const result = await sceneAssetValidator.validateAssets(asset_ids);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating assets', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Validate assets in a scene
 *
 * @param req - Request
 * @param res - Response
 */
export const validateSceneAssets = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get scene
    const { data: scene, error: sceneError } = await supabase
      .from('scenes')
      .select('*')
      .eq('id', scene_id)
      .single();

    if (sceneError) {
      logger.error('Error fetching scene', { error: sceneError, scene_id });
      res.status(404).json({
        success: false,
        error: {
          code: 'SCENE_NOT_FOUND',
          message: 'Scene not found',
        },
      });
      return;
    }

    // Extract asset IDs from scene
    const assetIds: string[] = [];
    if (scene.data && scene.data.objects) {
      scene.data.objects.forEach((obj: any) => {
        if (obj.asset_id) {
          assetIds.push(obj.asset_id);
        }
      });
    }

    if (assetIds.length === 0) {
      res.status(200).json({
        success: true,
        data: {
          results: {},
          summary: {
            totalAssets: 0,
            validAssets: 0,
            assetsWithErrors: 0,
            assetsWithWarnings: 0,
            totalOptimizationSuggestions: 0,
            estimatedSavings: 0,
          },
        },
      });
      return;
    }

    // Validate assets
    const result = await sceneAssetValidator.validateAssets(assetIds);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating scene assets', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on method and path
    if (req.method === 'GET' && req.params.scene_id) {
      await validateSceneAssets(req, res);
    } else if (req.method === 'GET' && req.params.asset_id) {
      await validateAsset(req, res);
    } else if (req.method === 'POST') {
      await validateAssets(req, res);
    } else {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
    }
  } catch (error) {
    logger.error('Error in asset validation handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
}
