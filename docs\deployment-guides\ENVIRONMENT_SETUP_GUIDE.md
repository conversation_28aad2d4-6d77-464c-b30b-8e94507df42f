# MVS-VR Environment Setup Guide

This guide provides comprehensive instructions for setting up the development, staging, and production environments for the MVS-VR platform. It covers environment configuration, infrastructure setup, and environment-specific settings.

## Table of Contents

1. [Development Environment](#development-environment)
2. [Staging Environment](#staging-environment)
3. [Production Environment](#production-environment)
4. [Multi-Region Deployment](#multi-region-deployment)
5. [Environment Configuration](#environment-configuration)
6. [Security Considerations](#security-considerations)
7. [Monitoring and Logging](#monitoring-and-logging)
8. [Troubleshooting](#troubleshooting)

## Development Environment

The development environment is used for local development and testing. It provides a lightweight, isolated environment that mimics the production environment as closely as possible while allowing for rapid development and testing.

### Prerequisites

- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 20.04+ recommended)
- **Docker**: Docker Desktop 4.0.0+
- **Node.js**: v18.0.0+
- **npm**: v8.0.0+
- **Git**: 2.30.0+
- **Visual Studio Code**: Latest version (recommended)
- **Supabase CLI**: Latest version

### Installation Steps

1. **Clone the Repository**

```bash
git clone https://github.com/your-organization/mvs-vr.git
cd mvs-vr
```

2. **Install Dependencies**

```bash
cd mvs-vr-v2/implementation/server
npm install
```

3. **Set Up Environment Variables**

Create a `.env.development` file in the `mvs-vr-v2/implementation/server` directory with the following content:

```
# Server
PORT=3000
NODE_ENV=development

# Supabase
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24ifQ.625_WdcF3KHqz5amU0x2X5WWHP-OEs_4qj0ssLNHzTs
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSJ9.vI9obAHOGyVVKa3pD--kJlyxp-Z2zV9UUMAhKpNLAcU

# JWT
JWT_SECRET=your-development-jwt-secret
JWT_EXPIRY=1d
REFRESH_TOKEN_EXPIRY=7d

# Storage
STORAGE_BUCKET=assets
STORAGE_URL=http://localhost:54321/storage/v1

# LLM
LLM_PROVIDER=openai
LLM_API_KEY=your-development-openai-api-key
LLM_MODEL=gpt-3.5-turbo

# Logging
LOG_LEVEL=debug
```

4. **Start Supabase Locally**

```bash
npx supabase start
```

5. **Run Database Migrations**

```bash
npm run migrate:dev
```

6. **Start the Development Server**

```bash
npm run dev
```

The server will be available at `http://localhost:3000`.

### Docker Setup

Alternatively, you can use Docker to run the entire stack:

```bash
cd mvs-vr-v2/implementation
docker-compose -f docker-compose.dev.yml up -d
```

This will start the following services:

- Server: `http://localhost:3000`
- Supabase: `http://localhost:54321`
- Admin Portal: `http://localhost:3001`
- Vendor Portal: `http://localhost:3002`

### Development Tools

The following tools are recommended for development:

- **Visual Studio Code Extensions**:
  - ESLint
  - Prettier
  - Docker
  - TypeScript
  - Supabase
  - REST Client
  - GitLens

- **Browser Extensions**:
  - React Developer Tools
  - Redux DevTools
  - Supabase Inspector

### Development Workflow

1. Create a feature branch from the `develop` branch
2. Implement and test your changes locally
3. Run linting and tests
4. Commit your changes with descriptive commit messages
5. Push your branch and create a pull request
6. After review and approval, merge your changes into the `develop` branch

## Staging Environment

The staging environment is a pre-production environment that closely mirrors the production environment. It is used for testing and validation before deploying to production.

### Prerequisites

- **Cloud Provider**: AWS, Azure, or GCP
- **Kubernetes**: Kubernetes 1.20+
- **Terraform**: Terraform 1.0.0+
- **Helm**: Helm 3.0.0+
- **CI/CD**: GitHub Actions, GitLab CI, or Jenkins

### Infrastructure Setup

1. **Create a Kubernetes Cluster**

```bash
# AWS EKS
eksctl create cluster --name mvs-vr-staging --region us-west-2 --nodegroup-name standard-workers --node-type t3.medium --nodes 3 --nodes-min 1 --nodes-max 4 --managed

# Azure AKS
az aks create --resource-group myResourceGroup --name mvs-vr-staging --node-count 3 --enable-addons monitoring --generate-ssh-keys

# GCP GKE
gcloud container clusters create mvs-vr-staging --zone us-central1-a --num-nodes 3
```

2. **Configure kubectl**

```bash
# AWS EKS
aws eks update-kubeconfig --name mvs-vr-staging --region us-west-2

# Azure AKS
az aks get-credentials --resource-group myResourceGroup --name mvs-vr-staging

# GCP GKE
gcloud container clusters get-credentials mvs-vr-staging --zone us-central1-a
```

3. **Set Up Supabase**

For staging, we recommend using a managed Supabase instance:

- Create a Supabase project at [https://app.supabase.io](https://app.supabase.io)
- Note the API URL and keys for configuration

4. **Deploy the Application**

```bash
# Clone the repository
git clone https://github.com/your-organization/mvs-vr.git
cd mvs-vr

# Switch to the staging branch
git checkout staging

# Deploy using Helm
helm upgrade --install mvs-vr ./helm/mvs-vr --namespace mvs-vr-staging --create-namespace --values ./helm/mvs-vr/values-staging.yaml
```

### Environment Configuration

Create a `values-staging.yaml` file with the following content:

```yaml
environment: staging

server:
  replicas: 2
  resources:
    limits:
      cpu: 1
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  env:
    NODE_ENV: staging
    PORT: 3000
    SUPABASE_URL: https://your-staging-project.supabase.co
    SUPABASE_ANON_KEY: your-staging-anon-key
    SUPABASE_SERVICE_ROLE_KEY: your-staging-service-role-key
    JWT_SECRET: your-staging-jwt-secret
    JWT_EXPIRY: 1d
    REFRESH_TOKEN_EXPIRY: 7d
    STORAGE_BUCKET: assets
    STORAGE_URL: https://your-staging-project.supabase.co/storage/v1
    LLM_PROVIDER: openai
    LLM_API_KEY: your-staging-openai-api-key
    LLM_MODEL: gpt-3.5-turbo
    LOG_LEVEL: info

adminPortal:
  replicas: 2
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 512Mi
  env:
    NODE_ENV: staging
    API_URL: https://api-staging.mvs-vr.com

vendorPortal:
  replicas: 2
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 512Mi
  env:
    NODE_ENV: staging
    API_URL: https://api-staging.mvs-vr.com

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: api-staging.mvs-vr.com
      paths:
        - path: /
          pathType: Prefix
          service: server
    - host: admin-staging.mvs-vr.com
      paths:
        - path: /
          pathType: Prefix
          service: admin-portal
    - host: vendor-staging.mvs-vr.com
      paths:
        - path: /
          pathType: Prefix
          service: vendor-portal
  tls:
    - secretName: mvs-vr-staging-tls
      hosts:
        - api-staging.mvs-vr.com
        - admin-staging.mvs-vr.com
        - vendor-staging.mvs-vr.com
```

### Staging Workflow

1. Merge changes from feature branches into the `develop` branch
2. Automated tests run on the `develop` branch
3. After successful tests, changes are automatically deployed to the staging environment
4. Perform manual testing and validation in the staging environment
5. After validation, merge changes from the `develop` branch into the `main` branch for production deployment

## Production Environment

The production environment is the live environment that serves real users. It requires high availability, scalability, and security.

### Prerequisites

- **Cloud Provider**: AWS, Azure, or GCP
- **Kubernetes**: Kubernetes 1.20+
- **Terraform**: Terraform 1.0.0+
- **Helm**: Helm 3.0.0+
- **CI/CD**: GitHub Actions, GitLab CI, or Jenkins
- **Monitoring**: Prometheus, Grafana, and Alertmanager
- **Logging**: ELK Stack or Cloud Provider's logging solution

### Infrastructure Setup

1. **Create a Kubernetes Cluster**

```bash
# AWS EKS
eksctl create cluster --name mvs-vr-production --region us-west-2 --nodegroup-name standard-workers --node-type m5.large --nodes 5 --nodes-min 3 --nodes-max 10 --managed

# Azure AKS
az aks create --resource-group myResourceGroup --name mvs-vr-production --node-count 5 --enable-addons monitoring --generate-ssh-keys

# GCP GKE
gcloud container clusters create mvs-vr-production --zone us-central1-a --num-nodes 5
```

2. **Configure kubectl**

```bash
# AWS EKS
aws eks update-kubeconfig --name mvs-vr-production --region us-west-2

# Azure AKS
az aks get-credentials --resource-group myResourceGroup --name mvs-vr-production

# GCP GKE
gcloud container clusters get-credentials mvs-vr-production --zone us-central1-a
```

3. **Set Up Supabase**

For production, we recommend using a managed Supabase instance with a dedicated database:

- Create a Supabase project at [https://app.supabase.io](https://app.supabase.io)
- Configure a dedicated database with appropriate resources
- Set up database backups and replication
- Note the API URL and keys for configuration

4. **Deploy the Application**

```bash
# Clone the repository
git clone https://github.com/your-organization/mvs-vr.git
cd mvs-vr

# Switch to the main branch
git checkout main

# Deploy using Helm
helm upgrade --install mvs-vr ./helm/mvs-vr --namespace mvs-vr-production --create-namespace --values ./helm/mvs-vr/values-production.yaml
```

### Environment Configuration

Create a `values-production.yaml` file with the following content:

```yaml
environment: production

server:
  replicas: 5
  resources:
    limits:
      cpu: 2
      memory: 4Gi
    requests:
      cpu: 1
      memory: 2Gi
  env:
    NODE_ENV: production
    PORT: 3000
    SUPABASE_URL: https://your-production-project.supabase.co
    SUPABASE_ANON_KEY: your-production-anon-key
    SUPABASE_SERVICE_ROLE_KEY: your-production-service-role-key
    JWT_SECRET: your-production-jwt-secret
    JWT_EXPIRY: 1d
    REFRESH_TOKEN_EXPIRY: 7d
    STORAGE_BUCKET: assets
    STORAGE_URL: https://your-production-project.supabase.co/storage/v1
    LLM_PROVIDER: openai
    LLM_API_KEY: your-production-openai-api-key
    LLM_MODEL: gpt-4
    LOG_LEVEL: warn

adminPortal:
  replicas: 3
  resources:
    limits:
      cpu: 1
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  env:
    NODE_ENV: production
    API_URL: https://api.mvs-vr.com

vendorPortal:
  replicas: 3
  resources:
    limits:
      cpu: 1
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  env:
    NODE_ENV: production
    API_URL: https://api.mvs-vr.com

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - host: api.mvs-vr.com
      paths:
        - path: /
          pathType: Prefix
          service: server
    - host: admin.mvs-vr.com
      paths:
        - path: /
          pathType: Prefix
          service: admin-portal
    - host: vendor.mvs-vr.com
      paths:
        - path: /
          pathType: Prefix
          service: vendor-portal
  tls:
    - secretName: mvs-vr-production-tls
      hosts:
        - api.mvs-vr.com
        - admin.mvs-vr.com
        - vendor.mvs-vr.com
```

### Production Workflow

1. Merge changes from the `develop` branch into the `main` branch
2. Automated tests run on the `main` branch
3. After successful tests, changes are automatically deployed to the production environment
4. Monitor the deployment for any issues
5. If issues are detected, roll back to the previous version

## Multi-Region Deployment

For high availability and low latency, you can deploy the MVS-VR platform to multiple regions.

### Prerequisites

- **Cloud Provider**: AWS, Azure, or GCP with multi-region support
- **Global Load Balancer**: AWS Global Accelerator, Azure Front Door, or GCP Global Load Balancer
- **Database Replication**: Supabase with read replicas or custom PostgreSQL with replication
- **Content Delivery Network**: AWS CloudFront, Azure CDN, or GCP Cloud CDN

### Infrastructure Setup

1. **Create Kubernetes Clusters in Multiple Regions**

```bash
# AWS EKS
eksctl create cluster --name mvs-vr-production-us-west --region us-west-2 --nodegroup-name standard-workers --node-type m5.large --nodes 5 --nodes-min 3 --nodes-max 10 --managed
eksctl create cluster --name mvs-vr-production-eu-west --region eu-west-1 --nodegroup-name standard-workers --node-type m5.large --nodes 5 --nodes-min 3 --nodes-max 10 --managed
eksctl create cluster --name mvs-vr-production-ap-southeast --region ap-southeast-1 --nodegroup-name standard-workers --node-type m5.large --nodes 5 --nodes-min 3 --nodes-max 10 --managed
```

2. **Set Up Database Replication**

For Supabase, you can use read replicas in different regions:

- Create a primary Supabase project in your primary region
- Create read replicas in secondary regions
- Configure your application to use the appropriate database based on the region

3. **Set Up a Global Load Balancer**

```bash
# AWS Global Accelerator
aws globalaccelerator create-accelerator --name mvs-vr-accelerator --enabled --tags Key=Name,Value=mvs-vr-accelerator
```

4. **Deploy the Application to Each Region**

```bash
# Deploy to US West
kubectl config use-context mvs-vr-production-us-west
helm upgrade --install mvs-vr ./helm/mvs-vr --namespace mvs-vr-production --create-namespace --values ./helm/mvs-vr/values-production-us-west.yaml

# Deploy to EU West
kubectl config use-context mvs-vr-production-eu-west
helm upgrade --install mvs-vr ./helm/mvs-vr --namespace mvs-vr-production --create-namespace --values ./helm/mvs-vr/values-production-eu-west.yaml

# Deploy to AP Southeast
kubectl config use-context mvs-vr-production-ap-southeast
helm upgrade --install mvs-vr ./helm/mvs-vr --namespace mvs-vr-production --create-namespace --values ./helm/mvs-vr/values-production-ap-southeast.yaml
```

### Region-Specific Configuration

Create region-specific values files with the following content:

```yaml
# values-production-us-west.yaml
environment: production
region: us-west

server:
  env:
    SUPABASE_URL: https://your-us-west-project.supabase.co
    REGION: us-west

# values-production-eu-west.yaml
environment: production
region: eu-west

server:
  env:
    SUPABASE_URL: https://your-eu-west-project.supabase.co
    REGION: eu-west

# values-production-ap-southeast.yaml
environment: production
region: ap-southeast

server:
  env:
    SUPABASE_URL: https://your-ap-southeast-project.supabase.co
    REGION: ap-southeast
```

## Environment Configuration

### Environment Variables

The MVS-VR platform uses environment variables for configuration. Here are the key environment variables:

- **NODE_ENV**: Environment (development, staging, production)
- **PORT**: Server port
- **SUPABASE_URL**: Supabase URL
- **SUPABASE_ANON_KEY**: Supabase anonymous key
- **SUPABASE_SERVICE_ROLE_KEY**: Supabase service role key
- **JWT_SECRET**: Secret for JWT tokens
- **JWT_EXPIRY**: JWT token expiry
- **REFRESH_TOKEN_EXPIRY**: Refresh token expiry
- **STORAGE_BUCKET**: Storage bucket name
- **STORAGE_URL**: Storage URL
- **LLM_PROVIDER**: LLM provider (openai, anthropic, etc.)
- **LLM_API_KEY**: LLM API key
- **LLM_MODEL**: LLM model
- **LOG_LEVEL**: Logging level

### Configuration Files

The MVS-VR platform uses the following configuration files:

- **.env.development**: Development environment variables
- **.env.staging**: Staging environment variables
- **.env.production**: Production environment variables
- **values-development.yaml**: Helm values for development
- **values-staging.yaml**: Helm values for staging
- **values-production.yaml**: Helm values for production

### Secrets Management

For sensitive information like API keys and passwords, use a secrets management solution:

- **Kubernetes Secrets**: For Kubernetes deployments
- **AWS Secrets Manager**: For AWS deployments
- **Azure Key Vault**: For Azure deployments
- **GCP Secret Manager**: For GCP deployments

## Security Considerations

### Network Security

- Use a private network for internal communication
- Use a VPN for administrative access
- Implement network policies to restrict traffic
- Use a Web Application Firewall (WAF) to protect against common attacks

### Authentication and Authorization

- Use strong passwords and enforce password policies
- Implement multi-factor authentication for administrative access
- Use role-based access control (RBAC) for authorization
- Regularly audit user access and permissions

### Data Security

- Encrypt data at rest and in transit
- Implement database encryption
- Use secure connections (HTTPS, SSL/TLS)
- Regularly backup data and test restoration

### Compliance

- Ensure compliance with relevant regulations (GDPR, CCPA, etc.)
- Implement data retention policies
- Provide mechanisms for data export and deletion
- Maintain audit logs for compliance purposes

## Monitoring and Logging

### Monitoring

- Use Prometheus for metrics collection
- Use Grafana for metrics visualization
- Set up alerts for critical metrics
- Monitor system health, performance, and resource usage

### Logging

- Use a centralized logging solution (ELK Stack, Loki, etc.)
- Implement structured logging
- Set appropriate log levels for different environments
- Retain logs for an appropriate period

### Alerting

- Set up alerts for critical issues
- Configure notification channels (email, Slack, PagerDuty, etc.)
- Implement escalation policies
- Document incident response procedures

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check database credentials
   - Verify network connectivity
   - Check database status and health
   - Verify connection pool configuration

2. **API Errors**
   - Check API logs for detailed error information
   - Verify API credentials
   - Check network connectivity
   - Verify API rate limits

3. **Deployment Failures**
   - Check deployment logs
   - Verify Kubernetes resources
   - Check container images
   - Verify environment variables and configuration

### Getting Help

If you encounter issues not covered in this guide, you can:

- Check the [Deployment FAQ](./DEPLOYMENT_FAQ.md)
- Visit the [MVS-VR Support Portal](https://support.mvs-vr.com)
- Contact <NAME_EMAIL>
