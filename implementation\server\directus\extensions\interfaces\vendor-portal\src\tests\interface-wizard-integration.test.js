/**
 * Integration test for the vendor portal interface with the Guided Setup Wizard
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import InterfaceVendorPortal from '../interface.vue';
import GuidedSetupWizard from '../components/GuidedSetupWizard/GuidedSetupWizard.vue';

// Mock the components
vi.mock('../components/GuidedSetupWizard/GuidedSetupWizard.vue', () => ({
  name: 'GuidedSetupWizard',
  render: h => h('div', { class: 'guided-setup-wizard-mock' }),
  props: ['vendorId']
}));

vi.mock('../components/DashboardOverview.vue', () => ({
  name: 'DashboardOverview',
  render: h => h('div', { class: 'dashboard-overview-mock' })
}));

vi.mock('../components/ProductManager.vue', () => ({
  name: 'ProductManager',
  render: h => h('div', { class: 'product-manager-mock' })
}));

vi.mock('../components/ClientManager.vue', () => ({
  name: 'ClientManager',
  render: h => h('div', { class: 'client-manager-mock' })
}));

vi.mock('../components/ShowroomManager.vue', () => ({
  name: 'ShowroomManager',
  render: h => h('div', { class: 'showroom-manager-mock' })
}));

vi.mock('../components/AnalyticsManager.vue', () => ({
  name: 'AnalyticsManager',
  render: h => h('div', { class: 'analytics-manager-mock' })
}));

vi.mock('../components/SubscriptionManager.vue', () => ({
  name: 'SubscriptionManager',
  render: h => h('div', { class: 'subscription-manager-mock' })
}));

vi.mock('../components/BrandingManager.vue', () => ({
  name: 'BrandingManager',
  render: h => h('div', { class: 'branding-manager-mock' })
}));

vi.mock('../components/CategoryManager.vue', () => ({
  name: 'CategoryManager',
  render: h => h('div', { class: 'category-manager-mock' })
}));

vi.mock('../components/VisualEditors/VisualEditors.vue', () => ({
  name: 'VisualEditors',
  render: h => h('div', { class: 'visual-editors-mock' }),
  props: ['vendorId']
}));

vi.mock('../components/CollaborationFeatures.vue', () => ({
  name: 'CollaborationFeatures',
  render: h => h('div', { class: 'collaboration-features-mock' })
}));

describe('InterfaceVendorPortal with Guided Setup Wizard', () => {
  let wrapper;
  
  beforeEach(() => {
    wrapper = mount(InterfaceVendorPortal, {
      propsData: {
        value: null,
        disabled: false,
        options: {}
      }
    });
  });
  
  afterEach(() => {
    wrapper.destroy();
  });
  
  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
  });
  
  it('includes the GuidedSetupWizard component', () => {
    expect(wrapper.findComponent(GuidedSetupWizard).exists()).toBe(true);
  });
  
  it('shows the GuidedSetupWizard when setup-wizard tab is active', async () => {
    // Arrange - Initially the dashboard tab is active
    expect(wrapper.vm.activeTab).toBe('dashboard');
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Act - Change to setup-wizard tab
    await wrapper.setData({ activeTab: 'setup-wizard' });
    
    // Assert
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(true);
  });
  
  it('passes the vendorId prop to GuidedSetupWizard', async () => {
    // Arrange
    const vendorId = 'test-vendor-id';
    await wrapper.setData({ 
      activeTab: 'setup-wizard',
      vendorId
    });
    
    // Assert
    const guidedSetupWizard = wrapper.findComponent(GuidedSetupWizard);
    expect(guidedSetupWizard.props('vendorId')).toBe(vendorId);
  });
  
  it('changes to setup-wizard tab when tab is clicked', async () => {
    // Arrange
    const tabs = wrapper.findAll('.tab');
    const setupWizardTab = tabs.filter(tab => tab.text().includes('Setup Wizard')).at(0);
    
    // Act
    await setupWizardTab.trigger('click');
    
    // Assert
    expect(wrapper.vm.activeTab).toBe('setup-wizard');
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(true);
  });
  
  it('hides other components when setup-wizard tab is active', async () => {
    // Arrange
    await wrapper.setData({ activeTab: 'setup-wizard' });
    
    // Assert
    expect(wrapper.find('.dashboard-overview-mock').isVisible()).toBe(false);
    expect(wrapper.find('.product-manager-mock').isVisible()).toBe(false);
    expect(wrapper.find('.client-manager-mock').isVisible()).toBe(false);
    expect(wrapper.find('.showroom-manager-mock').isVisible()).toBe(false);
    expect(wrapper.find('.analytics-manager-mock').isVisible()).toBe(false);
    expect(wrapper.find('.subscription-manager-mock').isVisible()).toBe(false);
    expect(wrapper.find('.branding-manager-mock').isVisible()).toBe(false);
    expect(wrapper.find('.category-manager-mock').isVisible()).toBe(false);
    expect(wrapper.find('.visual-editors-mock').isVisible()).toBe(false);
    expect(wrapper.find('.collaboration-features-mock').isVisible()).toBe(false);
  });
  
  it('shows the correct tab content when tab is changed', async () => {
    // Test dashboard tab
    await wrapper.setData({ activeTab: 'dashboard' });
    expect(wrapper.find('.dashboard-overview-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test products tab
    await wrapper.setData({ activeTab: 'products' });
    expect(wrapper.find('.product-manager-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test clients tab
    await wrapper.setData({ activeTab: 'clients' });
    expect(wrapper.find('.client-manager-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test showrooms tab
    await wrapper.setData({ activeTab: 'showrooms' });
    expect(wrapper.find('.showroom-manager-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test analytics tab
    await wrapper.setData({ activeTab: 'analytics' });
    expect(wrapper.find('.analytics-manager-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test subscription tab
    await wrapper.setData({ activeTab: 'subscription' });
    expect(wrapper.find('.subscription-manager-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test branding tab
    await wrapper.setData({ activeTab: 'branding' });
    expect(wrapper.find('.branding-manager-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test categories tab
    await wrapper.setData({ activeTab: 'categories' });
    expect(wrapper.find('.category-manager-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test visual-editors tab
    await wrapper.setData({ activeTab: 'visual-editors' });
    expect(wrapper.find('.visual-editors-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test collaboration tab
    await wrapper.setData({ activeTab: 'collaboration' });
    expect(wrapper.find('.collaboration-features-mock').isVisible()).toBe(true);
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(false);
    
    // Test setup-wizard tab
    await wrapper.setData({ activeTab: 'setup-wizard' });
    expect(wrapper.find('.guided-setup-wizard-mock').isVisible()).toBe(true);
    expect(wrapper.find('.dashboard-overview-mock').isVisible()).toBe(false);
  });
});
