#!/bin/bash

# MVS-VR Deployment and Testing Script
# This script deploys the services and tests basic connectivity

set -e

echo "🚀 Starting MVS-VR Deployment and Testing..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a service is healthy
check_service_health() {
    local service_name=$1
    local port=$2
    local path=${3:-/health}
    local max_attempts=30
    local attempt=1

    print_status "Checking health of $service_name on port $port..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://localhost:$port$path" > /dev/null 2>&1; then
            print_success "$service_name is healthy!"
            return 0
        fi
        
        echo -n "."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to become healthy after $max_attempts attempts"
    return 1
}

# Function to test Supabase connectivity
test_supabase_connection() {
    print_status "Testing Supabase connectivity..."
    
    local supabase_url="https://hiyqiqbgiueyyvqoqhht.supabase.co"
    
    if curl -s -f "$supabase_url/rest/v1/" > /dev/null 2>&1; then
        print_success "Supabase connection successful!"
        return 0
    else
        print_error "Failed to connect to Supabase"
        return 1
    fi
}

# Step 1: Stop any existing containers
print_status "Stopping any existing containers..."
docker-compose -f docker-compose.exported.yml down --remove-orphans || true

# Step 2: Create network if it doesn't exist
print_status "Creating Docker network..."
docker network create mvs-network 2>/dev/null || print_warning "Network mvs-network already exists"

# Step 3: Load Docker images
print_status "Loading Docker images..."
if [ -f "load-images.sh" ]; then
    chmod +x load-images.sh
    ./load-images.sh
else
    print_warning "load-images.sh not found, assuming images are already loaded"
fi

# Step 4: Start services
print_status "Starting services..."
docker-compose -f docker-compose.exported.yml up -d

# Step 5: Wait for services to start
print_status "Waiting for services to initialize..."
sleep 10

# Step 6: Test Supabase connectivity
test_supabase_connection

# Step 7: Check service health
print_status "Checking service health..."

# Core services
check_service_health "Redis" "6379" "" || print_warning "Redis health check failed"
check_service_health "Auth Service" "3005" "/health"
check_service_health "API Gateway" "4000" "/health"
check_service_health "Asset Service" "5000" "/health"
check_service_health "Scene Service" "6000" "/health"
check_service_health "Blueprint Service" "3003" "/health"
check_service_health "LLM Service" "7000" "/health"
check_service_health "Analytics Service" "8000" "/health"
check_service_health "Monitoring Service" "9090" "/health"
check_service_health "Directus" "8055" "/server/health"
check_service_health "Nginx" "80" "/health"

# Step 8: Test basic connectivity
print_status "Testing basic connectivity..."

# Test main domain
if curl -s -f "http://localhost/" > /dev/null 2>&1; then
    print_success "Main domain (port 80) is accessible!"
else
    print_error "Main domain (port 80) is not accessible"
fi

# Test API endpoint
if curl -s -f "http://localhost/api/health" > /dev/null 2>&1; then
    print_success "API endpoint is accessible!"
else
    print_warning "API endpoint may not be fully ready yet"
fi

# Step 9: Display service status
print_status "Service Status Summary:"
docker-compose -f docker-compose.exported.yml ps

# Step 10: Display useful information
echo ""
print_success "🎉 Deployment completed!"
echo ""
echo "Service URLs:"
echo "  Main Site:        http://localhost/"
echo "  API Gateway:      http://localhost/api/"
echo "  Admin Panel:      http://admin.mvs.kanousai.com/ (or http://localhost:8055/)"
echo "  Health Check:     http://localhost/health"
echo ""
echo "Individual Services:"
echo "  Auth Service:     http://localhost:3005/health"
echo "  Asset Service:    http://localhost:5000/health"
echo "  Scene Service:    http://localhost:6000/health"
echo "  Blueprint Service: http://localhost:3003/health"
echo "  LLM Service:      http://localhost:7000/health"
echo "  Analytics Service: http://localhost:8000/health"
echo "  Monitoring:       http://localhost:9090/health"
echo ""
echo "To view logs: docker-compose -f docker-compose.exported.yml logs -f [service-name]"
echo "To stop services: docker-compose -f docker-compose.exported.yml down"
echo ""
print_status "All services are configured to connect to remote Supabase server"
print_status "Domain: mvs.kanousai.com should point to **************"
