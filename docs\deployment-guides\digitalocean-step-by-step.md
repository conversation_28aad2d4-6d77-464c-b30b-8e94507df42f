# Complete DigitalOcean Deployment Guide - Step by Step

## Prerequisites
- DigitalOcean account with billing enabled
- Domain name configured (mvs.kanousai.com)
- Local development environment with project files

## Phase 1: DigitalOcean Infrastructure Setup

### Step 1: Create Volume for Persistent Storage
1. **Login to DigitalOcean Console**
   - Go to https://cloud.digitalocean.com
   - Navigate to **Volumes** in the left sidebar

2. **Create New Volume**
   - Click **"Create Volume"**
   - **Name**: `mvs-staging-data`
   - **Size**: `50 GB` (minimum recommended)
   - **Region**: Same as your droplet (e.g., NYC1)
   - **Filesystem**: `ext4`
   - Click **"Create Volume"**

3. **Note Volume Details**
   - Copy the volume ID (e.g., `vol-abc123def456`)
   - Note the mount commands provided

### Step 2: Create Droplet
1. **Navigate to Droplets**
   - Click **"Create"** → **"Droplets"**

2. **Configure Droplet**
   - **Image**: Ubuntu 22.04 (LTS) x64
   - **Plan**: 
     - **Basic Plan**
     - **4 vCPUs, 8 GB RAM, 160 GB SSD** ($48/month)
   - **Region**: NYC1 (or closest to your users)
   - **VPC**: Default VPC
   - **Authentication**: SSH Key (recommended) or Password
   - **Hostname**: `mvs-staging-server`
   - **Tags**: `mvs`, `staging`, `web-server`

3. **Advanced Options**
   - ✅ **Enable Monitoring**
   - ✅ **Enable Backups** (+20% cost)
   - ✅ **IPv6**
   - **User Data**: Leave empty for now

4. **Create Droplet**
   - Click **"Create Droplet"**
   - Wait 2-3 minutes for creation
   - Note the public IP address

### Step 3: Attach Volume to Droplet
1. **Go to Volumes**
   - Find your `mvs-staging-data` volume
   - Click **"More"** → **"Attach to Droplet"**
   - Select your `mvs-staging-server` droplet
   - Click **"Attach Volume"**

## Phase 2: Server Initial Setup

### Step 4: Connect to Server
```bash
# Replace YOUR_SERVER_IP with actual IP
ssh root@YOUR_SERVER_IP

# If using SSH key authentication
ssh -i ~/.ssh/your_key root@YOUR_SERVER_IP
```

### Step 5: Update System
```bash
# Update package lists
apt update

# Upgrade all packages
apt upgrade -y

# Install essential packages
apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
```

### Step 6: Mount Volume
```bash
# Check if volume is attached
lsblk

# Create mount point
mkdir -p /mnt/mvs-data

# Format volume (ONLY if new volume)
mkfs.ext4 /dev/disk/by-id/scsi-0DO_Volume_mvs-staging-data

# Mount volume
mount -o discard,defaults /dev/disk/by-id/scsi-0DO_Volume_mvs-staging-data /mnt/mvs-data

# Add to fstab for persistent mounting
echo '/dev/disk/by-id/scsi-0DO_Volume_mvs-staging-data /mnt/mvs-data ext4 defaults,nofail,discard 0 2' >> /etc/fstab

# Verify mount
df -h /mnt/mvs-data
```

### Step 7: Install Docker
```bash
# Add Docker's official GPG key
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# Add Docker repository
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null

# Update package lists
apt update

# Install Docker
apt install -y docker-ce docker-ce-cli containerd.io

# Start and enable Docker
systemctl start docker
systemctl enable docker

# Add current user to docker group
usermod -aG docker $USER

# Verify Docker installation
docker --version
```

### Step 8: Install Docker Compose
```bash
# Download Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose

# Make executable
chmod +x /usr/local/bin/docker-compose

# Create symlink
ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose

# Verify installation
docker-compose --version
```

### Step 9: Install Nginx
```bash
# Install Nginx
apt install -y nginx

# Start and enable Nginx
systemctl start nginx
systemctl enable nginx

# Check status
systemctl status nginx
```

### Step 10: Configure Firewall
```bash
# Install UFW if not installed
apt install -y ufw

# Set default policies
ufw default deny incoming
ufw default allow outgoing

# Allow SSH
ufw allow ssh

# Allow HTTP and HTTPS
ufw allow 80
ufw allow 443

# Enable firewall
ufw --force enable

# Check status
ufw status
```

## Phase 3: File Upload and Configuration

### Step 11: Create Project Directory
```bash
# Create application directory
mkdir -p /opt/mvs-vr
cd /opt/mvs-vr

# Create subdirectories
mkdir -p logs uploads backups
```

### Step 12: Upload Project Files
**Option A: Using Git (Recommended)**
```bash
# Clone repository
git clone https://github.com/your-username/MVS-VR.git .

# Navigate to project directory
cd mvs-vr-v2
```

**Option B: Using SCP from Local Machine**
```bash
# From your local machine, upload the entire project
scp -r /path/to/MVS-VR/mvs-vr-v2 root@YOUR_SERVER_IP:/opt/mvs-vr/

# Then on server
cd /opt/mvs-vr/mvs-vr-v2
```

**Option C: Using rsync (Most Efficient)**
```bash
# From your local machine
rsync -avz --progress /path/to/MVS-VR/mvs-vr-v2/ root@YOUR_SERVER_IP:/opt/mvs-vr/mvs-vr-v2/
```

### Step 13: Configure Environment Variables
```bash
# Copy environment template
cp .env.staging.example .env.staging

# Edit environment file
nano .env.staging
```

**Required Environment Variables to Update:**
```bash
# Generate secure passwords and keys
POSTGRES_PASSWORD=$(openssl rand -base64 32)
DIRECTUS_DB_PASSWORD=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 64)
CSRF_SECRET=$(openssl rand -base64 32)
API_KEY=$(openssl rand -base64 32)
DIRECTUS_KEY=$(openssl rand -base64 32)
DIRECTUS_SECRET=$(openssl rand -base64 64)
DIRECTUS_ADMIN_PASSWORD=$(openssl rand -base64 16)
GRAFANA_PASSWORD=$(openssl rand -base64 16)

# Update with your actual values
DIRECTUS_ADMIN_EMAIL=<EMAIL>
```

### Step 14: Set File Permissions
```bash
# Set ownership
chown -R root:root /opt/mvs-vr

# Set permissions
chmod -R 755 /opt/mvs-vr
chmod +x /opt/mvs-vr/mvs-vr-v2/scripts/deploy-staging.sh

# Create Docker volumes directory on mounted volume
mkdir -p /mnt/mvs-data/docker-volumes
ln -s /mnt/mvs-data/docker-volumes /opt/mvs-vr/mvs-vr-v2/volumes
```

## Phase 4: Application Deployment

### Step 15: Configure Docker Volumes
```bash
# Update docker-compose to use mounted volume
cd /opt/mvs-vr/mvs-vr-v2

# Create volume configuration
cat > docker-compose.override.yml << 'EOF'
version: '3.8'

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/mvs-data/docker-volumes/postgres
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/mvs-data/docker-volumes/redis
  directus_uploads:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/mvs-data/docker-volumes/directus-uploads
  directus_database:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/mvs-data/docker-volumes/directus-db
  prometheus_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/mvs-data/docker-volumes/prometheus
  grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/mvs-data/docker-volumes/grafana
  loki_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/mvs-data/docker-volumes/loki
  nginx_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /mnt/mvs-data/docker-volumes/nginx-logs
EOF

# Create volume directories
mkdir -p /mnt/mvs-data/docker-volumes/{postgres,redis,directus-uploads,directus-db,prometheus,grafana,loki,nginx-logs}
```

### Step 16: Build and Deploy Services
```bash
# Build Docker images
docker-compose -f docker-compose.staging.yml build

# Start services
docker-compose -f docker-compose.staging.yml up -d

# Check service status
docker-compose -f docker-compose.staging.yml ps
```

### Step 17: Wait for Services to Start
```bash
# Monitor logs
docker-compose -f docker-compose.staging.yml logs -f

# Check individual service health
docker-compose -f docker-compose.staging.yml exec postgres pg_isready -U postgres
docker-compose -f docker-compose.staging.yml exec redis redis-cli ping

# Wait for API to be ready (may take 2-3 minutes)
curl http://localhost:3000/health

# Wait for Directus to be ready
curl http://localhost:8055/server/health
```

## Phase 5: Domain and SSL Configuration

### Step 18: Configure DNS Records
**In your domain registrar (e.g., Namecheap, GoDaddy):**
```
Type: A Record
Name: api
Value: YOUR_SERVER_IP
TTL: 300

Type: A Record  
Name: admin
Value: YOUR_SERVER_IP
TTL: 300

Type: A Record
Name: staging
Value: YOUR_SERVER_IP
TTL: 300
```

### Step 19: Configure Nginx
```bash
# Copy Nginx configuration
cp /opt/mvs-vr/mvs-vr-v2/deployment/nginx/staging.conf /etc/nginx/sites-available/mvs-staging

# Create sites-enabled symlink
ln -s /etc/nginx/sites-available/mvs-staging /etc/nginx/sites-enabled/

# Remove default site
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t

# Reload Nginx
systemctl reload nginx
```

### Step 20: Install SSL Certificates
```bash
# Install Certbot
apt install -y certbot python3-certbot-nginx

# Obtain SSL certificates
certbot --nginx -d api.mvs.kanousai.com --non-interactive --agree-tos --email <EMAIL>
certbot --nginx -d admin.mvs.kanousai.com --non-interactive --agree-tos --email <EMAIL>  
certbot --nginx -d staging.mvs.kanousai.com --non-interactive --agree-tos --email <EMAIL>

# Test automatic renewal
certbot renew --dry-run

# Reload Nginx
systemctl reload nginx
```

## Phase 6: Final Validation

### Step 21: Run Health Checks
```bash
# API Health Check
curl https://api.mvs.kanousai.com/health

# Directus Health Check
curl https://admin.mvs.kanousai.com/server/health

# Check all services
docker-compose -f docker-compose.staging.yml ps
```

### Step 22: Access Applications
1. **API**: https://api.mvs.kanousai.com/health
2. **Admin Panel**: https://admin.mvs.kanousai.com
   - Email: <EMAIL>
   - Password: (from .env.staging DIRECTUS_ADMIN_PASSWORD)
3. **Monitoring**: http://YOUR_SERVER_IP:3001
   - Username: admin
   - Password: (from .env.staging GRAFANA_PASSWORD)

### Step 23: Setup Monitoring
```bash
# Check Prometheus targets
curl http://localhost:9090/targets

# Access Grafana
# Import dashboard configurations from deployment/monitoring/grafana/
```

## Phase 7: Backup and Maintenance

### Step 24: Configure Automated Backups
```bash
# Create backup script
cat > /opt/mvs-vr/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/mnt/mvs-data/backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Database backup
docker-compose -f /opt/mvs-vr/mvs-vr-v2/docker-compose.staging.yml exec -T postgres pg_dump -U postgres mvs_staging > "$BACKUP_DIR/database.sql"

# Volume backup
tar -czf "$BACKUP_DIR/volumes.tar.gz" -C /mnt/mvs-data/docker-volumes .

# Keep only last 7 days
find /mnt/mvs-data/backups -type d -mtime +7 -exec rm -rf {} +
EOF

chmod +x /opt/mvs-vr/backup.sh

# Add to crontab
echo "0 2 * * * /opt/mvs-vr/backup.sh" | crontab -
```

### Step 25: Setup Log Rotation
```bash
# Configure logrotate
cat > /etc/logrotate.d/mvs-vr << 'EOF'
/opt/mvs-vr/mvs-vr-v2/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
EOF
```

## 🎉 Deployment Complete!

### Service URLs
- **API**: https://api.mvs.kanousai.com
- **Admin**: https://admin.mvs.kanousai.com  
- **Staging**: https://staging.mvs.kanousai.com
- **Monitoring**: http://YOUR_SERVER_IP:3001

### Useful Commands
```bash
# View logs
docker-compose -f docker-compose.staging.yml logs -f

# Restart services
docker-compose -f docker-compose.staging.yml restart

# Update deployment
cd /opt/mvs-vr/mvs-vr-v2 && git pull && docker-compose -f docker-compose.staging.yml up -d --build

# Backup manually
/opt/mvs-vr/backup.sh
```

### Next Steps
1. ✅ Test all functionality
2. ✅ Configure monitoring alerts
3. ✅ Set up CI/CD pipeline
4. ✅ Document admin procedures
5. ✅ Plan production deployment
