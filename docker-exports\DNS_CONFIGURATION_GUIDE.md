# 🌐 DNS Configuration Guide for MVS-VR Subdomains

## 📋 Required DNS Records

You need to configure these DNS records for your domain `mvs.kanousai.com`:

### **A Records (Point to Server IP: **************)**

| Subdomain | Type | Name | Value | TTL |
|-----------|------|------|-------|-----|
| **Primary Domain** | A | `@` or blank | `**************` | 300 |
| **API Subdomain** | A | `api` | `**************` | 300 |
| **Admin Subdomain** | A | `admin` | `**************` | 300 |
| **Staging Subdomain** | A | `staging` | `**************` | 300 |

### **Optional CNAME Record**
| Subdomain | Type | Name | Value | TTL |
|-----------|------|------|-------|-----|
| **WWW Redirect** | CNAME | `www` | `mvs.kanousai.com` | 300 |

## 🔧 How to Configure DNS

### **Method 1: Most DNS Providers (Cloudflare, Namecheap, GoDaddy, etc.)**

1. **Log into your DNS provider dashboard**
2. **Navigate to DNS Management** for `mvs.kanousai.com`
3. **Add these A records:**

```
Type: A
Name: @              Value: **************
Name: api            Value: **************  
Name: admin          Value: **************
Name: staging        Value: **************
```

### **Method 2: Cloudflare (Recommended)**

If using Cloudflare:

1. **Add A Records:**
   - `mvs.kanousai.com` → `**************` (Proxied: ☁️)
   - `api.mvs.kanousai.com` → `**************` (Proxied: ☁️)
   - `admin.mvs.kanousai.com` → `**************` (Proxied: ☁️)
   - `staging.mvs.kanousai.com` → `**************` (DNS Only: 🌐)

2. **SSL/TLS Settings:**
   - Set to "Full" or "Full (Strict)"
   - Enable "Always Use HTTPS"

### **Method 3: DigitalOcean DNS (If using DO nameservers)**

```bash
# Using DigitalOcean CLI
doctl compute domain records create mvs.kanousai.com --record-type A --record-name @ --record-data **************
doctl compute domain records create mvs.kanousai.com --record-type A --record-name api --record-data **************
doctl compute domain records create mvs.kanousai.com --record-type A --record-name admin --record-data **************
doctl compute domain records create mvs.kanousai.com --record-type A --record-name staging --record-data **************
```

## ✅ Verification Commands

After configuring DNS, verify the records:

### **Check DNS Propagation**
```bash
# Check each subdomain
nslookup mvs.kanousai.com
nslookup api.mvs.kanousai.com
nslookup admin.mvs.kanousai.com
nslookup staging.mvs.kanousai.com

# Or use dig
dig mvs.kanousai.com
dig api.mvs.kanousai.com
dig admin.mvs.kanousai.com
dig staging.mvs.kanousai.com
```

### **Online DNS Checkers**
- https://dnschecker.org/
- https://www.whatsmydns.net/
- https://dns.google/ (Google DNS)

### **Expected Results**
All domains should resolve to: `**************`

## 🕐 DNS Propagation Timeline

- **Local DNS**: 5-15 minutes
- **ISP DNS**: 30 minutes - 2 hours  
- **Global Propagation**: 2-24 hours
- **Full Propagation**: Up to 48 hours

## 🧪 Test Your Configuration

Once DNS is configured, test each subdomain:

### **1. Primary Domain (Vendor UX Home)**
```bash
curl -I http://mvs.kanousai.com/
# Should return: HTTP/1.1 200 OK
```
**Browser Test:** http://mvs.kanousai.com/
**Expected:** Vendor UX landing page with platform overview

### **2. API Subdomain**
```bash
curl -I http://api.mvs.kanousai.com/
# Should return: HTTP/1.1 200 OK
```
**Browser Test:** http://api.mvs.kanousai.com/
**Expected:** API Gateway status page with endpoint documentation

### **3. Admin Subdomain**
```bash
curl -I http://admin.mvs.kanousai.com/
# Should return: HTTP/1.1 200 OK
```
**Browser Test:** http://admin.mvs.kanousai.com/
**Expected:** Admin panel (Directus CMS) login page

### **4. Staging Subdomain**
```bash
curl -I http://staging.mvs.kanousai.com/
# Should return: HTTP/1.1 200 OK
```
**Browser Test:** http://staging.mvs.kanousai.com/
**Expected:** Staging environment warning page

## 🔒 SSL Certificate Setup (Future)

Once DNS is working, you can add SSL certificates:

### **Using Let's Encrypt (Recommended)**
```bash
# SSH into your server and run:
sudo certbot --nginx -d mvs.kanousai.com
sudo certbot --nginx -d api.mvs.kanousai.com
sudo certbot --nginx -d admin.mvs.kanousai.com
sudo certbot --nginx -d staging.mvs.kanousai.com
```

### **Using Cloudflare SSL**
If using Cloudflare, SSL is automatic with proxy enabled.

## 🚨 Troubleshooting

### **DNS Not Resolving**
1. **Check TTL**: Lower TTL values propagate faster
2. **Flush DNS Cache**: 
   - Windows: `ipconfig /flushdns`
   - Mac: `sudo dscacheutil -flushcache`
   - Linux: `sudo systemctl restart systemd-resolved`

### **Wrong IP Resolution**
1. **Verify A Records**: Ensure all point to `**************`
2. **Check Propagation**: Use multiple DNS checkers
3. **Wait**: DNS changes can take up to 48 hours

### **Subdomain Not Working**
1. **Check Nginx Config**: Ensure server blocks are configured
2. **Restart Nginx**: `docker-compose restart nginx`
3. **Check Logs**: `docker-compose logs nginx`

## 📋 DNS Configuration Checklist

- [ ] **Primary Domain**: `mvs.kanousai.com` → `**************`
- [ ] **API Subdomain**: `api.mvs.kanousai.com` → `**************`
- [ ] **Admin Subdomain**: `admin.mvs.kanousai.com` → `**************`
- [ ] **Staging Subdomain**: `staging.mvs.kanousai.com` → `**************`
- [ ] **TTL Set**: 300 seconds (5 minutes) for faster updates
- [ ] **Propagation Check**: All domains resolve correctly
- [ ] **HTTP Test**: All subdomains return 200 OK
- [ ] **Browser Test**: All pages load correctly

## 🎯 Expected User Experience

### **Vendor Journey**
1. **Entry Point**: `mvs.kanousai.com` - Professional landing page
2. **API Access**: `api.mvs.kanousai.com` - Developer documentation
3. **Content Management**: `admin.mvs.kanousai.com` - Admin dashboard
4. **Testing**: `staging.mvs.kanousai.com` - Development environment

### **Domain Hierarchy**
```
mvs.kanousai.com (Primary - Vendor UX Home)
├── api.mvs.kanousai.com (API Gateway)
├── admin.mvs.kanousai.com (Admin Panel)
└── staging.mvs.kanousai.com (Staging Environment)
```

## 📞 Support

If you encounter DNS issues:
1. **Check your DNS provider's documentation**
2. **Contact your domain registrar support**
3. **Use online DNS tools for verification**
4. **Wait for full propagation (up to 48 hours)**

**Remember**: DNS changes are not instant. Be patient and test from multiple locations.
