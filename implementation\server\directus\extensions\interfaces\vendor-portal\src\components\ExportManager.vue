<template>
  <div class="export-manager">
    <div class="export-header">
      <h3>Export Analytics Data</h3>
      <p>Export your analytics data in various formats for further analysis or reporting.</p>
    </div>

    <div class="export-form">
      <div class="form-group">
        <label for="report-type">Report Type</label>
        <select id="report-type" v-model="reportType">
          <option value="visitors">Visitor Sessions</option>
          <option value="products">Product Interactions</option>
          <option value="showrooms">Showroom Analytics</option>
          <option value="conversions">Conversion Events</option>
          <option value="custom">Custom Report</option>
        </select>
      </div>

      <div v-if="reportType === 'custom'" class="form-group">
        <label for="custom-report">Custom Report</label>
        <select id="custom-report" v-model="customReportId">
          <option value="">Select a saved report</option>
          <option v-for="report in savedReports" :key="report.id" :value="report.id">
            {{ report.name }}
          </option>
        </select>
      </div>

      <div class="form-group">
        <label for="showroom-id">Showroom</label>
        <select id="showroom-id" v-model="showroomId">
          <option value="">All Showrooms</option>
          <option v-for="showroom in showrooms" :key="showroom.id" :value="showroom.id">
            {{ showroom.name }}
          </option>
        </select>
      </div>

      <div class="date-range">
        <div class="form-group">
          <label for="start-date">Start Date</label>
          <input type="date" id="start-date" v-model="startDate" :max="today" />
        </div>
        <div class="form-group">
          <label for="end-date">End Date</label>
          <input type="date" id="end-date" v-model="endDate" :max="today" :min="startDate" />
        </div>
      </div>

      <div class="form-group">
        <label for="export-format">Export Format</label>
        <select id="export-format" v-model="exportFormat">
          <option value="csv">CSV</option>
          <option value="excel">Excel</option>
          <option value="pdf">PDF</option>
        </select>
      </div>

      <div class="export-actions">
        <button class="export-button" @click="exportData" :disabled="isExporting || !isFormValid">
          <i class="material-icons">file_download</i>
          {{ isExporting ? 'Exporting...' : 'Export Data' }}
        </button>
      </div>
    </div>

    <div v-if="exportHistory.length > 0" class="export-history">
      <h4>Recent Exports</h4>
      <table class="history-table">
        <thead>
          <tr>
            <th>Date</th>
            <th>Report Type</th>
            <th>Format</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(export_item, index) in exportHistory" :key="index">
            <td>{{ formatDate(export_item.date) }}</td>
            <td>{{ formatReportType(export_item.reportType) }}</td>
            <td>{{ export_item.format.toUpperCase() }}</td>
            <td>
              <span class="status" :class="export_item.status">
                {{ export_item.status }}
              </span>
            </td>
            <td>
              <button 
                v-if="export_item.status === 'completed'" 
                class="download-button" 
                @click="downloadExport(export_item)"
              >
                <i class="material-icons">download</i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ExportManager',

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      reportType: 'visitors',
      customReportId: '',
      showroomId: '',
      startDate: this.getDefaultStartDate(),
      endDate: new Date().toISOString().split('T')[0],
      exportFormat: 'csv',
      isExporting: false,
      showrooms: [],
      savedReports: [],
      exportHistory: [],
      today: new Date().toISOString().split('T')[0]
    };
  },

  computed: {
    isFormValid() {
      if (this.reportType === 'custom' && !this.customReportId) {
        return false;
      }
      return this.startDate && this.endDate && this.reportType;
    }
  },

  mounted() {
    this.loadShowrooms();
    this.loadSavedReports();
    this.loadExportHistory();
  },

  methods: {
    // Get default start date (30 days ago)
    getDefaultStartDate() {
      const date = new Date();
      date.setDate(date.getDate() - 30);
      return date.toISOString().split('T')[0];
    },

    // Load showrooms
    async loadShowrooms() {
      try {
        const response = await fetch(`/api/showrooms?vendor_id=${this.vendorId}`);
        const data = await response.json();
        
        if (data.success) {
          this.showrooms = data.data;
        } else {
          console.error('Error loading showrooms:', data.error);
          this.showrooms = [];
        }
      } catch (error) {
        console.error('Error loading showrooms:', error);
        this.showrooms = [];
      }
    },

    // Load saved custom reports
    async loadSavedReports() {
      try {
        const response = await fetch(`/api/analytics/custom-reports?vendor_id=${this.vendorId}`);
        const data = await response.json();
        
        if (data.success) {
          this.savedReports = data.data;
        } else {
          console.error('Error loading saved reports:', data.error);
          this.savedReports = [];
        }
      } catch (error) {
        console.error('Error loading saved reports:', error);
        this.savedReports = [];
      }
    },

    // Load export history
    async loadExportHistory() {
      try {
        const response = await fetch(`/api/analytics/export/history?vendor_id=${this.vendorId}`);
        const data = await response.json();
        
        if (data.success) {
          this.exportHistory = data.data;
        } else {
          console.error('Error loading export history:', data.error);
          this.exportHistory = [];
        }
      } catch (error) {
        console.error('Error loading export history:', error);
        this.exportHistory = [];
      }
    },

    // Export data
    async exportData() {
      if (!this.isFormValid) return;
      
      this.isExporting = true;
      
      try {
        // Build query parameters
        let params = new URLSearchParams({
          vendor_id: this.vendorId,
          report_type: this.reportType,
          start_date: this.startDate,
          end_date: this.endDate,
          format: this.exportFormat
        });
        
        if (this.showroomId) {
          params.append('showroom_id', this.showroomId);
        }
        
        if (this.reportType === 'custom' && this.customReportId) {
          params.append('report_id', this.customReportId);
        }
        
        // Determine endpoint based on format
        const endpoint = `/api/analytics/export/${this.exportFormat}?${params.toString()}`;
        
        // Fetch the export
        const response = await fetch(endpoint);
        
        if (response.ok) {
          // For direct download
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          
          // Set filename based on report type and date
          const date = new Date().toISOString().split('T')[0];
          const reportName = this.reportType === 'custom' 
            ? this.savedReports.find(r => r.id === this.customReportId)?.name || 'custom-report'
            : this.reportType;
          
          a.download = `${reportName}-${date}.${this.exportFormat}`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          
          // Add to export history
          this.exportHistory.unshift({
            date: new Date(),
            reportType: this.reportType,
            format: this.exportFormat,
            status: 'completed',
            url: url
          });
        } else {
          const errorData = await response.json();
          console.error('Export failed:', errorData);
          
          // Add failed export to history
          this.exportHistory.unshift({
            date: new Date(),
            reportType: this.reportType,
            format: this.exportFormat,
            status: 'failed',
            error: errorData.error?.message || 'Export failed'
          });
        }
      } catch (error) {
        console.error('Error exporting data:', error);
        
        // Add failed export to history
        this.exportHistory.unshift({
          date: new Date(),
          reportType: this.reportType,
          format: this.exportFormat,
          status: 'failed',
          error: error.message || 'Export failed'
        });
      } finally {
        this.isExporting = false;
      }
    },

    // Download a previously exported file
    downloadExport(export_item) {
      if (export_item.url) {
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = export_item.url;
        a.download = `${export_item.reportType}-${this.formatDate(export_item.date)}.${export_item.format}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },

    // Format date for display
    formatDate(date) {
      return new Date(date).toLocaleString();
    },

    // Format report type for display
    formatReportType(type) {
      if (type === 'custom') {
        const report = this.savedReports.find(r => r.id === this.customReportId);
        return report ? report.name : 'Custom Report';
      }
      
      const typeMap = {
        visitors: 'Visitor Sessions',
        products: 'Product Interactions',
        showrooms: 'Showroom Analytics',
        conversions: 'Conversion Events'
      };
      
      return typeMap[type] || type;
    }
  }
};
</script>

<style scoped>
.export-manager {
  padding: 20px;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.export-header {
  margin-bottom: 20px;
}

.export-header h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.export-header p {
  color: #666;
  margin: 0;
}

.export-form {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.date-range {
  display: flex;
  gap: 15px;
}

.date-range .form-group {
  flex: 1;
}

.export-actions {
  margin-top: 20px;
}

.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.export-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.export-history {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.export-history h4 {
  margin-top: 0;
  margin-bottom: 15px;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
}

.history-table th,
.history-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.history-table th {
  font-weight: 500;
  color: #666;
}

.status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status.completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status.failed {
  background-color: #ffebee;
  color: #c62828;
}

.status.processing {
  background-color: #e3f2fd;
  color: #1565c0;
}

.download-button {
  background: none;
  border: none;
  color: #2196F3;
  cursor: pointer;
  padding: 5px;
}

.download-button:hover {
  color: #0b7dda;
}
</style>
