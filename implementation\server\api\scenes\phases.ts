import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { ScenePhaseManagerService } from '../../services/scene/scene-phase-manager';
import { PhaseType } from '../../config/phase-config';
import { TeamPhaseManagerService } from '../../services/team/team-phase-manager';
import { PhaseAnalyticsService } from '../../services/analytics/phase-analytics';

// Initialize services
const phaseManager = new ScenePhaseManagerService(supabase);
const teamPhaseManager = new TeamPhaseManagerService(supabase);
const analyticsService = new PhaseAnalyticsService(supabase);

/**
 * Get scene phase state
 *
 * @param req - Request
 * @param res - Response
 */
export const getScenePhaseState = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get phase state
    const phaseState = await phaseManager.getScenePhaseState(scene_id);

    res.status(200).json({
      success: true,
      data: phaseState,
    });
  } catch (error) {
    logger.error('Error getting scene phase state', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Initialize scene phase state
 *
 * @param req - Request
 * @param res - Response
 */
export const initializeScenePhaseState = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Initialize phase state
    const phaseState = await phaseManager.initializeScenePhaseState(scene_id);

    res.status(200).json({
      success: true,
      data: phaseState,
    });
  } catch (error) {
    logger.error('Error initializing scene phase state', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Transition to next phase
 *
 * @param req - Request
 * @param res - Response
 */
export const transitionToNextPhase = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const userId = req.user?.id;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get user roles if user is authenticated
    let userRoles: string[] | undefined;
    if (userId) {
      userRoles = await teamPhaseManager.getUserRolesForScene(scene_id, userId);
    }

    // Transition to next phase
    const result = await phaseManager.transitionToNextPhase(scene_id, userId, userRoles);

    if (!result.success) {
      res.status(400).json({
        success: false,
        error: {
          code: 'TRANSITION_FAILED',
          message: result.error || 'Failed to transition to next phase',
          details: {
            missing_validations: result.missing_validations,
          },
        },
      });
      return;
    }

    // Track analytics
    analyticsService.getPhaseAnalytics();

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error transitioning to next phase', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Transition to specific phase
 *
 * @param req - Request
 * @param res - Response
 */
export const transitionToPhase = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { target_phase } = req.body;
    const userId = req.user?.id;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!target_phase || !Object.values(PhaseType).includes(target_phase)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PHASE',
          message: 'Valid target phase is required',
        },
      });
      return;
    }

    // Get user roles if user is authenticated
    let userRoles: string[] | undefined;
    if (userId) {
      userRoles = await teamPhaseManager.getUserRolesForScene(scene_id, userId);
    }

    // Transition to phase
    const result = await phaseManager.transitionToPhase(scene_id, target_phase, userId, userRoles);

    if (!result.success) {
      res.status(400).json({
        success: false,
        error: {
          code: 'TRANSITION_FAILED',
          message: result.error || 'Failed to transition to phase',
          details: {
            missing_validations: result.missing_validations,
          },
        },
      });
      return;
    }

    // Track analytics
    analyticsService.getPhaseAnalytics();

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error transitioning to phase', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Validate phase
 *
 * @param req - Request
 * @param res - Response
 */
export const validatePhase = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { phase } = req.query;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get current phase if not specified
    let targetPhase: PhaseType;
    if (!phase) {
      const phaseState = await phaseManager.getScenePhaseState(scene_id);
      targetPhase = phaseState.current_phase;
    } else if (!Object.values(PhaseType).includes(phase as PhaseType)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PHASE',
          message: 'Valid phase is required',
        },
      });
      return;
    } else {
      targetPhase = phase as PhaseType;
    }

    // Validate phase
    const result = await phaseManager.validatePhase(scene_id, targetPhase);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating phase', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Record validation result
 *
 * @param req - Request
 * @param res - Response
 */
export const recordValidationResult = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { validation_type, phase, result } = req.body;
    const userId = req.user?.id;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!validation_type) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_VALIDATION_TYPE',
          message: 'Validation type is required',
        },
      });
      return;
    }

    if (!result || typeof result.valid !== 'boolean') {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_RESULT',
          message: 'Valid result is required',
        },
      });
      return;
    }

    // Get current phase if not specified
    let targetPhase: PhaseType;
    if (!phase) {
      const phaseState = await phaseManager.getScenePhaseState(scene_id);
      targetPhase = phaseState.current_phase;
    } else if (!Object.values(PhaseType).includes(phase as PhaseType)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PHASE',
          message: 'Valid phase is required',
        },
      });
      return;
    } else {
      targetPhase = phase as PhaseType;
    }

    // Record validation result
    const phaseState = await phaseManager.recordValidationResult(
      scene_id,
      targetPhase,
      validation_type,
      result,
      userId,
    );

    // Track analytics
    analyticsService.getPhaseAnalytics();

    res.status(200).json({
      success: true,
      data: phaseState,
    });
  } catch (error) {
    logger.error('Error recording validation result', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Skip phase
 *
 * @param req - Request
 * @param res - Response
 */
export const skipPhase = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { phase } = req.body;
    const userId = req.user?.id;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get current phase if not specified
    let targetPhase: PhaseType;
    if (!phase) {
      const phaseState = await phaseManager.getScenePhaseState(scene_id);
      targetPhase = phaseState.current_phase;
    } else if (!Object.values(PhaseType).includes(phase as PhaseType)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PHASE',
          message: 'Valid phase is required',
        },
      });
      return;
    } else {
      targetPhase = phase as PhaseType;
    }

    // Skip phase
    const result = await phaseManager.skipPhase(scene_id, targetPhase, userId);

    if (!result.success) {
      res.status(400).json({
        success: false,
        error: {
          code: 'SKIP_FAILED',
          message: result.error || 'Failed to skip phase',
        },
      });
      return;
    }

    // Track analytics
    analyticsService.getPhaseAnalytics();

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error skipping phase', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get phase progress
 *
 * @param req - Request
 * @param res - Response
 */
export const getPhaseProgress = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get phase progress
    const progress = await phaseManager.getPhaseProgress(scene_id);

    res.status(200).json({
      success: true,
      data: progress,
    });
  } catch (error) {
    logger.error('Error getting phase progress', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get phase assignments
 *
 * @param req - Request
 * @param res - Response
 */
export const getPhaseAssignments = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    // Get phase assignments
    const assignments = await teamPhaseManager.getPhaseAssignments(scene_id);

    res.status(200).json({
      success: true,
      data: assignments,
    });
  } catch (error) {
    logger.error('Error getting phase assignments', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Assign phase to team member
 *
 * @param req - Request
 * @param res - Response
 */
export const assignPhaseToTeamMember = async (req: Request, res: Response): Promise<void> => {
  try {
    const { scene_id } = req.params;
    const { phase, assigned_to, due_date, notes } = req.body;
    const userId = req.user?.id;

    // Validate parameters
    if (!scene_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_ID',
          message: 'Scene ID is required',
        },
      });
      return;
    }

    if (!phase || !Object.values(PhaseType).includes(phase as PhaseType)) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PHASE',
          message: 'Valid phase is required',
        },
      });
      return;
    }

    if (!assigned_to) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_USER',
          message: 'User ID to assign to is required',
        },
      });
      return;
    }

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'You must be logged in to assign phases',
        },
      });
      return;
    }

    // Assign phase
    const result = await teamPhaseManager.assignPhaseToTeamMember(
      scene_id,
      phase as PhaseType,
      assigned_to,
      userId,
      due_date,
      notes,
    );

    if (!result.success) {
      res.status(400).json({
        success: false,
        error: {
          code: 'ASSIGNMENT_FAILED',
          message: result.error || 'Failed to assign phase',
        },
      });
      return;
    }

    res.status(201).json({
      success: true,
      data: result.assignment,
    });
  } catch (error) {
    logger.error('Error assigning phase to team member', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Route based on method and path
    if (req.method === 'GET' && req.path.endsWith('/progress')) {
      await getPhaseProgress(req, res);
    } else if (req.method === 'GET' && req.path.endsWith('/assignments')) {
      await getPhaseAssignments(req, res);
    } else if (req.method === 'GET' && req.query.validate === 'true') {
      await validatePhase(req, res);
    } else if (req.method === 'GET') {
      await getScenePhaseState(req, res);
    } else if (req.method === 'POST' && req.path.endsWith('/initialize')) {
      await initializeScenePhaseState(req, res);
    } else if (req.method === 'POST' && req.path.endsWith('/next')) {
      await transitionToNextPhase(req, res);
    } else if (req.method === 'POST' && req.path.endsWith('/transition')) {
      await transitionToPhase(req, res);
    } else if (req.method === 'POST' && req.path.endsWith('/skip')) {
      await skipPhase(req, res);
    } else if (req.method === 'POST' && req.path.endsWith('/validation')) {
      await recordValidationResult(req, res);
    } else if (req.method === 'POST' && req.path.endsWith('/assign')) {
      await assignPhaseToTeamMember(req, res);
    } else {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
    }
  } catch (error) {
    logger.error('Error in phase handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
}
