# Blueprint Injection System

The Blueprint Injection System allows vendors to create and manage behavior blueprints for their VR experiences. These blueprints can be injected into UE scenes to add custom behavior without requiring code changes.

## Overview

The Blueprint Injection System consists of the following components:

1. **Database**: Stores blueprint definitions in Supabase.
2. **API**: Provides endpoints for managing blueprints.
3. **Admin Portal**: Provides a UI for creating and managing blueprints.
4. **UE Plugin**: Loads and injects blueprints into UE scenes.

## Blueprint Schema

A blueprint consists of the following components:

### Blueprint

```typescript
interface Blueprint {
  id: string;
  vendor_id: string;
  name: string;
  description: string | null;
  tags: string[];
  script: BlueprintScript;
  version: string;
  created_at: string;
  updated_at: string;
}
```

### Blueprint Script

```typescript
interface BlueprintScript {
  author: string;
  initialization_actions: BlueprintAction[];
  triggers: BlueprintTrigger[];
  properties: Record<string, string>;
}
```

### Blueprint Action

```typescript
interface BlueprintAction {
  type: string;
  parameters: Record<string, string>;
}
```

### Blueprint Trigger

```typescript
interface BlueprintTrigger {
  type: string;
  condition: string;
  actions: BlueprintAction[];
}
```

## API Endpoints

### List Blueprints

```
GET /api/blueprints
```

Query parameters:
- `vendor_id`: Filter by vendor ID
- `tags`: Filter by tags (comma-separated)
- `limit`: Maximum number of blueprints to return
- `offset`: Offset for pagination

### Get Blueprint

```
GET /api/blueprints/[blueprint_id]
```

### Create Blueprint

```
POST /api/blueprints
```

Request body:
```json
{
  "vendor_id": "string",
  "name": "string",
  "description": "string",
  "tags": ["string"],
  "script": {
    "author": "string",
    "initialization_actions": [
      {
        "type": "string",
        "parameters": {
          "key": "value"
        }
      }
    ],
    "triggers": [
      {
        "type": "string",
        "condition": "string",
        "actions": [
          {
            "type": "string",
            "parameters": {
              "key": "value"
            }
          }
        ]
      }
    ],
    "properties": {
      "key": "value"
    }
  },
  "version": "string"
}
```

### Update Blueprint

```
PUT /api/blueprints/[blueprint_id]
```

Request body: Same as create blueprint.

### Delete Blueprint

```
DELETE /api/blueprints/[blueprint_id]
```

## UE Plugin Integration

### Blueprint Component

The `UMVSBlueprintComponent` is the base component for blueprint-injected behavior. It provides the following functionality:

- Initialize with a blueprint script
- Trigger events
- Execute actions
- Get and set blueprint properties

### Blueprint Injector

The `UMVSBlueprintInjector` is responsible for loading and injecting blueprints into UE scenes. It provides the following functionality:

- Load blueprints from the server
- Inject blueprints into actors
- Check if a blueprint is loaded

### Bootstrap Manager Integration

The `UMVSBootstrapManager` has been updated to include blueprint injection as part of the bootstrap process. It performs the following steps:

1. Get the scene configuration
2. Extract blueprint IDs from the configuration
3. Load each blueprint using the blueprint injector
4. Inject each blueprint into the appropriate actor
5. Complete the bootstrap process when all blueprints are loaded and injected

## Action Types

The following action types are supported:

### Set Property

Sets a property on the blueprint.

Parameters:
- `name`: Property name
- `value`: Property value

### Spawn Actor

Spawns an actor in the scene.

Parameters:
- `class`: Actor class
- `location_x`: X location
- `location_y`: Y location
- `location_z`: Z location
- `rotation_pitch`: Pitch rotation
- `rotation_yaw`: Yaw rotation
- `rotation_roll`: Roll rotation
- `scale_x`: X scale
- `scale_y`: Y scale
- `scale_z`: Z scale

### Play Sound

Plays a sound.

Parameters:
- `sound`: Sound asset
- `volume`: Volume (0.0 - 1.0)
- `pitch`: Pitch (0.5 - 2.0)
- `location_x`: X location (optional)
- `location_y`: Y location (optional)
- `location_z`: Z location (optional)

### Play Animation

Plays an animation on a skeletal mesh.

Parameters:
- `animation`: Animation asset
- `loop`: Whether to loop the animation (true/false)
- `rate`: Playback rate (0.1 - 10.0)

### Set Visibility

Sets the visibility of an actor or component.

Parameters:
- `target`: Target actor or component
- `visible`: Whether the target should be visible (true/false)

## Trigger Types

The following trigger types are supported:

### On Initialize

Triggered when the blueprint is initialized.

### On Enter

Triggered when the player enters a trigger volume.

Parameters:
- `volume`: Trigger volume

### On Exit

Triggered when the player exits a trigger volume.

Parameters:
- `volume`: Trigger volume

### On Interact

Triggered when the player interacts with an actor.

Parameters:
- `actor`: Actor to interact with

### On Timer

Triggered after a specified time.

Parameters:
- `seconds`: Time in seconds

### On Event

Triggered when a custom event is fired.

Parameters:
- `event_name`: Event name

## Example Blueprint

```json
{
  "vendor_id": "123e4567-e89b-12d3-a456-426614174000",
  "name": "Door Controller",
  "description": "Controls a door that opens when the player approaches",
  "tags": ["door", "interaction"],
  "script": {
    "author": "John Doe",
    "initialization_actions": [
      {
        "type": "SetProperty",
        "parameters": {
          "name": "door_open",
          "value": "false"
        }
      }
    ],
    "triggers": [
      {
        "type": "OnEnter",
        "condition": "",
        "actions": [
          {
            "type": "SetProperty",
            "parameters": {
              "name": "door_open",
              "value": "true"
            }
          },
          {
            "type": "PlayAnimation",
            "parameters": {
              "animation": "DoorOpen",
              "loop": "false",
              "rate": "1.0"
            }
          },
          {
            "type": "PlaySound",
            "parameters": {
              "sound": "DoorOpenSound",
              "volume": "0.8",
              "pitch": "1.0"
            }
          }
        ]
      },
      {
        "type": "OnExit",
        "condition": "",
        "actions": [
          {
            "type": "SetProperty",
            "parameters": {
              "name": "door_open",
              "value": "false"
            }
          },
          {
            "type": "PlayAnimation",
            "parameters": {
              "animation": "DoorClose",
              "loop": "false",
              "rate": "1.0"
            }
          },
          {
            "type": "PlaySound",
            "parameters": {
              "sound": "DoorCloseSound",
              "volume": "0.8",
              "pitch": "1.0"
            }
          }
        ]
      }
    ],
    "properties": {
      "door_open": "false"
    }
  },
  "version": "1.0.0"
}
```
