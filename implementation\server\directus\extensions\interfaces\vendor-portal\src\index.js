import InterfaceComponent from './interface.vue'

export default {
  id: 'vendor-portal',
  name: 'Vendor Portal',
  description: 'Custom interface for vendor portal',
  icon: 'store',
  component: InterfaceComponent,
  types: ['json'],
  options: [
    {
      field: 'vendorId',
      name: 'Vendor ID',
      type: 'string',
      meta: {
        width: 'full',
        interface: 'input',
        options: {
          placeholder: 'Enter vendor ID',
        },
      },
    },
    {
      field: 'showDashboard',
      name: 'Show Dashboard',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showProducts',
      name: 'Show Products',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showCategories',
      name: 'Show Categories',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showClients',
      name: 'Show Clients',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showSubscription',
      name: 'Show Subscription',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showShowrooms',
      name: 'Show Showrooms',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showAnalytics',
      name: 'Show Analytics',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showBranding',
      name: 'Show Branding',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showPreviewTesting',
      name: 'Show Preview & Testing',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
    {
      field: 'showCollaboration',
      name: 'Show Collaboration',
      type: 'boolean',
      meta: {
        width: 'half',
        interface: 'boolean',
      },
      schema: {
        default_value: true,
      },
    },
  ],
}
