-- Migration: Enhance Scene Management
-- Description: This migration adds tables and functions for enhanced scene management

-- Create scenes table if it doesn't exist
CREATE TABLE IF NOT EXISTS scenes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  vendor_id UUID NOT NULL,
  project_id UUID,
  name TEXT NOT NULL,
  description TEXT,
  version TEXT NOT NULL,
  is_published BOOLEAN NOT NULL DEFAULT FALSE,
  is_template BOOLEAN NOT NULL DEFAULT FALSE,
  thumbnail_url TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_scenes_vendor
    FOREIGN KEY (vendor_id)
    REFERENCES vendors(id)
    ON DELETE CASCADE
);

-- Create scene_parts table
CREATE TABLE IF NOT EXISTS scene_parts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL,
  type TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  url TEXT,
  storage_path TEXT,
  content_type TEXT,
  hash TEXT NOT NULL,
  size INTEGER NOT NULL DEFAULT 0,
  metadata JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_scene_parts_scene
    FOREIGN KEY (scene_id)
    REFERENCES scenes(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_scene_part_type
    UNIQUE (scene_id, type)
);

-- Create scene_part_dependencies table
CREATE TABLE IF NOT EXISTS scene_part_dependencies (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  part_id UUID NOT NULL,
  dependency_id UUID NOT NULL,
  dependency_type TEXT NOT NULL,
  is_required BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_scene_part_dependencies_part
    FOREIGN KEY (part_id)
    REFERENCES scene_parts(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_scene_part_dependency
    UNIQUE (part_id, dependency_id)
);

-- Create scene_asset_bundles table
CREATE TABLE IF NOT EXISTS scene_asset_bundles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL,
  bundle_id UUID NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_scene_asset_bundles_scene
    FOREIGN KEY (scene_id)
    REFERENCES scenes(id)
    ON DELETE CASCADE,
    
  CONSTRAINT fk_scene_asset_bundles_bundle
    FOREIGN KEY (bundle_id)
    REFERENCES asset_bundles(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_scene_asset_bundle
    UNIQUE (scene_id, bundle_id)
);

-- Create scene_blueprints table
CREATE TABLE IF NOT EXISTS scene_blueprints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL,
  blueprint_id UUID NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_scene_blueprints_scene
    FOREIGN KEY (scene_id)
    REFERENCES scenes(id)
    ON DELETE CASCADE,
    
  CONSTRAINT fk_scene_blueprints_blueprint
    FOREIGN KEY (blueprint_id)
    REFERENCES blueprints(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_scene_blueprint
    UNIQUE (scene_id, blueprint_id)
);

-- Create scene_versions table to track version history
CREATE TABLE IF NOT EXISTS scene_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL,
  version TEXT NOT NULL,
  data JSONB NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_scene_versions_scene
    FOREIGN KEY (scene_id)
    REFERENCES scenes(id)
    ON DELETE CASCADE,
    
  CONSTRAINT unique_scene_version
    UNIQUE (scene_id, version)
);

-- Create scene_validations table
CREATE TABLE IF NOT EXISTS scene_validations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL,
  version TEXT NOT NULL,
  is_valid BOOLEAN NOT NULL,
  errors JSONB NOT NULL DEFAULT '[]',
  warnings JSONB NOT NULL DEFAULT '[]',
  missing_dependencies JSONB,
  performance_impact JSONB,
  validated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT fk_scene_validations_scene
    FOREIGN KEY (scene_id)
    REFERENCES scenes(id)
    ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX idx_scenes_vendor_id ON scenes(vendor_id);
CREATE INDEX idx_scenes_project_id ON scenes(project_id);
CREATE INDEX idx_scenes_is_published ON scenes(is_published);
CREATE INDEX idx_scenes_is_template ON scenes(is_template);
CREATE INDEX idx_scene_parts_scene_id ON scene_parts(scene_id);
CREATE INDEX idx_scene_parts_type ON scene_parts(type);
CREATE INDEX idx_scene_part_dependencies_part_id ON scene_part_dependencies(part_id);
CREATE INDEX idx_scene_asset_bundles_scene_id ON scene_asset_bundles(scene_id);
CREATE INDEX idx_scene_asset_bundles_bundle_id ON scene_asset_bundles(bundle_id);
CREATE INDEX idx_scene_blueprints_scene_id ON scene_blueprints(scene_id);
CREATE INDEX idx_scene_blueprints_blueprint_id ON scene_blueprints(blueprint_id);
CREATE INDEX idx_scene_versions_scene_id ON scene_versions(scene_id);
CREATE INDEX idx_scene_validations_scene_id ON scene_validations(scene_id);

-- Create function to store scene version history
CREATE OR REPLACE FUNCTION store_scene_version_history()
RETURNS TRIGGER AS $$
BEGIN
  -- Only store history if version changed
  IF OLD.version IS DISTINCT FROM NEW.version THEN
    -- Store the old version in history
    INSERT INTO scene_versions (
      scene_id,
      version,
      data,
      created_at
    ) VALUES (
      OLD.id,
      OLD.version,
      jsonb_build_object(
        'id', OLD.id,
        'vendor_id', OLD.vendor_id,
        'project_id', OLD.project_id,
        'name', OLD.name,
        'description', OLD.description,
        'is_published', OLD.is_published,
        'is_template', OLD.is_template,
        'thumbnail_url', OLD.thumbnail_url,
        'metadata', OLD.metadata,
        'created_at', OLD.created_at,
        'updated_at', OLD.updated_at
      ),
      OLD.updated_at
    );
  END IF;
  
  -- Update the updated_at timestamp
  NEW.updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to store version history
CREATE TRIGGER store_scene_version_history_trigger
BEFORE UPDATE OF version ON scenes
FOR EACH ROW
EXECUTE FUNCTION store_scene_version_history();

-- Create function to get scene with parts
CREATE OR REPLACE FUNCTION get_scene_with_parts(p_scene_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_scene JSONB;
  v_parts JSONB;
BEGIN
  -- Get scene
  SELECT jsonb_build_object(
    'id', s.id,
    'vendor_id', s.vendor_id,
    'project_id', s.project_id,
    'name', s.name,
    'description', s.description,
    'version', s.version,
    'is_published', s.is_published,
    'is_template', s.is_template,
    'thumbnail_url', s.thumbnail_url,
    'metadata', s.metadata,
    'created_at', s.created_at,
    'updated_at', s.updated_at
  ) INTO v_scene
  FROM scenes s
  WHERE s.id = p_scene_id;
  
  -- Get parts
  SELECT jsonb_agg(
    jsonb_build_object(
      'id', p.id,
      'type', p.type,
      'name', p.name,
      'description', p.description,
      'url', p.url,
      'hash', p.hash,
      'size', p.size,
      'metadata', p.metadata
    )
  ) INTO v_parts
  FROM scene_parts p
  WHERE p.scene_id = p_scene_id;
  
  -- Combine scene and parts
  RETURN jsonb_set(v_scene, '{parts}', COALESCE(v_parts, '[]'::jsonb));
END;
$$;

-- Add RLS policies for scenes
ALTER TABLE scenes ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see scenes they have access to
CREATE POLICY scenes_select_policy
  ON scenes
  FOR SELECT
  USING (
    vendor_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM vendors v
      WHERE v.id = vendor_id AND v.user_id = auth.uid()
    ) OR
    auth.jwt() ->> 'role' = 'admin'
  );

-- Add RLS policies for scene_parts
ALTER TABLE scene_parts ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see scene parts they have access to
CREATE POLICY scene_parts_select_policy
  ON scene_parts
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM scenes s
      WHERE s.id = scene_id AND (
        s.vendor_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM vendors v
          WHERE v.id = s.vendor_id AND v.user_id = auth.uid()
        ) OR
        auth.jwt() ->> 'role' = 'admin'
      )
    )
  );
