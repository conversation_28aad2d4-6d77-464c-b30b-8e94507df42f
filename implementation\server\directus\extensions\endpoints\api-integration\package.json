{"name": "directus-extension-api-integration", "version": "1.0.0", "description": "Custom API endpoints for integration with frontend and other services", "main": "index.js", "keywords": ["directus", "directus-extension", "directus-custom-endpoint", "api-integration"], "directus:extension": {"type": "endpoint", "path": "index.js", "source": "src/index.js", "host": "^10.0.0"}, "scripts": {"build": "directus-extension build"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "joi": "^17.9.2"}}