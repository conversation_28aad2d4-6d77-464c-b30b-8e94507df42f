<template>
  <div class="help-tooltip" :class="{ 'is-active': isActive }">
    <div 
      class="help-tooltip-trigger"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
      @click="toggleTooltip"
      @keydown.enter="toggleTooltip"
      @keydown.space="toggleTooltip"
      tabindex="0"
      role="button"
      :aria-expanded="isActive.toString()"
      :aria-controls="`tooltip-content-${_uid}`"
    >
      <slot name="trigger">
        <span class="help-icon">
          <i class="material-icons">help_outline</i>
        </span>
      </slot>
    </div>
    
    <transition name="tooltip-fade">
      <div 
        v-if="isActive"
        :id="`tooltip-content-${_uid}`"
        class="help-tooltip-content"
        :class="[position, size]"
        role="tooltip"
      >
        <div class="help-tooltip-arrow" :class="position"></div>
        <div class="help-tooltip-header" v-if="title">
          <h3 class="help-tooltip-title">{{ title }}</h3>
          <button 
            class="help-tooltip-close"
            @click="closeTooltip"
            aria-label="Close tooltip"
          >
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="help-tooltip-body">
          <slot>
            <div v-if="content" v-html="content"></div>
            <p v-else>No help content available.</p>
          </slot>
        </div>
        <div class="help-tooltip-footer" v-if="$slots.footer">
          <slot name="footer"></slot>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'HelpTooltip',
  
  props: {
    title: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    position: {
      type: String,
      default: 'top',
      validator: value => ['top', 'right', 'bottom', 'left'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    trigger: {
      type: String,
      default: 'hover',
      validator: value => ['hover', 'click', 'manual'].includes(value)
    },
    delay: {
      type: Number,
      default: 300
    },
    active: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      isActive: this.active,
      hoverTimeout: null
    };
  },
  
  watch: {
    active(newValue) {
      this.isActive = newValue;
    },
    
    isActive(newValue) {
      this.$emit('update:active', newValue);
      
      if (newValue) {
        this.$emit('show');
        this.addOutsideClickListener();
      } else {
        this.$emit('hide');
        this.removeOutsideClickListener();
      }
    }
  },
  
  mounted() {
    // Add escape key listener
    document.addEventListener('keydown', this.handleKeyDown);
  },
  
  beforeDestroy() {
    // Clean up event listeners
    document.removeEventListener('keydown', this.handleKeyDown);
    this.removeOutsideClickListener();
    
    // Clear any pending timeouts
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }
  },
  
  methods: {
    handleMouseEnter() {
      if (this.trigger === 'hover') {
        this.hoverTimeout = setTimeout(() => {
          this.isActive = true;
        }, this.delay);
      }
    },
    
    handleMouseLeave() {
      if (this.trigger === 'hover') {
        if (this.hoverTimeout) {
          clearTimeout(this.hoverTimeout);
        }
        
        this.hoverTimeout = setTimeout(() => {
          this.isActive = false;
        }, this.delay);
      }
    },
    
    toggleTooltip() {
      if (this.trigger === 'click' || this.trigger === 'manual') {
        this.isActive = !this.isActive;
      }
    },
    
    closeTooltip() {
      this.isActive = false;
    },
    
    handleKeyDown(event) {
      if (event.key === 'Escape' && this.isActive) {
        this.closeTooltip();
      }
    },
    
    addOutsideClickListener() {
      document.addEventListener('click', this.handleOutsideClick);
    },
    
    removeOutsideClickListener() {
      document.removeEventListener('click', this.handleOutsideClick);
    },
    
    handleOutsideClick(event) {
      const tooltip = this.$el;
      
      if (tooltip && !tooltip.contains(event.target)) {
        this.closeTooltip();
      }
    }
  }
};
</script>

<style scoped>
.help-tooltip {
  display: inline-block;
  position: relative;
}

.help-tooltip-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  outline: none;
}

.help-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground-subdued);
  font-size: 14px;
  transition: all 0.2s ease;
}

.help-icon:hover {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.help-tooltip-content {
  position: absolute;
  z-index: 1000;
  background-color: var(--theme--background);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--theme--border-color);
  max-width: 300px;
  min-width: 200px;
}

.help-tooltip-content.small {
  max-width: 200px;
}

.help-tooltip-content.medium {
  max-width: 300px;
}

.help-tooltip-content.large {
  max-width: 400px;
}

.help-tooltip-content.top {
  bottom: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
}

.help-tooltip-content.right {
  left: calc(100% + 10px);
  top: 50%;
  transform: translateY(-50%);
}

.help-tooltip-content.bottom {
  top: calc(100% + 10px);
  left: 50%;
  transform: translateX(-50%);
}

.help-tooltip-content.left {
  right: calc(100% + 10px);
  top: 50%;
  transform: translateY(-50%);
}

.help-tooltip-arrow {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: var(--theme--background);
  transform: rotate(45deg);
  border: 1px solid var(--theme--border-color);
}

.help-tooltip-arrow.top {
  bottom: -6px;
  left: 50%;
  margin-left: -5px;
  border-top: none;
  border-left: none;
}

.help-tooltip-arrow.right {
  left: -6px;
  top: 50%;
  margin-top: -5px;
  border-right: none;
  border-bottom: none;
}

.help-tooltip-arrow.bottom {
  top: -6px;
  left: 50%;
  margin-left: -5px;
  border-bottom: none;
  border-right: none;
}

.help-tooltip-arrow.left {
  right: -6px;
  top: 50%;
  margin-top: -5px;
  border-left: none;
  border-top: none;
}

.help-tooltip-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-bottom: 1px solid var(--theme--border-color);
}

.help-tooltip-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.help-tooltip-close {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  padding: 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.help-tooltip-body {
  padding: 12px;
  font-size: 13px;
  line-height: 1.5;
  color: var(--theme--foreground);
}

.help-tooltip-footer {
  padding: 8px 12px;
  border-top: 1px solid var(--theme--border-color);
  font-size: 12px;
}

/* Transitions */
.tooltip-fade-enter-active,
.tooltip-fade-leave-active {
  transition: opacity 0.2s, transform 0.2s;
}

.tooltip-fade-enter,
.tooltip-fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
