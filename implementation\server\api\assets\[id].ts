/**
 * Asset Detail API
 *
 * This module provides API endpoints for asset detail operations.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { logger } from '../../shared/utils/logger';
import { errorHandler } from '../../shared/utils/error-handler';
import { assetService, AssetType } from '../../services/asset/asset-service';

// Create logger instance
const apiLogger = new Logger();

/**
 * Get asset handler
 *
 * @param req Request
 * @param res Response
 */
async function getAsset(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { id } = req.query;

    // Get asset
    try {
      const asset = await assetService.getAsset(id as string);

      // Return success response
      res.status(200).json({
        success: true,
        data: asset,
      });
    } catch (error) {
      logger.error('Error getting asset', { error });
      res.status(404).json({
        success: false,
        error: {
          code: 'ASSET_NOT_FOUND',
          message: 'Asset not found',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in getAsset', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Update asset handler
 *
 * @param req Request
 * @param res Response
 */
async function updateAsset(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { id } = req.query;
    const { name, description, metadata, tags } = req.body;

    // Get asset to check authorization
    try {
      const asset = await assetService.getAsset(id as string);

      // Check if user is authorized to update this asset
      const { data: vendorData, error: vendorError } = await supabase
        .from('vendors')
        .select('user_id')
        .eq('id', asset.vendor_id)
        .single();

      if (vendorError) {
        logger.error('Error getting vendor for authorization check', { error: vendorError });
        res.status(500).json({
          success: false,
          error: {
            code: 'VENDOR_NOT_FOUND',
            message: 'Vendor not found',
          },
        });
        return;
      }

      if (session.user.id !== vendorData.user_id && session.user.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'You are not authorized to update this asset',
          },
        });
        return;
      }

      // Update asset
      const updatedAsset = await assetService.updateAsset(id as string, {
        name,
        description,
        metadata,
        tags,
      });

      // Return success response
      res.status(200).json({
        success: true,
        data: updatedAsset,
      });
    } catch (error) {
      logger.error('Error updating asset', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'UPDATE_ERROR',
          message: 'Failed to update asset',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in updateAsset', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Delete asset handler
 *
 * @param req Request
 * @param res Response
 */
async function deleteAsset(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Unauthorized',
        },
      });
      return;
    }

    const { id } = req.query;

    // Get asset to check authorization
    try {
      const asset = await assetService.getAsset(id as string);

      // Check if user is authorized to delete this asset
      const { data: vendorData, error: vendorError } = await supabase
        .from('vendors')
        .select('user_id')
        .eq('id', asset.vendor_id)
        .single();

      if (vendorError) {
        logger.error('Error getting vendor for authorization check', { error: vendorError });
        res.status(500).json({
          success: false,
          error: {
            code: 'VENDOR_NOT_FOUND',
            message: 'Vendor not found',
          },
        });
        return;
      }

      if (session.user.id !== vendorData.user_id && session.user.role !== 'admin') {
        res.status(403).json({
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: 'You are not authorized to delete this asset',
          },
        });
        return;
      }

      // Delete asset
      await assetService.deleteAsset(id as string);

      // Return success response
      res.status(200).json({
        success: true,
        data: {
          id,
          deleted: true,
        },
      });
    } catch (error) {
      logger.error('Error deleting asset', { error });
      res.status(500).json({
        success: false,
        error: {
          code: 'DELETE_ERROR',
          message: 'Failed to delete asset',
        },
      });
    }
  } catch (error) {
    logger.error('Unexpected error in deleteAsset', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error',
      },
    });
  }
}

/**
 * Handler for asset detail API endpoints
 *
 * @param req Request
 * @param res Response
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  switch (req.method) {
    case 'GET':
      await getAsset(req, res);
      break;
    case 'PUT':
      await updateAsset(req, res);
      break;
    case 'DELETE':
      await deleteAsset(req, res);
      break;
    default:
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: `Method ${req.method} not allowed`,
        },
      });
  }
}
