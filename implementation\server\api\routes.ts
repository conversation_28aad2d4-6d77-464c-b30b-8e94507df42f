import { Router } from 'express';
import multer from 'multer';
import { processAsset } from './assets/process';
import {
  processAssetChunked,
  getProcessingJobStatus,
  getProcessingJobsByAsset,
} from './assets/process-chunked';
import { uploadChunk } from './assets/upload-chunk';
import { authenticate } from './middleware/auth';
import { validateRequest } from './middleware/validation';
import { rateLimiter } from './middleware/rate-limiter';
import analyticsRouter from './analytics/index';
import cdnRouter from './assets/cdn';
import progressiveRouter from './assets/progressive';
import prefetchRouter from './assets/prefetch';

// Initialize router
const router = Router();

// Initialize multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
});

// Asset processing routes
router.post(
  '/assets/:assetId/process',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 10 }), // 10 requests per minute
  validateRequest,
  processAsset,
);

router.post(
  '/assets/:assetId/process-chunked',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 5 }), // 5 requests per minute
  validateRequest,
  processAssetChunked,
);

router.get(
  '/assets/processing-jobs/:jobId',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests per minute
  getProcessingJobStatus,
);

router.get(
  '/assets/:assetId/processing-jobs',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests per minute
  getProcessingJobsByAsset,
);

// Chunk upload route
router.post(
  '/assets/upload-chunk',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 60 }), // 60 requests per minute
  upload.single('file'),
  validateRequest,
  uploadChunk,
);

// Analytics routes
router.use('/analytics', analyticsRouter);

// CDN routes
router.use('/assets/cdn', cdnRouter);

// Progressive loading routes
router.use('/assets/progressive', progressiveRouter);

// Prefetching routes
router.use('/assets/prefetch', prefetchRouter);

export default router;
