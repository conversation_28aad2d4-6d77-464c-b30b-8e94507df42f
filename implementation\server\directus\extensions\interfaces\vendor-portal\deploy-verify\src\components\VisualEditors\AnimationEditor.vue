<template>
  <CollaborationProvider
    v-if="enableCollaboration"
    :enabled="true"
    :roomId="collaborationRoomId"
    :userId="userId"
    :userName="userName"
    :wsUrl="collaborationWsUrl"
    :animations="animations"
    @update="handleCollaborativeUpdate"
    @connected="handleCollaborationConnected"
    @disconnected="handleCollaborationDisconnected"
    @error="handleCollaborationError"
    @toggle="handleCollaborationToggle"
  >
    <div class="animation-editor" @mousemove="handleMouseMove">
      <!-- Basic layout containers -->
      <div class="editor-header">
        <div class="editor-title-section">
          <h3 class="editor-title">Animation Editor</h3>
          <div class="editor-subtitle">
            Create and manage animations for your 3D objects
            <Tooltip content="Create, edit, and manage animations for your 3D objects. Add tracks, keyframes, and control timing.">
              <template #trigger>
                <i class="material-icons help-icon">help_outline</i>
              </template>
            </Tooltip>
          </div>
        </div>
        <div class="editor-actions">
          <button class="action-button" @click="saveAnimations" :disabled="!hasChanges || isLoading">
            <i class="material-icons">{{ isLoading ? 'hourglass_empty' : 'save' }}</i>
            <span>{{ isLoading ? 'Saving...' : 'Save' }}</span>
            <Tooltip content="Save all animations to the server">
              <template #trigger>
                <i class="material-icons help-icon">help_outline</i>
              </template>
            </Tooltip>
          </button>
          <button class="action-button" @click="resetAnimations" :disabled="!hasChanges || isLoading">
            <i class="material-icons">refresh</i>
            <span>Reset</span>
            <Tooltip content="Reset all changes to the last saved state">
              <template #trigger>
                <i class="material-icons help-icon">help_outline</i>
              </template>
            </Tooltip>
          </button>
          <button class="action-button" @click="openBlendDialog" :disabled="animations.length < 2 || isLoading">
            <i class="material-icons">shuffle</i>
            <span>Blend Animations</span>
            <Tooltip content="Create a new animation by blending two existing animations">
              <template #trigger>
                <i class="material-icons help-icon">help_outline</i>
              </template>
            </Tooltip>
          </button>
          <button class="action-button" @click="toggleCollaboration">
            <i class="material-icons">{{ enableCollaboration ? 'people' : 'person' }}</i>
            <span>{{ enableCollaboration ? 'Collaborative' : 'Solo' }}</span>
            <Tooltip :content="enableCollaboration ? 'Disable collaborative editing' : 'Enable collaborative editing'">
              <template #trigger>
                <i class="material-icons help-icon">help_outline</i>
              </template>
            </Tooltip>
          </button>
        </div>
      </div>

    <!-- Error message -->
    <div v-if="error" class="error-message">
      <i class="material-icons">error</i>
      <span>{{ error }}</span>
      <button class="error-close" @click="error = null">
        <i class="material-icons">close</i>
      </button>
    </div>

    <div class="editor-content">
      <div class="editor-sidebar">
        <div class="sidebar-section">
          <h4 class="sidebar-title">Animations</h4>

          <div class="animation-list" ref="animationList" @scroll="useVirtualScrolling && handleScroll">
            <div v-if="animations.length === 0" class="no-animations">
              <i class="material-icons">movie</i>
              <p>No animations created yet</p>
              <p>Create a new animation to get started</p>
            </div>

            <!-- Virtual scrolling container -->
            <div v-if="useVirtualScrolling" class="virtual-scroll-container" :style="virtualListRenderer?.getVisibleItems().containerStyle">
              <div
                v-for="animation in visibleAnimations"
                :key="animation.id"
                class="animation-item"
                :class="{ active: selectedAnimationId === animation.id }"
                :style="animation.style"
                @click="selectAnimation(animation.id)"
              >
                <div class="animation-icon">
                  <i class="material-icons">movie</i>
                </div>
                <div class="animation-info">
                  <div class="animation-name">{{ animation.name }}</div>
                  <div class="animation-duration">{{ formatDuration(animation.duration) }}</div>
                </div>
                <div class="animation-actions">
                  <button class="animation-action-button" @click.stop="deleteAnimation(animation.id)">
                    <i class="material-icons">delete</i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Regular scrolling for smaller lists -->
            <template v-else>
              <div
                v-for="animation in animations"
                :key="animation.id"
                class="animation-item"
                :class="{ active: selectedAnimationId === animation.id }"
                @click="selectAnimation(animation.id)"
              >
                <div class="animation-icon">
                  <i class="material-icons">movie</i>
                </div>
                <div class="animation-info">
                  <div class="animation-name">{{ animation.name }}</div>
                  <div class="animation-duration">{{ formatDuration(animation.duration) }}</div>
                </div>
                <div class="animation-actions">
                  <button class="animation-action-button" @click.stop="deleteAnimation(animation.id)">
                    <i class="material-icons">delete</i>
                  </button>
                </div>
              </div>
            </template>
          </div>

          <button class="add-animation-button" @click="createAnimation">
            <i class="material-icons">add</i>
            <span>Create New Animation</span>
          </button>
        </div>
      </div>

      <div class="editor-main">
        <div v-if="!selectedAnimation" class="no-animation-selected">
          <i class="material-icons">movie</i>
          <p>Select an animation to edit or create a new one</p>
        </div>

        <div v-else class="animation-editor-content">
          <div class="timeline-container">
            <div class="timeline-header">
              <h4 class="timeline-title">Timeline</h4>
              <div class="timeline-controls">
                <button class="timeline-control-button" @click="playAnimation" :disabled="isPlaying">
                  <i class="material-icons">play_arrow</i>
                </button>
                <button class="timeline-control-button" @click="pauseAnimation" :disabled="!isPlaying">
                  <i class="material-icons">pause</i>
                </button>
                <button class="timeline-control-button" @click="stopAnimation" :disabled="!isPlaying && currentTime === 0">
                  <i class="material-icons">stop</i>
                </button>
                <div class="timeline-time-display">{{ formatTime(currentTime) }} / {{ formatTime(selectedAnimation.duration) }}</div>
              </div>
            </div>

            <div class="timeline-content">
              <div class="timeline-ruler" ref="timelineRuler">
                <div class="timeline-ruler-markers">
                  <div
                    v-for="marker in timeMarkers"
                    :key="marker.time"
                    class="timeline-marker"
                    :style="{ left: `${marker.position}px` }"
                  >
                    <div class="timeline-marker-line"></div>
                    <div class="timeline-marker-label">{{ marker.label }}</div>
                  </div>
                </div>
                <div
                  class="timeline-scrubber"
                  :style="{ left: `${scrubberPosition}px` }"
                  @mousedown="startScrubbing"
                ></div>
              </div>

              <div class="timeline-tracks">
                <div
                  v-for="(track, index) in selectedAnimation.tracks"
                  :key="track.id"
                  class="timeline-track"
                >
                  <div class="track-header">
                    <div class="track-name">{{ track.name }}</div>
                    <div class="track-type">{{ track.type }}</div>
                  </div>
                  <div class="track-content" :style="{ width: `${timelineWidth}px` }">
                    <div
                      v-for="keyframe in track.keyframes"
                      :key="keyframe.id"
                      class="keyframe"
                      :class="{ 'keyframe-selected': selectedKeyframeId === keyframe.id }"
                      :style="{ left: `${timeToPosition(keyframe.time)}px` }"
                      @click.stop="selectKeyframe(keyframe.id, track.id)"
                    ></div>
                  </div>
                </div>

                <div v-if="selectedAnimation.tracks.length === 0" class="no-tracks">
                  <p>No animation tracks yet</p>
                  <button class="add-track-button" @click="addTrack">
                    <i class="material-icons">add</i>
                    <span>Add Track</span>
                  </button>
                </div>
                <div v-else class="add-track-container">
                  <button class="add-track-button" @click="addTrack">
                    <i class="material-icons">add</i>
                    <span>Add Track</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="properties-panel">
            <div class="properties-header">
              <h4 class="properties-title">Properties</h4>
              <div class="properties-tabs">
                <button
                  class="properties-tab"
                  :class="{ active: activePropertiesTab === 'animation' }"
                  @click="activePropertiesTab = 'animation'"
                >
                  Animation
                </button>
                <button
                  class="properties-tab"
                  :class="{ active: activePropertiesTab === 'keyframe' }"
                  @click="activePropertiesTab = 'keyframe'"
                  :disabled="!selectedKeyframeId"
                >
                  Keyframe
                </button>
              </div>
            </div>

            <div class="properties-content">
              <!-- Animation Properties -->
              <div v-if="activePropertiesTab === 'animation'" class="animation-properties">
                <div class="property-group">
                  <h5 class="property-group-title">Animation Settings</h5>

                  <div class="property-field">
                    <label for="animation-name">Name</label>
                    <input
                      id="animation-name"
                      type="text"
                      v-model="selectedAnimation.name"
                      @input="updateAnimation"
                    />
                  </div>

                  <div class="property-field">
                    <label for="animation-duration">Duration (seconds)</label>
                    <input
                      id="animation-duration"
                      type="number"
                      min="0.1"
                      step="0.1"
                      v-model.number="selectedAnimation.duration"
                      @input="updateAnimationDuration"
                    />
                  </div>

                  <div class="property-field">
                    <label for="animation-loop">Loop Animation</label>
                    <div class="toggle-switch">
                      <input
                        id="animation-loop"
                        type="checkbox"
                        v-model="selectedAnimation.loop"
                        @change="updateAnimation"
                      />
                      <label for="animation-loop"></label>
                    </div>
                  </div>
                </div>

                <div class="property-group">
                  <h5 class="property-group-title">Playback Settings</h5>

                  <div class="property-field">
                    <label for="animation-speed">Playback Speed</label>
                    <div class="range-input-container">
                      <input
                        id="animation-speed"
                        type="range"
                        min="0.1"
                        max="2"
                        step="0.1"
                        v-model.number="playbackSpeed"
                      />
                      <span class="range-value">{{ playbackSpeed.toFixed(1) }}x</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Keyframe Properties -->
              <div v-else-if="activePropertiesTab === 'keyframe' && selectedKeyframe" class="keyframe-properties">
                <div class="property-group">
                  <h5 class="property-group-title">Keyframe Settings</h5>

                  <div class="property-field">
                    <label for="keyframe-time">Time (seconds)</label>
                    <input
                      id="keyframe-time"
                      type="number"
                      min="0"
                      :max="selectedAnimation.duration"
                      step="0.01"
                      v-model.number="selectedKeyframe.time"
                      @input="updateKeyframeTime"
                    />
                  </div>

                  <div class="property-field">
                    <label for="keyframe-easing">Easing</label>
                    <div class="easing-selector">
                      <select
                        id="keyframe-easing"
                        v-model="selectedKeyframe.easing"
                        @change="updateKeyframe"
                      >
                        <option value="linear">Linear</option>
                        <option value="easeInQuad">Ease In Quad</option>
                        <option value="easeOutQuad">Ease Out Quad</option>
                        <option value="easeInOutQuad">Ease In Out Quad</option>
                        <option value="easeInCubic">Ease In Cubic</option>
                        <option value="easeOutCubic">Ease Out Cubic</option>
                        <option value="easeInOutCubic">Ease In Out Cubic</option>
                        <option value="easeInElastic">Ease In Elastic</option>
                        <option value="easeOutElastic">Ease Out Elastic</option>
                        <option value="easeInOutElastic">Ease In Out Elastic</option>
                        <option value="easeInBounce">Ease In Bounce</option>
                        <option value="easeOutBounce">Ease Out Bounce</option>
                        <option value="easeInOutBounce">Ease In Out Bounce</option>
                        <option value="custom">Custom Curve</option>
                      </select>
                      <Tooltip content="Easing functions control how values change between keyframes. Different easing types create different animation feels.">
                        <template #trigger>
                          <i class="material-icons help-icon">help_outline</i>
                        </template>
                      </Tooltip>
                    </div>

                    <!-- Easing Preview -->
                    <div class="easing-preview">
                      <svg width="100%" height="60" viewBox="0 0 100 60" preserveAspectRatio="none">
                        <path
                          :d="getEasingPath(selectedKeyframe.easing)"
                          stroke="var(--theme--primary)"
                          stroke-width="2"
                          fill="none"
                        />
                        <line x1="0" y1="60" x2="100" y2="60" stroke="var(--theme--border-color)" stroke-width="1" />
                        <line x1="0" y1="0" x2="0" y2="60" stroke="var(--theme--border-color)" stroke-width="1" />
                      </svg>
                    </div>
                  </div>

                  <!-- Custom Curve Editor (shown only when easing is set to "custom") -->
                  <div class="property-field" v-if="selectedKeyframe.easing === 'custom'">
                    <label>Custom Curve</label>
                    <div class="custom-curve-editor">
                      <svg width="100%" height="100" viewBox="0 0 100 100" preserveAspectRatio="none" @mousedown="startDraggingControlPoint">
                        <!-- Background grid -->
                        <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                          <path d="M 10 0 L 0 0 0 10" fill="none" stroke="var(--theme--border-color)" stroke-width="0.5" />
                        </pattern>
                        <rect width="100" height="100" fill="url(#grid)" />

                        <!-- Bezier curve -->
                        <path
                          :d="`M0,100 C${selectedKeyframe.curve?.x1 * 100 || 25},${100 - (selectedKeyframe.curve?.y1 * 100 || 25)} ${selectedKeyframe.curve?.x2 * 100 || 75},${100 - (selectedKeyframe.curve?.y2 * 100 || 75)} 100,0`"
                          stroke="var(--theme--primary)"
                          stroke-width="2"
                          fill="none"
                        />

                        <!-- Control points -->
                        <circle
                          :cx="selectedKeyframe.curve?.x1 * 100 || 25"
                          :cy="100 - (selectedKeyframe.curve?.y1 * 100 || 25)"
                          r="4"
                          fill="var(--theme--primary)"
                          stroke="white"
                          stroke-width="1"
                          data-point="1"
                        />
                        <circle
                          :cx="selectedKeyframe.curve?.x2 * 100 || 75"
                          :cy="100 - (selectedKeyframe.curve?.y2 * 100 || 75)"
                          r="4"
                          fill="var(--theme--primary)"
                          stroke="white"
                          stroke-width="1"
                          data-point="2"
                        />

                        <!-- Lines from endpoints to control points -->
                        <line
                          x1="0"
                          y1="100"
                          :x2="selectedKeyframe.curve?.x1 * 100 || 25"
                          :y2="100 - (selectedKeyframe.curve?.y1 * 100 || 25)"
                          stroke="var(--theme--foreground-subdued)"
                          stroke-width="1"
                          stroke-dasharray="2,2"
                        />
                        <line
                          x1="100"
                          y1="0"
                          :x2="selectedKeyframe.curve?.x2 * 100 || 75"
                          :y2="100 - (selectedKeyframe.curve?.y2 * 100 || 75)"
                          stroke="var(--theme--foreground-subdued)"
                          stroke-width="1"
                          stroke-dasharray="2,2"
                        />
                      </svg>

                      <div class="curve-controls">
                        <button class="curve-control-button" @click="resetCustomCurve">Reset</button>
                        <button class="curve-control-button" @click="applyCustomCurve">Apply</button>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="property-group" v-if="selectedTrack && selectedTrack.type === 'transform'">
                  <h5 class="property-group-title">Transform Properties</h5>

                  <div class="property-field">
                    <label>Position</label>
                    <div class="vector-input">
                      <div class="vector-component">
                        <label>X</label>
                        <input
                          type="number"
                          step="0.1"
                          v-model.number="selectedKeyframe.value.position.x"
                          @input="updateKeyframe"
                        />
                      </div>
                      <div class="vector-component">
                        <label>Y</label>
                        <input
                          type="number"
                          step="0.1"
                          v-model.number="selectedKeyframe.value.position.y"
                          @input="updateKeyframe"
                        />
                      </div>
                      <div class="vector-component">
                        <label>Z</label>
                        <input
                          type="number"
                          step="0.1"
                          v-model.number="selectedKeyframe.value.position.z"
                          @input="updateKeyframe"
                        />
                      </div>
                    </div>
                  </div>

                  <div class="property-field">
                    <label>Rotation</label>
                    <div class="vector-input">
                      <div class="vector-component">
                        <label>X</label>
                        <input
                          type="number"
                          step="1"
                          v-model.number="selectedKeyframe.value.rotation.x"
                          @input="updateKeyframe"
                        />
                      </div>
                      <div class="vector-component">
                        <label>Y</label>
                        <input
                          type="number"
                          step="1"
                          v-model.number="selectedKeyframe.value.rotation.y"
                          @input="updateKeyframe"
                        />
                      </div>
                      <div class="vector-component">
                        <label>Z</label>
                        <input
                          type="number"
                          step="1"
                          v-model.number="selectedKeyframe.value.rotation.z"
                          @input="updateKeyframe"
                        />
                      </div>
                    </div>
                  </div>

                  <div class="property-field">
                    <label>Scale</label>
                    <div class="vector-input">
                      <div class="vector-component">
                        <label>X</label>
                        <input
                          type="number"
                          min="0.01"
                          step="0.1"
                          v-model.number="selectedKeyframe.value.scale.x"
                          @input="updateKeyframe"
                        />
                      </div>
                      <div class="vector-component">
                        <label>Y</label>
                        <input
                          type="number"
                          min="0.01"
                          step="0.1"
                          v-model.number="selectedKeyframe.value.scale.y"
                          @input="updateKeyframe"
                        />
                      </div>
                      <div class="vector-component">
                        <label>Z</label>
                        <input
                          type="number"
                          min="0.01"
                          step="0.1"
                          v-model.number="selectedKeyframe.value.scale.z"
                          @input="updateKeyframe"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div class="property-group" v-if="selectedTrack && selectedTrack.type === 'visibility'">
                  <h5 class="property-group-title">Visibility Properties</h5>

                  <div class="property-field">
                    <label for="keyframe-visibility">Visible</label>
                    <div class="toggle-switch">
                      <input
                        id="keyframe-visibility"
                        type="checkbox"
                        v-model="selectedKeyframe.value.visible"
                        @change="updateKeyframe"
                      />
                      <label for="keyframe-visibility"></label>
                    </div>
                  </div>

                  <div class="property-field">
                    <label for="keyframe-opacity">Opacity</label>
                    <div class="range-input-container">
                      <input
                        id="keyframe-opacity"
                        type="range"
                        min="0"
                        max="1"
                        step="0.01"
                        v-model.number="selectedKeyframe.value.opacity"
                        @input="updateKeyframe"
                      />
                      <span class="range-value">{{ Math.round(selectedKeyframe.value.opacity * 100) }}%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div v-else class="no-selection">
                <p>Select a keyframe to edit its properties</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Animation Blend Dialog -->
    <div v-if="showBlendDialog" class="blend-dialog-overlay">
      <div class="blend-dialog">
        <div class="blend-dialog-header">
          <h4 class="blend-dialog-title">Blend Animations</h4>
          <button class="blend-dialog-close" @click="closeBlendDialog">
            <i class="material-icons">close</i>
          </button>
        </div>
        <div class="blend-dialog-content">
          <div class="blend-form">
            <div class="blend-form-group">
              <label for="blend-animation-1">First Animation</label>
              <select id="blend-animation-1" v-model="blendSettings.animation1Id">
                <option v-for="animation in animations" :key="animation.id" :value="animation.id">
                  {{ animation.name }}
                </option>
              </select>
            </div>

            <div class="blend-form-group">
              <label for="blend-animation-2">Second Animation</label>
              <select id="blend-animation-2" v-model="blendSettings.animation2Id">
                <option v-for="animation in animations" :key="animation.id" :value="animation.id">
                  {{ animation.name }}
                </option>
              </select>
            </div>

            <div class="blend-form-group">
              <label for="blend-factor">Blend Factor: {{ blendSettings.factor.toFixed(2) }}</label>
              <input
                id="blend-factor"
                type="range"
                min="0"
                max="1"
                step="0.01"
                v-model.number="blendSettings.factor"
              />
              <div class="blend-factor-labels">
                <span>Animation 1</span>
                <span>Animation 2</span>
              </div>
            </div>

            <div class="blend-form-group">
              <label for="blend-name">New Animation Name</label>
              <input
                id="blend-name"
                type="text"
                v-model="blendSettings.name"
                placeholder="Blended Animation"
              />
            </div>
          </div>

          <div class="blend-preview">
            <h5 class="blend-preview-title">Preview</h5>
            <div class="blend-preview-content">
              <!-- Preview visualization would go here -->
              <div class="blend-preview-placeholder">
                <div class="blend-preview-animation">
                  <div class="blend-preview-timeline">
                    <div
                      v-for="(track, index) in previewBlendedAnimation.tracks"
                      :key="index"
                      class="blend-preview-track"
                    >
                      <div class="blend-preview-track-name">{{ track.name }}</div>
                      <div class="blend-preview-track-keyframes">
                        <div
                          v-for="keyframe in track.keyframes"
                          :key="keyframe.id"
                          class="blend-preview-keyframe"
                          :style="{ left: `${(keyframe.time / previewBlendedAnimation.duration) * 100}%` }"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="blend-dialog-footer">
          <button class="blend-dialog-button secondary" @click="closeBlendDialog">Cancel</button>
          <button class="blend-dialog-button primary" @click="createBlendedAnimation" :disabled="!canCreateBlend">
            Create Blended Animation
          </button>
        </div>
      </div>
    </div>
    <!-- Help Panel -->
    <HelpPanel
      title="Animation Editor Help"
      :tabs="[
        {
          title: 'Basic Usage',
          content: `
            <h4>Getting Started</h4>
            <p>The Animation Editor allows you to create and manage animations for your 3D objects.</p>
            <ul>
              <li><strong>Create Animation:</strong> Click the "Create Animation" button in the sidebar.</li>
              <li><strong>Select Animation:</strong> Click on an animation in the sidebar to select it.</li>
              <li><strong>Add Track:</strong> Click "Add Track" to add a new animation track.</li>
              <li><strong>Add Keyframe:</strong> Click on the timeline to add a keyframe at that position.</li>
              <li><strong>Edit Properties:</strong> Select a keyframe to edit its properties in the panel.</li>
              <li><strong>Save:</strong> Click the "Save" button to save your changes.</li>
            </ul>
          `
        },
        {
          title: 'Animation Blending',
          content: `
            <h4>Blending Animations</h4>
            <p>Animation blending allows you to create new animations by combining existing ones.</p>
            <ol>
              <li>Click the "Blend Animations" button in the header.</li>
              <li>Select two animations to blend.</li>
              <li>Adjust the blend factor to control how much each animation contributes.</li>
              <li>Name your blended animation.</li>
              <li>Click "Create Blended Animation" to generate a new animation.</li>
            </ol>
            <p>This is useful for creating transitions, variations, or combining partial animations.</p>
          `
        },
        {
          title: 'Easing & Curves',
          content: `
            <h4>Easing Functions</h4>
            <p>Easing functions control how values change between keyframes.</p>
            <p>Available easing types:</p>
            <ul>
              <li><strong>Linear:</strong> Constant rate of change</li>
              <li><strong>Ease In:</strong> Slow start, fast end</li>
              <li><strong>Ease Out:</strong> Fast start, slow end</li>
              <li><strong>Ease In Out:</strong> Slow start, fast middle, slow end</li>
              <li><strong>Bounce:</strong> Bouncing effect at the end</li>
              <li><strong>Elastic:</strong> Elastic effect at the end</li>
              <li><strong>Custom:</strong> Create your own curve</li>
            </ul>
            <p>Select a keyframe and choose an easing type in the properties panel.</p>
          `
        },
        {
          title: 'Tips & Shortcuts',
          content: `
            <h4>Keyboard Shortcuts</h4>
            <ul>
              <li><strong>Space:</strong> Play/Pause animation</li>
              <li><strong>Home:</strong> Go to start of animation</li>
              <li><strong>End:</strong> Go to end of animation</li>
              <li><strong>Delete:</strong> Delete selected keyframe</li>
              <li><strong>Ctrl+Z:</strong> Undo</li>
              <li><strong>Ctrl+Y:</strong> Redo</li>
              <li><strong>Ctrl+S:</strong> Save animations</li>
            </ul>

            <h4>Best Practices</h4>
            <ul>
              <li>Keep animations simple for better reusability</li>
              <li>Use meaningful names for animations and tracks</li>
              <li>Test animations in context</li>
              <li>Use as few keyframes as necessary</li>
              <li>Save your work frequently</li>
            </ul>
          `
        }
      ]"
      documentationUrl="docs/user-guides/ANIMATION_EDITOR_GUIDE.md"
    />
    </div>
  </CollaborationProvider>

  <!-- Non-collaborative mode -->
  <div v-else class="animation-editor">
    <!-- Basic layout containers -->
    <div class="editor-header">
      <div class="editor-title-section">
        <h3 class="editor-title">Animation Editor</h3>
        <div class="editor-subtitle">
          Create and manage animations for your 3D objects
          <Tooltip content="Create, edit, and manage animations for your 3D objects. Add tracks, keyframes, and control timing.">
            <template #trigger>
              <i class="material-icons help-icon">help_outline</i>
            </template>
          </Tooltip>
        </div>
      </div>
      <div class="editor-actions">
        <button class="action-button" @click="saveAnimations" :disabled="!hasChanges || isLoading">
          <i class="material-icons">{{ isLoading ? 'hourglass_empty' : 'save' }}</i>
          <span>{{ isLoading ? 'Saving...' : 'Save' }}</span>
          <Tooltip content="Save all animations to the server">
            <template #trigger>
              <i class="material-icons help-icon">help_outline</i>
            </template>
          </Tooltip>
        </button>
        <button class="action-button" @click="resetAnimations" :disabled="!hasChanges || isLoading">
          <i class="material-icons">refresh</i>
          <span>Reset</span>
          <Tooltip content="Reset all changes to the last saved state">
            <template #trigger>
              <i class="material-icons help-icon">help_outline</i>
            </template>
          </Tooltip>
        </button>
        <button class="action-button" @click="openBlendDialog" :disabled="animations.length < 2 || isLoading">
          <i class="material-icons">shuffle</i>
          <span>Blend Animations</span>
          <Tooltip content="Create a new animation by blending two existing animations">
            <template #trigger>
              <i class="material-icons help-icon">help_outline</i>
            </template>
          </Tooltip>
        </button>
        <button class="action-button" @click="toggleCollaboration">
          <i class="material-icons">{{ enableCollaboration ? 'people' : 'person' }}</i>
          <span>{{ enableCollaboration ? 'Collaborative' : 'Solo' }}</span>
          <Tooltip :content="enableCollaboration ? 'Disable collaborative editing' : 'Enable collaborative editing'">
            <template #trigger>
              <i class="material-icons help-icon">help_outline</i>
            </template>
          </Tooltip>
        </button>
      </div>
    </div>

    <!-- Error message -->
    <div v-if="error" class="error-message">
      <i class="material-icons">error</i>
      <span>{{ error }}</span>
      <button class="error-close" @click="error = null">
        <i class="material-icons">close</i>
      </button>
    </div>

    <div class="editor-content">
      <div class="editor-sidebar">
        <div class="sidebar-section">
          <h4 class="sidebar-title">Animations</h4>

          <div class="animation-list" ref="animationList" @scroll="useVirtualScrolling && handleScroll">
            <div v-if="animations.length === 0" class="no-animations">
              <i class="material-icons">movie</i>
              <p>No animations created yet</p>
              <p>Create a new animation to get started</p>
            </div>

            <!-- Virtual scrolling container -->
            <div v-if="useVirtualScrolling" class="virtual-scroll-container" :style="virtualListRenderer?.getVisibleItems().containerStyle">
              <div
                v-for="animation in visibleAnimations"
                :key="animation.id"
                class="animation-item"
                :class="{ active: selectedAnimationId === animation.id }"
                :style="animation.style"
                @click="selectAnimation(animation.id)"
              >
                <div class="animation-icon">
                  <i class="material-icons">movie</i>
                </div>
                <div class="animation-info">
                  <div class="animation-name">{{ animation.name }}</div>
                  <div class="animation-duration">{{ formatDuration(animation.duration) }}</div>
                </div>
                <div class="animation-actions">
                  <button class="animation-action-button" @click.stop="deleteAnimation(animation.id)">
                    <i class="material-icons">delete</i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Regular scrolling for smaller lists -->
            <template v-else>
              <div
                v-for="animation in animations"
                :key="animation.id"
                class="animation-item"
                :class="{ active: selectedAnimationId === animation.id }"
                @click="selectAnimation(animation.id)"
              >
                <div class="animation-icon">
                  <i class="material-icons">movie</i>
                </div>
                <div class="animation-info">
                  <div class="animation-name">{{ animation.name }}</div>
                  <div class="animation-duration">{{ formatDuration(animation.duration) }}</div>
                </div>
                <div class="animation-actions">
                  <button class="animation-action-button" @click.stop="deleteAnimation(animation.id)">
                    <i class="material-icons">delete</i>
                  </button>
                </div>
              </div>
            </template>
          </div>

          <button class="add-animation-button" @click="createAnimation">
            <i class="material-icons">add</i>
            <span>Create New Animation</span>
          </button>
        </div>
      </div>

      <div class="editor-main">
        <!-- Rest of the editor content -->
      </div>
    </div>

    <!-- Help panel -->
    <HelpPanel
      v-if="showHelpPanel"
      @close="showHelpPanel = false"
      :title="'Animation Editor Help'"
      :sections="[
        {
          title: 'Getting Started',
          content: 'Create a new animation using the button in the sidebar. Then add tracks and keyframes to define how objects should animate.'
        },
        {
          title: 'Working with Keyframes',
          content: 'Keyframes define the state of an object at specific points in time. Add keyframes at different times with different values to create animation.'
        },
        {
          title: 'Animation Blending',
          content: 'Blend two animations together to create a new animation that combines their properties.'
        }
      ]"
      documentationUrl="docs/user-guides/ANIMATION_EDITOR_GUIDE.md"
    />
  </div>
</template>

<script>
import AnimationService from '../../services/AnimationService';
import AnimationUtils from '../../utils/AnimationUtils';
import EasingFunctions from '../../utils/EasingFunctions';
import { PerformanceOptimizer, VirtualListRenderer, debounce, throttle } from '../../utils/PerformanceOptimizer';
import Tooltip from '../common/Tooltip.vue';
import HelpPanel from '../common/HelpPanel.vue';
import CollaborationProvider from './CollaborationProvider.vue';

export default {
  name: 'AnimationEditor',
  components: {
    Tooltip,
    HelpPanel,
    CollaborationProvider
  },

  props: {
    vendorId: {
      type: String,
      required: true
    },
    animationId: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      animations: [],
      selectedAnimationId: null,
      selectedKeyframeId: null,
      selectedTrackId: null,
      isLoading: false,
      originalAnimations: null,
      timelineWidth: 800,
      pixelsPerSecond: 100,
      currentTime: 0,
      isPlaying: false,
      playbackSpeed: 1,
      activePropertiesTab: 'animation',
      isDraggingScrubber: false,
      animationInterval: null,
      error: null,
      // Animation blending
      showBlendDialog: false,
      blendSettings: {
        animation1Id: null,
        animation2Id: null,
        factor: 0.5,
        name: 'Blended Animation'
      },
      previewBlendedAnimation: {
        name: '',
        duration: 0,
        tracks: []
      },
      // Custom curve editor
      draggingControlPoint: null,
      // Performance optimization
      cache: new PerformanceOptimizer(50, 10 * 60 * 1000), // 10 minutes TTL
      virtualListRenderer: null,
      scrollTop: 0,
      useVirtualScrolling: false,
      lastRenderTime: 0,
      renderCount: 0,
      performanceMetrics: {
        loadTime: 0,
        renderTime: 0,
        keyframeCount: 0,
        trackCount: 0
      },
      // Collaboration features
      enableCollaboration: false,
      collaborationRoomId: `animation_room_${this.vendorId}`,
      collaborationWsUrl: 'wss://collaboration.mvs-vr.com',
      userId: `user_${Date.now()}`, // Generate a unique user ID
      userName: 'Anonymous User', // Default user name
      lastCursorPosition: { x: 0, y: 0 },
      collaborationStatus: 'disconnected',
      showHelpPanel: false
    };
  },

  computed: {
    /**
     * Get visible animations for virtual scrolling
     * @returns {Array} - Visible animations
     */
    visibleAnimations() {
      if (!this.useVirtualScrolling || !this.virtualListRenderer) {
        return this.animations;
      }

      const result = this.virtualListRenderer.getVisibleItems();
      return result.visibleItems;
    },

    /**
     * Determine if virtual scrolling should be used based on animation count
     * @returns {Boolean} - Whether to use virtual scrolling
     */
    shouldUseVirtualScrolling() {
      return this.animations.length > 20; // Use virtual scrolling for more than 20 animations
    },

    selectedAnimation() {
      return this.animations.find(animation => animation.id === this.selectedAnimationId) || null;
    },

    hasChanges() {
      return this.originalAnimations &&
             JSON.stringify(this.animations) !== JSON.stringify(this.originalAnimations);
    },

    selectedTrack() {
      if (!this.selectedAnimation || !this.selectedTrackId) return null;
      return this.selectedAnimation.tracks.find(track => track.id === this.selectedTrackId) || null;
    },

    selectedKeyframe() {
      if (!this.selectedTrack || !this.selectedKeyframeId) return null;
      return this.selectedTrack.keyframes.find(keyframe => keyframe.id === this.selectedKeyframeId) || null;
    },

    // Animation blending computed properties
    animation1() {
      return this.animations.find(animation => animation.id === this.blendSettings.animation1Id) || null;
    },

    animation2() {
      return this.animations.find(animation => animation.id === this.blendSettings.animation2Id) || null;
    },

    canCreateBlend() {
      return this.animation1 &&
             this.animation2 &&
             this.blendSettings.animation1Id !== this.blendSettings.animation2Id &&
             this.blendSettings.name.trim() !== '';
    },

    timeMarkers() {
      if (!this.selectedAnimation) return [];

      const markers = [];
      const duration = this.selectedAnimation.duration;
      const secondsInterval = duration <= 10 ? 1 : duration <= 30 ? 5 : 10;

      for (let time = 0; time <= duration; time += secondsInterval) {
        markers.push({
          time,
          position: this.timeToPosition(time),
          label: this.formatTime(time)
        });
      }

      return markers;
    },

    scrubberPosition() {
      return this.timeToPosition(this.currentTime);
    },

    // Performance optimization computed properties
    visibleAnimations() {
      if (!this.useVirtualScrolling || !this.virtualListRenderer) {
        return this.animations;
      }

      // Update virtual list renderer with current animations
      this.virtualListRenderer.updateItems(this.animations);

      // Get visible items
      const { visibleItems } = this.virtualListRenderer.getVisibleItems();
      return visibleItems;
    },

    totalKeyframeCount() {
      let count = 0;
      this.animations.forEach(animation => {
        animation.tracks?.forEach(track => {
          count += track.keyframes?.length || 0;
        });
      });
      return count;
    },

    totalTrackCount() {
      let count = 0;
      this.animations.forEach(animation => {
        count += animation.tracks?.length || 0;
      });
      return count;
    },

    shouldUseVirtualScrolling() {
      // Use virtual scrolling if there are more than 50 animations
      // or more than 1000 total keyframes
      return this.animations.length > 50 || this.totalKeyframeCount > 1000;
    }
  },

  created() {
    // Initialize throttled and debounced methods
    this.handleScrubberDragThrottled = throttle(this.handleScrubberDrag, 16); // ~60fps
    this.updateBlendPreviewDebounced = debounce(this.updateBlendPreview, 300);

    // Load data
    this.loadData();
  },

  mounted() {
    // Initialize virtual list renderer with lazy loading
    this.initVirtualListRenderer();

    // Check if we should use virtual scrolling
    this.useVirtualScrolling = this.shouldUseVirtualScrolling;

    // Add scroll event listener for virtual scrolling
    if (this.useVirtualScrolling) {
      this.$refs.animationList?.addEventListener('scroll', this.handleScroll);
    }

    // Load initial data
    this.loadData();
  },

  beforeDestroy() {
    // Clean up event listeners
    window.removeEventListener('mousemove', this.handleScrubberDragThrottled);
    window.removeEventListener('mouseup', this.stopScrubbing);

    // Remove scroll event listener
    if (this.useVirtualScrolling) {
      this.$refs.animationList?.removeEventListener('scroll', this.handleScroll);
    }

    // Clear any running animation
    if (this.animationInterval) {
      clearInterval(this.animationInterval);
    }

    // Clean up resources
    this.cleanupResources();

    // Dispose of enhanced components
    if (this.virtualListRenderer) {
      this.virtualListRenderer.dispose();
    }

    if (this.cache && typeof this.cache.dispose === 'function') {
      this.cache.dispose();
    }
  },

  created() {
    // Try to get user information from local storage
    this.loadUserInfo();
  },

  methods: {
    async loadData() {
      const startTime = performance.now();
      this.isLoading = true;
      this.error = null;

      try {
        // Get the first page of animations with pagination
        const result = await this.loadAnimationsWithPagination(1, 20);

        // If no animations exist, create a default one
        if (result.length === 0) {
          const defaultAnimation = this.createDefaultAnimation();
          this.animations = [defaultAnimation];
        } else {
          this.animations = result;
        }

        this.originalAnimations = JSON.parse(JSON.stringify(this.animations));

        // If animationId is provided, select that animation
        if (this.animationId && this.animations.length > 0) {
          this.selectedAnimationId = this.animationId;
        } else if (this.animations.length > 0) {
          // Otherwise select the first animation
          this.selectedAnimationId = this.animations[0].id;
        }

        // Calculate timeline width based on animation duration
        this.updateTimelineWidth();

        // Add event listeners for scrubbing
        window.addEventListener('mousemove', this.handleScrubberDragThrottled);
        window.addEventListener('mouseup', this.stopScrubbing);

        // Update performance metrics
        this.performanceMetrics.keyframeCount = this.totalKeyframeCount;
        this.performanceMetrics.trackCount = this.totalTrackCount;

        // Check if we should use virtual scrolling
        this.useVirtualScrolling = this.shouldUseVirtualScrolling;

        // Update virtual list renderer with the loaded animations
        if (this.useVirtualScrolling && this.virtualListRenderer) {
          // Get total count from API if available
          try {
            const countResponse = await this.$api.get(`/items/animations?filter[vendor_id][_eq]=${this.vendorId}&aggregate[count]=id&limit=1`);
            const totalCount = countResponse.data?.data?.[0]?.count?.id || null;

            // Update virtual list renderer with total count
            this.virtualListRenderer.updateItems(this.animations, {
              totalItems: totalCount,
              hasMoreItems: totalCount ? totalCount > this.animations.length : true
            });
          } catch (error) {
            console.error('Error getting animation count:', error);
            this.virtualListRenderer.updateItems(this.animations);
          }
        }

      } catch (error) {
        console.error('Error loading animation data:', error);
        this.error = 'Failed to load animations. Please try again.';

        // Create a sample animation for testing if API fails
        this.animations = [this.createDefaultAnimation()];
        this.originalAnimations = JSON.parse(JSON.stringify(this.animations));
        this.selectedAnimationId = this.animations[0].id;
        this.updateTimelineWidth();
      } finally {
        this.isLoading = false;

        // Record load time
        this.performanceMetrics.loadTime = performance.now() - startTime;
      }
    },

    createDefaultAnimation() {
      return {
        id: `animation_${Date.now()}`,
        name: 'New Animation',
        duration: 5,
        loop: false,
        vendor_id: this.vendorId,
        tracks: [
          {
            id: `track_${Date.now()}`,
            name: 'Object Transform',
            type: 'transform',
            targetId: 'object_1',
            keyframes: [
              {
                id: `keyframe_${Date.now()}`,
                time: 0,
                easing: 'linear',
                value: {
                  position: { x: 0, y: 0, z: 0 },
                  rotation: { x: 0, y: 0, z: 0 },
                  scale: { x: 1, y: 1, z: 1 }
                }
              },
              {
                id: `keyframe_${Date.now() + 1}`,
                time: 5,
                easing: 'linear',
                value: {
                  position: { x: 0, y: 0, z: 0 },
                  rotation: { x: 0, y: 0, z: 0 },
                  scale: { x: 1, y: 1, z: 1 }
                }
              }
            ]
          }
        ]
      };
    },

    // Timeline methods
    timeToPosition(time) {
      return time * this.pixelsPerSecond;
    },

    positionToTime(position) {
      return Math.max(0, Math.min(position / this.pixelsPerSecond, this.selectedAnimation.duration));
    },

    updateTimelineWidth() {
      if (this.selectedAnimation) {
        this.timelineWidth = Math.max(800, this.selectedAnimation.duration * this.pixelsPerSecond + 100);
      }
    },

    formatTime(seconds) {
      if (seconds === undefined || seconds === null) return '0:00';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);
      const milliseconds = Math.floor((seconds - Math.floor(seconds)) * 100);

      if (minutes > 0) {
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
      } else {
        return `${remainingSeconds}.${milliseconds.toString().padStart(2, '0')}`;
      }
    },

    formatDuration(seconds) {
      if (!seconds) return '0:00';

      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = Math.floor(seconds % 60);

      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // Playback controls
    playAnimation() {
      if (this.isPlaying) return;

      this.isPlaying = true;
      const startTime = performance.now() - (this.currentTime * 1000);

      this.animationInterval = setInterval(() => {
        const elapsedSeconds = (performance.now() - startTime) / 1000 * this.playbackSpeed;

        if (elapsedSeconds >= this.selectedAnimation.duration) {
          if (this.selectedAnimation.loop) {
            // Loop back to start
            this.currentTime = 0;
            this.playAnimation();
          } else {
            // Stop at end
            this.currentTime = this.selectedAnimation.duration;
            this.stopAnimation();
          }
        } else {
          this.currentTime = elapsedSeconds;
        }
      }, 16); // ~60fps
    },

    pauseAnimation() {
      this.isPlaying = false;
      if (this.animationInterval) {
        clearInterval(this.animationInterval);
        this.animationInterval = null;
      }
    },

    stopAnimation() {
      this.pauseAnimation();
      this.currentTime = 0;
    },

    // Scrubber controls
    startScrubbing(event) {
      this.isDraggingScrubber = true;
      this.pauseAnimation();

      // Calculate initial position
      const timelineRect = this.$refs.timelineRuler.getBoundingClientRect();
      const position = event.clientX - timelineRect.left;
      this.currentTime = this.positionToTime(position);
    },

    handleScrubberDrag(event) {
      if (!this.isDraggingScrubber || !this.selectedAnimation) return;

      const timelineRect = this.$refs.timelineRuler.getBoundingClientRect();
      const position = Math.max(0, Math.min(event.clientX - timelineRect.left, this.timelineWidth));
      this.currentTime = this.positionToTime(position);
    },

    // Performance optimization methods

    /**
     * Handle scroll event for virtual scrolling
     * @param {Event} event - Scroll event
     */
    handleScroll(event) {
      // Update scroll position
      this.scrollTop = event.target.scrollTop;

      // Update virtual list renderer
      if (this.virtualListRenderer) {
        this.virtualListRenderer.updateScroll(this.scrollTop);

        // Check if we're near the bottom to trigger lazy loading
        const container = event.target;
        const scrollRatio = (container.scrollTop + container.clientHeight) / container.scrollHeight;

        // If we're within 20% of the bottom, try to load more items
        if (scrollRatio > 0.8) {
          // The VirtualListRenderer will handle the actual loading
          // through its loadMoreItems callback
          console.log('Near bottom, checking for more items...');
        }

        // Update performance metrics
        this.performanceMetrics.renderTime = performance.now() - this.lastRenderTime;
        this.lastRenderTime = performance.now();
        this.renderCount++;
      }
    },

    /**
     * Clean up resources to prevent memory leaks
     */
    cleanupResources() {
      // Clear cache if it exists
      if (this.cache && typeof this.cache.clear === 'function') {
        this.cache.clear();
      }

      // Clear any large objects
      this.virtualListRenderer = null;

      // Log performance metrics
      console.log('Performance metrics:', {
        ...this.performanceMetrics,
        cacheStats: this.cache && typeof this.cache.getStats === 'function' ? this.cache.getStats() : null
      });
    },

    /**
     * Initialize the virtual list renderer with lazy loading
     */
    initVirtualListRenderer() {
      // Get container height from the DOM if available
      let containerHeight = 400; // Default height
      if (this.$refs.animationList) {
        containerHeight = this.$refs.animationList.clientHeight;
      }

      // Create virtual list renderer with enhanced options
      this.virtualListRenderer = new VirtualListRenderer(
        this.animations,
        60, // Item height in pixels
        containerHeight,
        5, // Buffer size
        {
          // Lazy loading options
          lazyLoad: true,
          loadMoreItems: this.loadAnimationsWithPagination,
          loadThreshold: 0.7,
          pageSize: 20,
          totalItems: null, // Will be updated when we know the total count

          // Prefetching options
          prefetch: true,
          prefetchThreshold: 0.5, // Start prefetching at 50% of the current page

          // Web Worker options (disabled by default due to browser compatibility)
          useWorker: false // Set to true to enable web worker for background loading
        }
      );

      // Initialize enhanced cache for animations
      this.cache = new PerformanceOptimizer(50, 10 * 60 * 1000, {
        maxMemorySize: 50 * 1024 * 1024, // 50MB limit
        evictionThreshold: 0.8, // Start evicting at 80% capacity
        trackHitRate: true // Track cache hit rate for performance metrics
      });
    },

    /**
     * Measure render performance
     */
    measureRenderPerformance() {
      const startTime = performance.now();

      // Force a re-render by accessing the DOM
      if (this.$refs.animationList) {
        const dummy = this.$refs.animationList.offsetHeight;
      }

      // Calculate render time
      const renderTime = performance.now() - startTime;

      // Update performance metrics
      this.performanceMetrics.renderTime = renderTime;
      this.renderCount++;

      // Log performance metrics periodically
      if (this.renderCount % 10 === 0) {
        console.log('Performance metrics:', {
          ...this.performanceMetrics,
          renderCount: this.renderCount,
          animationCount: this.animations.length,
          visibleAnimationCount: this.visibleAnimations.length
        });

        // If we have the virtual list renderer, log its metrics too
        if (this.virtualListRenderer) {
          console.log('Virtual list metrics:', this.virtualListRenderer.getMetrics());
        }
      }
    },

    // Collaboration methods
    loadUserInfo() {
      try {
        const userInfo = JSON.parse(localStorage.getItem('mvs_vr_user_info'));
        if (userInfo) {
          this.userId = userInfo.userId || this.userId;
          this.userName = userInfo.userName || this.userName;
        }
      } catch (error) {
        console.error('Error loading user info:', error);
      }
    },

    saveUserInfo() {
      try {
        localStorage.setItem('mvs_vr_user_info', JSON.stringify({
          userId: this.userId,
          userName: this.userName
        }));
      } catch (error) {
        console.error('Error saving user info:', error);
      }
    },

    toggleCollaboration() {
      this.enableCollaboration = !this.enableCollaboration;

      if (this.enableCollaboration) {
        // If enabling collaboration, prompt for user name if not set
        if (this.userName === 'Anonymous User') {
          const name = prompt('Enter your name for collaborative editing:', '');
          if (name) {
            this.userName = name;
            this.saveUserInfo();
          }
        }
      }
    },

    handleMouseMove(event) {
      // Track cursor position for collaboration
      if (this.enableCollaboration) {
        const rect = event.currentTarget.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Only update if position changed significantly (throttle)
        const dx = Math.abs(x - this.lastCursorPosition.x);
        const dy = Math.abs(y - this.lastCursorPosition.y);

        if (dx > 5 || dy > 5) {
          this.lastCursorPosition = { x, y };
          // The cursor position will be sent to other users via the CollaborationProvider
        }
      }
    },

    handleCollaborativeUpdate(update) {
      // Handle updates from other users
      if (update.type === 'animation_update') {
        // Update animations without triggering our own update
        this.animations = update.data;
        this.hasChanges = true;
      } else if (update.type === 'selection_update') {
        // Show what another user has selected
        this.handleRemoteSelection(update.data, update.userId);
      }
    },

    handleCollaborationConnected(users) {
      this.collaborationStatus = 'connected';
      this.error = null;

      // Show notification that we're connected
      this.$emit('notification', {
        type: 'success',
        message: `Connected to collaboration session with ${users.length} other users`
      });
    },

    handleCollaborationDisconnected() {
      this.collaborationStatus = 'disconnected';

      // Show notification that we're disconnected
      this.$emit('notification', {
        type: 'warning',
        message: 'Disconnected from collaboration session'
      });
    },

    handleCollaborationError(error) {
      this.collaborationStatus = 'error';
      this.error = `Collaboration error: ${error.message}`;

      // Show notification about the error
      this.$emit('notification', {
        type: 'error',
        message: `Collaboration error: ${error.message}`
      });
    },

    handleCollaborationToggle(enabled) {
      this.enableCollaboration = enabled;
    },

    handleRemoteSelection(selection, userId) {
      // Highlight what another user has selected
      // This would typically add a temporary highlight to the selected element
      console.log(`User ${userId} selected:`, selection);

      // Implementation depends on the UI structure
      // For example, add a class to the selected element
      if (selection.animationId) {
        const animationElement = document.querySelector(`[data-animation-id="${selection.animationId}"]`);
        if (animationElement) {
          animationElement.classList.add('remote-selected');

          // Remove the highlight after a short delay
          setTimeout(() => {
            animationElement.classList.remove('remote-selected');
          }, 2000);
        }
      }
    },

    /**
     * Load animations with pagination
     * @param {Number} page - Page number to load
     * @param {Number} limit - Number of items per page
     * @returns {Promise<Array>} - Array of animations
     */
    async loadAnimationsWithPagination(page = 1, limit = 20) {
      const startTime = performance.now();

      // Don't set isLoading to true for subsequent pages to avoid UI flicker
      if (page === 1) {
        this.isLoading = true;
      }

      try {
        // Check cache first
        const cacheKey = `animations_${this.vendorId}_page${page}_limit${limit}`;
        const cachedData = this.cache.get(cacheKey);

        if (cachedData) {
          // Use cached data
          console.log(`Using cached animations data for page ${page}`);

          // Track cache performance
          const loadTime = performance.now() - startTime;
          this.performanceMetrics.cacheHits = (this.performanceMetrics.cacheHits || 0) + 1;
          this.performanceMetrics.cacheTimes = this.performanceMetrics.cacheTimes || [];
          this.performanceMetrics.cacheTimes.push(loadTime);

          return cachedData;
        }

        // Fetch animations from the API with pagination
        const response = await this.$api.get(
          `/items/animations?filter[vendor_id][_eq]=${this.vendorId}&limit=${limit}&page=${page}&sort=created_at`
        );

        const data = response.data.data || [];

        // Calculate approximate size of the data for memory management
        const dataSize = this.approximateDataSize(data);

        // Cache the result with size information
        this.cache.set(cacheKey, data, {
          size: dataSize,
          ttl: 10 * 60 * 1000 // 10 minutes TTL
        });

        // Log performance metrics for lazy loading
        const loadTime = performance.now() - startTime;
        console.log(`Loaded page ${page} with ${data.length} animations (${Math.round(dataSize / 1024)}KB) in ${loadTime}ms`);

        // Track API performance
        this.performanceMetrics.apiCalls = (this.performanceMetrics.apiCalls || 0) + 1;
        this.performanceMetrics.apiTimes = this.performanceMetrics.apiTimes || [];
        this.performanceMetrics.apiTimes.push(loadTime);

        return data;
      } catch (error) {
        console.error('Error loading animations with pagination:', error);

        // For lazy loading, return empty array instead of throwing
        // to prevent breaking the UI when scrolling
        return [];
      } finally {
        // Only update loading state for initial load
        if (page === 1) {
          this.isLoading = false;
        }

        // Update performance metrics
        this.performanceMetrics.loadTime = performance.now() - startTime;
      }
    },

    /**
     * Approximate the size of data in bytes
     * @param {Array|Object} data - Data to measure
     * @returns {Number} - Approximate size in bytes
     */
    approximateDataSize(data) {
      if (!data) return 0;

      // Convert to JSON string and measure length
      // This is a simple approximation, but works well for caching purposes
      const jsonString = JSON.stringify(data);
      return jsonString.length * 2; // Unicode characters are 2 bytes each
    },

    /**
     * Load animations with pagination
     * @param {Number} page - Page number to load
     * @param {Number} limit - Number of items per page
     */
    async loadAnimationsWithPagination(page = 1, limit = 20) {
      const startTime = performance.now();
      this.isLoading = true;

      try {
        // Check cache first
        const cacheKey = `animations_${this.vendorId}_page${page}_limit${limit}`;
        const cachedData = this.cache.get(cacheKey);

        if (cachedData) {
          // Use cached data
          return cachedData;
        }

        // Fetch animations from the API with pagination
        const response = await this.$api.get(
          `/items/animations?filter[vendor_id][_eq]=${this.vendorId}&limit=${limit}&page=${page}`
        );

        const data = response.data.data || [];

        // Cache the result
        this.cache.set(cacheKey, data);

        return data;
      } catch (error) {
        console.error('Error loading animations with pagination:', error);
        throw error;
      } finally {
        this.isLoading = false;
        this.performanceMetrics.loadTime = performance.now() - startTime;
      }
    },

    /**
     * Measure render performance
     */
    measureRenderPerformance() {
      const now = performance.now();
      const renderTime = now - this.lastRenderTime;

      // Update metrics
      this.performanceMetrics.renderTime = renderTime;
      this.lastRenderTime = now;
      this.renderCount++;

      // Log every 10 renders
      if (this.renderCount % 10 === 0) {
        console.log(`Average render time: ${this.performanceMetrics.renderTime.toFixed(2)}ms`);
      }
    },

    stopScrubbing() {
      this.isDraggingScrubber = false;
    },

    // Keyframe and track management
    selectKeyframe(keyframeId, trackId) {
      this.selectedKeyframeId = keyframeId;
      this.selectedTrackId = trackId;
      this.activePropertiesTab = 'keyframe';
    },

    updateKeyframe() {
      // This method is called when keyframe properties are updated
      // Update the animation preview
      this.updateAnimationPreview();

      // Mark as changed
      this.hasChanges = true;
    },

    updateKeyframeTime() {
      if (!this.selectedKeyframe || !this.selectedTrack) return;

      // Ensure keyframes are sorted by time
      this.selectedTrack.keyframes.sort((a, b) => a.time - b.time);

      // Update the animation preview
      this.updateAnimationPreview();

      // Mark as changed
      this.hasChanges = true;
    },

    updateAnimationPreview() {
      // In a real implementation, this would update a 3D preview
      // For now, we'll just log that the preview would be updated
      console.log('Animation preview updated');

      // This is where you would send the current animation state to a 3D preview component
      // For example:
      // this.$emit('preview-update', {
      //   animation: this.selectedAnimation,
      //   currentTime: this.currentTime
      // });
    },

    addTrack() {
      if (!this.selectedAnimation) return;

      // Create a new track with default properties
      const newTrack = {
        id: `track_${Date.now()}`,
        name: `Track ${this.selectedAnimation.tracks.length + 1}`,
        type: 'transform', // Default to transform track
        targetId: 'object_1', // Default target
        keyframes: [
          {
            id: `keyframe_${Date.now()}`,
            time: 0,
            easing: 'linear',
            value: {
              position: { x: 0, y: 0, z: 0 },
              rotation: { x: 0, y: 0, z: 0 },
              scale: { x: 1, y: 1, z: 1 }
            }
          },
          {
            id: `keyframe_${Date.now() + 1}`,
            time: this.selectedAnimation.duration,
            easing: 'linear',
            value: {
              position: { x: 0, y: 0, z: 0 },
              rotation: { x: 0, y: 0, z: 0 },
              scale: { x: 1, y: 1, z: 1 }
            }
          }
        ]
      };

      this.selectedAnimation.tracks.push(newTrack);
      this.selectedTrackId = newTrack.id;
      this.selectedKeyframeId = newTrack.keyframes[0].id;
      this.activePropertiesTab = 'keyframe';
    },

    // Animation management
    async saveAnimations() {
      this.isLoading = true;
      this.error = null;

      try {
        // Save animations to the API
        const savedAnimations = await AnimationService.saveAnimations(this.$api, this.animations, this.vendorId);

        // Update local animations with saved ones
        this.animations = savedAnimations;
        this.originalAnimations = JSON.parse(JSON.stringify(this.animations));

        // Emit update event to parent component
        this.$emit('update', this.animations);

        // Show success message or notification
        // This could be implemented with a toast notification system
        console.log('Animations saved successfully');
      } catch (error) {
        console.error('Error saving animations:', error);
        this.error = 'Failed to save animations. Please try again.';
      } finally {
        this.isLoading = false;
      }
    },

    resetAnimations() {
      if (this.originalAnimations) {
        this.animations = JSON.parse(JSON.stringify(this.originalAnimations));

        // Reset current selection if it no longer exists
        if (this.selectedAnimationId) {
          const animation = this.animations.find(a => a.id === this.selectedAnimationId);
          if (!animation) {
            this.selectedAnimationId = this.animations.length > 0 ? this.animations[0].id : null;
          }
        }

        // Reset track and keyframe selection
        this.selectedTrackId = null;
        this.selectedKeyframeId = null;
        this.activePropertiesTab = 'animation';

        // Reset playback
        this.stopAnimation();
      }
    },

    createAnimation() {
      // Create a new animation with default properties
      const newAnimation = {
        id: `animation_${Date.now()}`,
        name: `New Animation ${this.animations.length + 1}`,
        duration: 5, // 5 seconds default duration
        loop: false,
        vendor_id: this.vendorId,
        tracks: []
      };

      this.animations.push(newAnimation);
      this.selectedAnimationId = newAnimation.id;
      this.selectedTrackId = null;
      this.selectedKeyframeId = null;
      this.activePropertiesTab = 'animation';
      this.currentTime = 0;
      this.updateTimelineWidth();

      // Mark as changed
      this.hasChanges = true;
    },

    selectAnimation(animationId) {
      if (this.selectedAnimationId === animationId) return;

      // Stop any current playback
      this.stopAnimation();

      this.selectedAnimationId = animationId;
      this.selectedTrackId = null;
      this.selectedKeyframeId = null;
      this.activePropertiesTab = 'animation';
      this.currentTime = 0;
      this.updateTimelineWidth();
    },

    async deleteAnimation(animationId) {
      const index = this.animations.findIndex(animation => animation.id === animationId);

      if (index !== -1) {
        // If deleting the selected animation, select another one
        if (this.selectedAnimationId === animationId) {
          // Stop any current playback
          this.stopAnimation();

          if (this.animations.length > 1) {
            this.selectedAnimationId = this.animations[index === 0 ? 1 : index - 1].id;
          } else {
            this.selectedAnimationId = null;
          }

          this.selectedTrackId = null;
          this.selectedKeyframeId = null;
          this.activePropertiesTab = 'animation';
        }

        // Remove from local array
        const animation = this.animations[index];
        this.animations.splice(index, 1);

        // If this is a real animation (not a temporary one), delete it from the API
        if (animation.id && !animation.id.startsWith('animation_')) {
          try {
            this.isLoading = true;
            await AnimationService.deleteAnimation(this.$api, animation.id);
          } catch (error) {
            console.error(`Error deleting animation ${animation.id}:`, error);
            this.error = 'Failed to delete animation. Please try again.';

            // Restore the animation if API call fails
            this.animations.splice(index, 0, animation);
            if (this.selectedAnimationId === null) {
              this.selectedAnimationId = animation.id;
            }
          } finally {
            this.isLoading = false;
          }
        }
      }
    },

    updateAnimation() {
      // This method is called when animation properties are updated
      this.updateTimelineWidth();

      // Update the animation preview
      this.updateAnimationPreview();

      // Mark as changed
      this.hasChanges = true;
    },

    updateAnimationDuration() {
      if (!this.selectedAnimation) return;

      // Update timeline width
      this.updateTimelineWidth();

      // Ensure current time is within bounds
      this.currentTime = Math.min(this.currentTime, this.selectedAnimation.duration);
    },

    // Animation blending methods
    openBlendDialog() {
      // Initialize blend settings with the first two animations
      if (this.animations.length >= 2) {
        this.blendSettings.animation1Id = this.animations[0].id;
        this.blendSettings.animation2Id = this.animations[1].id;
        this.blendSettings.factor = 0.5;
        this.blendSettings.name = `Blend of ${this.animations[0].name} and ${this.animations[1].name}`;

        // Generate preview
        this.updateBlendPreview();
      }

      this.showBlendDialog = true;
    },

    closeBlendDialog() {
      this.showBlendDialog = false;
    },

    updateBlendPreview() {
      if (!this.animation1 || !this.animation2) return;

      // Create a preview of the blended animation
      this.previewBlendedAnimation = this.blendAnimations(
        this.animation1,
        this.animation2,
        this.blendSettings.factor
      );
    },

    blendAnimations(anim1, anim2, factor) {
      // Use the utility function to blend animations
      const blendedAnimation = AnimationUtils.blendAnimations(anim1, anim2, factor);

      // Set the name from the blend settings
      blendedAnimation.name = this.blendSettings.name;

      return blendedAnimation;
    },

    blendKeyframes(keyframes1, keyframes2, factor) {
      // Use the utility function to blend keyframes
      return AnimationUtils.blendKeyframes(keyframes1, keyframes2, factor);
    },

    getValueAtTime(keyframes, time) {
      // Use the utility function to get value at time
      return AnimationUtils.getValueAtTime(keyframes, time);
    },

    interpolateValues(value1, value2, t, easing = 'linear') {
      // Use the utility function to interpolate values
      return AnimationUtils.interpolateObjects(value1, value2, t, easing);
    },

    blendValues(value1, value2, factor) {
      // Use the utility function to blend values
      return AnimationUtils.blendValues(value1, value2, factor);
    },

    scaleTracksByFactor(tracks, factor) {
      // Use the utility function to scale tracks
      return AnimationUtils.scaleTracksByFactor(tracks, factor);
    },

    // Add new methods for easing and interpolation

    /**
     * Apply an easing preset to a keyframe
     * @param {String} keyframeId - The keyframe ID
     * @param {String} easing - The easing function name
     */
    applyEasingPreset(keyframeId, easing) {
      if (!this.selectedTrack) return;

      const keyframe = this.selectedTrack.keyframes.find(kf => kf.id === keyframeId);
      if (keyframe) {
        keyframe.easing = easing;
        this.updateKeyframe();
      }
    },

    /**
     * Preview an easing function
     * @param {String} easing - The easing function name
     * @param {Number} steps - Number of steps to generate
     * @returns {Array} - Array of points for visualization
     */
    previewEasing(easing, steps = 20) {
      const easingFn = EasingFunctions[easing] || EasingFunctions.linear;
      const points = [];

      for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        points.push({
          time: t,
          value: easingFn(t)
        });
      }

      return points;
    },

    /**
     * Interpolate between values using a custom curve
     * @param {Object} value1 - Start value
     * @param {Object} value2 - End value
     * @param {Number} t - Progress (0-1)
     * @param {Object} curve - Bezier curve control points
     * @returns {Object} - Interpolated value
     */
    interpolateWithCurve(value1, value2, t, curve) {
      // Calculate the eased t value using cubic bezier
      const easedT = this.cubicBezier(curve.x1, curve.y1, curve.x2, curve.y2, t);

      // Use the interpolateValues method with the eased t
      return this.interpolateValues(value1, value2, easedT);
    },

    /**
     * Calculate a point on a cubic bezier curve
     * @param {Number} x1 - First control point x
     * @param {Number} y1 - First control point y
     * @param {Number} x2 - Second control point x
     * @param {Number} y2 - Second control point y
     * @param {Number} t - Progress (0-1)
     * @returns {Number} - Value at t
     */
    cubicBezier(x1, y1, x2, y2, t) {
      // Calculate the cubic bezier value
      const cx = 3 * x1;
      const bx = 3 * (x2 - x1) - cx;
      const ax = 1 - cx - bx;

      const cy = 3 * y1;
      const by = 3 * (y2 - y1) - cy;
      const ay = 1 - cy - by;

      // Solve for y given t
      const t2 = t * t;
      const t3 = t2 * t;

      return ay * t3 + by * t2 + cy * t;
    },

    /**
     * Set a custom curve for a keyframe
     * @param {String} keyframeId - The keyframe ID
     * @param {Object} curve - Bezier curve control points
     */
    setCustomCurve(keyframeId, curve) {
      if (!this.selectedTrack) return;

      const keyframe = this.selectedTrack.keyframes.find(kf => kf.id === keyframeId);
      if (keyframe) {
        keyframe.curve = curve;
        keyframe.easing = 'custom';
        this.updateKeyframe();
      }
    },

    /**
     * Get the SVG path for an easing function
     * @param {String} easing - Easing function name
     * @returns {String} - SVG path data
     */
    getEasingPath(easing) {
      // Default to linear if easing is not specified
      if (!easing) return 'M0,60 L100,0';

      // If custom curve, use the bezier curve
      if (easing === 'custom' && this.selectedKeyframe?.curve) {
        const { x1, y1, x2, y2 } = this.selectedKeyframe.curve;
        return `M0,60 C${x1 * 100},${60 - y1 * 60} ${x2 * 100},${60 - y2 * 60} 100,0`;
      }

      // Generate path from easing function
      const points = [];
      const steps = 20;

      // Get the easing function
      const easingFn = EasingFunctions[easing] || EasingFunctions.linear;

      // Generate points
      for (let i = 0; i <= steps; i++) {
        const t = i / steps;
        const x = t * 100;
        const y = 60 - easingFn(t) * 60;
        points.push(`${x},${y}`);
      }

      return `M${points.join(' L')}`;
    },

    /**
     * Start dragging a control point in the custom curve editor
     * @param {Event} event - Mouse event
     */
    startDraggingControlPoint(event) {
      // Only handle clicks on control points
      const target = event.target;
      if (!target.dataset.point) return;

      // Get the point number (1 or 2)
      const pointNum = parseInt(target.dataset.point);

      // Set up dragging state
      this.draggingControlPoint = pointNum;

      // Add event listeners for dragging
      window.addEventListener('mousemove', this.dragControlPoint);
      window.addEventListener('mouseup', this.stopDraggingControlPoint);

      // Prevent default to avoid text selection
      event.preventDefault();
    },

    /**
     * Drag a control point
     * @param {Event} event - Mouse event
     */
    dragControlPoint(event) {
      if (!this.draggingControlPoint || !this.selectedKeyframe) return;

      // Get the SVG element
      const svg = event.currentTarget.querySelector('svg');
      if (!svg) return;

      // Get SVG bounding rect
      const rect = svg.getBoundingClientRect();

      // Calculate normalized coordinates (0-1)
      let x = (event.clientX - rect.left) / rect.width;
      let y = 1 - (event.clientY - rect.top) / rect.height;

      // Clamp values
      x = Math.max(0, Math.min(1, x));
      y = Math.max(0, Math.min(1, y));

      // Initialize curve if it doesn't exist
      if (!this.selectedKeyframe.curve) {
        this.selectedKeyframe.curve = {
          x1: 0.25,
          y1: 0.25,
          x2: 0.75,
          y2: 0.75
        };
      }

      // Update the appropriate control point
      if (this.draggingControlPoint === 1) {
        this.selectedKeyframe.curve.x1 = x;
        this.selectedKeyframe.curve.y1 = y;
      } else {
        this.selectedKeyframe.curve.x2 = x;
        this.selectedKeyframe.curve.y2 = y;
      }
    },

    /**
     * Stop dragging a control point
     */
    stopDraggingControlPoint() {
      this.draggingControlPoint = null;

      // Remove event listeners
      window.removeEventListener('mousemove', this.dragControlPoint);
      window.removeEventListener('mouseup', this.stopDraggingControlPoint);
    },

    /**
     * Reset the custom curve to default values
     */
    resetCustomCurve() {
      if (!this.selectedKeyframe) return;

      this.selectedKeyframe.curve = {
        x1: 0.25,
        y1: 0.25,
        x2: 0.75,
        y2: 0.75
      };
    },

    /**
     * Apply the custom curve to the keyframe
     */
    applyCustomCurve() {
      if (!this.selectedKeyframe) return;

      // Ensure the keyframe has a curve
      if (!this.selectedKeyframe.curve) {
        this.resetCustomCurve();
      }

      // Set easing to custom
      this.selectedKeyframe.easing = 'custom';

      // Update the keyframe
      this.updateKeyframe();
    },

    async createBlendedAnimation() {
      if (!this.canCreateBlend) return;

      this.isLoading = true;

      try {
        // Create the blended animation
        const blendedAnimation = this.blendAnimations(
          this.animation1,
          this.animation2,
          this.blendSettings.factor
        );

        // Add vendor ID
        blendedAnimation.vendor_id = this.vendorId;

        // Save to API
        const response = await this.$api.post('/items/animations', blendedAnimation);

        if (response.data && response.data.data) {
          // Add the new animation to the list
          this.animations.push(response.data.data);

          // Select the new animation
          this.selectedAnimationId = response.data.data.id;

          // Update original animations
          this.originalAnimations = JSON.parse(JSON.stringify(this.animations));

          // Close the dialog
          this.closeBlendDialog();

          // Emit update event
          this.$emit('update', this.animations);
        }
      } catch (error) {
        console.error('Error creating blended animation:', error);
        this.error = 'Failed to create blended animation. Please try again.';
      } finally {
        this.isLoading = false;
      }
    }
  },

  beforeDestroy() {
    // Clean up event listeners
    window.removeEventListener('mousemove', this.handleScrubberDrag);
    window.removeEventListener('mouseup', this.stopScrubbing);

    // Clear any running animation
    if (this.animationInterval) {
      clearInterval(this.animationInterval);
    }
  },

  watch: {
    'blendSettings.animation1Id'() {
      this.updateBlendPreviewDebounced();
    },
    'blendSettings.animation2Id'() {
      this.updateBlendPreviewDebounced();
    },
    'blendSettings.factor'() {
      this.updateBlendPreviewDebounced();
    },
    animations: {
      handler(newVal) {
        // Update performance metrics
        this.performanceMetrics.keyframeCount = this.totalKeyframeCount;
        this.performanceMetrics.trackCount = this.totalTrackCount;

        // Check if we should use virtual scrolling
        const shouldUseVirtual = this.shouldUseVirtualScrolling;
        if (shouldUseVirtual !== this.useVirtualScrolling) {
          this.useVirtualScrolling = shouldUseVirtual;

          // Add or remove scroll event listener
          if (this.useVirtualScrolling) {
            this.$refs.animationList?.addEventListener('scroll', this.handleScroll);
          } else {
            this.$refs.animationList?.removeEventListener('scroll', this.handleScroll);
          }
        }

        // Update virtual list renderer if needed
        if (this.useVirtualScrolling && this.virtualListRenderer) {
          this.virtualListRenderer.updateItems(newVal);
        }

        // Measure render performance
        this.measureRenderPerformance();
      },
      deep: true
    }
  }
};
</script>

<style scoped>
.animation-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
  position: relative;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.editor-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.editor-subtitle {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
  display: flex;
  align-items: center;
  gap: 6px;
}

.help-icon {
  font-size: 16px;
  color: var(--theme--foreground-subdued);
  cursor: help;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.help-icon:hover {
  opacity: 1;
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  cursor: pointer;
  position: relative;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editor-sidebar {
  width: 280px;
  border-right: 1px solid var(--theme--border-color);
  overflow-y: auto;
  background-color: var(--theme--background-subdued);
}

.sidebar-section {
  padding: 16px;
}

.sidebar-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.animation-list {
  max-height: 400px;
  overflow-y: auto;
  margin-bottom: 16px;
  position: relative; /* Required for virtual scrolling */
}

.virtual-scroll-container {
  width: 100%;
  position: relative;
}

.no-animations {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 16px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.no-animations i {
  font-size: 32px;
  margin-bottom: 8px;
}

.no-animations p {
  margin: 4px 0;
  font-size: 14px;
}

.animation-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 8px;
  background-color: var(--theme--background);
}

.animation-item:hover {
  background-color: var(--theme--background-accent);
}

.animation-item.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.animation-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.animation-info {
  flex: 1;
}

.animation-name {
  font-size: 14px;
  font-weight: 500;
}

.animation-duration {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.animation-item.active .animation-duration {
  color: var(--theme--primary-background);
  opacity: 0.8;
}

.animation-actions {
  display: flex;
}

.animation-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground);
  cursor: pointer;
}

.animation-item.active .animation-action-button {
  color: var(--theme--primary-background);
}

.animation-action-button:hover {
  background-color: var(--theme--danger);
  color: var(--theme--danger-background);
}

.add-animation-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  width: 100%;
  padding: 8px;
  border: 1px dashed var(--theme--border-color);
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.add-animation-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.editor-main {
  flex: 1;
  overflow: auto;
  background-color: var(--theme--background);
}

.no-animation-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.no-animation-selected i {
  font-size: 48px;
  margin-bottom: 16px;
}

.no-animation-selected p {
  margin: 0;
  font-size: 16px;
}

.animation-editor-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
}

.timeline-container {
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  margin-bottom: 16px;
  background-color: var(--theme--background-subdued);
}

.timeline-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timeline-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.timeline-content {
  padding: 16px;
  min-height: 200px;
  overflow-x: auto;
}

.timeline-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.timeline-control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
}

.timeline-control-button:hover {
  background-color: var(--theme--background-accent);
}

.timeline-control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.timeline-time-display {
  font-size: 14px;
  font-family: monospace;
  margin-left: 8px;
}

.timeline-ruler {
  position: relative;
  height: 32px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  margin-bottom: 8px;
  overflow: hidden;
}

.timeline-ruler-markers {
  position: relative;
  height: 100%;
}

.timeline-marker {
  position: absolute;
  top: 0;
  height: 100%;
}

.timeline-marker-line {
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 8px;
  background-color: var(--theme--foreground-subdued);
}

.timeline-marker:nth-child(5n+1) .timeline-marker-line {
  height: 16px;
  background-color: var(--theme--foreground);
}

.timeline-marker-label {
  position: absolute;
  top: 16px;
  left: -20px;
  width: 40px;
  font-size: 10px;
  text-align: center;
  color: var(--theme--foreground-subdued);
}

.timeline-scrubber {
  position: absolute;
  top: 0;
  width: 2px;
  height: 100%;
  background-color: var(--theme--primary);
  cursor: ew-resize;
}

.timeline-scrubber::before {
  content: '';
  position: absolute;
  top: 0;
  left: -4px;
  width: 10px;
  height: 10px;
  background-color: var(--theme--primary);
  border-radius: 50%;
}

.timeline-tracks {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.timeline-track {
  display: flex;
  height: 40px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.track-header {
  width: 150px;
  padding: 8px;
  background-color: var(--theme--background-subdued);
  border-right: 1px solid var(--theme--border-color);
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.track-name {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.track-type {
  font-size: 10px;
  color: var(--theme--foreground-subdued);
}

.track-content {
  flex: 1;
  position: relative;
  height: 100%;
}

.keyframe {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  margin-left: -6px;
  background-color: var(--theme--foreground);
  border-radius: 50%;
  cursor: pointer;
}

.keyframe:hover {
  background-color: var(--theme--primary);
}

.keyframe-selected {
  background-color: var(--theme--primary);
  box-shadow: 0 0 0 2px var(--theme--primary-background);
}

.no-tracks {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: var(--theme--background);
  border: 1px dashed var(--theme--border-color);
  border-radius: 4px;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.add-track-container {
  display: flex;
  justify-content: center;
  margin-top: 8px;
}

.add-track-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: 1px dashed var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.add-track-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.error-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: var(--theme--danger-background);
  color: var(--theme--danger);
  border-left: 4px solid var(--theme--danger);
  margin: 0 16px 16px;
  border-radius: 4px;
}

.error-message i {
  margin-right: 8px;
  font-size: 20px;
}

.error-message span {
  flex: 1;
}

.error-close {
  background: none;
  border: none;
  color: var(--theme--danger);
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
}

/* Animation Blend Dialog Styles */
.blend-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.blend-dialog {
  background-color: var(--theme--background);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 800px;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.blend-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--theme--border-color);
}

.blend-dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.blend-dialog-close {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
}

.blend-dialog-close:hover {
  background-color: var(--theme--background-accent);
}

.blend-dialog-content {
  padding: 20px;
  overflow-y: auto;
  display: flex;
  gap: 20px;
}

.blend-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.blend-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.blend-form-group label {
  font-size: 14px;
  font-weight: 500;
}

.blend-form-group select,
.blend-form-group input[type="text"] {
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  font-size: 14px;
}

.blend-form-group input[type="range"] {
  width: 100%;
}

.blend-factor-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.blend-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.blend-preview-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.blend-preview-content {
  flex: 1;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  padding: 12px;
  background-color: var(--theme--background-subdued);
  overflow: hidden;
}

.blend-preview-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.blend-preview-animation {
  flex: 1;
  overflow-y: auto;
}

.blend-preview-timeline {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.blend-preview-track {
  display: flex;
  align-items: center;
  gap: 8px;
}

.blend-preview-track-name {
  width: 100px;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.blend-preview-track-keyframes {
  flex: 1;
  height: 24px;
  background-color: var(--theme--background);
  border-radius: 2px;
  position: relative;
}

.blend-preview-keyframe {
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--theme--primary);
  border-radius: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.blend-dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid var(--theme--border-color);
}

.blend-dialog-button {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
}

.blend-dialog-button.primary {
  background-color: var(--theme--primary);
  color: white;
}

.blend-dialog-button.primary:disabled {
  background-color: var(--theme--primary-subdued);
  cursor: not-allowed;
}

.blend-dialog-button.secondary {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

/* Easing selector and preview styles */
.easing-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.easing-preview {
  margin-top: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
  height: 60px;
  overflow: hidden;
}

/* Custom curve editor styles */
.custom-curve-editor {
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
  padding: 8px;
  margin-top: 8px;
}

.curve-controls {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.curve-control-button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
  font-size: 12px;
}

.curve-control-button:hover {
  background-color: var(--theme--background-accent);
}

.properties-panel {
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  flex: 1;
  background-color: var(--theme--background-subdued);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.properties-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--theme--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.properties-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.properties-tabs {
  display: flex;
  gap: 8px;
}

.properties-tab {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
  font-size: 12px;
}

.properties-tab:hover {
  background-color: var(--theme--background-accent);
}

.properties-tab.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.properties-tab:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.properties-content {
  padding: 16px;
  overflow-y: auto;
  flex: 1;
}

.property-group {
  margin-bottom: 24px;
}

.property-group:last-child {
  margin-bottom: 0;
}

.property-group-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--theme--foreground-subdued);
}

.property-field {
  margin-bottom: 16px;
}

.property-field:last-child {
  margin-bottom: 0;
}

.property-field label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: var(--theme--foreground);
}

.property-field input[type="text"],
.property-field input[type="number"],
.property-field select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  font-size: 14px;
}

.property-field input[type="text"]:focus,
.property-field input[type="number"]:focus,
.property-field select:focus {
  border-color: var(--theme--primary);
  outline: none;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  transition: .4s;
  border-radius: 20px;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 1px;
  background-color: var(--theme--foreground-subdued);
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: var(--theme--primary);
  border-color: var(--theme--primary);
}

.toggle-switch input:checked + label:before {
  transform: translateX(20px);
  background-color: var(--theme--primary-background);
}

.range-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.range-input-container input[type="range"] {
  flex: 1;
}

.range-value {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  width: 40px;
  text-align: right;
}

.vector-input {
  display: flex;
  gap: 8px;
}

.vector-component {
  flex: 1;
}

.vector-component label {
  display: block;
  text-align: center;
  font-size: 10px;
  margin-bottom: 4px;
  color: var(--theme--foreground-subdued);
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

/* Collaboration styles */
.remote-selected {
  outline: 2px solid #ff5722;
  position: relative;
  z-index: 1;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { outline-color: #ff5722; }
  50% { outline-color: #ff9800; }
  100% { outline-color: #ff5722; }
}

.collaboration-users {
  display: flex;
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 100;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-left: -8px;
  border: 2px solid var(--theme--background);
  position: relative;
}

.user-avatar:first-child {
  margin-left: 0;
}

.user-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 1px solid var(--theme--background);
}

.user-status.online {
  background-color: #4caf50;
}

.user-status.idle {
  background-color: #ff9800;
}

.user-status.offline {
  background-color: #9e9e9e;
}

.remote-cursor {
  position: absolute;
  width: 20px;
  height: 20px;
  pointer-events: none;
  z-index: 1000;
  transition: transform 0.1s ease;
}

.remote-cursor::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 10px 5px 0 5px;
  border-color: var(--cursor-color, #ff5722) transparent transparent transparent;
  transform: rotate(-45deg);
}

.remote-cursor::after {
  content: attr(data-name);
  position: absolute;
  top: 15px;
  left: 15px;
  background-color: var(--cursor-color, #ff5722);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0.9;
}

.collaboration-chat {
  position: absolute;
  bottom: 16px;
  right: 16px;
  width: 300px;
  height: 400px;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 100;
  overflow: hidden;
}

.chat-header {
  padding: 8px 12px;
  background-color: var(--theme--background-subdued);
  border-bottom: 1px solid var(--theme--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  font-weight: 600;
  font-size: 14px;
}

.chat-close {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chat-message {
  max-width: 80%;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 14px;
  position: relative;
}

.chat-message.own {
  align-self: flex-end;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.chat-message.other {
  align-self: flex-start;
  background-color: var(--theme--background-subdued);
}

.message-sender {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 4px;
}

.message-time {
  font-size: 10px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.chat-input {
  padding: 12px;
  border-top: 1px solid var(--theme--border-color);
  display: flex;
  gap: 8px;
}

.chat-input input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 20px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.chat-input button {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
