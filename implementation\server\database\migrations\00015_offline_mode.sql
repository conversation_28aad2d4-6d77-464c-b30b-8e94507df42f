-- Migration: Offline Mode
-- Description: This migration adds tables and functions for offline mode functionality

-- Create network_quality_metrics table
CREATE TABLE IF NOT EXISTS network_quality_metrics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id TEXT NOT NULL,
  latency FLOAT NOT NULL,
  bandwidth FLOAT NOT NULL,
  packet_loss FLOAT NOT NULL,
  jitter FLOAT NOT NULL,
  status TEXT NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create cache_entries table
CREATE TABLE IF NOT EXISTS cache_entries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  client_id TEXT NOT NULL,
  resource_id UUID NOT NULL,
  resource_type TEXT NOT NULL,
  version TEXT NOT NULL,
  hash TEXT NOT NULL,
  size INTEGER NOT NULL,
  status TEXT NOT NULL,
  priority TEXT NOT NULL,
  last_accessed TIMESTAMPTZ NOT NULL,
  last_validated TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ NOT NULL,
  updated_at TIMESTAMPTZ NOT NULL,
  metadata JSONB,
  
  CONSTRAINT unique_cache_entry
    UNIQUE (client_id, resource_id, resource_type)
);

-- Create cache_validation_results table
CREATE TABLE IF NOT EXISTS cache_validation_results (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  cache_entry_id UUID NOT NULL,
  is_valid BOOLEAN NOT NULL,
  status TEXT NOT NULL,
  validation_method TEXT NOT NULL,
  error_message TEXT,
  validated_at TIMESTAMPTZ NOT NULL,
  
  CONSTRAINT fk_cache_validation_results_entry
    FOREIGN KEY (cache_entry_id)
    REFERENCES cache_entries(id)
    ON DELETE CASCADE
);

-- Create cache_resources table
CREATE TABLE IF NOT EXISTS cache_resources (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  resource_id UUID NOT NULL,
  resource_type TEXT NOT NULL,
  version TEXT NOT NULL,
  hash TEXT NOT NULL,
  size INTEGER NOT NULL,
  url TEXT NOT NULL,
  priority TEXT NOT NULL,
  dependencies UUID[],
  metadata JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  
  CONSTRAINT unique_cache_resource_version
    UNIQUE (resource_id, resource_type, version)
);

-- Create offline_mode_settings table
CREATE TABLE IF NOT EXISTS offline_mode_settings (
  client_id TEXT PRIMARY KEY,
  auto_switch_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  network_quality_threshold TEXT NOT NULL DEFAULT 'poor',
  cache_size_limit INTEGER,
  cache_ttl_days INTEGER NOT NULL DEFAULT 30,
  prioritize_recent BOOLEAN NOT NULL DEFAULT TRUE,
  sync_frequency_hours FLOAT NOT NULL DEFAULT 24,
  sync_on_startup BOOLEAN NOT NULL DEFAULT TRUE,
  sync_on_network_change BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL,
  updated_at TIMESTAMPTZ NOT NULL
);

-- Create offline_mode_status table
CREATE TABLE IF NOT EXISTS offline_mode_status (
  client_id TEXT PRIMARY KEY,
  is_offline_mode BOOLEAN NOT NULL DEFAULT FALSE,
  network_status TEXT NOT NULL,
  cache_status TEXT NOT NULL,
  last_online TIMESTAMPTZ,
  last_sync TIMESTAMPTZ,
  sync_progress FLOAT,
  sync_error TEXT,
  updated_at TIMESTAMPTZ NOT NULL
);

-- Create cache_sync_results table
CREATE TABLE IF NOT EXISTS cache_sync_results (
  sync_id UUID PRIMARY KEY,
  client_id TEXT NOT NULL,
  total_resources INTEGER NOT NULL DEFAULT 0,
  synced_resources INTEGER NOT NULL DEFAULT 0,
  failed_resources INTEGER NOT NULL DEFAULT 0,
  skipped_resources INTEGER NOT NULL DEFAULT 0,
  total_size INTEGER NOT NULL DEFAULT 0,
  sync_duration_ms INTEGER NOT NULL DEFAULT 0,
  completed BOOLEAN NOT NULL DEFAULT FALSE,
  error_message TEXT,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes
CREATE INDEX idx_network_quality_metrics_client_id ON network_quality_metrics(client_id);
CREATE INDEX idx_network_quality_metrics_timestamp ON network_quality_metrics(timestamp);
CREATE INDEX idx_cache_entries_client_id ON cache_entries(client_id);
CREATE INDEX idx_cache_entries_resource ON cache_entries(resource_id, resource_type);
CREATE INDEX idx_cache_entries_status ON cache_entries(status);
CREATE INDEX idx_cache_entries_priority ON cache_entries(priority);
CREATE INDEX idx_cache_validation_results_entry_id ON cache_validation_results(cache_entry_id);
CREATE INDEX idx_cache_resources_resource ON cache_resources(resource_id, resource_type);
CREATE INDEX idx_cache_resources_priority ON cache_resources(priority);
CREATE INDEX idx_cache_sync_results_client_id ON cache_sync_results(client_id);
CREATE INDEX idx_cache_sync_results_completed ON cache_sync_results(completed);

-- Create function to clean up old cache entries
CREATE OR REPLACE FUNCTION cleanup_old_cache_entries()
RETURNS TRIGGER AS $$
BEGIN
  -- Get TTL setting for this client
  DECLARE
    v_ttl_days INTEGER;
  BEGIN
    SELECT cache_ttl_days INTO v_ttl_days
    FROM offline_mode_settings
    WHERE client_id = NEW.client_id;
    
    -- Use default if not found
    IF v_ttl_days IS NULL THEN
      v_ttl_days := 30;
    END IF;
    
    -- Delete old entries
    DELETE FROM cache_entries
    WHERE client_id = NEW.client_id
      AND last_accessed < NOW() - (v_ttl_days || ' days')::INTERVAL;
  END;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to clean up old cache entries
CREATE TRIGGER cleanup_old_cache_entries_trigger
AFTER INSERT ON cache_entries
FOR EACH ROW
EXECUTE FUNCTION cleanup_old_cache_entries();

-- Create function to update cache statistics
CREATE OR REPLACE FUNCTION update_cache_statistics(p_client_id TEXT)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_total_entries INTEGER;
  v_total_size INTEGER;
  v_valid_entries INTEGER;
  v_outdated_entries INTEGER;
  v_corrupted_entries INTEGER;
  v_incomplete_entries INTEGER;
  v_missing_entries INTEGER;
  v_last_sync TIMESTAMPTZ;
  v_result JSONB;
BEGIN
  -- Get counts
  SELECT 
    COUNT(*),
    COALESCE(SUM(size), 0)
  INTO 
    v_total_entries,
    v_total_size
  FROM cache_entries
  WHERE client_id = p_client_id;
  
  -- Get status counts
  SELECT COUNT(*) INTO v_valid_entries
  FROM cache_entries
  WHERE client_id = p_client_id AND status = 'valid';
  
  SELECT COUNT(*) INTO v_outdated_entries
  FROM cache_entries
  WHERE client_id = p_client_id AND status = 'outdated';
  
  SELECT COUNT(*) INTO v_corrupted_entries
  FROM cache_entries
  WHERE client_id = p_client_id AND status = 'corrupted';
  
  SELECT COUNT(*) INTO v_incomplete_entries
  FROM cache_entries
  WHERE client_id = p_client_id AND status = 'incomplete';
  
  SELECT COUNT(*) INTO v_missing_entries
  FROM cache_entries
  WHERE client_id = p_client_id AND status = 'missing';
  
  -- Get last sync
  SELECT completed_at INTO v_last_sync
  FROM cache_sync_results
  WHERE client_id = p_client_id AND completed = TRUE
  ORDER BY completed_at DESC
  LIMIT 1;
  
  -- Build result
  v_result := jsonb_build_object(
    'client_id', p_client_id,
    'total_entries', v_total_entries,
    'total_size', v_total_size,
    'valid_entries', v_valid_entries,
    'outdated_entries', v_outdated_entries,
    'corrupted_entries', v_corrupted_entries,
    'incomplete_entries', v_incomplete_entries,
    'missing_entries', v_missing_entries,
    'last_sync', v_last_sync,
    'calculated_at', NOW()
  );
  
  RETURN v_result;
END;
$$;

-- Create function to get prioritized cache resources
CREATE OR REPLACE FUNCTION get_prioritized_cache_resources(
  p_client_id TEXT,
  p_limit INTEGER DEFAULT 100,
  p_resource_types TEXT[] DEFAULT NULL,
  p_priorities TEXT[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
  v_resources JSONB;
BEGIN
  WITH prioritized_resources AS (
    SELECT 
      r.id,
      r.resource_id,
      r.resource_type,
      r.version,
      r.hash,
      r.size,
      r.url,
      r.priority,
      r.dependencies,
      r.metadata,
      r.created_at,
      r.updated_at,
      -- Priority score (lower is higher priority)
      CASE
        WHEN r.priority = 'critical' THEN 1
        WHEN r.priority = 'high' THEN 2
        WHEN r.priority = 'medium' THEN 3
        WHEN r.priority = 'low' THEN 4
        WHEN r.priority = 'optional' THEN 5
        ELSE 6
      END AS priority_score,
      -- Check if already cached
      EXISTS (
        SELECT 1 FROM cache_entries e
        WHERE e.client_id = p_client_id
          AND e.resource_id = r.resource_id
          AND e.resource_type = r.resource_type
          AND e.version = r.version
          AND e.hash = r.hash
          AND e.status = 'valid'
      ) AS is_cached
    FROM cache_resources r
    WHERE (p_resource_types IS NULL OR r.resource_type = ANY(p_resource_types))
      AND (p_priorities IS NULL OR r.priority = ANY(p_priorities))
  )
  SELECT jsonb_agg(
    jsonb_build_object(
      'id', pr.id,
      'resource_id', pr.resource_id,
      'resource_type', pr.resource_type,
      'version', pr.version,
      'hash', pr.hash,
      'size', pr.size,
      'url', pr.url,
      'priority', pr.priority,
      'dependencies', pr.dependencies,
      'metadata', pr.metadata,
      'created_at', pr.created_at,
      'updated_at', pr.updated_at,
      'is_cached', pr.is_cached
    )
  ) INTO v_resources
  FROM prioritized_resources pr
  WHERE NOT pr.is_cached
  ORDER BY pr.priority_score, pr.size
  LIMIT p_limit;
  
  RETURN COALESCE(v_resources, '[]'::jsonb);
END;
$$;
