{"result": [{"scriptId": "1056", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/tests/setup/run.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 5707, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 5707, "count": 1}], "isBlockCoverage": true}, {"functionName": "bootstrap", "ranges": [{"startOffset": 888, "endOffset": 1800, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1819, "endOffset": 1897, "count": 0}], "isBlockCoverage": false}]}]}