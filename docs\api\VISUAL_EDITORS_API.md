# Visual Editors API Documentation

## Overview

This document provides comprehensive API documentation for the Visual Editors components in the MVS-VR platform. The Visual Editors consist of five main components:

1. ShowroomLayoutEditor
2. ProductConfigurator
3. MaterialTextureEditor
4. LightingEditor
5. AnimationEditor

Each editor component interacts with specific API endpoints and follows a consistent data flow pattern.

## Table of Contents

1. [Common API Patterns](#common-api-patterns)
2. [Authentication](#authentication)
3. [ShowroomLayoutEditor API](#showroomlayouteditor-api)
4. [ProductConfigurator API](#productconfigurator-api)
5. [MaterialTextureEditor API](#materialtextureeditor-api)
6. [LightingEditor API](#lightingeditor-api)
7. [AnimationEditor API](#animationeditor-api)
8. [Collaborative Editing API](#collaborative-editing-api)
9. [Error Handling](#error-handling)
10. [Rate Limiting](#rate-limiting)

## Common API Patterns

All Visual Editors components follow these common API patterns:

### Base URL

```
https://api.mvs-vr.com/v1
```

### Request Headers

| Header | Value | Description |
|--------|-------|-------------|
| `Authorization` | `Bearer {token}` | JWT authentication token |
| `Content-Type` | `application/json` | Request body format |
| `Accept` | `application/json` | Response format |

### Response Format

All API responses follow this standard format:

```json
{
  "data": {
    // Response data specific to the endpoint
  },
  "meta": {
    // Metadata about the response (pagination, etc.)
  }
}
```

### Error Response Format

```json
{
  "errors": [
    {
      "code": "ERROR_CODE",
      "message": "Human-readable error message",
      "details": {
        // Additional error details
      }
    }
  ]
}
```

## Authentication

All API requests to the Visual Editors endpoints require authentication. The MVS-VR platform uses JWT (JSON Web Tokens) for authentication.

### Obtaining a Token

```
POST /auth/login
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

**Response:**

```json
{
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires": "2023-12-31T23:59:59Z"
  }
}
```

## ShowroomLayoutEditor API

The ShowroomLayoutEditor component interacts with the following API endpoints:

### Get All Showroom Layouts

Retrieves all showroom layouts for a specific vendor.

```
GET /items/showroom_layouts?filter[vendor_id][_eq]={vendorId}
```

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `filter[vendor_id][_eq]` | string | Filter layouts by vendor ID |
| `limit` | number | Maximum number of layouts to return (default: 100) |
| `page` | number | Page number for pagination (default: 1) |

**Response:**

```json
{
  "data": [
    {
      "id": "layout_1",
      "name": "Main Showroom",
      "vendor_id": "vendor_123",
      "grid_size": { "width": 20, "height": 20 },
      "elements": [
        {
          "id": "element_1",
          "type": "product",
          "product_id": "product_1",
          "position": { "x": 5, "y": 0, "z": 5 },
          "rotation": { "x": 0, "y": 45, "z": 0 },
          "scale": { "x": 1, "y": 1, "z": 1 }
        }
      ]
    }
  ],
  "meta": {
    "filter_count": 1,
    "total_count": 1
  }
}
```

### Get Showroom Layout by ID

Retrieves a specific showroom layout by ID.

```http
GET /items/showroom_layouts/{layoutId}
```

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `layoutId` | string | ID of the layout to retrieve |

**Response:**

```json
{
  "data": {
    "id": "layout_1",
    "name": "Main Showroom",
    "vendor_id": "vendor_123",
    "grid_size": { "width": 20, "height": 20 },
    "elements": [
      {
        "id": "element_1",
        "type": "product",
        "product_id": "product_1",
        "position": { "x": 5, "y": 0, "z": 5 },
        "rotation": { "x": 0, "y": 45, "z": 0 },
        "scale": { "x": 1, "y": 1, "z": 1 }
      }
    ]
  }
}
```

### Create Showroom Layout

Creates a new showroom layout.

```http
POST /items/showroom_layouts
```

**Request Body:**

```json
{
  "name": "New Showroom",
  "vendor_id": "vendor_123",
  "grid_size": { "width": 20, "height": 20 },
  "elements": []
}
```

**Response:**

```json
{
  "data": {
    "id": "layout_2",
    "name": "New Showroom",
    "vendor_id": "vendor_123",
    "grid_size": { "width": 20, "height": 20 },
    "elements": []
  }
}
```

### Update Showroom Layout

Updates an existing showroom layout.

```http
PATCH /items/showroom_layouts/{layoutId}
```

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `layoutId` | string | ID of the layout to update |

**Request Body:**

```json
{
  "name": "Updated Showroom Name",
  "grid_size": { "width": 30, "height": 30 },
  "elements": [
    {
      "id": "element_1",
      "type": "product",
      "product_id": "product_1",
      "position": { "x": 10, "y": 0, "z": 10 },
      "rotation": { "x": 0, "y": 90, "z": 0 },
      "scale": { "x": 1, "y": 1, "z": 1 }
    }
  ]
}
```

**Response:**

```json
{
  "data": {
    "id": "layout_1",
    "name": "Updated Showroom Name",
    "vendor_id": "vendor_123",
    "grid_size": { "width": 30, "height": 30 },
    "elements": [
      {
        "id": "element_1",
        "type": "product",
        "product_id": "product_1",
        "position": { "x": 10, "y": 0, "z": 10 },
        "rotation": { "x": 0, "y": 90, "z": 0 },
        "scale": { "x": 1, "y": 1, "z": 1 }
      }
    ]
  }
}
```

## ProductConfigurator API

The ProductConfigurator component interacts with the following API endpoints:

### Get All Product Configurations

Retrieves all product configurations for a specific vendor.

```http
GET /items/product_configurations?filter[vendor_id][_eq]={vendorId}
```

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `filter[vendor_id][_eq]` | string | Filter configurations by vendor ID |
| `limit` | number | Maximum number of configurations to return (default: 100) |
| `page` | number | Page number for pagination (default: 1) |

**Response:**

```json
{
  "data": [
    {
      "id": "config_1",
      "name": "Standard Configuration",
      "product_id": "product_1",
      "vendor_id": "vendor_123",
      "options": [
        {
          "id": "option_1",
          "name": "Color",
          "type": "color",
          "values": [
            { "id": "value_1", "name": "Red", "value": "#ff0000" },
            { "id": "value_2", "name": "Blue", "value": "#0000ff" }
          ],
          "default_value_id": "value_1"
        }
      ]
    }
  ],
  "meta": {
    "filter_count": 1,
    "total_count": 1
  }
}
```

## MaterialTextureEditor API

The MaterialTextureEditor component interacts with the following API endpoints:

### Get All Materials

Retrieves all materials for a specific vendor.

```http
GET /items/materials?filter[vendor_id][_eq]={vendorId}
```

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `filter[vendor_id][_eq]` | string | Filter materials by vendor ID |
| `limit` | number | Maximum number of materials to return (default: 100) |
| `page` | number | Page number for pagination (default: 1) |

**Response:**

```json
{
  "data": [
    {
      "id": "material_1",
      "name": "Ceramic Tile",
      "vendor_id": "vendor_123",
      "properties": {
        "color": "#ffffff",
        "metalness": 0,
        "roughness": 0.5,
        "normal_scale": 1
      },
      "textures": [
        {
          "id": "texture_1",
          "type": "diffuse",
          "url": "https://assets.mvs-vr.com/textures/ceramic_diffuse.jpg"
        },
        {
          "id": "texture_2",
          "type": "normal",
          "url": "https://assets.mvs-vr.com/textures/ceramic_normal.jpg"
        }
      ]
    }
  ],
  "meta": {
    "filter_count": 1,
    "total_count": 1
  }
}
```

## LightingEditor API

The LightingEditor component interacts with the following API endpoints:

### Get All Showroom Lighting

Retrieves all lighting setups for a specific showroom.

```http
GET /items/showroom_lighting?filter[showroom_id][_eq]={showroomId}
```

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `filter[showroom_id][_eq]` | string | Filter lighting by showroom ID |
| `limit` | number | Maximum number of lighting setups to return (default: 100) |
| `page` | number | Page number for pagination (default: 1) |

**Response:**

```json
{
  "data": [
    {
      "id": "lighting_1",
      "name": "Daytime Lighting",
      "showroom_id": "showroom_1",
      "vendor_id": "vendor_123",
      "ambient_light": {
        "color": "#ffffff",
        "intensity": 0.5
      },
      "lights": [
        {
          "id": "light_1",
          "type": "directional",
          "name": "Main Light",
          "color": "#ffffff",
          "intensity": 1,
          "position": { "x": 10, "y": 10, "z": 10 },
          "target": { "x": 0, "y": 0, "z": 0 },
          "cast_shadow": true
        }
      ]
    }
  ],
  "meta": {
    "filter_count": 1,
    "total_count": 1
  }
}
```

## AnimationEditor API

The AnimationEditor component interacts with the following API endpoints:

### Get All Animations

Retrieves all animations for a specific vendor.

```http
GET /items/animations?filter[vendor_id][_eq]={vendorId}
```

**Query Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `filter[vendor_id][_eq]` | string | Filter animations by vendor ID |
| `limit` | number | Maximum number of animations to return (default: 100) |
| `page` | number | Page number for pagination (default: 1) |

**Response:**

```json
{
  "data": [
    {
      "id": "animation_1",
      "name": "Product Rotation",
      "duration": 5,
      "loop": true,
      "vendor_id": "vendor_123",
      "tracks": [
        {
          "id": "track_1",
          "name": "Rotation Track",
          "type": "transform",
          "targetId": "product_1",
          "keyframes": [
            {
              "id": "keyframe_1",
              "time": 0,
              "easing": "linear",
              "value": {
                "position": { "x": 0, "y": 0, "z": 0 },
                "rotation": { "x": 0, "y": 0, "z": 0 },
                "scale": { "x": 1, "y": 1, "z": 1 }
              }
            },
            {
              "id": "keyframe_2",
              "time": 5,
              "easing": "linear",
              "value": {
                "position": { "x": 0, "y": 0, "z": 0 },
                "rotation": { "x": 0, "y": 360, "z": 0 },
                "scale": { "x": 1, "y": 1, "z": 1 }
              }
            }
          ]
        }
      ]
    }
  ],
  "meta": {
    "filter_count": 1,
    "total_count": 1
  }
}
```

### Get Animation by ID

Retrieves a specific animation by ID.

```http
GET /items/animations/{animationId}
```

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `animationId` | string | ID of the animation to retrieve |

**Response:**

```json
{
  "data": {
    "id": "animation_1",
    "name": "Product Rotation",
    "duration": 5,
    "loop": true,
    "vendor_id": "vendor_123",
    "tracks": [
      {
        "id": "track_1",
        "name": "Rotation Track",
        "type": "transform",
        "targetId": "product_1",
        "keyframes": [
          {
            "id": "keyframe_1",
            "time": 0,
            "easing": "linear",
            "value": {
              "position": { "x": 0, "y": 0, "z": 0 },
              "rotation": { "x": 0, "y": 0, "z": 0 },
              "scale": { "x": 1, "y": 1, "z": 1 }
            }
          },
          {
            "id": "keyframe_2",
            "time": 5,
            "easing": "linear",
            "value": {
              "position": { "x": 0, "y": 0, "z": 0 },
              "rotation": { "x": 0, "y": 360, "z": 0 },
              "scale": { "x": 1, "y": 1, "z": 1 }
            }
          }
        ]
      }
    ]
  }
}
```

### Create Animation

Creates a new animation.

```http
POST /items/animations
```

**Request Body:**

```json
{
  "name": "New Animation",
  "duration": 5,
  "loop": false,
  "vendor_id": "vendor_123",
  "tracks": []
}
```

**Response:**

```json
{
  "data": {
    "id": "animation_2",
    "name": "New Animation",
    "duration": 5,
    "loop": false,
    "vendor_id": "vendor_123",
    "tracks": []
  }
}
```

### Update Animation

Updates an existing animation.

```http
PATCH /items/animations/{animationId}
```

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `animationId` | string | ID of the animation to update |

**Request Body:**

```json
{
  "name": "Updated Animation Name",
  "duration": 10,
  "loop": true,
  "tracks": [
    {
      "id": "track_1",
      "name": "Updated Track Name",
      "type": "transform",
      "targetId": "product_1",
      "keyframes": [
        {
          "id": "keyframe_1",
          "time": 0,
          "easing": "easeInOutQuad",
          "value": {
            "position": { "x": 0, "y": 0, "z": 0 },
            "rotation": { "x": 0, "y": 0, "z": 0 },
            "scale": { "x": 1, "y": 1, "z": 1 }
          }
        },
        {
          "id": "keyframe_2",
          "time": 10,
          "easing": "easeInOutQuad",
          "value": {
            "position": { "x": 0, "y": 0, "z": 0 },
            "rotation": { "x": 0, "y": 720, "z": 0 },
            "scale": { "x": 1, "y": 1, "z": 1 }
          }
        }
      ]
    }
  ]
}
```

**Response:**

```json
{
  "data": {
    "id": "animation_1",
    "name": "Updated Animation Name",
    "duration": 10,
    "loop": true,
    "vendor_id": "vendor_123",
    "tracks": [
      {
        "id": "track_1",
        "name": "Updated Track Name",
        "type": "transform",
        "targetId": "product_1",
        "keyframes": [
          {
            "id": "keyframe_1",
            "time": 0,
            "easing": "easeInOutQuad",
            "value": {
              "position": { "x": 0, "y": 0, "z": 0 },
              "rotation": { "x": 0, "y": 0, "z": 0 },
              "scale": { "x": 1, "y": 1, "z": 1 }
            }
          },
          {
            "id": "keyframe_2",
            "time": 10,
            "easing": "easeInOutQuad",
            "value": {
              "position": { "x": 0, "y": 0, "z": 0 },
              "rotation": { "x": 0, "y": 720, "z": 0 },
              "scale": { "x": 1, "y": 1, "z": 1 }
            }
          }
        ]
      }
    ]
  }
}
```

## Collaborative Editing API

The Visual Editors support real-time collaborative editing through a WebSocket-based API. This allows multiple users to work on the same content simultaneously.

### WebSocket Connection

To establish a collaborative editing session, connect to the WebSocket server:

```http
wss://collaboration.mvs-vr.com/{roomId}
```

**Path Parameters:**

| Parameter | Type | Description |
|-----------|------|-------------|
| `roomId` | string | Unique identifier for the collaboration room (e.g., `animation_{animationId}`) |

### WebSocket Authentication

When connecting to the WebSocket server, include an authentication token in the query parameters:

```http
wss://collaboration.mvs-vr.com/{roomId}?token={authToken}
```

### Message Format

All messages exchanged over the WebSocket connection follow this format:

```json
{
  "type": "MESSAGE_TYPE",
  "data": {
    // Message-specific data
  },
  "userId": "user_123",
  "timestamp": 1625097600000
}
```

### Message Types

#### Join Message

Sent when a user joins the collaboration session:

```json
{
  "type": "join",
  "data": {
    "userName": "John Doe",
    "userColor": "#ff0000"
  },
  "userId": "user_123",
  "timestamp": 1625097600000
}
```

#### Update Message

Sent when a user makes changes to the content:

```json
{
  "type": "update",
  "data": {
    "operations": [
      {
        "op": "add",
        "path": "/animations/0/tracks/0/keyframes/-",
        "value": {
          "id": "keyframe_3",
          "time": 2.5,
          "easing": "linear",
          "value": {
            "position": { "x": 0, "y": 0, "z": 0 },
            "rotation": { "x": 0, "y": 180, "z": 0 },
            "scale": { "x": 1, "y": 1, "z": 1 }
          }
        }
      }
    ]
  },
  "userId": "user_123",
  "timestamp": 1625097600000
}
```

#### Cursor Message

Sent when a user moves their cursor:

```json
{
  "type": "cursor",
  "data": {
    "position": { "x": 100, "y": 200 }
  },
  "userId": "user_123",
  "timestamp": 1625097600000
}
```

#### Selection Message

Sent when a user selects an item:

```json
{
  "type": "selection",
  "data": {
    "animationId": "animation_1",
    "trackId": "track_1",
    "keyframeId": "keyframe_1"
  },
  "userId": "user_123",
  "timestamp": 1625097600000
}
```

#### Chat Message

Sent when a user sends a chat message:

```json
{
  "type": "chat",
  "data": {
    "message": "Hello, everyone!"
  },
  "userId": "user_123",
  "timestamp": 1625097600000
}
```

#### Leave Message

Sent when a user leaves the collaboration session:

```json
{
  "type": "leave",
  "data": {},
  "userId": "user_123",
  "timestamp": 1625097600000
}
```

### Error Handling

If an error occurs during the WebSocket connection, an error message is sent:

```json
{
  "type": "error",
  "data": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message"
  },
  "timestamp": 1625097600000
}
```

## API Error Handling

All API endpoints return standard HTTP status codes:

| Status Code | Description |
|-------------|-------------|
| 200 | OK - The request was successful |
| 201 | Created - A new resource was created |
| 400 | Bad Request - The request was invalid |
| 401 | Unauthorized - Authentication is required |
| 403 | Forbidden - The user does not have permission |
| 404 | Not Found - The resource was not found |
| 409 | Conflict - The request conflicts with the current state |
| 422 | Unprocessable Entity - Validation error |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - An unexpected error occurred |

Error responses include detailed information about the error:

```json
{
  "errors": [
    {
      "code": "VALIDATION_ERROR",
      "message": "The name field is required",
      "field": "name"
    }
  ]
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse. The following headers are included in all responses:

| Header | Description |
|--------|-------------|
| `X-RateLimit-Limit` | Maximum number of requests allowed per time window |
| `X-RateLimit-Remaining` | Number of requests remaining in the current time window |
| `X-RateLimit-Reset` | Time (in seconds) until the rate limit resets |

If the rate limit is exceeded, a 429 Too Many Requests response is returned.
