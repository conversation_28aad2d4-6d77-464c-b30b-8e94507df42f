import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { SceneValidatorService } from '../../../services/scene/scene-validator';
import { validateRequest } from '../../middleware/validation';

// Initialize scene validator service
const sceneValidator = new SceneValidatorService(supabase);

/**
 * Validate scene data
 *
 * @param req - Request
 * @param res - Response
 */
export const validateSceneData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { data } = req.body;

    // Validate parameters
    if (!data) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_SCENE_DATA',
          message: 'Scene data is required',
        },
      });
      return;
    }

    // Validate scene data
    const errors = sceneValidator.validateSceneData(data);

    res.status(200).json({
      success: true,
      data: {
        valid: errors.length === 0,
        errors,
      },
    });
  } catch (error) {
    logger.error('Error validating scene data', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

// Export default handler
export default async function handler(req: Request, res: Response): Promise<void> {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      res.status(405).json({
        success: false,
        error: {
          code: 'METHOD_NOT_ALLOWED',
          message: 'Method not allowed',
        },
      });
      return;
    }

    await validateSceneData(req, res);
  } catch (error) {
    logger.error('Error in scene data validation handler', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
}
