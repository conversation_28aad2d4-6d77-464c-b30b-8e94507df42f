<template>
  <div class="showroom-layout-editor">
    <div class="editor-header">
      <div class="editor-title-section">
        <h3 class="editor-title">Showroom Layout Editor</h3>
        <div class="editor-subtitle">Design your virtual showroom layout</div>
      </div>
      <div class="editor-actions">
        <button class="action-button" @click="saveLayout" :disabled="!hasChanges">
          <i class="material-icons">save</i>
          <span>Save</span>
        </button>
        <button class="action-button" @click="resetLayout" :disabled="!hasChanges">
          <i class="material-icons">refresh</i>
          <span>Reset</span>
        </button>
      </div>
    </div>

    <div class="editor-content">
      <div class="editor-sidebar">
        <div class="sidebar-section">
          <h4 class="sidebar-title">Showroom Settings</h4>
          <div class="form-group">
            <label for="showroom-name">Name</label>
            <input 
              type="text" 
              id="showroom-name" 
              v-model="layout.name" 
              placeholder="Enter showroom name"
            >
          </div>
          <div class="form-group">
            <label for="showroom-template">Template</label>
            <select id="showroom-template" v-model="layout.template">
              <option v-for="template in templates" :key="template.id" :value="template.id">
                {{ template.name }}
              </option>
            </select>
          </div>
          <div class="form-group">
            <label for="showroom-size">Size</label>
            <select id="showroom-size" v-model="layout.size">
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
            </select>
          </div>
        </div>

        <div class="sidebar-section">
          <h4 class="sidebar-title">Products</h4>
          <div class="search-box">
            <input 
              type="text" 
              placeholder="Search products..." 
              v-model="searchQuery"
              @input="filterProducts"
            >
            <i class="material-icons">search</i>
          </div>
          <div class="product-list">
            <div 
              v-for="product in filteredProducts" 
              :key="product.id" 
              class="product-item"
              draggable="true"
              @dragstart="onDragStart($event, product)"
            >
              <div class="product-thumbnail">
                <img v-if="product.thumbnail" :src="product.thumbnail" :alt="product.name">
                <div v-else class="product-placeholder">
                  <i class="material-icons">inventory_2</i>
                </div>
              </div>
              <div class="product-info">
                <div class="product-name">{{ product.name }}</div>
                <div class="product-category">{{ getCategoryName(product.category) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="editor-main">
        <div class="editor-toolbar">
          <div class="toolbar-group">
            <button class="toolbar-button" @click="toggleGrid" :class="{ active: showGrid }">
              <i class="material-icons">grid_on</i>
            </button>
            <button class="toolbar-button" @click="toggleSnap" :class="{ active: snapToGrid }">
              <i class="material-icons">snap_align</i>
            </button>
          </div>
          <div class="toolbar-group">
            <button class="toolbar-button" @click="zoomIn">
              <i class="material-icons">zoom_in</i>
            </button>
            <button class="toolbar-button" @click="zoomOut">
              <i class="material-icons">zoom_out</i>
            </button>
            <button class="toolbar-button" @click="resetZoom">
              <i class="material-icons">fit_screen</i>
            </button>
          </div>
          <div class="toolbar-group">
            <button class="toolbar-button" @click="toggleViewMode">
              <i class="material-icons">{{ viewMode === '2d' ? '3d_rotation' : 'view_in_ar' }}</i>
            </button>
          </div>
        </div>

        <div class="editor-canvas-container">
          <!-- 2D Layout Editor -->
          <div v-if="viewMode === '2d'" class="editor-canvas" 
            :style="{ transform: `scale(${zoomLevel})` }"
            @dragover="onDragOver"
            @drop="onDrop"
          >
            <div v-if="showGrid" class="grid-overlay" :style="gridStyle"></div>
            
            <div 
              v-for="item in layout.items" 
              :key="item.id" 
              class="layout-item"
              :style="getItemStyle(item)"
              @mousedown="startDrag($event, item)"
            >
              <div class="item-content">
                <img v-if="item.thumbnail" :src="item.thumbnail" :alt="item.name">
                <div v-else class="item-placeholder">
                  <i class="material-icons">inventory_2</i>
                </div>
              </div>
              <div class="item-controls">
                <button class="item-control-button" @click.stop="removeItem(item)">
                  <i class="material-icons">delete</i>
                </button>
                <button class="item-control-button" @click.stop="rotateItem(item)">
                  <i class="material-icons">rotate_right</i>
                </button>
              </div>
            </div>
          </div>

          <!-- 3D Preview -->
          <div v-else class="editor-canvas-3d">
            <div class="preview-placeholder">
              <i class="material-icons">view_in_ar</i>
              <p>3D Preview</p>
              <p class="placeholder-subtitle">Coming soon</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ShowroomLayoutEditor',

  props: {
    vendorId: {
      type: String,
      required: true
    },
    showroomId: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      layout: {
        id: null,
        name: 'New Showroom',
        template: 'template_1',
        size: 'medium',
        items: []
      },
      originalLayout: null,
      templates: [
        { id: 'template_1', name: 'Modern Showroom' },
        { id: 'template_2', name: 'Classic Gallery' },
        { id: 'template_3', name: 'Open Space' }
      ],
      products: [],
      categories: [],
      filteredProducts: [],
      searchQuery: '',
      showGrid: true,
      snapToGrid: true,
      zoomLevel: 1,
      viewMode: '2d',
      draggedItem: null,
      dragStartX: 0,
      dragStartY: 0,
      itemStartX: 0,
      itemStartY: 0
    };
  },

  computed: {
    hasChanges() {
      return this.originalLayout && JSON.stringify(this.layout) !== JSON.stringify(this.originalLayout);
    },
    
    gridStyle() {
      return {
        backgroundSize: `${20 * this.zoomLevel}px ${20 * this.zoomLevel}px`
      };
    }
  },

  created() {
    this.loadData();
  },

  methods: {
    async loadData() {
      try {
        // Load products
        const productsResponse = await this.$api.get(`/items/products?filter[vendor_id][_eq]=${this.vendorId}`);
        this.products = productsResponse.data.data || [];
        this.filteredProducts = [...this.products];

        // Load categories
        const categoriesResponse = await this.$api.get('/items/categories');
        this.categories = categoriesResponse.data.data || [];

        // Load showroom layout if editing existing
        if (this.showroomId) {
          const layoutResponse = await this.$api.get(`/items/showroom_layouts/${this.showroomId}`);
          if (layoutResponse.data.data) {
            this.layout = layoutResponse.data.data;
            this.originalLayout = JSON.parse(JSON.stringify(this.layout));
          }
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    },

    filterProducts() {
      if (!this.searchQuery) {
        this.filteredProducts = [...this.products];
        return;
      }

      const query = this.searchQuery.toLowerCase();
      this.filteredProducts = this.products.filter(product => 
        product.name.toLowerCase().includes(query) || 
        this.getCategoryName(product.category).toLowerCase().includes(query)
      );
    },

    getCategoryName(categoryId) {
      const category = this.categories.find(c => c.id === categoryId);
      return category ? category.name : 'Uncategorized';
    },

    toggleGrid() {
      this.showGrid = !this.showGrid;
    },

    toggleSnap() {
      this.snapToGrid = !this.snapToGrid;
    },

    zoomIn() {
      if (this.zoomLevel < 2) {
        this.zoomLevel += 0.1;
      }
    },

    zoomOut() {
      if (this.zoomLevel > 0.5) {
        this.zoomLevel -= 0.1;
      }
    },

    resetZoom() {
      this.zoomLevel = 1;
    },

    toggleViewMode() {
      this.viewMode = this.viewMode === '2d' ? '3d' : '2d';
    },

    onDragStart(event, product) {
      event.dataTransfer.setData('product', JSON.stringify(product));
      event.dataTransfer.effectAllowed = 'copy';
    },

    onDragOver(event) {
      event.preventDefault();
    },

    onDrop(event) {
      event.preventDefault();
      const productData = event.dataTransfer.getData('product');
      if (productData) {
        const product = JSON.parse(productData);
        const rect = event.target.getBoundingClientRect();
        let x = event.clientX - rect.left;
        let y = event.clientY - rect.top;

        // Apply snap to grid if enabled
        if (this.snapToGrid) {
          x = Math.round(x / 20) * 20;
          y = Math.round(y / 20) * 20;
        }

        this.layout.items.push({
          id: `item_${Date.now()}`,
          productId: product.id,
          name: product.name,
          thumbnail: product.thumbnail,
          x: x / this.zoomLevel,
          y: y / this.zoomLevel,
          width: 100,
          height: 100,
          rotation: 0,
          zIndex: this.layout.items.length + 1
        });
      }
    },

    startDrag(event, item) {
      this.draggedItem = item;
      this.dragStartX = event.clientX;
      this.dragStartY = event.clientY;
      this.itemStartX = item.x;
      this.itemStartY = item.y;

      const onMouseMove = (moveEvent) => {
        if (this.draggedItem) {
          let deltaX = (moveEvent.clientX - this.dragStartX) / this.zoomLevel;
          let deltaY = (moveEvent.clientY - this.dragStartY) / this.zoomLevel;
          
          let newX = this.itemStartX + deltaX;
          let newY = this.itemStartY + deltaY;
          
          // Apply snap to grid if enabled
          if (this.snapToGrid) {
            newX = Math.round(newX / 20) * 20;
            newY = Math.round(newY / 20) * 20;
          }
          
          this.draggedItem.x = newX;
          this.draggedItem.y = newY;
        }
      };

      const onMouseUp = () => {
        this.draggedItem = null;
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
      };

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    },

    getItemStyle(item) {
      return {
        left: `${item.x}px`,
        top: `${item.y}px`,
        width: `${item.width}px`,
        height: `${item.height}px`,
        transform: `rotate(${item.rotation}deg)`,
        zIndex: item.zIndex
      };
    },

    removeItem(item) {
      const index = this.layout.items.findIndex(i => i.id === item.id);
      if (index !== -1) {
        this.layout.items.splice(index, 1);
      }
    },

    rotateItem(item) {
      item.rotation = (item.rotation + 45) % 360;
    },

    async saveLayout() {
      try {
        let response;
        if (this.layout.id) {
          // Update existing layout
          response = await this.$api.patch(`/items/showroom_layouts/${this.layout.id}`, this.layout);
        } else {
          // Create new layout
          this.layout.vendor_id = this.vendorId;
          response = await this.$api.post('/items/showroom_layouts', this.layout);
        }
        
        if (response.data.data) {
          this.layout = response.data.data;
          this.originalLayout = JSON.parse(JSON.stringify(this.layout));
          this.$emit('update', this.layout);
        }
      } catch (error) {
        console.error('Error saving layout:', error);
      }
    },

    resetLayout() {
      if (this.originalLayout) {
        this.layout = JSON.parse(JSON.stringify(this.originalLayout));
      }
    }
  }
};
</script>

<style scoped>
.showroom-layout-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.editor-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.editor-subtitle {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  cursor: pointer;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editor-sidebar {
  width: 280px;
  border-right: 1px solid var(--theme--border-color);
  overflow-y: auto;
  background-color: var(--theme--background-subdued);
}

.sidebar-section {
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.sidebar-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.search-box {
  position: relative;
  margin-bottom: 16px;
}

.search-box input {
  width: 100%;
  padding: 8px 32px 8px 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.search-box i {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
}

.product-list {
  max-height: 300px;
  overflow-y: auto;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: grab;
  margin-bottom: 8px;
  background-color: var(--theme--background);
}

.product-item:hover {
  background-color: var(--theme--background-accent);
}

.product-thumbnail {
  width: 40px;
  height: 40px;
  margin-right: 8px;
  border-radius: 4px;
  overflow: hidden;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
}

.product-category {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  border-bottom: 1px solid var(--theme--border-color);
  background-color: var(--theme--background-subdued);
}

.toolbar-group {
  display: flex;
  gap: 4px;
}

.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
}

.toolbar-button:hover {
  background-color: var(--theme--background-accent);
}

.toolbar-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.editor-canvas-container {
  flex: 1;
  overflow: auto;
  position: relative;
}

.editor-canvas {
  position: relative;
  width: 1200px;
  height: 800px;
  background-color: var(--theme--background);
  transform-origin: top left;
}

.grid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  pointer-events: none;
}

.layout-item {
  position: absolute;
  background-color: var(--theme--background);
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  cursor: move;
}

.item-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.item-content img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.item-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.item-controls {
  position: absolute;
  top: -30px;
  right: 0;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.layout-item:hover .item-controls {
  opacity: 1;
}

.item-control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.item-control-button:hover {
  background-color: var(--theme--background-accent);
}

.editor-canvas-3d {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--theme--background-subdued);
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.preview-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
}

.preview-placeholder p {
  margin: 0;
  font-size: 16px;
}

.placeholder-subtitle {
  font-size: 14px;
  margin-top: 8px;
}
</style>
