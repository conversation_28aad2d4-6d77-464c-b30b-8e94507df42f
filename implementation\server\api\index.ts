/**
 * Main API Router
 *
 * This module serves as the entry point for all API routes.
 */

import { Router } from 'express';
import { logger } from '../lib/logger';

// Import routers
import authRouter from './auth';
import usersRouter from './users';
import vendorsRouter from './vendors';
import productsRouter from './products';
import showroomsRouter from './showrooms';
import analyticsRouter from './analytics';
import monitoringRouter from './monitoring';

// Import UE compatibility routers
import ueCompatibilityRouter from './ue-compatibility';
import ueBlueprintsRouter from './ue-compatibility/blueprints';
import ueAssetsRouter from './ue-compatibility/assets';
import ueScenesRouter from './ue-compatibility/scenes';
import ueLLMRouter from './ue-compatibility/llm';

// Create main router
const router = Router();

// Register sub-routers
router.use('/auth', authRouter);
router.use('/users', usersRouter);
router.use('/vendors', vendorsRouter);
router.use('/products', productsRouter);
router.use('/showrooms', showroomsRouter);
router.use('/analytics', analyticsRouter);
router.use('/monitoring', monitoringRouter);

// Register UE compatibility routers
router.use('/ue-compatibility', ueCompatibilityRouter);
router.use('/ue-compatibility/blueprints', ueBlueprintsRouter);
router.use('/ue-compatibility/assets', ueAssetsRouter);
router.use('/ue-compatibility/scenes', ueScenesRouter);
router.use('/ue-compatibility/llm', ueLLMRouter);

// Log router initialization
logger.info('Main API router initialized');

export default router;
