/**
 * Real-time Visualization API
 *
 * This module provides endpoints for real-time data visualization.
 */

import { Router, Request, Response } from 'express';
import { supabase } from '../../lib/supabase';
import { authenticate } from '../../middleware/auth';
import { rateLimiter } from '../../middleware/rate-limiter';
import { logger } from '../../lib/logger';

const router = Router();

/**
 * Get real-time showroom data
 *
 * @param req - Request
 * @param res - Response
 */
export const getRealtimeShowroomData = async (req: Request, res: Response): Promise<void> => {
  try {
    const { vendor_id, showroom_id, interval = '5m' } = req.query;

    // Validate required parameters
    if (!vendor_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Vendor ID is required',
        },
      });
      return;
    }

    // Calculate time range based on interval
    const now = new Date();
    let startTime: Date;

    switch (interval) {
      case '5m':
        startTime = new Date(now.getTime() - 5 * 60 * 1000);
        break;
      case '15m':
        startTime = new Date(now.getTime() - 15 * 60 * 1000);
        break;
      case '30m':
        startTime = new Date(now.getTime() - 30 * 60 * 1000);
        break;
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 5 * 60 * 1000);
    }

    // Build query
    let query = supabase
      .from('visitor_sessions')
      .select('id, user_id, showroom_id, started_at, ended_at, duration, device_type')
      .eq('vendor_id', vendor_id)
      .gte('started_at', startTime.toISOString())
      .order('started_at', { ascending: false });

    // Filter by showroom if provided
    if (showroom_id && showroom_id !== 'all') {
      query = query.eq('showroom_id', showroom_id);
    }

    // Execute query
    const { data, error } = await query;

    if (error) {
      logger.error('Error fetching real-time showroom data:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: 'Error fetching real-time showroom data',
        },
      });
      return;
    }

    // Process data for visualization
    const visualizationData = processRealtimeData(data || []);

    res.status(200).json({
      success: true,
      data: visualizationData,
    });
  } catch (error) {
    logger.error('Error in getRealtimeShowroomData:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'SERVER_ERROR',
        message: 'Internal server error',
      },
    });
  }
};

/**
 * Process real-time data for visualization
 *
 * @param data - Raw data from database
 * @returns Processed data for visualization
 */
function processRealtimeData(data: any[]): any {
  // Group sessions by minute
  const timePoints: Record<string, any> = {};
  const deviceCounts: Record<string, number> = {};
  let activeUsers = 0;

  // Process each session
  data.forEach(session => {
    // Count active users (sessions without end time or ended in last 5 minutes)
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    if (!session.ended_at || new Date(session.ended_at) > fiveMinutesAgo) {
      activeUsers++;
    }

    // Count by device type
    const deviceType = session.device_type || 'unknown';
    deviceCounts[deviceType] = (deviceCounts[deviceType] || 0) + 1;

    // Group by minute for time series
    const startedAt = new Date(session.started_at);
    const minuteKey = startedAt.toISOString().substring(0, 16); // Format: YYYY-MM-DDTHH:MM

    if (!timePoints[minuteKey]) {
      timePoints[minuteKey] = {
        time: minuteKey,
        count: 0,
      };
    }

    timePoints[minuteKey].count++;
  });

  // Convert to arrays for visualization
  const timeSeriesData = Object.values(timePoints).sort((a, b) => a.time.localeCompare(b.time));

  const deviceData = Object.entries(deviceCounts).map(([device, count]) => ({
    device,
    count,
  }));

  return {
    activeUsers,
    timeSeriesData,
    deviceData,
    totalSessions: data.length,
  };
}

// Register routes
router.get(
  '/realtime-visualization',
  authenticate,
  rateLimiter({ windowMs: 60 * 1000, max: 30 }),
  getRealtimeShowroomData,
);

export default router;
