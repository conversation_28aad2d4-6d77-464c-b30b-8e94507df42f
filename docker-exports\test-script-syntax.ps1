# Quick syntax test for the deployment checker script
Write-Host "Testing PowerShell script syntax..." -ForegroundColor Cyan

try {
    # Test script syntax without executing
    $scriptPath = ".\Check-MVS-VR-Deployment.ps1"
    
    if (Test-Path $scriptPath) {
        $errors = $null
        $tokens = $null
        $ast = [System.Management.Automation.Language.Parser]::ParseFile($scriptPath, [ref]$tokens, [ref]$errors)
        
        if ($errors.Count -eq 0) {
            Write-Host "✅ Script syntax is valid!" -ForegroundColor Green
            Write-Host "Script contains $($ast.FindAll({$true}, $true).Count) AST nodes" -ForegroundColor Gray
        } else {
            Write-Host "❌ Script has syntax errors:" -ForegroundColor Red
            foreach ($error in $errors) {
                Write-Host "  Line $($error.Extent.StartLineNumber): $($error.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "❌ Script file not found: $scriptPath" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error testing script: $_" -ForegroundColor Red
}

Write-Host "`nPress any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
