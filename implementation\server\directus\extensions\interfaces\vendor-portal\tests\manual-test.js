// Manual test script for VirtualListRenderer with lazy loading

const { VirtualListRenderer } = require('../src/utils/PerformanceOptimizer');

// Create mock items
const mockItems = Array.from({ length: 50 }, (_, i) => ({
  id: `item-${i}`,
  name: `Item ${i}`,
  value: i
}));

// Create mock load more function
const mockLoadMoreItems = async (page, pageSize) => {
  console.log(`Loading page ${page} with pageSize ${pageSize}`);
  const startIndex = (page - 1) * pageSize;
  return Array.from({ length: pageSize }, (_, i) => ({
    id: `item-${startIndex + i + mockItems.length}`,
    name: `Item ${startIndex + i + mockItems.length}`,
    value: startIndex + i + mockItems.length
  }));
};

// Create renderer with lazy loading
const renderer = new VirtualListRenderer(
  mockItems,
  40, // Item height
  400, // Container height
  5, // Buffer
  {
    lazyLoad: true,
    loadMoreItems: mockLoadMoreItems,
    loadThreshold: 0.7,
    pageSize: 20,
    totalItems: 100
  }
);

// Test 1: Get visible items
console.log('\nTest 1: Get visible items');
const visibleItems = renderer.getVisibleItems();
console.log(`Visible items count: ${visibleItems.visibleItems.length}`);
console.log(`First visible item: ${visibleItems.visibleItems[0].id}`);
console.log(`Container style height: ${visibleItems.containerStyle.height}`);
console.log(`Performance metrics:`, visibleItems.metrics);

// Test 2: Scroll to trigger lazy loading
console.log('\nTest 2: Scroll to trigger lazy loading');
const totalHeight = renderer.items.length * renderer.itemHeight;
const scrollPosition = (totalHeight - renderer.containerHeight) * renderer.loadThreshold + 1;
console.log(`Scrolling to position: ${scrollPosition}`);
renderer.updateScroll(scrollPosition);

// Wait for lazy loading to complete
setTimeout(() => {
  console.log(`Items after lazy loading: ${renderer.items.length}`);
  console.log(`Has more items: ${renderer.hasMoreItems}`);
  
  // Test 3: Get metrics
  console.log('\nTest 3: Get metrics');
  const metrics = renderer.getMetrics();
  console.log('Performance metrics:', metrics);
  
  // Test 4: Reset metrics
  console.log('\nTest 4: Reset metrics');
  renderer.resetMetrics();
  console.log('Metrics after reset:', renderer.getMetrics());
}, 1000);
