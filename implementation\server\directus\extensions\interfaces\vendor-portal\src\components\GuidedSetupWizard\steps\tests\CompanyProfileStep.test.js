import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import CompanyProfileStep from '../CompanyProfileStep.vue';
import WizardStep from '../../WizardStep.vue';

// Mock the WizardStep component
vi.mock('../../WizardStep.vue', () => ({
  default: {
    name: 'WizardStep',
    render: h => h('div', { class: 'wizard-step-mock' }, [h('slot')]),
    props: ['title', 'description', 'stepData', 'validationSchema', 'helpTips'],
  },
}));

describe('CompanyProfileStep', () => {
  let wrapper;
  const mockStepData = {
    companyName: 'Test Company',
    industry: 'furniture',
    description: 'Test description',
    contactEmail: '<EMAIL>',
    contactPhone: '************',
    website: 'https://example.com',
    address: '123 Test St, Test City, TS 12345',
    logo: {
      file: null,
      preview: 'data:image/png;base64,test-image-data',
      name: 'test-logo.png',
      size: 1024,
      type: 'image/png',
    },
  };

  beforeEach(() => {
    wrapper = mount(CompanyProfileStep, {
      propsData: {
        stepData: mockStepData,
      },
    });
  });

  afterEach(() => {
    wrapper.destroy();
  });

  it('renders the component', () => {
    expect(wrapper.exists()).toBe(true);
  });

  it('passes the correct props to WizardStep', () => {
    const wizardStep = wrapper.findComponent(WizardStep);
    expect(wizardStep.exists()).toBe(true);

    expect(wizardStep.props('title')).toBe('Company Profile');
    expect(wizardStep.props('stepData')).toEqual(mockStepData);
    expect(wizardStep.props('validationSchema')).toBeTruthy();
    expect(wizardStep.props('helpTips')).toHaveLength(2);
  });

  it('initializes localStepData with stepData prop', () => {
    expect(wrapper.vm.localStepData).toEqual(mockStepData);
  });

  it('renders all form fields with correct values', () => {
    // Company name field
    const companyNameInput = wrapper.find('#company-name');
    expect(companyNameInput.exists()).toBe(true);
    expect(companyNameInput.element.value).toBe('Test Company');

    // Industry field
    const industrySelect = wrapper.find('#industry');
    expect(industrySelect.exists()).toBe(true);
    expect(industrySelect.element.value).toBe('furniture');

    // Description field
    const descriptionTextarea = wrapper.find('#description');
    expect(descriptionTextarea.exists()).toBe(true);
    expect(descriptionTextarea.element.value).toBe('Test description');

    // Contact email field
    const contactEmailInput = wrapper.find('#contact-email');
    expect(contactEmailInput.exists()).toBe(true);
    expect(contactEmailInput.element.value).toBe('<EMAIL>');

    // Contact phone field
    const contactPhoneInput = wrapper.find('#contact-phone');
    expect(contactPhoneInput.exists()).toBe(true);
    expect(contactPhoneInput.element.value).toBe('************');

    // Website field
    const websiteInput = wrapper.find('#website');
    expect(websiteInput.exists()).toBe(true);
    expect(websiteInput.element.value).toBe('https://example.com');

    // Address field
    const addressTextarea = wrapper.find('#address');
    expect(addressTextarea.exists()).toBe(true);
    expect(addressTextarea.element.value).toBe('123 Test St, Test City, TS 12345');

    // Logo preview
    const logoPreview = wrapper.find('.logo-preview');
    expect(logoPreview.exists()).toBe(true);
    expect(logoPreview.classes()).toContain('has-logo');

    const logoImage = logoPreview.find('img');
    expect(logoImage.exists()).toBe(true);
    expect(logoImage.attributes('src')).toBe('data:image/png;base64,test-image-data');
  });

  it('emits update:step-data when updateField is called', () => {
    // Act
    wrapper.vm.updateField('companyName', 'Updated Company Name');

    // Assert
    expect(wrapper.emitted('update:step-data')).toBeTruthy();
    expect(wrapper.emitted('update:step-data')[0][0].companyName).toBe('Updated Company Name');
  });

  it('emits validate when validateStep is called', () => {
    // Act
    wrapper.vm.validateStep(true);

    // Assert
    expect(wrapper.emitted('validate')).toBeTruthy();
    expect(wrapper.emitted('validate')[0][0]).toBe(true);
  });

  it('updates field value when input changes', async () => {
    // Arrange
    const companyNameInput = wrapper.find('#company-name');

    // Act
    await companyNameInput.setValue('Updated Company Name');

    // Assert
    expect(wrapper.vm.localStepData.companyName).toBe('Updated Company Name');
    expect(wrapper.emitted('update:step-data')).toBeTruthy();
    expect(wrapper.emitted('update:step-data')[0][0].companyName).toBe('Updated Company Name');
  });

  it('shows logo preview when logo is present', () => {
    // Assert
    const logoPreview = wrapper.find('.logo-preview');
    expect(logoPreview.classes()).toContain('has-logo');

    const logoImage = logoPreview.find('img');
    expect(logoImage.exists()).toBe(true);
    expect(logoImage.attributes('src')).toBe('data:image/png;base64,test-image-data');

    const removeButton = wrapper.find('.remove-button');
    expect(removeButton.exists()).toBe(true);
  });

  it('shows logo placeholder when no logo is present', async () => {
    // Arrange
    const newStepData = { ...mockStepData, logo: null };

    // Act
    await wrapper.setProps({ stepData: newStepData });

    // Assert
    const logoPreview = wrapper.find('.logo-preview');
    expect(logoPreview.classes()).not.toContain('has-logo');

    const logoPlaceholder = logoPreview.find('.logo-placeholder');
    expect(logoPlaceholder.exists()).toBe(true);

    const removeButton = wrapper.find('.remove-button');
    expect(removeButton.exists()).toBe(false);
  });

  it('removes logo when removeLogo is called', async () => {
    // Act
    wrapper.vm.removeLogo();

    // Assert
    expect(wrapper.vm.localStepData.logo).toBeNull();
    expect(wrapper.emitted('update:step-data')).toBeTruthy();
    expect(wrapper.emitted('update:step-data')[0][0].logo).toBeNull();
  });

  it('has correct validation schema', () => {
    // Assert
    const { validationSchema } = wrapper.vm;

    // Company name validation
    expect(validationSchema.companyName.required).toBe(true);
    expect(validationSchema.companyName.minLength).toBe(2);
    expect(validationSchema.companyName.maxLength).toBe(100);

    // Industry validation
    expect(validationSchema.industry.required).toBe(true);

    // Contact email validation
    expect(validationSchema.contactEmail.required).toBe(true);
    expect(validationSchema.contactEmail.pattern).toBeTruthy();

    // Website validation
    expect(validationSchema.website.pattern).toBeTruthy();
  });

  it('has help tips', () => {
    // Assert
    const { helpTips } = wrapper.vm;
    expect(helpTips).toHaveLength(2);

    expect(helpTips[0].title).toBe('Why is this important?');
    expect(helpTips[1].title).toBe('Logo Requirements');
  });
});
