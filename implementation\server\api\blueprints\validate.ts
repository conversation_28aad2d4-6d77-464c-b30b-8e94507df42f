import { Request, Response } from 'express';
import { supabase } from '../../shared/utils/supabase';
import { logger } from '../../shared/utils/logger';
import { BlueprintValidatorService } from '../../services/blueprint/blueprint-validator';
import { validateRequest } from '../middleware/validation';

// Initialize blueprint validator service
const blueprintValidator = new BlueprintValidatorService(supabase);

/**
 * Validate a blueprint
 *
 * @param req - Request
 * @param res - Response
 */
export const validateBlueprint = async (req: Request, res: Response): Promise<void> => {
  try {
    const { blueprint_id } = req.params;

    // Validate parameters
    if (!blueprint_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_BLUEPRINT_ID',
          message: 'Blueprint ID is required',
        },
      });
      return;
    }

    // Validate blueprint
    const result = await blueprintValidator.validateBlueprint(blueprint_id);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating blueprint', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Validate a blueprint instance
 *
 * @param req - Request
 * @param res - Response
 */
export const validateBlueprintInstance = async (req: Request, res: Response): Promise<void> => {
  try {
    const { blueprint_id } = req.params;
    const { properties } = req.body;

    // Validate parameters
    if (!blueprint_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_BLUEPRINT_ID',
          message: 'Blueprint ID is required',
        },
      });
      return;
    }

    if (!properties || typeof properties !== 'object') {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PROPERTIES',
          message: 'Properties must be an object',
        },
      });
      return;
    }

    // Validate blueprint instance
    const result = await blueprintValidator.validateBlueprintInstance(blueprint_id, properties);

    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error) {
    logger.error('Error validating blueprint instance', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
