<template>
  <div class="visual-editors">
    <div class="visual-editors-header">
      <div class="header-top">
        <h2 class="visual-editors-title">Visual Editors</h2>
        <div class="editor-controls">
          <div class="auto-save-toggle">
            <label class="toggle-label">
              <input type="checkbox" v-model="autoSaveEnabled" @change="toggleAutoSave">
              <span class="toggle-text">Auto-save</span>
            </label>
          </div>
          <div v-if="lastSaved" class="last-saved">
            Last saved: {{ formatLastSaved }}
          </div>
        </div>
      </div>

      <div class="visual-editors-tabs">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          class="tab-button"
          :class="{ active: activeTab === tab.id }"
          @click="setActiveTab(tab.id)"
        >
          <i class="material-icons">{{ tab.icon }}</i>
          <span>{{ tab.label }}</span>
        </button>
      </div>

      <!-- Selection controls based on active tab -->
      <div class="selection-controls">
        <div v-if="activeTab === 'layout' && showrooms.length > 0" class="select-container">
          <label for="showroom-select">Select Showroom:</label>
          <select
            id="showroom-select"
            v-model="selectedShowroomId"
            class="editor-select"
          >
            <option v-for="showroom in showrooms" :key="showroom.id" :value="showroom.id">
              {{ showroom.name }}
            </option>
          </select>
          <button class="new-item-button" @click="selectedShowroomId = null">
            <i class="material-icons">add</i> New Showroom
          </button>
        </div>

        <div v-if="activeTab === 'product' && products.length > 0" class="select-container">
          <label for="product-select">Select Product:</label>
          <select
            id="product-select"
            v-model="selectedProductId"
            class="editor-select"
          >
            <option v-for="product in products" :key="product.id" :value="product.id">
              {{ product.name }}
            </option>
          </select>
          <button class="new-item-button" @click="selectedProductId = null">
            <i class="material-icons">add</i> New Configuration
          </button>
        </div>

        <div v-if="activeTab === 'material' && materials.length > 0" class="select-container">
          <label for="material-select">Select Material:</label>
          <select
            id="material-select"
            v-model="selectedMaterialId"
            class="editor-select"
          >
            <option v-for="material in materials" :key="material.id" :value="material.id">
              {{ material.name }}
            </option>
          </select>
          <button class="new-item-button" @click="selectedMaterialId = null">
            <i class="material-icons">add</i> New Material
          </button>
        </div>

        <div v-if="activeTab === 'animation' && animations.length > 0" class="select-container">
          <label for="animation-select">Select Animation:</label>
          <select
            id="animation-select"
            v-model="selectedAnimationId"
            class="editor-select"
          >
            <option v-for="animation in animations" :key="animation.id" :value="animation.id">
              {{ animation.name }}
            </option>
          </select>
          <button class="new-item-button" @click="selectedAnimationId = null">
            <i class="material-icons">add</i> New Animation
          </button>
        </div>
      </div>
    </div>

    <!-- Loading indicator -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading editor data...</div>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="error-container">
      <div class="error-icon">
        <i class="material-icons">error</i>
      </div>
      <div class="error-message">{{ error }}</div>
      <button class="retry-button" @click="loadInitialData">
        <i class="material-icons">refresh</i>
        Retry
      </button>
    </div>

    <!-- Editor content -->
    <div v-else class="visual-editors-content">
      <!-- Showroom Layout Editor -->
      <div v-if="activeTab === 'layout'" class="editor-container">
        <ShowroomLayoutEditor
          :vendor-id="vendorId"
          :showroom-id="selectedShowroomId"
          @update="handleLayoutUpdate"
        />
      </div>

      <!-- Product Configurator -->
      <div v-if="activeTab === 'product'" class="editor-container">
        <ProductConfigurator
          :vendor-id="vendorId"
          :product-id="selectedProductId"
          @update="handleProductUpdate"
        />
      </div>

      <!-- Material and Texture Editor -->
      <div v-if="activeTab === 'material'" class="editor-container">
        <MaterialTextureEditor
          :vendor-id="vendorId"
          :material-id="selectedMaterialId"
          @update="handleMaterialUpdate"
        />
      </div>

      <!-- Lighting Editor -->
      <div v-if="activeTab === 'lighting'" class="editor-container">
        <LightingEditor
          :vendor-id="vendorId"
          :showroom-id="selectedShowroomId"
          @update="handleLightingUpdate"
        />
      </div>

      <!-- Animation Editor -->
      <div v-if="activeTab === 'animation'" class="editor-container">
        <AnimationEditor
          :vendor-id="vendorId"
          :animation-id="selectedAnimationId"
          @update="handleAnimationUpdate"
        />
      </div>
    </div>
  </div>
</template>

<script>
import ShowroomLayoutEditor from './ShowroomLayoutEditor.vue';
import ProductConfigurator from './ProductConfigurator.vue';
import MaterialTextureEditor from './MaterialTextureEditor.vue';
import LightingEditor from './LightingEditor.vue';
import AnimationEditor from './AnimationEditor.vue';

export default {
  name: 'VisualEditors',

  components: {
    ShowroomLayoutEditor,
    ProductConfigurator,
    MaterialTextureEditor,
    LightingEditor,
    AnimationEditor
  },

  props: {
    vendorId: {
      type: String,
      required: true
    }
  },

  data() {
    return {
      activeTab: 'layout',
      tabs: [
        { id: 'layout', label: 'Showroom Layout', icon: 'dashboard' },
        { id: 'product', label: 'Product Config', icon: 'settings' },
        { id: 'material', label: 'Materials', icon: 'palette' },
        { id: 'lighting', label: 'Lighting', icon: 'lightbulb' },
        { id: 'animation', label: 'Animation', icon: 'movie' }
      ],
      selectedShowroomId: null,
      selectedProductId: null,
      selectedMaterialId: null,
      selectedAnimationId: null,
      loading: false,
      showrooms: [],
      products: [],
      materials: [],
      animations: [],
      error: null,
      autoSaveEnabled: true,
      lastSaved: null
    };
  },

  computed: {
    formatLastSaved() {
      if (!this.lastSaved) return '';

      // Format the date as a relative time (e.g., "2 minutes ago")
      const now = new Date();
      const diffMs = now - this.lastSaved;
      const diffSec = Math.round(diffMs / 1000);
      const diffMin = Math.round(diffSec / 60);
      const diffHour = Math.round(diffMin / 60);

      if (diffSec < 60) {
        return 'just now';
      } else if (diffMin < 60) {
        return `${diffMin} minute${diffMin === 1 ? '' : 's'} ago`;
      } else if (diffHour < 24) {
        return `${diffHour} hour${diffHour === 1 ? '' : 's'} ago`;
      } else {
        // Format as date and time for older saves
        return this.lastSaved.toLocaleString();
      }
    }
  },

  mounted() {
    this.loadInitialData();
  },

  methods: {
    async loadInitialData() {
      this.loading = true;
      this.error = null;

      try {
        // Load data for all editors
        await Promise.all([
          this.loadShowrooms(),
          this.loadProducts(),
          this.loadMaterials(),
          this.loadAnimations()
        ]);

        // Set initial selections if available
        if (this.showrooms.length > 0) {
          this.selectedShowroomId = this.showrooms[0].id;
        }

        if (this.products.length > 0) {
          this.selectedProductId = this.products[0].id;
        }

        if (this.materials.length > 0) {
          this.selectedMaterialId = this.materials[0].id;
        }

        if (this.animations.length > 0) {
          this.selectedAnimationId = this.animations[0].id;
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
        this.error = 'Failed to load data. Please try again.';
      } finally {
        this.loading = false;
      }
    },

    async loadShowrooms() {
      try {
        const response = await this.$api.get(`/items/showroom_layouts?filter[vendor_id][_eq]=${this.vendorId}`);
        if (response.data && response.data.data) {
          this.showrooms = response.data.data;
        }
      } catch (error) {
        console.error('Error loading showrooms:', error);
        throw error;
      }
    },

    async loadProducts() {
      try {
        const response = await this.$api.get(`/items/products?filter[vendor_id][_eq]=${this.vendorId}`);
        if (response.data && response.data.data) {
          this.products = response.data.data;
        }
      } catch (error) {
        console.error('Error loading products:', error);
        throw error;
      }
    },

    async loadMaterials() {
      try {
        const response = await this.$api.get(`/items/materials?filter[vendor_id][_eq]=${this.vendorId}`);
        if (response.data && response.data.data) {
          this.materials = response.data.data;
        }
      } catch (error) {
        console.error('Error loading materials:', error);
        throw error;
      }
    },

    async loadAnimations() {
      try {
        const response = await this.$api.get(`/items/animations?filter[vendor_id][_eq]=${this.vendorId}`);
        if (response.data && response.data.data) {
          this.animations = response.data.data;
        }
      } catch (error) {
        console.error('Error loading animations:', error);
        throw error;
      }
    },

    setActiveTab(tabId) {
      this.activeTab = tabId;

      // Load specific data based on active tab
      switch (tabId) {
        case 'layout':
          if (this.showrooms.length === 0) {
            this.loadShowrooms();
          }
          break;
        case 'product':
          if (this.products.length === 0) {
            this.loadProducts();
          }
          break;
        case 'material':
          if (this.materials.length === 0) {
            this.loadMaterials();
          }
          break;
        case 'animation':
          if (this.animations.length === 0) {
            this.loadAnimations();
          }
          break;
      }
    },

    handleLayoutUpdate(data) {
      this.$emit('update', { type: 'layout', data });
      this.updateLastSaved();

      // If auto-save is enabled, update the showrooms list
      if (this.autoSaveEnabled) {
        const index = this.showrooms.findIndex(item => item.id === data.id);
        if (index !== -1) {
          this.showrooms.splice(index, 1, data);
        } else {
          this.showrooms.push(data);
        }
      }
    },

    handleProductUpdate(data) {
      this.$emit('update', { type: 'product', data });
      this.updateLastSaved();

      // If auto-save is enabled, update the products list
      if (this.autoSaveEnabled) {
        const index = this.products.findIndex(item => item.id === data.id);
        if (index !== -1) {
          this.products.splice(index, 1, data);
        } else {
          this.products.push(data);
        }
      }
    },

    handleMaterialUpdate(data) {
      this.$emit('update', { type: 'material', data });
      this.updateLastSaved();

      // If auto-save is enabled, update the materials list
      if (this.autoSaveEnabled) {
        const index = this.materials.findIndex(item => item.id === data.id);
        if (index !== -1) {
          this.materials.splice(index, 1, data);
        } else {
          this.materials.push(data);
        }
      }
    },

    handleLightingUpdate(data) {
      this.$emit('update', { type: 'lighting', data });
      this.updateLastSaved();
    },

    handleAnimationUpdate(data) {
      this.$emit('update', { type: 'animation', data });
      this.updateLastSaved();

      // If auto-save is enabled, update the animations list
      if (this.autoSaveEnabled) {
        const index = this.animations.findIndex(item => item.id === data.id);
        if (index !== -1) {
          this.animations.splice(index, 1, data);
        } else {
          this.animations.push(data);
        }
      }
    },

    updateLastSaved() {
      this.lastSaved = new Date();
    },

    toggleAutoSave() {
      this.autoSaveEnabled = !this.autoSaveEnabled;
    }
  }
};
</script>

<style scoped>
.visual-editors {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
}

.visual-editors-header {
  display: flex;
  flex-direction: column;
  padding: 16px 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.visual-editors-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.editor-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.auto-save-toggle {
  display: flex;
  align-items: center;
}

.toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-text {
  margin-left: 8px;
  font-size: 14px;
}

.last-saved {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.visual-editors-tabs {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 16px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
  color: var(--theme--foreground);
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.tab-button:hover {
  background-color: var(--theme--background-accent);
}

.tab-button.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.selection-controls {
  margin-top: 16px;
  padding: 16px;
  background-color: var(--theme--background-subdued);
  border-radius: 4px;
}

.select-container {
  display: flex;
  align-items: center;
  gap: 16px;
}

.editor-select {
  padding: 8px 12px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  min-width: 200px;
}

.new-item-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-item-button:hover {
  background-color: var(--theme--primary-accent);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  flex: 1;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme--background-subdued);
  border-top: 4px solid var(--theme--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: var(--theme--foreground-subdued);
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  flex: 1;
}

.error-icon {
  font-size: 48px;
  color: var(--theme--danger);
  margin-bottom: 16px;
}

.error-message {
  font-size: 16px;
  color: var(--theme--foreground);
  margin-bottom: 24px;
  text-align: center;
}

.retry-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: var(--theme--primary-accent);
}

.visual-editors-content {
  flex: 1;
  overflow: auto;
}

.editor-container {
  height: 100%;
  overflow: auto;
  padding: 16px;
}
</style>
