<template>
  <div class="collaboration-features">
    <div class="collaboration-header">
      <h2 class="collaboration-title">Collaboration Features</h2>
      
      <div class="collaboration-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          class="tab-button"
          :class="{ active: activeTab === tab.id }"
          @click="activeTab = tab.id"
        >
          <i class="material-icons">{{ tab.icon }}</i>
          <span>{{ tab.name }}</span>
        </button>
      </div>
    </div>
    
    <div class="collaboration-content">
      <!-- Team Members Tab -->
      <div v-if="activeTab === 'team-members'" class="tab-content">
        <TeamMemberManagement />
      </div>
      
      <!-- Comments & Feedback Tab -->
      <div v-else-if="activeTab === 'comments-feedback'" class="tab-content">
        <CommentingFeedbackSystem />
      </div>
      
      <!-- Activity Tracking Tab -->
      <div v-else-if="activeTab === 'activity-tracking'" class="tab-content">
        <ActivityTracking />
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import TeamMemberManagement from './TeamMemberManagement.vue';
import CommentingFeedbackSystem from './CommentingFeedbackSystem.vue';
import ActivityTracking from './ActivityTracking.vue';

export default {
  name: 'CollaborationFeatures',
  
  components: {
    TeamMemberManagement,
    CommentingFeedbackSystem,
    ActivityTracking
  },
  
  setup() {
    // Available tabs
    const tabs = [
      { id: 'team-members', name: 'Team Members', icon: 'people' },
      { id: 'comments-feedback', name: 'Comments & Feedback', icon: 'comment' },
      { id: 'activity-tracking', name: 'Activity Tracking', icon: 'history' }
    ];
    
    // Active tab
    const activeTab = ref('team-members');
    
    return {
      tabs,
      activeTab
    };
  }
};
</script>

<style scoped>
.collaboration-features {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: var(--theme--background);
}

.collaboration-header {
  display: flex;
  flex-direction: column;
  padding: 16px 24px;
  border-bottom: 1px solid var(--theme--border-color);
}

.collaboration-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
}

.collaboration-tabs {
  display: flex;
  gap: 8px;
  border-bottom: 1px solid var(--theme--border-color);
  padding-bottom: 8px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  transition: all 0.2s;
}

.tab-button:hover {
  color: var(--theme--foreground);
}

.tab-button.active {
  color: var(--theme--primary);
  border-bottom-color: var(--theme--primary);
}

.collaboration-content {
  flex: 1;
  overflow: hidden;
}

.tab-content {
  height: 100%;
}
</style>
