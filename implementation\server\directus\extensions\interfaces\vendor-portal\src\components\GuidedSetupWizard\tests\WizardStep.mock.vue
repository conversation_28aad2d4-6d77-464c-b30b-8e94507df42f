<template>
  <div 
    class="wizard-step" 
    :class="{ 'active': isActive, 'completed': isCompleted }"
    @click="$emit('step-click')"
  >
    <div class="wizard-step-title">{{ title }}</div>
    <div class="wizard-step-description">{{ description }}</div>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'WizardStep',
  props: {
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      default: ''
    },
    isActive: {
      type: Boolean,
      default: false
    },
    isCompleted: {
      type: Boolean,
      default: false
    },
    stepData: {
      type: Object,
      default: () => ({})
    },
    showHelpTips: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localStepData: this.stepData
    };
  },
  watch: {
    stepData: {
      handler(newVal) {
        this.localStepData = newVal;
      },
      deep: true
    }
  },
  methods: {
    updateStepData(data) {
      this.localStepData = data;
      this.$emit('update:step-data', data);
    }
  }
};
</script>

<style scoped>
.wizard-step {
  padding: 15px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.wizard-step.active {
  border-color: #2196F3;
  background-color: #E3F2FD;
}

.wizard-step.completed {
  border-color: #4CAF50;
  background-color: #E8F5E9;
}

.wizard-step-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.wizard-step-description {
  color: #666;
  font-size: 0.9em;
}
</style>
