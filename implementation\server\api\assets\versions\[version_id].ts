import { Request, Response } from 'express';
import { supabase } from '../../../shared/utils/supabase';
import { logger } from '../../../shared/utils/logger';
import { AssetVersionService } from '../../../services/asset/asset-version-service';

// Initialize asset version service
const assetVersionService = new AssetVersionService(supabase);

/**
 * Get asset version by ID
 *
 * @param req - Request
 * @param res - Response
 */
export const getAssetVersion = async (req: Request, res: Response): Promise<void> => {
  try {
    const { version_id } = req.params;

    // Validate version ID
    if (!version_id) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_VERSION_ID',
          message: 'Version ID is required',
        },
      });
      return;
    }

    // Get version
    const version = await assetVersionService.getAssetVersion(version_id);

    if (!version) {
      res.status(404).json({
        success: false,
        error: {
          code: 'VERSION_NOT_FOUND',
          message: 'Asset version not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: version,
    });
  } catch (error) {
    logger.error('Error getting asset version', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Get asset diff
 *
 * @param req - Request
 * @param res - Response
 */
export const getAssetDiff = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset_id, base_version, target_version } = req.query;

    // Validate parameters
    if (!asset_id || !base_version || !target_version) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Asset ID, base version, and target version are required',
        },
      });
      return;
    }

    // Get diff
    const diff = await assetVersionService.getAssetDiff(
      asset_id as string,
      base_version as string,
      target_version as string,
    );

    if (!diff) {
      res.status(404).json({
        success: false,
        error: {
          code: 'DIFF_NOT_FOUND',
          message: 'Asset diff not found',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: diff,
    });
  } catch (error) {
    logger.error('Error getting asset diff', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};

/**
 * Calculate asset delta
 *
 * @param req - Request
 * @param res - Response
 */
export const calculateAssetDelta = async (req: Request, res: Response): Promise<void> => {
  try {
    const { asset_id, base_version, target_version } = req.query;

    // Validate parameters
    if (!asset_id || !base_version || !target_version) {
      res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMETERS',
          message: 'Asset ID, base version, and target version are required',
        },
      });
      return;
    }

    // Calculate delta
    const delta = await assetVersionService.calculateAssetDelta(
      asset_id as string,
      base_version as string,
      target_version as string,
    );

    if (!delta) {
      res.status(404).json({
        success: false,
        error: {
          code: 'DELTA_CALCULATION_FAILED',
          message: 'Failed to calculate asset delta',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: delta,
    });
  } catch (error) {
    logger.error('Error calculating asset delta', { error });
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred',
      },
    });
  }
};
