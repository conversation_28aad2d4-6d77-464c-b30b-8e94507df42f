const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the directory of this script
const scriptDir = __dirname;
const projectRoot = path.resolve(scriptDir, '..');

// Define test files
const testFiles = [
  path.join(scriptDir, 'VisualEditors.spec.js'),
  path.join(scriptDir, 'VisualEditorsDeviceCompatibility.spec.js'),
  path.join(scriptDir, 'VisualEditorsPerformance.spec.js'),
  path.join(scriptDir, 'AnimationEditor.spec.js'),
  path.join(scriptDir, 'AnimationAdvancedFeatures.spec.js'),
  path.join(scriptDir, 'VisualEditorsPerformanceOptimization.spec.js'),
  path.join(scriptDir, 'VisualEditorsIntegration.spec.js'),
];

// Ensure all test files exist
testFiles.forEach(file => {
  if (!fs.existsSync(file)) {
    console.error(`Test file not found: ${file}`);
    process.exit(1);
  }
});

// Run the tests
console.log('Running Visual Editors tests...');
try {
  // Use Vitest to run the tests
  const command = `cd "${projectRoot}" && npx vitest run ${testFiles.map(f => `"${f}"`).join(' ')} --reporter verbose`;
  execSync(command, { stdio: 'inherit' });
  console.log('All tests completed successfully!');
} catch (error) {
  console.error('Tests failed with error:', error.message);
  process.exit(1);
}
