/**
 * Directus utility functions for API integration
 */

/**
 * Get the Directus API URL
 * @returns {string} The Directus API URL
 */
export function getDirectusUrl() {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined' && window.directus) {
    return window.directus.url;
  }
  
  // Default to the current origin with /api path
  if (typeof window !== 'undefined') {
    return `${window.location.origin}/api`;
  }
  
  // Fallback for server-side rendering
  return process.env.DIRECTUS_URL || 'http://localhost:8055';
}

/**
 * Get the current Directus authentication token
 * @returns {string|null} The authentication token or null if not authenticated
 */
export function getDirectusToken() {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    // Try to get the token from the Directus global object
    if (window.directus && window.directus.auth && window.directus.auth.token) {
      return window.directus.auth.token;
    }
    
    // Try to get the token from localStorage
    try {
      const authData = JSON.parse(localStorage.getItem('directus_auth'));
      if (authData && authData.access_token) {
        return authData.access_token;
      }
    } catch (error) {
      console.error('Error parsing auth data from localStorage:', error);
    }
  }
  
  // Fallback for server-side rendering or when token is not available
  return null;
}

/**
 * Get the current user's vendor ID
 * @returns {string|null} The vendor ID or null if not available
 */
export function getCurrentVendorId() {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    // Try to get the user from the Directus global object
    if (window.directus && window.directus.auth && window.directus.auth.user) {
      return window.directus.auth.user.vendor_id;
    }
    
    // Try to get the user from localStorage
    try {
      const authData = JSON.parse(localStorage.getItem('directus_auth'));
      if (authData && authData.user && authData.user.vendor_id) {
        return authData.user.vendor_id;
      }
    } catch (error) {
      console.error('Error parsing auth data from localStorage:', error);
    }
  }
  
  // Fallback for server-side rendering or when vendor ID is not available
  return null;
}

/**
 * Get the current user's role
 * @returns {string|null} The role or null if not available
 */
export function getCurrentUserRole() {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    // Try to get the user from the Directus global object
    if (window.directus && window.directus.auth && window.directus.auth.user) {
      return window.directus.auth.user.role;
    }
    
    // Try to get the user from localStorage
    try {
      const authData = JSON.parse(localStorage.getItem('directus_auth'));
      if (authData && authData.user && authData.user.role) {
        return authData.user.role;
      }
    } catch (error) {
      console.error('Error parsing auth data from localStorage:', error);
    }
  }
  
  // Fallback for server-side rendering or when role is not available
  return null;
}

/**
 * Check if the current user has a specific permission
 * @param {string} collection - The collection to check
 * @param {string} action - The action to check (create, read, update, delete)
 * @returns {boolean} Whether the user has the permission
 */
export function hasPermission(collection, action) {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined' && window.directus) {
    // Try to get the permissions from the Directus global object
    if (window.directus.permissions) {
      const permission = window.directus.permissions.find(
        p => p.collection === collection && p.action === action
      );
      
      return !!permission;
    }
  }
  
  // Default to true for admin role
  const role = getCurrentUserRole();
  return role === 'admin';
}

/**
 * Format a date for display
 * @param {string|Date} date - The date to format
 * @param {Object} options - The Intl.DateTimeFormat options
 * @returns {string} The formatted date
 */
export function formatDate(date, options = {}) {
  if (!date) return '';
  
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  return new Intl.DateTimeFormat(
    navigator.language || 'en-US',
    { ...defaultOptions, ...options }
  ).format(dateObj);
}

/**
 * Get a file URL from a file ID
 * @param {string} fileId - The file ID
 * @returns {string} The file URL
 */
export function getFileUrl(fileId) {
  if (!fileId) return '';
  
  const baseUrl = getDirectusUrl();
  return `${baseUrl}/assets/${fileId}`;
}

export default {
  getDirectusUrl,
  getDirectusToken,
  getCurrentVendorId,
  getCurrentUserRole,
  hasPermission,
  formatDate,
  getFileUrl
};
