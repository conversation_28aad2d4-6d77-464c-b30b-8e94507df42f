import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../shared/utils/logger';
import { errorHandler } from '../../../shared/utils/error-handler';
import { SceneService } from '../../../services/scene-service';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  scene_id: z.string().uuid(),
});

// Define the request body schema for POST/PUT
const PostBodySchema = z.object({
  name: z.string(),
  description: z.string().nullable(),
  flow: z.record(z.any()),
  is_active: z.boolean().optional(),
});

/**
 * Scene Flow API endpoint
 *
 * This endpoint handles getting and updating the flow configuration for a scene.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse({
      scene_id: req.query.scene_id,
    });

    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { scene_id } = queryResult.data;

    // Log the request
    logger.info('Scene flow request', {
      scene_id,
      method: req.method,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create scene service
    const sceneService = new SceneService(supabase);

    // Get scene
    const scene = await sceneService.getScene(scene_id);
    if (!scene) {
      return res.status(404).json({ error: 'Scene not found' });
    }

    // Check if user has permission to access this scene
    if (session.user.id !== scene.vendor_id && session.user.role !== 'admin') {
      return res.status(403).json({ error: 'Forbidden' });
    }

    // Handle GET request
    if (req.method === 'GET') {
      // Get scene flow
      const flow = await sceneService.getSceneFlow(scene_id);
      if (!flow) {
        return res.status(404).json({ error: 'Scene flow not found' });
      }

      // Prepare response
      const response = {
        status: 'success',
        data: {
          id: flow.id,
          scene_id: flow.scene_id,
          name: flow.name,
          description: flow.description,
          version: flow.version,
          is_active: flow.is_active,
          ...flow.flow,
        },
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        },
      };

      // Return flow
      return res.status(200).json(response);
    }

    // Handle POST/PUT request (create/update flow)
    if (req.method === 'POST' || req.method === 'PUT') {
      // Validate request body
      const bodyResult = PostBodySchema.safeParse(req.body);
      if (!bodyResult.success) {
        return res
          .status(400)
          .json({ error: 'Invalid request body', details: bodyResult.error.format() });
      }

      // Extract request data
      const { name, description, flow, is_active = true } = bodyResult.data;

      // Create or update flow
      const updatedFlow = await sceneService.createOrUpdateSceneFlow(
        scene_id,
        name,
        description,
        flow,
        is_active,
      );
      if (!updatedFlow) {
        return res.status(500).json({ error: 'Failed to create/update scene flow' });
      }

      // Prepare response
      const response = {
        status: 'success',
        data: {
          id: updatedFlow.id,
          scene_id: updatedFlow.scene_id,
          name: updatedFlow.name,
          description: updatedFlow.description,
          version: updatedFlow.version,
          is_active: updatedFlow.is_active,
          ...updatedFlow.flow,
        },
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0.0',
        },
      };

      // Return updated flow
      return res.status(200).json(response);
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    logger.error('Unexpected error in scene flow endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
