import { mount } from '@vue/test-utils';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import VisualEditors from '../src/components/VisualEditors/VisualEditors.vue';
import ShowroomLayoutEditor from '../src/components/VisualEditors/ShowroomLayoutEditor.vue';
import ProductConfigurator from '../src/components/VisualEditors/ProductConfigurator.vue';
import MaterialTextureEditor from '../src/components/VisualEditors/MaterialTextureEditor.vue';
import LightingEditor from '../src/components/VisualEditors/LightingEditor.vue';
import AnimationEditor from '../src/components/VisualEditors/AnimationEditor.vue';

// Mock API
const mockApi = {
  get: vi.fn(),
  post: vi.fn(),
  patch: vi.fn(),
  delete: vi.fn()
};

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

describe('Visual Editors Integration Tests', () => {
  let wrapper;
  
  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true
    });
    
    // Mock API responses
    mockApi.get.mockImplementation((url) => {
      if (url.includes('/items/showroom_layouts')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'showroom1', name: 'Test Showroom', vendor_id: 'vendor1' }
            ]
          }
        });
      } else if (url.includes('/items/products')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'product1', name: 'Test Product', vendor_id: 'vendor1' }
            ]
          }
        });
      } else if (url.includes('/items/materials')) {
        return Promise.resolve({
          data: {
            data: [
              { id: 'material1', name: 'Test Material', vendor_id: 'vendor1' }
            ]
          }
        });
      } else if (url.includes('/items/animations')) {
        return Promise.resolve({
          data: {
            data: [
              { 
                id: 'anim1', 
                name: 'Test Animation', 
                vendor_id: 'vendor1',
                duration: 5,
                tracks: []
              }
            ]
          }
        });
      } else if (url.includes('/items/showroom_lighting')) {
        return Promise.resolve({
          data: {
            data: [
              { 
                id: 'lighting1', 
                showroom_id: 'showroom1',
                lights: []
              }
            ]
          }
        });
      }
      return Promise.resolve({ data: { data: [] } });
    });
    
    // Create wrapper
    wrapper = mount(VisualEditors, {
      props: {
        vendorId: 'vendor1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
  });

  it('loads all editor data on mount', async () => {
    // Wait for component to load
    await wrapper.vm.$nextTick();
    
    // Check if API was called for all editors
    expect(mockApi.get).toHaveBeenCalledWith(expect.stringContaining('/items/showroom_layouts'));
    expect(mockApi.get).toHaveBeenCalledWith(expect.stringContaining('/items/products'));
    expect(mockApi.get).toHaveBeenCalledWith(expect.stringContaining('/items/materials'));
    expect(mockApi.get).toHaveBeenCalledWith(expect.stringContaining('/items/animations'));
    
    // Check if data was loaded
    expect(wrapper.vm.showrooms.length).toBe(1);
    expect(wrapper.vm.products.length).toBe(1);
    expect(wrapper.vm.materials.length).toBe(1);
    expect(wrapper.vm.animations.length).toBe(1);
  });

  it('switches between editors', async () => {
    // Wait for component to load
    await wrapper.vm.$nextTick();
    
    // Check initial tab
    expect(wrapper.vm.activeTab).toBe('layout');
    expect(wrapper.find('.editor-container').exists()).toBe(true);
    
    // Switch to product tab
    await wrapper.vm.setActiveTab('product');
    await wrapper.vm.$nextTick();
    
    // Check if tab changed
    expect(wrapper.vm.activeTab).toBe('product');
    
    // Switch to material tab
    await wrapper.vm.setActiveTab('material');
    await wrapper.vm.$nextTick();
    
    // Check if tab changed
    expect(wrapper.vm.activeTab).toBe('material');
    
    // Switch to lighting tab
    await wrapper.vm.setActiveTab('lighting');
    await wrapper.vm.$nextTick();
    
    // Check if tab changed
    expect(wrapper.vm.activeTab).toBe('lighting');
    
    // Switch to animation tab
    await wrapper.vm.setActiveTab('animation');
    await wrapper.vm.$nextTick();
    
    // Check if tab changed
    expect(wrapper.vm.activeTab).toBe('animation');
  });

  it('handles updates from child components', async () => {
    // Wait for component to load
    await wrapper.vm.$nextTick();
    
    // Mock update from layout editor
    const layoutData = { id: 'showroom1', name: 'Updated Showroom' };
    wrapper.vm.handleLayoutUpdate(layoutData);
    
    // Check if event was emitted
    expect(wrapper.emitted().update).toBeTruthy();
    expect(wrapper.emitted().update[0][0]).toEqual({ type: 'layout', data: layoutData });
    
    // Check if last saved was updated
    expect(wrapper.vm.lastSaved).not.toBeNull();
  });

  it('implements caching for API responses', async () => {
    // Wait for component to load
    await wrapper.vm.$nextTick();
    
    // Check if localStorage was used
    expect(mockLocalStorage.getItem).toHaveBeenCalled();
    
    // Mock cached data
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'showrooms_vendor1') {
        return JSON.stringify([
          { id: 'showroom1', name: 'Cached Showroom', vendor_id: 'vendor1' }
        ]);
      }
      return null;
    });
    
    // Reset API mock
    mockApi.get.mockClear();
    
    // Load showrooms again
    await wrapper.vm.loadShowrooms();
    
    // Check if API was not called (using cache)
    expect(mockApi.get).not.toHaveBeenCalled();
    
    // Check if cached data was used
    expect(wrapper.vm.showrooms[0].name).toBe('Cached Showroom');
  });

  it('handles auto-save toggle', async () => {
    // Wait for component to load
    await wrapper.vm.$nextTick();
    
    // Check initial auto-save state
    expect(wrapper.vm.autoSaveEnabled).toBe(true);
    
    // Toggle auto-save
    await wrapper.vm.toggleAutoSave();
    
    // Check if auto-save was toggled
    expect(wrapper.vm.autoSaveEnabled).toBe(false);
    
    // Toggle auto-save again
    await wrapper.vm.toggleAutoSave();
    
    // Check if auto-save was toggled back
    expect(wrapper.vm.autoSaveEnabled).toBe(true);
  });

  it('handles errors during data loading', async () => {
    // Reset wrapper
    wrapper.unmount();
    
    // Mock API error
    mockApi.get.mockRejectedValue(new Error('API Error'));
    
    // Create new wrapper
    wrapper = mount(VisualEditors, {
      props: {
        vendorId: 'vendor1'
      },
      global: {
        mocks: {
          $api: mockApi
        }
      }
    });
    
    // Wait for component to load
    await wrapper.vm.$nextTick();
    
    // Check if error was set
    expect(wrapper.vm.error).not.toBeNull();
    expect(wrapper.vm.error).toContain('Failed to load data');
  });

  it('integrates with localStorage for caching', async () => {
    // Wait for component to load
    await wrapper.vm.$nextTick();
    
    // Update showrooms
    wrapper.vm.showrooms = [
      { id: 'showroom1', name: 'Updated Showroom', vendor_id: 'vendor1' }
    ];
    
    // Call cache method
    wrapper.vm.cacheData('showrooms', wrapper.vm.showrooms);
    
    // Check if localStorage was used
    expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
      'showrooms_vendor1',
      JSON.stringify(wrapper.vm.showrooms)
    );
  });
});
