<template>
  <div class="admin-dashboard">
    <div class="d-flex justify-space-between align-center mb-4">
      <h2>Admin Dashboard</h2>
      <div>
        <v-btn color="primary" class="mr-2" @click="customizeLayout">
          <v-icon left>mdi-view-dashboard-edit</v-icon>
          Customize Dashboard
        </v-btn>
        <v-btn color="secondary" @click="refreshDashboard">
          <v-icon left>mdi-refresh</v-icon>
          Refresh
        </v-btn>
      </div>
    </div>

    <!-- Dashboard Widgets -->
    <v-row v-if="!isCustomizing">
      <draggable
        v-model="dashboardWidgets"
        class="row-draggable"
        handle=".widget-drag-handle"
        :animation="200"
        ghost-class="ghost-widget"
      >
        <template v-for="(widget, index) in dashboardWidgets">
          <v-col :key="widget.id" :cols="12" :md="widget.size">
            <dashboard-widget
              :widget-type="widget.type"
              :widget-title="widget.title"
              :widget-data="widget.data"
              :widget-settings="widget.settings"
              :loading="widget.loading"
              @refresh="refreshWidget(index)"
              @edit="editWidgetSettings(index)"
              @remove="removeWidget(index)"
            />
          </v-col>
        </template>
      </draggable>
    </v-row>

    <!-- Customize Mode -->
    <div v-else>
      <v-card class="mb-4">
        <v-card-title>Customize Dashboard</v-card-title>
        <v-card-text>
          <v-row>
            <v-col cols="12" md="6">
              <h3 class="mb-2">Available Widgets</h3>
              <v-list>
                <v-list-item v-for="widget in availableWidgets" :key="widget.type">
                  <v-list-item-content>
                    <v-list-item-title>{{ widget.title }}</v-list-item-title>
                    <v-list-item-subtitle>{{ widget.description }}</v-list-item-subtitle>
                  </v-list-item-content>
                  <v-list-item-action>
                    <v-btn small color="primary" @click="addWidget(widget)">
                      <v-icon>mdi-plus</v-icon>
                    </v-btn>
                  </v-list-item-action>
                </v-list-item>
              </v-list>
            </v-col>
            <v-col cols="12" md="6">
              <h3 class="mb-2">Current Layout</h3>
              <draggable
                v-model="dashboardWidgets"
                class="widget-list"
                handle=".widget-drag-handle"
                :animation="200"
                ghost-class="ghost-widget"
              >
                <v-list>
                  <v-list-item v-for="(widget, index) in dashboardWidgets" :key="widget.id">
                    <v-list-item-icon class="widget-drag-handle">
                      <v-icon>mdi-drag</v-icon>
                    </v-list-item-icon>
                    <v-list-item-content>
                      <v-list-item-title>{{ widget.title }}</v-list-item-title>
                    </v-list-item-content>
                    <v-list-item-action>
                      <v-btn icon small @click="editWidgetSettings(index)">
                        <v-icon>mdi-cog</v-icon>
                      </v-btn>
                    </v-list-item-action>
                    <v-list-item-action>
                      <v-btn icon small color="error" @click="removeWidget(index)">
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </v-list-item-action>
                  </v-list-item>
                </v-list>
              </draggable>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="cancelCustomization">Cancel</v-btn>
          <v-btn color="primary" @click="saveCustomization">Save Layout</v-btn>
        </v-card-actions>
      </v-card>
    </div>

    <!-- Quick Links -->
    <v-row class="mt-4">
      <v-col cols="12">
        <v-card>
          <v-card-title>Quick Links</v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="6" md="3">
                <v-btn block color="primary" class="mb-2" to="/admin/system-monitoring">
                  <v-icon left>mdi-monitor-dashboard</v-icon>
                  System Monitoring
                </v-btn>
              </v-col>

              <v-col cols="6" md="3">
                <v-btn block color="primary" class="mb-2" to="/admin/users">
                  <v-icon left>mdi-account-group</v-icon>
                  User Management
                </v-btn>
              </v-col>

              <v-col cols="6" md="3">
                <v-btn block color="primary" class="mb-2" to="/admin/vendors">
                  <v-icon left>mdi-store</v-icon>
                  Vendor Management
                </v-btn>
              </v-col>

              <v-col cols="6" md="3">
                <v-btn block color="primary" class="mb-2" to="/admin/settings">
                  <v-icon left>mdi-cog</v-icon>
                  Settings
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Widget Settings Dialog -->
    <v-dialog v-model="widgetSettingsDialog" max-width="500px">
      <v-card>
        <v-card-title>Widget Settings</v-card-title>
        <v-card-text>
          <v-form ref="widgetSettingsForm">
            <v-text-field
              v-model="editingWidget.title"
              label="Widget Title"
              required
            ></v-text-field>

            <v-select
              v-model="editingWidget.size"
              :items="widgetSizeOptions"
              label="Widget Size"
              required
            ></v-select>

            <v-select
              v-model="editingWidget.refreshInterval"
              :items="refreshIntervalOptions"
              label="Auto-refresh Interval"
              required
            ></v-select>

            <!-- Additional settings based on widget type -->
            <div v-if="editingWidget && editingWidget.type === 'chart' && editingWidget.settings">
              <v-select
                v-model="editingWidget.settings.chartType"
                :items="chartTypeOptions"
                label="Chart Type"
                required
              ></v-select>

              <v-switch
                v-model="editingWidget.settings.showLegend"
                label="Show Legend"
                color="primary"
              ></v-switch>
            </div>

            <div v-if="editingWidget && editingWidget.type === 'stats' && editingWidget.settings">
              <v-switch
                v-model="editingWidget.settings.showTrend"
                label="Show Trend Indicators"
                color="primary"
              ></v-switch>

              <v-switch
                v-model="editingWidget.settings.showChart"
                label="Show Mini Chart"
                color="primary"
              ></v-switch>
            </div>

            <div v-if="editingWidget && editingWidget.type === 'activity' && editingWidget.settings">
              <v-text-field
                v-model.number="editingWidget.settings.limit"
                label="Number of Activities to Show"
                type="number"
                min="1"
                max="20"
                required
              ></v-text-field>

              <v-switch
                v-model="editingWidget.settings.showTimestamp"
                label="Show Timestamps"
                color="primary"
              ></v-switch>

              <v-switch
                v-model="editingWidget.settings.showActions"
                label="Show Action Buttons"
                color="primary"
              ></v-switch>

              <v-switch
                v-model="editingWidget.settings.showViewAll"
                label="Show View All Button"
                color="primary"
              ></v-switch>
            </div>

            <div v-if="editingWidget && editingWidget.type === 'system' && editingWidget.settings">
              <v-switch
                v-model="editingWidget.settings.showServices"
                label="Show Services Status"
                color="primary"
              ></v-switch>

              <v-switch
                v-model="editingWidget.settings.showAlerts"
                label="Show Alerts"
                color="primary"
              ></v-switch>
            </div>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="widgetSettingsDialog = false">Cancel</v-btn>
          <v-btn color="primary" @click="saveWidgetSettings">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { SystemStatusOverview, AlertSummary } from './SystemMonitoring';
import { DashboardWidget } from './Dashboard';
import draggable from 'vuedraggable';
import { v4 as uuidv4 } from 'uuid';

export default {
  name: 'AdminDashboard',

  components: {
    SystemStatusOverview,
    AlertSummary,
    DashboardWidget,
    draggable
  },

  data() {
    return {
      stats: {
        totalUsers: 0,
        totalVendors: 0,
        totalShowrooms: 0,
        totalProducts: 0
      },
      systemHealth: null,
      loadingSystemHealth: false,
      recentActivity: [],
      isCustomizing: false,
      dashboardWidgets: [],
      savedDashboardWidgets: [],
      availableWidgets: [
        {
          type: 'stats',
          title: 'User Statistics',
          description: 'Shows user registration and activity statistics',
          defaultSize: 3,
          defaultSettings: {
            showTrend: true,
            showChart: true,
            dataPoints: 7
          }
        },
        {
          type: 'stats',
          title: 'Vendor Statistics',
          description: 'Shows vendor registration and activity statistics',
          defaultSize: 3,
          defaultSettings: {
            showTrend: true,
            showChart: true,
            dataPoints: 7
          }
        },
        {
          type: 'stats',
          title: 'Showroom Statistics',
          description: 'Shows showroom creation and usage statistics',
          defaultSize: 3,
          defaultSettings: {
            showTrend: true,
            showChart: true,
            dataPoints: 7
          }
        },
        {
          type: 'stats',
          title: 'Product Statistics',
          description: 'Shows product creation and usage statistics',
          defaultSize: 3,
          defaultSettings: {
            showTrend: true,
            showChart: true,
            dataPoints: 7
          }
        },
        {
          type: 'chart',
          title: 'User Growth',
          description: 'Chart showing user growth over time',
          defaultSize: 6,
          defaultSettings: {
            chartType: 'line',
            timeRange: 'month',
            showLegend: true
          }
        },
        {
          type: 'chart',
          title: 'Vendor Distribution',
          description: 'Chart showing vendor distribution by region',
          defaultSize: 6,
          defaultSettings: {
            chartType: 'pie',
            showLegend: true
          }
        },
        {
          type: 'activity',
          title: 'Recent Activity',
          description: 'Shows recent system activity',
          defaultSize: 6,
          defaultSettings: {
            limit: 10,
            showTimestamp: true,
            showActions: true,
            showViewAll: true
          }
        },
        {
          type: 'system',
          title: 'System Health',
          description: 'Shows system health metrics',
          defaultSize: 6,
          defaultSettings: {
            showServices: true,
            showAlerts: true
          }
        }
      ],
      widgetSettingsDialog: false,
      editingWidgetIndex: -1,
      editingWidget: null,
      widgetSizeOptions: [
        { text: 'Small (25%)', value: 3 },
        { text: 'Medium (50%)', value: 6 },
        { text: 'Large (75%)', value: 9 },
        { text: 'Full Width (100%)', value: 12 }
      ],
      refreshIntervalOptions: [
        { text: 'Never', value: 0 },
        { text: '30 seconds', value: 30 },
        { text: '1 minute', value: 60 },
        { text: '5 minutes', value: 300 },
        { text: '15 minutes', value: 900 },
        { text: '30 minutes', value: 1800 },
        { text: '1 hour', value: 3600 }
      ],
      chartTypeOptions: [
        { text: 'Line Chart', value: 'line' },
        { text: 'Bar Chart', value: 'bar' },
        { text: 'Pie Chart', value: 'pie' },
        { text: 'Doughnut Chart', value: 'doughnut' },
        { text: 'Area Chart', value: 'area' }
      ]
    };
  },

  mounted() {
    this.loadDashboardLayout();
    this.fetchStats();
    this.fetchSystemHealth();
    this.fetchRecentActivity();
  },

  methods: {
    // Load dashboard layout from localStorage
    loadDashboardLayout() {
      try {
        const savedLayout = localStorage.getItem('adminDashboardLayout');

        if (savedLayout) {
          this.dashboardWidgets = JSON.parse(savedLayout);
          this.savedDashboardWidgets = JSON.parse(JSON.stringify(this.dashboardWidgets));
        } else {
          // Set default layout
          this.setDefaultLayout();
        }

        // Fetch data for each widget
        this.dashboardWidgets.forEach((widget, index) => {
          this.fetchWidgetData(index);
        });
      } catch (error) {
        console.error('Error loading dashboard layout:', error);
        this.setDefaultLayout();
      }
    },

    // Set default dashboard layout
    setDefaultLayout() {
      this.dashboardWidgets = [
        {
          id: uuidv4(),
          type: 'stats',
          title: 'User Statistics',
          size: 3,
          settings: {
            showTrend: true,
            showChart: true,
            dataPoints: 7
          },
          data: null,
          loading: true
        },
        {
          id: uuidv4(),
          type: 'stats',
          title: 'Vendor Statistics',
          size: 3,
          settings: {
            showTrend: true,
            showChart: true,
            dataPoints: 7
          },
          data: null,
          loading: true
        },
        {
          id: uuidv4(),
          type: 'stats',
          title: 'Showroom Statistics',
          size: 3,
          settings: {
            showTrend: true,
            showChart: true,
            dataPoints: 7
          },
          data: null,
          loading: true
        },
        {
          id: uuidv4(),
          type: 'stats',
          title: 'Product Statistics',
          size: 3,
          settings: {
            showTrend: true,
            showChart: true,
            dataPoints: 7
          },
          data: null,
          loading: true
        },
        {
          id: uuidv4(),
          type: 'system',
          title: 'System Health',
          size: 6,
          settings: {
            showServices: true,
            showAlerts: true
          },
          data: null,
          loading: true
        },
        {
          id: uuidv4(),
          type: 'activity',
          title: 'Recent Activity',
          size: 6,
          settings: {
            limit: 10,
            showTimestamp: true,
            showActions: true,
            showViewAll: true
          },
          data: null,
          loading: true
        }
      ];

      this.savedDashboardWidgets = JSON.parse(JSON.stringify(this.dashboardWidgets));
    },

    // Fetch dashboard stats
    async fetchStats() {
      try {
        const response = await fetch('/api/admin/dashboard/stats');
        const data = await response.json();

        if (data.success) {
          this.stats = data.data;
        } else {
          console.error('Error fetching dashboard stats:', data.error);

          // Use mock data for demonstration
          this.stats = {
            totalUsers: 1254,
            totalVendors: 87,
            totalShowrooms: 142,
            totalProducts: 3567
          };
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);

        // Use mock data for demonstration
        this.stats = {
          totalUsers: 1254,
          totalVendors: 87,
          totalShowrooms: 142,
          totalProducts: 3567
        };
      }
    },

    // Fetch system health
    async fetchSystemHealth() {
      this.loadingSystemHealth = true;

      try {
        const response = await fetch('/api/monitoring/system-health');
        const data = await response.json();

        if (data.success) {
          this.systemHealth = data.data;
        } else {
          console.error('Error fetching system health:', data.error);
        }
      } catch (error) {
        console.error('Error fetching system health:', error);
      } finally {
        this.loadingSystemHealth = false;
      }
    },

    // Fetch recent activity
    async fetchRecentActivity() {
      try {
        const response = await fetch('/api/admin/dashboard/recent-activity');
        const data = await response.json();

        if (data.success) {
          this.recentActivity = data.data;
        } else {
          console.error('Error fetching recent activity:', data.error);

          // Use mock data for demonstration
          this.recentActivity = [
            {
              type: 'user',
              message: 'New user registered: <EMAIL>',
              timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
            },
            {
              type: 'vendor',
              message: 'New vendor registered: Acme Furniture',
              timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
            },
            {
              type: 'showroom',
              message: 'New showroom created: Modern Living Room',
              timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
            },
            {
              type: 'product',
              message: 'New product added: Ergonomic Office Chair',
              timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()
            },
            {
              type: 'system',
              message: 'System update completed successfully',
              timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
            }
          ];
        }
      } catch (error) {
        console.error('Error fetching recent activity:', error);

        // Use mock data for demonstration
        this.recentActivity = [
          {
            type: 'user',
            message: 'New user registered: <EMAIL>',
            timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString()
          },
          {
            type: 'vendor',
            message: 'New vendor registered: Acme Furniture',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
          },
          {
            type: 'showroom',
            message: 'New showroom created: Modern Living Room',
            timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
          },
          {
            type: 'product',
            message: 'New product added: Ergonomic Office Chair',
            timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()
          },
          {
            type: 'system',
            message: 'System update completed successfully',
            timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
          }
        ];
      }
    },

    // Get activity icon
    getActivityIcon(type) {
      switch (type) {
        case 'user':
          return 'mdi-account';
        case 'vendor':
          return 'mdi-store';
        case 'showroom':
          return 'mdi-eye';
        case 'product':
          return 'mdi-package-variant-closed';
        case 'system':
          return 'mdi-cog';
        default:
          return 'mdi-information';
      }
    },

    // Get activity color
    getActivityColor(type) {
      switch (type) {
        case 'user':
          return 'primary';
        case 'vendor':
          return 'success';
        case 'showroom':
          return 'info';
        case 'product':
          return 'warning';
        case 'system':
          return 'grey';
        default:
          return 'grey';
      }
    },

    // Format timestamp
    formatTimestamp(timestamp) {
      if (!timestamp) return 'N/A';

      const date = new Date(timestamp);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);

      if (diffMins < 60) {
        return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
      } else if (diffMins < 1440) {
        const hours = Math.floor(diffMins / 60);
        return `${hours} hour${hours === 1 ? '' : 's'} ago`;
      } else {
        return date.toLocaleDateString();
      }
    },

    // Navigate to alerts page
    navigateToAlerts() {
      this.$router.push('/admin/system-monitoring');
    },

    // Customize dashboard layout
    customizeLayout() {
      this.isCustomizing = true;
      this.savedDashboardWidgets = JSON.parse(JSON.stringify(this.dashboardWidgets));
    },

    // Cancel customization
    cancelCustomization() {
      this.isCustomizing = false;
      this.dashboardWidgets = JSON.parse(JSON.stringify(this.savedDashboardWidgets));
    },

    // Save customization
    saveCustomization() {
      this.isCustomizing = false;
      this.savedDashboardWidgets = JSON.parse(JSON.stringify(this.dashboardWidgets));

      try {
        localStorage.setItem('adminDashboardLayout', JSON.stringify(this.dashboardWidgets));
      } catch (error) {
        console.error('Error saving dashboard layout:', error);
      }
    },

    // Add widget to dashboard
    addWidget(widgetTemplate) {
      const newWidget = {
        id: uuidv4(),
        type: widgetTemplate.type,
        title: widgetTemplate.title,
        size: widgetTemplate.defaultSize,
        settings: { ...widgetTemplate.defaultSettings },
        data: null,
        loading: true
      };

      this.dashboardWidgets.push(newWidget);

      // Fetch data for the new widget
      this.fetchWidgetData(this.dashboardWidgets.length - 1);
    },

    // Remove widget from dashboard
    removeWidget(index) {
      this.dashboardWidgets.splice(index, 1);
    },

    // Edit widget settings
    editWidgetSettings(index) {
      this.editingWidgetIndex = index;
      this.editingWidget = JSON.parse(JSON.stringify(this.dashboardWidgets[index]));
      this.widgetSettingsDialog = true;
    },

    // Save widget settings
    saveWidgetSettings() {
      if (this.editingWidgetIndex >= 0 && this.editingWidget) {
        this.dashboardWidgets[this.editingWidgetIndex].title = this.editingWidget.title;
        this.dashboardWidgets[this.editingWidgetIndex].size = this.editingWidget.size;
        this.dashboardWidgets[this.editingWidgetIndex].refreshInterval = this.editingWidget.refreshInterval;

        // Update settings based on widget type
        if (this.editingWidget.type === 'chart' && this.editingWidget.settings) {
          this.dashboardWidgets[this.editingWidgetIndex].settings.chartType = this.editingWidget.settings.chartType;
        }

        // Refresh widget data if needed
        this.fetchWidgetData(this.editingWidgetIndex);
      }

      this.widgetSettingsDialog = false;
      this.editingWidgetIndex = -1;
      this.editingWidget = null;
    },

    // Refresh dashboard
    refreshDashboard() {
      this.fetchStats();
      this.fetchSystemHealth();
      this.fetchRecentActivity();

      // Refresh all widgets
      this.dashboardWidgets.forEach((widget, index) => {
        this.refreshWidget(index);
      });
    },

    // Refresh specific widget
    refreshWidget(index) {
      this.fetchWidgetData(index);
    },

    // Fetch data for a specific widget
    async fetchWidgetData(index) {
      if (index < 0 || index >= this.dashboardWidgets.length) return;

      const widget = this.dashboardWidgets[index];
      widget.loading = true;

      try {
        switch (widget.type) {
          case 'stats':
            await this.fetchStatsWidgetData(widget);
            break;
          case 'chart':
            await this.fetchChartWidgetData(widget);
            break;
          case 'activity':
            await this.fetchActivityWidgetData(widget);
            break;
          case 'system':
            await this.fetchSystemWidgetData(widget);
            break;
        }
      } catch (error) {
        console.error(`Error fetching data for widget ${widget.title}:`, error);
      } finally {
        widget.loading = false;
      }
    },

    // Fetch data for stats widget
    async fetchStatsWidgetData(widget) {
      // Determine which stats to fetch based on widget title
      let endpoint = '/api/admin/dashboard/stats';
      let statsType = 'users';

      if (widget.title.toLowerCase().includes('user')) {
        statsType = 'users';
      } else if (widget.title.toLowerCase().includes('vendor')) {
        statsType = 'vendors';
      } else if (widget.title.toLowerCase().includes('showroom')) {
        statsType = 'showrooms';
      } else if (widget.title.toLowerCase().includes('product')) {
        statsType = 'products';
      }

      try {
        const response = await fetch(`${endpoint}?type=${statsType}`);
        const data = await response.json();

        if (data.success) {
          widget.data = data.data;
        } else {
          console.error(`Error fetching stats for ${statsType}:`, data.error);

          // Use mock data
          widget.data = this.getMockStatsData(statsType);
        }
      } catch (error) {
        console.error(`Error fetching stats for ${statsType}:`, error);

        // Use mock data
        widget.data = this.getMockStatsData(statsType);
      }
    },

    // Fetch data for chart widget
    async fetchChartWidgetData(widget) {
      // Determine which chart data to fetch based on widget title
      let endpoint = '/api/admin/dashboard/chart-data';
      let chartType = 'users';

      if (widget.title.toLowerCase().includes('user')) {
        chartType = 'users';
      } else if (widget.title.toLowerCase().includes('vendor')) {
        chartType = 'vendors';
      } else if (widget.title.toLowerCase().includes('showroom')) {
        chartType = 'showrooms';
      } else if (widget.title.toLowerCase().includes('product')) {
        chartType = 'products';
      }

      try {
        const response = await fetch(`${endpoint}?type=${chartType}`);
        const data = await response.json();

        if (data.success) {
          widget.data = data.data;
        } else {
          console.error(`Error fetching chart data for ${chartType}:`, data.error);

          // Use mock data
          widget.data = this.getMockChartData(chartType, widget.settings.chartType);
        }
      } catch (error) {
        console.error(`Error fetching chart data for ${chartType}:`, error);

        // Use mock data
        widget.data = this.getMockChartData(chartType, widget.settings.chartType);
      }
    },

    // Fetch data for activity widget
    async fetchActivityWidgetData(widget) {
      try {
        const response = await fetch('/api/admin/dashboard/recent-activity');
        const data = await response.json();

        if (data.success) {
          widget.data = {
            activities: data.data
          };
        } else {
          console.error('Error fetching activity data:', data.error);

          // Use mock data
          widget.data = {
            activities: this.recentActivity
          };
        }
      } catch (error) {
        console.error('Error fetching activity data:', error);

        // Use mock data
        widget.data = {
          activities: this.recentActivity
        };
      }
    },

    // Fetch data for system widget
    async fetchSystemWidgetData(widget) {
      try {
        const response = await fetch('/api/monitoring/system-health');
        const data = await response.json();

        if (data.success) {
          widget.data = {
            system: data.data
          };
        } else {
          console.error('Error fetching system data:', data.error);

          // Use mock data
          widget.data = {
            system: this.systemHealth
          };
        }
      } catch (error) {
        console.error('Error fetching system data:', error);

        // Use mock data
        widget.data = {
          system: this.systemHealth
        };
      }
    },

    // Get mock stats data
    getMockStatsData(type) {
      switch (type) {
        case 'users':
          return {
            stats: [
              {
                label: 'Total',
                value: '1,254',
                icon: 'mdi-account-group',
                color: 'primary',
                trend: 12,
                trendValue: 12
              },
              {
                label: 'Active',
                value: '1,180',
                icon: 'mdi-account-check',
                color: 'success',
                trend: 8,
                trendValue: 8
              },
              {
                label: 'New (30d)',
                value: '245',
                icon: 'mdi-account-plus',
                color: 'info',
                trend: 24,
                trendValue: 24
              },
              {
                label: 'Inactive',
                value: '74',
                icon: 'mdi-account-off',
                color: 'error',
                trend: -5,
                trendValue: 5
              }
            ],
            chartData: {
              values: [28, 40, 36, 52, 38, 60, 55],
              labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
              gradient: ['#00c6ff', '#0072ff']
            }
          };
        case 'vendors':
          return {
            stats: [
              {
                label: 'Total',
                value: '87',
                icon: 'mdi-store',
                color: 'primary',
                trend: 5,
                trendValue: 5
              },
              {
                label: 'Active',
                value: '82',
                icon: 'mdi-store-check',
                color: 'success',
                trend: 3,
                trendValue: 3
              },
              {
                label: 'New (30d)',
                value: '12',
                icon: 'mdi-store-plus',
                color: 'info',
                trend: 15,
                trendValue: 15
              },
              {
                label: 'Inactive',
                value: '5',
                icon: 'mdi-store-off',
                color: 'error',
                trend: -2,
                trendValue: 2
              }
            ],
            chartData: {
              values: [5, 7, 8, 10, 12, 9, 11],
              labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
              gradient: ['#00c6ff', '#0072ff']
            }
          };
        case 'showrooms':
          return {
            stats: [
              {
                label: 'Total',
                value: '142',
                icon: 'mdi-eye',
                color: 'primary',
                trend: 8,
                trendValue: 8
              },
              {
                label: 'Active',
                value: '135',
                icon: 'mdi-eye-check',
                color: 'success',
                trend: 6,
                trendValue: 6
              },
              {
                label: 'New (30d)',
                value: '28',
                icon: 'mdi-eye-plus',
                color: 'info',
                trend: 18,
                trendValue: 18
              },
              {
                label: 'Inactive',
                value: '7',
                icon: 'mdi-eye-off',
                color: 'error',
                trend: -3,
                trendValue: 3
              }
            ],
            chartData: {
              values: [12, 15, 18, 22, 25, 28, 30],
              labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
              gradient: ['#00c6ff', '#0072ff']
            }
          };
        case 'products':
          return {
            stats: [
              {
                label: 'Total',
                value: '3,567',
                icon: 'mdi-package-variant-closed',
                color: 'primary',
                trend: 15,
                trendValue: 15
              },
              {
                label: 'Active',
                value: '3,450',
                icon: 'mdi-package-variant-closed-check',
                color: 'success',
                trend: 12,
                trendValue: 12
              },
              {
                label: 'New (30d)',
                value: '520',
                icon: 'mdi-package-variant-closed-plus',
                color: 'info',
                trend: 25,
                trendValue: 25
              },
              {
                label: 'Inactive',
                value: '117',
                icon: 'mdi-package-variant-closed-remove',
                color: 'error',
                trend: -8,
                trendValue: 8
              }
            ],
            chartData: {
              values: [45, 52, 58, 65, 72, 78, 85],
              labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
              gradient: ['#00c6ff', '#0072ff']
            }
          };
        default:
          return {
            stats: [
              {
                label: 'Total',
                value: '0',
                icon: 'mdi-information',
                color: 'primary',
                trend: 0,
                trendValue: 0
              }
            ],
            chartData: {
              values: [0, 0, 0, 0, 0, 0, 0],
              labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
              gradient: ['#00c6ff', '#0072ff']
            }
          };
      }
    },

    // Get mock chart data
    getMockChartData(type, chartType) {
      if (type === 'users' && chartType === 'line') {
        return {
          chartData: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            datasets: [{
              label: 'Users',
              data: [850, 950, 1050, 1150, 1200, 1250, 1254],
              borderColor: '#2196f3',
              backgroundColor: 'rgba(33, 150, 243, 0.1)',
              tension: 0.4
            }]
          }
        };
      } else if (type === 'vendors' && chartType === 'pie') {
        return {
          chartData: {
            labels: ['North America', 'Europe', 'Asia', 'Australia', 'South America', 'Africa'],
            datasets: [{
              data: [35, 25, 20, 10, 7, 3],
              backgroundColor: [
                '#4caf50',
                '#2196f3',
                '#ff9800',
                '#f44336',
                '#9c27b0',
                '#3f51b5'
              ]
            }]
          }
        };
      } else if (type === 'showrooms' && chartType === 'bar') {
        return {
          chartData: {
            labels: ['Living Room', 'Bedroom', 'Kitchen', 'Bathroom', 'Office', 'Outdoor'],
            datasets: [{
              label: 'Showrooms',
              data: [45, 35, 25, 15, 12, 10],
              backgroundColor: [
                'rgba(33, 150, 243, 0.7)',
                'rgba(33, 150, 243, 0.7)',
                'rgba(33, 150, 243, 0.7)',
                'rgba(33, 150, 243, 0.7)',
                'rgba(33, 150, 243, 0.7)',
                'rgba(33, 150, 243, 0.7)'
              ]
            }]
          }
        };
      } else if (type === 'products' && chartType === 'doughnut') {
        return {
          chartData: {
            labels: ['Furniture', 'Lighting', 'Decor', 'Appliances', 'Textiles'],
            datasets: [{
              data: [40, 20, 15, 15, 10],
              backgroundColor: [
                '#4caf50',
                '#2196f3',
                '#ff9800',
                '#f44336',
                '#9c27b0'
              ]
            }]
          }
        };
      } else {
        // Default line chart
        return {
          chartData: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            datasets: [{
              label: 'Data',
              data: [65, 59, 80, 81, 56, 55, 40],
              borderColor: '#2196f3',
              backgroundColor: 'rgba(33, 150, 243, 0.1)',
              tension: 0.4
            }]
          }
        };
      }
    }
  }
};
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.stat-card {
  height: 100%;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.875rem;
}

.row-draggable {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.ghost-widget {
  opacity: 0.5;
  background: #c8ebfb;
}

.widget-list {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  min-height: 200px;
  padding: 8px;
}

.widget-drag-handle {
  cursor: move;
}

.drag-handle-icon {
  opacity: 0.3;
}

.widget-drag-handle:hover .drag-handle-icon {
  opacity: 1;
}
</style>
