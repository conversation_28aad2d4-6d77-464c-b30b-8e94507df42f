<template>
  <div class="filter-condition">
    <div class="filter-content">
      <!-- Field Selection -->
      <div class="filter-field">
        <select v-model="selectedField" @change="onFieldChange">
          <option v-for="field in fields" :key="field.id" :value="field.id">
            {{ field.name }}
          </option>
        </select>
      </div>
      
      <!-- Operator Selection -->
      <div class="filter-operator">
        <select v-model="selectedOperator" @change="onOperatorChange">
          <option v-for="op in availableOperators" :key="op.value" :value="op.value">
            {{ op.label }}
          </option>
        </select>
      </div>
      
      <!-- Value Input -->
      <div class="filter-value" v-if="showValueInput">
        <!-- Text Input -->
        <input 
          v-if="fieldType === 'string'" 
          type="text" 
          v-model="filterValue"
          @input="onValueChange"
          placeholder="Enter value"
        >
        
        <!-- Number Input -->
        <input 
          v-else-if="fieldType === 'number'" 
          type="number" 
          v-model.number="filterValue"
          @input="onValueChange"
          placeholder="Enter number"
        >
        
        <!-- Date Input -->
        <input 
          v-else-if="fieldType === 'date'" 
          type="date" 
          v-model="filterValue"
          @input="onValueChange"
        >
        
        <!-- DateTime Input -->
        <input 
          v-else-if="fieldType === 'datetime'" 
          type="datetime-local" 
          v-model="filterValue"
          @input="onValueChange"
        >
        
        <!-- Boolean Input -->
        <select 
          v-else-if="fieldType === 'boolean'" 
          v-model="filterValue"
          @change="onValueChange"
        >
          <option value="true">True</option>
          <option value="false">False</option>
        </select>
        
        <!-- Enum Input -->
        <select 
          v-else-if="fieldType === 'enum'" 
          v-model="filterValue"
          @change="onValueChange"
        >
          <option v-for="option in fieldOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </option>
        </select>
        
        <!-- Default Text Input -->
        <input 
          v-else 
          type="text" 
          v-model="filterValue"
          @input="onValueChange"
          placeholder="Enter value"
        >
      </div>
      
      <!-- Remove Button -->
      <div class="filter-remove">
        <button class="remove-button" @click="removeFilter" title="Remove Filter">
          <i class="material-icons">close</i>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FilterCondition',

  props: {
    filter: {
      type: Object,
      required: true
    },
    fields: {
      type: Array,
      required: true
    }
  },

  data() {
    return {
      selectedField: this.filter.field,
      selectedOperator: this.filter.operator,
      filterValue: this.filter.value
    };
  },

  computed: {
    fieldType() {
      const field = this.fields.find(f => f.id === this.selectedField);
      return field ? field.type : 'string';
    },
    
    fieldOptions() {
      const field = this.fields.find(f => f.id === this.selectedField);
      return field && field.options ? field.options : [];
    },
    
    availableOperators() {
      // Define operators based on field type
      const commonOperators = [
        { value: 'equals', label: 'Equals' },
        { value: 'not_equals', label: 'Does not equal' }
      ];
      
      const nullOperators = [
        { value: 'is_null', label: 'Is empty' },
        { value: 'is_not_null', label: 'Is not empty' }
      ];
      
      switch (this.fieldType) {
        case 'string':
          return [
            ...commonOperators,
            { value: 'contains', label: 'Contains' },
            { value: 'not_contains', label: 'Does not contain' },
            { value: 'starts_with', label: 'Starts with' },
            { value: 'ends_with', label: 'Ends with' },
            ...nullOperators
          ];
        case 'number':
          return [
            ...commonOperators,
            { value: 'greater_than', label: 'Greater than' },
            { value: 'less_than', label: 'Less than' },
            { value: 'greater_than_equal', label: 'Greater than or equal to' },
            { value: 'less_than_equal', label: 'Less than or equal to' },
            ...nullOperators
          ];
        case 'date':
        case 'datetime':
          return [
            ...commonOperators,
            { value: 'greater_than', label: 'After' },
            { value: 'less_than', label: 'Before' },
            { value: 'greater_than_equal', label: 'On or after' },
            { value: 'less_than_equal', label: 'On or before' },
            ...nullOperators
          ];
        case 'boolean':
          return commonOperators;
        default:
          return [...commonOperators, ...nullOperators];
      }
    },
    
    showValueInput() {
      // Don't show value input for null operators
      return !['is_null', 'is_not_null'].includes(this.selectedOperator);
    }
  },

  watch: {
    filter: {
      handler(newFilter) {
        this.selectedField = newFilter.field;
        this.selectedOperator = newFilter.operator;
        this.filterValue = newFilter.value;
      },
      deep: true
    }
  },

  methods: {
    onFieldChange() {
      // Reset operator and value when field changes
      this.selectedOperator = this.availableOperators[0].value;
      this.filterValue = '';
      
      this.emitUpdate();
    },
    
    onOperatorChange() {
      // Reset value when operator changes to/from null operators
      if (['is_null', 'is_not_null'].includes(this.selectedOperator)) {
        this.filterValue = '';
      }
      
      this.emitUpdate();
    },
    
    onValueChange() {
      this.emitUpdate();
    },
    
    emitUpdate() {
      this.$emit('update:filter', {
        field: this.selectedField,
        operator: this.selectedOperator,
        value: this.filterValue,
        type: 'condition'
      });
    },
    
    removeFilter() {
      this.$emit('remove');
    }
  }
};
</script>

<style scoped>
.filter-condition {
  width: 100%;
}

.filter-content {
  display: flex;
  align-items: center;
  padding: 10px;
  gap: 10px;
}

.filter-field {
  flex: 2;
}

.filter-operator {
  flex: 2;
}

.filter-value {
  flex: 3;
}

.filter-remove {
  flex: 0 0 auto;
}

select, input {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: var(--theme--border-radius);
  background-color: var(--theme--background);
  color: var(--theme--foreground);
}

.remove-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--theme--foreground-subdued);
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-button:hover {
  color: var(--theme--danger);
}
</style>
