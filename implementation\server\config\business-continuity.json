{"services": [{"id": "api-gateway", "name": "API Gateway", "description": "Main API gateway for the MVS-VR system", "priority": 5, "recoveryTimeObjective": 300, "recoveryPointObjective": 60, "businessImpact": {"degraded": 3, "outage": 5, "maintenance": 2}, "dependencies": []}, {"id": "authentication", "name": "Authentication Service", "description": "User authentication and authorization", "priority": 5, "recoveryTimeObjective": 300, "recoveryPointObjective": 60, "businessImpact": {"degraded": 3, "outage": 5, "maintenance": 2}, "dependencies": ["api-gateway"]}, {"id": "asset-management", "name": "Asset Management Service", "description": "Manages 3D assets and resources", "priority": 4, "recoveryTimeObjective": 600, "recoveryPointObjective": 300, "businessImpact": {"degraded": 3, "outage": 4, "maintenance": 2}, "dependencies": ["api-gateway", "authentication", "storage"]}, {"id": "scene-management", "name": "Scene Management Service", "description": "Manages VR scenes and configurations", "priority": 4, "recoveryTimeObjective": 600, "recoveryPointObjective": 300, "businessImpact": {"degraded": 3, "outage": 4, "maintenance": 2}, "dependencies": ["api-gateway", "authentication", "asset-management"]}, {"id": "storage", "name": "Storage Service", "description": "Manages file storage and retrieval", "priority": 4, "recoveryTimeObjective": 600, "recoveryPointObjective": 300, "businessImpact": {"degraded": 3, "outage": 4, "maintenance": 2}, "dependencies": ["api-gateway"]}, {"id": "analytics", "name": "Analytics Service", "description": "Collects and processes analytics data", "priority": 3, "recoveryTimeObjective": 1800, "recoveryPointObjective": 900, "businessImpact": {"degraded": 2, "outage": 3, "maintenance": 1}, "dependencies": ["api-gateway", "authentication"]}, {"id": "reporting", "name": "Reporting Service", "description": "Generates reports and visualizations", "priority": 3, "recoveryTimeObjective": 1800, "recoveryPointObjective": 900, "businessImpact": {"degraded": 2, "outage": 3, "maintenance": 1}, "dependencies": ["api-gateway", "authentication", "analytics"]}, {"id": "notification", "name": "Notification Service", "description": "Sends notifications to users", "priority": 3, "recoveryTimeObjective": 1800, "recoveryPointObjective": 900, "businessImpact": {"degraded": 2, "outage": 3, "maintenance": 1}, "dependencies": ["api-gateway", "authentication"]}, {"id": "llm-integration", "name": "LLM Integration Service", "description": "Integrates with language models for AI features", "priority": 2, "recoveryTimeObjective": 3600, "recoveryPointObjective": 1800, "businessImpact": {"degraded": 1, "outage": 2, "maintenance": 1}, "dependencies": ["api-gateway", "authentication"]}, {"id": "backup", "name": "Backup Service", "description": "Manages system backups", "priority": 2, "recoveryTimeObjective": 3600, "recoveryPointObjective": 1800, "businessImpact": {"degraded": 1, "outage": 2, "maintenance": 1}, "dependencies": ["storage"]}], "businessMetrics": [{"id": "active-users", "name": "Active Users", "description": "Number of active users in the system", "unit": "users", "threshold": {"warning": 1000, "critical": 500}}, {"id": "session-duration", "name": "Average Session Duration", "description": "Average duration of user sessions", "unit": "seconds", "threshold": {"warning": 300, "critical": 180}}, {"id": "asset-uploads", "name": "Asset Uploads", "description": "Number of assets uploaded per hour", "unit": "uploads/hour", "threshold": {"warning": 50, "critical": 20}}, {"id": "scene-views", "name": "Scene Views", "description": "Number of scene views per hour", "unit": "views/hour", "threshold": {"warning": 100, "critical": 50}}, {"id": "report-generation", "name": "Report Generation", "description": "Number of reports generated per hour", "unit": "reports/hour", "threshold": {"warning": 20, "critical": 10}}]}