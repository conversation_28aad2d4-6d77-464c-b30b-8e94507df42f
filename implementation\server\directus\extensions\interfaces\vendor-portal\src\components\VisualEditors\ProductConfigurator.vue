<template>
  <div class="product-configurator-wrapper">
    <div class="product-configurator">
      <div class="editor-header">
        <div class="editor-title-section">
          <h3 class="editor-title">Product Configurator</h3>
          <div class="editor-subtitle">Create customizable product options</div>
        </div>
        <div class="editor-actions">
          <button class="action-button" @click="saveConfiguration" :disabled="!hasChanges">
            <i class="material-icons">save</i>
            <span>Save</span>
          </button>
          <button class="action-button" @click="resetConfiguration" :disabled="!hasChanges">
            <i class="material-icons">refresh</i>
            <span>Reset</span>
          </button>
        </div>
      </div>

      <div class="editor-content">
        <div class="editor-sidebar">
          <div class="sidebar-section">
            <h4 class="sidebar-title">Product Selection</h4>
            <div class="search-box">
              <input
                type="text"
                placeholder="Search products..."
                v-model="searchQuery"
                @input="filterProducts"
              >
              <i class="material-icons">search</i>
            </div>
            <div class="product-list">
              <div
                v-for="product in filteredProducts"
                :key="product.id"
                class="product-item"
                :class="{ active: selectedProductId === product.id }"
                @click="selectProduct(product)"
              >
                <div class="product-thumbnail">
                  <img v-if="product.thumbnail" :src="product.thumbnail" :alt="product.name">
                  <div v-else class="product-placeholder">
                    <i class="material-icons">inventory_2</i>
                  </div>
                </div>
                <div class="product-info">
                  <div class="product-name">{{ product.name }}</div>
                  <div class="product-category">{{ getCategoryName(product.category) }}</div>
                </div>
              </div>
            </div>
          </div>

        <div v-if="selectedProduct" class="sidebar-section">
          <h4 class="sidebar-title">Configuration Options</h4>
          <div class="option-groups">
            <div v-for="(group, groupIndex) in configuration.optionGroups" :key="groupIndex" class="option-group">
              <div class="option-group-header">
                <div class="option-group-title">
                  <input
                    type="text"
                    v-model="group.name"
                    placeholder="Option Group Name"
                    class="option-group-name-input"
                  >
                </div>
                <div class="option-group-settings">
                  <div class="option-group-category">
                    <select v-model="group.category">
                      <option v-for="category in optionCategories" :key="category.id" :value="category.id">
                        {{ category.name }}
                      </option>
                    </select>
                  </div>
                  <div class="option-group-toggles">
                    <label class="toggle-label">
                      <input type="checkbox" v-model="group.required">
                      <span class="toggle-text">Required</span>
                    </label>
                    <label class="toggle-label">
                      <input type="checkbox" v-model="group.multiSelect">
                      <span class="toggle-text">Multi-select</span>
                    </label>
                  </div>
                </div>
                <div class="option-group-actions">
                  <button class="option-action-button" @click="removeOptionGroup(groupIndex)">
                    <i class="material-icons">delete</i>
                  </button>
                </div>
              </div>

              <div class="option-items">
                <div
                  v-for="(option, optionIndex) in group.options"
                  :key="optionIndex"
                  class="option-item"
                >
                  <div class="option-item-content">
                    <div class="option-item-main">
                      <input
                        type="text"
                        v-model="option.name"
                        placeholder="Option Name"
                        class="option-name-input"
                      >
                      <div class="option-price-input">
                        <span class="price-currency">$</span>
                        <input
                          type="number"
                          v-model="option.price"
                          placeholder="0.00"
                          step="0.01"
                          min="0"
                        >
                      </div>
                    </div>

                    <div class="option-item-details">
                      <div class="option-detail">
                        <label>SKU:</label>
                        <input type="text" v-model="option.sku" placeholder="SKU">
                      </div>

                      <div class="option-detail">
                        <label>Description:</label>
                        <input type="text" v-model="option.description" placeholder="Description">
                      </div>
                    </div>

                    <div class="option-dependencies">
                      <div class="dependencies-header">
                        <span>Dependencies</span>
                        <button class="add-dependency-button" @click="showAddDependencyModal(groupIndex, optionIndex)">
                          <i class="material-icons">add_link</i>
                        </button>
                      </div>

                      <div v-if="option.dependencies?.length > 0" class="dependency-list">
                        <div v-for="(dependency, depIndex) in option.dependencies" :key="depIndex" class="dependency-item">
                          <span>
                            {{ getDependencyLabel(dependency) }}
                          </span>
                          <button class="remove-dependency-button" @click="removeDependency(groupIndex, optionIndex, depIndex)">
                            <i class="material-icons">remove_circle</i>
                          </button>
                        </div>
                      </div>
                      <div v-else class="no-dependencies">
                        No dependencies
                      </div>
                    </div>

                    <div class="option-incompatibilities">
                      <div class="incompatibilities-header">
                        <span>Incompatibilities</span>
                        <button class="add-incompatibility-button" @click="showAddIncompatibilityModal(groupIndex, optionIndex)">
                          <i class="material-icons">block</i>
                        </button>
                      </div>

                      <div v-if="option.incompatibilities?.length > 0" class="incompatibility-list">
                        <div v-for="(incompatibility, incIndex) in option.incompatibilities" :key="incIndex" class="incompatibility-item">
                          <span>
                            {{ getIncompatibilityLabel(incompatibility) }}
                          </span>
                          <button class="remove-incompatibility-button" @click="removeIncompatibility(groupIndex, optionIndex, incIndex)">
                            <i class="material-icons">remove_circle</i>
                          </button>
                        </div>
                      </div>
                      <div v-else class="no-incompatibilities">
                        No incompatibilities
                      </div>
                    </div>
                  </div>
                  <div class="option-item-actions">
                    <button class="option-action-button" @click="removeOption(groupIndex, optionIndex)">
                      <i class="material-icons">delete</i>
                    </button>
                  </div>
                </div>

                <button class="add-option-button" @click="addOption(groupIndex)">
                  <i class="material-icons">add</i>
                  <span>Add Option</span>
                </button>
              </div>
            </div>

            <button class="add-group-button" @click="addOptionGroup">
              <i class="material-icons">add</i>
              <span>Add Option Group</span>
            </button>
          </div>
        </div>
        </div>
      </div>

      <div class="editor-main">
        <div v-if="!selectedProduct" class="no-product-selected">
          <i class="material-icons">category</i>
          <p>Select a product to configure</p>
        </div>

        <div v-else class="product-preview">
          <div class="preview-header">
            <h3 class="preview-title">{{ selectedProduct.name }}</h3>
            <div class="preview-subtitle">Configuration Preview</div>
          </div>

          <div class="preview-content">
            <div class="preview-image">
              <img
                v-if="selectedProduct.thumbnail"
                :src="selectedProduct.thumbnail"
                :alt="selectedProduct.name"
              >
              <div v-else class="preview-placeholder">
                <i class="material-icons">image</i>
                <p>No preview image</p>
              </div>
            </div>

            <div class="preview-options">
              <div v-if="!configuration.optionGroups || configuration.optionGroups.length === 0" class="no-options">
                <p>No configuration options defined</p>
                <p>Add option groups and options to make this product configurable</p>
              </div>

              <div v-else class="option-preview-list">
                <div
                  v-for="(group, groupIndex) in (configuration.optionGroups || [])"
                  :key="groupIndex"
                  class="option-preview-group"
                >
                  <div class="option-preview-group-name">{{ group.name || 'Unnamed Group' }}</div>

                  <div class="option-preview-items">
                    <div
                      v-for="(option, optionIndex) in group.options"
                      :key="optionIndex"
                      class="option-preview-item"
                      :class="{
                        selected: isOptionSelected(groupIndex, optionIndex),
                        disabled: isOptionDisabled(groupIndex, optionIndex)
                      }"
                      @click="!isOptionDisabled(groupIndex, optionIndex) && selectOption(groupIndex, optionIndex)"
                    >
                      <div class="option-preview-name">
                        {{ option.name || 'Unnamed Option' }}
                        <span v-if="option.sku" class="option-preview-sku">({{ option.sku }})</span>
                      </div>
                      <div class="option-preview-details">
                        <div v-if="option.price && option.price > 0" class="option-preview-price">+${{ option.price.toFixed(2) }}</div>
                        <div v-if="isOptionDisabled(groupIndex, optionIndex)" class="option-preview-disabled-reason">
                          <i class="material-icons">block</i>
                          <span>Incompatible with selection</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="price-summary">
                  <div class="base-price">
                    <span>Base Price:</span>
                    <span>${{ (selectedProduct && selectedProduct.price) ? selectedProduct.price.toFixed(2) : '0.00' }}</span>
                  </div>

                  <div v-if="totalOptionsPrice > 0" class="options-price">
                    <span>Options:</span>
                    <span>+${{ totalOptionsPrice.toFixed(2) }}</span>
                  </div>

                  <div class="total-price">
                    <span>Total Price:</span>
                    <span>${{ totalPrice.toFixed(2) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dependency Modal -->
    <div v-if="showDependencyModal" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Add Dependency</h3>
          <button class="modal-close-button" @click="hideAddDependencyModal">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="modal-content">
          <p class="modal-description">
            Select an option that is required for this option to work.
            When a user selects this option, the dependent option will be automatically selected.
          </p>

          <div class="modal-form">
            <div class="form-group">
              <label>Option Group:</label>
              <select v-model="currentTargetGroup">
                <option v-for="(group, groupIndex) in configuration.optionGroups" :key="groupIndex" :value="groupIndex">
                  {{ group.name || `Group ${groupIndex + 1}` }}
                </option>
              </select>
            </div>

            <div class="form-group" v-if="currentTargetGroup !== null">
              <label>Option:</label>
              <select v-model="currentTargetOption">
                <option v-for="(option, optionIndex) in configuration.optionGroups[currentTargetGroup].options"
                        :key="optionIndex"
                        :value="optionIndex">
                  {{ option.name || `Option ${optionIndex + 1}` }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="modal-cancel-button" @click="hideAddDependencyModal">Cancel</button>
          <button class="modal-confirm-button"
                  @click="confirmAddDependency"
                  :disabled="currentTargetGroup === null || currentTargetOption === null">
            Add Dependency
          </button>
        </div>
      </div>
    </div>

    <!-- Incompatibility Modal -->
    <div v-if="showIncompatibilityModal" class="modal-overlay">
      <div class="modal-container">
        <div class="modal-header">
          <h3>Add Incompatibility</h3>
          <button class="modal-close-button" @click="hideAddIncompatibilityModal">
            <i class="material-icons">close</i>
          </button>
        </div>

        <div class="modal-content">
          <p class="modal-description">
            Select an option that cannot be used together with this option.
            When a user selects this option, the incompatible option will be disabled.
          </p>

          <div class="modal-form">
            <div class="form-group">
              <label>Option Group:</label>
              <select v-model="currentTargetGroup">
                <option v-for="(group, groupIndex) in configuration.optionGroups" :key="groupIndex" :value="groupIndex">
                  {{ group.name || `Group ${groupIndex + 1}` }}
                </option>
              </select>
            </div>

            <div class="form-group" v-if="currentTargetGroup !== null">
              <label>Option:</label>
              <select v-model="currentTargetOption">
                <option v-for="(option, optionIndex) in configuration.optionGroups[currentTargetGroup].options"
                        :key="optionIndex"
                        :value="optionIndex">
                  {{ option.name || `Option ${optionIndex + 1}` }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button class="modal-cancel-button" @click="hideAddIncompatibilityModal">Cancel</button>
          <button class="modal-confirm-button"
                  @click="confirmAddIncompatibility"
                  :disabled="currentTargetGroup === null || currentTargetOption === null">
            Add Incompatibility
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProductConfigurator',

  props: {
    vendorId: {
      type: String,
      required: true
    },
    productId: {
      type: String,
      default: null
    }
  },

  data() {
    return {
      products: [],
      categories: [],
      filteredProducts: [],
      searchQuery: '',
      selectedProductId: null,
      selectedProduct: null,
      configuration: {
        productId: null,
        optionGroups: []
      },
      originalConfiguration: null,
      selectedOptions: {},
      // New data properties for enhanced features
      optionCategories: [
        { id: 'appearance', name: 'Appearance' },
        { id: 'functionality', name: 'Functionality' },
        { id: 'accessories', name: 'Accessories' },
        { id: 'customization', name: 'Customization' }
      ],
      dependencies: [],
      compatibilityRules: [],
      configRules: [],
      // UI state for modals
      showDependencyModal: false,
      showIncompatibilityModal: false,
      currentSourceGroup: null,
      currentSourceOption: null,
      currentTargetGroup: null,
      currentTargetOption: null
    };
  },

  computed: {
    hasChanges() {
      return this.originalConfiguration &&
             JSON.stringify(this.configuration) !== JSON.stringify(this.originalConfiguration);
    },

    totalOptionsPrice() {
      let total = 0;

      Object.entries(this.selectedOptions).forEach(([groupIndex, optionIndexOrArray]) => {
        const group = this.configuration.optionGroups[groupIndex];
        if (!group) return;

        if (Array.isArray(optionIndexOrArray)) {
          // Handle multi-select option groups
          optionIndexOrArray.forEach(optionIndex => {
            if (group.options[optionIndex]) {
              total += parseFloat(group.options[optionIndex].price || 0);
            }
          });
        } else {
          // Handle single-select option groups
          const optionIndex = optionIndexOrArray;
          if (group.options[optionIndex]) {
            total += parseFloat(group.options[optionIndex].price || 0);
          }
        }
      });

      return total;
    },

    totalPrice() {
      const basePrice = parseFloat(this.selectedProduct?.price || 0);
      return basePrice + this.totalOptionsPrice;
    }
  },

  created() {
    this.loadData();
  },

  methods: {
    async loadData() {
      try {
        // Load products
        const productsResponse = await this.$api.get(`/items/products?filter[vendor_id][_eq]=${this.vendorId}`);
        this.products = productsResponse.data.data || [];
        this.filteredProducts = [...this.products];

        // Load categories
        const categoriesResponse = await this.$api.get('/items/categories');
        this.categories = categoriesResponse.data.data || [];

        // If productId is provided, select that product
        if (this.productId) {
          this.selectedProductId = this.productId;
          this.selectProductById(this.productId);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      }
    },

    filterProducts() {
      if (!this.searchQuery) {
        this.filteredProducts = [...this.products];
        return;
      }

      const query = this.searchQuery.toLowerCase();
      this.filteredProducts = this.products.filter(product =>
        product.name.toLowerCase().includes(query) ||
        this.getCategoryName(product.category).toLowerCase().includes(query)
      );
    },

    getCategoryName(categoryId) {
      const category = this.categories.find(c => c.id === categoryId);
      return category ? category.name : 'Uncategorized';
    },

    selectProductById(productId) {
      const product = this.products.find(p => p.id === productId);
      if (product) {
        this.selectProduct(product);
      }
    },

    async selectProduct(product) {
      this.selectedProductId = product.id;
      this.selectedProduct = product;

      // Reset selected options
      this.selectedOptions = {};

      try {
        // Load product configuration if it exists
        const configResponse = await this.$api.get(`/items/product_configurations?filter[product_id][_eq]=${product.id}`);
        const configs = configResponse.data.data || [];

        if (configs.length > 0) {
          this.configuration = configs[0];
        } else {
          // Create new configuration
          this.configuration = {
            productId: product.id,
            optionGroups: []
          };
        }

        this.originalConfiguration = JSON.parse(JSON.stringify(this.configuration));
      } catch (error) {
        console.error('Error loading product configuration:', error);

        // Reset configuration
        this.configuration = {
          productId: product.id,
          optionGroups: []
        };

        this.originalConfiguration = JSON.parse(JSON.stringify(this.configuration));
      }
    },

    addOptionGroup() {
      this.configuration.optionGroups.push({
        name: '',
        category: this.optionCategories[0].id,
        options: [],
        required: false,
        multiSelect: false
      });
    },

    removeOptionGroup(groupIndex) {
      this.configuration.optionGroups.splice(groupIndex, 1);

      // Update selected options
      delete this.selectedOptions[groupIndex];

      // Reindex selected options
      const newSelectedOptions = {};
      Object.entries(this.selectedOptions).forEach(([group, option]) => {
        const groupNum = parseInt(group);
        if (groupNum > groupIndex) {
          newSelectedOptions[groupNum - 1] = option;
        } else {
          newSelectedOptions[group] = option;
        }
      });

      this.selectedOptions = newSelectedOptions;
    },

    addOption(groupIndex) {
      this.configuration.optionGroups[groupIndex].options.push({
        name: '',
        price: 0,
        sku: '',
        description: '',
        image: null,
        dependencies: [],
        incompatibilities: []
      });
    },

    removeOption(groupIndex, optionIndex) {
      this.configuration.optionGroups[groupIndex].options.splice(optionIndex, 1);

      // Update selected option for this group
      if (this.selectedOptions[groupIndex] === optionIndex) {
        delete this.selectedOptions[groupIndex];
      } else if (this.selectedOptions[groupIndex] > optionIndex) {
        this.selectedOptions[groupIndex]--;
      }
    },

    selectOption(groupIndex, optionIndex) {
      // Check if this is a deselection
      if (this.selectedOptions[groupIndex] === optionIndex) {
        // Check if this option is required by any other selected option
        for (const [selectedGroupIndex, selectedOptionIndex] of Object.entries(this.selectedOptions)) {
          // Skip the current option
          if (parseInt(selectedGroupIndex) === groupIndex) continue;

          const selectedOption = this.configuration.optionGroups[selectedGroupIndex].options[selectedOptionIndex];

          // Check if the selected option depends on the option we're trying to deselect
          if (selectedOption.dependencies && selectedOption.dependencies.some(dep =>
            dep.groupIndex === groupIndex && dep.optionIndex === optionIndex)) {
            // Can't deselect because another option depends on it
            return;
          }
        }

        // If we get here, it's safe to deselect
        delete this.selectedOptions[groupIndex];
      } else {
        // Check if selecting this option would violate any incompatibilities
        if (!this.canSelectOption(groupIndex, optionIndex)) {
          return;
        }

        // If the option group doesn't allow multiple selections, deselect any existing selection
        if (!this.configuration.optionGroups[groupIndex].multiSelect) {
          this.selectedOptions[groupIndex] = optionIndex;
        } else {
          // For multi-select groups, we need to handle arrays of selected options
          if (!Array.isArray(this.selectedOptions[groupIndex])) {
            this.selectedOptions[groupIndex] = [optionIndex];
          } else if (!this.selectedOptions[groupIndex].includes(optionIndex)) {
            this.selectedOptions[groupIndex].push(optionIndex);
          }
        }

        // Auto-select dependencies if needed
        const option = this.configuration.optionGroups[groupIndex].options[optionIndex];
        if (option.dependencies && Array.isArray(option.dependencies)) {
          for (const dependency of option.dependencies) {
            // Only auto-select if not already selected
            if (this.selectedOptions[dependency.groupIndex] !== dependency.optionIndex) {
              this.selectOption(dependency.groupIndex, dependency.optionIndex);
            }
          }
        }
      }
    },

    isOptionSelected(groupIndex, optionIndex) {
      if (!this.selectedOptions[groupIndex]) return false;

      if (Array.isArray(this.selectedOptions[groupIndex])) {
        return this.selectedOptions[groupIndex].includes(optionIndex);
      } else {
        return this.selectedOptions[groupIndex] === optionIndex;
      }
    },

    // Check if an option is disabled based on dependencies and incompatibilities
    isOptionDisabled(groupIndex, optionIndex) {
      return !this.canSelectOption(groupIndex, optionIndex);
    },

    async saveConfiguration() {
      try {
        let response;

        if (this.configuration.id) {
          // Update existing configuration
          response = await this.$api.patch(
            `/items/product_configurations/${this.configuration.id}`,
            this.configuration
          );
        } else {
          // Create new configuration
          response = await this.$api.post('/items/product_configurations', {
            product_id: this.selectedProduct.id,
            option_groups: this.configuration.optionGroups
          });
        }

        if (response.data.data) {
          this.configuration = response.data.data;
          this.originalConfiguration = JSON.parse(JSON.stringify(this.configuration));
          this.$emit('update', this.configuration);
        }
      } catch (error) {
        console.error('Error saving product configuration:', error);
      }
    },

    resetConfiguration() {
      if (this.originalConfiguration) {
        this.configuration = JSON.parse(JSON.stringify(this.originalConfiguration));
      }
    },

    // New methods for dependencies and compatibility rules
    addDependency(sourceGroupIndex, sourceOptionIndex, targetGroupIndex, targetOptionIndex) {
      const sourceOption = this.configuration.optionGroups[sourceGroupIndex].options[sourceOptionIndex];

      // Create dependency object
      const dependency = {
        groupIndex: targetGroupIndex,
        optionIndex: targetOptionIndex
      };

      // Ensure dependencies array exists
      if (!sourceOption.dependencies) {
        sourceOption.dependencies = [];
      }

      // Add dependency if it doesn't already exist
      if (!sourceOption.dependencies.some(dep =>
        dep.groupIndex === dependency.groupIndex && dep.optionIndex === dependency.optionIndex)) {
        sourceOption.dependencies.push(dependency);
      }
    },

    removeDependency(sourceGroupIndex, sourceOptionIndex, dependencyIndex) {
      const sourceOption = this.configuration.optionGroups[sourceGroupIndex].options[sourceOptionIndex];
      if (sourceOption.dependencies && Array.isArray(sourceOption.dependencies)) {
        sourceOption.dependencies.splice(dependencyIndex, 1);
      }
    },

    addIncompatibility(sourceGroupIndex, sourceOptionIndex, targetGroupIndex, targetOptionIndex) {
      const sourceOption = this.configuration.optionGroups[sourceGroupIndex].options[sourceOptionIndex];

      // Create incompatibility object
      const incompatibility = {
        groupIndex: targetGroupIndex,
        optionIndex: targetOptionIndex
      };

      // Ensure incompatibilities array exists
      if (!sourceOption.incompatibilities) {
        sourceOption.incompatibilities = [];
      }

      // Add incompatibility if it doesn't already exist
      if (!sourceOption.incompatibilities.some(inc =>
        inc.groupIndex === incompatibility.groupIndex && inc.optionIndex === incompatibility.optionIndex)) {
        sourceOption.incompatibilities.push(incompatibility);
      }
    },

    removeIncompatibility(sourceGroupIndex, sourceOptionIndex, incompatibilityIndex) {
      const sourceOption = this.configuration.optionGroups[sourceGroupIndex].options[sourceOptionIndex];
      if (sourceOption.incompatibilities && Array.isArray(sourceOption.incompatibilities)) {
        sourceOption.incompatibilities.splice(incompatibilityIndex, 1);
      }
    },

    // Check if an option can be selected based on dependencies and incompatibilities
    canSelectOption(groupIndex, optionIndex) {
      // Check if selecting this option would violate any incompatibilities
      if (!this.configuration.optionGroups) return true;

      for (const [selectedGroupIndex, selectedOptionIndex] of Object.entries(this.selectedOptions)) {
        const group = this.configuration.optionGroups[selectedGroupIndex];
        if (!group || !group.options) continue;

        const selectedOption = group.options[selectedOptionIndex];

        // Check if the selected option has an incompatibility with the option we want to select
        if (selectedOption.incompatibilities && selectedOption.incompatibilities.some(inc =>
          inc.groupIndex === groupIndex && inc.optionIndex === optionIndex)) {
          return false;
        }

        // Check if the option we want to select has an incompatibility with the selected option
        const targetGroup = this.configuration.optionGroups[groupIndex];
        if (!targetGroup || !targetGroup.options) continue;

        const targetOption = targetGroup.options[optionIndex];
        if (targetOption.incompatibilities && targetOption.incompatibilities.some(inc =>
          inc.groupIndex === parseInt(selectedGroupIndex) && inc.optionIndex === selectedOptionIndex)) {
          return false;
        }
      }

      return true;
    },

    // Check if all dependencies for the current selection are satisfied
    areDependenciesSatisfied() {
      for (const [groupIndex, optionIndex] of Object.entries(this.selectedOptions)) {
        const option = this.configuration.optionGroups[groupIndex].options[optionIndex];

        // Check if all dependencies of this option are satisfied
        if (option.dependencies && Array.isArray(option.dependencies)) {
          for (const dependency of option.dependencies) {
            if (this.selectedOptions[dependency.groupIndex] !== dependency.optionIndex) {
              return false;
            }
          }
        }
      }

      return true;
    },

    // Add a configuration rule
    addConfigRule() {
      this.configRules.push({
        name: '',
        condition: {
          type: 'AND',
          rules: []
        },
        action: {
          type: 'SHOW',
          target: {
            groupIndex: null,
            optionIndex: null
          }
        }
      });
    },

    // Remove a configuration rule
    removeConfigRule(ruleIndex) {
      this.configRules.splice(ruleIndex, 1);
    },

    // UI methods for dependencies and incompatibilities
    showAddDependencyModal(groupIndex, optionIndex) {
      this.currentSourceGroup = groupIndex;
      this.currentSourceOption = optionIndex;
      this.currentTargetGroup = null;
      this.currentTargetOption = null;
      this.showDependencyModal = true;
    },

    hideAddDependencyModal() {
      this.showDependencyModal = false;
    },

    confirmAddDependency() {
      if (this.currentSourceGroup !== null &&
          this.currentSourceOption !== null &&
          this.currentTargetGroup !== null &&
          this.currentTargetOption !== null) {
        this.addDependency(
          this.currentSourceGroup,
          this.currentSourceOption,
          this.currentTargetGroup,
          this.currentTargetOption
        );
      }
      this.hideAddDependencyModal();
    },

    showAddIncompatibilityModal(groupIndex, optionIndex) {
      this.currentSourceGroup = groupIndex;
      this.currentSourceOption = optionIndex;
      this.currentTargetGroup = null;
      this.currentTargetOption = null;
      this.showIncompatibilityModal = true;
    },

    hideAddIncompatibilityModal() {
      this.showIncompatibilityModal = false;
    },

    confirmAddIncompatibility() {
      if (this.currentSourceGroup !== null &&
          this.currentSourceOption !== null &&
          this.currentTargetGroup !== null &&
          this.currentTargetOption !== null) {
        this.addIncompatibility(
          this.currentSourceGroup,
          this.currentSourceOption,
          this.currentTargetGroup,
          this.currentTargetOption
        );
      }
      this.hideAddIncompatibilityModal();
    },

    // Get readable labels for dependencies and incompatibilities
    getDependencyLabel(dependency) {
      const group = this.configuration.optionGroups[dependency.groupIndex];
      if (!group) return 'Unknown dependency';

      const option = group.options[dependency.optionIndex];
      if (!option) return 'Unknown dependency';

      return `${group.name || 'Unnamed Group'}: ${option.name || 'Unnamed Option'}`;
    },

    getIncompatibilityLabel(incompatibility) {
      const group = this.configuration.optionGroups[incompatibility.groupIndex];
      if (!group) return 'Unknown incompatibility';

      const option = group.options[incompatibility.optionIndex];
      if (!option) return 'Unknown incompatibility';

      return `${group.name || 'Unnamed Group'}: ${option.name || 'Unnamed Option'}`;
    }
  }
};
</script>

<style scoped>
.product-configurator {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.editor-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.editor-subtitle {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  cursor: pointer;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.editor-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.editor-sidebar {
  width: 280px;
  border-right: 1px solid var(--theme--border-color);
  overflow-y: auto;
  background-color: var(--theme--background-subdued);
}

.sidebar-section {
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.sidebar-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.search-box {
  position: relative;
  margin-bottom: 16px;
}

.search-box input {
  width: 100%;
  padding: 8px 32px 8px 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.search-box i {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
}

.product-list {
  max-height: 300px;
  overflow-y: auto;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 8px;
  background-color: var(--theme--background);
}

.product-item:hover {
  background-color: var(--theme--background-accent);
}

.product-item.active {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.product-item.active .product-category {
  color: var(--theme--primary-background);
  opacity: 0.8;
}

.product-thumbnail {
  width: 40px;
  height: 40px;
  margin-right: 8px;
  border-radius: 4px;
  overflow: hidden;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
}

.product-category {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
}

.option-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-group {
  background-color: var(--theme--background);
  border-radius: 4px;
  overflow: hidden;
}

.option-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--theme--background-accent);
}

.option-group-title {
  flex: 1;
}

.option-group-name-input {
  width: 100%;
  padding: 4px 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.option-group-actions {
  display: flex;
  gap: 4px;
}

.option-action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
}

.option-action-button:hover {
  background-color: var(--theme--danger);
  color: var(--theme--danger-background);
}

.option-items {
  padding: 8px 12px;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
}

.option-item-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8px;
}

.option-item-main {
  display: flex;
  gap: 8px;
}

.option-name-input {
  flex: 1;
  padding: 4px 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.option-price-input {
  position: relative;
  width: 80px;
}

.price-currency {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme--foreground-subdued);
}

.option-price-input input {
  width: 100%;
  padding: 4px 8px 4px 16px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.option-item-details {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.option-detail {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.option-detail label {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-bottom: 4px;
}

.option-detail input {
  padding: 4px 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.option-dependencies,
.option-incompatibilities {
  margin-top: 8px;
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
}

.dependencies-header,
.incompatibilities-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 600;
}

.add-dependency-button,
.add-incompatibility-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
}

.add-dependency-button:hover,
.add-incompatibility-button:hover {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.dependency-list,
.incompatibility-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.dependency-item,
.incompatibility-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: var(--theme--background);
  font-size: 12px;
}

.remove-dependency-button,
.remove-incompatibility-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
}

.remove-dependency-button:hover,
.remove-incompatibility-button:hover {
  color: var(--theme--danger);
}

.no-dependencies,
.no-incompatibilities {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  text-align: center;
  padding: 4px;
}

.option-item-actions {
  display: flex;
  gap: 4px;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  width: 500px;
  max-width: 90%;
  background-color: var(--theme--background);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--theme--border-color);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground);
  cursor: pointer;
}

.modal-close-button:hover {
  background-color: var(--theme--background-accent);
}

.modal-content {
  padding: 16px;
}

.modal-description {
  margin-bottom: 16px;
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.modal-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
}

.form-group select {
  padding: 8px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px;
  border-top: 1px solid var(--theme--border-color);
}

.modal-cancel-button {
  padding: 8px 16px;
  border: 1px solid var(--theme--border-color);
  border-radius: 4px;
  background-color: var(--theme--background);
  color: var(--theme--foreground);
  cursor: pointer;
}

.modal-confirm-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
  cursor: pointer;
}

.modal-confirm-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-option-button,
.add-group-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border: 1px dashed var(--theme--border-color);
  border-radius: 4px;
  background-color: transparent;
  color: var(--theme--foreground-subdued);
  cursor: pointer;
  width: 100%;
  justify-content: center;
}

.add-option-button:hover,
.add-group-button:hover {
  background-color: var(--theme--background-accent);
  color: var(--theme--foreground);
}

.editor-main {
  flex: 1;
  overflow: auto;
  background-color: var(--theme--background);
}

.no-product-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--theme--foreground-subdued);
}

.no-product-selected i {
  font-size: 48px;
  margin-bottom: 16px;
}

.product-preview {
  padding: 24px;
}

.preview-header {
  margin-bottom: 24px;
}

.preview-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.preview-subtitle {
  font-size: 14px;
  color: var(--theme--foreground-subdued);
}

.preview-content {
  display: flex;
  gap: 24px;
}

.preview-image {
  width: 300px;
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
  background-color: var(--theme--background-subdued);
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--theme--foreground-subdued);
}

.preview-placeholder i {
  font-size: 48px;
  margin-bottom: 16px;
}

.preview-options {
  flex: 1;
}

.no-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--theme--foreground-subdued);
  text-align: center;
}

.option-preview-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.option-preview-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-preview-group-name {
  font-size: 16px;
  font-weight: 600;
}

.option-preview-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.option-preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background-color: var(--theme--background-subdued);
  cursor: pointer;
  min-width: 120px;
}

.option-preview-item:hover {
  background-color: var(--theme--background-accent);
}

.option-preview-item.selected {
  background-color: var(--theme--primary);
  color: var(--theme--primary-background);
}

.option-preview-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--theme--background-subdued);
  border: 1px dashed var(--theme--border-color);
}

.option-preview-item.disabled:hover {
  background-color: var(--theme--background-subdued);
}

.option-preview-name {
  font-size: 14px;
}

.option-preview-sku {
  font-size: 12px;
  color: var(--theme--foreground-subdued);
  margin-left: 4px;
}

.option-preview-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.option-preview-price {
  font-size: 14px;
  font-weight: 500;
}

.option-preview-disabled-reason {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  color: var(--theme--danger);
}

.option-preview-disabled-reason i {
  font-size: 14px;
}

.price-summary {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--theme--border-color);
}

.base-price,
.options-price,
.total-price {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.total-price {
  font-size: 18px;
  font-weight: 600;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--theme--border-color);
}
</style>
