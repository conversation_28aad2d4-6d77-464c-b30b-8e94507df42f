# API Specification for Server-Driven MVS-VR Platform

## 1. Overview

This document specifies the API endpoints, request/response formats, and authentication mechanisms for the server-driven MVS-VR platform. The API is designed to support the delivery of assets, configurations, and functionality to the UE Plugin.

## 2. Base URL

```
https://api.mvs-vr.com/v1
```

## 3. Authentication

### 3.1 Authentication Methods

The API supports the following authentication methods:

1. **JWT Authentication**: For authenticated user sessions
2. **API Key Authentication**: For plugin and service-to-service communication
3. **OAuth 2.0**: For third-party integrations

### 3.2 JWT Authentication

JWT tokens are issued upon successful login and must be included in the `Authorization` header:

```
Authorization: Bearer {token}
```

JWT tokens have a 15-minute expiration and can be refreshed using the refresh token endpoint.

### 3.3 API Key Authentication

API keys must be included in the `X-API-Key` header:

```
X-API-Key: {api_key}
```

API keys are vendor-specific and have associated rate limits and permissions.

## 4. Common Headers

| Header | Description | Required |
|--------|-------------|----------|
| `Content-Type` | Media type of the request body | Yes, for requests with body |
| `Accept` | Media types acceptable for the response | No |
| `Authorization` | Authentication credentials | Yes, for authenticated endpoints |
| `X-API-Key` | API key for service authentication | Yes, for service endpoints |
| `X-Request-ID` | Unique identifier for the request | No |
| `X-Client-Version` | Client version for compatibility | Yes |

## 5. Common Response Formats

### 5.1 Success Response

```json
{
  "status": "success",
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

### 5.2 Error Response

```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error details
    }
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0",
    "request_id": "req_123456789"
  }
}
```

## 6. API Endpoints

### 6.1 Authentication Endpoints

#### 6.1.1 Login

```
POST /auth/login
```

Request:

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

Response:

```json
{
  "status": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 900,
    "token_type": "Bearer"
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

#### 6.1.2 Refresh Token

```
POST /auth/refresh
```

Request:

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

Response:

```json
{
  "status": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 900,
    "token_type": "Bearer"
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

### 6.2 Bootstrap Endpoints

#### 6.2.1 Get Bootstrap Configuration

```
GET /bootstrap/{vendor_id}/{environment_key}
```

Parameters:

- `vendor_id`: Vendor identifier
- `environment_key`: Environment identifier

Query Parameters:

- `client_version`: Client version for compatibility
- `cached_hashes`: JSON array of cached configuration hashes

Response:

```json
{
  "status": "success",
  "data": {
    "bootstrap_id": "bs_123456789",
    "vendor_id": "vendor_123",
    "environment_key": "env_456",
    "configurations": [
      {
        "type": "scene_flow",
        "url": "https://assets.mvs-vr.com/configs/scene_flow_123.json",
        "hash": "abc123def456",
        "version": "1.0.0",
        "required": true
      },
      {
        "type": "store_config",
        "url": "https://assets.mvs-vr.com/configs/store_config_456.json",
        "hash": "def456ghi789",
        "version": "1.0.0",
        "required": true
      },
      {
        "type": "bundle_manifest",
        "url": "https://assets.mvs-vr.com/configs/bundle_manifest_789.json",
        "hash": "ghi789jkl012",
        "version": "1.0.0",
        "required": true
      },
      {
        "type": "material_library",
        "url": "https://assets.mvs-vr.com/configs/material_library_012.json",
        "hash": "jkl012mno345",
        "version": "1.0.0",
        "required": false
      },
      {
        "type": "assistant_prompt",
        "url": "https://assets.mvs-vr.com/configs/assistant_prompt_345.json",
        "hash": "mno345pqr678",
        "version": "1.0.0",
        "required": false
      }
    ],
    "settings": {
      "offline_mode_enabled": true,
      "telemetry_enabled": true,
      "debug_mode": false
    }
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

### 6.3 Asset Endpoints

#### 6.3.1 Get Asset Bundle

```
GET /assets/bundles/{bundle_id}
```

Parameters:

- `bundle_id`: Bundle identifier

Query Parameters:

- `include_assets`: Boolean to include asset data (default: false)

Response:

```json
{
  "status": "success",
  "data": {
    "bundle_id": "bundle_123",
    "hash": "abc123def456",
    "version": "1.0.0",
    "created_at": "2025-05-15T10:00:00Z",
    "assets": [
      {
        "id": "asset_123",
        "type": "model",
        "name": "Modern Chair",
        "url": "https://assets.mvs-vr.com/models/chair_modern_01.glb",
        "hash": "def456ghi789",
        "size": 1024000,
        "metadata": {
          "category": "furniture",
          "tags": ["chair", "modern", "living room"]
        }
      },
      {
        "id": "asset_456",
        "type": "layout",
        "name": "Floor A Layout",
        "url": "https://assets.mvs-vr.com/layouts/layout_floorA.json",
        "hash": "ghi789jkl012",
        "size": 5120,
        "metadata": {
          "category": "layout",
          "tags": ["floor", "layout"]
        }
      }
    ]
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

#### 6.3.2 Get Asset

```
GET /assets/{asset_id}
```

Parameters:

- `asset_id`: Asset identifier

Response:
Binary data with appropriate Content-Type header

### 6.4 Scene Endpoints

#### 6.4.1 Get Scene Flow

```
GET /scenes/{scene_id}/flow
```

Parameters:

- `scene_id`: Scene identifier

Response:

```json
{
  "status": "success",
  "data": {
    "scene_id": "scene_123",
    "version": "1.0.0",
    "name": "Dubai Mall",
    "description": "Dubai Mall virtual showroom",
    "locations": [
      {
        "id": "location_123",
        "name": "Dubai Mall",
        "exhibitions": [
          {
            "id": "exhibition_123",
            "name": "Furniture Expo",
            "spaces": [
              {
                "id": "space_123",
                "name": "Modern Living",
                "assets": ["asset_123", "asset_456"],
                "lighting": "bright_daylight",
                "audio": "ambient_mall"
              }
            ]
          }
        ]
      }
    ]
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

#### 6.4.2 Validate Scene

```
GET /scenes/validate/{id}
```

Parameters:

- `id`: Scene identifier

Response:

```json
{
  "status": "success",
  "data": {
    "valid": true,
    "errors": [],
    "warnings": []
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

#### 6.4.3 Analyze Scene Performance

```
GET /scenes/validate/{id}/performance
```

Parameters:

- `id`: Scene identifier

Response:

```json
{
  "status": "success",
  "data": {
    "impact": "low",
    "metrics": {
      "assetCount": 10,
      "totalAssetSize": 5000000,
      "complexityScore": 50,
      "estimatedLoadTime": 2.5,
      "estimatedMemoryUsage": 7500000
    },
    "recommendations": [
      {
        "code": "LARGE_TEXTURES",
        "message": "Scene contains 2 large textures (>5MB), consider reducing texture size",
        "priority": "high"
      }
    ]
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

#### 6.4.4 Check Scene Compatibility

```
GET /scenes/validate/{id}/compatibility
```

Parameters:

- `id`: Scene identifier
- `target_environment` (query, optional): Target environment (e.g., 'quest2', 'quest3', 'pico4', 'steamvr')

Response:

```json
{
  "status": "success",
  "data": {
    "compatible": true,
    "targetEnvironment": "quest2",
    "issues": [
      {
        "code": "TEXTURE_TOO_LARGE",
        "message": "Texture asset_123 resolution (4096x4096) exceeds maximum for quest2",
        "severity": "warning",
        "path": "assets.asset_123"
      }
    ]
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

#### 6.4.5 Validate Scene Data

```
POST /scenes/validate/data
```

Request Body:

```json
{
  "data": {
    "objects": [],
    "settings": {}
  }
}
```

Response:

```json
{
  "status": "success",
  "data": {
    "valid": true,
    "errors": []
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

#### 6.4.6 Validate Scene Flow

```
POST /scenes/validate/flow
```

Request Body:

```json
{
  "flow": {
    "startup": {
      "space": "space-1",
      "next": "node-2"
    },
    "node-2": {
      "space": "space-2",
      "options": {
        "option1": "node-3",
        "option2": "node-4"
      }
    },
    "node-3": {
      "space": "space-3"
    },
    "node-4": {
      "space": "space-4"
    }
  }
}
```

Response:

```json
{
  "status": "success",
  "data": {
    "valid": true,
    "errors": [],
    "warnings": []
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

### 6.5 Blueprint Endpoints

#### 6.5.1 Get Blueprint

```
GET /blueprints/{blueprint_id}
```

Parameters:

- `blueprint_id`: Blueprint identifier

Response:

```json
{
  "status": "success",
  "data": {
    "blueprint_id": "blueprint_123",
    "version": "1.0.0",
    "name": "Drawer Behavior",
    "description": "Behavior for drawer opening and closing",
    "tags": ["drawer", "interactive"],
    "script": {
      "type": "behavior",
      "triggers": [
        {
          "event": "interaction",
          "action": "toggle",
          "parameters": {
            "animation": "drawer_open_close",
            "sound": "drawer_slide"
          }
        }
      ]
    }
  },
  "meta": {
    "timestamp": "2025-05-18T12:34:56Z",
    "version": "1.0.0"
  }
}
```

## 7. Versioning

The API is versioned using the URL path (e.g., `/v1/endpoint`). Breaking changes will result in a new API version.

## 8. Rate Limiting

Rate limits are applied based on the authentication method and endpoint:

- JWT Authentication: 100 requests per minute
- API Key Authentication: Configurable per vendor
- Unauthenticated: 10 requests per minute

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 99
X-RateLimit-Reset: 1589547834
```

## 9. Pagination

List endpoints support pagination using the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)

Pagination metadata is included in the response:

```json
"meta": {
  "pagination": {
    "total": 100,
    "pages": 5,
    "page": 1,
    "limit": 20
  }
}
```
