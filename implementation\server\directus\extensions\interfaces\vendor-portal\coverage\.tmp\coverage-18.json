{"result": [{"scriptId": "1145", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/vitest.setup.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10584, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 289, "endOffset": 460, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1063, "endOffset": 1320, "count": 1}], "isBlockCoverage": true}, {"functionName": "getItem", "ranges": [{"startOffset": 1114, "endOffset": 1139, "count": 0}], "isBlockCoverage": false}, {"functionName": "setItem", "ranges": [{"startOffset": 1154, "endOffset": 1211, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeItem", "ranges": [{"startOffset": 1229, "endOffset": 1268, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 1281, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "MaterialIconsMock", "ranges": [{"startOffset": 1975, "endOffset": 2007, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1161", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/steps/tests/CompanyProfileStep.test.js", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 35225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 35225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 349, "endOffset": 554, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 407, "endOffset": 464, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 978, "endOffset": 7423, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1464, "endOffset": 1606, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1622, "endOffset": 1656, "count": 12}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1690, "endOffset": 1742, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1793, "endOffset": 2168, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2225, "endOffset": 2296, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2352, "endOffset": 4132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4194, "endOffset": 4456, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4511, "endOffset": 4695, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4746, "endOffset": 5163, "count": 1}, {"startOffset": 4907, "endOffset": 5162, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5215, "endOffset": 5634, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5693, "endOffset": 6202, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6253, "endOffset": 6512, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6554, "endOffset": 7173, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7199, "endOffset": 7419, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1162", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/steps/CompanyProfileStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 23020, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 23020, "count": 1}], "isBlockCoverage": true}, {"functionName": "default", "ranges": [{"startOffset": 2072, "endOffset": 2082, "count": 0}], "isBlockCoverage": false}, {"functionName": "data", "ranges": [{"startOffset": 2099, "endOffset": 3758, "count": 12}], "isBlockCoverage": true}, {"functionName": "updateStepData", "ranges": [{"startOffset": 3780, "endOffset": 3906, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateField", "ranges": [{"startOffset": 3917, "endOffset": 4049, "count": 1}], "isBlockCoverage": true}, {"functionName": "validateStep", "ranges": [{"startOffset": 4060, "endOffset": 4128, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleLogoUpload", "ranges": [{"startOffset": 4139, "endOffset": 4961, "count": 0}], "isBlockCoverage": false}, {"functionName": "removeLogo", "ranges": [{"startOffset": 4972, "endOffset": 5088, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vue2_injectStyles", "ranges": [{"startOffset": 5369, "endOffset": 5475, "count": 12}, {"startOffset": 5438, "endOffset": 5473, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5931, "endOffset": 6020, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6153, "endOffset": 6236, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6285, "endOffset": 6329, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1163", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/steps/CompanyProfileStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 54167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 54167, "count": 1}], "isBlockCoverage": true}, {"functionName": "render", "ranges": [{"startOffset": 198, "endOffset": 8777, "count": 15}, {"startOffset": 278, "endOffset": 282, "count": 0}, {"startOffset": 7594, "endOffset": 7627, "count": 14}, {"startOffset": 7663, "endOffset": 7696, "count": 14}, {"startOffset": 7697, "endOffset": 7815, "count": 14}, {"startOffset": 7816, "endOffset": 7986, "count": 1}, {"startOffset": 8386, "endOffset": 8419, "count": 14}, {"startOffset": 8420, "endOffset": 8625, "count": 14}, {"startOffset": 8626, "endOffset": 8636, "count": 1}], "isBlockCoverage": true}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 1405, "endOffset": 1550, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 1552, "endOffset": 1647, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.change", "ranges": [{"startOffset": 2041, "endOffset": 2419, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.change", "ranges": [{"startOffset": 2421, "endOffset": 2513, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 3815, "endOffset": 3960, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 3962, "endOffset": 4057, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 4748, "endOffset": 4894, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 4896, "endOffset": 4992, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 5551, "endOffset": 5697, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 5699, "endOffset": 5795, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 6312, "endOffset": 6453, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 6455, "endOffset": 6546, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 7068, "endOffset": 7209, "count": 0}], "isBlockCoverage": false}, {"functionName": "_c.on.input", "ranges": [{"startOffset": 7211, "endOffset": 7302, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8928, "endOffset": 8950, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 9057, "endOffset": 9088, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1164", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/src/components/GuidedSetupWizard/steps/CompanyProfileStep.vue", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 562, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 562, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1165", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueComponentNormalizer", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14728, "count": 1}], "isBlockCoverage": true}, {"functionName": "normalizeComponent", "ranges": [{"startOffset": 186, "endOffset": 2747, "count": 1}, {"startOffset": 502, "endOffset": 525, "count": 0}, {"startOffset": 746, "endOffset": 781, "count": 0}, {"startOffset": 897, "endOffset": 1767, "count": 0}, {"startOffset": 1823, "endOffset": 1991, "count": 0}, {"startOffset": 2062, "endOffset": 2466, "count": 0}, {"startOffset": 2624, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "hook", "ranges": [{"startOffset": 926, "endOffset": 1642, "count": 0}], "isBlockCoverage": false}, {"functionName": "hook", "ranges": [{"startOffset": 1825, "endOffset": 1991, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithStyleInjection", "ranges": [{"startOffset": 2335, "endOffset": 2460, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1166", "url": "file:///C:/Users/<USER>/projects/MVS-VR/mvs-vr-v2/implementation/server/directus/extensions/interfaces/vendor-portal/%00/vite/vueHotReload", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43098, "count": 1}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.install", "ranges": [{"startOffset": 497, "endOffset": 1066, "count": 1}, {"startOffset": 543, "endOffset": 553, "count": 0}, {"startOffset": 597, "endOffset": 610, "count": 0}, {"startOffset": 781, "endOffset": 812, "count": 0}, {"startOffset": 904, "endOffset": 1064, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.createRecord", "ranges": [{"startOffset": 1255, "endOffset": 1518, "count": 1}, {"startOffset": 1294, "endOffset": 1304, "count": 0}, {"startOffset": 1361, "endOffset": 1412, "count": 0}], "isBlockCoverage": true}, {"functionName": "__VUE_HMR_RUNTIME__.isRecorded", "ranges": [{"startOffset": 1618, "endOffset": 1675, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeOptionsHot", "ranges": [{"startOffset": 1778, "endOffset": 2449, "count": 1}, {"startOffset": 1843, "endOffset": 2092, "count": 0}], "isBlockCoverage": true}, {"functionName": "options.render", "ranges": [{"startOffset": 1898, "endOffset": 2088, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2138, "endOffset": 2290, "count": 12}, {"startOffset": 2202, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2333, "endOffset": 2442, "count": 12}], "isBlockCoverage": true}, {"functionName": "injectHook", "ranges": [{"startOffset": 2623, "endOffset": 2811, "count": 2}, {"startOffset": 2728, "endOffset": 2796, "count": 1}, {"startOffset": 2778, "endOffset": 2796, "count": 0}, {"startOffset": 2801, "endOffset": 2809, "count": 1}], "isBlockCoverage": true}, {"functionName": "tryWrap", "ranges": [{"startOffset": 2813, "endOffset": 3060, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2845, "endOffset": 3058, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateOptions", "ranges": [{"startOffset": 3062, "endOffset": 3296, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3337, "endOffset": 5941, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5981, "endOffset": 7113, "count": 0}], "isBlockCoverage": false}, {"functionName": "patchScopedSlots", "ranges": [{"startOffset": 7357, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}]}]}