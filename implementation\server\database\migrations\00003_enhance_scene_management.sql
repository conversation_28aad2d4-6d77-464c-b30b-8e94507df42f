-- Enhance scene management tables
-- This migration adds scene_flows table and enhances existing scenes table

-- Update scenes table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'scenes') THEN
    -- Check if flow column exists
    IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'scenes' AND column_name = 'flow') THEN
      -- Rename flow column to configuration
      ALTER TABLE scenes RENAME COLUMN flow TO configuration;
    END IF;
  ELSE
    -- Create scenes table if it doesn't exist
    CREATE TABLE scenes (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
      name TEXT NOT NULL,
      description TEXT,
      version TEXT NOT NULL,
      configuration JSONB NOT NULL,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
  END IF;
END $$;

-- Create scene_flows table
CREATE TABLE IF NOT EXISTS scene_flows (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  flow JSONB NOT NULL,
  version TEXT NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create scene_versions table to track scene versions
CREATE TABLE IF NOT EXISTS scene_versions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  version TEXT NOT NULL,
  configuration JSONB NOT NULL,
  is_current BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(scene_id, version)
);

-- Create scene_assets table to track assets used in scenes
CREATE TABLE IF NOT EXISTS scene_assets (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  asset_id UUID NOT NULL REFERENCES assets(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(scene_id, asset_id)
);

-- Create scene_blueprints table to track blueprints used in scenes
CREATE TABLE IF NOT EXISTS scene_blueprints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  scene_id UUID NOT NULL REFERENCES scenes(id) ON DELETE CASCADE,
  blueprint_id UUID NOT NULL REFERENCES blueprints(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(scene_id, blueprint_id)
);

-- Apply updated_at trigger to new tables
CREATE TRIGGER update_scene_flows_updated_at
BEFORE UPDATE ON scene_flows
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scene_versions_updated_at
BEFORE UPDATE ON scene_versions
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on new tables
ALTER TABLE scene_flows ENABLE ROW LEVEL SECURITY;
ALTER TABLE scene_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE scene_assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE scene_blueprints ENABLE ROW LEVEL SECURITY;

-- Create policies for scene_flows table
CREATE POLICY "Scene flows are viewable by authenticated users" ON scene_flows
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Scene flows are editable by admins" ON scene_flows
  FOR ALL USING (auth.role() = 'admin');

CREATE POLICY "Scene flows are editable by vendor owners" ON scene_flows
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM scenes s
      WHERE s.id = scene_id
      AND s.vendor_id = auth.uid()
    )
  );

-- Create policies for scene_versions table
CREATE POLICY "Scene versions are viewable by authenticated users" ON scene_versions
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Scene versions are editable by admins" ON scene_versions
  FOR ALL USING (auth.role() = 'admin');

CREATE POLICY "Scene versions are editable by vendor owners" ON scene_versions
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM scenes s
      WHERE s.id = scene_id
      AND s.vendor_id = auth.uid()
    )
  );

-- Create policies for scene_assets table
CREATE POLICY "Scene assets are viewable by authenticated users" ON scene_assets
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Scene assets are editable by admins" ON scene_assets
  FOR ALL USING (auth.role() = 'admin');

CREATE POLICY "Scene assets are editable by vendor owners" ON scene_assets
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM scenes s
      WHERE s.id = scene_id
      AND s.vendor_id = auth.uid()
    )
  );

-- Create policies for scene_blueprints table
CREATE POLICY "Scene blueprints are viewable by authenticated users" ON scene_blueprints
  FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Scene blueprints are editable by admins" ON scene_blueprints
  FOR ALL USING (auth.role() = 'admin');

CREATE POLICY "Scene blueprints are editable by vendor owners" ON scene_blueprints
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM scenes s
      WHERE s.id = scene_id
      AND s.vendor_id = auth.uid()
    )
  );

-- Create indexes for performance
CREATE INDEX idx_scene_flows_scene_id ON scene_flows(scene_id);
CREATE INDEX idx_scene_flows_is_active ON scene_flows(is_active);
CREATE INDEX idx_scene_versions_scene_id ON scene_versions(scene_id);
CREATE INDEX idx_scene_versions_is_current ON scene_versions(is_current);
CREATE INDEX idx_scene_assets_scene_id ON scene_assets(scene_id);
CREATE INDEX idx_scene_assets_asset_id ON scene_assets(asset_id);
CREATE INDEX idx_scene_blueprints_scene_id ON scene_blueprints(scene_id);
CREATE INDEX idx_scene_blueprints_blueprint_id ON scene_blueprints(blueprint_id);

-- Add function to get latest version of a scene
CREATE OR REPLACE FUNCTION get_latest_scene_version(p_scene_id UUID)
RETURNS TABLE (
  id UUID,
  scene_id UUID,
  version TEXT,
  configuration JSONB,
  is_current BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM scene_versions
  WHERE scene_id = p_scene_id
  ORDER BY created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Add function to get active flow for a scene
CREATE OR REPLACE FUNCTION get_active_scene_flow(p_scene_id UUID)
RETURNS TABLE (
  id UUID,
  scene_id UUID,
  name TEXT,
  description TEXT,
  flow JSONB,
  version TEXT,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM scene_flows
  WHERE scene_id = p_scene_id
  AND is_active = true
  LIMIT 1;
END;
$$ LANGUAGE plpgsql;
