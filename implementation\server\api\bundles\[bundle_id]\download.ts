import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../shared/utils/logger';
import { errorHandler } from '../../../shared/utils/error-handler';
import { AssetService } from '../../../services/asset-service';
import axios from 'axios';
import archiver from 'archiver';
import { Readable } from 'stream';

// Define the query parameters schema
const QueryParamsSchema = z.object({
  bundle_id: z.string().uuid(),
  client_id: z.string().optional(),
});

/**
 * Asset Bundle Download API endpoint
 *
 * This endpoint creates a ZIP file containing all assets in a bundle and streams it to the client.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse({
      bundle_id: req.query.bundle_id,
      client_id: req.query.client_id,
    });

    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { bundle_id, client_id } = queryResult.data;

    // Log the request
    logger.info('Asset bundle download request', {
      bundle_id,
      client_id,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Create asset service
    const assetService = new AssetService(supabase);

    // Get bundle with assets
    const bundle = await assetService.getAssetBundleWithAssets(bundle_id);
    if (!bundle) {
      return res.status(404).json({ error: 'Asset bundle not found' });
    }

    // Record client download if client_id is provided
    if (client_id) {
      // In a real implementation, we would record this in a database
      logger.info('Recording client download', { client_id, bundle_id });
    }

    // Set response headers
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename=bundle_${bundle_id}.zip`);

    // Create ZIP archive
    const archive = archiver('zip', {
      zlib: { level: 9 }, // Compression level
    });

    // Pipe archive to response
    archive.pipe(res);

    // Add manifest file
    const manifest = {
      bundleId: bundle.id,
      hash: bundle.hash,
      version: bundle.version,
      assets: bundle.assets.map(asset => asset.name),
      created_at: bundle.created_at,
    };

    archive.append(JSON.stringify(manifest, null, 2), { name: 'manifest.json' });

    // Add each asset to the archive
    for (const asset of bundle.assets) {
      try {
        // Get the asset from its URL
        const response = await axios({
          method: 'get',
          url: asset.url,
          responseType: 'stream',
        });

        // Add to archive
        archive.append(response.data, { name: `assets/${asset.name}` });
      } catch (error) {
        logger.error('Error downloading asset for bundle', {
          error,
          asset_id: asset.id,
          asset_url: asset.url,
        });
        // Continue with other assets
      }
    }

    // Finalize the archive
    await archive.finalize();
  } catch (error) {
    logger.error('Unexpected error in asset bundle download endpoint', { error });
    return res.status(500).json({ error: 'Internal server error' });
  }
}
