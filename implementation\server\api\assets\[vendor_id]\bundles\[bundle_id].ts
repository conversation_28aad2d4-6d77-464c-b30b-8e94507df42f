import { createServerSupabaseClient } from '@supabase/auth-helpers-nextjs';
import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { logger } from '../../../../shared/utils/logger';
import { errorHandler } from '../../../../shared/utils/error-handler';

// Define the asset info schema
const AssetInfoSchema = z.object({
  id: z.string().uuid(),
  type: z.string(),
  name: z.string(),
  url: z.string().url(),
  thumbnail_url: z.string().url().nullable(),
  hash: z.string(),
  size: z.number().int().positive(),
  version: z.string(),
  metadata: z.record(z.any()),
});

// Define the response schema
const AssetBundleResponseSchema = z.object({
  id: z.string().uuid(),
  vendor_id: z.string().uuid(),
  name: z.string(),
  description: z.string().nullable(),
  hash: z.string(),
  version: z.string(),
  assets: z.array(AssetInfoSchema),
});

// Define the query parameters schema
const QueryParamsSchema = z.object({
  vendor_id: z.string().uuid(),
  bundle_id: z.string().uuid(),
});

/**
 * Asset Bundle API endpoint
 *
 * This endpoint returns information about a specific asset bundle.
 *
 * @param req - The request object
 * @param res - The response object
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Validate query parameters
    const queryResult = QueryParamsSchema.safeParse(req.query);
    if (!queryResult.success) {
      return res
        .status(400)
        .json({ error: 'Invalid query parameters', details: queryResult.error.format() });
    }

    // Extract parameters
    const { vendor_id, bundle_id } = queryResult.data;

    // Log the request
    logger.info('Asset bundle request', {
      vendor_id,
      bundle_id,
    });

    // Create Supabase client
    const supabase = createServerSupabaseClient({ req, res });

    // Authenticate request
    const {
      data: { session },
    } = await supabase.auth.getSession();
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get vendor
    const { data: vendor, error: vendorError } = await supabase
      .from('vendors')
      .select('id, name, api_key')
      .eq('id', vendor_id)
      .single();

    if (vendorError) {
      logger.error('Error fetching vendor', { error: vendorError, vendor_id });
      return res.status(500).json({ error: 'Error fetching vendor' });
    }

    if (!vendor) {
      return res.status(404).json({ error: 'Vendor not found' });
    }

    // Get asset bundle
    const { data: bundle, error: bundleError } = await supabase
      .from('asset_bundles')
      .select('*')
      .eq('id', bundle_id)
      .eq('vendor_id', vendor_id)
      .single();

    if (bundleError) {
      logger.error('Error fetching asset bundle', { error: bundleError, vendor_id, bundle_id });
      return res.status(500).json({ error: 'Error fetching asset bundle' });
    }

    if (!bundle) {
      return res.status(404).json({ error: 'Asset bundle not found' });
    }

    // Get assets in the bundle
    const { data: assets, error: assetsError } = await supabase
      .from('assets')
      .select('id, type, name, url, thumbnail_url, hash, size, version, metadata')
      .in('id', bundle.asset_ids);

    if (assetsError) {
      logger.error('Error fetching assets', { error: assetsError, asset_ids: bundle.asset_ids });
      return res.status(500).json({ error: 'Error fetching assets' });
    }

    // Prepare response
    const response = {
      id: bundle.id,
      vendor_id: bundle.vendor_id,
      name: bundle.name,
      description: bundle.description,
      hash: bundle.hash,
      version: bundle.version,
      assets: assets,
    };

    // Validate response
    const responseResult = AssetBundleResponseSchema.safeParse(response);
    if (!responseResult.success) {
      logger.error('Invalid response format', { error: responseResult.error.format(), response });
      return res.status(500).json({ error: 'Invalid response format' });
    }

    // Log success
    logger.info('Asset bundle response', {
      vendor_id,
      bundle_id,
      assets_count: assets.length,
    });

    // Return response
    return res.status(200).json(response);
  } catch (error) {
    // Handle errors
    return errorHandler(error, res);
  }
}
