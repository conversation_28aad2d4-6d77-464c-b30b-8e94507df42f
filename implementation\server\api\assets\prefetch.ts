/**
 * Prefetching API
 *
 * This API provides endpoints for prefetching and preloading assets.
 */

import { Router } from 'express';
import { PrefetchingService, PrefetchStrategy } from '../../services/asset/prefetching-service';
import { ProgressiveLoadingService } from '../../services/asset/progressive-loading-service';
import { CdnIntegrationService } from '../../services/asset/cdn-integration-service';
import { AssetBundleOptimizer } from '../../services/asset/asset-bundle-optimizer';
import { logger } from '../../shared/utils/logger';
import { supabase } from '../../shared/utils/supabase-client';

const router = Router();
const cdnService = new CdnIntegrationService(supabase);
const bundleOptimizer = new AssetBundleOptimizer(supabase);
const progressiveLoadingService = new ProgressiveLoadingService(
  supabase,
  cdnService,
  bundleOptimizer,
  {
    maxConcurrentLoads: 4,
    chunkSize: 1024 * 1024, // 1MB
    adaptiveQuality: true,
    preloadCriticalAssets: true,
    prioritizeVisibleAssets: true,
  },
);
const prefetchingService = new PrefetchingService(supabase, progressiveLoadingService);

/**
 * Prefetch assets
 *
 * @route POST /api/assets/prefetch
 * @body {string[]} assetIds - Asset IDs to prefetch
 * @body {string} strategy - Prefetch strategy
 * @body {object} context - Context (optional)
 * @body {string} userId - User ID (optional)
 * @body {string} sessionId - Session ID (optional)
 * @returns {object} Success message
 */
router.post('/', async (req, res) => {
  try {
    const { assetIds, strategy, context, userId, sessionId } = req.body;

    // Validate asset IDs
    if (!assetIds || !Array.isArray(assetIds) || assetIds.length === 0) {
      return res.status(400).json({ error: 'Asset IDs are required' });
    }

    // Validate strategy
    if (!strategy || !Object.values(PrefetchStrategy).includes(strategy)) {
      return res.status(400).json({
        error: 'Invalid strategy',
        validStrategies: Object.values(PrefetchStrategy),
      });
    }

    // Prefetch assets
    await prefetchingService.prefetchAssets({
      assetIds,
      strategy: strategy as PrefetchStrategy,
      context,
      userId,
      sessionId,
    });

    return res.json({ message: 'Prefetch request processed' });
  } catch (error) {
    logger.error('Error prefetching assets', { error });
    return res.status(500).json({ error: 'Error prefetching assets' });
  }
});

/**
 * Record user interaction with an asset
 *
 * @route POST /api/assets/prefetch/interaction
 * @body {string} userId - User ID
 * @body {string} sessionId - Session ID
 * @body {string} assetId - Asset ID
 * @returns {object} Success message
 */
router.post('/interaction', (req, res) => {
  try {
    const { userId, sessionId, assetId } = req.body;

    // Validate parameters
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }
    if (!sessionId) {
      return res.status(400).json({ error: 'Session ID is required' });
    }
    if (!assetId) {
      return res.status(400).json({ error: 'Asset ID is required' });
    }

    // Record interaction
    prefetchingService.recordInteraction(userId, sessionId, assetId);

    return res.json({ message: 'Interaction recorded' });
  } catch (error) {
    logger.error('Error recording interaction', { error });
    return res.status(500).json({ error: 'Error recording interaction' });
  }
});

/**
 * Set client idle state
 *
 * @route POST /api/assets/prefetch/idle
 * @body {boolean} idle - Whether the client is idle
 * @returns {object} Success message
 */
router.post('/idle', (req, res) => {
  try {
    const { idle } = req.body;

    // Validate idle
    if (idle === undefined) {
      return res.status(400).json({ error: 'Idle state is required' });
    }

    // Set idle state
    prefetchingService.setIdle(idle);

    return res.json({ message: 'Idle state set' });
  } catch (error) {
    logger.error('Error setting idle state', { error });
    return res.status(500).json({ error: 'Error setting idle state' });
  }
});

export default router;
