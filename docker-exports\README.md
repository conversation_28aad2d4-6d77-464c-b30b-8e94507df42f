# MVS-VR Deployment Guide

This directory contains the fixed Docker deployment files for the MVS-VR system with simplified startup scripts and Supabase connectivity.

## 🔧 Fixes Applied

### 1. Service Startup Scripts Fixed
- **Issue**: Docker containers were using shell script entrypoints that didn't exist
- **Fix**: Override entrypoints with direct `node` commands
- **Services Fixed**: All microservices now use `["node", "dist/services/[service]/server.js"]`

### 2. Nginx Configuration Simplified
- **Issue**: Complex routing to potentially non-working services
- **Fix**: Streamlined configuration focusing on core services
- **Result**: Clean routing for API Gateway, Directus, and health checks

### 3. Supabase Connection Configured
- **Issue**: Environment variables pointing to placeholder values
- **Fix**: All services now connect to remote Supabase instance
- **Database**: `https://hiyqiqbgiueyyvqoqhht.supabase.co`

### 4. Port 80 Accessibility
- **Issue**: Complex port mapping and routing
- **Fix**: Simple Nginx proxy on port 80 with health checks
- **Result**: Direct access via `http://localhost/` and `http://mvs.kanousai.com/`

## 🚀 Quick Deployment

### Prerequisites
- Docker and Docker Compose installed
- Port 80 available
- Internet connection for Supabase

### Deploy Services
```bash
# Make scripts executable
chmod +x deploy-and-test.sh test-connectivity.sh load-images.sh

# Load Docker images (if needed)
./load-images.sh

# Deploy and test services
./deploy-and-test.sh
```

### Test Connectivity
```bash
# Run connectivity tests
./test-connectivity.sh
```

## 🌐 Service URLs

### Main Access Points
- **Main Site**: `http://localhost/` or `http://mvs.kanousai.com/`
- **API Gateway**: `http://localhost/api/` or `http://mvs.kanousai.com/api/`
- **Admin Panel**: `http://localhost:8055/` or `http://admin.mvs.kanousai.com/`
- **Health Check**: `http://localhost/health`

### Individual Services
- **Auth Service**: `http://localhost:3005/health`
- **Asset Service**: `http://localhost:5000/health`
- **Scene Service**: `http://localhost:6000/health`
- **Blueprint Service**: `http://localhost:3003/health`
- **LLM Service**: `http://localhost:7000/health`
- **Analytics Service**: `http://localhost:8000/health`
- **Monitoring Service**: `http://localhost:9090/health`

## 🔗 DNS Configuration

For production deployment, configure DNS:
```
mvs.kanousai.com        A    **************
admin.mvs.kanousai.com  A    **************
api.mvs.kanousai.com    A    **************
```

## 📊 Monitoring

### Check Service Status
```bash
docker-compose -f docker-compose.exported.yml ps
```

### View Logs
```bash
# All services
docker-compose -f docker-compose.exported.yml logs -f

# Specific service
docker-compose -f docker-compose.exported.yml logs -f auth-service
```

### Stop Services
```bash
docker-compose -f docker-compose.exported.yml down
```

## 🗄️ Database Connection

All services are configured to connect to the remote Supabase instance:
- **URL**: `https://hiyqiqbgiueyyvqoqhht.supabase.co`
- **Database**: PostgreSQL on Supabase
- **Storage**: Supabase Storage for assets
- **Auth**: Supabase Auth for authentication

## 🔐 Environment Variables

Key environment variables are configured in `.env`:
- `SUPABASE_URL`: Remote Supabase instance
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for backend operations
- `SUPABASE_ANON_KEY`: Anonymous key for client operations
- `JWT_SECRET`: JWT signing secret
- `REDIS_PASSWORD`: Redis authentication

## 🛠️ Troubleshooting

### Service Won't Start
1. Check Docker logs: `docker-compose -f docker-compose.exported.yml logs [service-name]`
2. Verify image exists: `docker images | grep mvs-vr-v2`
3. Check port conflicts: `netstat -tulpn | grep [port]`

### Can't Connect to Supabase
1. Test connectivity: `curl -s https://hiyqiqbgiueyyvqoqhht.supabase.co/rest/v1/`
2. Verify environment variables in `.env`
3. Check service logs for authentication errors

### Port 80 Not Accessible
1. Check if Nginx is running: `docker-compose -f docker-compose.exported.yml ps nginx`
2. Verify no other service is using port 80: `sudo netstat -tulpn | grep :80`
3. Check Nginx logs: `docker-compose -f docker-compose.exported.yml logs nginx`

## 📝 Files Overview

- `docker-compose.exported.yml`: Main deployment configuration
- `nginx.conf`: Nginx reverse proxy configuration
- `.env`: Environment variables
- `deploy-and-test.sh`: Automated deployment and testing
- `test-connectivity.sh`: Connectivity testing script
- `load-images.sh`: Docker image loading script
