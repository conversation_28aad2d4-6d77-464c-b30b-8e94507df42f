/**
 * WebSocket Service for Real-time Collaboration
 * 
 * This service provides WebSocket communication for collaborative editing features.
 * It handles connection management, message passing, and reconnection logic.
 */

// Event types for collaboration
export const CollaborationEventTypes = {
  JOIN: 'join',
  LEAVE: 'leave',
  CURSOR_MOVE: 'cursor_move',
  SELECTION_CHANGE: 'selection_change',
  DOCUMENT_CHANGE: 'document_change',
  PRESENCE_UPDATE: 'presence_update',
  CHAT_MESSAGE: 'chat_message',
  ERROR: 'error',
  SYNC: 'sync'
};

/**
 * WebSocket Service for real-time collaboration
 */
export class WebSocketService {
  /**
   * Create a new WebSocket service
   * @param {Object} options - Configuration options
   * @param {string} options.roomId - Unique identifier for the collaboration room
   * @param {string} options.userId - Current user's ID
   * @param {string} options.userName - Current user's display name
   * @param {string} options.userColor - Color to represent the user (hex code)
   * @param {string} options.wsUrl - WebSocket server URL
   * @param {number} options.reconnectInterval - Milliseconds between reconnection attempts
   * @param {number} options.maxReconnectAttempts - Maximum number of reconnection attempts
   */
  constructor(options) {
    this.roomId = options.roomId;
    this.userId = options.userId;
    this.userName = options.userName;
    this.userColor = options.userColor || this.generateUserColor(options.userId);
    this.wsUrl = options.wsUrl || this.getDefaultWsUrl();
    this.reconnectInterval = options.reconnectInterval || 3000;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    
    this.socket = null;
    this.reconnectAttempts = 0;
    this.isConnecting = false;
    this.eventHandlers = {};
    this.messageQueue = [];
    
    // Bind methods to maintain 'this' context
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.reconnect = this.reconnect.bind(this);
    this.onOpen = this.onOpen.bind(this);
    this.onMessage = this.onMessage.bind(this);
    this.onClose = this.onClose.bind(this);
    this.onError = this.onError.bind(this);
    this.sendMessage = this.sendMessage.bind(this);
    this.processQueue = this.processQueue.bind(this);
  }

  /**
   * Get the default WebSocket URL based on the current environment
   * @returns {string} WebSocket URL
   */
  getDefaultWsUrl() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/ws/collaboration`;
  }

  /**
   * Generate a consistent color for a user based on their ID
   * @param {string} userId - User ID to generate color for
   * @returns {string} Hex color code
   */
  generateUserColor(userId) {
    // Simple hash function to generate a number from a string
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = userId.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    // Convert to hex color
    let color = '#';
    for (let i = 0; i < 3; i++) {
      const value = (hash >> (i * 8)) & 0xFF;
      color += ('00' + value.toString(16)).substr(-2);
    }
    
    return color;
  }

  /**
   * Connect to the WebSocket server
   * @returns {Promise} Resolves when connected, rejects on failure
   */
  connect() {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      return Promise.resolve();
    }
    
    if (this.isConnecting) {
      return new Promise((resolve, reject) => {
        this.addEventListener('open', () => resolve());
        this.addEventListener('error', (error) => reject(error));
      });
    }
    
    this.isConnecting = true;
    
    return new Promise((resolve, reject) => {
      try {
        const fullUrl = `${this.wsUrl}/${this.roomId}?userId=${this.userId}&userName=${encodeURIComponent(this.userName)}`;
        this.socket = new WebSocket(fullUrl);
        
        this.socket.onopen = (event) => {
          this.onOpen(event);
          resolve();
        };
        
        this.socket.onmessage = this.onMessage;
        this.socket.onclose = this.onClose;
        this.socket.onerror = (error) => {
          this.onError(error);
          reject(error);
        };
      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  /**
   * Disconnect from the WebSocket server
   */
  disconnect() {
    if (this.socket) {
      // Send leave message before closing
      this.sendMessage({
        type: CollaborationEventTypes.LEAVE,
        userId: this.userId,
        userName: this.userName
      });
      
      this.socket.close();
      this.socket = null;
    }
    
    this.reconnectAttempts = 0;
    this.isConnecting = false;
  }

  /**
   * Attempt to reconnect to the WebSocket server
   * @returns {Promise} Resolves when reconnected, rejects when max attempts reached
   */
  reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      const error = new Error(`Failed to reconnect after ${this.maxReconnectAttempts} attempts`);
      this.triggerEvent('error', error);
      return Promise.reject(error);
    }
    
    this.reconnectAttempts++;
    
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        this.connect()
          .then(() => {
            this.reconnectAttempts = 0;
            resolve();
          })
          .catch(error => {
            this.reconnect().then(resolve).catch(reject);
          });
      }, this.reconnectInterval);
    });
  }

  /**
   * Handle WebSocket open event
   * @param {Event} event - WebSocket event
   */
  onOpen(event) {
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // Send join message
    this.sendMessage({
      type: CollaborationEventTypes.JOIN,
      userId: this.userId,
      userName: this.userName,
      userColor: this.userColor,
      timestamp: Date.now()
    });
    
    // Process any queued messages
    this.processQueue();
    
    // Trigger open event
    this.triggerEvent('open', event);
  }

  /**
   * Handle WebSocket message event
   * @param {MessageEvent} event - WebSocket message event
   */
  onMessage(event) {
    try {
      const message = JSON.parse(event.data);
      this.triggerEvent('message', message);
      
      // Also trigger event based on message type
      if (message.type) {
        this.triggerEvent(message.type, message);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
      this.triggerEvent('error', error);
    }
  }

  /**
   * Handle WebSocket close event
   * @param {CloseEvent} event - WebSocket close event
   */
  onClose(event) {
    this.isConnecting = false;
    this.triggerEvent('close', event);
    
    // Attempt to reconnect if not a normal closure
    if (event.code !== 1000 && event.code !== 1001) {
      this.reconnect();
    }
  }

  /**
   * Handle WebSocket error event
   * @param {Event} event - WebSocket error event
   */
  onError(event) {
    this.isConnecting = false;
    this.triggerEvent('error', event);
  }

  /**
   * Send a message through the WebSocket
   * @param {Object} message - Message to send
   * @returns {boolean} True if sent, false if queued
   */
  sendMessage(message) {
    if (!message) return false;
    
    // Add timestamp if not present
    if (!message.timestamp) {
      message.timestamp = Date.now();
    }
    
    // If socket is open, send immediately
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
      return true;
    }
    
    // Otherwise, queue the message
    this.messageQueue.push(message);
    
    // Try to connect if not already connecting
    if (!this.isConnecting) {
      this.connect();
    }
    
    return false;
  }

  /**
   * Process queued messages
   */
  processQueue() {
    if (this.messageQueue.length === 0) return;
    
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      const queue = [...this.messageQueue];
      this.messageQueue = [];
      
      for (const message of queue) {
        this.socket.send(JSON.stringify(message));
      }
    }
  }

  /**
   * Add an event listener
   * @param {string} event - Event name
   * @param {Function} callback - Event callback
   */
  addEventListener(event, callback) {
    if (!this.eventHandlers[event]) {
      this.eventHandlers[event] = [];
    }
    
    this.eventHandlers[event].push(callback);
  }

  /**
   * Remove an event listener
   * @param {string} event - Event name
   * @param {Function} callback - Event callback to remove
   */
  removeEventListener(event, callback) {
    if (!this.eventHandlers[event]) return;
    
    this.eventHandlers[event] = this.eventHandlers[event].filter(
      handler => handler !== callback
    );
  }

  /**
   * Trigger an event
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  triggerEvent(event, data) {
    if (!this.eventHandlers[event]) return;
    
    for (const callback of this.eventHandlers[event]) {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} event handler:`, error);
      }
    }
  }
}

export default WebSocketService;
